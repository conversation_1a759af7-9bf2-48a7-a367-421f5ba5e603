using UnityEditor;
using UnityEngine;
using System.IO;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
namespace NF
{
    public class UnityTitleEditorWindow : EditorWindow
    {
        private string filePath = "/Editor/UpdateUnityEditorProcess.cs";
        private static string fileName = "";
        [MenuItem("Tool/修改unity项目显示名称")]
        public static void ShowWindow()
        {
            var names = Application.dataPath.Split('/');
            if (names.Length > 1)
            {
                fileName = names[0] + "/" + names[1];
            }
            var win = GetWindow(typeof(UnityTitleEditorWindow));
            win.minSize = new Vector2(500, 200);
            win.titleContent = new GUIContent("修改unity项目显示名称");
            win.Show();
        }
    
        void OnGUI()
        {
            EditorGUILayout.Space(20);
            EditorGUILayout.BeginVertical();
            fileName = EditorGUILayout.TextField("Unity项目名称：", fileName);
            EditorGUILayout.Space(20);
            
            if (GUILayout.Button("确定"))
            {
                var path = Application.dataPath + filePath;
                if (!File.Exists(path))
                {
                    Debug.LogError("没有路径=" + path);
                    return;
                }

                StringBuilder sb = new StringBuilder();
                var lines = File.ReadAllLines(path);
                for (int i = 0, n = lines.Length; i < n; i++)
                {
                    var line = lines[i];
                    if (line.Contains("private static string ProjectName"))
                    {
                        sb.Append("\t\t");
                        line = string.Format(@"private static string ProjectName = ""{0}"";", fileName);
                    }
                    sb.Append(line);
                    sb.Append("\n");
                }
                File.WriteAllText(path, sb.ToString());
                Debug.Log("修改成功！！！！！！！！！！");
            }
            EditorGUILayout.EndVertical();
        }
    }
}