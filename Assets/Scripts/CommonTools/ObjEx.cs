using UnityEngine;
using System.Collections.Generic;
using System;

public static class ObjEx
{
    public static void Print(this object a, params object[] str)
    {
        if (str == null) return;

        var list = new List<object>(str);

        Debug.LogFormat("{0}:{1}", Time.time, String.Join("\t", list.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
    }
    //public static void PrintError(this object a, params object[] str)
    //{
    //    var list = new List<object>(str);

    //    Debug.LogErrorFormat("{0}:{1}", Time.time, String.Join("\t", list.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
    //}
    //public static void print(params object[] str)
    //{
    //    var list = new List<object>(str);

    //    Debug.LogFormat("{0}:{1}", Time.time, String.Join("\t", list.ConvertAll(o => (o == null ? "null" : o.ToString())).ToArray()));
    //}
}