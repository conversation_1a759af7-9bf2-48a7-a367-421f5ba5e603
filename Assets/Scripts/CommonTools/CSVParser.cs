using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System.Text;

public class CSVParser
{
	public class RecordData
    {
		public int idx;
		public Dictionary<string,string> data = new Dictionary<string, string>();

		public bool AddColValue(int colIdx, string value, List<string> colName)
        {
			var maxCount = colName.Count;
			if(colIdx >= maxCount)
            {
				Debug.LogError($"添加第{colIdx}列失败，超出最大列:{maxCount}");
				return false;
            }
			data[colName[colIdx]] = value;
			return true;
        }

		public string ToString(List<string> colNameSet)
        {
			StringBuilder stringBuilder = new StringBuilder();
			var count = colNameSet.Count;
			for(int i = 0; i < count; i++)
            {
				var colName = colNameSet[i];
				if (data.TryGetValue(colName, out string tmpColValue))
                {
					stringBuilder.Append(tmpColValue);
				}
				if(i < count - 1 )
                {
					stringBuilder.Append(",");
                }
			}
			return stringBuilder.ToString();
		}

		public string this [string key]
        {
			get
            {
				string value;
				if(data.TryGetValue(key, out value))
                {
					return value;
                }
				return null;
            }
        }
    }

	public class RecordFile
    {
		public int bias;
		public List<string> colName = new List<string>();
        public List<RecordData> parsedData = new List<RecordData>();

		public RecordData GetRecord(int rowIdx)
        {
			int realdataIdx = rowIdx + bias;
			int lenth = parsedData.Count;
			if(realdataIdx >= lenth)
            {
				Debug.LogError($"最大行数:{lenth - bias},行数越界:{rowIdx + 1}");
				return null;
            }
			return parsedData[realdataIdx];
        }

		public List<RecordData> GetRecord(string colName, string colValue)
        {
			List<RecordData> resultData = new List<RecordData>();
			foreach(var recordData in parsedData)
            {
				if(recordData[colName] == colValue)
                {
					resultData.Add(recordData);
                }
            }

			return resultData;
        }

		public int MaxRecord()
        {
			int lenth = parsedData.Count - bias;
			return lenth;
		}

		public void ClearRecord(bool bClearHeader)
        {
			if(bClearHeader)
            {
				parsedData.Clear();
            }
			else
            {
				int removeCount = parsedData.Count - bias;
				parsedData.RemoveRange(bias, removeCount);
            }
        }

		public bool ParseColName(string recoredLine, Match matches)
        {
			var groups = matches.Groups;
			if(groups.Count < 1)
            {
				Debug.LogError("格式错误，列名匹配失败");
				return false;
            }
			string firstName = groups[0].Value;
			if(!firstName.Contains("{name}"))
            {
				return false;
            }
			else
            {
				colName.Add(firstName.Replace("{name}", ""));
			}
			string[] colNames = recoredLine.Split(',');
			for(int i = 1; i < colNames.Length; i++)
            {
				colName.Add(colNames[i]);
            }
			return true;
		}
    }

	const string semanticFlagPattern = @"\{.+\}[^,]*";

	public static RecordFile Load(string filePath, string encodingName = "UTF-8")
	{
		var fullPath = Path.GetFullPath(filePath);
		if (!File.Exists(fullPath))
		{
			Debug.LogError($"文件不存在:{fullPath}");
			return null;
		}

		RecordFile recordFile = new RecordFile();
		int idx = 0;
		using (FileStream srcFile = File.Open(fullPath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
        {
#pragma warning disable IDE0063
            using (System.IO.StreamReader reader = new System.IO.StreamReader(srcFile, Encoding.GetEncoding(encodingName)))
#pragma warning restore IDE0063
            {
				while (!reader.EndOfStream)
				{
					string line = reader.ReadLine();
					idx++;
					string[] token = line.Split(',');
					var matches = Regex.Match(line, semanticFlagPattern);
					if (!string.IsNullOrEmpty(matches.Value))
					{
						if (recordFile.bias < idx)
						{
							recordFile.bias = idx;
						}
						recordFile.ParseColName(line, matches);
					}

					var recordData = new RecordData()
					{
						idx = idx
					};
					for (int colIdx = 0; colIdx < token.Length; colIdx++)
					{
						recordData.AddColValue(colIdx, token[colIdx], recordFile.colName);
					}
					recordFile.parsedData.Add(recordData);
				}
			}
		}
		return recordFile;
	}

	public static RecordFile LoadFileData(string strFileData)
	{
		if (string.IsNullOrEmpty(strFileData))
		{
			Debug.LogError($"文件内容为空");
			return null;
		}

		RecordFile recordFile = new RecordFile();
		int idx = 0;
		string[] lines = strFileData.Split('\n');
		for(int i = 0; i < lines.Length; i++)
        {
			string line = lines[i];
			line = line.Replace("\r", "");
			if(string.IsNullOrEmpty(line))
            {
				continue;
            }
			idx++;
			string[] token = line.Split(',');
			var matches = Regex.Match(line, semanticFlagPattern);
			if (!string.IsNullOrEmpty(matches.Value))
			{
				if (recordFile.bias < idx)
				{
					recordFile.bias = idx;
				}
				recordFile.ParseColName(line, matches);
			}

			var recordData = new RecordData()
			{
				idx = idx
			};
			for (int colIdx = 0; colIdx < token.Length; colIdx++)
			{
				recordData.AddColValue(colIdx, token[colIdx], recordFile.colName);
			}
			recordFile.parsedData.Add(recordData);
		}
		return recordFile;
	}

	public static bool Save(string filePath, RecordFile recordFile, string encodingName = "UTF-8")
    {
		try
		{
			if(!File.Exists(filePath))
            {
				using (new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.Read)){ };
			}
            using (FileStream srcFile = File.Open(filePath, FileMode.Create, FileAccess.Write, FileShare.Read))
            {
                using (System.IO.StreamWriter writer = new System.IO.StreamWriter(srcFile, Encoding.GetEncoding(encodingName)))
                {
					StringBuilder strBuilder = new StringBuilder();
					var parsedData = recordFile.parsedData;
					var count = parsedData.Count;
					for (int i = 0; i < count; i++)
					{
						strBuilder.Clear();
						var recordData = parsedData[i];
						strBuilder.Append(recordData.ToString(recordFile.colName));
						if (i < count - 1)
						{
							strBuilder.Append("\r\n");
						}
						writer.Write(strBuilder);
					}
				}
			}
			Debug.LogWarning($"保存:{filePath}, 成功。");
		}
		catch(System.Exception e)
        {
#if UNITY_EDITOR
			UnityEditor.EditorUtility.DisplayDialog("失败提示", e.Message, "ok");
#endif
			Debug.LogError($"保存:{filePath}, 失败。{e}");
			return false;
        }

		return true;
	}
}
