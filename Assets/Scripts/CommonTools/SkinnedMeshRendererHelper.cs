using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using War.Battle;
using Sirenix.OdinInspector;

[ExecuteInEditMode]
public class SkinnedMeshRendererHelper : MonoBehaviour
{
    public List<OrderConfig> list = new List<OrderConfig>();

    [OnValueChanged("ChangeRenderer")]
    public Renderer select;

    public OrderConfig Edit;
    public void ChangeRenderer()
    {
        var mr = select;
        if (mr == null) return;
        Edit = new OrderConfig() { mr = mr, order = mr.sortingOrder };
    }
    [Button]
    public void FindAllMeshRenderer()
    {
        list.Clear();
        var mrs = gameObject.GetComponentsInChildren<Renderer>(true);
        foreach (var mr in mrs)
        {
            if(list.Find(c=>c.mr == mr)==null)
            {
                list.Add(new OrderConfig() { mr = mr, order = mr.sortingOrder });
            }
        }
    }
    public int SeqOrder = 200;
    [Button]
    public void SeqSorter()
    {
        var mrs = gameObject.GetComponentsInChildren<Renderer>(true);
        for (int i = 0; i < mrs.Length; i++)
        {
            Renderer mr = mrs[i];
            //if(mr.sortingOrder==0)
            mr.sortingOrder = SeqOrder;
        }
    }

    [Serializable]
    public class OrderConfig
    {
        public Renderer mr;
        [OnValueChanged("ChangeOrder")]
        public int order;

        public void ChangeOrder()
        {
            if(mr)
            {
                if(mr.sortingOrder!=order)
                {
                    Debug.Log("Change order in:"+mr.name + " new:" + order + " ,old:" + mr.sortingOrder);
                    mr.sortingOrder = order;
                }
            }
        }

    }
}

