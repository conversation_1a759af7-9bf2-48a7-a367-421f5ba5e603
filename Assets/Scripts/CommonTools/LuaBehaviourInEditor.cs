//#if UNITY_EDITOR
/*
 * <PERSON><PERSON> is pleased to support the open source community by making xLua available.
 * Copyright (C) 2016 THL A29 Limited, a Tencent company. All rights reserved.
 * Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at
 * http://opensource.org/licenses/MIT
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/

using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using XLua;
using System;
using System.IO;
using System.Text;
using Sirenix.OdinInspector;
using War.Base;

[System.Serializable]
public class Injection
{
	public string name;
	public GameObject value;
}

public enum UseType
{
    NETGENERATOR, //生成协议
    OTHER,
}
[ExecuteInEditMode]
//[LuaCallCSharp]
public class LuaBehaviourInEditor : MonoBehaviour {
    public UseType useType = UseType.OTHER;

    [ValueDropdown("GET_PROTO_PATH")]
    public string rootPath = "协议所在路径";     //例：G:\\Projects\\UnityProjects\\xHero\\Src\\Proto\\Message\\leagueComp.proto
    static List<string> GET_PROTO_PATH()
    {
        var proto_path = Application.dataPath + "/../../../Src/Proto/Message";
        var files = Directory.GetFiles(proto_path, "*.proto", SearchOption.AllDirectories);

        return new List<string>(files);

    }
    [ValueDropdown("GET_MicroServiceType")]
    public string microServiceType = "None";     //
    static List<string> GET_MicroServiceType()
    {
        var proto_path = Application.dataPath + "/../../../Src/Proto/Message/lua.proto";
        var content = File.ReadAllText(proto_path, Encoding.UTF8);
        var normal = new System.Text.RegularExpressions.Regex(@"(MicroService_\S+).*=");

        var list = new List<string>();
        var matches = normal.Matches(content);
        //ObjEx.print("matches.Count",matches.Count);

        for (int i = 0; i < matches.Count; i++)
        {
            //ObjEx.print("matches",i, matches[i].Groups[1].Value);
            if(matches[i].Success)
            {
                list.Add(matches[i].Groups[1].Value);
            }
        }
        list.Add("None");

        //ObjEx.print(AssetBundleManager.ToJson(list));

        return list;

    }

    public bool startWhenPlay = false;
	public TextAsset luaScript;
	[TextArea]
	public string Script;
    public Injection[] injections;
    public string[] loaders=new string[] {
        "Assets/Lua"
    };

    internal static LuaEnv luaEnv = null;// new LuaEnv(); //all lua behaviour shared one luaenv only!
	internal static float lastGCTime = 0;
	internal const float GCInterval = 1;//1 second 

	private Action luaStart;
	private Action luaUpdate;
    private Action luaOnDestroy;
    //public Func<string, string> luaEvent;

    private LuaTable scriptEnv;
    private string chunk = "LuaBehaviour";

    void OnEnable()
	{
        luaEnv = new LuaEnv();

        scriptEnv = (luaEnv.NewTable());
        chunk = luaScript ? luaScript.name : "LuaBehaviour";
        // 为每个脚本设置一个独立的环境，可一定程度上防止脚本间全局变量、函数冲突
        LuaTable meta = luaEnv.NewTable();
		meta.Set("__index", luaEnv.Global);
		scriptEnv.SetMetaTable(meta);
		meta.Dispose();

		scriptEnv.Set("self", this);
        if (useType == UseType.NETGENERATOR)
        {
            scriptEnv.Set("clientPath", Application.dataPath+"/../");
            scriptEnv.Set("rootPath", rootPath);
            scriptEnv.Set("microServiceType", microServiceType);
        }
        

        foreach (var injection in injections)
		{
			scriptEnv.Set(injection.name, injection.value);
		}
        luaEnv.AddLoader((ref string module) =>
        {
            foreach (var folder in loaders)
            {

                var files = System.IO.Directory.GetFiles("Assets/Lua", module + ".txt", System.IO.SearchOption.AllDirectories);
                if(files.Length > 0)
                {
                    var bs = File.ReadAllText(files[0], UTF8Encoding.UTF8);
                    return System.Text.UTF8Encoding.UTF8.GetBytes(bs);
                }
            }
            return null;
        });
        luaEnv.DoString(luaScript.text, chunk, scriptEnv);

        Action luaAwake = scriptEnv.Get<Action>("awake");
        //luaStart = null;
        //luaUpdate = null;
        //luaOnDestroy = null; 
        scriptEnv.Get("start", out luaStart);
		scriptEnv.Get("update", out luaUpdate);
        scriptEnv.Get("ondestroy", out luaOnDestroy);
        //scriptEnv.Get("onevent", out luaEvent);

        if (luaAwake != null)
		{
			luaAwake();
		}
	}

	// Use this for initialization
	void Start ()
	{
		if (luaStart != null)
		{
			luaStart();
		}


        if (Application.isPlaying && startWhenPlay)
        {
            Exe();
        }
    }

	// Update is called once per frame
	void Update ()
	{
		if (luaUpdate != null)
		{
            //scriptEnv.Set("clickpos", Camera.main.ScreenToWorldPoint(Input.mousePosition));

            luaUpdate();
		}
		if (Time.time - LuaBehaviourInEditor.lastGCTime > GCInterval)
		{
			luaEnv.Tick();
			LuaBehaviourInEditor.lastGCTime = Time.time;
		}
	}

	void OnDestroy()
	{
		if (luaOnDestroy != null)
		{
			luaOnDestroy();
		}
		luaOnDestroy = null;
		luaUpdate = null;
		luaStart = null;
        if(scriptEnv!=null)
        {
		    scriptEnv.Dispose();
        }
		injections = null;
	}

	[ContextMenu("Exe")]
	[Sirenix.OdinInspector.Button("Exe")]
    void Exe()
    {
        ExeScript(Script);
    }
    public object[] ExeScript(string Script)
    {
        Debug.Log(Script);
        OnEnable();
        var r = luaEnv.DoString(Script, chunk, scriptEnv);
        return r;
    }
 
    //public string OnEvent(string Script)
    //{
    //    OnEnable();
    //    var r = luaEvent.Invoke(Script);
    //    return r;
    //}
}


//#endif