namespace CommonTools
{
    using UnityEngine;

    public class SpriteLooper : MonoBehaviour
    {
        public float speed = 1.0f;
        [Tooltip("额外的重叠像素，用于消除黑线")]
        public float overlapPixels = 1f;

        private SpriteRenderer[] spriteRenderers;
        private float[] widths;
        private float pixelsPerUnit = 100f;

        void Start()
        {
            // 获取所有SpriteRenderer组件
            spriteRenderers = GetComponentsInChildren<SpriteRenderer>();
            widths = new float[spriteRenderers.Length];

            // 计算每个SpriteRenderer的宽度
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                widths[i] = spriteRenderers[i].sprite.bounds.size.x * spriteRenderers[i].transform.localScale.x;
            }
            
            // 计算像素单位比（3D比例为1:100）
            if (spriteRenderers.Length > 0 && spriteRenderers[0].sprite != null)
            {
                pixelsPerUnit = spriteRenderers[0].sprite.pixelsPerUnit;
            }
        }

        void LateUpdate()
        {
            // 首先移动所有精灵
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                spriteRenderers[i].transform.localPosition += Vector3.left * speed * Time.deltaTime;
            }

            // 然后检查是否需要重置位置
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                // 计算精灵的右边界（局部空间）
                float rightEdgeLocal = spriteRenderers[i].transform.localPosition.x + widths[i] / 2;
                
                // 如果精灵完全移出左侧视图
                if (rightEdgeLocal < -widths[i])
                {
                    // 获取当前最右边界位置（局部空间）
                    float farRight = GetFarRightPositionLocal();
                    
                    // 计算新位置：紧贴最右精灵的右侧
                    // 添加小量重叠以消除黑线
                    float overlap = overlapPixels / pixelsPerUnit;
                    float newX = farRight + widths[i] / 2 - overlap;
                    
                    Vector3 newPos = spriteRenderers[i].transform.localPosition;
                    newPos.x = newX;
                    spriteRenderers[i].transform.localPosition = newPos;
                }
            }
        }

        // 获取局部空间中最右边界的X位置
        private float GetFarRightPositionLocal()
        {
            float farRight = float.MinValue;
            
            for (int i = 0; i < spriteRenderers.Length; i++)
            {
                // 计算精灵的右边界（局部空间）
                float rightEdge = spriteRenderers[i].transform.localPosition.x + widths[i] / 2;
                if (rightEdge > farRight)
                {
                    farRight = rightEdge;
                }
            }
            
            return farRight;
        }
    }
}