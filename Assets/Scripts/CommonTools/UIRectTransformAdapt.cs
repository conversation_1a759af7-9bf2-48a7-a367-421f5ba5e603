using System;
using Sirenix.OdinInspector;
using UnityEngine;

namespace CommonTools
{
    [DefaultExecutionOrder(100)]
    public class UIRectTransformAdapt : MonoBehaviour
    {
        public enum AdaptType
        {
            X,
            Y,
        }

        public AdaptType type;
        [LabelText("$GetPoint1LabelText")] public RectTransform point1;
        [LabelText("$GetPoint2LabelText")] public RectTransform point2;

        private string GetPoint1LabelText()
        {
            return type == AdaptType.X ? "左" : "上";
        }

        private string GetPoint2LabelText()
        {
            return type == AdaptType.X ? "右" : "下";
        }

        private RectTransform _rectTransform;
        private Canvas _canvas;

#if UNITY_EDITOR
        [Button("测试")]
        public void OnTest()
        {
            Start();
        }

#endif

        public void Start()
        {
            if (point1 == null || point2 == null)
            {
                return;
            }

            _canvas = GetComponentInParent<Canvas>();
            _rectTransform = GetComponent<RectTransform>();
            if (_canvas != null && _rectTransform != null)
            {
                switch (type)
                {
                    case AdaptType.X:
                        AdaptSizeX();
                        break;
                    case AdaptType.Y:
                        AdaptSizeY();
                        break;
                }
            }
        }

        private Vector2 GetCanvasPosition(RectTransform point)
        {
            Vector3 worldPosition = point.TransformPoint(Vector3.zero);
            Vector2 screenPosition = RectTransformUtility.WorldToScreenPoint(_canvas.worldCamera, worldPosition);
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(_canvas.transform as RectTransform,
                    screenPosition,
                    _canvas.worldCamera, out var pos))
            {
                return pos;
            }

            return Vector2.zero;
        }

        private void AdaptSizeX()
        {
            var point1Pos = GetCanvasPosition(point1);
            var point1X = point1Pos.x + (1 - point1.pivot.x) * point1.sizeDelta.x;
            var point2Pos = GetCanvasPosition(point2);
            var point2X = point2Pos.x - point2.pivot.x * point2.sizeDelta.x;
            var sizeX = point1X - point2X;
            if (sizeX < 0) sizeX *= -1;
            _rectTransform.sizeDelta = new Vector2(sizeX, _rectTransform.sizeDelta.y);
        }

        private void AdaptSizeY()
        {
            var point1Pos = GetCanvasPosition(point1);
            var point1Y = point1Pos.y - point1.pivot.y * point1.sizeDelta.y;
            var point2Pos = GetCanvasPosition(point2);
            var point2Y = point2Pos.y + (1 - point2.pivot.y) * point2.sizeDelta.y;

            var sizeY = point1Y - point2Y;
            if (sizeY < 0) sizeY *= -1;
            _rectTransform.sizeDelta = new Vector2(_rectTransform.sizeDelta.x, sizeY);
        }
    }
}