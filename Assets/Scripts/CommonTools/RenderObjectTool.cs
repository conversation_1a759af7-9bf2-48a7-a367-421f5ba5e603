using System.Collections.Generic;
using UnityEngine;

class RTTData
{
    public RenderTexture renderTexture;

    public void Destroy()
    {
        if (renderTexture == null)
        {
            return;
        }
        RenderTexture.ReleaseTemporary(renderTexture);
        renderTexture = null;
    }
}

public class RenderObjectTool
{
    private RenderObjectTool() { }
    private static RenderObjectTool _instance;

    public static RenderObjectTool Instance
    {
        get
        {
            if(_instance == null)
            {
                _instance = new RenderObjectTool();
            }
            return _instance;
        }
    }

    readonly Dictionary<string, RTTData> cachedRenderer = new Dictionary<string, RTTData>();
    Color transparentBlack = new Color(0, 0, 0, 0);

    void ClearRenderTexture(RenderTexture renderTex)
    {
        RenderTexture oldActive = RenderTexture.active;
        RenderTexture.active = renderTex;
        GL.Clear(true, true, transparentBlack);
        RenderTexture.active = oldActive;
        renderTex.DiscardContents();
    }

    public RenderTexture RenderGameObject(GameObject gameObj, string rttName, int width, int height, Camera renderCamera)
    {
        if(renderCamera == null)
        {
            Debug.LogError("摄像机不能设置为空");
            return null;
        }
        RTTData rttInfo = null;
        if (!cachedRenderer.TryGetValue(rttName, out rttInfo))
        {
            rttInfo = new RTTData();
            cachedRenderer[rttName] = rttInfo;
        }
        if (null != rttInfo.renderTexture &&
            (rttInfo.renderTexture.width != width || rttInfo.renderTexture.height != height))
        {
            RenderTexture.ReleaseTemporary(rttInfo.renderTexture);
            rttInfo.renderTexture = null;
        }
        if (rttInfo.renderTexture == null)
        {
            rttInfo.renderTexture = RenderTexture.GetTemporary(width, height, 24);
            rttInfo.renderTexture.name = rttName;
        }
        Camera camera = renderCamera;
        RenderTexture orgTexture = camera.targetTexture;

        //先清理残留图片，防止可能由于 Camera 未设置清除引起的问题
        ClearRenderTexture(rttInfo.renderTexture);
        camera.targetTexture = rttInfo.renderTexture;
        camera.Render();
        camera.targetTexture = orgTexture;

        return rttInfo.renderTexture;
    }

    public void ReleaseRenderTexture(string rttName)
    {
        RTTData rttData;
        if (!cachedRenderer.TryGetValue(rttName, out rttData))
        {
            return;
        }
        cachedRenderer.Remove(rttName);
        rttData.Destroy();
    }
}
