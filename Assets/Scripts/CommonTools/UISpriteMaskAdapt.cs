using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace CommonTools
{
    [DefaultExecutionOrder(10000)]
    public class UISpriteMaskAdapt : MonoBehaviour
    {
        public Vector2 defaultSize;

        public RectTransform rect;

        // Start is called before the first frame update
        void Start()
        {
            if (defaultSize == Vector2.zero || rect == null)
                return;

            var sizeDelta = rect.rect.size;
            var rateX = sizeDelta.x / defaultSize.x;
            var rateY = sizeDelta.y / defaultSize.y;

            Debug.Log($"rateX:{rateX}, rateY:{rateY}");
            var transform1 = transform;
            var scale = transform1.localScale;
            transform1.localScale = new Vector3(rateX * scale.x, rateY * scale.y, scale.z);

            var position = transform1.localPosition;
            transform1.localPosition = new Vector3(rateX * position.x, rateY * position.y, position.z);
        }
    }
}