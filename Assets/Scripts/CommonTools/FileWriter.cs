using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public class FileWriter
{
    private static string m_abPath = Application.streamingAssetsPath + "/abName.txt";
    private static HashSet<String> m_abNames = new HashSet<string>();

    public static void AppendAbNameText(string content)
    {
        if (string.IsNullOrEmpty(content))
        {
            return;
        }
        if (m_abNames.Contains(content))
        {
            return;
        }
        try
        {
            File.AppendAllText(m_abPath, content + "\n");
        }
        catch (Exception e)
        {
            Debug.LogError("写入文件失败: " + e.Message);
        }
    }
}