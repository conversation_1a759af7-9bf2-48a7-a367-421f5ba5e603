using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Common_Util
{
    public class UICDButton : Button
    {
        public float intervalTime = 0.5f;

        private float m_lastClickTime = 0;

        public override void OnPointerClick(PointerEventData eventData)
        {
            if (Time.time - m_lastClickTime < intervalTime)
            {
                return;
            }

            m_lastClickTime = Time.time;
            base.OnPointerClick(eventData);
        }
    }
}