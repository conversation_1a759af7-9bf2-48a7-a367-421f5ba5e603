using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

//[XLua.CSharpCallLua]
public class LogHelp{
	static LogHelp _instance;

	public static LogHelp Instance {
		get {
			if(_instance==null)
			{
				_instance = new LogHelp ();
			}
			return _instance;
		}
	}
    List<string> cache = new List<string>();
	DateTime? startTime;
	DateTime last;
	public void Reset(){
		var thisTime = System.DateTime.Now;
		startTime = thisTime;
		last = thisTime;
//		Debug.Log (string.Format ("LogHelp:{3}==now:{0} lastInterval:{1} since_start:{2}", thisTime.ToShortTimeString (), (thisTime - last).TotalSeconds, (thisTime - startTime.Value).TotalSeconds, "Reset"));
	}
	public void Log(string title){
		var thisTime = System.DateTime.Now;
		if(startTime==null)
		{
			startTime = thisTime;
		}
        var str = string.Format("LogHelp:{3}==now:{0} lastItvl:{1} since_start:{2}", thisTime.ToShortTimeString(), (thisTime - last).TotalSeconds, (thisTime - startTime.Value).TotalSeconds, title);

        if(cache.Count>10000)
        {
            cache.RemoveAt(0);
        }
        cache.Add(str);
        Debug.Log(str);
        last = thisTime;
	}
	static public string clipboard
  	{
  		get
  		{
  			TextEditor te = new TextEditor();
  			te.Paste();
  			return te.text;
  		}
  		set
  		{
  			TextEditor te = new TextEditor();
  			te.content = new GUIContent(value);
  			te.OnFocus();
  			te.Copy();
  		}
  	}
    public void PrintAll()
    {
        var newl = new List<string>(cache);
        newl.Reverse();
        var all = string.Join("\n", newl.ToArray());
        Debug.LogWarning(all);
        var p = string.Format("../Log/LogHelp/{0}_{1}", System.DateTime.UtcNow.Ticks, "Fightlog.txt");
		SaveText(p,all);
    }    
	
	public void SaveText(string path,string content)
    {        
		var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
		File.WriteAllText(path,content);
    } 

}