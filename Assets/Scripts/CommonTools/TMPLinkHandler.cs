using System;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;

// 注意,如果遇到无法点击的时候，检查一下文本是不是没有自动大小或者RaycastTarget有没有打开
[RequireComponent(typeof(TextMeshProUGUI))]
public class TMPLinkHandler : MonoBehaviour, IPointerClickHandler
{
    [Serializable]
    public class LinkClickEvent : UnityEvent<string>
    {
    }

    [SerializeField] public LinkClickEvent m_OnLinkClick = new LinkClickEvent();

    /// <summary>
    /// 超链接点击事件
    /// </summary>
    public LinkClickEvent onLinkClick
    {
        get { return m_OnLinkClick; }
        set { m_OnLinkClick = value; }
    }

    private Camera camera;
    private TextMeshProUGUI textMeshPro;

    private void Awake()
    {
        camera = GetComponentInParent<Canvas>().worldCamera;
        textMeshPro = GetComponent<TextMeshProUGUI>();
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        // 获取鼠标点击的位置
        int linkIndex = TMP_TextUtilities.FindIntersectingLink(textMeshPro, eventData.position, Camera.main);

        if (linkIndex != -1) // 如果检测到点击到链接
        {
            string linkId = textMeshPro.textInfo.linkInfo[linkIndex].GetLinkID();
            HandleLinkClick(linkId);
        }
    }

    private void HandleLinkClick(string linkId)
    {
        if (onLinkClick != null)
        {
            onLinkClick.Invoke(linkId);
        }
    }
}