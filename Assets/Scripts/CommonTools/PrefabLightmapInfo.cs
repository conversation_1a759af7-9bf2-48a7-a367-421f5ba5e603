using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEngine.SceneManagement;

#if UNITY_EDITOR
using UnityEditor.SceneManagement;
#endif

using System.IO;

[ExecuteInEditMode]
public class PrefabLightmapInfo : MonoBehaviour
{
    [System.Serializable]
    public struct RendererInfo
    {
        public Renderer renderer;

        public int lightmapIndex;
        public Vector4 lightmapOffsetScale;
    }
    [System.Serializable]
    public struct LightBakingOutputBaked
    {
        public Light light;
        public LightmapBakeType lightmapBaketype;
        public MixedLightingMode mixedLightingMode;
    }
    [System.Serializable]
    public class LightmapDataBaked
    {
        public Texture2D lightmapColor;
        public Texture2D lightmapDir;
        public Texture2D shadowMask;

        [System.NonSerialized]
        public int runtimeLightmapIdx = -1;
    }

    [SerializeField]
    List<RendererInfo> rendererSet = new List<RendererInfo>();

    [SerializeField]
    List<LightmapDataBaked> lightmapBakingSet = new List<LightmapDataBaked>();

    [SerializeField]
    List<LightBakingOutputBaked> lightBakingSet = new List<LightBakingOutputBaked>();

    [SerializeField]
    Object lightingSettingsObj;

    [ReadOnly]
    public string lightingDataPath;

    [System.NonSerialized]
    public bool hasApplyLightmapData = false;

    static LightmapData[] orgSceneLightmapDatas;
    static LightmapsMode orgSceneLightmapsMode;
    static bool hasInitSceneLightmap = false;

    // 增加烘焙版本控制，之前的版本不能开启 prefab 烘焙功能
    public static int prefabBakeVersion = 1;

    public void Awake()
    {
        //InitPrefabLightmap();
    }

    public void OnEnable()
    {
        InitPrefabLightmap();

        SceneManager.sceneLoaded += OnSceneLoaded;
    }

    void OnDisable()
    {
        RemoveLightmapData();

        SceneManager.sceneLoaded -= OnSceneLoaded;
    }

    void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        InitPrefabLightmap();
    }

    public void InitPrefabLightmap()
    {
        Debug.LogWarning($"PrefabLightmapInfo enabled:{enabled},hasApplyLightmapData:{hasApplyLightmapData}");

        if (!enabled)
        {
            RemoveLightmapData();
        }
        else
        {
            ApplyLightmapData();
        }
    }

    public void ApplyLightmapData()
    {
        if(hasApplyLightmapData)
        {
            return;
        }

        if (rendererSet == null || rendererSet.Count == 0)
        {
            Debug.LogWarningFormat(this, $"未生成烘焙数据，请点击 [烘焙 Prefab] 进行烘焙");
            return;
        }

        var lightmaps = LightmapSettings.lightmaps;
        int lightmapsCount = lightmaps.Length;

        if(orgSceneLightmapDatas == null && !hasInitSceneLightmap)
        {
            orgSceneLightmapDatas = LightmapSettings.lightmaps;
            orgSceneLightmapsMode = LightmapSettings.lightmapsMode;
            hasInitSceneLightmap = true;
        }

        bool directionalLightmap = true;

        List<LightmapData> appendLightmaps = new List<LightmapData>();
        for (int i = 0; i < lightmapBakingSet.Count; i++)
        {
            bool lightmapExist = false;

            for (int j = 0; j < lightmaps.Length; j++)
            {
                var lightmapData = lightmaps[j];
                if (lightmapData == null)
                {
                    continue;
                }
                if (lightmapBakingSet[i].lightmapColor == lightmapData.lightmapColor)
                {
                    lightmapExist = true;
                    lightmapBakingSet[i].runtimeLightmapIdx = j;
                    break;
                }
            }

            if (!lightmapExist)
            {
                var newlightmapdata = new LightmapData
                {
                    lightmapColor = lightmapBakingSet[i].lightmapColor,
                    lightmapDir = lightmapBakingSet[i].lightmapDir,
                    shadowMask = lightmapBakingSet[i].shadowMask,
                };

                if(newlightmapdata.lightmapColor == null)
                {
                    Debug.LogError($"PrefabLightmapInfo lightmapColor is null");
                    continue;
                }
                Debug.LogWarning($"PrefabLightmapInfo lightmapColor:{newlightmapdata.lightmapColor}");

                appendLightmaps.Add(newlightmapdata);
                lightmapBakingSet[i].runtimeLightmapIdx = appendLightmaps.Count - 1;
                lightmapsCount += 1;
            }

            if (directionalLightmap && lightmapBakingSet[i].lightmapDir == null)
            {
                directionalLightmap = false;
            }
        }

        LightmapData[] combinedLightmaps;
        if (appendLightmaps.Count > 0)
        {
            combinedLightmaps = new LightmapData[lightmapsCount];

            if (lightmaps != null && lightmaps.Length > 0)
            {
                lightmaps.CopyTo(combinedLightmaps, 0);
            }
            else
            {
                Debug.LogWarning($"PrefabLightmapInfo lightmaps is empty");
            }
            appendLightmaps.CopyTo(combinedLightmaps, lightmaps.Length);
        }
        else
        {
            combinedLightmaps = lightmaps;
        }

        if (combinedLightmaps == null || combinedLightmaps.Length == 0)
        {
            Debug.LogError($"PrefabLightmapInfo lightmaps is empty, baked lightmap is empty");
            return;
        }
        LightmapSettings.lightmaps = combinedLightmaps;
        LightmapSettings.lightmapsMode = directionalLightmap ? LightmapsMode.CombinedDirectional : LightmapsMode.NonDirectional;
        ApplyRendererInfo(rendererSet, lightmapBakingSet, lightBakingSet);
        //StaticBatchingUtility.Combine(gameObject);
        hasApplyLightmapData = true;
    }

    public static void ApplyRendererInfo(List<RendererInfo> rendererInfoSet, List<LightmapDataBaked> lightmapBakingSet, List<LightBakingOutputBaked> lightsInfo)
    {
        Debug.LogWarning($"PrefabLightmapInfo ApplyRendererInfo lightmap");

        var maxLightmapBakingCount = lightmapBakingSet.Count;

        Dictionary<string, Shader> cachedShader = new Dictionary<string, Shader>();

        for (int i = 0; i < rendererInfoSet.Count; i++)
        {
            var rendererInfo = rendererInfoSet[i];
            if(rendererInfo.lightmapIndex < 0 || rendererInfo.lightmapIndex >= maxLightmapBakingCount)
            {
                Debug.LogError($"{rendererInfo.renderer.gameObject} lightmapIndex:{rendererInfo.lightmapIndex} 越界, max count;{maxLightmapBakingCount}");
                continue;
            }
            var lightmapIdx = lightmapBakingSet[rendererInfo.lightmapIndex].runtimeLightmapIdx;
            if(lightmapIdx >= 0)
            {
                rendererInfo.renderer.lightmapIndex = lightmapIdx;
                rendererInfo.renderer.lightmapScaleOffset = rendererInfo.lightmapOffsetScale;
            }

            //Material[] matSet = rendererInfo.renderer.sharedMaterials;
            //Shader applyShader;
            //Material mat;
            //string shaderName;
            //for (int j = 0; j < matSet.Length; j++)
            //{
            //    mat = matSet[j];
            //    if (mat == null)
            //    {
            //        continue;
            //    }
            //    shaderName = mat.shader.name;
            //    if (!cachedShader.TryGetValue(shaderName, out applyShader))
            //    {
            //        applyShader = Shader.Find(shaderName);
            //        cachedShader[shaderName] = applyShader;
            //    }
            //    if (applyShader != null)
            //    {
            //        mat.shader = applyShader;
            //    }
            //}
        }

        foreach(var lightBaking in lightsInfo)
        {
            LightBakingOutput bakingOutput = new LightBakingOutput
            {
                isBaked = true,
                lightmapBakeType = lightBaking.lightmapBaketype,
                mixedLightingMode = lightBaking.mixedLightingMode
            };

            lightBaking.light.bakingOutput = bakingOutput;
        }
    }

    public void RemoveLightmapData()
    {
        Debug.LogWarning($"PrefabLightmapInfo remove lightmap:{enabled},hasApplyLightmapData:{hasApplyLightmapData}");

        if (!hasApplyLightmapData)
        {
            return;
        }

        if (rendererSet == null || rendererSet.Count == 0)
        {
            return;
        }

        if (LightmapSettings.lightmaps != orgSceneLightmapDatas && hasInitSceneLightmap)
        {
            LightmapSettings.lightmaps = orgSceneLightmapDatas;
            orgSceneLightmapDatas = null;
            LightmapSettings.lightmapsMode = orgSceneLightmapsMode;
            hasInitSceneLightmap = false;
        }

        for (int i = 0; i < rendererSet.Count; i++)
        {
            var rendererInfo = rendererSet[i];
            if (rendererInfo.lightmapIndex < 0)
            {
                continue;
            }
            // A value of -1 (0xFFFF) means no lightmap has been assigned, which is the default. A value of 0xFFFE is internally used for objects that have their scale in lightmap set to 0
            rendererInfo.lightmapIndex = -1;
        }

        hasApplyLightmapData = false;
    }

#if UNITY_EDITOR

    public void ClearBakedData()
    {
        rendererSet.Clear();
        lightmapBakingSet.Clear();
        lightBakingSet.Clear();
    }

    public static bool IsValidPrefab(GameObject obj, out GameObject assetPrefab)
    {
        assetPrefab = null;

        var prefabType = PrefabUtility.GetPrefabAssetType(obj);
        if (prefabType == PrefabAssetType.NotAPrefab || prefabType == PrefabAssetType.MissingAsset)
        {
            return false;
        }
        assetPrefab = PrefabUtility.GetCorrespondingObjectFromSource(obj);
        return true;
    }

    /// <summary>
    /// var activePrefabPath = GetAssetPath(gameObject);
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static string GetAssetPath(GameObject obj)
    {
        IsValidPrefab(obj, out GameObject assetPrefab);
        var path = AssetDatabase.GetAssetPath(assetPrefab);
        return path;
    }

    public static void CopyAllFiles(string sourcePath, string targetPath)
    {
        //Now Create all of the directories
        foreach (string dirPath in Directory.GetDirectories(sourcePath, "*", SearchOption.AllDirectories))
        {
            Directory.CreateDirectory(dirPath.Replace(sourcePath, targetPath));
        }

        //Copy all the files & Replaces any files with the same name
        foreach (string newPath in Directory.GetFiles(sourcePath, "*.*", SearchOption.AllDirectories))
        {
            File.Copy(newPath, newPath.Replace(sourcePath, targetPath), true);
        }
    }

    public static void MoveAllFiles(string sourcePath, string targetPath)
    {
        //Now Create all of the directories
        foreach (string dirPath in Directory.GetDirectories(sourcePath, "*", SearchOption.AllDirectories))
        {
            Directory.CreateDirectory(dirPath.Replace(sourcePath, targetPath));
        }

        //Copy all the files & Replaces any files with the same name
        foreach (string newPath in Directory.GetFiles(sourcePath, "*.*", SearchOption.AllDirectories))
        {
            File.Move(newPath, newPath.Replace(sourcePath, targetPath));
        }
    }

    public static void DeleteAllFiles(string folderPath)
    {
        //Copy all the files & Replaces any files with the same name
        foreach (string newPath in Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories))
        {
            File.Delete(newPath);
        }
    }

    /// <summary>
    /// 烘培 Prefab
    /// 点击 "烘焙 Prefab" 按钮执行
    /// </summary>
    public static void GenerateLightmapInfo(PrefabLightmapInfo prefabLightmapInfo, string lightmapDataPath)
    {
        var folderName = Path.GetFileName(lightmapDataPath);
        if (folderName != "LightingData")
        {
            Debug.LogError($"Lightmap 保存路径文件夹名称必须为 [LightingData]");
            return;
        }

        var lightingSettings = prefabLightmapInfo.lightingSettingsObj as LightingSettings;
        if (lightingSettings == null)
        {
            Debug.LogError("未设置 Lighting Settings 参数");
            return;
        }
        if (lightingSettings.autoGenerate)
        {
            Debug.LogError("Lighting Settings Auto Generate 需要取消勾选");
            return;
        }
        Lightmapping.Bake();

        var activeScene = EditorSceneManager.GetActiveScene();
        var scenePath = activeScene.path;
        var lightmapBuildFolderPath = scenePath.Replace(".unity", "");
        Debug.Log($"Move assets from {lightmapBuildFolderPath} to {lightmapDataPath}");

        DeleteAllFiles(lightmapDataPath);

        AssetDatabase.StartAssetEditing();
        MoveAllFiles(lightmapBuildFolderPath, lightmapDataPath);
        AssetDatabase.StopAssetEditing();

        // 清除上次烘培数据
        prefabLightmapInfo.ClearBakedData();

        PrefabLightmapInfo[] prefabLightmapSet = FindObjectsOfType<PrefabLightmapInfo>();
        foreach(var prefabLightmap in prefabLightmapSet)
        {
            var gameObject = prefabLightmap.gameObject;
            GenerateLightmapInfo(gameObject, lightingSettings, prefabLightmapInfo.rendererSet, prefabLightmapInfo.lightmapBakingSet, prefabLightmapInfo.lightBakingSet);

            if(IsValidPrefab(gameObject, out GameObject _))
            {
                PrefabUtility.ApplyPrefabInstance(gameObject, InteractionMode.AutomatedAction);
            }
            else
            {
                Debug.LogErrorFormat(gameObject, $"{gameObject} 不是预制体，PrefabLightmapInfo 需要绑定在预制体上");
            }
        }
    }

    public static int LightmapDataBakedIndex(List<LightmapDataBaked> lightmapBakingSet, Texture2D lightmapColor)
    {
        for(var idx = 0; idx < lightmapBakingSet.Count; idx ++)
        {
            if(lightmapBakingSet[idx].lightmapColor == lightmapColor)
            {
                return idx;
            }
        }

        return -1;
    }

    public static int AddLightmapDataBaked(List<LightmapDataBaked> lightmapBakingSet, LightmapData lightmapData)
    {
        lightmapBakingSet.Add(new LightmapDataBaked()
        {
            lightmapColor = lightmapData.lightmapColor,
            lightmapDir = lightmapData.lightmapDir,
            shadowMask = lightmapData.shadowMask
        });
        return lightmapBakingSet.Count - 1;
    }

    static void GenerateLightmapInfo(GameObject root, LightingSettings lightingSettings, List<RendererInfo> rendererInfos, List<LightmapDataBaked> lightmapBakingSet, List<LightBakingOutputBaked> lightsInfo)
    {
        var renderers = root.GetComponentsInChildren<MeshRenderer>();
        foreach (MeshRenderer renderer in renderers)
        {
            // https://docs.unity3d.com/ScriptReference/Renderer-lightmapIndex.html
            if (renderer.lightmapIndex == 0xFFFF || renderer.lightmapIndex == 0xFFFE || renderer.lightmapIndex < 0)
            {
                continue;
            }
            RendererInfo rendererInfo = new RendererInfo
            {
                renderer = renderer
            };
            
            if (renderer.lightmapScaleOffset != Vector4.zero)
            {
                rendererInfo.lightmapOffsetScale = renderer.lightmapScaleOffset;

                var lightmapData = LightmapSettings.lightmaps[renderer.lightmapIndex];
            
                rendererInfo.lightmapIndex = LightmapDataBakedIndex(lightmapBakingSet, lightmapData.lightmapColor);
                if (rendererInfo.lightmapIndex == -1)
                {
                    rendererInfo.lightmapIndex = AddLightmapDataBaked(lightmapBakingSet, lightmapData);
                }
            
                rendererInfos.Add(rendererInfo);
            }
        }

        var lightSet = root.GetComponentsInChildren<Light>(true);
        foreach (Light light in lightSet)
        {
            LightBakingOutputBaked lightInfo = new LightBakingOutputBaked()
            {
                light = light,
                lightmapBaketype = light.lightmapBakeType,
                mixedLightingMode = lightingSettings.mixedBakeMode
            };
            lightsInfo.Add(lightInfo);
        }
    }
#endif //UNITY_EDITOR

}