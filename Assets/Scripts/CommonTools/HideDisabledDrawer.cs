
#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using System.Collections;

public class HideDisabledDrawer : MaterialPropertyDrawer
{
    protected string[] argValue;
    bool bElementHidden;

    public HideDisabledDrawer(string name)
    {
        argValue = new string[] { name };
    }

    public HideDisabledDrawer(string name1, string name2)
    {
        argValue = new string[] { name1, name2 };
    }

    //-------------------------------------------------------------------------------------------------

    public override void OnGUI(Rect position, UnityEditor.MaterialProperty prop, string label, MaterialEditor editor)
    {
        bElementHidden = false;
        for (int i = 0; i < editor.targets.Length; i++)
        {
            Material mat = editor.targets[i] as Material;
            if(mat == null)
            {
                continue;
            }
            for (int j = 0; j < argValue.Length; j++)
            {
                bElementHidden |= !mat.IsKeywordEnabled(argValue[j]);
                if(bElementHidden)
                {
                    break;
                }
            }
        }

        if (!bElementHidden)
        {
            editor.DefaultShaderProperty(prop, label);
        }
    }

    public override float GetPropertyHeight(UnityEditor.MaterialProperty prop, string label, MaterialEditor editor)
    {
        return 0;
    }

}
#endif