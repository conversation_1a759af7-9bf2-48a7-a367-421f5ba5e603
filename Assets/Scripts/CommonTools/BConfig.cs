using System.Collections.Generic;

public class BConfig
{

    static BConfig _instance;

    public static BConfig Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new BConfig();
            }
            return _instance;
        }
    }
    Dictionary<string, bool> cDic = new Dictionary<string, bool>()
    {
        { "IsSMCombineOn",true },
        { "IsOfflineOn",true },
    };
    public bool this[string ind]
    {
        get
        {
            return cDic.ContainsKey(ind) && cDic[ind];
        }
    }

    public bool GetConfig(string name)
    {
        bool r = false;
        cDic.TryGetValue(name, out r);
        return r;
    }
}