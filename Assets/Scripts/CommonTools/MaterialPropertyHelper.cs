
using CustomMaterialProperty;
using System.Collections.Generic;
using UnityEngine;

namespace CustomMaterialProperty
{
    [System.Serializable]
    public class FloatValue
    {
        public FloatValue(string propertyName, float value)
        {
            this.propertyName = propertyName;
            this.value = value;
        }
        public string propertyName;
        public float value;

        public void SetValue(MaterialPropertyBlock propertyBlock)
        {
            propertyBlock.SetFloat(propertyName, value);
        }

        public void SetValue(Material material)
        {
            material.SetFloat(propertyName, value);
        }
    }
}

[ExecuteInEditMode,RequireComponent(typeof(Renderer)),DisallowMultipleComponent]
public class MaterialPropertyHelper : MonoBehaviour
{
    MaterialPropertyBlock propertyBlock;

    public List<FloatValue> propertySet = new List<FloatValue>();

    void Awake()
    {
    }

    void OnEnable()
    {
        propertyBlock = new MaterialPropertyBlock();

        var renderer = GetComponent<Renderer>();
        if (Application.isPlaying)
        {
            Material material = renderer.material;
            foreach (var property in propertySet)
            {
                property.SetValue(material);
            }
        }
        else
        {
            renderer.GetPropertyBlock(propertyBlock);
            foreach (var property in propertySet)
            {
                property.SetValue(propertyBlock);
            }
            renderer.SetPropertyBlock(propertyBlock);
        }
    }

    void OnDisable()
    {
        if(propertyBlock != null)
        {
            var renderer = GetComponent<Renderer>();
            renderer.SetPropertyBlock(null);
        }
        propertyBlock = null;
    }

    public void SetFloatValue(string name, float value)
    {
        FloatValue floatValue;
        for(int i = 0; i < propertySet.Count; i++)
        {
            floatValue = propertySet[i];
            if(floatValue.propertyName == name)
            {
                floatValue.value = value;
                return;
            }
        }

        propertySet.Add(new FloatValue(name, value));
    }
}

