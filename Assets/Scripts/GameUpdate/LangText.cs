using bc.Lang;
using System;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace bc.Lang
{
    public class LangText : MonoBehaviour
    {
        public static System.Action<MonoBehaviour> regist_action;
        public string key;

        private void Awake()
        {
            if( !IsValid() )
            {
                Debug.LogError("û���ҵ����ʵ�Text���", this);
                return;
            }
            regist_action?.Invoke(this);
        }

        public bool IsValid()
        {
            return GetComponent<Text>() || GetComponent<TextMeshProUGUI>();
        }
    }

}