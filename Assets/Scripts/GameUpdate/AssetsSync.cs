#if UNITY_EDITOR
#endif

using System.Collections.Generic;
using System.IO;
using UnityEngine;
using hashCheck = War.Base.hashCheck; 
//
public class AssetsSync
{
    static AssetsSync instance;

    public static AssetsSync Instance
    {
        get
        {
            if (instance == null)
            {
                instance = new AssetsSync();
            }
            return instance;
        }
    }

    public  string file_url;
    public  string resource_url;
    //public  hashCheck hashRemote;
    //public  hashCheck inst_hashLocal;
    public string m_BaseDownloadingPath = "";

     List<string> sAssetEncrptSets = new List<string>();


    public  void SetEncryptSets()
    {
        sAssetEncrptSets.Clear();
        sAssetEncrptSets.Add("luascript");
        sAssetEncrptSets.Add("configs");
        sAssetEncrptSets.Add("art/characters");
    }

    public  bool IsEncryptType(string fileName)
    {
        foreach (var value in sAssetEncrptSets)
        {
            if (fileName.StartsWith(value))
                return true;
        }
        return false;
    }

     private string GetEncryPath(string path, bool isManifest, string fileName)
    {
        if (!isManifest && IsEncryptType(fileName))
        {
            System.Text.StringBuilder str = new System.Text.StringBuilder();
            str.Append(path);
            str.Append(".zip");
            return str.ToString();
        }
        return path;
    }  

    static public void SaveFile(string path, object bytes, string assetbundleName)
    {
        print("SaveFile:" + path);

        CheckDir(path);
        if (bytes is string)
        {
            File.WriteAllText(path, bytes.ToString());
        }
        else
        {
            File.WriteAllBytes(path, (byte[])bytes);
        }
    } 

    public void UpdateFileTxt(hashCheck hashLocal)
    {
        if (!hashLocal || !hashLocal.isDirty) return;

        string hashlocalJson = ToJson(hashLocal);
        print("UpdateFileTxt:"+ hashLocal.list.Count +" "+ hashlocalJson);
        System.Text.StringBuilder str = new System.Text.StringBuilder();
        str.Append(m_BaseDownloadingPath);
        str.Append("files.txt");
        SaveFile(str.ToString(), hashlocalJson, "files.txt");
        hashLocal.isDirty = false;
    }

    public static void CheckDir(string path)
    {
        var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
    }
    public static string ToJson(object o)
    {
        return Newtonsoft.Json.JsonConvert.SerializeObject(o);
    }
    public static T ToObj<T>(string s)
    {
        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(s);
    }

    public static void print(object message)
    {
        Debug.Log(message);
    } 
}