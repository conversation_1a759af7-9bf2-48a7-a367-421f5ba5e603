using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.SceneManagement;

public class GameReStart : MonoBehaviour
{
    private static GameReStart _instance;
    public static GameReStart Instance => _instance != null ? _instance : InitializeSingleton();

    private static GameReStart InitializeSingleton()
    {
        // 尝试查找现有实例
        _instance = FindObjectOfType<GameReStart>();

        if (_instance == null)
        {
            // 使用预设名称创建新实例
            GameObject singleton = new GameObject("GameReStart_Singleton");
            _instance = singleton.AddComponent<GameReStart>();
            DontDestroyOnLoad(singleton);
        }

        return _instance;
    }

    private readonly List<Action> _callbacks = new List<Action>();

    void Awake()
    {
        // 单例冲突处理
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }

        _instance = this;
        DontDestroyOnLoad(gameObject);
    }

    public void ExecuteCallbacks()
    {
        // 安全执行所有回调
        foreach (var callback in _callbacks.ToArray()) // 使用副本避免修改集合
        {
            try
            {
                callback?.Invoke();
            }
            catch (Exception e)
            {
                Debug.LogError($"Callback execution failed: {e}");
            }
        }


    }

    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.A)) 
        {
            // 根据平台加载场景
            string targetScene =
#if UNITY_EDITOR
                "start1";
#else
            "update";
#endif

            SceneManager.LoadScene(targetScene);
        }
    }

    public void ClearDontDestroyObj()
{
    // 获取DontDestroyOnLoad场景
    Scene ddolScene = gameObject.scene;
    var rootObjects = ddolScene.GetRootGameObjects();

        foreach (var obj in rootObjects)
        {
            if (obj != gameObject)
            {
                Debug.Log($"Destroying object: {obj.name}");
                Destroy(obj);
            }
        }
    }
     public void RegisterCallback(Action callback)
    {
        if (callback == null || _callbacks.Contains(callback)) return;
        _callbacks.Add(callback);
    }

    public void UnregisterCallback(Action callback)
    {
        if (callback != null) _callbacks.Remove(callback);
    }

    void OnDestroy()
    {
        // 清理回调引用
        _callbacks.Clear();

        // 重置单例实例
        if (_instance == this)
        {
            _instance = null;
        }
    }
}