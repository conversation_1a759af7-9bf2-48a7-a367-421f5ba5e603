/*
 * Q1Engine - GPU Scene Material Property Attribute System
 * 
 * File: MaterialPropertyAttribute.cs
 * Description: Material property binding attribute for GPU Scene component-to-shader mapping.
 *              Enables automatic binding of ECS components to shader material properties.
 *              Supports GPU memory size override for optimal buffer layout optimization.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Automatic component-to-shader property binding with string-based mapping
 * - GPU memory size override for optimal buffer alignment and performance
 * - Support for both IComponentData and ISharedComponentData mapping
 * - Multi-attribute support for components mapping to multiple shader properties
 * - Compile-time shader property validation and type safety
 * - Integration with GPU Scene archetype system for batch optimization
 */

using System;

namespace Unity.Rendering
{
    /// <summary>
    /// Marks an IComponentData as an input to a material property on a particular shader.
    /// </summary>
    [AttributeUsage(AttributeTargets.Struct, AllowMultiple = true)]
    public class MaterialPropertyAttribute : Attribute
    {
        /// <summary>
        /// Constructs a material property attribute.
        /// </summary>
        /// <param name="materialPropertyName">The name of the material property.</param>
        /// <param name="overrideSizeGPU">An optional size of the property on the GPU. This is in bytes.</param>
        public MaterialPropertyAttribute(string materialPropertyName, short overrideSizeGPU = -1)
        {
            Name = materialPropertyName;
            OverrideSizeGPU = overrideSizeGPU;
        }

        /// <summary>
        /// The name of the material property.
        /// </summary>
        public string Name { get; }

        /// <summary>
        /// The size of the property in bytes on the GPU.
        /// </summary>
        public short OverrideSizeGPU { get; }
    }
}
