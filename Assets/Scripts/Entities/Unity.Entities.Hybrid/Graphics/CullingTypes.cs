/*
 * Q1Engine - GPU Scene Culling Types and Utilities (Advanced Culling Data Structures)
 * 
 * File: CullingTypes.cs
 * Description: Core data structures and utilities for GPU Scene culling system.
 *              Defines fixed-point camera distance calculations and LOD enablement data structures.
 *              Provides efficient bit-packed LOD representation for optimal GPU memory usage.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Fixed-point camera distance representation with 16-bit precision for memory efficiency
 * - Chunk-level instance LOD enablement using bit-packed arrays for optimal memory usage
 * - High-precision distance calculations with configurable resolution scaling
 * - Efficient bit manipulation for up to 128 instances per chunk with dual 64-bit words
 * - Mathematical utilities for distance quantization with ceiling and floor operations
 * - Cache-optimized data structures for high-performance culling operations
 * - Cross-platform compatibility with consistent numerical precision
 * - Integration with GPU Scene LOD system for seamless rendering pipeline support
 */

using Unity.Mathematics;

namespace Unity.Rendering
{
    /// <summary>
    /// Fixed-point 16-bit camera distance representation for efficient LOD calculations.
    /// Provides high-precision distance quantization with 100x resolution scaling for optimal memory usage.
    /// </summary>
    internal struct Fixed16CamDistance
    {
        /// <summary>Resolution factor for fixed-point conversion (100.0 provides 0.01 unit precision)</summary>
        public const float kRes = 100.0f;

        /// <summary>
        /// Converts floating-point distance to fixed-point with ceiling rounding.
        /// Ensures that the fixed-point value is always greater than or equal to the original value.
        /// </summary>
        /// <param name="f">Floating-point distance value</param>
        /// <returns>16-bit fixed-point representation with ceiling rounding</returns>
        public static ushort FromFloatCeil(float f)
        {
            return (ushort)math.clamp((int)math.ceil(f * kRes), 0, 0xffff);
        }

        /// <summary>
        /// Converts floating-point distance to fixed-point with floor rounding.
        /// Ensures that the fixed-point value is always less than or equal to the original value.
        /// </summary>
        /// <param name="f">Floating-point distance value</param>
        /// <returns>16-bit fixed-point representation with floor rounding</returns>
        public static ushort FromFloatFloor(float f)
        {
            return (ushort)math.clamp((int)math.floor(f * kRes), 0, 0xffff);
        }
    }

    /// <summary>
    /// Bit-packed chunk-level instance LOD enablement structure for efficient memory usage.
    /// Supports up to 128 instances per chunk using dual 64-bit words for optimal cache performance.
    /// Provides high-performance bit manipulation for GPU Scene LOD culling operations.
    /// </summary>
    internal unsafe struct ChunkInstanceLodEnabled
    {
        /// <summary>
        /// Dual 64-bit words supporting up to 128 instances per chunk with bit-packed enablement flags.
        /// Index 0 covers instances 0-63, Index 1 covers instances 64-127.
        /// Each bit represents the LOD enablement state for the corresponding entity instance.
        /// </summary>
        public fixed ulong Enabled[2];
    }
}
