/*
 * Q1Engine - GPU Scene Draw Command Flags Update System (Advanced Render State Management)
 * 
 * File: UpdateDrawCommandFlags.cs
 * Description: High-performance draw command flags update system for GPU Scene chunk processing.
 *              Manages motion vector generation, winding order detection, and per-object render flags.
 *              Features Burst-compiled parallel chunk processing with optimized bit manipulation.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Burst-compiled parallel chunk processing with IJobChunk implementation
 * - Automatic motion vector mode detection with per-object motion flag management
 * - Advanced winding order detection using matrix determinant calculation
 * - Efficient bit-field manipulation with 64-bit masking for entity flags
 * - Render filter settings integration with shared component handling
 * - Chunk-level culling data management with EntitiesGraphicsChunkInfo updates
 * - Transform matrix analysis for proper front-face culling orientation
 * - Memory-efficient batch filter settings lookup with default fallbacks
 */

using Unity.Assertions;
using Unity.Burst;
using Unity.Burst.Intrinsics;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Entities.Graphics;
using Unity.Transforms;
using UnityEngine;
using UnityEngine.Rendering;

#if UNITY_2022_2_OR_NEWER

namespace Unity.Rendering
{
    /// <summary>
    /// High-performance Burst-compiled job for updating draw command flags in GPU Scene chunks.
    /// Processes chunk-level render state including motion vectors and winding order detection.
    /// Optimized for parallel execution with efficient bit manipulation and transform analysis.
    /// </summary>
    [BurstCompile]
    internal unsafe struct UpdateDrawCommandFlagsJob : IJobChunk
    {
        [ReadOnly] public ComponentTypeHandle<LocalToWorld> LocalToWorld;
        [ReadOnly] public SharedComponentTypeHandle<RenderFilterSettings> RenderFilterSettings;
        public ComponentTypeHandle<EntitiesGraphicsChunkInfo> EntitiesGraphicsChunkInfo;

        [ReadOnly] public NativeParallelHashMap<int, BatchFilterSettings> FilterSettings;
        public BatchFilterSettings DefaultFilterSettings;

        /// <summary>
        /// Executes draw command flags update for a single chunk with motion vector and winding order processing.
        /// Analyzes transform matrices to determine per-object motion requirements and correct face culling orientation.
        /// Updates chunk culling data with optimized bit manipulation for GPU Scene rendering pipeline.
        /// </summary>
        public void Execute(in ArchetypeChunk chunk, int unfilteredChunkIndex, bool useEnabledMask, in v128 chunkEnabledMask)
        {
            // This job is not written to support queries with enableable component types.
            Assert.IsFalse(useEnabledMask);

            var chunkInfo = chunk.GetChunkComponentData(ref EntitiesGraphicsChunkInfo);
            Assert.IsTrue(chunkInfo.Valid, "Attempted to process a chunk with uninitialized Hybrid chunk info");

            var localToWorld = chunk.GetNativeArray(ref LocalToWorld);

            // This job runs for all chunks that have structural changes, so if different
            // RenderFilterSettings get set on entities, they should be picked up by
            // the order change filter.
            int filterIndex = chunk.GetSharedComponentIndex(RenderFilterSettings);
            if (!FilterSettings.TryGetValue(filterIndex, out var filterSettings))
                filterSettings = DefaultFilterSettings;

            bool hasPerObjectMotion = filterSettings.motionMode != MotionVectorGenerationMode.Camera;
            if (hasPerObjectMotion)
                chunkInfo.CullingData.Flags |= EntitiesGraphicsChunkCullingData.kFlagPerObjectMotion;
            else
                chunkInfo.CullingData.Flags &= unchecked((byte)~EntitiesGraphicsChunkCullingData.kFlagPerObjectMotion);

            for (int i = 0, chunkEntityCount = chunk.Count; i < chunkEntityCount; i++)
            {
                bool flippedWinding = RequiresFlippedWinding(localToWorld[i]);

                int qwordIndex = i / 64;
                int bitIndex = i % 64;
                ulong mask = 1ul << bitIndex;

                if (flippedWinding)
                    chunkInfo.CullingData.FlippedWinding[qwordIndex] |= mask;
                else
                    chunkInfo.CullingData.FlippedWinding[qwordIndex] &= ~mask;
            }

            chunk.SetChunkComponentData(ref EntitiesGraphicsChunkInfo, chunkInfo);
        }

        /// <summary>
        /// Determines if transform matrix requires flipped winding order based on determinant analysis.
        /// Negative determinant indicates mirrored/inverted transformation requiring reverse face culling.
        /// Essential for correct rendering of scaled or reflected objects in GPU Scene pipeline.
        /// </summary>
        private bool RequiresFlippedWinding(LocalToWorld localToWorld)
        {
            return math.determinant(localToWorld.Value) < 0.0;
        }
    }
}

#endif
