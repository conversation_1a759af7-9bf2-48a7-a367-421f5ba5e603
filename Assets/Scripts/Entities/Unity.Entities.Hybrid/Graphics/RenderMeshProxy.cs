/*
 * Q1Engine - GPU Scene Render Mesh Proxy System (Advanced Entity Rendering)
 * 
 * File: RenderMeshProxy.cs
 * Description: High-performance render mesh proxy system for GPU Scene entity rendering.
 *              Provides unmanaged mesh and material references with optimized sub-mesh indexing.
 *              Features memory-efficient hash calculation and sub-mesh range support.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Unmanaged Unity object references for mesh and material assets
 * - Advanced sub-mesh indexing with SubMeshIndexInfo32 bit-packing
 * - Memory-efficient hash calculation using stack-allocated buffers
 * - XXHash-based fast comparison with collision resistance
 * - Support for baking-time mesh and material assignment
 * - Entity conversion compatibility with GameObject sources
 * - Optimal GPU instancing support with material validation
 * - Sub-mesh range rendering for complex multi-material objects
 */

using System;
using System.Collections.Generic;
using Unity.Assertions;
using Unity.Core;
using Unity.Entities;
using UnityEngine;

namespace Unity.Rendering
{
    /// <summary>
    /// Advanced unmanaged render mesh component for high-performance GPU Scene rendering.
    /// Defines mesh, material, and sub-mesh properties for entity rendering during baking and runtime.
    /// Optimized for GPU instancing with memory-efficient UnityObjectRef storage.
    /// </summary>
    [Serializable]
    //[TemporaryBakingType]
    public struct RenderMeshUnmanaged :IComponentData, IEquatable<RenderMeshUnmanaged>
    {
        /// <summary>
        /// A reference to a [UnityEngine.Mesh](https://docs.unity3d.com/ScriptReference/Mesh.html) object.
        /// </summary>
        public UnityObjectRef<Mesh> mesh;

        /// <summary>
        /// The submesh index, or range of submeshes to render.
        /// </summary>
        internal SubMeshIndexInfo32 subMeshInfo;

        /// <summary>
        /// A reference to a [UnityEngine.Material](https://docs.unity3d.com/ScriptReference/Material.html) object.
        /// </summary>
        /// <remarks>For efficient rendering, the material should enable GPU instancing.
        /// For entities converted from GameObjects, this value is derived from the Materials array of the source
        /// Mesh Renderer Component.
        /// </remarks>
        public UnityObjectRef<Material> materialForSubMesh;

        /// <summary>
        /// Constructs an unmanaged RenderMesh with mesh, material, and sub-mesh configuration.
        /// Optimized for GPU Scene rendering with UnityObjectRef for efficient asset referencing.
        /// </summary>
        /// <param name="mesh">The Mesh asset reference for rendering geometry.</param>
        /// <param name="materialForSubMesh">The material asset reference for the specified sub-mesh.</param>
        /// <param name="subMeshIndex">Optional sub-mesh index within the mesh (default: 0).</param>
        public RenderMeshUnmanaged(
            UnityObjectRef<Mesh> mesh,
            UnityObjectRef<Material> materialForSubMesh = default,
            int subMeshIndex = default)
        {
            Assert.IsTrue(mesh != null, "Must have a non-null Mesh to create RenderMesh.");

            this.mesh = mesh;
            this.materialForSubMesh = materialForSubMesh;
            this.subMeshInfo = new SubMeshIndexInfo32((ushort)subMeshIndex);
        }

        /// <summary>
        /// Internal constructor for advanced sub-mesh configuration with SubMeshIndexInfo32 support.
        /// Enables complex multi-material rendering scenarios with bit-packed sub-mesh indexing.
        /// </summary>
        /// <param name="mesh">The Mesh asset reference for rendering geometry.</param>
        /// <param name="materialForSubMesh">The material asset reference for the specified sub-mesh.</param>
        /// <param name="subMeshInfo">Advanced sub-mesh information with range and indexing support.</param>
        internal RenderMeshUnmanaged(
            UnityObjectRef<Mesh> mesh,
            UnityObjectRef<Material> materialForSubMesh = default,
            SubMeshIndexInfo32 subMeshInfo = default)
        {
            Assert.IsTrue(mesh != null, "Must have a non-null Mesh to create RenderMesh.");

            this.mesh = mesh;
            this.materialForSubMesh = materialForSubMesh;
            this.subMeshInfo = subMeshInfo;
        }

        /// <summary>
        /// Determines equality between RenderMeshUnmanaged instances based on mesh, material, and sub-mesh properties.
        /// Uses efficient UnityObjectRef comparison for optimal performance in GPU Scene batching.
        /// </summary>
        /// <param name="other">Another RenderMeshUnmanaged instance to compare.</param>
        /// <returns>True if all properties (mesh, material, sub-mesh info) are equal.</returns>
        public bool Equals(RenderMeshUnmanaged other)
        {
            return
                mesh == other.mesh &&
                materialForSubMesh == other.materialForSubMesh &&
                subMeshInfo == other.subMeshInfo;
        }

        /// <summary>
        /// Calculates optimized hash code using XXHash algorithm for efficient RenderMeshUnmanaged comparison.
        /// Uses stack-allocated buffer for zero-allocation hash computation in GPU Scene systems.
        /// </summary>
        /// <returns>Consistent hash code for identical RenderMeshUnmanaged configurations.</returns>
        public override int GetHashCode()
        {
            int hash = 0;

            unsafe
            {
                var buffer = stackalloc[]
                {
                    ReferenceEquals(mesh, null) ? 0 : mesh.GetHashCode(),
                    ReferenceEquals(materialForSubMesh, null) ? 0 : materialForSubMesh.GetHashCode(),
                    subMeshInfo.GetHashCode(),
                };

                hash = (int)XXHash.Hash32((byte*)buffer, 3 * 4);
            }

            return hash;
        }
    }

}
