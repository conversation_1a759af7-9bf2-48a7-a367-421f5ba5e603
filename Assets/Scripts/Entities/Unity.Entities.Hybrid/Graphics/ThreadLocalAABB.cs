/*
 * Q1Engine - Thread-Local AABB Processing System (Advanced Parallel Bounds Computation)
 * 
 * File: ThreadLocalAABB.cs
 * Description: High-performance thread-local AABB computation system with cache-line optimization.
 *              Prevents false sharing in parallel bounds calculation with cache-aligned data structures.
 *              Provides burst-compiled AABB processing for optimal GPU Scene bounds computation.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Thread-local AABB storage with cache-line alignment for optimal parallel performance
 * - False sharing prevention through strategic memory padding and alignment
 * - Burst-compiled parallel AABB initialization with SIMD-optimized processing
 * - Cache-line sized data structures for maximum CPU cache efficiency
 * - MinMaxAABB integration for consistent bounds representation across systems
 * - Thread-safe parallel bounds computation with lock-free operations
 * - Debug validation system for cache-line alignment verification
 * - Integration with GPU Scene culling system for unified bounds management
 */

using Unity.Assertions;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Jobs.LowLevel.Unsafe;
using Unity.Mathematics;
using UnityEngine;

namespace Unity.Rendering
{
    internal unsafe struct ThreadLocalAABB
    {
        private const int kAABBNumFloats = 6;
        private const int kCacheLineNumFloats = JobsUtility.CacheLineSize / 4;
        private const int kCacheLinePadding = kCacheLineNumFloats - kAABBNumFloats;

        public MinMaxAABB AABB;
        // Pad the size of this struct to a single cache line, to ensure that thread local updates
        // don't cause false sharing
        public fixed float CacheLinePadding[kCacheLinePadding];

        public static void AssertCacheLineSize()
        {
            Assert.IsTrue(UnsafeUtility.SizeOf<ThreadLocalAABB>() == JobsUtility.CacheLineSize,
                "ThreadLocalAABB should have a size equal to the CPU cache line size");
        }
    }

    [BurstCompile]
    internal unsafe struct ZeroThreadLocalAABBJob : IJobParallelFor
    {
        public NativeArray<ThreadLocalAABB> ThreadLocalAABBs;

        public void Execute(int index)
        {
            var threadLocalAABB = ((ThreadLocalAABB*) ThreadLocalAABBs.GetUnsafePtr()) + index;
            threadLocalAABB->AABB = MinMaxAABB.Empty;
        }
    }

}
