/*
 * Q1Engine - Lua Entity System Base (HybridCLR Foundation Framework)
 * 
 * File: LuaEntitySystemBase.cs
 * Description: Foundation base class for Lua-integrated entity systems with HybridCLR seamless execution.
 *              Provides unified job scheduling and dependency management for Lua-C# hybrid systems.
 *              Features optimized update loop with profiler integration and shared dependency coordination.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - HybridCLR-optimized base class for Lua entity systems with zero overhead integration
 * - Unified job dependency management across all Lua entity systems for optimal scheduling
 * - Shared dependency tracking to prevent job conflicts and ensure proper execution order
 * - Built-in profiler integration for performance monitoring of Lua-C# hybrid execution
 * - Abstract update interface for consistent system implementation patterns
 * - Thread-safe job handle coordination for parallel Lua system execution
 * - Automatic system ordering and dependency resolution for complex Lua workflows
 * - Memory-efficient system state management with minimal overhead
 */

using UnityEngine.Jobs;
using Unity.Jobs;
using UnityEngine;

namespace Unity.Entities
{
    /// <summary>
    /// Foundation base class for HybridCLR Lua-integrated entity systems.
    /// Provides unified job scheduling and dependency management for seamless Lua-C# execution.
    /// Features optimized update patterns and shared dependency coordination.
    /// </summary>
    public abstract unsafe class LuaEntitySystemBase : ComponentSystemBase
    {
        /// <summary>
        /// Shared job dependency handle for coordinated Lua system execution.
        /// Ensures proper job scheduling across all HybridCLR Lua entity systems.
        /// </summary>
        static JobHandle s_LastDependency;
        
        /// <summary>
        /// HybridCLR-optimized update method with profiler integration and shared dependency management.
        /// Provides unified execution pattern for all Lua entity systems.
        /// </summary>
        public override void Update()
        {

#if UNITY_2022_2_OR_NEWER && ENABLE_PROFILER
            var state = CheckedState();
            using (state->m_ProfilerMarker.Auto())
#endif
            {
                if (Enabled)
                    s_LastDependency = OnUpdate(s_LastDependency);
            }

        }

        /// <summary>
        /// Abstract update method for HybridCLR Lua entity system implementation.
        /// Receives shared dependency handle and returns updated dependency for next system.
        /// </summary>
        /// <param name="inputDeps">Input job dependencies from previous systems</param>
        /// <returns>Output job dependencies for subsequent systems</returns>
        protected abstract JobHandle OnUpdate(JobHandle inputDeps);
    }
}
