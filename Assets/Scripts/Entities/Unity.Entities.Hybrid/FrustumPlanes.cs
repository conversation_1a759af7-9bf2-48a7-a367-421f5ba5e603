/*
 * Q1Engine - Frustum Culling System (Advanced SIMD-Optimized Culling)
 * 
 * File: FrustumPlanes.cs
 * Description: High-performance frustum culling system with SIMD-optimized parallel plane intersection tests.
 *              Features Structure-of-Arrays plane packing for vectorized AABB and sphere intersection calculations.
 *              Provides burst-compatible culling operations with cache-friendly data layouts.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - SIMD-optimized plane packet processing with 4-wide vectorization
 * - Structure-of-Arrays (SOA) plane data layout for maximum SIMD efficiency
 * - Burst-compatible AABB and sphere intersection tests with aggressive inlining
 * - High-performance parallel culling with cache-friendly memory access patterns
 * - Unity Camera integration with automatic frustum plane extraction
 * - Vectorized dot product calculations using float4 SIMD operations
 * - Support for partial intersection detection for fine-grained culling
 * - Memory-efficient plane packet storage with optimal cache-line utilization
 */

using System;
using System.Runtime.CompilerServices;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;

namespace Unity.Entities
{
    /// <summary>
    /// Advanced frustum culling system with SIMD-optimized plane intersection tests.
    /// Provides high-performance AABB and sphere culling using Structure-of-Arrays plane packing.
    /// Features vectorized calculations and burst-compatible operations for optimal GPU Scene culling.
    /// </summary>
    public struct FrustumPlanes
    {
        /// <summary>
        /// Intersection test result enumeration for precise culling classification.
        /// Enables fine-grained culling decisions and partial visibility handling.
        /// </summary>
        public enum IntersectResult
        {
            /// <summary>Object is completely outside the frustum (culled)</summary>
            Out,
            /// <summary>Object is completely inside the frustum (fully visible)</summary>
            In,
            /// <summary>Object partially intersects the frustum (requires per-instance culling)</summary>
            Partial
        };

        /// <summary>
        /// Extracts frustum planes from Unity Camera and converts to float4 format for SIMD processing.
        /// Automatically calculates six frustum planes (left, right, bottom, top, near, far) with proper normals.
        /// Optimized for burst-compiled culling operations with vectorized plane storage.
        /// </summary>
        /// <param name="camera">Source Unity Camera for frustum extraction</param>
        /// <param name="planes">Output array of 6 float4 planes (normal.xyz + distance.w)</param>
        static public void FromCamera(Camera camera, NativeArray<float4> planes)
        {
            Plane[] sourcePlanes = GeometryUtility.CalculateFrustumPlanes(camera);

            for (int i = 0; i < 6; ++i)
            {
                planes[i] = new float4(sourcePlanes[i].normal.x, sourcePlanes[i].normal.y, sourcePlanes[i].normal.z,
                    sourcePlanes[i].distance);
            }
        }

        /// <summary>
        /// Extracts frustum planes from Unity Camera using pre-allocated source array for garbage-free operation.
        /// Provides high-performance frustum extraction with minimal memory allocation overhead.
        /// </summary>
        /// <param name="camera">Source Unity Camera for frustum extraction</param>
        /// <param name="planes">Output NativeArray of Unity Plane structures</param>
        /// <param name="sourcePlanes">Pre-allocated source array to avoid GC allocations</param>
        static public void FromCamera(Camera camera, NativeArray<Plane> planes, Plane[] sourcePlanes)
        {
            GeometryUtility.CalculateFrustumPlanes(camera, sourcePlanes);

            for (int i = 0; i < 6; ++i)
            {
                planes[i] = new Plane(sourcePlanes[i].normal,
                    sourcePlanes[i].distance);
            }
        }

        /// <summary>
        /// Performs high-performance AABB vs frustum intersection test with partial intersection detection.
        /// Uses optimized plane-AABB distance calculations for precise culling classification.
        /// Supports partial intersection results for hierarchical and per-instance culling scenarios.
        /// </summary>
        /// <param name="cullingPlanes">Array of frustum planes in float4 format (normal.xyz + distance.w)</param>
        /// <param name="a">Axis-aligned bounding box to test against frustum</param>
        /// <returns>Intersection result: Out (culled), In (fully visible), or Partial (needs instance culling)</returns>
        static public IntersectResult Intersect(NativeArray<float4> cullingPlanes, AABB a)
        {
            float3 m = a.Center;
            float3 extent = a.Extents;

            var inCount = 0;
            for (int i = 0; i < cullingPlanes.Length; i++)
            {
                float3 normal = cullingPlanes[i].xyz;
                float dist = math.dot(normal, m) + cullingPlanes[i].w;
                float radius = math.dot(extent, math.abs(normal));
                if (dist + radius <= 0)
                    return IntersectResult.Out;
                
                if (dist > radius)
                    inCount++;
                
            }
            
            return (inCount == cullingPlanes.Length) ? IntersectResult.In : IntersectResult.Partial;
        }

        /// <summary>
        /// SIMD-optimized Structure-of-Arrays plane packet for vectorized culling operations.
        /// Stores 4 planes in separate X, Y, Z, and distance vectors for maximum SIMD efficiency.
        /// Enables 4-wide parallel plane intersection tests with single SIMD instructions.
        /// </summary>
        public struct PlanePacket4
        {
            /// <summary>X components of 4 plane normals packed for SIMD processing</summary>
            public float4 Xs;
            /// <summary>Y components of 4 plane normals packed for SIMD processing</summary>
            public float4 Ys;
            /// <summary>Z components of 4 plane normals packed for SIMD processing</summary>
            public float4 Zs;
            /// <summary>Distance values of 4 planes packed for SIMD processing</summary>
            public float4 Distances;
        }

        /// <summary>
        /// Builds SIMD-optimized Structure-of-Arrays plane packets from Unity Plane array.
        /// Converts traditional Array-of-Structures plane data to SOA format for vectorized processing.
        /// Automatically pads remaining slots with "always-in" planes for consistent 4-wide SIMD operations.
        /// </summary>
        /// <param name="cullingPlanes">Source array of Unity Plane structures</param>
        /// <param name="allocator">Memory allocator for the resulting plane packets</param>
        /// <returns>NativeArray of SIMD-optimized PlanePacket4 structures</returns>
        public static NativeArray<PlanePacket4> BuildSOAPlanePackets(NativeArray<Plane> cullingPlanes, Allocator allocator)
        {
            int cullingPlaneCount = cullingPlanes.Length;
            int packetCount = (cullingPlaneCount + 3) >> 2;
            var planes = new NativeArray<PlanePacket4>(packetCount, allocator, NativeArrayOptions.UninitializedMemory);

            for (int i = 0; i < cullingPlaneCount; i++)
            {
                var p = planes[i >> 2];
                p.Xs[i & 3] = cullingPlanes[i].normal.x;
                p.Ys[i & 3] = cullingPlanes[i].normal.y;
                p.Zs[i & 3] = cullingPlanes[i].normal.z;
                p.Distances[i & 3] = cullingPlanes[i].distance;
                planes[i >> 2] = p;
            }

            // Populate the remaining planes with values that are always "in"
            for (int i = cullingPlaneCount; i < 4 * packetCount; ++i)
            {
                var p = planes[i >> 2];
                p.Xs[i & 3] = 1.0f;
                p.Ys[i & 3] = 0.0f;
                p.Zs[i & 3] = 0.0f;
                p.Distances[i & 3] = 32786.0f; //float.MaxValue;
                planes[i >> 2] = p;
            }

            return planes;
        }

        /// <summary>
        /// Builds SIMD-optimized Structure-of-Arrays plane packets into pre-allocated output array.
        /// Garbage-free version that reuses existing PlanePacket4 array for optimal performance.
        /// Ideal for high-frequency culling operations where memory allocation must be minimized.
        /// </summary>
        /// <param name="cullingPlanes">Source array of Unity Plane structures</param>
        /// <param name="planes">Pre-allocated output array for PlanePacket4 structures</param>
        public static void BuildSOAPlanePackets(NativeArray<Plane> cullingPlanes, NativeArray<PlanePacket4> planes)
        {
            int cullingPlaneCount = cullingPlanes.Length;
            int packetCount = (cullingPlaneCount + 3) >> 2;

            for (int i = 0; i < cullingPlaneCount; i++)
            {
                var p = planes[i >> 2];
                p.Xs[i & 3] = cullingPlanes[i].normal.x;
                p.Ys[i & 3] = cullingPlanes[i].normal.y;
                p.Zs[i & 3] = cullingPlanes[i].normal.z;
                p.Distances[i & 3] = cullingPlanes[i].distance;
                planes[i >> 2] = p;
            }

            // Populate the remaining planes with values that are always "in"
            for (int i = cullingPlaneCount; i < 4 * packetCount; ++i)
            {
                var p = planes[i >> 2];
                p.Xs[i & 3] = 1.0f;
                p.Ys[i & 3] = 0.0f;
                p.Zs[i & 3] = 0.0f;
                p.Distances[i & 3] = 32786.0f; //float.MaxValue;
                planes[i >> 2] = p;
            }
        }

        /// <summary>
        /// High-performance SIMD-optimized AABB vs frustum intersection test using PlanePacket4 format.
        /// Processes 4 planes simultaneously with vectorized operations for maximum throughput.
        /// Supports partial intersection detection for hierarchical culling and LOD selection.
        /// </summary>
        /// <param name="cullingPlanePackets">SIMD-optimized plane packets for vectorized testing</param>
        /// <param name="a">Axis-aligned bounding box to test against frustum</param>
        /// <returns>Intersection result with partial intersection support for fine-grained culling</returns>
        static public IntersectResult Intersect2(NativeArray<PlanePacket4> cullingPlanePackets, AABB a)
        {
            float4 mx = a.Center.xxxx;
            float4 my = a.Center.yyyy;
            float4 mz = a.Center.zzzz;

            float4 ex = a.Extents.xxxx;
            float4 ey = a.Extents.yyyy;
            float4 ez = a.Extents.zzzz;

            int4 outCounts = 0;
            int4 inCounts = 0;

            for (int i = 0; i < cullingPlanePackets.Length; i++)
            {
                var p = cullingPlanePackets[i];
                float4 distances = dot4(p.Xs, p.Ys, p.Zs, mx, my, mz) + p.Distances;
                float4 radii = dot4(ex, ey, ez, math.abs(p.Xs), math.abs(p.Ys), math.abs(p.Zs));

                outCounts += (int4) (distances + radii <= 0);
                inCounts += (int4) (distances > radii);
            }

            int inCount = math.csum(inCounts);
            int outCount = math.csum(outCounts);

            if (outCount != 0)
                return IntersectResult.Out;
            else
                return (inCount == 4 * cullingPlanePackets.Length) ? IntersectResult.In : IntersectResult.Partial;
        }

        /// <summary>
        /// Optimized SIMD AABB vs frustum test without partial intersection detection.
        /// Simplified version for cases where only In/Out classification is needed.
        /// Provides maximum performance for binary culling decisions without hierarchical processing.
        /// </summary>
        /// <param name="cullingPlanePackets">SIMD-optimized plane packets for vectorized testing</param>
        /// <param name="a">Axis-aligned bounding box to test against frustum</param>
        /// <returns>Binary intersection result (In or Out only, no Partial)</returns>
        static public IntersectResult Intersect2NoPartial(NativeArray<PlanePacket4> cullingPlanePackets, AABB a)
        {
            float4 mx = a.Center.xxxx;
            float4 my = a.Center.yyyy;
            float4 mz = a.Center.zzzz;

            float4 ex = a.Extents.xxxx;
            float4 ey = a.Extents.yyyy;
            float4 ez = a.Extents.zzzz;

            int4 masks = 0;

            for (int i = 0; i < cullingPlanePackets.Length; i++)
            {
                var p = cullingPlanePackets[i];
                float4 distances = dot4(p.Xs, p.Ys, p.Zs, mx, my, mz) + p.Distances;
                float4 radii = dot4(ex, ey, ez, math.abs(p.Xs), math.abs(p.Ys), math.abs(p.Zs));

                masks += (int4) (distances + radii <= 0);
            }

            int outCount = math.csum(masks);
            return outCount > 0 ? IntersectResult.Out : IntersectResult.In;
        }

        /// <summary>
        /// Vectorized 4-wide dot product calculation for SIMD plane distance computation.
        /// Computes dot products of 4 plane normals with a point simultaneously using SIMD operations.
        /// Fundamental building block for high-performance vectorized culling algorithms.
        /// </summary>
        /// <param name="xs">X components of 4 plane normals</param>
        /// <param name="ys">Y components of 4 plane normals</param>
        /// <param name="zs">Z components of 4 plane normals</param>
        /// <param name="mx">X coordinate replicated 4 times</param>
        /// <param name="my">Y coordinate replicated 4 times</param>
        /// <param name="mz">Z coordinate replicated 4 times</param>
        /// <returns>float4 containing 4 dot product results</returns>
        public static float4 dot4(float4 xs, float4 ys, float4 zs, float4 mx, float4 my, float4 mz)
        {
            return xs * mx + ys * my + zs * mz;
        }

        /// <summary>
        /// High-performance sphere vs frustum intersection test for bounding sphere culling.
        /// Uses optimized plane-sphere distance calculations for precise culling classification.
        /// Ideal for LOD selection and coarse-grained culling of spherical bounds.
        /// </summary>
        /// <param name="planes">Array of frustum planes in float4 format</param>
        /// <param name="center">Sphere center position</param>
        /// <param name="radius">Sphere radius for intersection testing</param>
        /// <returns>Intersection result with partial support for hierarchical culling</returns>
        static public IntersectResult Intersect(NativeArray<float4> planes, float3 center, float radius)
        {
            var inCount = 0;

            for (int i = 0; i < planes.Length; i++)
            {
                var d = math.dot(planes[i].xyz, center) + planes[i].w;
                if (d < -radius)
                {
                    return IntersectResult.Out;
                }

                if (d > radius)
                {
                    inCount++;
                }
            }

            return (inCount == planes.Length) ? IntersectResult.In : IntersectResult.Partial;
        }

        /// <summary>
        /// SIMD-optimized sphere vs frustum test without partial intersection detection.
        /// Processes 4 planes simultaneously for maximum performance in binary culling scenarios.
        /// Optimized for high-throughput sphere culling where hierarchical processing is not needed.
        /// </summary>
        /// <param name="cullingPlanePackets">SIMD-optimized plane packets</param>
        /// <param name="center">Sphere center position</param>
        /// <param name="radius">Sphere radius for intersection testing</param>
        /// <returns>Binary intersection result (In or Out only)</returns>
        static public IntersectResult Intersect2NoPartial(NativeArray<PlanePacket4> cullingPlanePackets, float3 center, float radius)
        {
            float4 mx = center.xxxx;
            float4 my = center.yyyy;
            float4 mz = center.zzzz;

            float4 radius4 = new float4(radius, radius, radius, radius);

            int4 masks = 0;

            for (int i = 0; i < cullingPlanePackets.Length; i++)
            {
                var p = cullingPlanePackets[i];
                float4 distances = dot4(p.Xs, p.Ys, p.Zs, mx, my, mz) + p.Distances;
                float4 radii = dot4(radius4, math.abs(p.Xs), math.abs(p.Ys), math.abs(p.Zs));

                masks += (int4)(distances + radii <= 0);
            }

            int outCount = math.csum(masks);
            return outCount > 0 ? IntersectResult.Out : IntersectResult.In;
        }

        /// <summary>
        /// Ultra-optimized single PlanePacket4 sphere intersection test with aggressive inlining.
        /// Designed for maximum performance in tight culling loops with minimal function call overhead.
        /// Uses reference parameter to avoid PlanePacket4 copying for optimal cache performance.
        /// </summary>
        /// <param name="cullingPlanePackets">Reference to single SIMD plane packet</param>
        /// <param name="center">Sphere center position</param>
        /// <param name="radius">Sphere radius for intersection testing</param>
        /// <returns>Binary intersection result (In or Out only)</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        static public IntersectResult Intersect2NoPartial(ref PlanePacket4 cullingPlanePackets, float3 center, float radius)
        {
            float4 mx = center.xxxx;
            float4 my = center.yyyy;
            float4 mz = center.zzzz;

            float4 radius4 = new float4(radius, radius, radius, radius);

            int4 masks = 0;

            {
                float4 distances = dot4(cullingPlanePackets.Xs, cullingPlanePackets.Ys, cullingPlanePackets.Zs, mx, my, mz) + cullingPlanePackets.Distances;
                float4 radii = dot4(radius4, math.abs(cullingPlanePackets.Xs), math.abs(cullingPlanePackets.Ys), math.abs(cullingPlanePackets.Zs));

                masks += (int4)(distances + radii <= 0);
            }

            int outCount = math.csum(masks);
            return outCount > 0 ? IntersectResult.Out : IntersectResult.In;
        }


        /// <summary>
        /// Vectorized radius calculation for 4-wide sphere intersection tests.
        /// Computes effective radius distances for multiple spheres simultaneously using SIMD operations.
        /// Used in optimized sphere culling algorithms for batch processing multiple bounds.
        /// </summary>
        /// <param name="radius4">Radius values replicated 4 times for SIMD processing</param>
        /// <param name="mx">X components of 4 vectors</param>
        /// <param name="my">Y components of 4 vectors</param>
        /// <param name="mz">Z components of 4 vectors</param>
        /// <returns>float4 containing 4 computed radius distances</returns>
        public static float4 dot4(float4 radius4, float4 mx, float4 my, float4 mz)
        {
            return radius4 * mx + radius4 * my + radius4 * mz;
        }

    }

}
