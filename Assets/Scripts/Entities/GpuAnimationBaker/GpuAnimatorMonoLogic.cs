/*
 * Q1Engine - GPU Animator MonoBehaviour Logic with Hot-reload Support
 * 
 * File: GpuAnimatorMonoLogic.cs
 * Description: MonoBehaviour wrapper for GPU animation with hybrid ECS/MonoBehaviour architecture.
 *              Provides seamless transition between traditional Unity and ECS workflows.
 *              Features hot-reload support, material effects, and attack loop timing control.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Hybrid ECS/MonoBehaviour animation system with automatic fallback
 * - Hot-reload support through ScriptConnector for runtime code updates
 * - Advanced material effects (dissolve, self-lighting, shadow control)
 * - Attack loop interval system for gameplay mechanics
 * - Material property block optimization for performance
 * - Automatic entity lifecycle management and cleanup
 * - Runtime material switching and keyword management
 */

using System;
using System.Collections;
using FBoard;
using Unity.Entities;
using Unity.Mathematics;
using UnityEngine;
using War.Base;
using Object = UnityEngine.Object;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Collections;
using System.Collections.Generic;

namespace GPUAnimationBaker.Engine
{
    /// <summary>
    /// Shader property ID cache for GPU animation material properties.
    /// Provides pre-cached shader property IDs for efficient material property setting.
    /// Reduces runtime string-to-ID conversion overhead for frequently used material properties.
    /// </summary>
    public class ShaderIDs
    {
        /// <summary>Shader property ID for self-illumination color with packed RGBA values</summary>
        public static int SelfLightColor = Shader.PropertyToID("_SelfLightColor");
        /// <summary>Shader property ID for self-illumination intensity factor</summary>
        public static int SelfLightness = Shader.PropertyToID("_SelfLightness");
    }

    /// <summary>
    /// Advanced GPU animator logic class with hot-reload support and hybrid ECS/MonoBehaviour architecture.
    /// Provides seamless GPU animation with automatic system fallback and runtime code update capabilities.
    /// Features ScriptConnector integration for hot-reload development workflow and material effects.
    /// </summary>
    [AutoRegisterMethod(Id = "GpuAnimatorMonoLogic")]
    //--Todo：考虑到转移脚本到CSharp工程里数据需要重新导出，后续还要跟ECS联合开发，这里先用反射过渡，后续再迁移并批量重导数据
    public class GpuAnimatorMonoLogic
    {
        public int totalNbrOfFrames;
        public GpuAnimationData[] animations;
        public string[] stateNames;

        GpuAnimatorStateComponent gpuAnimatorState;
        //GpuAnimatorControlStates controlState;

        GpuAnimatorControl animControl;
        int attackID;

        public int animatorDataGuid;
        GpuAnimMeshComponent[] meshComponents;

        MaterialPropertyBlock propertyBlock;

        MeshRenderer[] renders;

        private RuntimeEcsMaterial[][] runtimeECSmats;
        Material[][] cacheModifiedMats;
        Material[][] cacheECSMats;

        static int AnimationStateID = Shader.PropertyToID("_AnimationState");
        float4 animationStateParam;

        //特殊效果需求，参数私有保护一下
        #region 溶解&&定时攻击循环
        static int dissolveID = Shader.PropertyToID("_SliceAmount");
        static int shadowID = Shader.PropertyToID("_Shadow_Color");
        bool hasReplaceMatByCopy = false;
        bool needDelayRefreshOnce = false;


        Entity entity;
        TransformAccessEntity transEntity;
        //int[] rendererHashCodes;
        #endregion

        SkinMeshSystem skinMeshSystem;

        StructuralChangePresentationSystemGroup structuralChangePresentationSystemGroup;
        #region 热更支持逻辑
        private GpuAnimatorMonoAsset gpuAniAsset;

        public ScriptConnector connector;
        public string name;
        public GameObject gameObject;
        public Transform transform;

        private int m_Layer;
        /// <summary>
        /// Creates GPU animator instance with ScriptConnector integration for hot-reload support.
        /// Initializes animation data from GpuAnimatorMonoAsset and establishes GameObject references.
        /// Provides error handling for missing or mismatched asset types with detailed logging.
        /// </summary>
        /// <param name="obj">ScriptConnector object containing GameObject and asset references</param>
        /// <returns>Configured GpuAnimatorMonoLogic instance ready for animation playback</returns>
        public static object CreateInstance(object obj)
        {
            GpuAnimatorMonoLogic ins = new GpuAnimatorMonoLogic();
            ins.connector = (ScriptConnector)obj;
            ins.gameObject = ins.connector.gameObject;
            ins.transform = ins.connector.transform;
            ins.name = ins.connector.name;

            Object assetObj = ins.connector.GetObject("gpuAniAsset");
            if (assetObj is GpuAnimatorMonoAsset gpuAniAsset)
            {
                ins.gpuAniAsset = gpuAniAsset;
                ins.totalNbrOfFrames = gpuAniAsset.totalNbrOfFrames;
                ins.animatorDataGuid = gpuAniAsset.animatorDataGuid;
                ins.animations = gpuAniAsset.animations;
                ins.stateNames = gpuAniAsset.stateNames;
            }
            else
            {
                if (assetObj == null)
                {
                    Debug.LogError($"assetObj is null for fieldName: gpuAniAsset, gameObject: {ins.connector.gameObject.name}");
                }
                else
                {
                    Debug.LogError($"assetObj type(GpuAnimatorMonoAsset) mismatch for fieldName: gpuAniAsset, gameObject: {ins.connector.gameObject.name}");
                }
            }
            return ins;
        }

        public bool enabled
        {
            get
            {
                if (connector != null)
                    return connector.enabled;

                return false;
            }
            set
            {
                if (connector != null)
                    connector.enabled = value;
            }
        }
        #endregion

        /// <summary>
        /// Sets attack animation loop interval for gameplay-specific timing control.
        /// Configures whether attack animations should use fixed intervals instead of natural looping.
        /// Supports both ECS entity and traditional MonoBehaviour animation systems.
        /// </summary>
        /// <param name="useLoopInterval">Whether to use fixed interval timing for attack loops</param>
        /// <param name="attackInterval">Time interval between attack loop repetitions in seconds</param>
        public void SetAttackLoopInterval(bool useLoopInterval, float attackInterval)
        {
            if (entity != Entity.Null)
            {
                EntityHybridUtility.PlayStateLoopInterval(entity, attackID, useLoopInterval, attackInterval);
            }
            else
            {
                animControl.useAttackLoopInterval = useLoopInterval;
                animControl.attackLoopInterval = attackInterval;
                //animations[animControl.attackID].loop = !useLoopInterval;
                if (useLoopInterval)
                    SetAnimatorState(animControl.attackID);
                else
                {
                    SetAnimatorState(0);
                }
            }
        }

        /// <summary>
        /// Sets current animation state with automatic normalized time reset for state changes.
        /// Supports both ECS entity and traditional MonoBehaviour animation systems.
        /// Validates state ID bounds and resets timing only when transitioning to different states.
        /// </summary>
        /// <param name="id">Animation state index to transition to</param>
        public void SetAnimatorState(int id)
        {
            if (animations == null ||  id >= animations.Length || id < 0)
            {
                return;
            }

            if(entity != Entity.Null)
            {
                EntityHybridUtility.PlayAnimationState(entity, id);
            }
            else
            {
                //切换了状态才重置时间
                if (animControl.animationID != id)
                {
                    gpuAnimatorState.currentNormalizedTime = 0;
                    animControl.animationID = id;
                }

                gpuAnimatorState.stoppedCurrent = false;
            }
        }

        private void InitMaterials()
        {
            runtimeECSmats = new RuntimeEcsMaterial[renders.Length][];
            cacheECSMats = new Material[renders.Length][];
            for (int i = 0; i < renders.Length; i++)
            {
                var mats = renders[i].sharedMaterials;
                cacheECSMats[i] = mats;
                runtimeECSmats[i] = new RuntimeEcsMaterial[mats.Length];
                GenRuntimeECSMaterial(mats, runtimeECSmats[i]);
            }
        }

        private void InitModifiedMats()
        {
            if (runtimeECSmats == null)
            {
                InitMaterials();
            }
            cacheModifiedMats = new Material[renders.Length][];
            for (int i = 0; i < runtimeECSmats.Length; i++)
            {
                cacheModifiedMats[i] = new Material[runtimeECSmats[i].Length];
                for (int j = 0; j < runtimeECSmats[i].Length; j++)
                {
                    var copyMat = new Material(runtimeECSmats[i][j].material);
                    if (!runtimeECSmats[i][j].isShadow)
                    {
                        copyMat.EnableKeyword("DISSOLVE_ON");
                    }
                    cacheModifiedMats[i][j] = copyMat;
                }
                //cacheModifiedMats[i] = Array.ConvertAll(runtimeECSmats[i], r => new Material(r.material) );
            }
        }

        private void GenRuntimeECSMaterial(Material[] mats, RuntimeEcsMaterial[] ecsMaterials)
        {
            var len = mats.Length;
            if (ecsMaterials == null)
            {
                ecsMaterials = new RuntimeEcsMaterial[len];
            }
            for (int j = 0; j < mats.Length; j++)
            {
                var isShadow = mats[j].shader.name.Contains("Shadow");
                ecsMaterials[j] = new RuntimeEcsMaterial { isShadow = isShadow, material = new Material(mats[j]) };
            }
            return;
        }

        /// <summary>
        /// Sets dissolve effect value with automatic material switching and shadow color adjustment.
        /// Manages material replacement between original and dissolve-enabled versions for performance.
        /// Updates material property block with dissolve amount and corresponding shadow opacity.
        /// </summary>
        /// <param name="dissolve">Dissolve amount from 0 (solid) to 1 (fully dissolved)</param>
        public void SetDissolve(float dissolve)
        {
            if (entity != Entity.Null)
            {
                return;
            }

            if (cacheModifiedMats == null)
            {
                InitModifiedMats();
            }


            if (dissolve > 0)//&& dissolve <=1原本客户端代码不加保护
            {
                //for (int i = 0;i< runtimeECSmats.Length;i++)//因为缓存了材质数组，不用每次都去设关键字
                //{
                //    for(int j = 0; j< runtimeECSmats[i].Length; j++)
                //    {
                //        if (!runtimeECSmats[i][j].isShadow)
                //        {
                //            cacheModifiedMats[i][j].EnableKeyword("DISSOLVE_ON");
                //        }
                //    }
                //}


                if (!hasReplaceMatByCopy)
                {
                    for (int i = 0; i < renders.Length; i++)
                    {
                        renders[i].sharedMaterials = cacheModifiedMats[i];
                    }
                    hasReplaceMatByCopy = true;
                    needDelayRefreshOnce = true;
                }
            }
            else
            {
                //for (int i = 0; i < runtimeECSmats.Length; i++)
                //{
                //    for (int j = 0; j < runtimeECSmats[i].Length; j++)
                //    {
                //        if (runtimeECSmats[i][j].isShadow)
                //        {
                //            cacheModifiedMats[i][j].DisableKeyword("DISSOLVE_ON");
                //        }
                //    }
                //}
                if (hasReplaceMatByCopy)
                {
                    for (int i = 0; i < renders.Length; i++)
                    {
                        renders[i].sharedMaterials = cacheECSMats[i];
                    }
                    hasReplaceMatByCopy = false;
                    needDelayRefreshOnce = true;//这里溶解以及结束，但是还没能开始更新MPB，需要延迟更新一次
                }

                dissolve = 0;

            }

            propertyBlock.SetFloat(dissolveID, dissolve);
            float value = (1 - dissolve) * 0.4f;
            propertyBlock.SetColor(shadowID, new Color(0, 0, 0, value));
        }

        public void SetRenderPropertiesFloat(string propertie, float value)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetFloat(propertie, value);
            }
        }

        public void SetRenderPropertiesColor(string propertie, Color color)
        {
            if (propertyBlock != null)
            {
                propertyBlock.SetColor(propertie, color);
            }
        }
        /// <summary>
        /// Sets material float property with special handling for self-light color packed values.
        /// Supports both ECS entity and traditional MonoBehaviour material property systems.
        /// Automatically unpacks self-light color from double to RGBA components with proper scaling.
        /// </summary>
        /// <param name="shaderID">Shader property ID to set</param>
        /// <param name="value">Value to set, with special packed color handling for self-light</param>
        public void SetMaterialFloat(int shaderID, double value)
        {
            if (entity != Entity.Null)
            {
                EntityHybridUtility.SetMaterialFloat(entity, shaderID, value);
            }
            else
            {
                if (shaderID == ShaderIDs.SelfLightColor)
                {
                    uint packed = (uint)value;
                    Color color = new Color((byte)(packed >> 24)/128.0f, (byte)(packed >> 16) / 128.0f, (byte)(packed >> 8) / 128.0f, (byte)(packed) / 128.0f);
                    propertyBlock.SetColor(shaderID, color);
                    propertyBlock.SetFloat(ShaderIDs.SelfLightness, color.a);
                }
            }

        }


        /// <summary>
        /// Initializes GPU animator with hybrid ECS/MonoBehaviour system detection and setup.
        /// Creates ECS entity if SkinMeshSystem is available, otherwise falls back to MonoBehaviour mode.
        /// Configures attack and death animations with special loop handling and material property blocks.
        /// </summary>
        public void Awake()
        {
            animControl.animationID = 0;
            //animControl.blendFactor = 1; 
            animControl.speedFactor = 1;
            animControl.attackLoopInterval = float.MaxValue;
            propertyBlock = new MaterialPropertyBlock();
            renders = gameObject.GetComponentsInChildren<MeshRenderer>();

            var world = World.DefaultGameObjectInjectionWorld;
            skinMeshSystem = (SkinMeshSystem)world.GetExistingSystemManaged(typeof(SkinMeshSystem));
            if (skinMeshSystem != null && skinMeshSystem.Enabled)
            {
                int hashCode = 31 * renders[0].sharedMaterial.GetInstanceID() + gpuAniAsset.GetInstanceID();
                entity = EntityHybridUtility.Instantiate(this.gameObject, hashCode, true);
                EntityHybridUtility.SetEnable(entity, false);
                for (int i=0; i< stateNames.Length; i++)
                {
                    if (stateNames[i] == "Attack")
                        attackID = i;
                }

                foreach (var r in renders)
                {
                    r.enabled = false;
                }

            }
            else
            {
                //临时处理攻击循环和死亡问题
                for (int i = 0; i < animations.Length; i++)
                {
                    if (stateNames[i] == "Attack")
                    {
                        animations[i].loop = true;
                        animControl.attackID = i;
                        attackID = i;
                    }
                    else if (stateNames[i] == "Dead")
                    {
                        animations[i].nextStateIndex = -1;
                    }

                }

                propertyBlock.SetVector(AnimationStateID, animationStateParam);

                foreach (var r in renders)
                {
                    var mats = r.sharedMaterials;
                    r.SetPropertyBlock(propertyBlock);
                }
            }

        }

        private Coroutine updateCoroutine;


        public void SetLayer(int layer)
        {
            if (World.DefaultGameObjectInjectionWorld != null && entity != Entity.Null)
            {
                if (m_Layer == layer)
                    return;
                EntityHybridUtility.SetLayer(entity, layer);
                m_Layer = layer;
            }
            else
            {
                foreach (var r in renders)
                {
                    r.gameObject.layer = layer;
                }
            }
        }
        public void OnEnable()
        {
           
            if (World.DefaultGameObjectInjectionWorld != null && entity != Entity.Null)
            {
                EntityHybridUtility.SetEnable(entity, true);
            }
            else
            {
                updateCoroutine = connector.StartCoroutine(UpdateCoroutine());
            }
        }

        public void OnDisable()
        {
            StopUpdate();
            if (World.DefaultGameObjectInjectionWorld != null && entity != Entity.Null)
            {
                EntityHybridUtility.SetEnable(entity, false);
            }
        }

        public void OnDestroy()
        {
            if (World.DefaultGameObjectInjectionWorld != null && entity != Entity.Null)
            {
                EntityHybridUtility.DestroyEntity(entity);
            }
        }
        public void StopUpdate()
        {
            if (updateCoroutine != null)
            {
                connector.StopCoroutine(updateCoroutine);
                updateCoroutine = null;
            }
        }

        public void StartUpdate()
        {
            if (updateCoroutine == null)
            {
                updateCoroutine = connector.StartCoroutine(UpdateCoroutine());
            }
        }

        IEnumerator UpdateCoroutine()
        {
            while (true)
            {
                try
                {
                    CUpdate();
                    
                }
                catch(System.Exception e)
                {
                    Debug.LogError($"gpu animator {gameObject} 动画错误. {animControl.animationID} {animations}\n {e.ToString()}  ", gameObject);
                    StopUpdate();
                    //yield break;
                }
                yield return null; // Wait for the next frame
            }
        }

        //private void OnValidate()
        //{
        //    if(lastID != curAnimationID)
        //    {
        //        gpuAnimatorState.stoppedCurrent = false;
        //        lastID = curAnimationID;
        //    }
        //}

        void CUpdate()
        {
            GpuAnimationData animationData = animations[animControl.animationID];
            if (gpuAnimatorState.stoppedCurrent && animationData.nextStateIndex >= 0 )
            {
                SetAnimatorState(animationData.nextStateIndex);
                animationData = animations[animControl.animationID];

            }
            if(animControl.useAttackLoopInterval && (animControl.animationID == animControl.attackID || animControl.animationID == animations[animControl.attackID].nextStateIndex))
            {
                var deltaTime = Time.deltaTime;
                animControl.interval += deltaTime;
                if (animControl.interval > animControl.attackLoopInterval) 
                {
                    SetAnimatorState(animControl.attackID);
                    animationData = animations[animControl.attackID];
                    animControl.interval = 0;
                }
                gpuAnimatorState.stoppedCurrent = false;
            }
            //if (controlState == GpuAnimatorControlStates.Start)
            //    gpuAnimatorState.stoppedCurrent = false;
            //else if (controlState == GpuAnimatorControlStates.Stop)
            //    gpuAnimatorState.stoppedCurrent = true;
            //controlState = GpuAnimatorControlStates.KeepCurrentState;

            if (!gpuAnimatorState.stoppedCurrent)
            {
                UpdateAnimatorState(ref gpuAnimatorState.currentNormalizedTime, ref gpuAnimatorState.stoppedCurrent, /*controlState,*/
                      out float primaryBlendFactor, out float primaryTransitionToNextFrame, out int primaryFrameIndex,
                      false, Time.deltaTime);

                int prevPrimaryFrameIndex = (int)animationStateParam.z;
                animationStateParam = new float4(primaryBlendFactor, primaryTransitionToNextFrame, primaryFrameIndex, 0);
                if(prevPrimaryFrameIndex != primaryFrameIndex)
                {
                    propertyBlock.SetVector(AnimationStateID, animationStateParam);
                }

                foreach (var r in renders)
                {
                    r.SetPropertyBlock(propertyBlock);
                }
                needDelayRefreshOnce = false;

            }
            else if(hasReplaceMatByCopy || needDelayRefreshOnce)//虽然动画停止了，但是可能还存在着材质属性修改，这里也还要设置一下
            {
                foreach (var r in renders)
                {
                    r.SetPropertyBlock(propertyBlock);
                }
                needDelayRefreshOnce = false;
            }
        }
        private void UpdateAnimatorState(ref float normalizedTime, ref bool stopped/*, in GpuAnimatorControlStates controlState*/, out float primaryBlendFactor, out float primaryTransitionToNextFrame, out int primaryFrameIndex, bool forPrevious, float deltaTime)
        {
            ref GpuAnimationData animationData = ref animations[animControl.animationID];
            //if (animationData.nbrOfInBetweenSamples == 1)
            {
                UpdateAnimationNormalizedTime(ref normalizedTime, ref stopped, animControl.speedFactor,/* controlState,*/
                    animationData,out float transitionToNextFrame, out int relativeFrameIndex, forPrevious, deltaTime);

                primaryBlendFactor = 1;
                primaryTransitionToNextFrame = transitionToNextFrame;
                primaryFrameIndex = animationData.startFrameIndex + relativeFrameIndex;

            }

        }

        private void UpdateAnimationNormalizedTime(ref float normalizedTime, ref bool stopped,
            float speedFactor, /*in GpuAnimatorControlStates controlState,*/
            in GpuAnimationData animationData,
            out float transitionToNextFrame, out int relativeFrameIndex, bool forPrevious, float deltaTime)
        {
            int endFrame = animationData.nbrOfFramesPerSample - 1;
            float animationLength = (float)endFrame / GlobalConstants.SampleFrameRate;
            float currentTime = normalizedTime * animationLength;
            if (!stopped) currentTime += deltaTime * speedFactor;
            float normalizedTimeLastUpdate = normalizedTime;
            normalizedTime = currentTime / animationLength;

            if (!forPrevious && (animationData.loop /*|| controlState == GpuAnimatorControlStates.Start*/) && ((!animControl.useAttackLoopInterval || animControl.animationID != animControl.attackID)))
            {
                normalizedTime = normalizedTime % 1f;
            }
            else
            {
                if (normalizedTime >= 1f)
                {
                    normalizedTime = 1f;
                    stopped = true;
                }
            }

            if (normalizedTime == 1f)
            {
                relativeFrameIndex = endFrame - 1;
                transitionToNextFrame = 1f;
            }
            else
            {
                float relativeFrameIndexFloat = normalizedTime * (float)endFrame;
                relativeFrameIndex = (int)math.floor(relativeFrameIndexFloat);
                transitionToNextFrame = relativeFrameIndexFloat - (float)relativeFrameIndex;
            }

        }

        /// <summary>
        /// Animation speed factor property with hybrid ECS/GameObject support.
        /// Multiplies base animation playback speed for dynamic gameplay effects.
        /// Automatically routes to appropriate system (ECS entity or MonoBehaviour control).
        /// </summary>
        public float speedFactor
        {
            get
            {
                return animControl.speedFactor;
            }
            set
            {
                if (entity != Entity.Null)
                {
                    EntityHybridUtility.SetSpeedFactor(entity, value);
                }
                else
                {
                    animControl.speedFactor = value;
                }
            }
        }
    }
}
