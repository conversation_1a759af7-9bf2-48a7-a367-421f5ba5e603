/*
 * Q1Engine - GPU Animation Data Management with Hash-Based Sharing
 * 
 * File: GpuAnimationDataArray.cs
 * Description: Memory-efficient GPU animation data management system.
 *              Handles shared animation data allocation with hash-based deduplication.
 *              Optimized for large-scale character animation rendering.
 * 
 * Author: Q1 Engine
 * Created: 2025
 * 
 * Key Features:
 * - Hash-based animation data sharing to reduce memory usage
 * - Free list memory management for efficient allocation/deallocation
 * - Native container support for job system compatibility
 * - Dynamic resizing with minimal fragmentation
 * - Parallel hash map for concurrent access patterns
 */

using Unity.Collections;
using Unity.Mathematics;
using Unity.Collections.LowLevel.Unsafe;
using GPUAnimationBaker.Engine;
using Unity.Entities;

namespace GPUAnimationBaker.Engine
{
    /// <summary>
    /// Index and count pair for GPU animation data range management.
    /// Represents allocated range within the shared animation data array for memory efficiency.
    /// Used for hash-based data sharing and range tracking in GPU animation systems.
    /// </summary>
    public struct IndexAndCount : IComponentData
    {
        /// <summary>Starting index in the shared animation data array</summary>
        public int index;
        /// <summary>Number of consecutive elements allocated from the index</summary>
        public int count;
    }
    /// <summary>
    /// Advanced GPU animation data array with hash-based sharing and free list memory management.
    /// Optimizes memory usage through data deduplication and efficient allocation/deallocation patterns.
    /// Features parallel hash map for concurrent access and native container job system compatibility.
    /// </summary>
    public unsafe class GpuAnimationDataArray
    {
        /// <summary>Maximum supported free list size for different allocation counts</summary>
        const int Max = 64;
        /// <summary>Native list storing shared GPU animation data with dynamic resizing</summary>
        public NativeList<GpuAnimationData> animDataList;
        /// <summary>Free list tracking available slots by allocation count for efficient reuse</summary>
        NativeArray<int> freeCountAtIndex;

        /// <summary>Total number of free elements available for reallocation</summary>
        int freeCount;
        /// <summary>Total number of currently allocated elements in the array</summary>
        int allocatedCount;

        /// <summary>Parallel hash map for O(1) lookup of shared animation data by hash code</summary>
        public NativeParallelHashMap<int, IndexAndCount> rangeHashMap;

        /// <summary>
        /// Initializes GPU animation data array with persistent allocators and free list management.
        /// Creates native containers optimized for job system usage with initial capacity of 128 elements.
        /// Sets up hash map for data sharing and initializes free list with -1 sentinels.
        /// </summary>
        public GpuAnimationDataArray()
        {
            animDataList = new NativeList<GpuAnimationData>(128, Allocator.Persistent);
            rangeHashMap = new NativeParallelHashMap<int, IndexAndCount>(128, Allocator.Persistent);
            freeCountAtIndex = new NativeArray<int>(Max, Allocator.Persistent);

            freeCount = 0;
            allocatedCount = 0;
            for (int i = 0; i < Max; i++)
            {
                freeCountAtIndex[i] = -1;
            }

        }


        /// <summary>
        /// Retrieves shared animation data range by hash code for efficient data sharing.
        /// Performs O(1) lookup in parallel hash map to find existing allocated ranges.
        /// Returns false and sets invalid range if hash is not found in the map.
        /// </summary>
        /// <param name="hash">Hash code of the animation data to lookup</param>
        /// <param name="indexAndCount">Output range containing index and count of shared data</param>
        /// <returns>True if hash exists in map, false otherwise</returns>
        public bool GetElement(int hash, out IndexAndCount indexAndCount)
        {
            bool ret = rangeHashMap.TryGetValue(hash, out indexAndCount);
            if (!ret)
            {
                indexAndCount.count = 0;
                indexAndCount.index = -1;
            }
            return ret;
        }

        /// <summary>
        /// Gets existing or creates new shared animation data range with hash-based deduplication.
        /// Allocates new range if hash doesn't exist, otherwise reuses existing shared data.
        /// Copies animation data into allocated range and updates hash map for future sharing.
        /// </summary>
        /// <param name="hash">Hash code identifying the unique animation data set</param>
        /// <param name="shareGpuAnimationData">Animation data array to store in shared memory</param>
        /// <returns>Index and count range for accessing the stored animation data</returns>
        public IndexAndCount GetOrCreateElement(int hash, GpuAnimationData[] shareGpuAnimationData)
        {

            if (!GetElement(hash, out var indexAndCount))
            {
                indexAndCount = AllocateElement(shareGpuAnimationData.Length);
                rangeHashMap.TryAdd(hash, indexAndCount);
            }

            for (int i = 0; i < indexAndCount.count; i++)
            {
                animDataList[i + indexAndCount.index] = shareGpuAnimationData[i];
            }

            return indexAndCount;
        }

        /// <summary>
        /// Allocates contiguous range in animation data array using free list optimization.
        /// Attempts to reuse freed ranges first, otherwise extends array with new allocation.
        /// Updates free list pointers and allocation counters for efficient memory tracking.
        /// </summary>
        /// <param name="count">Number of consecutive elements to allocate</param>
        /// <returns>Index and count range representing the allocated memory block</returns>
        public IndexAndCount AllocateElement(int count)
        {
            int index = -1;
            if (freeCountAtIndex[count] >= 0)
            {
                index = freeCountAtIndex[count];
                freeCountAtIndex[count] = animDataList[index].nextStateIndex;
                freeCount -= count;
            }
            else
            {
                index = animDataList.Length;
                animDataList.ResizeUninitialized(index + count);
            }
            allocatedCount += count;


            return new IndexAndCount { index = index, count = count };
        }

        /// <summary>
        /// Frees allocated animation data range and adds it to free list for efficient reuse.
        /// Updates free list linked structure and allocation counters for optimal memory management.
        /// Invalidates the input range reference to prevent accidental reuse of freed memory.
        /// </summary>
        /// <param name="renderer">Reference to range being freed, will be invalidated after call</param>
        public void FreeNode(ref IndexAndCount renderer)
        {
            int index = renderer.index;
            int count = renderer.count;

            if (index < 0 || count <= 0)
            {
                return;
            }

            allocatedCount -= count;
            freeCount += count;

            GpuAnimationData animData = animDataList[index];
            animData.nextStateIndex = freeCountAtIndex[count];
            animDataList[index] = animData;
            freeCountAtIndex[count] = index;

            renderer.count = 0;
            renderer.index = -1;

        }

        /// <summary>
        /// Disposes all native containers and releases persistent memory allocations.
        /// Safely cleans up animation data list, hash map, and free list arrays.
        /// Must be called to prevent memory leaks when GPU animation system is destroyed.
        /// </summary>
        public void Dispose()
        {
            if (animDataList.IsCreated) animDataList.Dispose();
            if (rangeHashMap.IsCreated) rangeHashMap.Dispose();
            if (freeCountAtIndex.IsCreated) freeCountAtIndex.Dispose();
        }

    }

}