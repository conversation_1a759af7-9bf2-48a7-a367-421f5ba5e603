using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Entities;
using Unity.Mathematics;

public class SpriteRenderBehaviour : MonoBehaviour
{
    Entity entity = Entity.Null;
    [SerializeField] private Sprite sprite;
    [SerializeField] private Color color = Color.white;
    [SerializeField] private bool flipX;
    [SerializeField] private bool flipY;
    [SerializeField] private DrawModeType drawMode = DrawModeType.Simple;
    [SerializeField] private Material material;
    [SerializeField] private string sortingLayer = "Default";
    [SerializeField] private int orderInLayer;
    [SerializeField] private float tiledWidth;
    [SerializeField] private float tiledHeight;
    [SerializeField] private Vector4 animationSpeed;

    public enum DrawModeType { Simple, Tiled } //��֧��Slicedģʽ
    public enum SortPointType { Center, Pivot }

    // Start is called before the first frame update
    void OnEnable()
    {
        if (tiledWidth == 0f) tiledWidth = 1.0f;
        if (tiledHeight == 0f) tiledHeight = 1.0f;
    }

    // Update is called once per frame
    void Update()
    {
        if (!sprite)
        {
            return;
        }
        var spriteData = new GpuSpriteRenderData
        {
            color = new float4(color.r, color.g, color.b, color.a),
            flip = new int2(flipX ? 1 : 0, flipY ? 1 : 0),
            drawMode = (int)drawMode,
            sortingLayerID = SortingLayer.NameToID(sortingLayer),
            orderInLayer = orderInLayer,
            //spriteParmas
            spriteTextureWidth = sprite.texture.width,
            spriteTextureHeight = sprite.texture.height,
            spriteRectWidth = sprite.rect.width,
            spriteRectHeight = sprite.rect.height,
            spriteRectxMin = sprite.rect.xMin,
            spriteRectyMin = sprite.rect.yMin,
            spritePixelsPerUnit = sprite.pixelsPerUnit,
            spritePivot = sprite.pivot,
            spriteTextureRect = new float4(sprite.textureRect.x, sprite.textureRect.y,sprite.textureRect.width,sprite.textureRect.height),
            animationSpeed = animationSpeed,
            tileSize = new float2(tiledWidth, tiledHeight),
        };
        if(drawMode == DrawModeType.Tiled)
            gameObject.transform.localScale = new Vector3(tiledWidth, tiledHeight, gameObject.transform.localScale.z);
            
        if (entity != Entity.Null)
        {
            World.Instance.entityManager.UpdateSpriteMeshEntityData(entity,gameObject.GetComponent<Transform>(),spriteData);
        }
        else
        {
            entity = World.Instance.entityManager.CreateSpriteEntity(gameObject.transform, gameObject.GetComponent<SpriteRenderBehaviour>().material, sprite.texture, spriteData);
        }
    }

    private void OnDisable()
    {
        if (entity != Entity.Null)
        {
            World.Instance.entityManager.DestoryEntity(entity);
            entity = Entity.Null;
        }
    }

}
