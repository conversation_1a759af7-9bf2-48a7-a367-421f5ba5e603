using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using Unity.Collections;
using System.IO;
using UnityEngine.Experimental.Rendering;
using UnityEngine.SocialPlatforms;
using System.Security.Cryptography;
using System.Linq;
using Unity.Mathematics;
using System.Reflection;
using System.Linq.Expressions;
using static System.Net.Mime.MediaTypeNames;
using System.Xml.Linq;
using System;
using System.Text;



#if UNITY_EDITOR
using UnityEditor;
#endif

public class RegionTexExporter : MonoBehaviour
{
#if UNITY_EDITOR
    [HideInInspector]
    [Header("Input Settings")]
    public Texture2D inputTexture;

    [HideInInspector]
    [Header("Output Settings")]
    public string FolderPath;

    [HideInInspector]
    public string outputFileName = "_SDF_Output";

    [Header("SDF Parameters")]
    [Tooltip("关心多少像素")]
    [Range(32, 256)]
    public float maxDistance = 64;

    [Header("Multi-Resolution Settings")]
    [Tooltip("生成多个分辨率的SDF版本")]
    public bool generateMultiResolution = true;

    [Tooltip("超采样倍数：生成高分辨率SDF然后下采样，保持精度")]
    [Range(1, 6)]
    public int supersamplingFactor = 2;

    [Tooltip("目标输出分辨率列表，空则使用原分辨率")]
    public int[] targetResolutions = new int[] { 64 };

    [Tooltip("Alpha通道阈值")]
    [Range(0f, 1f)]
    public float alphaThreshold = 0.1f;

    [Header("Corner Protection Settings")]
    [Tooltip("启用角点保护算法")]
    public bool enableCornerProtection = true;

    [Tooltip("角点检测敏感度：越高越容易检测到角点")]
    [Range(1, 8)]
    public int cornerDetectionSensitivity = 4;

    [Tooltip("角点保护强度：越高越保护尖锐特征")]
    [Range(0.5f, 2.0f)]
    public float cornerProtectionStrength = 2.0f;

    [Header("Use Input Texture Alpha")]
    public bool useInputTextureAlpha = true;

    [Header("Debug")]
    public bool DebugAlpha = false;

    private int inputTexWidth;

    /// <summary>
    /// RG编码距离值，使用两个通道提供高精度
    /// </summary>
    public static Vector2 EncodeDistanceRG(float distance)
    {
        // 将距离值编码到RG两个通道中
        float2 encode = new float2(1.0f, 255.0f) * distance;
        encode = math.frac(encode);
        encode.x -= encode.y * (1.0f / 255.0f);
        return new Vector2(encode.x, encode.y);
    }

    /// <summary>
    /// 从RG两个通道解码距离值
    /// </summary>
    public static float DecodeDistanceRG(Vector2 encoded)
    {
        float2 decode = new float2(1.0f, 1.0f / 255.0f);
        return math.dot(encoded, decode);
    }


    /// <summary>
    /// 高质量降采样Alpha通道：保持Alpha通道的细节和边缘
    /// </summary>
    private float[,] DownsampleAlphaChannel(Texture2D inputTexture, int targetWidth, int targetHeight)
    {
        float[,] alphaField = new float[targetHeight, targetWidth];
        int originalWidth = inputTexture.width;
        int originalHeight = inputTexture.height;

        float scaleX = (float)originalWidth / targetWidth;
        float scaleY = (float)originalHeight / targetHeight;

        for (int y = 0; y < targetHeight; y++)
        {
            for (int x = 0; x < targetWidth; x++)
            {
                // 计算在源图像中的采样区域
                float srcCenterX = (x + 0.5f) * scaleX - 0.5f;
                float srcCenterY = (y + 0.5f) * scaleY - 0.5f;

                // 使用高质量alpha采样
                float alphaValue = SampleAlphaWithQuality(inputTexture, originalWidth, originalHeight, srcCenterX, srcCenterY, scaleX, scaleY);
                alphaField[y, x] = alphaValue;
            }
        }

        return alphaField;
    }

    /// <summary>
    /// 保持边缘特征的Alpha采样
    /// </summary>
    private float SampleAlphaPreservingEdges(List<float> alphaValues, List<float> weights)
    {
        // 分别统计高alpha和低alpha的值
        List<float> highAlphaValues = new List<float>();
        List<float> lowAlphaValues = new List<float>();
        List<float> highAlphaWeights = new List<float>();
        List<float> lowAlphaWeights = new List<float>();

        float threshold = alphaThreshold;

        for (int i = 0; i < alphaValues.Count; i++)
        {
            if (alphaValues[i] > threshold)
            {
                highAlphaValues.Add(alphaValues[i]);
                highAlphaWeights.Add(weights[i]);
            }
            else
            {
                lowAlphaValues.Add(alphaValues[i]);
                lowAlphaWeights.Add(weights[i]);
            }
        }

        // 根据哪一类占主导来决定结果
        float highAlphaSum = 0f;
        float lowAlphaSum = 0f;
        float highWeightSum = 0f;
        float lowWeightSum = 0f;

        for (int i = 0; i < highAlphaValues.Count; i++)
        {
            highAlphaSum += highAlphaValues[i] * highAlphaWeights[i];
            highWeightSum += highAlphaWeights[i];
        }

        for (int i = 0; i < lowAlphaValues.Count; i++)
        {
            lowAlphaSum += lowAlphaValues[i] * lowAlphaWeights[i];
            lowWeightSum += lowAlphaWeights[i];
        }

        // 根据权重和数量决定最终值
        if (highWeightSum > lowWeightSum * 1.2f)
        {
            return highWeightSum > 0 ? highAlphaSum / highWeightSum : 1.0f;
        }
        else if (lowWeightSum > highWeightSum * 1.2f)
        {
            return lowWeightSum > 0 ? lowAlphaSum / lowWeightSum : 0.0f;
        }
        else
        {
            // 平衡情况，混合两者
            float totalWeightedSum = highAlphaSum + lowAlphaSum;
            float totalWeight = highWeightSum + lowWeightSum;
            return totalWeight > 0 ? totalWeightedSum / totalWeight : 0.5f;
        }
    }

    /// <summary>
    /// 高质量Alpha采样：保持alpha通道的精度和边缘特征
    /// </summary>
    private float SampleAlphaWithQuality(Texture2D texture, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        // 计算采样半径
        int sampleRadius = Mathf.Max(1, Mathf.CeilToInt(Mathf.Max(scaleX, scaleY) * 0.6f));

        List<float> alphaValues = new List<float>();
        List<float> weights = new List<float>();

        // 在采样区域内收集alpha值
        for (int dy = -sampleRadius; dy <= sampleRadius; dy++)
        {
            for (int dx = -sampleRadius; dx <= sampleRadius; dx++)
            {
                int x = Mathf.Clamp(Mathf.RoundToInt(centerX + dx), 0, width - 1);
                int y = Mathf.Clamp(Mathf.RoundToInt(centerY + dy), 0, height - 1);

                Color pixel = texture.GetPixel(x, y);
                float alpha = pixel.a;

                // 计算距离权重
                float distance = Mathf.Sqrt(dx * dx + dy * dy);
                float weight = 1.0f / (1.0f + distance * 0.5f);

                alphaValues.Add(alpha);
                weights.Add(weight);
            }
        }

        // 检测是否在alpha边界附近
        bool hasAlphaEdge = false;
        float minAlpha = 1.0f;
        float maxAlpha = 0.0f;

        for (int i = 0; i < alphaValues.Count; i++)
        {
            minAlpha = Mathf.Min(minAlpha, alphaValues[i]);
            maxAlpha = Mathf.Max(maxAlpha, alphaValues[i]);
        }

        hasAlphaEdge = (maxAlpha - minAlpha) > 0.3f;

        if (hasAlphaEdge)
        {
            // 在边缘区域，保持最接近边界的特征
            return SampleAlphaPreservingEdges(alphaValues, weights);
        }
        else
        {
            // 在平坦区域，使用加权平均
            float weightedSum = 0f;
            float totalWeight = 0f;

            for (int i = 0; i < alphaValues.Count; i++)
            {
                weightedSum += alphaValues[i] * weights[i];
                totalWeight += weights[i];
            }

            return totalWeight > 0 ? weightedSum / totalWeight : 0f;
        }
    }

    /// <summary>
    /// RGBA编码距离值，提供超高精度
    /// </summary>
    public static Vector4 EncodeDistanceRGBA(float distance)
    {
        // 将距离值编码到RGBA四个通道中，提供32位精度
        float4 encode = new float4(1.0f, 255.0f, 65025.0f, 16581375.0f) * distance;
        encode = math.frac(encode);
        encode.xyz -= encode.yzw * (1.0f / 255.0f);
        return new Vector4(encode.x, encode.y, encode.z, encode.w);
    }

    /// <summary>
    /// 从RGBA四个通道解码距离值
    /// </summary>
    public static float DecodeDistanceRGBA(Vector4 encoded)
    {
        float4 decode = new float4(1.0f, 1.0f / 255.0f, 1.0f / 65025.0f, 1.0f / 16581375.0f);
        return math.dot(encoded, decode);
    }


    /// <summary>
    /// RGB编码距离值，提供更高精度
    /// </summary>
    public static Vector3 EncodeDistanceRGB(float distance)
    {
        // 将距离值编码到RGB三个通道中
        float3 encode = new float3(1.0f, 255.0f, 65025.0f) * distance;
        encode = math.frac(encode);
        encode.xy -= encode.yz * (1.0f / 255.0f);
        return new Vector3(encode.x, encode.y, encode.z);
    }

    /// <summary>
    /// 从RGB三个通道解码距离值
    /// </summary>
    public static float DecodeDistanceRGB(Vector3 encoded)
    {
        float3 decode = new float3(1.0f, 1.0f / 255.0f, 1.0f / 65025.0f);
        return math.dot(encoded, decode);
    }

    /// <summary>
    /// 解码SDF值：将0-1范围映射回-1到1的有向距离场
    /// 在Shader中使用：float sdf = DecodeSignedDistance(encodedValue);
    /// </summary>
    public static float DecodeSignedDistance(float encoded01)
    {
        return encoded01 * 2.0f - 1.0f;
    }

    /// <summary>
    /// 生成SDF贴图
    /// </summary>
    public void GenerateSDFTexture()
    {
        if (inputTexture == null)
        {
            Debug.LogError("Input texture is null!");
            return;
        }

        // 确保贴图可读
        int cacheMaxSize = 0;
        string inputPath = "";
        TextureImporter textureImporter = null;
        TextureImporterPlatformSettings androidSettings=null;
        TextureImporterPlatformSettings iponeSettings=null;
        TextureImporterFormat cacheTexForamt = TextureImporterFormat.ASTC_6x6;
        if (!inputTexture.isReadable)
        {
            inputPath =  AssetDatabase.GetAssetPath(inputTexture);
            textureImporter = AssetImporter.GetAtPath(inputPath) as TextureImporter;
            textureImporter.isReadable = true;
            cacheMaxSize = inputTexture.width;
            textureImporter.maxTextureSize = 2048;

            androidSettings = textureImporter.GetPlatformTextureSettings("Android");
            if(androidSettings.overridden)
            {
                androidSettings.maxTextureSize = 2048;
                cacheTexForamt = androidSettings.format;
                //androidSettings.format = TextureImporterFormat.ARGB32;
                textureImporter.SetPlatformTextureSettings(androidSettings);
            }
    

            iponeSettings = textureImporter.GetPlatformTextureSettings("iPhone");
            if(iponeSettings.overridden)
            {
                iponeSettings.maxTextureSize = 2048;
                cacheTexForamt = iponeSettings.format;
                //iponeSettings.format = TextureImporterFormat.ARGB32;
                textureImporter.SetPlatformTextureSettings(iponeSettings);
            }


            textureImporter.SaveAndReimport();
        }

        int width = inputTexture.width;
        inputTexWidth = width;
        int height = inputTexture.height;

        Debug.Log($"Processing texture: {width}x{height}, Supersampling: {supersamplingFactor}x");

        // 1. 使用超采样生成高分辨率SDF
        int supersampledWidth = width * supersamplingFactor;
        int supersampledHeight = height * supersamplingFactor;

        // 超采样提取Alpha映射
        bool[,] supersampledAlphaMap = ExtractSupersampledAlphaMap(inputTexture, supersampledWidth, supersampledHeight);
        if(DebugAlpha)
        {
            Texture2D alphaRegionMap = CreateDebugAlphaRegionTexture(supersampledAlphaMap);
            string debugFileName = inputTexture.name + "_Debug_Alpha";
            SaveTexture(alphaRegionMap, debugFileName, alphaRegionMap.width);
        }

        // 计算高分辨率距离场
        float[,] highResDistanceField = ComputeSignedDistanceField(supersampledAlphaMap, supersampledWidth, supersampledHeight);

        // 2. 生成多分辨率版本
        if (generateMultiResolution && targetResolutions != null && targetResolutions.Length > 0)
        {
            foreach (int targetRes in targetResolutions)
            {
                if (targetRes > 0)
                {
                    // 计算目标尺寸（保持宽高比）
                    float aspectRatio = (float)width / height;
                    int targetWidth, targetHeight;

                    if (width >= height)
                    {
                        targetWidth = targetRes;
                        targetHeight = Mathf.RoundToInt(targetRes / aspectRatio);
                    }
                    else
                    {
                        targetHeight = targetRes;
                        targetWidth = Mathf.RoundToInt(targetRes * aspectRatio);
                    }

                    // 智能下采样
                    float[,] downsampledField = DownsampleSDF(highResDistanceField, supersampledWidth, supersampledHeight, targetWidth, targetHeight);

                    // 降采样Alpha通道
                    float[,] downsampledAlpha = DownsampleAlphaChannel(inputTexture, targetWidth, targetHeight);


                    // 创建并保存该分辨率的SDF（包含alpha）
                    Texture2D sdfTexture = CreateSDFTextureWithAlpha(downsampledField, downsampledAlpha, targetWidth, targetHeight);
                    string fileName = $"{inputTexture.name}{outputFileName}_{targetRes}";
                    SaveTexture(sdfTexture, fileName, targetWidth);

                    Debug.Log($"Generated SDF at resolution: {targetWidth}x{targetHeight}");
                }
            }
        }
        else
        {
            // 原始分辨率版本
            float[,] originalResField = DownsampleSDF(highResDistanceField, supersampledWidth, supersampledHeight, width, height);

            // 降采样Alpha通道到原始分辨率
            float[,] originalResAlpha = DownsampleAlphaChannel(inputTexture, width, height);

            Texture2D sdfTexture = CreateSDFTextureWithAlpha(originalResField, originalResAlpha, width, height);
            string fileName = inputTexture.name + outputFileName;
            SaveTexture(sdfTexture, fileName, width);
        }

        if(textureImporter!=null)
        {
            textureImporter = AssetImporter.GetAtPath(inputPath) as TextureImporter;
            textureImporter.isReadable = false;
            textureImporter.maxTextureSize = cacheMaxSize;

            if(androidSettings != null && androidSettings.overridden==true)
            {
                androidSettings.format = cacheTexForamt;
                androidSettings.maxTextureSize = cacheMaxSize;
                textureImporter.SetPlatformTextureSettings(androidSettings);
            }
         

            if(iponeSettings!=null&&iponeSettings.overridden==true)
            {
                iponeSettings.format = cacheTexForamt;
                iponeSettings.maxTextureSize = cacheMaxSize;

                textureImporter.SetPlatformTextureSettings(iponeSettings);
            }

            textureImporter.SaveAndReimport();
        }

        Debug.Log($"SDF texture generated successfully: {outputFileName}");
    }

    /// <summary>
    /// 超采样提取Alpha通道映射
    /// </summary>
    private bool[,] ExtractSupersampledAlphaMap(Texture2D texture, int supersampledWidth, int supersampledHeight)
    {
        bool[,] alphaMap = new bool[supersampledHeight, supersampledWidth];
        int originalWidth = texture.width;
        int originalHeight = texture.height;

        for (int y = 0; y < supersampledHeight; y++)
        {
            for (int x = 0; x < supersampledWidth; x++)
            {
                // 映射到原始纹理坐标
                float u = (float)x / supersampledWidth;
                float v = (float)y / supersampledHeight;

                int originalX = Mathf.Clamp(Mathf.FloorToInt(u * originalWidth), 0, originalWidth - 1);
                int originalY = Mathf.Clamp(Mathf.FloorToInt(v * originalHeight), 0, originalHeight - 1);

                Color pixel = texture.GetPixel(originalX, originalY);
                alphaMap[y, x] = pixel.a > alphaThreshold;
            }
        }

        return alphaMap;
    }

    /// <summary>
    /// 智能SDF下采样：保持距离场特性的专用下采样算法
    /// </summary>
    private float[,] DownsampleSDF(float[,] sourceField, int sourceWidth, int sourceHeight, int targetWidth, int targetHeight)
    {
        float[,] targetField = new float[targetHeight, targetWidth];

        float scaleX = (float)sourceWidth / targetWidth;
        float scaleY = (float)sourceHeight / targetHeight;

        for (int y = 0; y < targetHeight; y++)
        {
            for (int x = 0; x < targetWidth; x++)
            {
                // 计算在源图像中的采样区域
                float srcCenterX = (x + 0.5f) * scaleX - 0.5f;
                float srcCenterY = (y + 0.5f) * scaleY - 0.5f;

                // 使用多种采样策略的混合
                float sdfValue = SampleSDFForDownsampling(sourceField, sourceWidth, sourceHeight, srcCenterX, srcCenterY, scaleX, scaleY);

                targetField[y, x] = sdfValue;
            }
        }

        return targetField;
    }



    /// <summary>
    /// SDF专用采样：结合多种策略保持距离场精度
    /// </summary>
    private float SampleSDFForDownsampling(float[,] field, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        // 策略1: 中心点采样
        float centerSample = BilinearSample(field, width, height, centerX, centerY);

        // 策略2: 区域内最小绝对距离采样（保持边界特征）
        float minAbsSample = SampleMinAbsDistance(field, width, height, centerX, centerY, scaleX, scaleY);

        // 策略3: 符号保持采样
        float signPreservingSample = SampleWithSignPreservation(field, width, height, centerX, centerY, scaleX, scaleY);

        // 策略4: 角点检测和保护采样（新增）
        float cornerSample = SampleWithCornerPreservation(field, width, height, centerX, centerY, scaleX, scaleY);


        // 根据距离到边界的远近选择策略
        float absCenterValue = Mathf.Abs(centerSample - 0.5f); // 0.5是SDF的零点

        // 检测是否在角点区域
        bool isCornerRegion = enableCornerProtection && DetectCornerRegion(field, width, height, centerX, centerY, scaleX, scaleY);

        if (isCornerRegion) // 接近边界，使用符号保持策略
        {
            return cornerSample;
        }
        else if (absCenterValue < 0.1f)
        {
            return signPreservingSample;
        }
        else if (absCenterValue < 0.3f) // 中等距离，混合策略
        {
            return Mathf.Lerp(signPreservingSample, minAbsSample, 0.5f);
        }
        else // 远离边界，使用中心采样
        {
            return centerSample;
        }
    }

    /// <summary>
    /// 检测角点区域：检测采样区域内是否包含尖锐转角
    /// </summary>
    private bool DetectCornerRegion(float[,] field, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        int sampleRadius = Mathf.Max(2, Mathf.CeilToInt(Mathf.Max(scaleX, scaleY) * 0.7f));

        // 收集采样区域内的值
        List<float> samples = new List<float>();
        List<Vector2Int> positions = new List<Vector2Int>();

        for (int dy = -sampleRadius; dy <= sampleRadius; dy++)
        {
            for (int dx = -sampleRadius; dx <= sampleRadius; dx++)
            {
                int x = Mathf.Clamp(Mathf.RoundToInt(centerX + dx), 0, width - 1);
                int y = Mathf.Clamp(Mathf.RoundToInt(centerY + dy), 0, height - 1);
                samples.Add(field[y, x]);
                positions.Add(new Vector2Int(x, y));
            }
        }

        // 检测符号变化的复杂性
        int signChanges = 0;
        float threshold = 0.5f;

        // 检查8个方向的符号变化
        Vector2Int[] directions = {
            new Vector2Int(1, 0), new Vector2Int(1, 1), new Vector2Int(0, 1), new Vector2Int(-1, 1),
            new Vector2Int(-1, 0), new Vector2Int(-1, -1), new Vector2Int(0, -1), new Vector2Int(1, -1)
        };

        int centerIdx = samples.Count / 2;
        bool centerSign = samples[centerIdx] > threshold;

        for (int i = 0; i < directions.Length; i++)
        {
            int x = Mathf.Clamp(Mathf.RoundToInt(centerX + directions[i].x * sampleRadius * 0.5f), 0, width - 1);
            int y = Mathf.Clamp(Mathf.RoundToInt(centerY + directions[i].y * sampleRadius * 0.5f), 0, height - 1);

            bool directionSign = field[y, x] > threshold;
            if (directionSign != centerSign)
            {
                signChanges++;
            }
        }

        // 如果有指定数量或更多方向的符号变化，认为是角点区域
        return signChanges >= cornerDetectionSensitivity;
    }

    /// <summary>
    /// 角点保护采样：专门处理尖锐转角的采样策略
    /// </summary>
    private float SampleWithCornerPreservation(float[,] field, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        // 使用更大的采样半径以捕捉角点特征
        int sampleRadius = Mathf.Max(2, Mathf.CeilToInt(Mathf.Max(scaleX, scaleY) * 0.8f));

        // 收集所有采样点的信息
        List<SamplePoint> samplePoints = new List<SamplePoint>();

        for (int dy = -sampleRadius; dy <= sampleRadius; dy++)
        {
            for (int dx = -sampleRadius; dx <= sampleRadius; dx++)
            {
                int x = Mathf.Clamp(Mathf.RoundToInt(centerX + dx), 0, width - 1);
                int y = Mathf.Clamp(Mathf.RoundToInt(centerY + dy), 0, height - 1);

                float value = field[y, x];
                float distance = Mathf.Sqrt(dx * dx + dy * dy);

                samplePoints.Add(new SamplePoint
                {
                    value = value,
                    distance = distance,
                    position = new Vector2Int(x, y)
                });
            }
        }

        // 策略1: 保持最小绝对距离（保护尖锐特征）
        float minAbsDistance = float.MaxValue;
        float minAbsValue = 0.5f;

        foreach (var point in samplePoints)
        {
            float absDistance = Mathf.Abs(point.value - 0.5f);
            if (absDistance < minAbsDistance)
            {
                minAbsDistance = absDistance;
                minAbsValue = point.value;
            }
        }

        // 策略2: 权重平均（距离越近权重越大，边界点权重加强）
        float weightedSum = 0f;
        float totalWeight = 0f;

        foreach (var point in samplePoints)
        {
            float distanceWeight = 1.0f / (1.0f + point.distance * 0.5f);

            // 如果是边界点（接近0.5），增加权重
            float boundaryWeight = 1.0f;
            float absFromBoundary = Mathf.Abs(point.value - 0.5f);
            if (absFromBoundary < 0.2f)
            {
                boundaryWeight = 2.0f; // 边界点权重加倍
            }

            float finalWeight = distanceWeight * boundaryWeight;
            weightedSum += point.value * finalWeight;
            totalWeight += finalWeight;
        }

        float weightedAverage = totalWeight > 0 ? weightedSum / totalWeight : 0.5f;

        // 策略3: 形态学操作保护
        float morphologyProtected = ApplyMorphologyProtection(samplePoints);

        // 综合三种策略
        float result;
        if (minAbsDistance < 0.1f) // 很接近边界
        {
            // 优先保护尖锐特征
            result = minAbsValue;
        }
        else if (minAbsDistance < 0.3f) // 中等距离
        {
            // 混合策略，根据保护强度调整混合比例
            float blendRatio = Mathf.Lerp(0.4f, 0.8f, (cornerProtectionStrength - 0.5f) / 1.5f);
            result = Mathf.Lerp(minAbsValue, weightedAverage, blendRatio);
        }
        else // 远离边界
        {
            // 使用形态学保护的加权平均
            result = Mathf.Lerp(weightedAverage, morphologyProtected, 0.3f * cornerProtectionStrength);
        }

        return result;
    }


    /// <summary>
    /// 低分辨率后处理优化：专门针对64x64等极低分辨率的额外优化
    /// </summary>
    private float[,] ApplyLowResolutionOptimization(float[,] field, int width, int height)
    {
        float[,] optimizedField = new float[height, width];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                float originalValue = field[y, x];

                // 检测是否是关键边界点
                bool isCriticalBoundary = IsCriticalBoundaryPoint(field, x, y, width, height);

                if (isCriticalBoundary)
                {
                    // 对关键边界点应用特殊优化
                    optimizedField[y, x] = OptimizeCriticalBoundaryPoint(field, x, y, width, height);
                }
                else
                {
                    // 普通点使用轻微的边界锐化
                    optimizedField[y, x] = ApplyBoundarySharpening(field, x, y, width, height, originalValue);
                }
            }
        }

        return optimizedField;
    }

    /// <summary>
    /// 检测关键边界点：这些点在低分辨率下容易丢失重要信息
    /// </summary>
    private bool IsCriticalBoundaryPoint(float[,] field, int x, int y, int width, int height)
    {
        float currentValue = field[y, x];
        float threshold = 0.5f;
        bool currentSign = currentValue > threshold;

        // 检查周围8个方向的符号变化
        int signChangeCount = 0;
        float maxDistanceFromBoundary = 0f;

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                if (dx == 0 && dy == 0) continue;

                int nx = Mathf.Clamp(x + dx, 0, width - 1);
                int ny = Mathf.Clamp(y + dy, 0, height - 1);

                float neighborValue = field[ny, nx];
                bool neighborSign = neighborValue > threshold;

                if (neighborSign != currentSign)
                {
                    signChangeCount++;
                }

                maxDistanceFromBoundary = Mathf.Max(maxDistanceFromBoundary, Mathf.Abs(neighborValue - threshold));
            }
        }

        // 如果符号变化多且距离边界较远，认为是关键点
        return signChangeCount >= 2 && maxDistanceFromBoundary > 0.1f;
    }

    /// <summary>
    /// 优化关键边界点：保护重要的几何特征
    /// </summary>
    private float OptimizeCriticalBoundaryPoint(float[,] field, int x, int y, int width, int height)
    {
        // 收集3x3邻域的信息
        List<float> neighborValues = new List<float>();
        float centerValue = field[y, x];

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                int nx = Mathf.Clamp(x + dx, 0, width - 1);
                int ny = Mathf.Clamp(y + dy, 0, height - 1);
                neighborValues.Add(field[ny, nx]);
            }
        }

        // 策略1：保持最接近边界的值（保护边界锐利度）
        float minAbsDistance = float.MaxValue;
        float closestToBoundary = centerValue;

        foreach (float value in neighborValues)
        {
            float absDistance = Mathf.Abs(value - 0.5f);
            if (absDistance < minAbsDistance)
            {
                minAbsDistance = absDistance;
                closestToBoundary = value;
            }
        }

        // 策略2：符号一致性检查
        int positiveCount = 0;
        int negativeCount = 0;
        float positiveSum = 0f;
        float negativeSum = 0f;

        foreach (float value in neighborValues)
        {
            if (value > 0.5f)
            {
                positiveCount++;
                positiveSum += value;
            }
            else
            {
                negativeCount++;
                negativeSum += value;
            }
        }

        // 如果某一侧占绝对优势，使用该侧的平均值
        if (positiveCount > negativeCount * 2)
        {
            return positiveSum / positiveCount;
        }
        else if (negativeCount > positiveCount * 2)
        {
            return negativeSum / negativeCount;
        }

        // 否则保护边界特征
        return closestToBoundary;
    }

    /// <summary>
    /// 边界锐化：轻微增强边界对比度
    /// </summary>
    private float ApplyBoundarySharpening(float[,] field, int x, int y, int width, int height, float originalValue)
    {
        float threshold = 0.5f;
        float sharpening = 0.1f; // 锐化强度

        // 计算邻域平均值
        float sum = 0f;
        int count = 0;

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                int nx = Mathf.Clamp(x + dx, 0, width - 1);
                int ny = Mathf.Clamp(y + dy, 0, height - 1);
                sum += field[ny, nx];
                count++;
            }
        }

        float average = sum / count;

        // 应用锐化：远离平均值
        float difference = originalValue - average;
        float sharpenedValue = originalValue + difference * sharpening;

        // 限制在合理范围内
        return Mathf.Clamp01(sharpenedValue);
    }

    /// <summary>
    /// 形态学保护：防止细小特征被过度平滑
    /// </summary>
    private float ApplyMorphologyProtection(List<SamplePoint> samplePoints)
    {
        if (samplePoints.Count == 0) return 0.5f;

        // 统计内部和外部点
        List<float> insideValues = new List<float>();
        List<float> outsideValues = new List<float>();

        foreach (var point in samplePoints)
        {
            if (point.value > 0.5f)
                insideValues.Add(point.value);
            else
                outsideValues.Add(point.value);
        }

        // 如果只有一种类型的点，取其平均值
        if (insideValues.Count == 0)
        {
            return outsideValues.Average();
        }
        else if (outsideValues.Count == 0)
        {
            return insideValues.Average();
        }

        // 混合情况：检查哪种类型占主导
        if (insideValues.Count > outsideValues.Count * 2)
        {
            // 内部占主导，取内部平均值
            return insideValues.Average();
        }
        else if (outsideValues.Count > insideValues.Count * 2)
        {
            // 外部占主导，取外部平均值
            return outsideValues.Average();
        }
        else
        {
            // 相对平衡，使用边界值但偏向密度更高的一侧
            float insideAvg = insideValues.Average();
            float outsideAvg = outsideValues.Average();

            float ratio = (float)insideValues.Count / (insideValues.Count + outsideValues.Count);
            return Mathf.Lerp(outsideAvg, insideAvg, ratio);
        }
    }


    /// <summary>
    /// 采样点数据结构
    /// </summary>
    private struct SamplePoint
    {
        public float value;
        public float distance;
        public Vector2Int position;
    }

    /// <summary>
    /// 双线性采样
    /// </summary>
    private float BilinearSample(float[,] field, int width, int height, float x, float y)
    {
        int x1 = Mathf.FloorToInt(x);
        int y1 = Mathf.FloorToInt(y);
        int x2 = Mathf.Min(x1 + 1, width - 1);
        int y2 = Mathf.Min(y1 + 1, height - 1);

        x1 = Mathf.Max(x1, 0);
        y1 = Mathf.Max(y1, 0);

        float fx = x - x1;
        float fy = y - y1;

        float v1 = field[y1, x1] * (1 - fx) + field[y1, x2] * fx;
        float v2 = field[y2, x1] * (1 - fx) + field[y2, x2] * fx;

        return v1 * (1 - fy) + v2 * fy;
    }

    /// <summary>
    /// 最小绝对距离采样：在采样区域内找到最接近边界的点
    /// </summary>
    private float SampleMinAbsDistance(float[,] field, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        int sampleRadius = Mathf.Max(1, Mathf.CeilToInt(Mathf.Max(scaleX, scaleY) * 0.5f));

        float minAbsValue = float.MaxValue;
        float resultValue = 0.5f;

        for (int dy = -sampleRadius; dy <= sampleRadius; dy++)
        {
            for (int dx = -sampleRadius; dx <= sampleRadius; dx++)
            {
                int x = Mathf.Clamp(Mathf.RoundToInt(centerX + dx), 0, width - 1);
                int y = Mathf.Clamp(Mathf.RoundToInt(centerY + dy), 0, height - 1);

                float value = field[y, x];
                float absDistance = Mathf.Abs(value - 0.5f);

                if (absDistance < minAbsValue)
                {
                    minAbsValue = absDistance;
                    resultValue = value;
                }
            }
        }

        return resultValue;
    }

    /// <summary>
    /// 符号保持采样：确保符号不会因为采样而翻转
    /// </summary>
    private float SampleWithSignPreservation(float[,] field, int width, int height, float centerX, float centerY, float scaleX, float scaleY)
    {
        // 收集采样区域内的所有值
        List<float> samples = new List<float>();
        int sampleRadius = Mathf.Max(1, Mathf.CeilToInt(Mathf.Max(scaleX, scaleY) * 0.5f));

        for (int dy = -sampleRadius; dy <= sampleRadius; dy++)
        {
            for (int dx = -sampleRadius; dx <= sampleRadius; dx++)
            {
                int x = Mathf.Clamp(Mathf.RoundToInt(centerX + dx), 0, width - 1);
                int y = Mathf.Clamp(Mathf.RoundToInt(centerY + dy), 0, height - 1);
                samples.Add(field[y, x]);
            }
        }

        // 统计正负采样数量
        int positiveCount = 0;
        int negativeCount = 0;
        float positiveSum = 0;
        float negativeSum = 0;

        foreach (float sample in samples)
        {
            if (sample > 0.5f)
            {
                positiveCount++;
                positiveSum += sample;
            }
            else if (sample < 0.5f)
            {
                negativeCount++;
                negativeSum += sample;
            }
        }

        // 基于主导符号返回平均值
        if (positiveCount > negativeCount)
        {
            return positiveCount > 0 ? positiveSum / positiveCount : 0.5f;
        }
        else if (negativeCount > positiveCount)
        {
            return negativeCount > 0 ? negativeSum / negativeCount : 0.5f;
        }
        else
        {
            // 平衡情况，使用双线性采样
            return BilinearSample(field, width, height, centerX, centerY);
        }
    }


    /// <summary>
    /// 计算距离场（内部或外部）
    /// </summary>
    /// <param name="alphaMap"></param>
    /// <param name="width"></param>
    /// <param name="height"></param>
    /// <param name="computeInside">true=计算内部距离，false=计算外部距离</param>
    /// <returns></returns>
    private float[,] ComputeDistanceField(bool[,] alphaMap, int width, int height, bool computeInside)
    {
        float[,] distanceField = new float[height, width];
        float maxDist = Mathf.Max(width, height);
        // 初始化距离场
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                bool isInsideAlpha = alphaMap[y, x];
                bool shouldProcess = computeInside ? isInsideAlpha : !isInsideAlpha;
                if (shouldProcess)
                {
                    if (IsAtBoundary(alphaMap, x, y, width, height))
                    {
                        distanceField[y, x] = 0; // 边界距离为0
                    }
                    else
                    {
                        distanceField[y, x] = maxDist; // 初始化为最大距离
                    }
                }
                else
                {
                    distanceField[y, x] = maxDist; // 不处理的区域设为最大距离
                }
            }
        }
        // 使用Chamfer距离变换
        ComputeChamferTransform(distanceField, width, height);

        return distanceField;

    }
    /// <summary>
    /// 距离场计算 Charme
    /// </summary>
    /// <param name="alphaMap"></param>
    /// <param name="width"></param>
    /// <param name="height"></param>
    /// <returns></returns>
    private float[,] ComputeSignedDistanceField(bool[,] alphaMap, int width, int height)
    {
        // 计算内部距离场
        float[,] insideDistances = ComputeDistanceField(alphaMap, width, height, true);

        // 计算外部距离场
        float[,] outsideDistances = ComputeDistanceField(alphaMap, width, height, false);

        // 合并为有向距离场
        float[,] signedField = new float[height, width];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                if (alphaMap[y, x])
                {
                    // 内部：正距离
                    signedField[y, x] = insideDistances[y, x];
                }
                else
                {
                    // 外部：负距离
                    signedField[y, x] = -outsideDistances[y, x];
                }
            }
        }

        NormalizeSignedDistanceField(signedField, width, height);

        return signedField;
    }

    /// <summary>
    /// 是否处于边界
    /// </summary>
    /// <param name="alphaMap"></param>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <param name="width"></param>
    /// <param name="height"></param>
    /// <returns></returns>
    private bool IsAtBoundary(bool[,] alphaMap, int x, int y, int width, int height)
    {
        bool currentValue = alphaMap[y, x];

        for (int dy = -1; dy <= 1; dy++)
        {
            for (int dx = -1; dx <= 1; dx++)
            {
                if (dx == 0 && dy == 0) continue;

                int nx = x + dx;
                int ny = y + dy;

                // 边界检查
                if (nx < 0 || nx >= width || ny < 0 || ny >= height)
                {
                    // 图像边界：如果当前是Alpha区域，则认为在边界上
                    if (currentValue) return true;
                    continue;
                }

                // 如果邻域值与当前值不同，则在边界上
                if (alphaMap[ny, nx] != currentValue)
                {
                    return true;
                }
            }
        }
        return false;
    }


    private void ComputeChamferTransform(float[,] distances, int width, int height)
    {
        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                if (distances[y, x] <= 0) continue; // 跳过非Alpha区域和边界

                float minDist = distances[y, x];
                // 检查上方和左方的邻域
                minDist = Math.Min(minDist, distances[y - 1, x - 1] + 1.414f);
                minDist = Math.Min(minDist, distances[y - 1, x] + 1.0f);
                minDist = Math.Min(minDist, distances[y - 1, x + 1] + 1.414f);
                minDist = Math.Min(minDist, distances[y, x - 1] + 1.0f);

                distances[y, x] = minDist;
            }
        }

        for (int y = height - 2; y > 0; y--)
        {
            for (int x = width - 2; x > 0; x--)
            {
                if (distances[y, x] <= 0) continue; // 跳过非Alpha区域和边界
                float minDist = distances[y, x];
                // 检查下方和右方的邻域
                minDist = Math.Min(minDist, distances[y, x + 1] + 1.0f);
                minDist = Math.Min(minDist, distances[y + 1, x - 1] + 1.414f);
                minDist = Math.Min(minDist, distances[y + 1, x] + 1.0f);
                minDist = Math.Min(minDist, distances[y + 1, x + 1] + 1.414f);

                distances[y, x] = minDist;
            }
        }
    }

    /// <summary>
    /// 归一化距离场
    /// </summary>
    private void NormalizeSignedDistanceField(float[,] signedField, int width, int height)
    {
        float maxAbsDist = 0;

        // 找到最大绝对距离
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                maxAbsDist = Mathf.Max(maxAbsDist, Mathf.Abs(signedField[y, x]));
            }
        }

        // 归一化并映射到[0, 1]
        if (maxAbsDist > 0)
        {
            float scale = maxDistance / maxAbsDist;
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    float scaledDist = signedField[y, x] * scale;
                    // 限制在[-maxDistance, maxDistance]范围内
                    float clampedDist = Mathf.Clamp(signedField[y, x] * scale, -maxDistance, maxDistance);
                    signedField[y, x] = (clampedDist + maxDistance) / (2.0f * maxDistance);
                }
            }
        }
    }

    /// <summary>
    /// 创建测试贴图
    /// </summary>
    private Texture2D CreateDebugAlphaRegionTexture(bool[,] alphaRegion)
    {
        int height = alphaRegion.GetLength(0);
        int width = alphaRegion.GetLength(1);
        Texture2D debugAlphaRegion = new Texture2D(width, height, TextureFormat.RGBA32, false);
        Color[] pixels = new Color[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                int index = y * width + x;
                if (alphaRegion[y,x])
                {
                    pixels[index] = new Color(1, 1, 1, 1);
                }
                else
                {
                    pixels[index] = new Color(0, 0, 0, 1);
                }
            }
        }
        debugAlphaRegion.SetPixels(pixels);
        debugAlphaRegion.Apply();

        return debugAlphaRegion;
    }

    /// <summary>
    /// 创建SDF贴图
    /// </summary>
    private Texture2D CreateSDFTextureWithAlpha(float[,] signedDistanceField, float[,] alphaField, int width, int height)
    {
        Texture2D sdfTexture = new Texture2D(width, height, TextureFormat.RGBA32, false);
        Color[] pixels = new Color[width * height];

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                int index = y * width + x;
                float distance01 = signedDistanceField[y, x]; // 已经在[0,1]范围内
                float alpha01 = alphaField[y, x]; // 已经在[0,1]范围内
                // 使用RGBA编码距离值（提供更高精度）
                Vector2 encodedDistance = EncodeDistanceRG(distance01);

                // R通道: 距离场编码高位
                // G通道: 距离场编码低位  
                // B通道: 降采样的Alpha值
                // A通道: 设为1（或可用作其他用途）
                pixels[index] = new Color(encodedDistance.x, encodedDistance.y, alpha01, alpha01);
            }
        }
        sdfTexture.SetPixels(pixels);
        sdfTexture.Apply();

        return sdfTexture;
    }
    /// <summary>
    /// 保存贴图
    /// </summary>
    private void SaveTexture(Texture2D texture, string fileName,int width)
    {
        // 确保文件存在
        string fullPath = Path.Combine(FolderPath, fileName + ".png");
        byte[] bytes = texture.EncodeToPNG();
        File.WriteAllBytes(fullPath, bytes);

        AssetDatabase.Refresh();

        // 保存贴图到Unity
        TextureImporter textureImporter = AssetImporter.GetAtPath(fullPath) as TextureImporter;
        if (textureImporter != null)
        {
            textureImporter.textureType = TextureImporterType.Default;
            textureImporter.isReadable = false;
            textureImporter.filterMode = FilterMode.Bilinear;
            textureImporter.wrapMode = TextureWrapMode.Clamp;
            textureImporter.textureType = TextureImporterType.Sprite;
            textureImporter.sRGBTexture = false; // SDF数据不需要sRGB
            textureImporter.alphaIsTransparency = false;
            float scalePerUnit = (float)width / (float)inputTexWidth;
            textureImporter.spritePixelsPerUnit = scalePerUnit * 100;
            // 使用SerializedObject设置MeshType
            SerializedObject serializedObject = new SerializedObject(textureImporter);
            SerializedProperty meshTypeProperty = serializedObject.FindProperty("m_SpriteMeshType");

            if (meshTypeProperty != null)
            {
                meshTypeProperty.intValue = 0;
                serializedObject.ApplyModifiedProperties();
            }
            //textureImporter.alphaIsTransparency = false;

            TextureImporterPlatformSettings androidSettings = new TextureImporterPlatformSettings();
            androidSettings.name = "Android";
            androidSettings.overridden = true; // Override
            androidSettings.maxTextureSize = width;
            androidSettings.format = TextureImporterFormat.RGB24;
            androidSettings.allowsAlphaSplitting = false;

            TextureImporterPlatformSettings iponeSettings = new TextureImporterPlatformSettings();
            iponeSettings.name = "iPhone";
            iponeSettings.overridden = true; // Override
            iponeSettings.maxTextureSize = width;
            iponeSettings.format = TextureImporterFormat.RGB24;
            iponeSettings.allowsAlphaSplitting = false;

            textureImporter.SetPlatformTextureSettings(androidSettings);
            textureImporter.SetPlatformTextureSettings(iponeSettings);
            textureImporter.SaveAndReimport();
        }

        Debug.Log($"SDF texture saved to: {fullPath}");
    }
#endif


}

