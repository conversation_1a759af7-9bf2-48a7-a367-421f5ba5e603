#if UNITY_EDITOR
using UnityEngine;
using System.Collections.Generic;
using Fog;
using UnityEngine.PostProcessing;
using UnityEngine.UI;

public class UpdateMaterialShader: MonoBehaviour
{
    private List<Material> cachedMaterials = new List<Material>();
    private HashSet<int> materialIds = new HashSet<int>();
    public bool isProcessing = false;
    public float updateIntervalTime = 0.1f;
    private float lastTime = 0;
    private void Update()
    {
        if (!isProcessing)
        {
            return;
        }

        if (Time.realtimeSinceStartup - lastTime <= updateIntervalTime)
        {
            return;
        }

        lastTime = Time.realtimeSinceStartup;
        cachedMaterials.Clear();
        materialIds.Clear();
        
        var allObjects = FindObjectsOfType(typeof(GameObject), true);
        foreach (GameObject obj in allObjects)
        {
            CollectRendererMaterials(obj.GetComponent<Renderer>());
        }

        var objs = FindObjectsOfType(typeof(Image), true);
        foreach (var obj in objs)
        {
            Image image = (Image) obj;
            if (image && image.material != null)
            {
                int id = image.material.GetInstanceID();
                if (!materialIds.Contains(id))
                {
                    cachedMaterials.Add(image.material);
                    materialIds.Add(id);
                }
            }
        }

        GameObject go = GameObject.Find("UIRoot/CanvasWithMesh/UIMainSlg(Clone)/Auto_Right/right_scroll_view_1/effectMask/effectMask(Clone)");
        if (go)
        {
            Image image = go.GetComponent<Image>();
            if (image && image.material != null)
            {
                int id = image.material.GetInstanceID();
                if (!materialIds.Contains(id))
                {
                    cachedMaterials.Add(image.material);
                    materialIds.Add(id);
                }
            }
        }
        
        var objects = FindObjectsOfType(typeof(FogKit), true);
        foreach (var obj in objects)
        {
            FogKit fogKit = (FogKit) obj;
            if (fogKit && fogKit.m_BlurMaterial != null)
            {
                int id = fogKit.m_BlurMaterial.GetInstanceID();
                if (!materialIds.Contains(id))
                {
                    cachedMaterials.Add(fogKit.m_BlurMaterial);
                    materialIds.Add(id);
                }
            }
        }
        
        objects = FindObjectsOfType(typeof(PostProcessingBehaviour), true);
        foreach (var obj in objects)
        {
            PostProcessingBehaviour post = (PostProcessingBehaviour) obj;
            if (post)
            {
                post.enabled = false;
            }
        }

        //Debug.Log($"UpdateMaterials allObjects：{allObjects.Length}, {cachedMaterials.Count}");
        UpdateMaterials();
    }
    
    private void CollectRendererMaterials(Renderer renderer)
    {
        if (renderer == null) return;
        
        foreach (Material mat in renderer.materials)
        {
            int id = mat.GetInstanceID();
            if (!materialIds.Contains(id))
            {
                cachedMaterials.Add(mat);
                materialIds.Add(id);
            }
        }
    }
    
    private void UpdateMaterials()
    {
        var processedShaders = new HashSet<string>();
        var stopwatch = new System.Diagnostics.Stopwatch();
        stopwatch.Start();

        foreach (var material in cachedMaterials)
        {
            if (material == null) continue;

            string shaderName = material.shader.name;
            if (string.IsNullOrEmpty(shaderName)) continue;
            
            Shader shader = Shader.Find(shaderName);
            
            if (shader != null)
            {
                material.shader = shader;
                processedShaders.Add($"{material.name}_{material.shader.name}");
            }
        }

        stopwatch.Stop();
        //Debug.Log($"UpdateMaterials {processedShaders.Count} shaders in {stopwatch.ElapsedMilliseconds}ms");
    }
    
}
#endif