using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
public class CardSkillClip : PlayableAsset, ITimelineClipAsset
{
    [NonSerialized]
    public CardSkillBehaviour template = new CardSkillBehaviour();

    [NonSerialized]
    public SkillEffectConfig config = null;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.None; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<CardSkillBehaviour>.Create(graph, template);
        CardSkillBehaviour behaviour = playable.GetBehaviour();
        behaviour.config = config;
        return playable;
    }
}