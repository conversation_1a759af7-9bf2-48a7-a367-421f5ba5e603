using System;
using UnityEngine;
using UnityEngine.Playables;
using War.Script;

[Serializable]
public class TimeScaleBehaviour : PlayableBehaviour
{
    public float timeScale = 1;
    [Tooltip("优先使用此TimeScale")]
    public bool isCutDownOther;//是否打断其他

    public override void OnBehaviourPlay(Playable playable, FrameData info)
    {
        //打断   直接设置TimeScale值
        if (isCutDownOther)
        {
            TSControllerManeger.Instance.SetTimeScaleByTimelineTrack(timeScale, playable.GetTime() + playable.GetDuration());
        }
        else if (TSControllerManeger.Instance.TimeScaleIsEquals())//只有timescale为原始值(不为原始值则是播放timeline)
        {
            TSControllerManeger.Instance.SetTimeScaleByTimelineTrack(timeScale, playable.GetTime() + playable.GetDuration());
        }
        else
        {
            playable.SetDuration(0);
            //其他过渡时间都为0
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        if (playable.GetDuration() > 0 )
        {
            TSControllerManeger.Instance.ResetTimeScaleTrack(playable.GetTime(), timeScale);
        }
    }

}
