using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[TrackColor(0.25f, 0.88f, 0.60f)]
[TrackClipType(typeof(CrazingClip))]
[TrackBindingType(typeof(Transform))]
public class CrazingTrack : TrackAsset
{
    public int segment = 0;
    [NonSerialized]
    public BattleActorNode source;

    public override Playable CreateTrackMixer(PlayableGraph graph, GameObject go, int inputCount)
    {
        PlayableDirector director = go.GetComponent<PlayableDirector>();

        foreach (TimelineClip clip in GetClips())
        {
            var playableAsset = clip.asset as CrazingClip;
            if (playableAsset)
            {
                playableAsset.source = source;
            }
        }

        return ScriptPlayable<DefaultMixerBehaviour>.Create(graph, inputCount);
    }

    public override void GatherProperties(PlayableDirector director, IPropertyCollector driver)
    {
#if UNITY_EDITOR
        var comp = director.GetGenericBinding(this) as Transform;
        if (comp == null)
            return;
        using (var so = new UnityEditor.SerializedObject(comp))
        {
            var iter = so.GetIterator();
            while (iter.NextVisible(true))
            {
                if (iter.hasVisibleChildren)
                    continue;
                driver.AddFromName<Transform>(comp.gameObject, iter.propertyPath);
            }
        }
#endif
        base.GatherProperties(director, driver);
    }
}
