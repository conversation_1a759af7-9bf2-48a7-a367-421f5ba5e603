using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

[Serializable]
class TransformAnimationBehaviour : PlayableBehaviour
{
    public AnimationClip clip;
    public bool shouldDistinguishNodeFoward = false;
    public bool shouldInvertYAxis = false;

    [NonSerialized]
    public BattleActorNode node;
    private GameObject dummy;
    private struct TransformSnapshot
    {
        public Vector3 pos;
        public Quaternion rotate;
        public Vector3 scale;
    }

    TransformSnapshot node_shot;
    TransformSnapshot mask_shot;
    TransformSnapshot hud_shot;

    public override void OnGraphStart(Playable playable)
    {
        //if (!clip.legacy)
        //    clip.legacy = true;

        if (node)
        {
            node_shot = TakeSnapshot(node.transform);
            //if (node.Mask != null)
            //    mask_shot = TakeSnapshot(node.Mask.transform);
            if (node.Hud)
                hud_shot = TakeSnapshot(node.Hud.transform);

            if (dummy == null)
                dummy = new GameObject("TransformAnimationDummy");
        }
    }
    public override void ProcessFrame(Playable playable, FrameData info, object playerData)
    {
        if (node && node.card)
        {
            clip.SampleAnimation(dummy, (float)playable.GetTime());

            ApplyAnimation(node.transform, ref node_shot, dummy.transform);

            //if (node.Mask)
            //    ApplyAnimationRevert(node.Mask.transform, ref mask_shot, dummy.transform);

            if (node.Hud)
                ApplyAnimation(node.Hud.transform, ref hud_shot, dummy.transform);
        }
    }

    public override void OnBehaviourPause(Playable playable, FrameData info)
    {
        switch (info.evaluationType)
        {
            case FrameData.EvaluationType.Evaluate:
                if (info.weight == 0)
                {

                }
                break;
            case FrameData.EvaluationType.Playback:
                if (info.deltaTime != 0)
                {
                    CleanUp();
                }
                break;
        }
    }

    public override void OnGraphStop(Playable playable)
    {
        CleanUp();
    }

    private void CleanUp()
    {
        if (node)
        {
            Restore(node.transform, ref node_shot);

            //if (node.Mask)
            //    Restore(node.Mask.transform, ref mask_shot);

            if (node.Hud)
                Restore(node.Hud.transform, ref hud_shot);

            if (dummy)
            {
                if (Application.isPlaying)
                    GameObject.Destroy(dummy);
                else
                    GameObject.DestroyImmediate(dummy);
            }
        }
    }

    private TransformSnapshot TakeSnapshot(Transform transform)
    {
        TransformSnapshot shot = new TransformSnapshot();
        shot.pos = transform.localPosition;
        shot.rotate = transform.localRotation;
        shot.scale = transform.localScale;
        return shot;
    }

    private void Restore(Transform transform, ref TransformSnapshot shot)
    {
        transform.localPosition = shot.pos;
        transform.localRotation = shot.rotate;
        transform.localScale = shot.scale;
    }

    /// <summary>
    /// dir == 0,-1,1
    /// </summary>
    /// <param name="dir"></param>
    /// <param name="value"></param>
    public void ApplySign(int dir, ref float value)
    {
        if (dir == -1)
        {
            value *= dir;
        }
        //dir == 1不需要乘
    }

    public void ApplySign(Vector3 dir, ref Vector3 value)
    {
        int _dir = Math.Sign(dir.x);
        ApplySign(_dir, ref value.x);

        _dir = Math.Sign(dir.y);
        if (shouldInvertYAxis)
        {
            _dir *= -1;
        }
        ApplySign(_dir, ref value.y);

        _dir = Math.Sign(dir.z);
        ApplySign(_dir, ref value.z);
    }

    private void ApplyAnimation(Transform transform, ref TransformSnapshot shot, Transform animation)
    {
        Vector3 pos = animation.localPosition;
        if(shouldDistinguishNodeFoward)
        {
            ApplySign(node.torward, ref pos);
        }

        transform.localPosition = shot.pos + pos;
        transform.localRotation = shot.rotate * animation.localRotation;
        transform.localScale = Vector3.Scale(shot.scale, animation.localScale);
    }

    private void ApplyAnimationRevert(Transform transform, ref TransformSnapshot shot, Transform animation)
    {
        Vector3 pos = animation.localPosition;
        if (shouldDistinguishNodeFoward)
        {
            ApplySign(node.torward, ref pos);
        }
        pos.x = -pos.x;

        transform.localPosition = shot.pos + pos;
        transform.localRotation = shot.rotate * animation.localRotation;
        transform.localScale = Vector3.Scale(shot.scale, animation.localScale);
    }
}
