using System;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using War.Battle;

public class SpriteColorClip : PlayableAsset, ITimelineClipAsset
{
    public SpriteColorBehaviour template = new SpriteColorBehaviour();

    [NonSerialized]
    public BattleActorNode node = null;
    [NonSerialized]
    public LayerMask mask = -1;

    public ClipCaps clipCaps
    {
        get { return ClipCaps.Blending; }
    }

    public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
    {
        var playable = ScriptPlayable<SpriteColorBehaviour>.Create(graph, template);
        SpriteColorBehaviour behaviour = playable.GetBehaviour();
        behaviour.node = node;
        behaviour.mask = mask;
        return playable;
    }
}
