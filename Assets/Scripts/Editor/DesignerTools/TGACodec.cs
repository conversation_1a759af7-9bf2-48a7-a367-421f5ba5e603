using System.IO;
using UnityEngine;
using System;

public class ImageData
    {
        public int Width { get; set; }
        public int Height { get; set; }
        public int Depth { get; set; }

        //byte[] data = null;

        public TextureFormat Format { get; set; }

        public int BytesPerPixel
        {
            get
            {
                switch(Format)
                {
                    case TextureFormat.ARGB32:
                        return 4;
                    case TextureFormat.RGB24:
                        return 3;
                    default:
                        throw new NotSupportedException();
                }
            }
           
        }
    }

    public interface ImageCodec
    {
        void WriteFileHeader(FileStream file, ImageData image);

        ImageData ReadFileHeader(FileStream file);

        void Encode(FileStream file, ImageData image, Color32[] data, int dataWidth, int dataHeight, int xBase, int yBase);
        void Encode(FileStream file, Color32[] data, int width, int height, TextureFormat format);

        Color32[] Decode(FileStream file, ImageData image, int dataWidth, int dataHeight, int xBase, int yBase);
    }

public class TGACodec : ImageCodec
{
    private const int m_HeaderSize = 18;

    public void WriteFileHeader(FileStream file, ImageData image)
    {
        file.Seek(0, SeekOrigin.Begin);

        byte idlength = 0;
        byte colourmaptype = 0;
        byte datatypecode = 2;   // uncompressed RGB
        short colourmaporigin = 0;
        short colourmaplength = 0;
        byte colourmapdepth = 0;
        short x_origin = 0;
        short y_origin = 0;
        short width = (short)image.Width;
        short height = (short)image.Height;
        byte bitsperpixel;
        byte imagedescriptor;

        switch (image.Format)
        {
            case TextureFormat.RGB24:
                bitsperpixel = 24;    // 24bit no alpha format
                imagedescriptor = 0;  // for targa 24 
                break;
            case TextureFormat.ARGB32:
                bitsperpixel = 32;    // 32bit with alpha format
                imagedescriptor = 8;  // 8 for targa 32
                break;
            default:
                throw new NotSupportedException(string.Format("{0} not support!", image.Format));
        }

        BinaryWriter writer = new BinaryWriter(file);
        writer.Write(idlength);
        writer.Write(colourmaptype);
        writer.Write(datatypecode);
        writer.Write(colourmaporigin);
        writer.Write(colourmaplength);
        writer.Write(colourmapdepth);
        writer.Write(x_origin);
        writer.Write(y_origin);
        writer.Write(width);
        writer.Write(height);
        writer.Write(bitsperpixel);
        writer.Write(imagedescriptor);

        file.Seek(m_HeaderSize + width * height * image.BytesPerPixel, SeekOrigin.Begin);
    }

    public ImageData ReadFileHeader(FileStream file)
    {
        file.Seek(0, SeekOrigin.Begin);
        BinaryReader reader = new BinaryReader(file);

#pragma warning disable 0219
        byte idlength = reader.ReadByte();
        byte colourmaptype = reader.ReadByte();
        byte datatypecode = reader.ReadByte();   // only suport type 2 uncompressed RGB
        short colourmaporigin = reader.ReadInt16();
        short colourmaplength = reader.ReadInt16();
        byte colourmapdepth = reader.ReadByte();
        short x_origin = reader.ReadInt16();
        short y_origin = reader.ReadInt16();
        short width = reader.ReadInt16();
        short height = reader.ReadInt16();
        byte bitsperpixel = reader.ReadByte();   // just implement 24bit no alpha format
        byte imagedescriptor = reader.ReadByte(); // for targa 24 
#pragma warning restore


        ImageData data = new ImageData();
        data.Width = width;
        data.Height = height;
        data.Depth = 1;

        switch (bitsperpixel)
        {
            case 24:
                data.Format = TextureFormat.RGB24;
                if (imagedescriptor != 0)
                    throw new NotSupportedException("");
                break;
            case 32:
                data.Format = TextureFormat.ARGB32;
                if (imagedescriptor != 8)
                    throw new NotSupportedException("");
                break;
            case 16:
                throw new NotSupportedException("Tagar 16 not support!");
            default:
                throw new InvalidDataException("TGA Data corrupt");
        }

        return data;
    }

    public void Encode(FileStream file, ImageData image, Color32[] data, int dataWidth, int dataHeight, int xBase, int yBase)
    {
        int bytesPerPixel = image.BytesPerPixel;
        bool hasAlpha = image.Format == TextureFormat.ARGB32;

        byte[] lineData = new byte[dataWidth * bytesPerPixel];

        if (hasAlpha)
        {
            for (int row = 0; row < dataHeight; row++)
            {
                for (int column = 0; column < dataWidth; column++)
                {
                    Color32 color = data[row * dataWidth + column];
                    lineData[column * bytesPerPixel + 0] = color.b;
                    lineData[column * bytesPerPixel + 1] = color.g;
                    lineData[column * bytesPerPixel + 2] = color.r;
                    lineData[column * bytesPerPixel + 3] = color.a;
                }

                file.Seek(m_HeaderSize + (image.Width * (yBase + row) + xBase) * bytesPerPixel, SeekOrigin.Begin);
                file.Write(lineData, 0, lineData.Length);
            }
        }
        else
        {
            for (int row = 0; row < dataHeight; row++)
            {
                for (int column = 0; column < dataWidth; column++)
                {
                    Color32 color = data[row * dataWidth + column];
                    lineData[column * bytesPerPixel + 0] = color.b;
                    lineData[column * bytesPerPixel + 1] = color.g;
                    lineData[column * bytesPerPixel + 2] = color.r;
                }

                file.Seek(m_HeaderSize + (image.Width * (yBase + row) + xBase) * bytesPerPixel, SeekOrigin.Begin);
                file.Write(lineData, 0, lineData.Length);
            }
        }
    }

    public Color32[] Decode(FileStream file, ImageData image, int dataWidth, int dataHeight, int xBase, int yBase)
    {
        int bytesPerPixel = image.BytesPerPixel;
        bool hasAlpha = image.Format == TextureFormat.ARGB32;

        Color32[] data = new Color32[dataWidth * dataHeight];

        byte[] lineData = new byte[dataWidth * bytesPerPixel];

        if (hasAlpha)
        {
            for (int row = 0; row < dataHeight; row++)
            {
                file.Seek(m_HeaderSize + (image.Width * (yBase + row) + xBase) * bytesPerPixel, SeekOrigin.Begin);
                file.Read(lineData, 0, lineData.Length);

                for (int column = 0; column < dataWidth; column++)
                {
                    int lineIndex = column * bytesPerPixel;
                    int colorIndex = row * dataWidth + column;

                    data[colorIndex].b = lineData[lineIndex + 0];
                    data[colorIndex].g = lineData[lineIndex + 1];
                    data[colorIndex].r = lineData[lineIndex + 2];
                    data[colorIndex].a = lineData[lineIndex + 3];
                }
            }
        }
        else
        {
            for (int row = 0; row < dataHeight; row++)
            {
                file.Seek(m_HeaderSize + (image.Width * (yBase + row) + xBase) * bytesPerPixel, SeekOrigin.Begin);
                file.Read(lineData, 0, lineData.Length);

                for (int column = 0; column < dataWidth; column++)
                {
                    int lineIndex = column * bytesPerPixel;
                    int colorIndex = row * dataWidth + column;

                    data[colorIndex].b = lineData[lineIndex + 0];
                    data[colorIndex].g = lineData[lineIndex + 1];
                    data[colorIndex].r = lineData[lineIndex + 2];
                    data[colorIndex].a = 255;
                }

            }
        }

        return data;
    }

    public void Encode(FileStream file, Color32[] data, int width, int height, TextureFormat format)
    {
        if (format == TextureFormat.ARGB32 || format == TextureFormat.RGB24)
        {
            ImageData image = new ImageData();
            image.Width = width;
            image.Height = height;
            image.Depth = 1;
            image.Format = format;

            WriteFileHeader(file, image);
            Encode(file, image, data, width, height, 0, 0);
        }
    }
}


