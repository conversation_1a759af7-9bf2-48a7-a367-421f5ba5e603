using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Security.Cryptography;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System;
using UnityEngine.UI;

/*
 * 该文件夹下的哪些精灵图片没有被使用  
 * TIP:代码可以优化
*/

public class FindNoUseImage
{

    static private string szSelectedPath = null;
    [MenuItem("Assets/Find No Use Image", true, 12)]
    static private bool Find()
    {
        bool res = false;
        do
        {
            EditorSettings.serializationMode = SerializationMode.ForceText;
            szSelectedPath = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(szSelectedPath))
            {
                Debug.Log("路径不存在");
                break;
            }


            List<string> withoutExtensions = new List<string>() { ".png",".jpg"};
            string[] arrImgPath = GetFilesPath(szSelectedPath, withoutExtensions);
            if (null == arrImgPath)
            {
                Debug.Log("获取图片路径失败");
                break;
            }
            
            withoutExtensions = new List<string>() { ".prefab" };
            string[] arrPrefabPath = GetFilesPath("Assets", withoutExtensions);
            if (null == arrPrefabPath)
            {
                Debug.Log("获取预制体路径失败");
                break;
            }
            Dictionary<string, string> dicPrefabText = GetFilesText(arrPrefabPath);
            if (null == dicPrefabText)
            {
                Debug.Log("获取预制体内容失败");
                break;
            }

            Dictionary<string, string> dicMD5 = File2MD5(arrImgPath);
            if (null == dicMD5)
            {
                Debug.Log("所有图片生成MD5失败");
                break;
            }


            Dictionary<string, List<string>> dicOnlyMD5 = GetOnlyMD5(dicMD5);
            if (null == dicOnlyMD5)
            {
                Debug.Log("相同图片生成MD5失败");
                break;
            }

            Dictionary<string, KeyValuePair<string, Sprite>> dicSprite = Img2Sprite(dicOnlyMD5);
            if (null == dicSprite)
            {
                Debug.Log("图片生成Sprite失败");
                break;
            }

            List<string> dicNoUseImgInPre = Find(dicOnlyMD5, dicSprite, dicPrefabText);

            withoutExtensions = new List<string>() { ".unity", ".mat", ".asset" };
            string[] arrOtherPath = GetFilesPath("Assets", withoutExtensions);
            if (null == arrOtherPath)
            {
                Debug.Log("获取其他文件路径失败");
                break;
            }

            Dictionary<string, string> dicOtherText = GetFilesText(arrOtherPath);
            if (null == dicOtherText)
            {
                Debug.Log("获取其他文件（.unity.mat.asset）内容失败");
                break;
            }


            List<string> dicNoUseImg = CheckResult(dicNoUseImgInPre, dicOtherText);

            SaveResult(dicNoUseImg);
            Debug.Log("查找图片结束");


        } while (false);
        return res;
    }

    //检查图片是否被不是预制体的资源使用
    static private List<string> CheckResult(List<string> result,Dictionary<string,string> arrFilesPath)
    {
        List<string> res = null;
        do
        {
            if (null == result || 0 == result.Count || 0 == arrFilesPath.Count || null ==arrFilesPath)
                break;
            res = new List<string>();

            for (int nIndex = 0; nIndex<result.Count;nIndex++)
            {
                if (!IsMatch(arrFilesPath, result[nIndex]))
                    res.Add(result[nIndex]);
            }
            

        } while (false);
        return res;
    }


    static private bool SaveResult(List<string> dicNoUseImg)
    {
        bool ret = false;
        do
        {
            string szNoUseImgPath = szSelectedPath + "/NoUseImg.txt";//没有使用图片的路径
            File.Delete(szNoUseImgPath);

            if (null == dicNoUseImg || 0 == dicNoUseImg.Count)
                break;

            for (int nIndex = 0; nIndex < dicNoUseImg.Count; ++nIndex)
            {
                string noUseImgPath = dicNoUseImg[nIndex];
                File.AppendAllText(szNoUseImgPath, noUseImgPath + "\r\n");
            }


        } while (false);
        return ret;
    }


    //字符串数组替换
    static private string[] ArrStrReplace(string[] szData, char oldValue, char newValue)
    {
        string[] res = null;
        do
        {
            if (null == szData || 0 == szData.Length)
                break;
            for (int nIndex = 0; nIndex < szData.Length; ++nIndex)
                szData[nIndex] = szData[nIndex].Replace(oldValue, newValue);
            res = szData;
        } while (false);
        return res;
    }

    //字符串数组替换
    static private string[] ArrStrReplace(string[] szData, string oldValue, string newValue)
    {
        string[] res = null;
        do
        {
            if (null == szData || 0 == szData.Length || string.IsNullOrEmpty(oldValue) || string.IsNullOrEmpty(newValue))
                break;
            for (int nIndex = 0; nIndex < szData.Length; ++nIndex)
                szData[nIndex] = szData[nIndex].Replace(oldValue, newValue);


            res = szData;
        } while (false);
        return res;
    }

    //查找文件夹中资源
    static private string[] GetFilesPath(string path, List<string> withoutExtensions)
    {
        string[] files = null;
        if (!string.IsNullOrEmpty(path) && Directory.Exists(path) && null != withoutExtensions && 0!= withoutExtensions.Count)
        {
            files = FindFiles(withoutExtensions, path);
            files = ArrStrReplace(files, "\\", "/");
        }
        return files;
    }

    static private string[] FindFiles(List<string> param, string path)
    {
        string[] res = null;
        List<string> withoutExtensions = param;
        if (null != withoutExtensions && 0 != withoutExtensions.Count && !string.IsNullOrEmpty(path))
        {
            string[] files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories)
                .Where(s => !string.IsNullOrEmpty(Path.GetExtension(s))
                && withoutExtensions.Contains(Path.GetExtension(s).ToLower())).ToArray();

            res = files;
        }
        return res;

    }

    //将制定文件内容转化为MD5
    //返回值： key：MD5，value：路径
    static private Dictionary<string, string> File2MD5(string[] arrFilePath)
    {
        Dictionary<string, string> dicMD5 = null;
        do
        {
            if (null == arrFilePath || 0 == arrFilePath.Length)
                break;
            dicMD5 = new Dictionary<string, string>();
            for (int nIndex = 0; nIndex < arrFilePath.Length; ++nIndex)//遍历生成MD5
            {
                string szMD5 = File2MD5(arrFilePath[nIndex]);
                if (null != szMD5 && !szMD5.Equals(""))
                    dicMD5.Add(arrFilePath[nIndex], szMD5);
            }
        } while (false);
        return dicMD5;
    }

    //将图片转化为Sprite
    //返回值： key：MD5，value：路径，精灵图片Sprite
    static private Dictionary<string, KeyValuePair<string, Sprite>> Img2Sprite(Dictionary<string, List<string>> dicSameMD5)
    {
        Dictionary<string, KeyValuePair<string, Sprite>> res = null;
        do
        {
            if (0 == dicSameMD5.Count || null == dicSameMD5)
                break;
            Dictionary<string, KeyValuePair<string, Sprite>> dicSprite = new Dictionary<string, KeyValuePair<string, Sprite>>();

            foreach (KeyValuePair<string, List<string>> kv in dicSameMD5)
            {
                string imgPath = kv.Value[0];
                Sprite sprite = CreateSprite(imgPath);

                if (null != sprite)                 //可以生成精灵图片
                {
                    KeyValuePair<string, Sprite> imgSprite = new KeyValuePair<string, Sprite>(imgPath, sprite);
                    dicSprite.Add(kv.Key, imgSprite);
                }
                else//不能生成精灵图片的要写入到文件
                {
                    List<string> arrPath = kv.Value;
                    for (int nIndex = 0; nIndex < arrPath.Count; ++nIndex)
                        Debug.Log(" 无法创建精灵图片 " + arrPath[nIndex]);
                    Debug.Log("---------------------------------------");
                }
            }

            res = dicSprite;
        } while (false);
        return res;
    }

    //将制定文件内容转化为MD5
    static private string File2MD5(string path)
    {
        string res = null;
        if (File.Exists(path))
        {
            byte[] data = File.ReadAllBytes(path);
            MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
            byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
            md5.Clear();

            string destString = "";
            for (int i = 0; i < md5Data.Length; i++)
            {
                destString += System.Convert.ToString(md5Data[i], 16).PadLeft(2, '0');
            }
            destString = destString.PadLeft(32, '0');
            res = destString;
        }

        return res;
    }

    //获取相同MD5的字典
    //返回值： key：MD5，value：路径列表
    static private Dictionary<string, List<string>> GetOnlyMD5(Dictionary<string, string> dicMD5)
    {
        Dictionary<string, List<string>> res = null;
        do
        {
            if (0 == dicMD5.Count || null == dicMD5)
                break;
            Dictionary<string, List<string>> dicSame = new Dictionary<string, List<string>>();
            foreach (KeyValuePair<string, string> kv in dicMD5)
            {
                List<string> listMD5 = null;
                if (dicSame.TryGetValue(kv.Value, out listMD5))//相同
                {
                    listMD5.Add(kv.Key);
                }
                else if (null == listMD5)
                {
                    listMD5 = new List<string>();
                    listMD5.Add(kv.Key);
                    dicSame.Add(kv.Value, listMD5);
                }
            }

            res = dicSame;
        } while (false);
        return res;
    }

    //key:文件路径，value：文件内容
    static private Dictionary<string, string> GetFilesText(string[] arrFilesPath)
    {
        Dictionary<string, string> res = null;
        do
        {
            if (null == arrFilesPath || 0 == arrFilesPath.Length)
                break;
            res = new Dictionary<string, string>();
            for (int nIndex = 0; nIndex < arrFilesPath.Length; ++nIndex)
            {
                if (!string.IsNullOrEmpty(arrFilesPath[nIndex]) && File.Exists(arrFilesPath[nIndex]))
                {
                    string szContent = File.ReadAllText(arrFilesPath[nIndex]);
                    if (string.IsNullOrEmpty(szContent))
                        Debug.Log(arrFilesPath[nIndex] + "读取文件内容失败");
                    else
                    {
                        string szText = null;
                        if (!res.TryGetValue(arrFilesPath[nIndex], out szText))
                            res.Add(arrFilesPath[nIndex], szContent);
                    }
                }

            }

        } while (false);

        return res;
    }

    //匹配源文件组中是否包含模板文件
    static private List<string> Match(Dictionary<string,string> inputs, string pattern)
    {
        List<string> res = null;
        do
        {
            if (null == inputs || 0 == inputs.Count || string.IsNullOrEmpty(pattern))
                break;
            List<string> listMatch = new List<string>();
            foreach (KeyValuePair<string,string> kv in inputs)
            {
                if (IsMatch(kv.Value, pattern))
                    listMatch.Add(kv.Key);
            }
            res = listMatch;
        } while (false);
        return res;
    }

    //匹配源文件中是否包含模板文件
    static private bool IsMatch(string input, string patternFile)
    {
        bool res = false;
        do
        {
            if (string.IsNullOrEmpty(input) || string.IsNullOrEmpty(patternFile))
                break;
            string guid = AssetDatabase.AssetPathToGUID(patternFile);
            if (string.IsNullOrEmpty(guid))
                break;

            if (Regex.IsMatch(input, guid))
                res = true;
        } while (false);
        return res;
    }

    //匹配源文件中是否包含模板文件
    static private bool IsMatch(Dictionary<string, string> arrFilesPath, string patternFile)
    {
        bool res = false;
        do
        {
            if (null == arrFilesPath || 0 == arrFilesPath.Count || string.IsNullOrEmpty(patternFile))
                break;

            foreach (KeyValuePair<string, string> kv in arrFilesPath)
            {
                if (true == (res = IsMatch(kv.Value, patternFile)))
                    break;
            }
        } while (false);
        return res;
    }

    //修改预制体，将预制体中相同图片的替换 
    //key:被替换预制体的路径，value：被替换图片列表<被替换图片，替换图片>
    static private List<string> Find(Dictionary<string, List<string>> dicImgMD5, Dictionary<string, KeyValuePair<string, Sprite>> dicSprite, Dictionary<string,string> dicPrefabPathText)
    {
        List<string> res = null;
        do
        {
            if (null == dicImgMD5 || 0 == dicImgMD5.Count
                || null == dicSprite || 0 == dicSprite.Count
                || null == dicPrefabPathText || 0 == dicPrefabPathText.Count)
                break;
            res = new List<string>();

            foreach (KeyValuePair<string, KeyValuePair<string, Sprite>> kv in dicSprite)
            {
                List<string> listPath = null;
                if (dicImgMD5.TryGetValue(kv.Key, out listPath))
                {

                    for (int nIndex = 0; nIndex < listPath.Count; ++nIndex)
                    {
                        string szCheckPath = listPath[nIndex];
                        List<string> listPrefabPath = Match(dicPrefabPathText, szCheckPath);//检索哪个预制体用了
                        if (null == listPrefabPath || 0 == listPrefabPath.Count)//没找到使用该图片
                            res.Add(szCheckPath);
                    }
                }
            }
        } while (false);
        return res;
    }

    static private Sprite CreateSprite(string imgPath)
    {
        return string.IsNullOrEmpty(imgPath) ? null : (Sprite)AssetDatabase.LoadAssetAtPath(imgPath, typeof(Sprite));
    }

    [MenuItem("Assets/Find No Use Image", true, 13)]
    static private bool VFind()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        return (!string.IsNullOrEmpty(path));
    }
}