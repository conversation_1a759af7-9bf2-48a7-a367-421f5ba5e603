using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Text.RegularExpressions;
using System.IO;
using System.Text;
public class FindLocalLang 
{
	[MenuItem("Designer/Find Chinese Text from Prefab")]
    static void FindChineseTextFromPrefab()
	{
        EditorUtility.DisplayProgressBar("提取中","请稍后",0);
        string[] pathes=  Directory.GetFiles("Assets/UI/Prefabs", "*.prefab", SearchOption.AllDirectories);
        StringBuilder str = new StringBuilder();
        for(int i=0;i< pathes.Length;i++)
        {
            string path = pathes[i];
            EditorUtility.DisplayProgressBar("提取中", "请稍后", (float)i/ pathes.Length);
            GameObject obj= AssetDatabase.LoadAssetAtPath(path,typeof(GameObject)) as GameObject;
            if(obj!=null)
            {
                SearchPrefabString(obj.transform,ref str);
            }
        }
        string tPath = Application.dataPath + "/prefabChineseLang.txt";
        using (StreamWriter sw = new StreamWriter(tPath, false, Encoding.UTF8))
        {
            sw.Write(str);
            sw.Flush();
        }
        AssetDatabase.Refresh();
        EditorUtility.ClearProgressBar();
    }


    public static bool HasChinese(string str)
    {
        return Regex.IsMatch(str, @"[\u4e00-\u9fa5]");
    }


    static public void SearchPrefabString(Transform root, ref StringBuilder str)
    {
        // List<string> strs = new List<string>();
        int  count = root.childCount;
        for(int i=0;i<count;i++)
        {
            Transform chind = root.GetChild(i);
            Text label = chind.GetComponent<Text>();
            if (label != null)
            {
                string text = label.text;
                if (HasChinese(text))
                {
                    //strs.Add(text);
                    str.Append(text + "\n");
                }
            }
            if (chind.childCount > 0)
                SearchPrefabString(chind,ref str);
        }
    }
      
}
