using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityEditor;

public class TimelineOffsetEditor : EditorWindow
{
    float offset;
    float flashOffset;

    [MenuItem("Designer/Timeline偏移编辑器")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        TimelineOffsetEditor window = (TimelineOffsetEditor)EditorWindow.GetWindow(typeof(TimelineOffsetEditor));
        window.Show();
    }

    private void OnGUI()
    {
        GUILayout.Label("_skill.playable start整体偏移");
        offset = EditorGUILayout.FloatField("开始时间偏移", offset);
        flashOffset =EditorGUILayout.FloatField("flashTrack结束时间偏移", flashOffset);
        if(GUILayout.Button("偏移")){
            DoOffsetForAllSkill();
        }

        GUILayout.Label("");
        GUILayout.Label("统一更换flash特效文件，目录：");
        GUILayout.Label("Assets/Art/Effects/Prefabs/Characters_common/ef_flash.prefab");
        
        if (GUILayout.Button("替换"))
        {
            ReplaceFlashTrack();
        }        
    }


    private void DoOffsetForAllSkill()
    {
        string[] files = System.IO.Directory.GetFiles(Application.dataPath + "/Art/Skill", "*_skill.playable", System.IO.SearchOption.AllDirectories);
        foreach (string path in files)
        {
            string t = "Assets" + path.Replace(Application.dataPath, "").Replace("\\", "/");
            TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(t);
            Do(timeline, offset,flashOffset);
        }
    }


    private void Do(TimelineAsset timeline, float offset,float flashOffset)
    {
        TrackAsset replaceTrack = null;
        TrackAsset flashTrack = null;
        AnimationTriggerTrack triggerTrack = null;
        JumpoutTrack jumpTrack = null;

        foreach (TrackAsset track in timeline.GetOutputTracks())
        {
            if (track is ParticleAttackTrack 
                || track is EventTrack
                || track is AudioTrack
                || track is CameraShakeTrack)
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    clip.start += offset;
                }
            }
            else if (track is JumpoutTrack)
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    clip.start += offset;
                }
                jumpTrack = track as JumpoutTrack;
            }
            else if (track is AnimationTriggerTrack)
            {
                triggerTrack = track as AnimationTriggerTrack;
            }
            else if (track is FlashTrack && track.name == "Flash Track")
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    
                    //    FlashClip flash = clip.asset as FlashClip;
                    //    if (flash && flash.template.style == FlashBehaviour.Style.Grid)
                    //    {
                    //        flashTrack = track;
                    //        break;
                    //    }
                }
            }
            else if (track is ParticleSpawnTrack)
            {
                replaceTrack = track as ParticleSpawnTrack;
            }
            else if (track is FlashTrack && track.name == "Flash Track1")
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    clip.duration += flashOffset;
                }
            }
        }

        if (replaceTrack)
        {
            timeline.DeleteTrack(replaceTrack);
            replaceTrack = timeline.CreateTrack<ParticleAttackTrack>(null, "Particle Attack Track");

            TimelineClip clip = replaceTrack.CreateDefaultClip();
            ParticleAttackClip particleAttack = clip.asset as ParticleAttackClip;
            clip.duration = 1;
            particleAttack.template.prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Effects/Prefabs/Characters_common/skill_common_shifang.prefab");
        }

        if (flashTrack)
        {
            timeline.DeleteTrack(flashTrack);
        }

        if (jumpTrack && jumpTrack.muted == false && triggerTrack)
        {
            foreach (TimelineClip clip in triggerTrack.GetClips())
            {
                clip.start += offset;
            }
        }

        EditorUtility.SetDirty(timeline);
    }

    private void ReplaceFlashTrack()
    {
        string[] files_skill = System.IO.Directory.GetFiles(Application.dataPath + "/Art/Skill", "*_skill.playable", System.IO.SearchOption.AllDirectories);
        GameObject prefab_skill = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Effects/Prefabs/Characters_common/ef_flash.prefab");
        foreach (string path in files_skill)
        {
            string t = "Assets" + path.Replace(Application.dataPath, "").Replace("\\", "/");
            TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(t);
            DoReplaceFlash(timeline, prefab_skill);
        }
        

        //string[] files_attack = System.IO.Directory.GetFiles(Application.dataPath + "/Art/Skill", "*_attack.playable", System.IO.SearchOption.AllDirectories);
        //GameObject prefab_attack = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Effects/Prefabs/Characters_common/ef_flash_grid.prefab");
        //foreach (string path in files_attack)
        //{
        //    string t = "Assets" + path.Replace(Application.dataPath, "").Replace("\\", "/");
        //    TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(t);
        //    DoReplaceFlash(timeline, prefab_attack);
        //}
    }   

    private void DoReplaceFlash(TimelineAsset timeline, GameObject prefab)
    {
        bool dirty = false;
        foreach (TrackAsset track in timeline.GetOutputTracks())
        {
            if (track is FlashTrack)
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    FlashClip flashClip = clip.asset as FlashClip;

                    if (flashClip.template.prefab.name == "ef_flash")
                    {
                        clip.start = 0;
                        clip.duration = 1.3f;
                        dirty = true;
                    }
                }
            }
            else if (track is ParticleAttackTrack)
            {
                foreach (TimelineClip clip in track.GetClips())
                {
                    ParticleAttackClip flashClip = clip.asset as ParticleAttackClip;

                    if (flashClip.template.prefab.name == "skill_common_shifang")
                    {
                        clip.start = 0;
                        clip.duration = 1.3f;
                        dirty = true;
                    }
                }
            }
        }
        if (dirty)
            EditorUtility.SetDirty(timeline);
    }

}
