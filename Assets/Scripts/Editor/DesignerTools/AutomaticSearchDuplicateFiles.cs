using System.IO;
using UnityEngine;
using UnityEditor;

using System.Text;
using System;
using System.Collections.Generic;

public class NewEditorScript1 : ScriptableObject
{
	[MenuItem("Designer/T Duplicate Files")]
	static void DoIt()
	{
		int a = 0;
		string[] list = AssetDatabase.GetAllAssetPaths();
		Dictionary<string, string> dic = new Dictionary<string, string>();
		int i = 0;
		for (i = 0; i < list.Length; i++)
		{
			string Object = Path.GetExtension(list[i]);                        //获取后缀名".***"
			//string filename = Path.GetFileName(list[i]);
			Object = Object.ToLower();
			if (Object == ".png" || Object == ".tga")
			{
				string Md5 = GetMD5HashFromFile(list[i]);

				if (!dic.ContainsKey(Md5) )
				{
					dic.Add(Md5, list[i]);
				}
				else
				{
					Debug.Log(list[i]);
					FileInfo fileInfo = new FileInfo(list[i]);
					a = a + (int)fileInfo.Length;
				}
			}
		}
		Debug.LogWarning(a);
	}

	private static string GetMD5HashFromFile(string fileName)
	{
		try
		{
			FileStream file = new FileStream(fileName, FileMode.Open);
			System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
			byte[] retVal = md5.ComputeHash(file);
			file.Close();

			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < retVal.Length; i++)
			{
				sb.Append(retVal[i].ToString("x2"));
			}
			return sb.ToString();
		}
		catch (Exception ex)
		{
			throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
		}
	}
}