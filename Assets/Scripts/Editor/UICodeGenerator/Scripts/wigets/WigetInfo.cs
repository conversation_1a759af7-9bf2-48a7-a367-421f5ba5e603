using System.Collections.Generic;
using UnityEngine;

namespace UICodeGenerator
{
    public class WidgetInfo
    {
        /// <summary>
        /// 组件类型
        /// </summary>
        public EWidgetType Type { get; private set; }

        /// <summary>
        /// 组件名字：组件前缀 + 对象名
        /// </summary>
        public string Name { get; private set; }

        /// <summary>
        /// 对象相对预设层级
        /// </summary>
        public string Path { get; private set; }

        public WidgetInfo() : this(EWidgetType.None, string.Empty) { }
        public WidgetInfo(EWidgetType type, string path)
        {
            Type = type;
            Path = path;
            string name = "";
			var splitnames = path.Split ('/');
			for (int i = splitnames.Length - 1; i >= 0; i--) {
				var nm = dealName (splitnames [i]);
				name  =nm+ "_" +name;
				name = name.TrimEnd ('_');
				if (nameDic.ContainsKey (name)) {
					continue;
				}
				break;
			}

			if (nameDic.ContainsKey (name)) {
				var go = GameObject.Find (path);
				if(go)
				{
					name = dealName (splitnames [splitnames.Length - 1]) + (uint)go.GetHashCode ();
				}
				else{
					Debug.LogError ("name fix error:" + path);
				}
			}
			nameDic [name] = true;

            Name = WidgetPrefix[type] + name;
        }

		public static string dealName(string aName)
		{
			aName = aName ?? "";
			aName = aName.Trim(' ','(',')');
			aName = aName.Replace (" ", "_");
			aName = aName.Replace ("(", "_");
			aName = aName.Replace (")", "_");
			aName = aName.Replace ("Auto_", "");

			return aName;
		}
		readonly public static Dictionary<string, bool> nameDic = new Dictionary<string, bool>();

        readonly public static Dictionary<EWidgetType, string> WidgetPrefix = new Dictionary<EWidgetType, string>
        {
            { EWidgetType.None, "" },
            { EWidgetType.Button, Config.PrefixButton },
            { EWidgetType.Text, Config.PrefixText },
            { EWidgetType.ScrollRectTable, Config.PrefixScroll },
			{ EWidgetType.Image, Config.PrefixImage},
			{ EWidgetType.RectTransform, Config.PrefixRectTransform}
        };

        readonly public static Dictionary<EWidgetType, string> WidgetTypeStr = new Dictionary<EWidgetType, string>
        {
            { EWidgetType.None, "" },
            { EWidgetType.Button, Config.TypeStrButton },
            { EWidgetType.Text, Config.TypeStrText },
            { EWidgetType.ScrollRectTable, Config.TypeStrScroll },
			{ EWidgetType.Image, Config.TypeStrImage},
			{ EWidgetType.RectTransform, Config.TypeStrRectTransform}
        };

        /// <summary>
        /// 类型字符串，Button和Text是包含双引号的，ScrollRect不包含
        /// </summary>
        public string TypeStr { get { return WidgetTypeStr[Type]; } }

        /// <summary>
        /// 组件定义
        /// </summary>
        public string WidgetDefine
        {
            get
            {
                ButtonCheck tem = new ButtonCheck(new ButtonInfo(Name));
                string FuncProxyName = "";
                if (TypeStr == WidgetTypeStr[EWidgetType.Button])
                {
                    FuncProxyName = tem.btn.BtnFuncProxyName;
                }
                return Config.WidgetDefine.Replace("#WidgetName#", Name).
                    Replace("#WidgetPath#", Path).Replace("#WidgetTypeStr#", TypeStr).Replace("#BtnFuncProxyName#", FuncProxyName);
            }
        }

    }
}
