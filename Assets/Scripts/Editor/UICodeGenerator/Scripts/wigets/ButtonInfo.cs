using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace UICodeGenerator
{
    sealed public class ButtonInfo
    {
        public string Name { get; private set; }
        public ButtonInfo(string name) { Name = name; }

        /// <summary>
        /// 获取按钮函数名
        /// </summary>
        public string BtnFunctionName { get { return Config.BtnFunctionName.Replace("#WidgetName#", Name); } }

        /// <summary>
        /// 获取按钮函数定义字段
        /// </summary>
        public string BtnFunctionLabel { get { return BtnFunction.Split('\r')[0]; } }

        /// <summary>
        /// 按钮函数的定义
        /// </summary>
        public string BtnFunction { get { return Config.BtnFunction.Replace("#BtnFunctionName#", BtnFunctionName); } }

        /// <summary>
        /// 按钮事件订阅字段，不判断是否为空
        /// </summary>
        public string BtnAddListenerNoCheck { get { return Config.BtnAddListenerNoCheck.Replace("#WidgetName#", Name).Replace("#BtnFuncProxyName#", BtnFuncProxyName); } }

        /// <summary>
        /// 按钮事件订阅字段
        /// </summary>
        public string BtnAddListener { get { return Config.BtnAddListener.Replace("#WidgetName#", Name).Replace("#BtnAddListenerNoCheck#", BtnAddListenerNoCheck); } }

        /// <summary>
        /// 按钮事件注销字段，不判断是否为空
        /// </summary>
        public string BtnRemoveListenerNoCheck { get { return Config.BtnRemoveListenerNoCheck.Replace("#WidgetName#", Name).Replace("#BtnFuncProxyName#", BtnFuncProxyName); } }

        /// <summary>
        /// 按钮事件注销字段
        /// </summary>
        public string BtnRemoveListener { get { return Config.BtnRemoveListener.Replace("#WidgetName#", Name).Replace("#BtnRemoveListenerNoCheck#", BtnRemoveListenerNoCheck); } }

        /// <summary>
        /// 按钮委托函数名
        /// </summary>
        public string BtnFuncProxyName { get { return Config.BtnFuncProxyName.Replace("#BtnFunctionName#", BtnFunctionName); } }

        /// <summary>
        /// 按钮委托函数
        /// </summary>
        public string BtnFuncProxy { get { return Config.BtnFuncProxy.Replace("#BtnFuncProxyName# ", BtnFuncProxyName).Replace("#BtnFunctionName#", BtnFunctionName); } }

        /// <summary>
        /// 按钮委托标签
        /// </summary>
        public string BtnFuncProxyLabel { get { return BtnFuncProxy.Split('\r')[0]; } }

        /// <summary>
        /// 按钮事件定义
        /// </summary>
        public string BtnFuncEventName { get { return Config.BtnWidgetEventName.Replace("#WidgetName#", Name).Replace("#BtnFuncProxyName#", BtnFuncProxyName); } }
    }
}
