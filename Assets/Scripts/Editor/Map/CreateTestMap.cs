using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;


public class CreateTestMap : EditorWindow
{
    string gridHeight = "";
    string gridWidth = "";
    GameObject gridGameObject;

    
    [MenuItem("Map/CreateTestMap")]
    public static void OnMenuItem()
    {
        CreateTestMap window = ScriptableObject.CreateInstance<CreateTestMap>();
        window.titleContent = new GUIContent("CreateTestMap");
        window.ShowUtility();
    }

    private void OnGUI()
    {
        GUILayout.Space(10);
        gridWidth = EditorGUILayout.TextField("Map Width", gridWidth);
        gridHeight = EditorGUILayout.TextField("Map Height", gridHeight);
        gridGameObject = (GameObject)EditorGUILayout.ObjectField("Grid Game Object", gridGameObject, typeof(GameObject), true);

        if (GUILayout.Button("Create"))
        {
            createGrid();
        }
    }
 
    /// <summary>
    /// 创建地图格子
    /// </summary>
    void createGrid()
    {
        // 创建父物体
        GameObject parentObj = new GameObject();
        parentObj.name = "tileGrid";
        parentObj.transform.localScale = new Vector3(1.5f, 1.5f, 1.5f);
        parentObj.transform.localRotation = Quaternion.Euler(-20.705f, -49.107f, 22.208f);
        for (int i = 0; i < Convert.ToInt16(gridWidth); i++)
        {
            for (int j = 0; j < Convert.ToInt16(gridHeight); j++)
            {
                GameObject nobj = (GameObject)GameObject.Instantiate(gridGameObject);
                nobj.gameObject.transform.parent = parentObj.transform;
                nobj.transform.localPosition = new Vector3(i, 0, j);
                nobj.transform.localRotation = Quaternion.Euler(30f, 45f, 0f);
                nobj.transform.localScale = new Vector3(0.6666669f, 0.6666669f, 0f);
                //Debug.Log("nobj" + (nobj.transform.localPosition.x) + "*" + (nobj.transform.localPosition.z));
                nobj.name = i + "_" + j;
                nobj.SetActive(true);

            }
        }
    }

}
