using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using UI.UGUIExtend;
using Sirenix.OdinInspector.Demos;

[CustomEditor(typeof(War.Game.GameManager))]
public class MapBrush : Editor
{
    private const string MAP_BYTES_PATH = "Assets/Art/Maps/ToT/res/{0}.bytes";
    private const string EVENT_KEY_NAME = "sid";
    Vector2 _scroll;
    //选择贴图的对象
    private Sprite m_sprite;
    //已添加所有的图片
    private List<Sprite> textures = new List<Sprite>(20);
    //是否使用笔刷
    //public bool canBrush = false;
    //当前选中的图片
    public Sprite selectedTexture;
    private int selectIndex = -1;
    private int selectPos
    {
        // Use EditorPrefs to hold persisntent user-variables.
        get { return GetConfig().TEvent.index; }
        set { GetConfig().TEvent.index = value; }
    }
    //flip功能
    private bool flipX = false;
    private bool flipY = false;

    private bool bDealEvent
    {
        // Use EditorPrefs to hold persisntent user-variables.
        get { return EditorPrefs.GetBool(DealEventmark, false); }
        set { EditorPrefs.SetBool(DealEventmark, value); }
    }
    // 操作笔刷
    private bool bToggleEvent = false;
    //用于预览笔刷
    private GameObject previewObject;

    string levelIdmark;
    string dealEventmark;

    //删除功能
    private bool delSprite = false;

    private int levelId
    {
        // Use EditorPrefs to hold persisntent user-variables.
        get { return EditorPrefs.GetInt(LevelIdmark, 0); }
        set { EditorPrefs.SetInt(LevelIdmark, value); }
    }

    public string LevelIdmark
    {
        get
        {
            if (levelIdmark == null)
            {
                levelIdmark = "GameManager" + target.GetHashCode().ToString();
            }
            return levelIdmark;
        }

    } 
    public string DealEventmark
    {
        get
        {
            if (dealEventmark == null)
            {
                dealEventmark = "GameManagerDealEvent" + target.GetHashCode().ToString();
            }
            return dealEventmark;
        }

    }



    private int spriteWidth = 64;
    private int spriteHeight = 64;
    private int spriteMargin = 6;
    public float scale = 0.7f;

    public Vector2 gridOffset = Vector3.zero;
    public float gridScale = 0.7f;

    private Color selectedColor = new Color(0.5f, 0.8f, 1f); 

    private int maxIndex = int.MaxValue;

    //[MenuItem("Map/Map Brush")]
    //public static void OnMenuItem()
    //{
    //    MapBrush window = ScriptableObject.CreateInstance<MapBrush>();
    //    window.titleContent = new GUIContent("Map Brush");
    //    window.ShowUtility();
    //}

    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        OnGUI();
    }

    private void OnGUI()
    {
        GUILayout.Space(10);

        EditorGUILayout.LabelField("!!! svn更新时请注意先备份本地Assets/Art/Maps/ToT/TotEventConfig.asset文件", EditorStyles.boldLabel);
        m_sprite = EditorGUILayout.ObjectField("添加精灵", m_sprite, typeof(Sprite), false) as Sprite;
        if (delSprite == false)
            AddNewSprite();

        EditorGUILayout.BeginHorizontal();
        var nlevelId = EditorGUILayout.IntField("LevelId", levelId);
        if (GUILayout.Button("Update"))
        {
            var gm = target as War.Game.GameManager;
            GetConfig().UpdateMaxIndex(nlevelId,gm.gridWidth * gm.gridHeight);
            GetConfig().AddMap(nlevelId);
            SaveConfig();
            if (bDealEvent)
            {
                UpdateAllDisplay();
            }
            AssetDatabase.Refresh();
        }
        if (GUILayout.Button("更新最大MaxIndex"))
        {
            var gm = target as War.Game.GameManager;
            Debug.Log(gm.gridWidth + "*" + gm.gridHeight);
            GetConfig().UpdateMaxIndex(nlevelId, gm.gridWidth * gm.gridHeight);
        }

        EditorGUILayout.EndHorizontal();

        if (nlevelId!=levelId)
        {
            //if (bDealEvent)
            //{
            //    GetConfig().AddMap(nlevelId);
            //    selectPos = -1;
            //    levelId = nlevelId;
            //    UpdateAllDisplay();
            //}
            //else
            {
                levelId = nlevelId;
            }
        }
        scale = EditorGUILayout.FloatField("Scale", scale);
        //scale = EditorGUILayout.Slider(scale, 0.1f, 2f);
        if (GUILayout.Button("事件Scale 0.7"))
        {
            scale = 0.7f;
        }
        MapGridOffSet();
        //_scroll = EditorGUILayout.BeginScrollView(_scroll);
        //GUILayout.EndScrollView();                   //结束一个滚动视野

        GUILayout.BeginVertical("HelpBox");
        Toggles();
        AddSelects();

        CheckCleanAll();

        CheckBuild();

        ExportMap();

        ExportEvents();

        CheckEvent();

        CheckSprites();
        GUILayout.EndVertical();
    }

    private void Toggles()
    {
        GUILayout.BeginHorizontal(EditorStyles.toolbar);
        bDealEvent = !GUILayout.Toggle(!bDealEvent, "地块", EditorStyles.toolbarButton);
        bDealEvent = GUILayout.Toggle(bDealEvent, "事件", EditorStyles.toolbarButton);
        GUILayout.EndHorizontal();
    }

    /// <summary>
    /// 格子规格控制
    /// </summary>
    private bool showOffset;
    private void MapGridOffSet()
    {
        showOffset = EditorGUILayout.Toggle("展开地块偏移参数", showOffset,EditorStyles.toggle);
        if (showOffset)
        {
            GUILayout.BeginVertical("HelpBox");

            gridOffset = EditorGUILayout.Vector2Field("格子偏移(x:长，y:宽)", gridOffset);
            gridScale = EditorGUILayout.FloatField("格子缩放", gridScale);
            if (GUILayout.Button("执行"))
            {
                //gridOffset = new Vector2(0.04f, 0.05f);
                var gm = target as War.Game.GameManager;
                var w = gm.gridWidth;
                var h = gm.gridHeight;
                var go = gm.gameObject;
                var nparent = go.transform.Find("tileGrid");
                for (int i = 0; i < w; i++)
                {
                    for (int j = 0; j < h; j++)
                    {
                        var chname = GetPosName(i, j);
                        var ch = nparent.Find(chname);
                        if (ch)
                        {
                            ch.localScale = Vector3.one * gridScale;
                            ch.localPosition = new Vector3(i + j * gridOffset.x, 0, j + i * gridOffset.y);
                        }
                    }
                }

                var fparent = go.transform.Find("tileGridFog");
                for (int i = 0; i < w; i++)
                {
                    for (int j = 0; j < h; j++)
                    {
                        var chname = GetPosName(i, j);
                        var ch = fparent.Find(chname);
                        if (ch)
                        {
                            ch.localScale = Vector3.one * gridScale;
                            ch.localPosition = new Vector3(i + j * gridOffset.x, 0, j + i * gridOffset.y);
                        }
                    }
                }
            }
            GUILayout.EndVertical();
        }
    }

    private void CheckSprites()
    {
        //if (!bDealEvent) return;
         

        bToggleEvent = EditorGUILayout.Toggle("操作笔刷", bToggleEvent); 
        
        //if (!bToggleEvent) {
        //    GUILayout.EndHorizontal();                     //结束一个垂直布局
        //    return;
        //}

        if (GUILayout.Button("同步表事件"))
        {
            Sirenix.OdinInspector.Demos.TotEventConfig.Instance.fillSprites();
        } 
        if (bDealEvent)
        {
            GUILayout.BeginHorizontal("Box");
            int approxCount = Mathf.RoundToInt(EditorGUIUtility.currentViewWidth - 20f) / spriteWidth;

            selectIndex = SelectEventGrid(selectIndex, GetConfig().sprites, approxCount);

            GUILayout.EndHorizontal();                     //结束一个垂直布局
        }
        else
        {
            if (GUILayout.Button("切换可见"))
            {

                if (selectIndex >= 0 && textures[selectIndex] != null)
                {
                    if (GetConfig().visibleSpriteList.Contains(textures[selectIndex]))
                    {
                        GetConfig().visibleSpriteList.Remove(textures[selectIndex]);
                    }
                    else
                    {
                        GetConfig().visibleSpriteList.Add(textures[selectIndex]);
                    }
                    Repaint();
                }
            }
            GUILayout.BeginHorizontal("Box");

            int approxCount = Mathf.RoundToInt(EditorGUIUtility.currentViewWidth - 20f) / spriteWidth;
            selectIndex = SelectionGrid(selectIndex, textures, approxCount);
            if (selectIndex >= 0 && textures[selectIndex] != null)
            {
                if (selectedTexture && selectedTexture.Equals(textures[selectIndex]))
                {
                    selectedTexture = null;
                }
                else
                {
                    selectedTexture = textures[selectIndex];
                }
            }
            GUILayout.EndHorizontal();

            if (delSprite == true)
                DeleteSprite();
        }
    }

    private void CheckEvent()
    {
        if (levelId == 0) return;
        //if (!bDealEvent) return;
        //GetConfig().AddMap(levelId);

        //scale = 0.5f;

        var cfg = GetConfig();


        GUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("事件index");
        var nselectPos = EditorGUILayout.IntField(selectPos);
        GUILayout.EndHorizontal();
        if (nselectPos != selectPos)
        {
            selectPos = nselectPos;
            var te =  GetConfig().GetTotEvent(selectPos, bDealEvent)  ;
            if (te!=null)
            {
                GetConfig().TEvent = te.clone();
            }
        }


        var tE = cfg.TEvent;

        List<TotEventConfig.DisplayAtt> disAtts = bDealEvent ? GetConfig().displayAtts : GetConfig().mapsAtts;
         

        var defatt = disAtts[0].att;
        OnGUIAtt(cfg, defatt);
        for (int i = 1; i < disAtts.Count; i++)
        {
            var defatt2 = disAtts[i];
            if(defatt2.eType == tE["type"])
            {
                OnGUIAtt(cfg, defatt2.att);
            }
        }
    }

    private void OnGUIAtt(Sirenix.OdinInspector.Demos.TotEventConfig cfg, List<Sirenix.OdinInspector.Demos.TotEventConfig.DisplayKey> defatt)
    {
        var tE = cfg.TEvent;
        tE = cfg.GetTotEvent(tE.index,bDealEvent)??tE;
        var bdirty = false;
        var updateEvent = bDealEvent ?(System.Action<int, string, string>) GetConfig().UpdateEvent : GetConfig().UpdateIndState;
        for (int i = 0; i < defatt.Count; i++)
        {
            var att = defatt[i];

            if (!att.show) continue;

            GUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(att.des);

            if(att._vtype == "bool")
            {
                var id = EditorGUILayout.Toggle(tE[att.keyName]=="true", EditorStyles.radioButton);
                var bId = id ? "true" : "false";
                if (bId != tE[att.keyName])
                {
                    bdirty = true;
                    updateEvent(selectPos, att.keyName, bId);
                    OnAttChange(att.keyName);
                }
            }
            else
            {
                var id = EditorGUILayout.TextField(tE[att.keyName]);
                if (id != tE[att.keyName])
                {
                    bdirty = true;
                    updateEvent(selectPos, att.keyName, id);
                    OnAttChange(att.keyName);
                }
            }
            GUILayout.EndHorizontal();
        }
        if(bdirty)
        {
            UpdateDisplay(selectPos);
        }
    }

    void OnAttChange(string keyname)
    {
        if(keyname==EVENT_KEY_NAME)
        {
            if (selectPos > 0)
            {
                UpdateDisplay(selectPos);
            }
        }
    }

    private void UpdateAllDisplay()
    {
        var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var w = gm.gridWidth;
        var h = gm.gridHeight;
        var len = w * h;
        for (int i = 0; i < len; i++)
        {
            UpdateDisplay(i);
        }
    }

    private void RemoveAllEvent()
    {
        var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var w = gm.gridWidth;
        var h = gm.gridHeight;
        var len = w * h;
        for (int i = 0; i < len; i++)
        {
            GetConfig().RemoveEvent(i, GetSaveList());
        }
    }


    private void UpdateDisplay(int pos)
    {
        if (!bDealEvent) return;
        var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var chname = GetPosName(pos);
        var nparent = go.transform.Find("tileGrid");
        var ch = nparent.Find(chname);
        if (ch)
        {
            string sid = "";
            var id = 0;

            Sprite sprite = null;
            var te = GetConfig().GetTotEvent(pos, bDealEvent);
            if(te!=null)
            {
                sid = te[EVENT_KEY_NAME];
                int.TryParse(sid, out id);

                var spritev = GetConfig().sprites.Find(s => s.id == id);
                if(spritev!=null)
                {
                    sprite = spritev.sprite;
                }
            }
            var b = id > 0;

            var sp = ch.GetComponent<SpriteRenderer>();
            sp.sprite = sprite;

            ToggleValidSprite(sp, b);

            var text = GetChildUIScript<TextMesh>("Text", sp.gameObject);
            if(!text && b)
            {
                var tPrefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Maps/ToT/tot_tool/Text.prefab");
                var tgo = GameObject.Instantiate(tPrefab, sp.transform, false);
                tgo.name = "Text";
                text = tgo.GetComponent<TextMesh>();
            }
            if (text)
            {
                text.text =b ? sid:"";
            }
        }
    }

    public static T GetChildUIScript<T>(string name, GameObject uiObj,bool forceadd = false) where T:Component
    {
        if (uiObj != null)
        {
            Transform tra = string.IsNullOrEmpty(name) ? uiObj.transform : uiObj.transform.Find(name);
            if (tra != null)
            {
                var c = tra.gameObject.GetComponent<T>();
                if(!c && forceadd)
                {
                    c = tra.gameObject.AddComponent<T>();
                }
                return c;
            }
        }

        return default(T);
    }


    private void ExportMap()
    {

        if (GUILayout.Button("导出通行区域"))
        {
            string path = string.Format(TotEventConfig.Instance.MAP_BYTES_PATH, levelId);
            if (File.Exists(path))
            {
                if (!EditorUtility.DisplayDialog("Warning", path + " 已经存在是否覆盖?", "覆盖"))
                {
                    return;
                }
            }
            ExportMapBytes(path);


        }

        if (GUILayout.Button("打开服务器上传目录"))
        {
            var folder = Path.GetDirectoryName(TotEventConfig.Instance.MAP_BYTES_PATH);
            EditorHelp.ExplorePath(folder);
        }

        if (GUILayout.Button("打开PeakOfTimeRefresh表目录"))
        {
            //var folder = Path.GetDirectoryName(TotEventConfig.Instance.MAP_REFRESH_PATH);
            EditorHelp.ExplorePath(TotEventConfig.Instance.MAP_REFRESH_PATH, true);
        }
        if (GUILayout.Button("生成PeakOfTimeRefresh表"))
        {
            TotEventConfig.Instance.BuildCsv();
        }
        if (GUILayout.Button("SaveProject"))
        {
            SaveConfig();
        }

    }

    private  void SaveConfig()
    {
        EditorUtility.SetDirty(GetConfig());
        var map = GetConfig().GetMap(levelId);
        if(map!=null)
        {
            EditorUtility.SetDirty(map.root);
        }
        AssetDatabase.SaveAssets();
    }

    [MenuItem("Assets/Tot/ExportMapBytes")]
    public static void ReExportAllMapBytes()
    {
        var sels = Selection.gameObjects;
        foreach (var sel in sels)
        {
            if (sel.name.Contains("Clone")) continue;
            var gm = sel.GetComponent<War.Game.GameManager>();
            var sri = sel.GetComponent<ScrollRectItem>();
            var mapname = sri.monos[0].name;
            if(string.IsNullOrEmpty(mapname))
            {
                continue;
            }
            var levelidStr = mapname.Replace("level","");
            var levelid = 0;
            if(int.TryParse(levelidStr, out levelid))
            {
                string path = string.Format(TotEventConfig.Instance.MAP_BYTES_PATH, levelid);
                ExportMapBytes(gm, path, levelid);
            }
        }
        AssetDatabase.Refresh();
    }

    private void ExportMapBytes(string path)
    {
        var gm = target as War.Game.GameManager;
        ExportMapBytes(gm, path, levelId);
        AssetDatabase.Refresh();
        BuildEventBytes(levelId, gm.gameObject);
        AssetDatabase.Refresh();

    }

    private static void ExportMapBytes(War.Game.GameManager gm, string path, int levelId)
    {
        //var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var w = gm.gridWidth;
        var h = gm.gridHeight;
        float bsLen = 12 + w * h ;
        int len = Mathf.CeilToInt(bsLen) * 8;
        byte[] bs = new byte[len];
        List<byte> bsClient = new List<byte>();
        var nparent = go.transform.Find("tileGrid");

        int ind = 0;
        var sb = new System.Text.StringBuilder();
        int bInd = 0;
        Push((uint)w, sizeof(uint), ref bInd, bs);
        Push((uint)h, sizeof(uint), ref bInd, bs);
        Push((uint)levelId, sizeof(uint), ref bInd, bs);
        ind = bInd ;
        GetConfig().AddMap(levelId);
        var vsList = GetConfig().visibleSpriteList;

        var mapAtts = GetConfig().mapsAtts;

        var atts = mapAtts[0];

        var csvName = "state";
        var list = new List<TotEventConfig.DisplayKey>() {
            atts.att.Find((di)=>di._arrtype=="bit8"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit7"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit6"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit5"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit4"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit3"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit2"&&di.csvName==csvName),
            atts.att.Find((di)=>di._arrtype=="bit1"&&di.csvName==csvName),
        };

        for (int j = 0; j < h; j++)
        {
            for (int i = 0; i < w; i++, ind++)
            {
                var chname = i + "_" + j;
                var ch = nparent.Find(chname);
                if (ch)
                {
                    var sp = ch.GetComponent<SpriteRenderer>();
                    if (!sp)
                    {
                        Debug.LogErrorFormat("{0} has no SpriteRenderer !!!", chname);
                        continue;
                    }
                    var b = sp.color.a > 0.8f ? 1 : 0;
                    if(b>0)
                    {
                        if (!vsList.Contains(sp.sprite))
                        {
                            b = 2;
                        }
                    }
                    //if (ind % 8 == 0)
                    //{
                    //    bs[ind / 8] = (byte)0;
                    //}
                    //bs[ind / 8] <<= 1;
                    //bs[ind / 8] |= (byte)(b==1?1:0);

                    var sind = GetPosStatic(ch.name,w,h);

                    var ist = GetConfig().GetTotIndState(sind);
                    byte state = 0;
                    state = 0;
                    if (ist != null)
                    for (int di = 0; di < 8; di++)
                    {
                        if (list[di] == null) continue;
                        var v = ist[list[di].keyName];
                        state = (byte)(state | (v=="true" ? 1 : 0) << di);
                    }
                    if(b==1)
                    {
                        state |= 128;
                    }
                    // 先保存服务器用
                    bs[ind] = state;

                    // 客户端用
                    if(b==2)
                    {
                        // 从左往右,第一位表示是否显示,第二位表示是否障碍
                        state |= 128;
                        state |= 64;
                    }
                    sb.Append(ByteToBitStr(state));
                    sb.Append("\t");
                    bsClient.Add(state);
                }
                else
                {
                    Debug.LogErrorFormat("{0} has no child !!!", chname);

                }
            }
            sb.Append("\n");
        }

        Debug.Log(sb.ToString());

        var sb1 = new System.Text.StringBuilder();

        for (int i = 0; i < len; i++)
        {
            sb1.Append((bs[i / 8] & (1 << (7 - i % 8))) > 0 ? 1 : 0);
        }
        Debug.Log(sb1.ToString());
        sb1= sb1.Remove(0, 8 * 12);
        File.WriteAllBytes(path, bs);


        string cpath = string.Format(TotEventConfig.Instance.MAP_BYTES_CLI_PATH, levelId);
        File.WriteAllBytes(cpath, bsClient.ToArray());

        Debug.Log("ExportMap:" + levelId);
        Debug.Log("bbb:" + ByteToBitStr(254));

    }

    public static string ByteToBitStr(byte b)
    {
        var sb = new System.Text.StringBuilder();
        for (int i = 7; i >= 0; i--)
        {
            sb.Append(b % (1 << (i + 1)) / (1 << i));
        }

        return sb.ToString();
    }

    private void ExportEvents()
    {
        if (!bDealEvent) return;
        if (GUILayout.Button("导出客户端事件"))
        {
            EditorUtility.SetDirty(GetConfig());
            AssetDatabase.SaveAssets();
            string path = string.Format(TotEventConfig.Instance.MAP_BYTES_CLI_PATH, levelId);
            path = path.Replace(".bytes", "_event.bytes");
            if (File.Exists(path))
            {
                if (!EditorUtility.DisplayDialog("Warning", path + " 已经存在是否覆盖?", "覆盖"))
                {
                    return;
                }
            }
            var map = GetConfig().GetMap(levelId);
            List<object> list = new List<object>();
            //var list = new Dictionary<int, object>();
            var ID_PRESERVE_NUM = GetConfig().ID_PRESERVE_NUM;

            Debug.Log("levelId:" + levelId);
            var atts = GetConfig().displayAtts;
            var keyList = new Dictionary<string, TotEventConfig.DisplayKey>();
            foreach (var l in atts)
            {
                foreach (var ll in l.att)
                {
                    keyList[ll.keyName] = ll;
                }
            }
            if(map==null)
            {
                Debug.LogError("地图id 没有配置数据:" + levelId);
                return;
            }
            foreach (var ev in map.events)
            {
                var dic = new Dictionary<string, object>();
                foreach (var att in ev.props)
                {
                    if (string.IsNullOrEmpty(att.value)) continue;
                    if(keyList.ContainsKey(att.key))
                    {
                        var ll = keyList[att.key];
                        //switch(ll._vtype)
                        switch (ll._vtype)
                        {
                            case "int32":
                                int v = 0;
                                int.TryParse(att.value, out v);
                                dic[att.key] = v;

                                break;
                            default:
                                dic[att.key] = att.value;
                                break;
                        }
                    }
                }
                dic["ID"] = levelId * ID_PRESERVE_NUM + ev.index;
                list.Add(dic);
                //list[ev.index] = dic;
            }
            var dict = new Dictionary<string, object>();
            dict["mapid"] = levelId;
            dict["events"] = list;
            var json = UIHelper.ToJson(dict);
            File.WriteAllText(path, json);
            AssetDatabase.Refresh();
            //var mapbytesasset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);

        }

    }

    private void BuildEventBytes(int levelId,GameObject go = null)
    {
        if(go == null)
        {
            var gm = target as War.Game.GameManager;
            go = gm.gameObject;
        }
        string path = string.Format(TotEventConfig.Instance.MAP_BYTES_CLI_PATH, levelId);
        var sri = GetChildUIScript<UI.UGUIExtend.ScrollRectItem>("", go, true);
        var mapbytesasset = AssetDatabase.LoadAssetAtPath<TextAsset>(path);
        AddMonos(sri, 0, mapbytesasset);
        var npath = path.Replace(".bytes", "_event.bytes");
        mapbytesasset = AssetDatabase.LoadAssetAtPath<TextAsset>(npath);
        AddMonos(sri, 1, mapbytesasset);
    }

    public void AddMonos(ScrollRectItem refer, int i, UnityEngine.Object obj)
    {
        List<UnityEngine.Object> monos = null;

        if (refer.monos != null)
            monos = new List<UnityEngine.Object>(refer.monos);
        else
            monos = new List<UnityEngine.Object>();


        //monos.Add(obj);
        if (i < 0)
        {
            monos.Add(obj);
        }
        else
        {
            while (monos.Count <= i)
                monos.Add(null);
            monos[i] = obj;
        }
        refer.monos = monos.ToArray();
    }
    public UnityEngine.Object GetMonos(ScrollRectItem refer, int index)
    {
        if (index >= 0 && index < refer.monos.Length)
            return refer.monos[index];
        return null;
    }
    void ToggleValidSprite(SpriteRenderer sp ,bool b)
    {
        var c = sp.color;
        c.a = b ?1f:.1f;
        sp.color = c; 
    }
    private void AddSelects()
    {
        if (bDealEvent) return;

        delSprite = EditorGUILayout.Toggle("删除精灵", delSprite);
        //canBrush = EditorGUILayout.Toggle("Brush", canBrush);
        flipX = EditorGUILayout.Toggle("FlipX", flipX);
        flipY = EditorGUILayout.Toggle("FlipY", flipY);

        if (GUILayout.Button("添加选择的Sprites"))
        {
            foreach (var item in Selection.objects)
            {

                if (item is Sprite)
                {
                    m_sprite = item as Sprite;
                    AddNewSprite();
                }
                if (item is Texture2D)
                {
                    var sps = AssetDatabase.LoadAllAssetsAtPath(AssetDatabase.GetAssetPath(item));
                    foreach (var ii in sps)
                    {
                        if (ii is Sprite)
                        {
                            m_sprite = ii as Sprite;
                            AddNewSprite();
                        }
                    }
                }
            }
        }
    }
    private void CheckBuild()
    {
        if (GUILayout.Button("创建新的布局"))
        {
            var gm = target as War.Game.GameManager;
            var go = gm.gameObject;
            var newgo = GameObject.Instantiate(go) as GameObject;
            newgo.transform.SetParent(go.transform.parent, true);
            var newgm = newgo.GetComponent<War.Game.GameManager>();
            var w = newgm.gridWidth;
            var h = newgm.gridHeight;
            var nparent = newgo.transform.Find("tileGrid");
            GameObject proto = null;
            for (int i = 0; i < w; i++)
            {
                for (int j = 0; j < h; j++)
                {
                    var chname = GetPosName(i,j);
                    var ch = nparent.Find(chname);
                    if (ch)
                    {
                        proto = ch.gameObject;
                    }
                    if(proto)
                    {
                        var n = ch ?? (GameObject.Instantiate(proto,proto.transform.parent,false) as GameObject).transform;
                        n.localPosition = new Vector3(i, 0, j);
                        n.name = chname;
                    }

                }
            }
        } 
    }

    private void CheckCleanAll()
    {
        if (!bDealEvent) return;
        if (GUILayout.Button("重置所有地块为空"))
        {
            RemoveAllEvent();
            UpdateAllDisplay();
        }
    }

    /// <summary>
    /// 选中图片之后重新设置贴图
    /// </summary>
    void AddNewSprite()
    {
        if (m_sprite == null)
            return;
        if (textures.Count == 0)
        {
            textures.Add(m_sprite);
            GetConfig().visibleSpriteList.Add(m_sprite);
            return;
        }
        for (int i = 0; i < textures.Count; i++)
        {
            if (textures[i].Equals(m_sprite) == true)
            {
                return;
            }
        }
        textures.Add(m_sprite);
        GetConfig().visibleSpriteList.Add(m_sprite);
    }

    private void DeleteSprite()
    {
        if (selectedTexture == null)
            return;
        textures.Remove(selectedTexture);
        selectIndex = -1;
    }

    public int SelectEventGrid(int selected, List<Sirenix.OdinInspector.Demos.TotEventConfig.SpriteVO> content, int xCount)
    {

        int yCount = content.Count / xCount;
        if (content.Count % xCount != 0)
            yCount++;

        float horizenSpace = spriteMargin * (xCount - 1);
        float verticalSpace = spriteMargin * (yCount - 1);

        Rect rect = GUILayoutUtility.GetRect(spriteWidth * xCount + horizenSpace, spriteHeight * yCount + verticalSpace);
        Rect[] rectArray = CalcMouseRects(rect, content.Count, xCount, spriteWidth, spriteHeight, spriteMargin);

        switch (Event.current.type)
        {
            case EventType.Repaint:
                for (int i = 0; i < content.Count; ++i)
                {
                    if (content[i] == null) continue;
                    Sprite sp = content[i].sprite;
                    if (sp==null) continue;

                    Texture texture = sp.texture;
                    Rect uvRect = content[i].sprite.textureRect;
                    uvRect.x /= texture.width;
                    uvRect.width /= texture.width;
                    uvRect.y /= texture.height;
                    uvRect.height /= texture.height;

                    Color c = GUI.color;
                    if (i == selected)
                        GUI.color = selectedColor;

                    GUI.DrawTextureWithTexCoords(rectArray[i], texture, uvRect);
                    GUI.Label(rectArray[i], content[i].id + "");
                    GUI.color = Color.white;
                }
                break;
            case EventType.MouseUp:
                for (int i = 0; i < content.Count; ++i)
                {
                    if (rectArray[i].Contains(Event.current.mousePosition))
                    {
                        if (selected != i)
                        {
                            selected = i;
                            if(bDealEvent)
                            {
                                GetConfig().TEvent[EVENT_KEY_NAME] = content[i].id + "";

                                GetConfig().UpdateEvent(selectPos,EVENT_KEY_NAME, content[i].id + "");
                                UpdateDisplay(selectPos);
                            } 
                            selectedTexture = content[i].sprite;
                        }
                        else
                        {
                            selectPos = -1;
                            selected = -1;
                            selectedTexture = null;
                        }
                        Repaint();
                    }
                }
                break;
        }


        return selected;
    }

    public int SelectionGrid(int selected, List<Sprite> content, int xCount)
    {
        int yCount = content.Count / xCount;
        if (content.Count % xCount != 0)
            yCount++;

        float horizenSpace = spriteMargin * (xCount - 1);
        float verticalSpace = spriteMargin * (yCount - 1);

        Rect rect = GUILayoutUtility.GetRect(spriteWidth * xCount + horizenSpace, spriteHeight * yCount + verticalSpace);
        Rect[] rectArray = CalcMouseRects(rect, content.Count, xCount, spriteWidth, spriteHeight, spriteMargin);

        switch (Event.current.type)
        {
            case EventType.Repaint:
                for (int i = 0; i < content.Count; ++i)
                {
                    Sprite sp = content[i];
                    Texture texture = sp.texture;
                    Rect uvRect = content[i].textureRect;
                    uvRect.x /= texture.width;
                    uvRect.width /= texture.width;
                    uvRect.y /= texture.height;
                    uvRect.height /= texture.height;

                    Color c = GUI.color;
                    if (i == selected)
                        GUI.color = selectedColor;

                    GUI.DrawTextureWithTexCoords(rectArray[i], texture,  uvRect);
                    GUI.Label(rectArray[i], i+ (GetConfig().visibleSpriteList.Contains(sp) ? "可见":""));
                    GUI.color = Color.white;
                }
                break;
            case EventType.MouseUp:
                for (int i = 0; i < content.Count; ++i)
                {
                    if (rectArray[i].Contains(Event.current.mousePosition))
                    {
                        if(selected!=i)
                        {
                            selected = i;
                            selectedTexture = content[i];
                        }
                        else
                        {
                            selected = -1;
                            selectedTexture = null;
                        }
                        Repaint();
                    }
                }
                break;
        }

        return selected;
    }

    private Rect[] CalcMouseRects(Rect position, int contentLength, int xCount, float elemWidth, float elemHeight, float margin)
    {
        int length = contentLength;
        float xMin = position.xMin;
        float yMin = position.yMin;
        Rect[] rectArray = new Rect[length];

        int y = 0;
        for (int i = 0; i < length; i++)
        {
            rectArray[i] = new Rect(xMin, yMin, elemWidth, elemHeight);

            xMin += elemWidth + margin;
            y++;
            if (y >= xCount)
            {
                y = 0;
                yMin += elemHeight + margin;
                xMin = position.xMin;
            }
        }
        return rectArray;
    }

    #region  cache interface
    //private int GetValueInt(string key, int def = -1)
    //{
    //    int t = def;
    //    var str = "";

    //    if (keyBind.TryGetValue(key, out str))
    //    {
    //        int.TryParse(str, out t);
    //    }


    //    return t;
    //}
    //private string GetValueString(string key, string def = null)
    //{
    //    var t = def;
    //    var str = "";

    //    keyBind.TryGetValue(key, out str);
    //    return t;
    //}
    #endregion

    #region util

    private static byte[] Push(uint v, int count, ref int destIndex, byte[] dest)
    {
        for (int i = 0; i < count; i++)
        {
            dest[i + destIndex] = (byte)(v >> (i * 8) & 0xFF);
        }
        destIndex += count;
        return dest;

    }
    #endregion

    #region data
    private static Sirenix.OdinInspector.Demos.TotEventConfig GetConfig()
    {
        return Sirenix.OdinInspector.Demos.TotEventConfig.Instance;
    }
    #endregion

   
    private void OnSceneCall(SceneView view)
    {
        OnSceneGUI();
    }
    private void OnSceneGUI(/*SceneView view*/)
    {
        Event current = Event.current;
        if (current.button != 0) return; 
        var cTarget = (target as Component);
        if (!cTarget) return;

        if (Selection.activeGameObject != cTarget.gameObject) return;
        int controlID = GUIUtility.GetControlID("MapBrush".GetHashCode(), FocusType.Passive);
        switch (current.GetTypeForControl(controlID))
        {
            case EventType.MouseDown:

                //Debug.LogError("MouseDown");
                Ray ray = HandleUtility.GUIPointToWorldRay(Event.current.mousePosition);
                var hits = Physics2D.RaycastAll(ray.origin, Vector2.zero);
                foreach (var hit in hits)
                {
                    if (hit.transform != null)
                    {
                        if (hit.transform.GetComponentInParent(target.GetType()) != cTarget) continue;
                        //Debug.Log(current.button);
                        SpriteRenderer sp = hit.transform.GetComponent<SpriteRenderer>();
                        var hitName = hit.transform.name;
                        selectPos = GetPos(hitName);
                        if (bDealEvent)
                        {

                            if (!bToggleEvent)
                            {
                                GetConfig().SyncEvent(selectPos, GetSaveList());
                                Repaint();
                                return;
                            }

                            var sps = GetConfig().sprites;
                            if (sp && selectIndex >= 0 && selectIndex < sps.Count)
                            {
                                sp.sprite = sps[selectIndex].sprite;
                                sp.flipX = flipX;
                                sp.flipY = flipY;

                                ToggleValidSprite(sp, true);

                                sp.transform.localScale = new Vector3(scale, scale);

                                if (bDealEvent)
                                {
                                    GetConfig().UpdateEvent(selectPos, EVENT_KEY_NAME, GetConfig().TEvent[EVENT_KEY_NAME]);
                                }
                                else
                                {
                                    GetConfig().UpdateIndState(selectPos, EVENT_KEY_NAME, GetConfig().TEvent[EVENT_KEY_NAME]);
                                }

                                UpdateDisplay(selectPos);
                                Debug.LogFormat("{0},{1},{2}", hitName, selectPos, GetPosName(selectPos));

                            }
                            if (sp && (selectIndex == -1 || current.shift))
                            {

                                ToggleValidSprite(sp, false);

                                //var hitName = hit.transform.name;
                                //selectPos = GetPos(hitName);
                                GetConfig().RemoveEvent(selectPos, GetSaveList());
                                UpdateDisplay(selectPos);
                            }
                            Repaint();
                        }
                        else
                        {
                            if (sp && selectIndex >= 0 && selectIndex < textures.Count)
                            {

                                if (levelId != 0)
                                {
                                    GetConfig().SyncEvent(selectPos, GetSaveList());
                                    Repaint();
                                }
                                if (!bToggleEvent)
                                {
                                    return;
                                }
                                //Undo.RegisterCompleteObjectUndo(sp.sprite, "MapBrushSprite" + sp.GetHashCode() + EditorApplication.timeSinceStartup);
                                sp.sprite = textures[selectIndex];
                                sp.flipX = flipX;
                                sp.flipY = flipY;
                                ToggleValidSprite(sp, true);

                                sp.transform.localScale = new Vector3(scale, scale);
                            }

                            if (!bToggleEvent)
                            {
                                return;
                            }
                            if (sp && (selectIndex == -1 || current.shift))
                            {
                                ToggleValidSprite(sp, false);
                            }

                        }
                    }
                }
                if (previewObject != null)
                    DestroyImmediate(previewObject);
                break;

            case EventType.MouseDrag:

            case EventType.MouseUp:
                //Debug.LogError("MouseUp");
                break;


            case EventType.MouseMove:
                //Debug.LogError("MouseMove");
                if (bToggleEvent)
                {
                    Ray ray_2 = HandleUtility.GUIPointToWorldRay(Event.current.mousePosition);
                    var hits_2 = Physics2D.RaycastAll(ray_2.origin, Vector2.zero);
                    foreach (var hit in hits_2)
                    {
                        if (hit.transform != null)
                        {
                            if (hit.transform.GetComponentInParent(target.GetType()) != cTarget) continue;
                            //Debug.Log("hit.name>>>>>>" + hit.transform.name + "/" + selectIndex + "," + textures.Count);
                            if (bDealEvent)
                            {
                                var sps = GetConfig().sprites;
                                if (selectIndex >= 0 && selectIndex < sps.Count)
                                {
                                    SetPreviewObject(hit.transform.position,sps[selectIndex].sprite);
                                }
                            }
                            else
                            {
                                if (selectIndex >= 0 && selectIndex < textures.Count)
                                {
                                    SetPreviewObject(hit.transform.position, textures[selectIndex]);
                                }
                            }
                        }
                    }
                    if (hits_2.Length <= 0 && previewObject != null)
                    {
                        DestroyImmediate(previewObject);
                    }
                }
                else
                {
                    if (previewObject != null)
                        DestroyImmediate(previewObject);
                }
                break;

            case EventType.Layout:
                if (this.IsModificationToolActive())
                {
                    HandleUtility.AddDefaultControl(controlID);
                    break;
                }
                break;
            case EventType.MouseLeaveWindow:
                if (previewObject != null)
                    DestroyImmediate(previewObject);
                break;
        }
    }

    //生成预览
    private void SetPreviewObject(Vector3 pos,Sprite sprite)
    {
        SpriteRenderer psp = null;
        if (previewObject == null)
        {
            previewObject = new GameObject("preview");
            psp = previewObject.AddComponent<SpriteRenderer>();
            psp.sortingOrder = 100;
        }
        else
        {
            psp = previewObject.GetComponent<SpriteRenderer>();
        }
        psp.sprite = sprite;
        psp.transform.position = pos;
        ToggleValidSprite(psp, true);
        psp.transform.localScale = new Vector3(scale, scale);

    }

    private List<TotEventConfig.TotEvent> GetSaveList()
    {
        return bDealEvent ? GetConfig().map.events : GetConfig().map.indexStates;
    }

    private static int GetPosStatic(string hitName,int w,int h)
    {
        var ij = hitName.Split('_');
        var j = -1;
        var i = -1;
        int.TryParse(ij[0], out i);
        int.TryParse(ij[1], out j); 

        var pos = j * w + i;

        return pos;
    }

    private int GetPos(string hitName)
    {
        var ij = hitName.Split('_');
        var j = -1;
        var i = -1;
        int.TryParse(ij[0], out i);
        int.TryParse(ij[1], out j);
        var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var w = gm.gridWidth;
        var h = gm.gridHeight;

        var pos = j * w + i;

        return pos;
    }

    private string GetPosName(int x,int y)
    {
        return x + "_" + y;
    }

    private string GetPosName(int pos)
    {
        var gm = target as War.Game.GameManager;
        var go = gm.gameObject;
        var w = gm.gridWidth;
        var h = gm.gridHeight;
        var i = pos % w; 
        var j = pos / w;

        return GetPosName(i,j);
    }

    private bool IsModificationToolActive()
    {
        return true;
    }

    private void OnEnable()
    {
        if(Application.isPlaying==false)
        {
            SceneView.onSceneGUIDelegate -= this.OnSceneCall;
            SceneView.onSceneGUIDelegate += this.OnSceneCall;
        }

        War.Game.GameManager manager = target as War.Game.GameManager;

        Dictionary<Sprite, Sprite> set = new Dictionary<Sprite, Sprite>();

        if (bDealEvent)
        {
            
        }
        else
        {
            Transform gridParent = manager.transform.Find("tileGrid");
            if (!gridParent) return;
            SpriteRenderer[] spList = gridParent.GetComponentsInChildren<SpriteRenderer>();
            foreach (SpriteRenderer sp in spList)
            {
                if (sp.sprite != null && (set.ContainsKey(sp.sprite) == false))
                {
                    set.Add(sp.sprite, sp.sprite);
                }
            }

            textures = new List<Sprite>(set.Keys);
        }

    }

    private void OnDisable()
    {
        SceneView.onSceneGUIDelegate -= this.OnSceneCall;
    }
}
