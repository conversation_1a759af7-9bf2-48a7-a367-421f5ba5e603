using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using War.Battle;

[CustomEditor(typeof(War.Battle.BattleJumpoutManager))]
public class BattleJumpoutEditor : Editor
{
    BoxBoundHandleEditor upperEditor = new BoxBoundHandleEditor();
    BoxBoundHandleEditor lowerEditor = new BoxBoundHandleEditor();


    public BattleJumpoutEditor()
    {
        upperEditor.CopyColliderPropertiesToHandle += CopyColliderPropertiesToHandleForUpper;
        upperEditor.CopyHandlePropertiesToCollider += CopyHandlePropertiesToColliderForUpper;

        lowerEditor.CopyColliderPropertiesToHandle += CopyColliderPropertiesToHandleForLower;
        lowerEditor.CopyHandlePropertiesToCollider += CopyHandlePropertiesToColliderForLower;
    }

    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        serializedObject.Update();
        SerializedProperty iterator = serializedObject.GetIterator();
        for (bool flag = true; iterator.NextVisible(flag); flag = false)
        {
            using (new EditorGUI.DisabledScope("m_Script" == iterator.propertyPath))
            {
           
                if (iterator.propertyPath == "upper")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    upperEditor.DrawInspectorGUI("Edit Bound");

                }
                else if (iterator.propertyPath == "lower")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    lowerEditor.DrawInspectorGUI("Edit Bound");

                }
                else
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                }
            }
        }
        serializedObject.ApplyModifiedProperties();
        EditorGUI.EndChangeCheck();
    }

    protected void OnSceneGUI()
    {
        BattleJumpoutManager target = (BattleJumpoutManager)base.target;
        lowerEditor.DrawSceneGUI(target.transform);
        upperEditor.DrawSceneGUI(target.transform);
    }

    private void CopyColliderPropertiesToHandleForUpper(BoxBoundsHandle handle)
    {
        BattleJumpoutManager target = (BattleJumpoutManager)base.target;
        handle.center = BoxBoundHandleEditor.TransformColliderCenterToHandleSpace(target.transform, target.upper.center);
        handle.size = Vector3.Scale(target.upper.size, target.transform.lossyScale);
    }

    private void CopyHandlePropertiesToColliderForUpper(BoxBoundsHandle handle)
    {
        BattleJumpoutManager target = (BattleJumpoutManager)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.upper.center = BoxBoundHandleEditor.TransformHandleCenterToColliderSpace(target.transform, handle.center);
        Vector3 vector = Vector3.Scale(handle.size, BoxBoundHandleEditor.InvertScaleVector(target.transform.lossyScale));
        float x = Mathf.Abs(vector.x);
        float y = Mathf.Abs(vector.y);
        vector = new Vector3(x, y, Mathf.Abs(vector.z));
        target.upper.size = vector;
    }

    private void CopyColliderPropertiesToHandleForLower(BoxBoundsHandle handle)
    {
        BattleJumpoutManager target = (BattleJumpoutManager)base.target;
        handle.center = BoxBoundHandleEditor.TransformColliderCenterToHandleSpace(target.transform, target.lower.center);
        handle.size = Vector3.Scale(target.lower.size, target.transform.lossyScale);
    }

    private void CopyHandlePropertiesToColliderForLower(BoxBoundsHandle handle)
    {
        BattleJumpoutManager target = (BattleJumpoutManager)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.lower.center = BoxBoundHandleEditor.TransformHandleCenterToColliderSpace(target.transform, handle.center);
        Vector3 vector = Vector3.Scale(handle.size, BoxBoundHandleEditor.InvertScaleVector(target.transform.lossyScale));
        float x = Mathf.Abs(vector.x);
        float y = Mathf.Abs(vector.y);
        vector = new Vector3(x, y, Mathf.Abs(vector.z));
        target.lower.size = vector;
    }
}
