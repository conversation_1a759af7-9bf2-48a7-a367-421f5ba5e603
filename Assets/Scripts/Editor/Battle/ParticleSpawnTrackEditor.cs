using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEditor;

[CustomEditor(typeof(ParticleSpawnTrack), true), CanEditMultipleObjects]
internal class ParticleSpawnTrackEditor : Editor
{
    // Fields
    private SerializedProperty bindingStamp;


    public void OnEnable()
    {
        this.bindingStamp = serializedObject.FindProperty("bindingStamp");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(this.bindingStamp);

        serializedObject.ApplyModifiedProperties();
    }
}
