using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;


public class SkillTrackCompileEditor : EditorWindow
{
    [MenuItem("Designer/Skill Compiler")]
    public static void OnMenuItem()
    {
        SkillTrackCompileEditor window = ScriptableObject.CreateInstance<SkillTrackCompileEditor>();
        window.titleContent = new GUIContent("Skill Compiler");
        window.ShowUtility();
    }

    private void OnGUI()
    {
        if (GUILayout.Button("增量编译"))
        {
            Compile(false);
        }

        if (GUILayout.Button("强制编译"))
        {
            Compile(true);
        }
    }

    private void Compile(bool force)
    {
        SkillTrackCompiler compiler = new SkillTrackCompiler();
        compiler.ReadVisualFxConfig(Application.dataPath + "/../../../Tools/csv_script/Particle.csv");
        compiler.CompileConfig(Application.dataPath + "/../../../Tools/csv_script/SkillCompile.csv", force);
    }
}
