//using UnityEngine;
//using UnityEditor;
//using Cinemachine;

//[CustomEditor(typeof(SkillEditHelper))]
//public class SkillEditHelperEditor : Editor
//{
//    Object signalSource;
//    SkillEditHelper t;
//    public override void OnInspectorGUI()
//    {
//        DrawDefaultInspector();

//        GUILayout.Label("初始化");
//        GUILayout.BeginHorizontal();

//        if (!t || t != target)
//        {
//            t = target as SkillEditHelper;
//        }

//        if (GUILayout.Button("Reload", GUILayout.Width(70)))
//        {
//            t.Unload();
//            t.Load();
//        }
//        if (GUILayout.Button("load", GUILayout.Width(70)))
//        {
//            t.Load();
//        }

//        if (GUILayout.Button("unload", GUILayout.Width(70)))
//        {
//            t.Unload();
//        }

//        if (GUILayout.Button("bind", GUILayout.Width(70)))
//        {
//            t.Bind();
//        }
//        GUILayout.EndHorizontal();

//        GUILayout.Label("播放");
//        GUILayout.BeginHorizontal();
//        if (GUILayout.Button("▶", GUILayout.Width(70)))
//        {
//            t.PlayDirector();
//        }
//        GUILayout.EndHorizontal();
//    }
//}


