using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

public class BattleCutsceneEditorWindow : EditorWindow
{
    [<PERSON>u<PERSON><PERSON>("Tool/Cutscene Editor")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        BattleCutsceneEditorWindow window = (BattleCutsceneEditorWindow)EditorWindow.GetWindow(typeof(BattleCutsceneEditorWindow));
        window.Show();
    }

    private void OnGUI()
    {
        if (GUILayout.Button("Play"))
        {
            if (Selection.objects.Length > 0)
            {
                GameObject go = Selection.objects[0] as GameObject;
                if (go)
                {
                    BattleCutscene cutscene = go.GetComponent<BattleCutscene>();
                    if (cutscene)
                        cutscene.Play();
                }
            }
        }
    }
}
