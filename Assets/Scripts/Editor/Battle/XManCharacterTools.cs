using GPUAnimationBaker;
using System;
using System.IO;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

class XManCharacterTools
{
    [MenuItem("Assets/X-Hero工具/XMan模型处理", false, 1)]
    public static void AutoCharecterToolsCollction()
    {
        try
        {
            UnityEngine.Object[] objs = Selection.objects;
            foreach (var obj in objs)
            {
                string path = AssetDatabase.GetAssetPath(obj).Replace("\\", "/");
                SetCharatersObj(path);
                //DeleteSimpleModel(path);
            }
            EditorUtility.ClearProgressBar();
            GC.Collect();
        }
        catch (Exception)
        {
            EditorUtility.ClearProgressBar();
            GC.Collect();
            throw;
        }
    }

    [MenuItem("Assets/X-Hero工具/XMan模型检测", false, 2)]
    public static void AutoCheckCharecterGPUAnim()
    {
        string path = "Assets/Art/Characters";
        string[] files = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
        for (int i = 0; i < files.Length; i++)
        {
            string file = files[i].Replace("\\", "/");
            if(file.Contains("_") || file.Contains("common"))continue;
            string floderPath = file.Substring(0, file.IndexOf("/Prefabs"));
            string[] overrideControllers = Directory.GetFiles(floderPath, "*.overrideController", SearchOption.AllDirectories);
            if (overrideControllers.Length <= 1)
            {
                Debug.LogError("此英雄overrideController数量小于2" + floderPath);
            }
        }
    }

    //[MenuItem("Assets/X-Hero工具/(慎用)XMan全部模型处理", false, 2)]
    public static void AutoAllCharecterGPUAnim()
    {
        try
        {
            string path = "Assets/Art/Characters";
            string[] files = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string file = files[i].Replace("\\", "/");
                if(file.Contains("_") || file.Contains("common"))continue;
                SetCharatersObj(file);
            }
            EditorUtility.ClearProgressBar();
            GC.Collect();
        }
        catch (Exception)
        {
            EditorUtility.ClearProgressBar();
            GC.Collect();
            throw;
        }
    }

    private static void SetCharatersObj(string path)
    {
        string floderPath = path.Substring(0, path.IndexOf("/Prefabs"));
        EditorUtility.DisplayProgressBar("", "初始化模型中.......", 0.1f);
        SkillEffectTools.SetupCharecter();
        bool createSimple = true;
        string[] overrideControllers = Directory.GetFiles(floderPath, "*.overrideController", SearchOption.AllDirectories);
        if (overrideControllers.Length <= 1)
        {
            Debug.LogError("此英雄overrideController数量小于2" + floderPath);
            createSimple = false;
        }
        string sourceModelPath = "";
        if (createSimple)
        {
            EditorUtility.DisplayProgressBar("", "生成_simple模型中.......", 0.2f);
            sourceModelPath = CreateSimpleModel(path);
        }

        EditorUtility.DisplayProgressBar("", "生成新mesh中.......", 0.4f);
        Sirenix.OdinInspector.Demos.OptimizeAnimationClipTool.AutoSetFbxAnimation(floderPath);
        EditorUtility.DisplayProgressBar("", "生成animation中.......", 0.6f);
        Sirenix.OdinInspector.Demos.OptimizeAnimationClipTool.AutoModifyAnimation(floderPath);
        EditorUtility.DisplayProgressBar("", "优化animation中.......", 0.8f);
        Sirenix.OdinInspector.Demos.OptimizeAnimationClipTool.Instance.AutoOptimizeAnimation(floderPath);
        if (createSimple)
        {
            EditorUtility.DisplayProgressBar("", "生成GPU模型中.......", 0.9f);
            GpuAnimationBakerServices.DoGenerateGPUAnimator(AssetDatabase.LoadAssetAtPath<GameObject>(sourceModelPath));
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        EditorUtility.DisplayProgressBar("", "完成", 1f);
    }

    private static void DeleteSimpleModel(string path)
    {
        string simplePath = path.Replace(".prefab", "_Simple.prefab");
        if (File.Exists(simplePath))
            File.Delete(simplePath);
        if (File.Exists(simplePath + ".meta"))
            File.Delete(simplePath + ".meta");
    }
    private static string CreateSimpleModel(string path)
    {
        string simplePath = path.Replace(".prefab", "_Simple.prefab");
        if (File.Exists(simplePath))
        {
            File.Delete(simplePath);
        }
        File.Copy(path, simplePath, true);
        AssetDatabase.Refresh();
        GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(simplePath);
        if (go != null)
        {
            Animator animator = go.GetComponent<Animator>();
            if (animator == null || animator.runtimeAnimatorController == null) return simplePath;

            string animStr = AssetDatabase.GetAssetPath(animator.runtimeAnimatorController);
            if (animStr.Contains("_Override"))
            {
                animStr = animStr.Replace("_Override", "_Simple_Override");
            }
            else
            {
                animStr = animStr.Replace("_new", "_Simple_new");
            }
            go.GetComponent<Animator>().runtimeAnimatorController = AssetDatabase.LoadAssetAtPath<RuntimeAnimatorController>(animStr);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        return simplePath;
    }

    [MenuItem("Assets/X-Hero工具/(慎用)XMan全部Override处理", false, 3)]
    private static void RefershSimpleOverrideController()
    {
        try
        {
            string path = "Assets/Art/Characters";
            string[] files = Directory.GetFiles(path, "*.overrideController", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string file = files[i].Replace("\\", "/");
                CreateOverrideController(file);
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            GC.Collect();
        }
        catch (Exception)
        {
            EditorUtility.ClearProgressBar();
            GC.Collect();
            throw;
        }
    }

    private static void CreateOverrideController(string file)
    {
        string path = "Assets/Art/Characters";
        if (file.Contains("common")) return;
        if (File.Exists(file))
        {
            string aocPath = "Assets/Art/Characters/common/Animations/Characters_Base_Override.overrideController";
            string aocSimplePath = "Assets/Art/Characters/common/Animations/Characters_Base_Simple_Override.overrideController";
            if (file.Contains("_Simple"))
            {
                File.Copy(aocSimplePath, file, true);
            }
            else
            {
                File.Copy(aocPath, file, true);
            }
            AssetDatabase.Refresh();
        }
        string modelName = file.Split('/')[3];
        AnimatorOverrideController aoc = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(file);
        List<KeyValuePair<AnimationClip, AnimationClip>> n = new List<KeyValuePair<AnimationClip, AnimationClip>>();
        aoc.GetOverrides(n);
        for (int j = 0; j < n.Count; j++)
        {
            if (n[j].Value == null)
            {
                string fbxFilePath = path + "/" + modelName + "/Animations/" + modelName + "@" + n[j].Key.name + ".FBX";
                if (!File.Exists(fbxFilePath)) continue;
                UnityEngine.Object[] animationClips = AssetDatabase.LoadAllAssetsAtPath(fbxFilePath);
                for (int k = 0; k < animationClips.Length; k++)
                {
                    var clip = animationClips[k] as AnimationClip;
                    if (clip && n[j].Key.name == clip.name)
                    {
                        n[j] = new KeyValuePair<AnimationClip, AnimationClip>(n[j].Key, clip);
                    }
                }
            }
        }
        aoc.ApplyOverrides(n);
    }
}
