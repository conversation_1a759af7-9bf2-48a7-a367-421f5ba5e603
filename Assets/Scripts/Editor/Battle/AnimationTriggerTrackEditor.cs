using UnityEditor;

[CustomEditor(typeof(AnimationTriggerTrack), true), CanEditMultipleObjects]
internal class AnimationTriggerTrackEditor : Editor
{
    private SerializedProperty segment;

    public void OnEnable()
    {
        segment = serializedObject.FindProperty("segment");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(segment);

        serializedObject.ApplyModifiedProperties();
    }
}
