using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

//[CustomEditor(typeof(BattleCutsceneDialog))]
//public class BattleCutsceneDialogEditor : Editor
//{
//    public override void OnInspectorGUI()
//    {
//        DrawDefaultInspector();
//        if (GUILayout.Button("Play"))
//        {
//            ((BattleCutsceneDialog)target).Play();
//        }
//    }
//}