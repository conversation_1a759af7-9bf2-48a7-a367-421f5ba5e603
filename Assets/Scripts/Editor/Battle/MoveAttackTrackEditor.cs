using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEditor;

[CustomEditor(typeof(ParticleAttackTrack), true), CanEditMultipleObjects]
internal class MoveAttackTrackEditor : Editor
{
    // Fields
    private SerializedProperty segment;


    public void OnEnable()
    {
        this.segment = serializedObject.FindProperty("segment");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(this.segment);

        serializedObject.ApplyModifiedProperties();
    }
}
