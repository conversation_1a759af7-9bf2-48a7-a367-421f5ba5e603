using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using War.Battle;

[CustomEditor(typeof(BattleActorNode))]
class BattleActorEditor : Editor
{
    BoxBoundHandleEditor boxBoundingEditor = new BoxBoundHandleEditor();

    public BattleActorEditor()
    {
        boxBoundingEditor.CopyColliderPropertiesToHandle += CopyColliderPropertiesToHandle;
        boxBoundingEditor.CopyHandlePropertiesToCollider += CopyHandlePropertiesToCollider;
    }

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        BattleActorNode node = target as BattleActorNode;

        GUI.enabled = node.manualBound;
        boxBoundingEditor.DrawInspectorGUI("Edit Manual Bounds");
        GUI.enabled = true;

        // 方便先查找到显示异常的英雄 role id，然后再按 role id 去查战报排查问题
        if (node.role != null)
        {
            GUI.enabled = false;
            // 基于对于服务器下发英雄列表顺序，lua 中解的 palID，casterid 就是这个
            EditorGUILayout.IntField("role id:", (int)node.role.id);
            // 服务器下发指定的英雄站位，但并不是真实的游戏中看到的显示位置，lua 中解析的位置是这个
            EditorGUILayout.IntField("role 服务器站位(下标从0开始):", node.role.position);
            // node map中指定的位置映射，基本与真实显示位置一致，但有细节未处理，不保证完全一致
            // 真实的显示位置就是 BattleActorNode 绑定的 GameObject 节点对应的数字了
            int displayPos = -1;
            if (node.manager != null && node.manager.descriptor.nodeMap.Length > node.role.position)
            {
                displayPos = node.manager.descriptor.nodeMap[node.role.position];
            }
            EditorGUILayout.IntField("role display position:", displayPos);
            EditorGUILayout.TextField("role res:", node.role.res);
            GUI.enabled = true;
        }
    }



    protected void OnSceneGUI()
    {
        BattleActorNode target = (BattleActorNode)base.target;
        boxBoundingEditor.DrawSceneGUI(target.transform);
    }


    private void CopyColliderPropertiesToHandle(BoxBoundsHandle handle)
    {
        BattleActorNode target = (BattleActorNode)base.target;
        handle.center = BoxBoundHandleEditor.TransformColliderCenterToHandleSpace(target.transform, target.bounds.center);
        handle.size = Vector3.Scale(target.bounds.size, target.transform.lossyScale);
    }

    private void CopyHandlePropertiesToCollider(BoxBoundsHandle handle)
    {
        BattleActorNode target = (BattleActorNode)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.bounds.center = BoxBoundHandleEditor.TransformHandleCenterToColliderSpace(target.transform, handle.center);
        Vector3 vector = Vector3.Scale(handle.size, BoxBoundHandleEditor.InvertScaleVector(target.transform.lossyScale));
        float x = Mathf.Abs(vector.x);
        float y = Mathf.Abs(vector.y);
        vector = new Vector3(x, y, Mathf.Abs(vector.z));
        target.bounds.size = vector;
    }
}
