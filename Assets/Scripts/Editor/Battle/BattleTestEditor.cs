using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

[CustomEditor(typeof(BattlePlayerTest))]
class BattleTestEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        if (GUILayout.Button("replay"))
        {
            BattlePlayerTest test = target as BattlePlayerTest;
            test.Replay();
        }
    }
}
