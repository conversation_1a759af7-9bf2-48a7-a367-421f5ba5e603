using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEditor;

[CustomEditor(typeof(EventTrack), true), CanEditMultipleObjects]
internal class EventTrackEditor : Editor
{
    // Fields
    private SerializedProperty segment;

    public void OnEnable()
    {
        this.segment = serializedObject.FindProperty("segment");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(this.segment);

        serializedObject.ApplyModifiedProperties();
    }
}
