using UnityEngine;
using UnityEditor;
using System.IO;
using UnityEditorInternal;

public class CardPrefabSyncEditor : Editor
{
    [MenuItem("CONTEXT/CardConfig/ͬ����Ԥ����")]
    public static void PrefabSync(MenuCommand menu)
    {
        string prefabResPath = "Assets/Art/Characters/[name]/Prefabs/[name].prefab";
        var go = Selection.activeGameObject;
        Component compType = (Component)menu.context;
        ComponentUtility.CopyComponent(compType);

        if (go != null)
        {
            string compPath = go.name; //���·��
            string prefabName = "";

            if (compPath.Contains("(Clone)"))
            {
                compPath = compPath.Replace("(Clone)", "");
            }
            prefabName = compPath;

            if (!string.IsNullOrEmpty(prefabName))
            {
                string pfName = prefabResPath.Replace("[name]", prefabName);
                if (File.Exists(pfName))
                {
                    var prefabIns = AssetDatabase.LoadAssetAtPath<GameObject>(pfName);
                    Transform t = prefabIns.transform;
                    if (t != null)
                    {
                        var comp = t.GetComponent(compType.GetType().Name);
                        if (comp != null)
                        {
                            ComponentUtility.PasteComponentValues(comp);
                        }
                        else
                        {
                            ComponentUtility.PasteComponentAsNew(t.gameObject);
                        }

                        SkillEffectTools.BackupCardConfig(pfName, prefabIns,true);

                        EditorUtility.SetDirty(prefabIns);
                        AssetDatabase.Refresh();
                        Debug.Log("ͬ���ɹ�"+ pfName);
                    }
                    else
                    {
                        Debug.LogError("Ԥ������·�����������飺" + compPath);
                    }
                }
                else
                {
                    Debug.LogError("Ԥ���岻����Ŀ¼�У�" + pfName);
                }
            }
            else
            {
                Debug.LogError("û���ҵ����Ԥ����"+ prefabName);
            }
        }
    }
}
