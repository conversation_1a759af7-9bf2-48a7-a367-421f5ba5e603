using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

[CustomEditor(typeof(BattleActorManager))]
public class BattleActorManagerEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        if (GUILayout.Button("Sync Hud Position"))
        {
            BattleActorManager manager = target as BattleActorManager;

            Transform mask = manager.transform.parent.Find("Mask");
            Transform HudManager = manager.transform.parent.Find("CanvasHud");

            for (int i = 0; i < HudManager.childCount; ++i)
            {
                Transform hud = HudManager.GetChild(i);
                string[] words = hud.name.Split('_');
                string name = string.Format("Mask_{0}", words[1]);
                Transform mask_pos = mask.Find(name);
                hud.position = mask_pos.position;
            }
        }

        if (GUILayout.Button("Sync Node Position"))
        {
            BattleActorManager manager = target as BattleActorManager;

            Transform mask = manager.transform.parent.Find("Mask");
            Transform nodes = manager.transform.parent.Find("Node");

            for (int i = 0; i < nodes.childCount; ++i)
            {
                Transform node = nodes.GetChild(i);
                string name = string.Format("Mask_{0}", node.name);
                Transform mask_pos = mask.Find(name);
                node.position = mask_pos.position;
            }
        }

        if(GUILayout.Button("Sync bg Position"))
        {
            BattleActorManager manager = target as BattleActorManager;

            Transform mask = manager.transform.Find("Mask");
            Transform nodes = manager.transform.Find("Background");

            for (int i = 0; i < nodes.childCount; ++i)
            {
                Transform bg = nodes.GetChild(i);
                string[] words = bg.name.Split('_');
                if (words.Length == 1)
                    continue;
                string name = string.Format("Mask_{0}", words[1]);
                Transform mask_pos = mask.Find(name);
                bg.position = mask_pos.position;
            }
        }

        if (GUILayout.Button("Set Ps's render layer"))
        {
            ParticleSystem[] pss = FindObjectsOfType<ParticleSystem>();
            foreach (ParticleSystem ps in pss)
            {
                Renderer render = ps.GetComponent<Renderer>();
                render.sortingLayerName = "SkillEffect";
            }
        }
    }
}


