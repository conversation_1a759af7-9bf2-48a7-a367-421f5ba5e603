using System;
using System.Collections.Generic;
using UnityEngine.Timeline;

public class ConfigContext
{
    public enum Entry
    {
        SKILLID,
        PATH,
        COMPILE,
        FORWARD,
        BACKWARD,
        CAST_ANIM,
        CAST_VFX,
        FLYING_VFX,
        HIT_VFX,
        CAST_SFX,
        EVENT,

        ENTRY_MAX,
    }

    public int[] index;

    public ConfigContext(string[] headers)
    {
        index = new int[(int)Entry.ENTRY_MAX];
        index[(int)Entry.SKILLID] = System.Array.FindIndex(headers, item => item == "nSkillID");
        index[(int)Entry.PATH] = System.Array.FindIndex(headers, item => item == "strPath");
        index[(int)Entry.COMPILE] = System.Array.FindIndex(headers, item => item == "bCompile");
        index[(int)Entry.FORWARD] = System.Array.FindIndex(headers, item => item == "forward");
        index[(int)Entry.BACKWARD] = System.Array.FindIndex(headers, item => item == "backward");
        index[(int)Entry.CAST_ANIM] = System.Array.FindIndex(headers, item => item == "castAnim");
        index[(int)Entry.CAST_VFX] = System.Array.FindIndex(headers, item => item == "castVfx");
        index[(int)Entry.FLYING_VFX] = System.Array.FindIndex(headers, item => item == "flyingVfx");
        index[(int)Entry.HIT_VFX] = System.Array.FindIndex(headers, item => item == "hitVfx");
        index[(int)Entry.CAST_SFX] = System.Array.FindIndex(headers, item => item == "castSfx");
        index[(int)Entry.EVENT] = System.Array.FindIndex(headers, item => item == "hitEvent");
    }


    public string Read(string[] values, Entry entry)
    {
        return values[index[(int)entry]];
    }

    public int ReadAsInt(string[] values, Entry entry)
    {
        return int.Parse(values[index[(int)entry]]);
    }

    public string CalculateConfigMD5Hash(string[] values)
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        for (int i = (int)Entry.FORWARD; i < (int)Entry.ENTRY_MAX; ++i)
        {
            if (values[i] != null && values[i] != "")
                sb.Append(values[i]);
        }
        return CalculateMD5Hash(sb.ToString());
    }

    private string CalculateMD5Hash(string input)
    {
        System.Security.Cryptography.MD5 md5 = System.Security.Cryptography.MD5.Create();
        byte[] inputBytes = System.Text.Encoding.ASCII.GetBytes(input);
        byte[] hash = md5.ComputeHash(inputBytes);

        System.Text.StringBuilder sb = new System.Text.StringBuilder();
        for (int i = 0; i < hash.Length; i++)
            sb.Append(hash[i].ToString("x2"));

        return sb.ToString();
    }
}

public class SkillConext
{
    public TimelineAsset skillAsset;

    Dictionary<Type, List<TrackAsset>> tracks = new Dictionary<Type, List<TrackAsset>>();

    public T GetTrackAsset<T>(War.Battle.TrackBindingStamp stamp = War.Battle.TrackBindingStamp.Binding_Default) where T : TrackAsset, new()
    {
        List<TrackAsset> binds;
        if (tracks.TryGetValue(typeof(T), out binds) == false)
        {
            binds = new List<TrackAsset>(5);
            for (int i = 0; i < 5; i++)
                binds.Add(null);
            tracks.Add(typeof(T), binds);
        }

        T track = binds[(int)stamp] as T;
        if (track == null)
        {
            track = skillAsset.CreateTrack<T>(null, typeof(T).Name);
            binds[(int)stamp] = track;
        }
        return track;
    }

    public double FrameToTime(int frame)
    {
        return (double)frame / skillAsset.editorSettings.fps;
    }
}
