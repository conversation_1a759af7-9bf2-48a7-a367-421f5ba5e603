using System.Collections;
using System.Collections.Generic;
//using CasualGame.TinyFish.Utilities;
using UnityEngine;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using War.Battle;
using War.UI;

[CustomEditor(typeof(War.Battle.CardConfig))]
public class CardConfigEditor : Editor
{
    BoxBoundHandleEditor boundingEditor = new BoxBoundHandleEditor();
    FreeMoveHandleEditor headPosEditor = new FreeMoveHandleEditor();
    FreeMoveHandleEditor shifaPosEditor = new FreeMoveHandleEditor();
    FreeMoveHandleEditor shoujiPosEditor = new FreeMoveHandleEditor();

    FreeMoveHandleEditor buffHeadPosEditor = new FreeMoveHandleEditor();
    FreeMoveHandleEditor buffCenterPosEditor = new FreeMoveHandleEditor();

    private bool isUseMesh = false;
    public CardConfigEditor()
    {
        boundingEditor.CopyColliderPropertiesToHandle += CopyColliderPropertiesToHandle;
        boundingEditor.CopyHandlePropertiesToCollider += CopyHandlePropertiesToCollider;

        headPosEditor.CopyColliderPropertiesToHandle += CopyHeadPosToHandle;
        headPosEditor.CopyHandlePropertiesToCollider += CopyHandleToHeadPos;

        shifaPosEditor.CopyColliderPropertiesToHandle += CopyShifaPosToHandle;
        shifaPosEditor.CopyHandlePropertiesToCollider += CopyHandleToShifaPos;

        shoujiPosEditor.CopyColliderPropertiesToHandle += CopyShoujiPosToHandle;
        shoujiPosEditor.CopyHandlePropertiesToCollider += CopyHandleToShoujiPos;

        buffHeadPosEditor.CopyColliderPropertiesToHandle += CopyBuffHeadPosToHandle;
        buffHeadPosEditor.CopyHandlePropertiesToCollider += CopyHandleToBuffHeadPos;

        buffCenterPosEditor.CopyColliderPropertiesToHandle += CopyBuffCenterPosToHandle;
        buffCenterPosEditor.CopyHandlePropertiesToCollider += CopyHandleToBuffCenterPos;
    }
    public void OnEnable()
    {
    }

    public override void OnInspectorGUI()
    {
        EditorGUI.BeginChangeCheck();
        serializedObject.Update();
        SerializedProperty iterator = serializedObject.GetIterator();
        for (bool flag = true; iterator.NextVisible(flag); flag = false)
        {
            using (new EditorGUI.DisabledScope("m_Script" == iterator.propertyPath))
            {
                if (iterator.propertyPath == "anchor")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    headPosEditor.DrawInspectorGUI("Edit Head Pos");
                }
                else if (iterator.propertyPath == "size")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    boundingEditor.DrawInspectorGUI("Edit Bound");

                }
                else if (iterator.propertyPath == "shifaPoint")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    shifaPosEditor.DrawInspectorGUI("Edit Bound");

                }
                else if (iterator.propertyPath == "shoujiPoint")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    shoujiPosEditor.DrawInspectorGUI("Edit Bound");

                }
                else if (iterator.propertyPath == "buffHeadPoint")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    buffHeadPosEditor.DrawInspectorGUI("Edit Buff Head Pos");

                }
                else if (iterator.propertyPath == "buffCenterPoint")
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                    buffCenterPosEditor.DrawInspectorGUI("Edit Buff Bound");

                }
                //else if (iterator.propertyPath == "rayBackground")
                //{
                //    CardConfig card = target as CardConfig;
                //    string name = card.rayBackground;
                //    PrefabAssets assets = AssetDatabase.LoadAssetAtPath<PrefabAssets>("Assets/Art/Maps/Battle/raybg/background.asset");
                //    int count = assets.GetPrefabCount();
                //    List<string> choices = new List<string>();
                //    int index = 0;
                //    for (int i = 0; i < count; ++i)
                //    {
                //        GameObject sp = assets.GetPrefabByIndex(i);
                //        if (sp.name == name)
                //            index = i;

                //        choices.Add(sp.name);
                //    }

                //    index = EditorGUILayout.Popup("Ray Background", index, choices.ToArray());
                //    card.rayBackground = assets.GetPrefabByIndex(index).name;
                //}
                else
                {
                    EditorGUILayout.PropertyField(iterator, true, new GUILayoutOption[0]);
                }
            }
        }
        serializedObject.ApplyModifiedProperties();
        EditorGUI.EndChangeCheck();

        //boundingEditor.DrawInspectorGUI("Edit Bound");
        //headPosEditor.DrawInspectorGUI("Edit Head Pos");

        if (GUILayout.Button("Setup Packing Tag"))
        {
            SetupPackingTag();
        }

        if (GUILayout.Button("保存到备份"))
        {
            SaveToBackupFilde();
        }

        if (GUILayout.Button("添加mesh组件"))
        {
            AddMeshMiplify();
        }

        if (GUILayout.Button("对比mesh(再次点击还原)"))
        {
            CardConfig target = (CardConfig)base.target;
            isUseMesh = !isUseMesh;
            for (int i = 0; i < target.cardMeshSimplifys.Count; i++)
            {
                CardMeshSimplify cards = target.cardMeshSimplifys[i];
                SkinnedMeshRenderer skin = cards.skin;
                if (isUseMesh && cards.simplifyMesh)
                {
                    skin.sharedMesh = cards. simplifyMesh;
                }
                else if (cards.originalMesh)
                {
                    skin.sharedMesh = cards.originalMesh;
                }
            }
        }
    }

    public void AddMeshMiplify()
    {
        CardConfig target = (CardConfig)base.target;
        SkinnedMeshRenderer[] skins = target.gameObject.GetComponentsInChildren<SkinnedMeshRenderer>();
        if (target.cardMeshSimplifys.Count > 0)
        {
            target.cardMeshSimplifys.Clear();
        }
        for (int i = 0; i < skins.Length; i++)
        {
            if(skins[i].gameObject.GetComponent<CardMeshSimplify> () == null)
            {
                skins[i].gameObject.AddComponent<CardMeshSimplify>();
            }
            skins[i].gameObject.GetComponent<CardMeshSimplify>().skin = skins[i];
            skins[i].gameObject.GetComponent<CardMeshSimplify>().originalMesh = skins[i].sharedMesh;
            string path = AssetDatabase.GetAssetPath(skins[i].sharedMesh);
            path = path.Replace("_mesh", "_LOD1");
            Mesh mesh = AssetDatabase.LoadAssetAtPath<Mesh>(path);
            if(mesh == null)
            {
                EditorUtility.DisplayDialog("提示", "请添加  "  + skins[i].sharedMesh.name + "的  LOD1  资源，并再次使用此功能", "确认");
                return;
            }
            SetABNameByPath(mesh);
            skins[i].gameObject.GetComponent<CardMeshSimplify>().simplifyMesh = mesh;
            target.cardMeshSimplifys.Add(skins[i].gameObject.GetComponent<CardMeshSimplify>());
        }
    }
    void SetABNameByPath(Object o)
    {
        var assetPath = AssetDatabase.GetAssetPath(o);
        var assetImporter = AssetImporter.GetAtPath(assetPath);
        string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
        assetImporter.assetBundleName = assetBundleName;
    }
    protected void OnSceneGUI()
    {
        CardConfig target = (CardConfig)base.target;
        boundingEditor.DrawSceneGUI(target.transform,target.AttackRange);
        headPosEditor.DrawSceneGUI(target.transform);
        shifaPosEditor.DrawSceneGUI(target.transform);
        shoujiPosEditor.DrawSceneGUI(target.transform);
        buffHeadPosEditor.DrawSceneGUI(target.transform);
        buffCenterPosEditor.DrawSceneGUI(target.transform);
    }

    private void CopyColliderPropertiesToHandle(BoxBoundsHandle handle)
    {
        CardConfig target = (CardConfig)base.target;
        handle.center = BoxBoundHandleEditor.TransformColliderCenterToHandleSpace(target.transform, target.center);
        //handle.size = Vector3.Scale(Vector3.one*target.size, target.transform.lossyScale);
        handle.size = Vector3.one * target.size;

        float rowScale = 1;

        if (target.GetComponent<Card>())
        {
            rowScale = target.GetComponent<Card>().thisOrderSize;
        }
        target.gameObject.transform.localScale = handle.size* rowScale;
    }

    private void CopyHandlePropertiesToCollider(BoxBoundsHandle handle)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.center = BoxBoundHandleEditor.TransformHandleCenterToColliderSpace(target.transform, handle.center);
        Vector3 vector = Vector3.Scale(handle.size, BoxBoundHandleEditor.InvertScaleVector(target.transform.lossyScale));
        float x = Mathf.Abs(vector.x);
        float y = Mathf.Abs(vector.y);
        vector = new Vector3(x, y, Mathf.Abs(vector.z));
        target.size = vector.x;
    }

    private void CopyHeadPosToHandle(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        pos = target.anchor;
    }

    private void CopyHandleToHeadPos(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.anchor = pos;
    }

    private void CopyShifaPosToHandle(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        pos = target.shifaPoint;
    }

    private void CopyHandleToShifaPos(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.shifaPoint = pos;
    }

    private void CopyShoujiPosToHandle(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        pos = target.shoujiPoint;
    }

    private void CopyHandleToShoujiPos(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.shoujiPoint = pos;
    }

    private void CopyBuffHeadPosToHandle(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        pos = target.buffHeadPoint;
    }

    private void CopyHandleToBuffHeadPos(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.buffHeadPoint = pos;
    }


    private void CopyBuffCenterPosToHandle(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        pos = target.buffCenterPoint;
    }

    private void CopyHandleToBuffCenterPos(ref Vector3 pos)
    {
        CardConfig target = (CardConfig)base.target;
        Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));

        target.buffCenterPoint = pos;
    }

    private void SetupPackingTag()
    {
        //CardConfig card = target as CardConfig;
        //Anima2D.SpriteMeshInstance[] meshes = card.GetComponentsInChildren<Anima2D.SpriteMeshInstance>(true);

        //foreach (Anima2D.SpriteMeshInstance mesh in meshes)
        //{
        //    Sprite sprite = mesh.spriteMesh.sprite;
        //    if (sprite == null)
        //        continue;

        //    string path = AssetDatabase.GetAssetPath(sprite);
        //    TextureImporter importer = (TextureImporter)TextureImporter.GetAtPath(path);
        //    switch (mesh.gameObject.layer)
        //    {
        //        // layerid == 30   BattleActorModelBg
        //        case 30:
        //            importer.spritePackingTag = System.IO.Path.GetDirectoryName(path).Replace("Assets/", "").Replace('/', '.').ToLower() + "_beijing";
        //            break;
        //        default:
        //            importer.spritePackingTag = System.IO.Path.GetDirectoryName(path).Replace("Assets/", "").Replace('/', '.').ToLower();
        //            break;
        //    }

        //    AssetDatabase.WriteImportSettingsIfDirty(path);
        //    importer.SaveAndReimport();
        //}
    }

    public void SaveToBackupFilde()
    {

    }
    public static void SetupPackingTagOne(string p)
    {
        Debug.Log("SetupPackingTagOne:"+p);

        var go = AssetDatabase.LoadAssetAtPath<GameObject>(p);

        //CardConfig card = target as CardConfig;
        //Anima2D.SpriteMeshInstance[] meshes = go.GetComponentsInChildren<Anima2D.SpriteMeshInstance>(true);
        //var list = new List<string>();
        //foreach (Anima2D.SpriteMeshInstance mesh in meshes)
        //{
        //    Sprite sprite = mesh.spriteMesh.sprite;
        //    if (sprite == null)
        //        continue;

        //    string path = AssetDatabase.GetAssetPath(sprite);
        //    TextureImporter importer = (TextureImporter)TextureImporter.GetAtPath(path);
        //    if (!mesh) continue;
        //    switch (mesh.gameObject.layer)
        //    {
        //        // layerid == 30   BattleActorModelBg
        //        case 30:
        //            importer.spritePackingTag = System.IO.Path.GetDirectoryName(path).Replace("Assets/", "").Replace('/', '.').ToLower() + "_beijing";
        //            break;
        //        default:
        //            importer.spritePackingTag = System.IO.Path.GetDirectoryName(path).Replace("Assets/", "").Replace('/', '.').ToLower();
        //            break;
        //    }
        //    list.Add(path);
        //}
        //UIHelper.ImportAssets(list.ToArray());

            //AssetDatabase.WriteImportSettingsIfDirty(path);
            //importer.SaveAndReimport();
    }
}
