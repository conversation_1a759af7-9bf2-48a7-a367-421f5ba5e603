using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.UI;
using War.Base;
using War.Battle;

#if UNITY_EDITOR
public class SkillEffectTools
{

    static string succStr = "<color=#55ff55>Succ</color>";
    [MenuItem("Assets/X-Hero����/��ʼ��ģ��", false, 1)]
    static public void SetupCharecter()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);

        if (!string.IsNullOrEmpty(path))
        {
            try
            {
                string card_check = succStr, cardconfig_check = succStr, mat_check = succStr, shadow_check = succStr;
                GameObject go = Selection.activeObject as GameObject;
                //TODO:����cardConfig���
                CardConfig _config = go.GetComponent<CardConfig>();
                if (_config == null)
                {
                    _config = go.AddComponent<CardConfig>();
                    _config.anchor = new Vector2(0, 1.4f);
                    _config.center = new Vector2(0, 1);
                    _config.size = 1;
                    _config.shifaPoint = new Vector3(0, 1, 0);
                    _config.shoujiPoint = new Vector3(0, 1, 0);
                    _config.aligment = CardConfig.Alignment.Model_3D;
                    _config.GetComponent<CardConfigEditor>().AddMeshMiplify();
                    ////��ԭ����
                    //BackupCardConfig(path, go);
                }
                else
                {
                    cardconfig_check = "�Ѱ���";

                }
                //TODO:����card���
                Card _card = go.GetComponent<Card>();
                if (_card == null)
                {
                     _card = go.AddComponent<Card>();
                    _card.isBattleCard = true;
                    _card.isOffline = true;
                }
                else
                {
                    card_check = "�Ѱ���";
                }
                //TODO:����Surface3D Shader����

                go.layer = LayerMask.NameToLayer("");

                //�����Σ���ģ�͸�Ŀ¼
                string matPath = path.Substring(0, path.LastIndexOf('/'));
                matPath = matPath.Substring(0, matPath.LastIndexOf('/')) + "/Materials/" + go.name + "_battle.mat";
                Material mainMat = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                if (mainMat == null)
                {
                    mainMat = new Material(Shader.Find("Comic/3DSurface"));
                    AssetDatabase.CreateAsset(mainMat, matPath);

                    EnableKeyword(mainMat, "DISSOLVE_ON", "_EnableDissolve");
                    EnableKeyword(mainMat, "GRAY_ON", "_EnableGray");
                    EnableKeyword(mainMat, "SELF_LIGHT_ON", "_EnableSelfLight");

                    SkinnedMeshRenderer[] meshes = go.GetComponentsInChildren<SkinnedMeshRenderer>();
                    foreach (SkinnedMeshRenderer renderer in meshes)
                    {
                        renderer.gameObject.layer = LayerMask.NameToLayer("BattleActorModel");
                        Material originMat = renderer.sharedMaterial;
                        
                        Texture sp = originMat.mainTexture;
                        Texture normal = originMat.GetTexture("_BumpMap");
                        Texture emission = originMat.GetTexture("_EmissionMap");

                        renderer.sharedMaterial = mainMat;
                        renderer.sharedMaterial.mainTexture = sp;
                        renderer.sharedMaterial.SetTexture("_BumpMap", normal);
                        renderer.sharedMaterial.SetTexture("_EmissionMap", emission);
                        renderer.sharedMaterial.renderQueue = 2450;
                        renderer.sharedMaterial.SetInt("_Stencil", 2);
                        renderer.sharedMaterial.SetInt("_StencilReadMask", 1);
                        renderer.sharedMaterial.SetInt("_StencilComp", (int)UnityEngine.Rendering.CompareFunction.Equal);
                        renderer.sharedMaterial.SetInt("_StencilOp", (int)UnityEngine.Rendering.StencilOp.Replace);
                        //TODO:����Shadow����
                        int mat_length = renderer.sharedMaterials.Length;
                        if (mat_length <= 1)
                        {
                            Material[] newMaterials = new Material[mat_length + 1];
                            newMaterials[0] = renderer.sharedMaterial;
                            Material shadowMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art/Common/Shadow.mat");
                            if (shadowMat)
                            {
                                newMaterials[1] = shadowMat;
                            }
                            renderer.sharedMaterials = newMaterials;
                        }
                        else
                        {
                            shadow_check = "faild";
                        }
                    }
                }
                else
                {
                    mat_check = "�Ѵ���";
                    shadow_check = "faild";
                    Debug.Log($"�Ѵ��ڲ���{matPath}��������Ҫ���ֶ�ɾ���ٳ��Գ�ʼ��");
                }
                string[] deps = AssetDatabase.GetDependencies(AssetDatabase.GetAssetPath(go));
                for (int i = 0; i < deps.Length; i++)
                {
                    if (deps[i].Contains("Texture"))
                    {
                        TextureImporter importer = AssetImporter.GetAtPath(deps[i]) as TextureImporter;
                        importer.mipmapEnabled = false;
                        EditorUtility.SetDirty(importer);
                        importer.SaveAndReimport();
                    }
                }
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                AssetbundlesMenuItems.SetAssetBundleByPath();
                Debug.Log($"Card:{card_check}\tCardConfig:{cardconfig_check}\tMaterial:{mat_check}\tShadow:{shadow_check}\n���޸��ļ���{ path}");
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
    }

    /// <summary>
    /// ���ñ���CardConfig
    /// </summary>
    /// <param name="path">����·��</param>
    /// <param name="go"></param>
    /// <param name="isOverried"></param>
    public static void BackupCardConfig(string path, GameObject go, bool isOverried = false)
    {
        CardConfig _config = go.GetComponent<CardConfig>();

        //��ȡ���ݵ���Դ
        string configPath = path.Substring(0, path.LastIndexOf('/'));
        configPath = configPath.Substring(0, configPath.LastIndexOf('/'));
        configPath = configPath.Substring(0, configPath.LastIndexOf('/')) + "/configbackup/";

        string backupPath = configPath + "config_" + go.name + ".prefab";
        GameObject backupObject = AssetDatabase.LoadAssetAtPath<GameObject>(backupPath);

        if (backupObject)
        {
            if (isOverried)
            {
                CardConfig upbackCardConfig = backupObject.GetComponent<CardConfig>();
                CardConfig idleCardConfig = go.GetComponent<CardConfig>();

                //����Ǹ�д����go��Config�滻backup��
                ComponentUtility.CopyComponent(idleCardConfig);
                ComponentUtility.PasteComponentValues(upbackCardConfig);
            }
            else
            {
                CardConfig upbackCardConfig = backupObject.GetComponent<CardConfig>();
                CardConfig idleCardConfig = go.GetComponent<CardConfig>();

                //�ñ��ݵĻ�ԭ
                ComponentUtility.CopyComponent(upbackCardConfig);
                ComponentUtility.PasteComponentValues(idleCardConfig);
            }
        }
        else
        {
            GameObject temp = new GameObject();
            CardConfig tempConfig = temp.AddComponent<CardConfig>();
            ComponentUtility.CopyComponent(_config);
            ComponentUtility.PasteComponentValues(tempConfig);
            backupObject = PrefabUtility.SaveAsPrefabAsset(temp, backupPath);
            Editor.DestroyImmediate(temp);
        }

        CardConfigAssets relate = AssetDatabase.LoadAssetAtPath<CardConfigAssets>(configPath + "relate.asset");

        if (relate && backupObject)
        {
            bool isExist = false;
            if (relate.RelateList == null)
                relate.RelateList = new List<CardRelate>();
            foreach (CardRelate item in relate.RelateList)
            {
                if (item.heroName.Equals(go.name))
                {
                    item.card = go;
                    item.config = backupObject as GameObject;
                    isExist = true;
                    break;
                }
            }

            if (!isExist)
            {
                CardRelate temp = new CardRelate();
                temp.heroName = go.name;
                temp.card = go;
                temp.config = backupObject as GameObject;
                relate.RelateList.Add(temp);
            }
        }
        Debug.Log($"{go.name} Config���ݳɹ� " );
    }

    [MenuItem("Tools/ģ�ʹ���/�������RenderQueue", false, 2)]
    static public void FixMaterialRenderQueue()
    {
        string charecterPath = "Assets/art/Characters";
        if (Directory.Exists(charecterPath))
        {

            DirectoryInfo direction = new DirectoryInfo(charecterPath);
            FileInfo[] files = direction.GetFiles("*",SearchOption.AllDirectories);
            foreach (FileInfo mat in files)
            {
                if (mat.Name.EndsWith(".mat"))
                {
                    string matPath = mat.FullName;
                    matPath = matPath.Substring(matPath.LastIndexOf("Assets"));
                    Material matObject = AssetDatabase.LoadAssetAtPath<Material>(matPath);
                    matObject.renderQueue = 2450;
                    Debug.Log($"���ʣ�path={matPath}��mat={matObject.renderQueue}");
                }
            }
        }
        else
        {
            Debug.LogError("û����Դ");
        }
    }

    [MenuItem("Tools/ģ�ʹ���/�����ɫLayer", false, 3)]
    static public void FixCharecterLayer()
    {
        string charecterPath = "Assets/art/Characters";
        if (Directory.Exists(charecterPath))
        {

            DirectoryInfo direction = new DirectoryInfo(charecterPath);
            FileInfo[] files = direction.GetFiles("*", SearchOption.AllDirectories);
            foreach (FileInfo obj in files)
            {
                if (obj.Name.EndsWith(".prefab"))
                {
                    string matPath = obj.FullName;
                    matPath = matPath.Substring(matPath.LastIndexOf("Assets"));
                    GameObject matObject = AssetDatabase.LoadAssetAtPath<GameObject>(matPath);
                    if (matObject.GetComponent<Card>())
                    {
                        matObject.layer = 31;
                    }
                    else if(matObject.name.EndsWith("_Skill"))
                    {
                        matObject.layer = 26;
                    }
                    Debug.Log($"���ò㼶��path={matPath}��layer={matObject.layer}");
                }
            }
        }
        else
        {
            Debug.LogError("û����Դ");
        }
    }

    [MenuItem("Tools/ģ�ʹ���/��������CardConfig", false, 4)]
    static public void CreateConfigBackup()
    {
        string charecterPath = "Assets/art/Characters";
        if (Directory.Exists(charecterPath))
        {

            DirectoryInfo direction = new DirectoryInfo(charecterPath);
            FileInfo[] files = direction.GetFiles("*", SearchOption.AllDirectories);
            foreach (FileInfo obj in files)
            {
                if (obj.Name.EndsWith(".prefab") && !obj.Name.Contains("_Skill") && !obj.Name.Contains("config_"))
                {
                    string matPath = obj.FullName;
                    matPath = matPath.Substring(matPath.LastIndexOf("Assets"));
                    GameObject matObject = AssetDatabase.LoadAssetAtPath<GameObject>(matPath);

                    matPath = matPath.Replace('\\', '/');
                    BackupCardConfig(matPath, matObject,true);

                }
            }
        }
        else
        {
            Debug.LogError("û����Դ");
        }
    }

    [MenuItem("Tools/ģ�ʹ���/��ԭ����CardConfig", false, 5)]
    static public void RecoverConfigBackup()
    {
        string charecterPath = "Assets/art/Characters";
        if (Directory.Exists(charecterPath))
        {

            DirectoryInfo direction = new DirectoryInfo(charecterPath);
            FileInfo[] files = direction.GetFiles("*", SearchOption.AllDirectories);
            foreach (FileInfo obj in files)
            {
                if (obj.Name.EndsWith(".prefab") && !obj.Name.Contains("_Skill") && !obj.Name.Contains("config_"))
                {
                    string matPath = obj.FullName;
                    matPath = matPath.Substring(matPath.LastIndexOf("Assets"));
                    GameObject matObject = AssetDatabase.LoadAssetAtPath<GameObject>(matPath);

                    matPath = matPath.Replace('\\', '/');
                    BackupCardConfig(matPath, matObject);

                }
            }
        }
        else
        {
            Debug.LogError("û����Դ");
        }
    }

    static void EnableKeyword(Material mat, string keyword, string uniform)
    {
        mat.SetFloat(uniform, 1);
        mat.EnableKeyword(keyword);
    }

    [MenuItem("GameObject/X-Hero����/��Ӳ���Camera", false, 10)]
    public static void AddCamera()
    {
        Camera cam = Selection.activeGameObject.GetComponentInChildren<Camera>(true);
        if(cam != null)
        {
            RenderTexture rt = AssetDatabase.LoadAssetAtPath<RenderTexture>("Assets/art/effects_source/test/test_skillrt.rendertexture");
            if (rt == null)
            {
                Debug.LogError("û����Դ");
                return;
            }
            cam.targetTexture = rt;

            CanvasGroup canvaAlpha = GameObject.Find("director/ComicBattlePlayer/6v6_f3b3_f3b3_0/CanvasOutsideSkill/group").GetComponent<CanvasGroup>();
            canvaAlpha.alpha = 1;

            RawImage ri = GameObject.Find("director/ComicBattlePlayer/6v6_f3b3_f3b3_0/CanvasOutsideSkill/group/TargetImage").GetComponent<RawImage>();
            ri.texture = rt;
            Debug.Log("���óɹ�");
        }
        else
        {
            Debug.LogError("û���ҵ������");
        }
    }

    [MenuItem("GameObject/X-Hero����/ɾ�����Դ���", false, 11)]
    public static void RemoveCamera()
    {
        CanvasGroup canvaAlpha = GameObject.Find("director/ComicBattlePlayer/6v6_f3b3_f3b3_0/CanvasOutsideSkill/group").GetComponent<CanvasGroup>();
        canvaAlpha.alpha = 0;
        GameObject.DestroyImmediate(Selection.activeGameObject);
    }

    [MenuItem("Assets/X-Hero����/���Ը��ƶ���״̬��", false, 21)]
    static public void CopyOverrideController()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);

        if (!string.IsNullOrEmpty(path))
        {
            try
            {
                Dictionary<string, AnimationClip> clipList = new Dictionary<string, AnimationClip>();
                AnimatorOverrideController overrideController = Selection.activeObject as AnimatorOverrideController;
                string logStr = $"״̬�� >>>>> {overrideController.name}��length>> {overrideController.clips.Length}";
                foreach (var item in overrideController.clips)
                {
                    if (item.overrideClip)
                    {
                        clipList.Add(item.originalClip.name, item.overrideClip);
                        logStr += $"\n oriClip >>>> {item.originalClip.name}��overried >> {item.overrideClip.name}";
                    }
                    else if (item.originalClip)
                    {
                        logStr += $"\n oriClip >>>> {item.originalClip.name}��overried >> missing";
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }
    }
}
#endif