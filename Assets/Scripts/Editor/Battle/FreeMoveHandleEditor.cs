using UnityEngine;
using UnityEditor;
using UnityEditor.IMGUI.Controls;


class FreeMoveHandleEditor
{
    private bool m_IsEditing = false;
    public static Color s_handleColor = new Color(145f / 255f, 244f / 255f, 139f / 255f, 210f / 255f);
    public static Color s_handleColorDisabled = new Color(84f / 255f, 200f / 255f, 77f / 255f, 140f / 255f);

    public delegate void SynceProperties(ref Vector3 point);

    public SynceProperties CopyColliderPropertiesToHandle;
    public SynceProperties CopyHandlePropertiesToCollider;

    public void DrawInspectorGUI(string label)
    {
        GUIStyle style = new GUIStyle("Button");
        EditorGUI.BeginChangeCheck();
        m_IsEditing = ToggleButton(m_IsEditing, label, style);
        if (EditorGUI.EndChangeCheck())
        {
            SceneView.RepaintAll();
        }
    }

    private bool ToggleButton(bool toggle, string label, GUIStyle style)
    {
        Rect rect = EditorGUILayout.GetControlRect(true, 23f, style);
        Rect position = new Rect(rect.xMin + EditorGUIUtility.labelWidth, rect.yMin, 33f, 23f);
        GUIContent content = new GUIContent(label);
        Vector2 vector = GUI.skin.label.CalcSize(content);
        Rect labelRect = new Rect(position.xMax + 5f, rect.yMin + ((rect.height - vector.y) * 0.5f), vector.x, rect.height);

        bool t = GUI.Toggle(position, toggle, EditorGUIUtility.IconContent("EditCollider"), style);
        GUI.Label(labelRect, label);
        return t;
    }

    public void DrawSceneGUI(Transform transform)
    {
        if (m_IsEditing)
        {
            if (!Mathf.Approximately(transform.lossyScale.sqrMagnitude, 0f))
            {
                using (new Handles.DrawingScope(Matrix4x4.TRS(transform.position, transform.rotation, transform.localScale)))
                {
                    Color c = Handles.color;
                    Handles.color = s_handleColor;
                    Vector3 pos = Vector3.zero;
                    CopyColliderPropertiesToHandle(ref pos);
                    EditorGUI.BeginChangeCheck();
#if UNITY_2022_1_OR_NEWER
                    pos = Handles.FreeMoveHandle(pos, HandleUtility.GetHandleSize(pos) * 0.1f, Vector3.zero, Handles.CubeHandleCap);
#else
                    pos = Handles.FreeMoveHandle(pos, Quaternion.identity, HandleUtility.GetHandleSize(pos) * 0.1f, Vector3.zero, Handles.CubeHandleCap);
#endif
                    Handles.color = c;
                    if (EditorGUI.EndChangeCheck())
                    {
                        CopyHandlePropertiesToCollider(ref pos);
                    }
                }
            }
        }
    }

}
