using UnityEditor;

[CustomEditor(typeof(AudioExTrack), true), CanEditMultipleObjects]
internal class AudioExTrackEditor : Editor
{
    private SerializedProperty segment;

    public void OnEnable()
    {
        segment = serializedObject.FindProperty("segment");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(segment);

        serializedObject.ApplyModifiedProperties();
    }
}
