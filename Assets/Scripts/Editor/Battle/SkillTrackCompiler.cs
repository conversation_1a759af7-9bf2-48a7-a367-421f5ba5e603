using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.Timeline;
using System.IO;

public class SkillTrackCompiler
{
    Dictionary<uint, string> vfx = null;

    private delegate void CompileHandler(SkillConext context, string clip);
    Dictionary<ConfigContext.Entry, CompileHandler> compileHandlers = new Dictionary<ConfigContext.Entry, CompileHandler>();

    public SkillTrackCompiler()
    {
        compileHandlers.Add(ConfigContext.Entry.CAST_ANIM, CompileCastAnimationTrack);
        compileHandlers.Add(ConfigContext.Entry.CAST_VFX, CompileVisualFxSpawnTrack);
        compileHandlers.Add(ConfigContext.Entry.FLYING_VFX, CompileVisualFxFlyingTrack);
        compileHandlers.Add(ConfigContext.Entry.HIT_VFX, CompileVisualFxSpawnTrack);
        compileHandlers.Add(ConfigContext.Entry.CAST_SFX, CompileCastSoundFxTrack);
        compileHandlers.Add(ConfigContext.Entry.EVENT, CompileEventTrack);
    }

    public void ReadVisualFxConfig(string path)
    {
        vfx = new Dictionary<uint, string>();

        using (StreamReader reader = new StreamReader(path))
        {
            int lineNumber = 1;
            string line = reader.ReadLine();
            string[] values = line.Split(',');
            values[0] = values[0].Replace("{name}", "");
            int ID = System.Array.FindIndex(values, item => item == "nParticleID");
            int PATH = System.Array.FindIndex(values, item => item == "strResPath");

            for (int i = 0; i < 4; i++)
            {
                reader.ReadLine();
                ++lineNumber;
            }

            while (!reader.EndOfStream)
            {
                line = reader.ReadLine();
                values = line.Split(',');

                try
                {
                    vfx.Add(uint.Parse(values[ID]), values[PATH]);
                }
                catch (Exception e)
                {
                    Debug.LogErrorFormat("Error occured when parsing file {0}, at line {1}, message: {2}", path, lineNumber, e.Message);
                }
            }
        }
    }

    public void CompileConfig(string path, bool force)
    {
        using (StreamReader reader = new StreamReader(path))
        {
            int lineNumber = 1;
            string line = reader.ReadLine();
            string[] values = line.Split(',');
            values[0] = values[0].Replace("{name}", "");

            ConfigContext context = new ConfigContext(values);

            for (int i = 0; i < 6; i++)
            {
                reader.ReadLine();
                ++lineNumber;
            }

            while (!reader.EndOfStream)
            {
                line = reader.ReadLine();
                values = line.Split(',');

                try
                {
                    Compile(context, values, force);
                }
                catch (Exception e)
                {
                    Debug.LogErrorFormat("Error occured when parsing file {0}, at line {1}, message: {2}", path, lineNumber, e.Message);
                }
            }
        }
    }

    private void ReadUserData(string userData, ref string config, ref DateTime dt)
    {
        string[] words = userData.Split(',');
        for (int i = 0; i < words.Length; i++)
        {
            string[] tokens = words[i].Split(':');
            if (tokens[0] == "config")
            {
                config = tokens[1];
            }
            else if (tokens[1] == "time")
            {
                dt = DateTime.Parse("1970-1-1").Add(new TimeSpan(0, 0, int.Parse(tokens[1])));
            }
        }
    }

    private string WriteUserData(string config, DateTime time)
    {
        int seconds = (int)time.Subtract(DateTime.Parse("1970-1-1")).TotalSeconds;
        return string.Format("config:{0},time:{1}", config, seconds);
    }

    private void Compile(ConfigContext context, string[] line, bool force)
    {
        string path = context.Read(line, ConfigContext.Entry.PATH);
        string assetPath = "Assets/" + path;

        AssetImporter importer = AssetImporter.GetAtPath(assetPath);
        string hash = context.CalculateConfigMD5Hash(line);

        string compiledHash = null;
        DateTime compiledTime = new DateTime();
        if (importer != null)
            ReadUserData(importer.userData, ref compiledHash, ref compiledTime);

        bool compile = true;
        string temp = context.Read(line, ConfigContext.Entry.COMPILE);
        if (temp != null && temp != "")
            compile = false;

        if (force || ((hash != compiledHash) && compile))
        {
            TimelineAsset skill = CompileSkill(context, line);
            string userdata = WriteUserData(hash, DateTime.UtcNow);
            Save(skill, path, userdata);
        }
    }

    private TimelineAsset CompileSkill(ConfigContext context, string[]line)
    {
        SkillConext skillContext = new SkillConext();
        skillContext.skillAsset = new TimelineAsset();

        for(int i = (int)ConfigContext.Entry.FORWARD; i < (int)ConfigContext.Entry.ENTRY_MAX; ++i)
        {
            string clips = line[context.index[i]];
            if (clips != null && clips != "")
            {
                CompileHandler handler;
                if (compileHandlers.TryGetValue((ConfigContext.Entry)i, out handler))
                {
                    handler.Invoke(skillContext, clips);
                }
            }
        }

        //CompileFlowTipsTweenTrack(skillContext, "30#30#mp");

        return skillContext.skillAsset;
    }

    private void CompileCastAnimationTrack(SkillConext context, string clips)
    {
        string[] clipArray = clips.Split(';');
        foreach (string clipData in clipArray)
        {
            string[] tokens = clipData.Split('#');
            int startFrame = int.Parse(tokens[0]);
            int durationFrame = int.Parse(tokens[1]);
            string trigger = tokens[2];

            AnimationTriggerTrack track = context.GetTrackAsset<AnimationTriggerTrack>();
            TimelineClip clip = track.CreateDefaultClip();
            AnimationTriggerClip clipAsset = clip.asset as AnimationTriggerClip;
            clip.start = context.FrameToTime(startFrame);
            clip.duration = context.FrameToTime(durationFrame);
            clipAsset.template.trigger = trigger;
        }
    }

    private void CompileVisualFxSpawnTrack(SkillConext context, string clips)
    {
        string[] clipArray = clips.Split(';');
        foreach(string clipData in clipArray)
        {
            string[] tokens = clipData.Split('#');
            int startFrame = int.Parse(tokens[0]);
            int durationFrame = int.Parse(tokens[1]);
            War.Battle.TrackBindingStamp stamp = (War.Battle.TrackBindingStamp)int.Parse(tokens[2]);
            uint fxID = uint.Parse(tokens[3]);
            string fxPath = vfx[fxID];

            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/" + fxPath);
            if (prefab.GetComponent<ParticleSystem>() != null)
            {
                ParticleSpawnTrack track = context.GetTrackAsset<ParticleSpawnTrack>(stamp);
                TimelineClip clip = track.CreateDefaultClip();
                ParticleSpawnClip clipAsset = clip.asset as ParticleSpawnClip;
                clip.start = context.FrameToTime(startFrame);
                clip.duration = context.FrameToTime(durationFrame);
                clipAsset.template.prefab = prefab;
                track.bindingStamp = stamp;
            }
        }
    }

    private void CompileVisualFxFlyingTrack(SkillConext context, string clips)
    {
        string[] clipArray = clips.Split(';');
        foreach (string clipData in clipArray)
        {
            string[] tokens = clipData.Split('#');
            int startFrame = int.Parse(tokens[0]);
            int durationFrame = int.Parse(tokens[1]);
            War.Battle.TrackBindingStamp stamp = (War.Battle.TrackBindingStamp)int.Parse(tokens[2]);
            uint fxID = uint.Parse(tokens[3]);
            string fxPath = vfx[fxID];

            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/" + fxPath);
            ParticleSpawnTrack track = context.GetTrackAsset<ParticleSpawnTrack>();
            TimelineClip clip = track.CreateDefaultClip();
            ParticleSpawnClip clipAsset = clip.asset as ParticleSpawnClip;
            clip.start = context.FrameToTime(startFrame);
            clip.duration = context.FrameToTime(durationFrame);
            clipAsset.template.prefab = prefab;
        }
    }

    private void CompileCastSoundFxTrack(SkillConext context, string clips)
    {

    }

    private void CompileEventTrack(SkillConext context, string clips)
    {
        string[] clipArray = clips.Split(';');
        foreach (string clipData in clipArray)
        {
            string[] tokens = clips.Split('#');
            int startFrame = int.Parse(tokens[0]);
            int durationFrame = int.Parse(tokens[1]);
            string eventName = tokens.Length == 3 ? tokens[2] : "OnHit";

            EventTrack track = context.GetTrackAsset<EventTrack>();
            TimelineClip clip = track.CreateDefaultClip();
            EventClip clipAsset = clip.asset as EventClip;
            clip.start = context.FrameToTime(startFrame);
            clip.duration = context.FrameToTime(durationFrame);
            clipAsset.template.eventName = eventName;
        }
    }

    private void CompileFlowTipsTweenTrack(SkillConext context, string clips)
    {
        //string[] clipArray = clips.Split(';');
        //foreach (string clipData in clipArray)
        //{
        //    string[] tokens = clips.Split('#');
        //    int startFrame = int.Parse(tokens[0]);
        //    int durationFrame = int.Parse(tokens[1]);
        //    string config = tokens[2];

        //    FlowTipsTweenTrack track = context.GetTrackAsset<FlowTipsTweenTrack>();
        //    TimelineClip clip = track.CreateDefaultClip();
        //    FlowTipsTweenClip clipAsset = clip.asset as FlowTipsTweenClip;
        //    clip.start = context.FrameToTime(startFrame);
        //    clip.duration = context.FrameToTime(durationFrame);
        //    clipAsset.tween_config = config;
        //    clipAsset.ApplyConfig();
        //}
    }

    public void Save(TimelineAsset timeline, string path, string userdata)
    {
        string assetpath = "assets/" + path;
        TimelineAsset asset = AssetDatabase.LoadAssetAtPath<TimelineAsset>(assetpath);
        if (asset)
        {
            EditorUtility.CopySerialized(timeline, asset);
            EditorUtility.SetDirty(asset);
        }
        else
        {
            AssetDatabase.CreateAsset(timeline, assetpath);
            AssetImporter importer = AssetImporter.GetAtPath(assetpath);
            importer.assetBundleName = path;
            importer.userData = userdata;
        }
    }


}
