using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.IMGUI.Controls;

public class BoxBoundHandleEditor
{
    public readonly BoxBoundsHandle m_BoundsHandle;

    private bool m_IsEditing = false;

    public static Color s_handleColor = new Color(145f / 255f, 244f / 255f, 139f / 255f, 210f / 255f);
    public static Color s_handleColorDisabled = new Color(84f / 255f, 200f / 255f, 77f / 255f, 140f / 255f);


    public delegate void SynceProperties(BoxBoundsHandle boxbounding);

    public SynceProperties CopyColliderPropertiesToHandle;
    public SynceProperties CopyHandlePropertiesToCollider;

    public BoxBoundHandleEditor()
    {
        m_BoundsHandle = new BoxBoundsHandle();
    }

    public void DrawInspectorGUI(string label)
    {
        GUIStyle style = new GUIStyle("Button");
        EditorGUI.BeginChangeCheck();
        m_IsEditing = ToggleButton(m_IsEditing, label, style);
        if (EditorGUI.EndChangeCheck())
        {
            SceneView.RepaintAll();
        }
    }

    private bool ToggleButton(bool toggle, string label, GUIStyle style)
    {
        Rect rect = EditorGUILayout.GetControlRect(true, 23f, style);
        Rect position = new Rect(rect.xMin + EditorGUIUtility.labelWidth, rect.yMin, 33f, 23f);
        GUIContent content = new GUIContent(label);
        Vector2 vector = GUI.skin.label.CalcSize(content);
        Rect labelRect = new Rect(position.xMax + 5f, rect.yMin + ((rect.height - vector.y) * 0.5f), vector.x, rect.height);

        bool t = GUI.Toggle(position, toggle, EditorGUIUtility.IconContent("EditCollider"), style);
        GUI.Label(labelRect, label);
        return t;
    }


    public void DrawSceneGUI(Transform transform, float range = 0)
    {
        if (m_IsEditing)
        {
            if (!Mathf.Approximately(transform.lossyScale.sqrMagnitude, 0f))
            {
                using (new Handles.DrawingScope(Matrix4x4.TRS(transform.position, transform.rotation, Vector3.one)))
                {
                    CopyColliderPropertiesToHandle(m_BoundsHandle);
                    m_BoundsHandle.SetColor(s_handleColor);
                    EditorGUI.BeginChangeCheck();
                    m_BoundsHandle.DrawHandle();
                    if (EditorGUI.EndChangeCheck())
                    {
                        this.CopyHandlePropertiesToCollider(m_BoundsHandle);
                    }

                    //Color c = Handles.color;
                    //Handles.color = !target.enabled ? s_handleColorDisabled : s_handleColor;
                    //Vector3 pos = target.anchor;
                    //EditorGUI.BeginChangeCheck();
                    //pos = Handles.FreeMoveHandle(pos, Quaternion.identity, HandleUtility.GetHandleSize(pos) * 0.1f, Vector3.zero, Handles.CubeHandleCap);
                    //Handles.color = c;
                    //if (EditorGUI.EndChangeCheck())
                    //{
                    //    Undo.RecordObject(target, string.Format("Modify {0}", ObjectNames.NicifyVariableName(base.target.GetType().Name)));
                    //    target.anchor = pos;
                    //}

                    if (range > 0)
                    {
                        Handles.color = Color.red;
                        Vector3 pos = transform.position;
                        pos.y += 1;
                        Handles.DrawWireDisc(pos, Vector3.up, range);
                    }
                }
            }
        }
    }

    public static Vector3 InvertScaleVector(Vector3 scaleVector)
    {
        for (int i = 0; i < 3; i++)
        {
            scaleVector[i] = (scaleVector[i] != 0f) ? (1f / scaleVector[i]) : 0f;
        }
        return scaleVector;
    }

    public static Vector3 TransformColliderCenterToHandleSpace(Transform colliderTransform, Vector3 colliderCenter)
    {
        return ((Vector3)(Handles.inverseMatrix * (colliderTransform.localToWorldMatrix * colliderCenter)));
    }

    public static Vector3 TransformHandleCenterToColliderSpace(Transform colliderTransform, Vector3 handleCenter)
    {
        return ((Vector3)(colliderTransform.localToWorldMatrix.inverse * (Handles.matrix * handleCenter)));
    }
}
