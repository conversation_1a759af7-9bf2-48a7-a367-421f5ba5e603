using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEditor;

[CustomEditor(typeof(FlashTrack), true), CanEditMultipleObjects]
internal class FlashTrackEditor : Editor
{
    // Fields
    private SerializedProperty bindingStamp;
    private SerializedProperty segment;


    public void OnEnable()
    {
        this.bindingStamp = serializedObject.FindProperty("bindingStamp");
        this.segment = serializedObject.FindProperty("segment");
    }

    public override void OnInspectorGUI()
    {
        //base.OnInspectorGUI();
        base.serializedObject.Update();

        EditorGUILayout.PropertyField(this.bindingStamp);
        EditorGUILayout.PropertyField(this.segment);

        serializedObject.ApplyModifiedProperties();
    }
}
