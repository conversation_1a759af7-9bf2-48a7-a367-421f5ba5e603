using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using ICSharpCode.SharpZipLib;
using ICSharpCode.SharpZipLib.Zip;
using ICSharpCode.SharpZipLib.Checksums;
using System.Diagnostics;

namespace CLeopardZip
{
	/// <summary> 
	/// 适用与ZIP压缩 
	/// </summary> 
	public class ZipHelper
	{

		//TargetFile是目标zip文件，fileDir是解压存放的目录
		public static string UnZipFile(string TargetFile, string fileDir)
		{
			TargetFile = Path.GetFullPath(TargetFile);
			fileDir = Path.GetFullPath(fileDir);

			string split = Path.DirectorySeparatorChar.ToString();
			string rootFile = "";
			try
			{
				ZipInputStream s = new ZipInputStream(File.OpenRead(TargetFile.Trim()));
				ZipEntry theEntry;
				string path = fileDir;
				string rootDir = " ";
				while ((theEntry = s.GetNextEntry()) != null)
				{
					if(theEntry.Name.Contains("move.bat"))
					{
						Print(theEntry.Name);
					}
					rootDir = Path.GetDirectoryName(theEntry.Name);
					if (rootDir.IndexOf(split) >= 0)
					{
						rootDir = rootDir.Substring(0, rootDir.IndexOf(split) + 1);
					}
					string dir = Path.GetDirectoryName(theEntry.Name);
					string fileName = Path.GetFileName(theEntry.Name);
					if (dir != " ")
						//创建根目录下的子文件夹,不限制级别
					{
						if (!Directory.Exists(fileDir + split + dir))
						{
							path = fileDir + split + dir;
							//在指定的路径创建文件夹
							Directory.CreateDirectory(path);
						}
					}
					else if (dir == " " && fileName != "")
						//根目录下的文件
					{
						path = fileDir;
						rootFile = fileName;
					}
					else if (dir != " " && fileName != "")
						//根目录下的第一级子文件夹下的文件
					{
						if (dir.IndexOf(split) > 0)
							//指定文件保存的路径
						{
							path = fileDir + split + dir;
						}
					}
					if (dir == rootDir)
						//判断是不是需要保存在根目录下的文件
					{
						path = fileDir + split + rootDir;
					}
					//以下为解压缩zip文件的基本步骤
					//基本思路就是遍历压缩文件里的所有文件，创建一个相同的文件。
					if (fileName != String.Empty)
					{
						var filepath = path + split + fileName;
						if(fileName.Contains("move.bat"))
						Print(filepath);
						FileStream streamWriter = File.Create (filepath);
						int size = 2048;
						byte[] data = new byte[2048];
						while (true)
						{
							size = s.Read(data, 0, data.Length);
							if (size > 0)
							{
								streamWriter.Write(data, 0, size);
							}
							else
							{
								break;
							}
						}
						streamWriter.Close();
					}
				}
				s.Close();
				return rootFile;
			}
			catch (Exception ex)
			{
				return "1; " + ex.Message;
			}
		}

		public static void ZipFile(string strFile, string strZip,string staticfile)
		{
			if (strFile[strFile.Length - 1] != Path.DirectorySeparatorChar)
				strFile += Path.DirectorySeparatorChar;
			ZipOutputStream s = new ZipOutputStream(File.Create(strZip));
			strFile = Path.GetFullPath (strFile);
			staticfile = Path.GetFullPath (staticfile);
			s.SetLevel(9); // 0 - store only to 9 - means best compression
			try { 
				zip(strFile, s, staticfile);
			} finally {
				s.Finish();
				s.Close();
			}
		}


		private static void zip(string strFile, ZipOutputStream s, string staticFile)
		{
			string split = Path.DirectorySeparatorChar.ToString();
			if (strFile[strFile.Length - 1] != Path.DirectorySeparatorChar)
				strFile += Path.DirectorySeparatorChar;
			ICSharpCode.SharpZipLib.Checksums.Crc32 crc = new ICSharpCode.SharpZipLib.Checksums.Crc32();
			string[] filenames = Directory.GetFileSystemEntries(strFile);
			foreach (string file in filenames)
			{
				if (Directory.Exists(file))
				{
					zip(file, s, staticFile);
				}

				else // 否则直接压缩文件
				{
					//打开压缩文件
					FileStream fs = File.OpenRead(file);

					byte[] buffer = new byte[fs.Length];
					fs.Read(buffer, 0, buffer.Length); 
					string tempfile = file.Substring(staticFile.LastIndexOf (split) + 1);
					ZipEntry entry = new ZipEntry(tempfile);

					entry.DateTime = DateTime.Now;
					entry.Size = fs.Length;
					fs.Close();
					crc.Reset();
					crc.Update(buffer);
					entry.Crc = crc.Value;
					s.PutNextEntry(entry);

					s.Write(buffer, 0, buffer.Length);
				}
			}
		} 
		static void Print (string s)
		{
			UnityEngine.Debug.LogError(s);
		}
	}
}