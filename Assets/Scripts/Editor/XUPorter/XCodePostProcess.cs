//#define UNITY_IOS
//#define UNITY_EDITOR_OSX
//#undef UNITY_ANDROID

using UnityEngine;
using System.Collections.Generic;
using System;
#if UNITY_IOS
using UnityEditor.iOS.Xcode;
using UnityEditor.iOS.Xcode.Extensions;
#endif
#if UNITY_EDITOR
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEditor.XCodeEditor;
using System.Xml;
#endif
using System.IO;
using editor.tools.asset;

public partial class XClass : System.IDisposable
{
    private string filePath;

    public XClass(string fPath)
    {
        filePath = fPath;
        if (!System.IO.File.Exists(filePath))
        {
            Debug.LogError(filePath + "路径下文件不存在");
            return;
        }
    }

    public void WriteBelow(string below, string text)
    {
        StreamReader streamReader = new StreamReader(filePath);
        string text_all = streamReader.ReadToEnd();
        streamReader.Close();

        int beginIndex = text_all.IndexOf(below);
        if (beginIndex == -1)
        {
            Debug.LogError(filePath + "中没有找到标志" + below);
            return;
        }

        int endIndex = text_all.LastIndexOf("\n", beginIndex + below.Length);

        text_all = text_all.Substring(0, endIndex) + "\n" + text + "\n" + text_all.Substring(endIndex);

        StreamWriter streamWriter = new StreamWriter(filePath);
        streamWriter.Write(text_all);
        streamWriter.Close();
    }

    public void Replace(string below, string newText)
    {
        StreamReader streamReader = new StreamReader(filePath);
        string text_all = streamReader.ReadToEnd();
        streamReader.Close();

        int beginIndex = text_all.IndexOf(below);
        if (beginIndex == -1)
        {
            Debug.LogError(filePath + "中没有找到标志" + below);
            return;
        }

        text_all = text_all.Replace(below, newText);
        StreamWriter streamWriter = new StreamWriter(filePath);
        streamWriter.Write(text_all);
        streamWriter.Close();

    }

    public void Dispose()
    {

    }
}

public static class XCodePostProcess
{
    static readonly string editor_build_config = "../../Tools/GameConfig/custom/custom_editor_build_config.json";
    public static bool IsEnableSwitch(Dictionary<string, object> configDic, string key)
    {
        object value;
        if (!configDic.TryGetValue(key, out value))
        {
            Debug.LogWarning("Do not find " + key);
            return false;
        }
        var boolValue = Convert.ToBoolean(value);
        Debug.LogWarning("Editor Build Config, " + key + " is " + boolValue);
        return boolValue;
    }

    /// <summary>
    /// 获取xcode版本，jenkins.sh中会获取xcode版本并保存在项目路径./Client/xcodeVersion.txt中
    /// </summary>
    /// <returns></returns>
    public static float GetXcodeVersion()
    {
        string cfgPath = Application.dataPath + "/../xcodeVersion.txt";
        if (File.Exists(cfgPath))
        {
            var cfgText = File.ReadAllText(cfgPath);
            if(!string.IsNullOrEmpty(cfgText))
            {
                Debug.Log("versionStr = " + cfgText);
                if (float.TryParse(cfgText, out float xcodeVersion))
                {
                    Debug.Log("versionStr to float:" + xcodeVersion);
                    return xcodeVersion;
                }
            }
        }

        return 0.0f;
    }

#if UNITY_EDITOR
    [PostProcessBuild(45)]
    public static void OnPostProcessGenPodfile(BuildTarget buildTarget, string pathToBuiltProject)
    {
        Debug.LogWarning("XCodePostProcess OnPostProcessGenPodfile() target:" + buildTarget + ",path to built project:" + pathToBuiltProject);
        if (buildTarget != BuildTarget.iOS)
        {
            Debug.LogWarning("Target is not iPhone. XCodePostProcess will not run");
            return;
        }
        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
        var channelTag = gameConfig["CHANNEL_TAG"];
        if (IsEnableSwitch(gameConfig, "ENABLE_Q1SDK") || IsEnableSwitch(gameConfig, "ENABLE_ADMOB") || IsEnableSwitch(gameConfig, "ENABLE_FIREBASE"))
        {
            string podfilePath = pathToBuiltProject + "/Podfile";
            if(File.Exists(podfilePath))
            {
                string allTextStr = File.ReadAllText(podfilePath);
                string firebase_email_str = "use_modular_headers!";//firebase pod版本10.3.0安装必须要在podfile中添加这条
                string sdkPodUrl = "source 'https://gitee.com/q1com/glaspecmanager.git'";
                if(allTextStr.Contains(sdkPodUrl))
                {
                    Debug.Log("sdk pod路径包含在Podfile文件中 ");
                    allTextStr = allTextStr.Replace(sdkPodUrl,firebase_email_str);
                    StreamWriter sw = new StreamWriter(podfilePath);
                    sw.WriteLine(sdkPodUrl);
                    sw.Flush();
                    sw.Close();
                    sw.Dispose();
                    File.AppendAllText(podfilePath,allTextStr);

                    string code ="post_install do |installer|\n    installer.pods_project.targets.each do |target|\n        target.build_configurations.each do |config|\n            if target.respond_to?(:product_type) and target.product_type == 'com.apple.product-type.bundle'\n                target.build_configurations.each do |config|\n                    config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'\n                end\n            end\n        end\n    end\nend";
                    File.AppendAllText(podfilePath, code);
                }
                else
                {
                    Debug.LogError("sdk pod路径 不 在Podfile文件中，需要检查代码中url，修改一致 ");
                }
            }
            else
            {
                Debug.Log("podfile not exist!!  path = " + podfilePath);
            }
        }
    }

    [PostProcessBuild(999)]
    public static void OnPostProcessBuild(BuildTarget target, string pathToBuiltProject)
    {
        Debug.LogWarning("XCodePostProcess target:" + target + ",path to built project:" + pathToBuiltProject);
        if (target != BuildTarget.iOS)
        {
            Debug.LogWarning("Target is not iPhone. XCodePostProcess will not run");
            return;
        }

        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);

        if (IsEnableSwitch(gameConfig, "ENABLE_Q1SDK") || IsEnableSwitch(gameConfig, "ENABLE_ADMOB") || IsEnableSwitch(gameConfig, "ENABLE_FIREBASE"))
        {
            //调用pod install
#if UNITY_STANDALONE_OSX || UNITY_EDITOR_OSX
            AutoCopySymbolsPostprocessor.ProcessCommand(Application.dataPath + "/../pod.sh", pathToBuiltProject);
            Debug.Log("pod.sh execute");
#endif
        }

        // Create a new project object from build target
        XCProject project = new XCProject(pathToBuiltProject);

        Debug.LogWarning("Find *.projmods in :" + Application.dataPath);
        // Find and run through all projmods files to patch the project.
        // Please pay attention that ALL projmods files in your project folder will be excuted!
        string[] files = Directory.GetFiles(Application.dataPath, "*.projmods", SearchOption.AllDirectories);
        foreach (string file in files)
        {
            UnityEngine.Debug.Log("ProjMod File: " + file);
            //加上异常捕获，Lunar.projmods目前处理有问题，方便类似问题定位
            try
            {
                project.ApplyMod(file);
            }
            catch (System.Exception e)
            {
                Debug.LogError(e);
            }
        }

        //TODO implement generic settings as a module option
        //project.overwriteBuildSetting("CODE_SIGN_IDENTITY[sdk=iphoneos*]", "iPhone Distribution", "Release");
        if (!project.overwriteBuildSetting("CODE_SIGNING_STYLE", "Manual"))
        {
            Debug.LogError("CODE_SIGNING_STYLE change failed");
        }
        if (!project.overwriteBuildSetting("ENABLE_BITCODE", "NO"))
        {
            Debug.LogError("ENABLE_BITCODE change failed");
        }
        if (!project.overwriteBuildSetting("ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES", "Yes"))
        {
            Debug.LogError("ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES change failed");
        }
        if (!project.overwriteBuildSetting("DEBUG_INFORMATION_FORMAT", "dwarf-with-dsym", "Release"))
        {
            Debug.LogError("DEBUG_INFORMATION_FORMAT Release change failed");
        }
        if (!project.overwriteBuildSetting("DEBUG_INFORMATION_FORMAT", "dwarf-with-dsym", "ReleaseForProfiling"))
        {
            Debug.LogError("DEBUG_INFORMATION_FORMAT ReleaseForProfiling change failed");
        }
        if (!project.overwriteBuildSetting("DEBUG_INFORMATION_FORMAT", "dwarf-with-dsym", "ReleaseForRunning"))
        {
            Debug.LogError("DEBUG_INFORMATION_FORMAT ReleaseForRunning change failed");
        }
        if (!project.overwriteBuildSetting("DEBUG_INFORMATION_FORMAT", "dwarf", "Debug"))
        {
            Debug.LogError("DEBUG_INFORMATION_FORMAT Debug change failed");
        }
		if (!project.overwriteBuildSetting("GCC_ENABLE_OBJC_EXCEPTIONS", "YES"))
        {
            Debug.LogError("GCC_ENABLE_OBJC_EXCEPTIONS change failed");
        }

        if(GetXcodeVersion() >= 15.0f)
        {
            if (!project.AddOtherLinkerFlags("-ld64"))
            {
                Debug.LogError("xcode15 add -ld64 failed");
            }
        }
        else
        {
            Debug.LogWarning("Xcode version < 15.0  xcodeVersion:"+ GetXcodeVersion());
        }

#if UNITY_IOS
        // Google Mobile Ads Unity Plugin 升级到 v6.0.2 后，按官方文档及技术支持说明，必须勾选 "Link frameworks statically"， 即添加 :linkage => :static 到 Podfile
        // 此修改导致 Facebook 相关 sdk 导入失败，先引入 swift 文件
        if (IsEnableSwitch(gameConfig, "ENABLE_ADMOB"))
        {
            //复制 Swift 桥接文件
            var swift_path = "./sdk/Xcode/Swift";
            var swift_targetPath = pathToBuiltProject + "/";
            DirectoryCopy(swift_path, swift_targetPath);
            if (!project.overwriteBuildSetting("SWIFT_OBJC_BRIDGING_HEADER", "Unity-iPhone-Bridging-Header.h"))
            {
                Debug.LogError("SWIFT_OBJC_BRIDGING_HEADER failed");
            }
        }
#endif

        // Finally save the xcode project
        project.Save();
        project.Dispose();

#if UNITY_IOS
        if (IsEnableFirebase(gameConfig))
        {
            EditCodeForFirebase(pathToBuiltProject,gameConfig);

            MoveGoogleServicesInfo(pathToBuiltProject);
        }

        ModifyLunarConsole(pathToBuiltProject);
        ModifyLocalization(pathToBuiltProject);

        bool isQ1Debug;
        ModifyPlist(pathToBuiltProject, out isQ1Debug);

        string projPath = UnityEditor.iOS.Xcode.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.PBXProject proj = new UnityEditor.iOS.Xcode.PBXProject();
        proj.ReadFromString(File.ReadAllText(projPath));

        ModifySigningConfig(pathToBuiltProject, gameConfig);

        if (IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC")) 
        {
            // 同步位面1修改，q1sdk改为pod导入，fb framework不在plugins目录下
            ModifyEmbedFrameworks(proj, pathToBuiltProject, gameConfig);
        }

#if UNITY_2020_1_OR_NEWER
        var targetGuid16 = proj.GetUnityMainTargetGuid();
#else
        var targetGuid16 = proj.TargetGuidByName("Unity-iPhone");
#endif
        if(GetXcodeVersion() >= 16.0f)
        {
            proj.SetBuildProperty(targetGuid16, "OTHER_CFLAGS", "$(inherited)");//升级至xcode16.2后-mno-thumb报错，去掉-mno-thumb编译选项
        }
        else
        {
            Debug.LogWarning("Xcode version < 16.0  xcodeVersion:"+ GetXcodeVersion());
        }       

        proj.WriteToFile(projPath);
		
        if (IsEnableSwitch(gameConfig, "ENABLE_ADMOB"))
        {
            ModifySwift(proj, projPath, pathToBuiltProject);
        }
        
        //升级unity Adjust版本到4.34.1以后,如果不加这个库,会闪退
        if (IsEnableSwitch(gameConfig, "ENABLE_ADJUST"))
        {
            var targetGuid = proj.GetUnityFrameworkTargetGuid();
            string coreFrameworkName = "AdServices.framework"; // framework 的文件名
            proj.AddFrameworkToProject(targetGuid, coreFrameworkName, true);
        }
        
        //iOS接入AIHelp,需添加PhotosUI.framework,不加这个会闪退
        if (IsEnableSwitch(gameConfig, "ENABLE_AIHELP"))
        {
            var targetGuid = proj.GetUnityMainTargetGuid();
            string coreFrameworkName = "PhotosUI.framework"; 
            proj.AddFrameworkToProject(targetGuid, coreFrameworkName, false);
        }

        DeleteDisallowedFramework(proj, projPath);

        if(JenkinsEnv.Instance.GetBool("IsAddAppleCapability", !isQ1Debug))
        {
            if (!IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC"))
            {
                // iOS 不使用图片推送功能情况下，可以不配置 GLANotificationServices 相关内容，先屏蔽
                //ModifyNotificationCfg(proj, projPath, pathToBuiltProject, isQ1Debug);
                ModifyAppleCfg(projPath, isQ1Debug, gameConfig);
            }
        }
        else
        {
            //test证书没有添加in-app purchase,当添加StoreKit.framework至Unity-iPhone Target中时，会自动添加in-app purchase，
            //此处内网需要出ipa，添加in-app purchase会导致自动出ipa失败
            RemoveFrameworkToUnityIPhoneTarget(pathToBuiltProject, "StoreKit.framework");
        }

        ModifyBuildScriptPhase(pathToBuiltProject);

        //ModifyXCWorkspace(pathToBuiltProject, null);

        UpdateXcodeBuildSystem(pathToBuiltProject);

        ModifySupportSimulator(projPath, proj);


#if UNITY_2020_1_OR_NEWER
        ModifyMainAppReference(pathToBuiltProject);
#endif
        AutoAddUnityFrameworkToUnityIPhoneTarget(pathToBuiltProject);

        //PrivacyInfo.xcprivacy 2024.3.5根据苹果政策 需要添加隐私文件
        string privacyInfoPath = "./Assets/Plugins/iOS/PrivacyInfo.xcprivacy";
        AddFile2UnityiPhoneTarget(proj, projPath, pathToBuiltProject, privacyInfoPath, "PrivacyInfo.xcprivacy");

#if !(UNITY_STANDALONE_OSX || UNITY_EDITOR_OSX)
        //从window系统打出xcode，pbxproj文件内容有 \\ 符号，需转换成 / 否则xcode工程库依赖路径无法识别
        var pbxproj = Path.Combine(pathToBuiltProject, "Unity-iPhone.xcodeproj", "project.pbxproj");
        var pbxprojContent = File.ReadAllText(pbxproj);
        var newContent = pbxprojContent.Replace("\\\\", "/");
        File.WriteAllText(pbxproj, newContent);
#endif
#endif

    }

#if UNITY_IOS
    private static void ModifySupportSimulator(string projPath, UnityEditor.iOS.Xcode.PBXProject proj)
    {
        var isSimulator = JenkinsEnv.Instance.GetBool("IsSimulator", false);
        Debug.LogWarning($"IsSimulator:{isSimulator}");
        if (isSimulator)
        {
            // 不使用 XCProject -> AddOtherLinkerFlags，这将导致添加到所有 target 后编译报错
            var targetGuid = proj.GetUnityMainTargetGuid();
            proj.AddBuildProperty(targetGuid, "OTHER_LDFLAGS", "-pagezero_size 10000 -image_base 100000000");
            proj.WriteToFile(projPath);
        }
    }

    private static bool IsEnableFirebase(Dictionary<string, object> gameConfig)
    {
        var enableFirebaseCfg = gameConfig["ENABLE_FIREBASE"];
        if (enableFirebaseCfg == null)
        {
            //未配置使用 Firebase
            Debug.LogWarning("Do not use Firebase");
            return false;
        }
        var enableFirebaseValue = Convert.ToBoolean(enableFirebaseCfg);
        if (!enableFirebaseValue)
        {
            Debug.LogWarning("Do not use Firebase, ENABLE_FIREBASE is false");
            return false;
        }
        return true;
    }

    private static void EditCodeForFirebase(string filePath,Dictionary<string, object> gameConfig)
    {
		//读取UnityAppController.mm文件
        XClass UnityAppController = new XClass(filePath + "/Classes/UnityAppController.mm");
 
        //添加Firebase.h引用
        UnityAppController.WriteBelow("#include <sys/sysctl.h>","#include <Firebase.h>");
 
        //初始化Firebase
        UnityAppController.WriteBelow("[KeyboardDelegate Initialize];","[FIRApp configure];");
 
    }

    static void ModifyMainAppReference(string pathToBuiltProject)
    {
        //main.mm中找不到UnityFramework/UnityFramework.h的问题
		var mainAppPath = Path.Combine(pathToBuiltProject, "MainApp", "main.mm");
		var mainContent = File.ReadAllText(mainAppPath);
		var newContent = mainContent.Replace("#include <UnityFramework/UnityFramework.h>", @"#include ""../UnityFramework/UnityFramework.h""");
		File.WriteAllText(mainAppPath, newContent);
    }

    static void ModifySigningConfig(string pathToBuiltProject, Dictionary<string, object> gameConfig)
    {
        if (IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC"))
        {
            string projPath = UnityEditor.iOS.Xcode.Custom.PBXProject.GetPBXProjectPath(pathToBuiltProject);

            if(!File.Exists(projPath))
            {
                Debug.Log(projPath + " not exists!");
                return;
            }

            UnityEditor.iOS.Xcode.Custom.PBXProject pBXProject = new UnityEditor.iOS.Xcode.Custom.PBXProject();
            pBXProject.ReadFromString(File.ReadAllText(projPath));

            string target = pBXProject.TargetGuidByName("Unity-iPhone Tests");
            Debug.Log("ModifySigningConfig target: " + target + " projPath:" + projPath + " time:" + System.DateTime.Now);

            //pBXProject.SetTargetAttributes("ProvisioningStyle", "Manual");
            //pBXProject.SetBuildProperty(target, "PROVISIONING_PROFILE_SPECIFIER", "wmzz2_DEV20210907");

            pBXProject.SetTeamId(target, "VUYA94FSAK");
            Debug.Log("ModifySigningConfig SetTeamId: " + target + " to VUYA94FSAK");

            pBXProject.WriteToFile(projPath);
        }
        else
        {

        }
    }

    static void ModifyEmbedFrameworks(UnityEditor.iOS.Xcode.PBXProject proj, string pathToBuiltProject, Dictionary<string, object> gameConfig)
    {

        //EmbedFrameworks
#if UNITY_2020_1_OR_NEWER
        var targetGuid = proj.GetUnityFrameworkTargetGuid();
#else
        var targetGuid = proj.TargetGuidByName("Unity-iPhone");
#endif
        const string defaultLocationInProj = "Plugins/iOS/Q1SDK";//framework 存放的路径
        List<string> frameworkNameSet = new List<string>()
        {
            //"FBSDKCoreKit.framework",
            "FBSDKLoginKit.framework",
            "FBSDKShareKit.framework"
            //"AdServices.framework"
        };

        //国内版不需要fb
        if(IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC"))
        {
            frameworkNameSet.Clear();
        }
        // Untiy 方式不需要主动添加 AdjustSigSdk.framework
        //if(IsEnableAdjustSig(pathToBuiltProject))
        //{
        //    Debug.LogWarning("Enable Adjust Signature");
        //    frameworkNameSet.Add("AdjustSigSdk.framework");
        //}
        //else
        //{
        //    Debug.LogWarning("Disable Adjust Signature");
        //}
        foreach (var frameworkName in frameworkNameSet)
        {
            string coreFrameworkName = frameworkName; // framework 的文件名
            string framework = Path.Combine(defaultLocationInProj, coreFrameworkName);
            string fileGuid = proj.AddFile(framework, "Frameworks/" + framework, PBXSourceTree.Sdk);
            PBXProjectExtensions.AddFileToEmbedFrameworks(proj, targetGuid, fileGuid);
        }
        proj.SetBuildProperty(targetGuid, "LD_RUNPATH_SEARCH_PATHS", "$(inherited) @executable_path/Frameworks");//如果没有这句话，运行会崩溃，报 image not found 错误
                                                                                                                 //EmbedFrameworks end
    }

    /// <summary>
    /// 添加本地化多语言配置
    /// </summary>
    /// <param name="pathToBuiltProject"></param>
    static void ModifyLocalization(string pathToBuiltProject)
    {
        //复制本地化语言
        var localizationPath = "./sdk/Xcode/Localization";
        var targetPath = pathToBuiltProject + "/Localization";
        DirectoryCopy(localizationPath, targetPath);

        Debug.Log("localization projectPath:" + pathToBuiltProject + ",localization files path:" + targetPath);
        //NativeLocale.AddLocalizedStringsIOS(pathToBuiltProject, targetPath);
    }

    static public void AddDirectoryGroup(UnityEditor.iOS.Xcode.PBXProject proj, string targetGuid, string sourceDirectory, string targetDirectory)
    {
        try
        {
            DirectoryInfo dir = new DirectoryInfo(sourceDirectory);
            //获取目录下（不包含子目录）的文件和子目录
            FileSystemInfo[] fileinfo = dir.GetFileSystemInfos();
            foreach (FileSystemInfo i in fileinfo)
            {
                //判断是否文件夹
                if (i is DirectoryInfo)
                {
                    //递归调用添加子文件夹
                    AddDirectoryGroup(proj, targetGuid, i.FullName, targetDirectory); // targetDirectory + "/" + i.Name
                }
                else
                {
                    //不是文件夹即添加到工程
                    //Debug.LogWarning("add file reference:" + i.FullName + " to " + (targetDirectory + "/" + i.Name));
                    proj.AddFileToBuild(targetGuid, proj.AddFile(i.FullName, targetDirectory + "/" + i.Name));
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("添加文件异常:" + ex.Message);
        }
    }

    static public List<string> AddDirectoryGroup(XCProject proj, PBXGroup modGroup, string sourceDirectory, bool createBuildFile = true, Dictionary<string, bool> excludeTargetDict = null)
    {
        List<string> files = new List<string>();
        foreach (string file in Directory.GetFiles(sourceDirectory, "*.*", SearchOption.AllDirectories))
        {
            string absoluteFilePath = file;
            proj.AddFile(absoluteFilePath, modGroup, "SOURCE_ROOT", createBuildFile, false, excludeTargetDict);
            files.Add(absoluteFilePath);
        }
        return files;
    }

    static void ModifyLunarConsole(string pathToBuiltProject)
    {
        var lunarConsoleGroupRoot = "LunarConsole";

        //移除 LunarConsole group 引用,移除失败...
        //var fileGuid = proj.FindFileGuidByProjectPath(lunarConsoleGroupRoot);
        //Debug.Log(lunarConsoleGroupRoot + " fileGuid:" + fileGuid);
        //if (!string.IsNullOrEmpty(fileGuid))
        //{
        //    proj.RemoveFileFromBuild(targetGuid, fileGuid);
        //    proj.RemoveFile(fileGuid);
        //}

        //复制 LunarConsole 文件
        var lunarConsolePath = "./sdk/" + lunarConsoleGroupRoot + "/Editor/iOS/";
        var targetPath = pathToBuiltProject + "/" + lunarConsoleGroupRoot;
        DirectoryCopy(lunarConsolePath, targetPath);
        Debug.Log("copy " + lunarConsolePath + " to " + targetPath);

        XCProject project = new XCProject(pathToBuiltProject);

        Dictionary<string, bool> excludeTargetDict = null;
#if UNITY_2020_1_OR_NEWER
        //unity2019开始，不再由unity-iphone调用lunarConsole，改由unityFramework调用，去掉unity-iphone的引用
        string projPath = UnityEditor.iOS.Xcode.Custom.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.Custom.PBXProject pBXProject = new UnityEditor.iOS.Xcode.Custom.PBXProject();
        pBXProject.ReadFromString(File.ReadAllText(projPath));
        string excludeTarget = pBXProject.TargetGuidByName("Unity-iPhone");
        string excludeBuildPhase = pBXProject.GetSourcesBuildPhaseByTarget(excludeTarget);
        excludeTargetDict = new Dictionary<string, bool>();
        excludeTargetDict.Add(excludeBuildPhase, true);
#endif

        //将文件添加到工程
        PBXGroup modGroup = project.GetGroup("Lunar Console");
        AddDirectoryGroup(project, modGroup, targetPath, true, excludeTargetDict);

        // Finally save the xcode project
        project.Save();
        project.Dispose();
    }

    static void MoveGoogleServicesInfo(string pathToBuiltProject)
    {
		//复制GoogleService-Info.plist到xcode工程根目录下
        string infoPath = "./Assets/Plugins/iOS/Firebase/GoogleService-Info.plist";
        string targetPath = pathToBuiltProject + "/GoogleService-Info.plist";
        if (File.Exists(infoPath))
        {
            File.Copy(infoPath, targetPath, true);
        } 
    }

    //add script build phase
    // 2017.4.30 UnityEditor.iOS.Xcode.PBXProject 类中没有 AppendShellScriptBuildPhase || AddShellScriptBuildPhase 等接口，引入 XCodeAPI 库
    static void ModifyBuildScriptPhase(string pathToBuiltProject)
    {
        string projPath = UnityEditor.iOS.Xcode.Custom.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.Custom.PBXProject pBXProject = new UnityEditor.iOS.Xcode.Custom.PBXProject();
        pBXProject.ReadFromString(File.ReadAllText(projPath));

        ModifyBuglyBuildScriptPhase(pBXProject);

        pBXProject.WriteToFile(projPath);
    }

    static void GetBuglyInfo(bool bQ1Debug, Dictionary<string, object> gameConfig, out string appId, out string appKey, out string bundleId)
    {
        bundleId = GameConfig.Instance().TryGetString("CHANNEL_TAG", "com.q1.xhero");

        var buglyChannelAsset = Resources.Load<com.bugly.sdk.BuglyChannelAsset>("bugly_config");
        var buglyCfg = buglyChannelAsset.GetBuglyChannelCfg(bundleId);
        if(buglyCfg == null)
        {
            Debug.LogError($"未能找到 bugly  配置:{bundleId}");
            EditorApplication.Exit(1);
        }

        if (bQ1Debug)
        {
            appId = buglyCfg.debugAppID;
            appKey = buglyCfg.debugAppKey;
        }
        else
        {
            appId = buglyCfg.appID;
            appKey = buglyCfg.appKey;
        }
        Debug.Log("Q1Debug:" + bQ1Debug);
        Debug.Log("bugly, appId:" + appId);
        Debug.Log("bugly, appKey:" + appKey);
    }

    static bool IsEnableBugly(Dictionary<string, object> gameConfig)
    {
        var enableBuglyCfg = gameConfig["ENABLE_BUGLY"];
        if (enableBuglyCfg == null)
        {
            //未配置使用 Bugly
            Debug.LogWarning("Do not use Bugly");
            return false;
        }
        var enableBuglyValue = Convert.ToBoolean(enableBuglyCfg);
        if (!enableBuglyValue)
        {
            Debug.LogWarning("Do not use Bugly, ENABLE_BUGLY is false");
            return false;
        }
        return true;
    }

    static void ModifyBuglyBuildScriptPhase(UnityEditor.iOS.Xcode.Custom.PBXProject pBXProject)
    {
        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
        if(!IsEnableBugly(gameConfig))
        {
            Debug.LogWarning("Disable Bugly run script phasse");
            return;
        }

        //外网正式配置
        string appId;
        string appKey;
        string bundleId;
        bool q1DebugValue = false;

        var q1DebugCfg = gameConfig["ENABLE_Q1_DEBUG_MODE"];
        if (q1DebugCfg != null)
        {
            q1DebugValue = Convert.ToBoolean(q1DebugCfg);
        }
        GetBuglyInfo(q1DebugValue, gameConfig, out appId, out appKey, out bundleId);

        if (File.Exists(editor_build_config))
        {
            var strEditorBuildConfig = File.ReadAllText(editor_build_config);
            var editorBuildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strEditorBuildConfig);
            if (!War.Base.BuildScript.IsEnableSwitch(editorBuildConfig, "UPLOAD_BUGLY_SYMBOL"))
            {
                Debug.LogWarning("Disable Upload Bugly Symbol");
                return;
            }
        }

        string dsymShellPath = "./Assets/Plugins/Bugly/iOS/dSYMUpload.sh";
        string strDSYM = File.ReadAllText(dsymShellPath);
        strDSYM = strDSYM.Replace("BUGLY_APP_ID=\"YOUR_APP_ID\"", "BUGLY_APP_ID=\"" + appId + "\"")
            .Replace("BUGLY_APP_KEY=\"YOUR_APP_KEY\"", "BUGLY_APP_KEY=\"" + appKey + "\"")
            .Replace("BUNDLE_IDENTIFIER=\"YOUR_BUNDLE_IDENTIFIER\"", "BUNDLE_IDENTIFIER=\"" + bundleId + "\"");

        string targetGuid = pBXProject.TargetGuidByName("Unity-iPhone");
        pBXProject.AppendShellScriptBuildPhase(targetGuid, "Run Script upload bugly symbol set", "/bin/sh", strDSYM);
    }

    static void ModifyAppleCfg(string projPath, bool isQ1Debug,Dictionary<string, object> gameConfig)
    {
        //string targetGuid = proj.TargetGuidByName("Unity-iPhone");
        //proj.AddCapability(targetGuid, PBXCapabilityType.InAppPurchase);
        //proj.AddCapability(targetGuid, PBXCapabilityType.SignInWithApple);

        var capManager = new ProjectCapabilityManager(projPath, "xheroRelease.entitlements", "Unity-iPhone");
        ProjectCapabilityManagerExtension.AddSignInWithAppleWithCompatibility(capManager);

        //内购
        capManager.AddInAppPurchase();
        //推送
        capManager.AddPushNotifications(isQ1Debug);
        //BackgroundModes
        capManager.AddBackgroundModes(BackgroundModesOptions.RemoteNotifications);
        //深度链接
        // capManager.AddAssociatedDomains(new string[] { "applinks:d2v4nymyrwymp8.cloudfront.net" });
        capManager.WriteToFile();
    }

    static void AddBackgroundModes(PlistElementDict rootDic,Dictionary<string, object> gameConfig) 
    {
        var backgroundModesDic = rootDic["UIBackgroundModes"] as PlistElementArray;
        if (backgroundModesDic == null)
        {
            backgroundModesDic = rootDic.CreateArray("UIBackgroundModes");
        }

        var channelTag = (string)gameConfig["CHANNEL_TAG"];
        switch (channelTag)
        {
            case "com.tanwanhk.towerclimb":
                //贪玩韩国iOS
                backgroundModesDic.AddString("remote-notification");
                break;
            case "com.hh.titanthnew.ios":
                //贪玩越南iOS
                backgroundModesDic.AddString("remote-notification");
                break;
            default:
                break;
        }
    }

    /// <summary>
    /// 把一个文件夹下所有文件复制到另一个文件夹下 
    /// </summary>
    /// <param name="sourceDirectory">源目录</param>
    /// <param name="targetDirectory">目标目录</param>
    static public void DirectoryCopy(string sourceDirectory, string targetDirectory)
    {
        try
        {
            DirectoryInfo dir = new DirectoryInfo(sourceDirectory);
            //获取目录下（不包含子目录）的文件和子目录
            FileSystemInfo[] fileinfo = dir.GetFileSystemInfos();
            foreach (FileSystemInfo i in fileinfo)
            {
                if (i is DirectoryInfo)     //判断是否文件夹
                {
                    if (!Directory.Exists(targetDirectory + "/" + i.Name))
                    {
                        //目标目录下不存在此文件夹即创建子文件夹
                        Directory.CreateDirectory(targetDirectory + "/" + i.Name);
                    }
                    //递归调用复制子文件夹
                    DirectoryCopy(i.FullName, targetDirectory + "/" + i.Name);
                }
                else
                {
                    //不是文件夹即复制文件，true表示可以覆盖同名文件
                    File.Copy(i.FullName, targetDirectory + "/" + i.Name, true);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError("复制文件异常:" + ex.Message);
        }
    }

#if UNITY_2020_1_OR_NEWER
    static void AddExtensionFile(UnityEditor.iOS.Xcode.PBXProject pBXProject, string extensionGUID, string buildPhaseID, string relativeNotificationPath, string notificationTargetName, string fileName)
    {
        pBXProject.AddFileToBuildSection(extensionGUID, buildPhaseID,
            pBXProject.AddFile($"{relativeNotificationPath}/{fileName}", $"{notificationTargetName}/{fileName}"));
    }
#endif

    static void ModifyNotificationCfg(UnityEditor.iOS.Xcode.PBXProject pBXProject, string projPath, string pathToBuiltProject, bool isQ1Debug)
    {
        //先复制 APNs 配置文件
        var apnsPath = "./sdk/PushMsg/iOS/APNs/";
        DirectoryCopy(apnsPath, pathToBuiltProject);
        Debug.Log("copy " + apnsPath + " to " + pathToBuiltProject);

        var notificationTargetName = "GLANotificationService";

        var pathToNotificationService = Path.Combine(pathToBuiltProject, notificationTargetName);
        var notificationServicePlistPath = pathToNotificationService + "/Info.plist";

        if (!File.Exists(notificationServicePlistPath))
        {
            Debug.LogError(notificationServicePlistPath + " can not find");
            return;
        }

        PlistDocument notificationServicePlist = new PlistDocument();
        notificationServicePlist.ReadFromFile(notificationServicePlistPath);
        notificationServicePlist.root.SetString("CFBundleShortVersionString", PlayerSettings.bundleVersion);
        notificationServicePlist.root.SetString("CFBundleVersion", PlayerSettings.iOS.buildNumber.ToString());

#if UNITY_2020_1_OR_NEWER
        var targetGuid = pBXProject.GetUnityFrameworkTargetGuid();
#else
        var targetGuid = pBXProject.TargetGuidByName("Unity-iPhone");
#endif

        var notificationServiceTarget = PBXProjectExtensions.AddAppExtension(pBXProject, targetGuid, notificationTargetName,
            PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.iOS) + "." + notificationTargetName, notificationServicePlistPath);

        var relativeNotificationPath = "./" + notificationTargetName;
#if UNITY_2020_1_OR_NEWER
        var buildPhaseID = pBXProject.AddSourcesBuildPhase(targetGuid);
        AddExtensionFile(pBXProject, notificationServiceTarget, buildPhaseID, relativeNotificationPath, notificationTargetName, "NotificationService.h");
        AddExtensionFile(pBXProject, notificationServiceTarget, buildPhaseID, relativeNotificationPath, notificationTargetName, "NotificationService.m");
        AddExtensionFile(pBXProject, notificationServiceTarget, buildPhaseID, relativeNotificationPath, notificationTargetName, "Info.plist");
#else
        pBXProject.AddFileToBuild(notificationServiceTarget,
            pBXProject.AddFile(relativeNotificationPath + "/NotificationService.h", notificationTargetName + "/NotificationService.h"));
        pBXProject.AddFileToBuild(notificationServiceTarget,
            pBXProject.AddFile(relativeNotificationPath + "/NotificationService.m", notificationTargetName + "/NotificationService.m"));
        pBXProject.AddFileToBuild(notificationServiceTarget,
            pBXProject.AddFile(relativeNotificationPath + "/Info.plist", notificationTargetName + "/Info.plist"));
#endif

        pBXProject.AddFrameworkToProject(notificationServiceTarget, "NotificationCenter.framework", true);
        pBXProject.AddFrameworkToProject(notificationServiceTarget, "UserNotifications.framework", true);

        pBXProject.SetBuildProperty(notificationServiceTarget, "ARCHS", "$(ARCHS_STANDARD)");
        pBXProject.SetBuildProperty(notificationServiceTarget, "DEVELOPMENT_TEAM", PlayerSettings.iOS.appleDeveloperTeamID);
        pBXProject.SetBuildProperty(notificationServiceTarget, "IPHONEOS_DEPLOYMENT_TARGET", "11.0");
        pBXProject.SetBuildProperty(notificationServiceTarget, "TARGETED_DEVICE_FAMILY", "1,2");
        pBXProject.SetBuildProperty(notificationServiceTarget, "INFOPLIST_FILE", relativeNotificationPath + "/Info.plist");

        notificationServicePlist.WriteToFile(notificationServicePlistPath);
        pBXProject.WriteToFile(projPath);

    }

    static void ModifySwift(UnityEditor.iOS.Xcode.PBXProject pBXProject, string projPath, string pathToBuiltProject)
    {
        var relativeSwiftRootPath = "./";

#if UNITY_2020_1_OR_NEWER
        var targetGuid = pBXProject.GetUnityFrameworkTargetGuid();

        var buildPhaseID = pBXProject.AddSourcesBuildPhase(targetGuid);
        pBXProject.AddFileToBuildSection(targetGuid, buildPhaseID,
            pBXProject.AddFile(relativeSwiftRootPath + "/File.swift", "File.swift"));
        pBXProject.AddFileToBuildSection(targetGuid, buildPhaseID,
            pBXProject.AddFile(relativeSwiftRootPath + "/Unity-iPhone-Bridging-Header.h", "Unity-iPhone-Bridging-Header.h"));
#else
        var targetGuid = pBXProject.TargetGuidByName("Unity-iPhone");
        pBXProject.AddFileToBuild(targetGuid,
            pBXProject.AddFile(relativeSwiftRootPath + "/File.swift", "File.swift"));
        pBXProject.AddFileToBuild(targetGuid,
            pBXProject.AddFile(relativeSwiftRootPath + "/Unity-iPhone-Bridging-Header.h", "Unity-iPhone-Bridging-Header.h"));
#endif

        pBXProject.SetBuildProperty(targetGuid, "SWIFT_VERSION", "5.0");

        // pBXProject.SetBuildProperty(targetGuid,"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES","NO");
        // pBXProject.SetBuildProperty(pBXProject.GetUnityMainTargetGuid(),"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES","YES");

        pBXProject.WriteToFile(projPath);
    }

    //XCode上传testflight报错ITMS-90206，UnityFramework包含Framework问题的解决方案
    // ERROR ITMS-90206: "Invalid Bundle. The bundle at 'my.app/Frameworks/UnityFramework.framework' contains disallowed file 'Frameworks'."
    static void DeleteDisallowedFramework(UnityEditor.iOS.Xcode.PBXProject pBXProject, string projPath)
    {
        pBXProject.SetBuildProperty(pBXProject.GetUnityFrameworkTargetGuid(),"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES","NO");
        pBXProject.SetBuildProperty(pBXProject.GetUnityMainTargetGuid(),"ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES","YES");
        pBXProject.WriteToFile(projPath);
    }

    static void ModifyPlist(string pathToBuiltProject, out bool isQ1Debug)
    {
        string gameDisplayName = "X-Hero";
        bool bQ1Debug = false;
        int thinkingArea = 101;
        int regionId = 1; //默认欧美区

        TextAsset textAsset = Resources.Load<TextAsset>("game_config");
        var gameConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(textAsset.text);
        string channelId = Convert.ToString(gameConfig["CHANNEL_ID"]);
        Debug.LogWarning("Channel Id:" + channelId);
        string dis_channels_path = Application.dataPath + "/../dis_channels/" + channelId + ".config";
		
		string package_config_Path=Application.dataPath +"/../channel_res/"+gameConfig["CHANNEL_TAG"]+"/package_config.txt";
		if (File.Exists(package_config_Path))
			dis_channels_path=package_config_Path;
		
        if (File.Exists(dis_channels_path))
        {
            string[] lines = File.ReadAllLines(dis_channels_path);
            foreach (var line in lines)
            {
                if (string.IsNullOrEmpty(line))
                {
                    continue;
                }
                string[] key_pairSet = line.Split(':');
                //应用名可能有分号,特殊处理一下
                if (key_pairSet.Length != 2&& key_pairSet[0]!="name")
                {
                    continue;
                }
                Debug.LogWarning(key_pairSet[0] + ":" + key_pairSet[1]);
                switch (key_pairSet[0])
                {
                    case "name":
                        {
							if(key_pairSet.Length == 3)
							{
								gameDisplayName = key_pairSet[1]+":"+key_pairSet[2];
							}
							else
							{
					            gameDisplayName = key_pairSet[1];
							}
                        }
                        break;
                    case "Q1Debug":
                        {
                            int bDebug;
                            if (int.TryParse(key_pairSet[1], out bDebug))
                            {
                                bQ1Debug = bDebug == 1 ? true : false;
                                Debug.LogWarning("bQ1Debug:" + bQ1Debug);
                            }
                        }
                        break;
                    case "ThinkingArea":
                        {
                            int areaId;
                            if (int.TryParse(key_pairSet[1], out areaId))
                            {
                                thinkingArea = areaId;
                                Debug.LogWarning("thinkingArea Id:" + thinkingArea);
                            }
                        }
                        break;
                    case "REGION_ID":
                        {
                            //0.外网中国区 1.外网欧美区 2.外网亚太区
                            int _regionId;
                            if(int.TryParse(key_pairSet[1], out _regionId))
                            {
                                regionId = _regionId;
                            }
                            Debug.LogWarning("region Id:" + regionId);
                        }
                        break;
                }
            }
        }

        //优先使用 game_config 中的配置
        var q1DebugCfg = gameConfig["ENABLE_Q1_DEBUG_MODE"];
        if (q1DebugCfg != null)
        {
            Debug.LogWarning("ENABLE_Q1_DEBUG_MODE:" + q1DebugCfg);

            bQ1Debug = Convert.ToBoolean(q1DebugCfg);
            Debug.LogWarning("jenkins ENABLE_Q1_DEBUG_MODE:" + bQ1Debug);
        }
        var regionIdCfg = gameConfig["REGION_ID"];
        if(regionIdCfg != null)
        {
            Debug.LogWarning("REGION_ID:" + regionIdCfg);

            int regionIdValue = Convert.ToInt32(regionIdCfg);
            Debug.LogWarning("jenkins REGION_ID:" + regionIdValue);

            //regionId 不能为负值,否则使用原配置
            if(regionIdValue >= 0)
            {
                regionId = regionIdValue;
            }
            else
            {
                Debug.LogError("game_config REGION_ID:" + regionIdValue + ",can not be negative");
            }
        }

        isQ1Debug = bQ1Debug;

        var channelTag = gameConfig["CHANNEL_TAG"];

        //使用unity自带接口修改plist
        string plistPath = pathToBuiltProject + "/Info.plist";
        Debug.LogWarning("XCodePostProcess ModifyPlist path:" + plistPath);

        PlistDocument plist = new PlistDocument();
        plist.ReadFromFile(plistPath);

        PlistElementDict rootDic = plist.root;

        //关闭 ATS 机制，防止非HTTPS 协议失败
        PlistElementDict arbitraryLoad = rootDic.CreateDict("NSAppTransportSecurity");
        arbitraryLoad.SetBoolean("NSAllowsArbitraryLoads", true);

        //获取 idfa 权限描述
        rootDic.SetString("NSUserTrackingUsageDescription", "App need to use your ad-tracking permissions to track ads, This authorization will be used for delivering personalized advertisements");

        //出口合规信息
        rootDic.SetBoolean("ITSAppUsesNonExemptEncryption", false);

        if (IsEnableSwitch(gameConfig, "ENABLE_FACEBOOK"))
        {
            //Facebook及google配置
            ModifyFacebookAndGooglePlist(rootDic, gameDisplayName, gameConfig);
        }
        else
        {
            Debug.LogWarning($"ENABLE_FACEBOOK: false,不修改 Facebook 相关 Info.plist 设置");
        }
        //添加位置请求权限
        rootDic.SetString("NSLocationWhenInUseUsageDescription", "We need your geographic location information to get the relevant data around you.");
        if ((string)channelTag == "com.xgame.asia.kr")//韩国iOS
        {
            rootDic.SetString("NSUserTrackingUsageDescription", "필요한 권한은 맞춤형 광고를 제공하기 위한 목적으로만 사용됩니다.");//获取 idfa 权限描述
            rootDic.SetString("NSLocationWhenInUseUsageDescription", "귀하 주변의 관련 데이터를 수집하기 위해 지리 위치 정보가 필요합니다.");//添加位置请求权限
        }
        if ((string)channelTag == "com.xgame.asia.jp")//日本iOS
        {
            rootDic.SetString("NSUserTrackingUsageDescription", "必要な許可はパーソナライズされた広告を提供するためだけに使用されます。");//获取 idfa 权限描述
            rootDic.SetString("NSLocationWhenInUseUsageDescription", "お客様の周辺の関連データを取得するのに、お客様の地理的位置情報が必要です。");//添加位置请求权限
        }
        if (IsEnableTwitter(gameConfig))//是否支持推特配置
        {
            //推特配置
            ModifyTwitter(rootDic,gameConfig);
        }
        //韩国和台湾包新增谷歌登录
        ModifyGoogle(rootDic,gameConfig);

        //ThinkingSDK配置
        rootDic.SetBoolean("Q1Debug", bQ1Debug);
        rootDic.SetInteger("ThinkingArea", thinkingArea);

        //SDK region id配置
        rootDic.SetInteger("RegionId", regionId);

        //Pid配置
        var pid = gameConfig["Q1_PID"];
        Debug.Log("Pid配置:" + pid+",channelId："+channelId);
        if (pid != null && Convert.ToString(pid) != "0")
        {
            rootDic.SetString("Pid", Convert.ToString(pid));
        }
        else
        {
            rootDic.SetString("Pid", channelId);
        }

        if ((string)channelTag == "com.cnsj.bcwl")//超能世界iOS
        {
            rootDic.SetString("CFBundleDevelopmentRegion", "zh_CN");
        }

        // 国内版的包需要增加权限设置
        if(IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC"))
        {
            rootDic.SetString("NSPhotoLibraryAddUsageDescription", "App需要您的同意，才能保存您的游戏截图");
            rootDic.SetString("NSPhotoLibraryUsageDescription", "App需要您的同意,截图才能访问相册");
            rootDic.SetString("NSUserTrackingUsageDescription", "该标识符将用于向您投放个性化广告");
        }
        else
        {
            rootDic.SetString("Q1_USERPROTOCOL", "https://protocol.9z-play.com/GlaciersGame/enUserAgreement.html");
            rootDic.SetString("Q1_PRIVACYPOLICY", "https://protocol.9z-play.com/GlaciersGame/enPrivacyPolicy.html");
        }

        if ((string)channelTag == "com.hh.titanthnew.ios")//贪玩越南iOS
        {
            rootDic.SetString("GameAppID","43");
            rootDic.SetString("GameAppKey","AYelOPNpNXQ0mHj5#43#sfCJQMmWH5186YYT");
            rootDic.SetString("AppleAppID ","6480279388");
            rootDic.SetString("AppsFlyerDevKey","qK4nSngPy8dQseX6EFofBY");
            rootDic.SetString("TaAppID","5f4e8197ef394cc398fadca02ab9bcc4");
            rootDic.SetString("GADApplicationIdentifier","ca-app-pub-5035864031709582~4716855031");//广告集成参数
            var appDic = rootDic["NSAppTransportSecurity"] as PlistElementDict;
            if (appDic == null)
            {
                appDic = rootDic.CreateDict("NSAppTransportSecurity");
            }
            appDic.SetBoolean("NSAllowsArbitraryLoads", false);//能否发起http请求App Transport Security Settings/Allow Arbitrary Loads/false
            rootDic.SetBoolean("ITSAppUsesNonExemptEncryption", false);//出口合规证明App Uses Non-Exempt Encryption
            rootDic.SetBoolean("UIViewControllerBasedStatusBarAppearance", false);//状态栏显示由application控制/View controller-based status bar appearance
            rootDic.SetString("NSUserTrackingUsageDescription", "We need to obtain your ad tracking permissions to track ads, Your data will be used in ad-analysis, so that we can provide personalized services to you, is this allowed?");//广告标识权限
            rootDic.SetString("NSAdvertisingAttributionReportEndpoint", "https://appsflyer-skadnetwork.com/");//广告归因回传，直接复制该值即可Advertising attribution report endpoint URL
        
            AddBackgroundModes(rootDic,gameConfig);
        }

        //上传头像需要添加相机权限
        rootDic.SetString("NSCameraUsageDescription", "It is convenient for you to upload images when customizing an avatar.");

        //UIApplicationExitsOnSuspend: Deprecated, The UIApplicationExitsOnSuspend key is no longer supported in iOS 13
        //删除配置，否则部分工具在提交到 testflight 时失败
        var rootValues = rootDic.values;
        rootValues.Remove("UIApplicationExitsOnSuspend");

        ModifyBuglyPlist(gameConfig, rootDic);
        ModifyADMobPlist(gameConfig, rootDic);
        ModifyCustomURIScheme(rootDic);
        ModifyQ1SDKConfigure(gameConfig, rootDic, pathToBuiltProject);
		ModifyAdjusyConfigure(gameConfig, rootDic);
        string projPath = UnityEditor.iOS.Xcode.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        AddDeepLinkCfg(rootDic, projPath, gameConfig);
        plist.WriteToFile(plistPath);

        //Unity-iPhone.xcscheme 修改
        //ModifyXCScheme(pathToBuiltProject);
    }
	
    static void ModifyAdjusyConfigure(Dictionary<string, object> gameConfig, PlistElementDict rootDic)
    {
        var adjustConfigureDic = rootDic["AdjustConfigure"] as PlistElementDict;
        if (adjustConfigureDic == null)
        {
            adjustConfigureDic = rootDic.CreateDict("AdjustConfigure");
        }
        var adjustMode = gameConfig["ADJUST_SANDBOX_MODE"];
        Debug.Log("adjustMode:" + adjustMode);
        adjustConfigureDic.SetBoolean("ADJUST_SANDBOX_MODE", Convert.ToBoolean(adjustMode));
    }

    //设置深度链接配置
    public static void AddDeepLinkCfg(PlistElementDict rootDic, string projPath, Dictionary<string, object> gameConfig)
    {
        string channelTag = (string)gameConfig["CHANNEL_TAG"];
        var tDataCfg = AssetDatabase.LoadAssetAtPath<DeepLinkChannelAsset>("Assets/Scripts/Editor/AssetTools/deeplink_config.asset");
        DeepLinkChannelAsset.DeepLinkCfg deepLinkCfg = tDataCfg.GetDeepLinkCfg(channelTag);
        if (deepLinkCfg != null)
        {
            //添加urlSchemes
            if (deepLinkCfg.urlSchemes != null && deepLinkCfg.urlSchemes.Count>0)
            {
                foreach (var urlscheme in deepLinkCfg.urlSchemes)
                {
                    var urlTypes = rootDic["CFBundleURLTypes"] as PlistElementArray;
                    if(urlTypes == null)
                    {
                        urlTypes = rootDic.CreateArray("CFBundleURLTypes");
                    }
                    PlistElementDict urlDic = urlTypes.AddDict();
                    urlDic.SetString("CFBundleTypeRole", "Editor");
                    urlDic.SetString("CFBundleURLName", urlscheme.identifier);
                    PlistElementArray urlSchemes = urlDic.CreateArray("CFBundleURLSchemes");
                    foreach (var link in urlscheme.schemes)
                    {
                        urlSchemes.AddString(link);
                    }
                } 
               
            }

            //添加universalLinks
            if (deepLinkCfg.universalLinks != null && deepLinkCfg.universalLinks.Count>0)
            {
                var capManager = new ProjectCapabilityManager(projPath, "xheroRelease.entitlements", "Unity-iPhone");
                capManager.AddAssociatedDomains(deepLinkCfg.universalLinks.ToArray());
                capManager.WriteToFile();
            }
        }
    }

    static void ModifyBuglyPlist(Dictionary<string, object> gameConfig, PlistElementDict rootDic)
    {
        if (!IsEnableBugly(gameConfig))
        {
            Debug.LogWarning("Disable Bugly plist");
            return;
        }
        string appId;
        string appKey;
        string bundleId;
        bool q1DebugValue = false;

        var q1DebugCfg = gameConfig["ENABLE_Q1_DEBUG_MODE"];
        if (q1DebugCfg != null)
        {
            q1DebugValue = Convert.ToBoolean(q1DebugCfg);
        }
        GetBuglyInfo(q1DebugValue, gameConfig, out appId, out appKey, out bundleId);

        //- Appid
        rootDic.SetString("BuglyAppIDString", appId);
        //- 渠道标识
        rootDic.SetString("BuglyAppChannelString", bundleId);
        //-版本信息,(工程给到IT中心后，IT中心会修改版本号)
        //rootDic.SetString("BuglyAppVersionString", channelId);
        //- 开启Debug信息显示
        rootDic.SetBoolean("BuglyDebugEnable", q1DebugValue);
    }

    static void ModifyADMobPlist(Dictionary<string, object> gameConfig, PlistElementDict rootDic)
    {
        bool IsEnable = false;
        var enableAdmob = gameConfig["ENABLE_ADMOB"];
        if (enableAdmob != null)
        {
            var enableAdmobValue = Convert.ToBoolean(enableAdmob);
            if (enableAdmobValue)
            {
                IsEnable = true;
            }
        }
        if (IsEnable == true)
        {
            rootDic.SetString("GADApplicationIdentifier", "ca-app-pub-2892001563789649~2750415137");
        }
        
    }

    /*
     * 	
    <key>CFBundleURLTypes</key>
	<array>
		<dict>
        	<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>CFBundleURLName</key>
			<string>com.q1.${PRODUCT_NAME}</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>xherotravel</string>
			</array>
		</dict>
	</array>
     */
    static void ModifyCustomURIScheme(PlistElementDict rootDic)
    {
        var urlTypes = rootDic["CFBundleURLTypes"] as PlistElementArray;
        if(urlTypes == null)
        {
            urlTypes = rootDic.CreateArray("CFBundleURLTypes");
        }

        PlistElementDict urlDic = urlTypes.AddDict();
        urlDic.SetString("CFBundleTypeRole", "Viewer");

        PlistElementArray urlSchemes = urlDic.CreateArray("CFBundleURLSchemes");
        urlSchemes.AddString("xherotravel");

        urlDic.SetString("CFBundleURLName", "com.q1.${PRODUCT_NAME}");
    }

    static void CopyBundle(UnityEditor.iOS.Xcode.PBXProject proj,string pathToBuiltProject,string filePath,string fileName,string targetGuid)
    {
        string resPath = pathToBuiltProject + filePath + fileName +".bundle";
        if(Directory.Exists(resPath))
        {
            Debug.Log(fileName+".bundle  floder exist");
            CopyAndReplaceDirectory(resPath, pathToBuiltProject + "/sdkResource/"+fileName+".bundle");

            proj.AddFileToBuild(targetGuid, proj.AddFile("sdkResource/"+fileName+".bundle", filePath+fileName+".bundle", PBXSourceTree.Absolute));
        }
    }

    static void DeleteOKUnity(UnityEditor.iOS.Xcode.PBXProject proj,string pathToBuiltProject)
    {
        try
        {
            string resPath = pathToBuiltProject + "/Frameworks/CasualGame/SmartBrain/Plugins/Jupiter";
            string OKUnityCore_frameworkPath = "Frameworks/CasualGame/SmartBrain/Plugins/Jupiter/OKPlugins/iOS/OKUnityCore.framework";
            string OKUnitySdk_frameworkPath = "Frameworks/CasualGame/SmartBrain/Plugins/Jupiter/OKPlugins/iOS/OKUnitySdk.framework";
            if(Directory.Exists(resPath))
            {
                string guid_OKUnityCore = proj.FindFileGuidByRealPath(OKUnityCore_frameworkPath);
                proj.RemoveFile(guid_OKUnityCore);
                string guid_OKUnitySdk = proj.FindFileGuidByProjectPath(OKUnitySdk_frameworkPath);
                proj.RemoveFile(guid_OKUnitySdk);
                Directory.Delete(resPath,true);
            }
        }
        catch (Exception e)
        {
            Debug.LogError("Delete /Frameworks/CasualGame/SmartBrain/Plugins/Jupiter Exception:" + e);
        }
    }
    static void ModifyQ1SDKConfigure(Dictionary<string, object> gameConfig, PlistElementDict rootDic, string pathToBuiltProject)
    {
        if (!IsEnableSwitch(gameConfig, "ENABLE_Q1SDK"))
        {
            Debug.LogWarning("Disable Q1SDK plist");
            return;
        }
        //支持http传输方法
        var transportSecurityDic = rootDic["NSAppTransportSecurity"] as PlistElementDict;
        if (transportSecurityDic == null)
        {
            transportSecurityDic = rootDic.CreateDict("NSAppTransportSecurity");
        }
        transportSecurityDic.SetBoolean("NSAllowsArbitraryLoads", true);

        //配置SDK初始化参数 马甲包不需要设置
        var channelTag = gameConfig["CHANNEL_TAG"];
        if ((string)channelTag != "com.cnsj.youyi.test" ||(string)channelTag != "hn.yhsw.cp")
        {
            var q1SDKConfigureDic = rootDic["Q1SDKConfigure"] as PlistElementDict;
            if (q1SDKConfigureDic == null)
            {
                q1SDKConfigureDic = rootDic.CreateDict("Q1SDKConfigure");
            }

            q1SDKConfigureDic.SetString("Q1_APPKEY", "dfcf0d7f03ab76e89bc2810047ba3259");
            q1SDKConfigureDic.SetString("Q1_APPID", "2162");
            var pid = gameConfig["Q1_PID"];
            Debug.Log("Q1_PID:" + pid);
            if (pid != null && Convert.ToString(pid) != "0")
            {
                q1SDKConfigureDic.SetString("Q1_PID", Convert.ToString(pid));
            }
            else
            {
                q1SDKConfigureDic.SetString("Q1_PID", Convert.ToString(gameConfig["CHANNEL_ID"]));
            }
            int regionID = -1;
            if (gameConfig["REGION_ID"] != null)
            {
                regionID = Convert.ToInt32(gameConfig["REGION_ID"]);
            }
            q1SDKConfigureDic.SetBoolean("Q1_ENABLELOG", regionID == 3 || regionID == 4);
            string environmentStr = "DEBUG";
            if (regionID == 0)
                environmentStr = "PRO";
            else if (regionID == 2)
                environmentStr = "EA";
            else if (regionID == 3)
                environmentStr = "REVIEW";
            else if (regionID == 4)
                environmentStr = "DEBUG";
            q1SDKConfigureDic.SetString("Q1_ENVIRONMENT", environmentStr);
            q1SDKConfigureDic.SetBoolean("Q1_VISITORUPGRADE", false);
            //位面2海外IOS统一用这个域名主体9z-play.com
            q1SDKConfigureDic.SetString("Q1_DOMAIN", "9z-play.com");
            q1SDKConfigureDic.SetBoolean("Q1_OPENPUSH", true); //推送由接口调用改为配置 <ref: v2.14.0.2408 备注>
        }

        if(IsEnableSwitch(gameConfig, "Q1SDK_DOMESTIC"))
        {
            //添加获取idfa权限描述文字
            string tips = "游戏需要使用您的广告追踪权限定位广告来源方便您更好的体验游戏";
            rootDic.SetString("NSUserTrackingUsageDescription", tips);
        }

        //将ResourceName.bundle拷贝到工程根目录，否则sdk读不到配置，接口不通
        string src = pathToBuiltProject + "/Pods/GLASDK/SDK/Resources";
        if ((string)channelTag == "hn.yhsw.cp")
        {
            src = pathToBuiltProject + "/Frameworks/Plugins/iOS/SDK/WMResource.bundle";
        }
        else if ((string)channelTag == "com.hh.titanthnew.ios")
        {
            src = pathToBuiltProject + "/Frameworks/Plugins/iOS/SDK/CanSu.bundle";
        }
         string projPath1 = UnityEditor.iOS.Xcode.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.PBXProject proj = new UnityEditor.iOS.Xcode.PBXProject();

        // XCProject project = new XCProject(pathToBuiltProject);
        proj.ReadFromString(File.ReadAllText(projPath1));
#if UNITY_2020_1_OR_NEWER
        var targetGuid = proj.GetUnityMainTargetGuid();
#else
        var targetGuid = proj.TargetGuidByName("Unity-iPhone");
#endif
        if (Directory.Exists(src))
        {
            string dst = pathToBuiltProject + "/Resource";
            if ((string)channelTag == "hn.yhsw.cp")
            {
                dst = pathToBuiltProject;
                 //copy WMResource.bundle
                CopyBundle(proj,pathToBuiltProject,"/Frameworks/Plugins/iOS/SDK/","WMResource",targetGuid);
                File.WriteAllText(projPath1, proj.WriteToString());
            }
            else if ((string)channelTag == "com.hh.titanthnew.ios")
            {
                CopyBundle(proj,pathToBuiltProject,"/Frameworks/Plugins/iOS/SDK/","CanSu",targetGuid);
                CopyBundle(proj,pathToBuiltProject,"/Frameworks/Plugins/iOS/","Resources",targetGuid);
                DeleteOKUnity(proj,pathToBuiltProject);
                File.WriteAllText(projPath1, proj. WriteToString());
            }
            else
            {
                DirectoryCopy(src, dst);
            }

            Debug.Log("copy ResourceName.bundle from " + src + " to " + dst + "guid " + "");


        }
        else
        {
            Debug.Log("ResourceName.bundle not exist, path:" + src);
        }

        // 添加马甲包sdk资源包到xcode buildPhases中
        string projPath = UnityEditor.iOS.Xcode.Custom.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        if(!File.Exists(projPath))
        {
            Debug.Log(projPath + " not exists!");
            return;
        }

        UnityEditor.iOS.Xcode.Custom.PBXProject pBXProject = new UnityEditor.iOS.Xcode.Custom.PBXProject();
        pBXProject.ReadFromString(File.ReadAllText(projPath));
        if ((string)channelTag == "com.cnsj.youyi.test")
        {
            string bundlePath = pathToBuiltProject + "/Frameworks/Plugins/iOS/SDK/ResourceName.bundle";
            string guid = pBXProject.FindFileGuidByProjectPath(bundlePath);
            if (guid != null)
            {
                pBXProject.AddResourcesBuildPhase(guid);
            }
            
        }
        

        //将masonry库删除再导入，否则进游戏报错，原理不详
        //string masonryDir = pathToBuiltProject + "/Pods/Masonry";
        //if (Directory.Exists(masonryDir))
        //{

        //    string guid = pBXProject.FindFileGuidByProjectPath(masonryDir);
        //    if (guid != null)
        //    {
        //        pBXProject.RemoveFile(guid);

        //        foreach (string file in Directory.GetFiles(masonryDir, "*.*", SearchOption.AllDirectories))
        //        {
        //            string absoluteFilePath = file;
        //            pBXProject.AddFile(absoluteFilePath, absoluteFilePath, UnityEditor.iOS.Xcode.Custom.PBXSourceTree.Absolute);
        //            Debug.Log("add masonry file:" + absoluteFilePath);
        //        }
        //    }
        //    else
        //    {
        //        Debug.Log("can not find masonry file guide");
        //    }
        //}
        //else
        //{
        //    Debug.Log("masonry not exist, path: " + src);
        //}
        //pBXProject.WriteToFile(projPath);
    }

    static void CopyAndReplaceDirectory(string srcPath, string dstPath)
    {
        //路径下该文件夹若存在，则删除
        if (Directory.Exists(dstPath))
        {
            Directory.Delete(dstPath);
        }
        //路径下的文件若存在，则删除
        if (File.Exists(dstPath))
        {
            File.Delete(dstPath);
        }
        //创建该路径下文件夹
        Directory.CreateDirectory(dstPath);
        Debug.Log(dstPath + "----" + srcPath);

        foreach (var file in Directory.GetFiles(srcPath))
        {
            Debug.Log(Path.Combine(dstPath, Path.GetFileName(file)));
            File.Copy(file, Path.Combine(dstPath, Path.GetFileName(file)));
        }

        foreach (var dir in Directory.GetDirectories(srcPath))
            CopyAndReplaceDirectory(dir, Path.Combine(dstPath, Path.GetFileName(dir)));
    }

    
    //需要手动拖动UnityFramework到 link binary with libraries，这里自动化处理
     static void AutoAddUnityFrameworkToUnityIPhoneTarget(string pathToBuiltProject)
    {
        Debug.Log("手动拖动UnityFramework到Unity-iPhone Target自动化");
        //手动拖动UnityFramework到Unity-iPhone Target自动化测试
        string projPath = UnityEditor.iOS.Xcode.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.PBXProject proj = new UnityEditor.iOS.Xcode.PBXProject();
        proj.ReadFromString(File.ReadAllText(projPath));
        // proj.AddCopyFilesBuildPhase()
        string unityIPhoneTargetGuid = proj.GetUnityMainTargetGuid();
        if(proj.ContainsFramework(unityIPhoneTargetGuid,"UnityFramework.framework"))
        {
            Debug.Log("Unity-iPhone Target 中存在 UnityFramework.framework");
        }
        else
        {
            Debug.Log("Unity-iPhone Target 中 不 存在 UnityFramework.framework");
        }
        // proj.RemoveFrameworkFromProject(unityIPhoneTargetGuid,"UnityFramework.framework");
        // proj.AddFrameworksBuildPhase(unityIPhoneTargetGuid);
        proj.AddFrameworkToProject(unityIPhoneTargetGuid,"UnityFramework.framework",false);
        if(proj.ContainsFramework(unityIPhoneTargetGuid,"UnityFramework.framework"))
        {
            Debug.Log("Unity-iPhone Target 中添加UnityFramework.framework之后    存在 UnityFramework.framework");
        }
        else
        {
            Debug.Log("Unity-iPhone Target 中添加UnityFramework.framework之后 不 存在 UnityFramework.framework");
        }
        proj.WriteToFile(projPath);
        Debug.Log("手动拖动UnityFramework到Unity-iPhone Target自动化---------------------");
    }

    //framework移除出Unity-iPhone Target并在UnityFramework Target中添加
    static void RemoveFrameworkToUnityIPhoneTarget(string pathToBuiltProject,string framework)
    {
        Debug.Log("Unity-iPhone Target中删除framework");
        string projPath = UnityEditor.iOS.Xcode.PBXProject.GetPBXProjectPath(pathToBuiltProject);
        UnityEditor.iOS.Xcode.PBXProject proj = new UnityEditor.iOS.Xcode.PBXProject();
        proj.ReadFromString(File.ReadAllText(projPath));
        string unityIPhoneTargetGuid = proj.GetUnityMainTargetGuid();
        if (proj.ContainsFramework(unityIPhoneTargetGuid, framework))
        {
            Debug.Log("Unity-iPhone Target 中存在 " + framework);
        }
        else
        {
            Debug.Log("Unity-iPhone Target 中 不 存在 " + framework);
        }
        proj.RemoveFrameworkFromProject(unityIPhoneTargetGuid, framework);
        if (proj.ContainsFramework(unityIPhoneTargetGuid, framework))
        {
            Debug.Log("Unity-iPhone Target 中移除" + framework +"失败");
        }
        else
        {
            Debug.Log("Unity-iPhone Target 中移除" + framework + "成功");
        }

        var targetGuid = proj.GetUnityFrameworkTargetGuid();
        proj.AddFrameworkToProject(targetGuid, framework, true);

        proj.WriteToFile(projPath);
        Debug.Log("Unity-iPhone Target中移除------" + framework);
    }

    /// <summary>
    /// 添加文件到Unity-iPhone Target中
    /// </summary>
    /// <param name="pBXProject"></param>
    /// <param name="projPath"></param>
    /// <param name="pathToBuiltProject"></param>
    /// <param name="filePathInUnity">存放在unity中的文件路径</param>
    /// <param name="fileName">文件名称（包含后缀）</param>
    static private void AddFile2UnityiPhoneTarget(UnityEditor.iOS.Xcode.PBXProject pBXProject, string projPath, string pathToBuiltProject, string filePathInUnity, string fileName)
    {
        string targetPath = pathToBuiltProject + "/" + fileName;
        if (File.Exists(filePathInUnity))
        {
            File.Copy(filePathInUnity, targetPath, true);
        }
        else
        {
            Debug.LogError("PrivacyInfo.xcprivacy not exist");
            return;
        }

        pBXProject.ReadFromString(File.ReadAllText(projPath));
#if UNITY_2020_1_OR_NEWER
        var targetGuid = pBXProject.GetUnityMainTargetGuid();

        var buildPhaseID = pBXProject.AddSourcesBuildPhase(targetGuid);
        pBXProject.AddFileToBuildSection(targetGuid, buildPhaseID, pBXProject.AddFile(targetPath, fileName));
#endif
        pBXProject.WriteToFile(projPath);
    }

    private static void UpdateXcodeBuildSystem(string projectPath)
    {
        string workspaceSettingsPath = Path.Combine(projectPath,"Unity-iPhone.xcodeproj/project.xcworkspace/xcshareddata/" +"WorkspaceSettings.xcsettings");
        if (File.Exists(workspaceSettingsPath))
        {
            // Read the plist document, and find the root element
            PlistDocument workspaceSettings = new PlistDocument();
            workspaceSettings.ReadFromFile(workspaceSettingsPath);
            PlistElementDict root = workspaceSettings.root;

            // Modify the document as necessary.
            bool workspaceSettingsChanged = false;
            // Remove the BuildSystemType entry because it specifies the
            // legacy Xcode build system, which is deprecated
            if (root.values.ContainsKey("BuildSystemType"))
            {
                root.values.Remove("BuildSystemType");
                workspaceSettingsChanged = true;
            }
            // If actual changes to the document occurred, write the result
            // back to disk.
            if (workspaceSettingsChanged)
            {
                Debug.Log("UpdateXcodeBuildSystem: Writing updated " +  "workspace settings to disk.");
                try
                {
                    workspaceSettings.WriteToFile(workspaceSettingsPath);
                }
                catch (System.Exception e)
                {
                    Debug.LogError(string.Format("UpdateXcodeBuildSystem: Exception occurred writing workspace settings to disk: \n{0}", e.Message));
                    throw;
                }
            }
            else
            {
                Debug.Log("UpdateXcodeBuildSystem: workspace settings did not require modifications.");
            }
        }
        else
        {
            Debug.LogWarningFormat("UpdateXcodeBuildSystem: could not find workspace settings files [{0}]", workspaceSettingsPath);
        }
    }

    /// <summary>
    /// 
    //<key>CFBundleURLTypes</key>
    //<array>
    //    <dict>
    //        <key>CFBundleTypeRole</key>
    //        <string>Editor</string>
    //        <key>CFBundleURLSchemes</key>
    //        <array>
    //            <string>"Your FBID"</string>
    //        </array>
    //    </dict>
    //    <dict>
    //        <key>CFBundleTypeRole</key>
    //        <string>Editor</string>
    //        <key>CFBundleURLName</key>
    //        <string></string>
    //        <key>CFBundleURLSchemes</key>
    //        <array>
    //            <string>'your GoogleAppKey'</string>
    //        </array>
    //    </dict>
    //</array>
    //<key>FacebookAppID</key>
    //    <string>Your FBID</string>
    //<key>FacebookDisplayName</key>
    //    <string>Your App Name</string>
    //<key>LSApplicationQueriesSchemes</key>
    //<array>
    //    <string>fbapi</string>
    //    <string>fb-messenger-api</string>
    //    <string>fbauth2</string>
    //    <string>fbshareextension</string>
    //</array>
    /// </summary>
    /// <param name="rootDic"></param>
    static void ModifyFacebookAndGooglePlist(PlistElementDict rootDic, string displayName, Dictionary<string, object> gameConfig)
    {
        PlistElementArray CFBundleURLTypes = rootDic["CFBundleURLTypes"] as PlistElementArray;
        if(CFBundleURLTypes == null)
        {
            CFBundleURLTypes = rootDic.CreateArray("CFBundleURLTypes");
        }

        var channelTag = gameConfig["CHANNEL_TAG"];
        var fbDataCfg = AssetDatabase.LoadAssetAtPath<editor.tools.asset.DataSetAsset>("Assets/Scripts/Editor/AssetTools/facebook_config.asset");
        var fbAppID = fbDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag },"fbAppID", RuntimePlatform.IPhonePlayer);
        if(string.IsNullOrEmpty(fbAppID))
        {
            //fb appid：688028982030752   app name：   X-Hero 2020.07.08
            fbAppID = "688028982030752";
            Debug.LogError($"Facebook, {channelTag} 未配置 fbAppID (FacebookAppID)");
        }
        // e.g. "fb688028982030752" app name：   X-Hero 2020.07.08
        var schemesFbAppID = $"fb{fbAppID}";

        var fbClientToken = fbDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag }, "fbClientToken", RuntimePlatform.IPhonePlayer);
        if(string.IsNullOrEmpty(fbClientToken))
        {
            Debug.LogError($"Facebook, {channelTag} 未配置 fbClientToken (FacebookClientToken)");
            EditorApplication.Exit(1);
        }

        //修改 CFBundleURLTypes
        PlistElementDict fbid = CFBundleURLTypes.AddDict();
        fbid.SetString("CFBundleTypeRole", "Editor");
        PlistElementArray cfbUrlSchemes = fbid.CreateArray("CFBundleURLSchemes");
        cfbUrlSchemes.AddString(schemesFbAppID);//"Your FBID"
        fbid.SetString("CFBundleURLName", "fb");
        
#if UNITY_EDITOR && UNITY_ANDROID
        PlistElementDict googleKey = CFBundleURLTypes.AddDict();
        googleKey.SetString("CFBundleTypeRole", "Editor");
        googleKey.SetString("CFBundleURLName", "");
        cfbUrlSchemes = googleKey.CreateArray("CFBundleURLSchemes");
        cfbUrlSchemes.AddString("1030525918888-2r05shgsj1ikai12ol5j68heabp3goen.apps.googleusercontent.com");//"your GoogleAppKey"
#endif

        //添加位置请求权限
        rootDic.SetString("NSLocationWhenInUseUsageDescription", "We need your geographic location information to get the relevant data around you.");
        //rootDic.SetString("NSLocationWhenInUseUsageDescription", "我们需要通过您的地理位置信息获取您周边的相关数据");

        //修改 FacebookAppID
        rootDic.SetString("FacebookAppID", fbAppID);
        rootDic.SetString("FacebookClientToken", fbClientToken);
        rootDic.SetString("FacebookDisplayName", displayName);

        rootDic.SetString("CFBundleDisplayName", displayName);

        rootDic.SetString("FacebookAutoLogAppEventsEnabled", "TRUE");

        rootDic.SetString("FacebookAdvertiserIDCollectionEnabled", "TRUE");

        //修改 LSApplicationQueriesSchemes
        PlistElementArray LSApplicationQueriesSchemes = GetPlistArray(rootDic);
        LSApplicationQueriesSchemes.AddString("fbapi");
        LSApplicationQueriesSchemes.AddString("fb-messenger-api");
        LSApplicationQueriesSchemes.AddString("fbauth2");
        LSApplicationQueriesSchemes.AddString("fbshareextension");
        
        if (IsEnableTwitter(gameConfig))//是否支持推特配置
        {
            LSApplicationQueriesSchemes.AddString("fbapi");
            LSApplicationQueriesSchemes.AddString("fb-messenger-share-api");
            LSApplicationQueriesSchemes.AddString("fbauth2");
            LSApplicationQueriesSchemes.AddString("fbshareextension");
            LSApplicationQueriesSchemes.AddString("fbauth");
        }
#if UNITY_IPHONE
        if ((string)channelTag == "com.hh.titanthnew.ios")//贪玩越南iOS
        {
            rootDic.SetString("GIDClientID", "723580245729-eonsk0ojhe1721ueq91cpokbqo466u6t.apps.googleusercontent.com");//设置GoogleClientId参数 
            PlistElementDict googleKey = CFBundleURLTypes.AddDict();
            googleKey.SetString("CFBundleTypeRole", "Editor");
            googleKey.SetString("CFBundleURLName", "");
            cfbUrlSchemes = googleKey.CreateArray("CFBundleURLSchemes");
            cfbUrlSchemes.AddString("com.googleusercontent.apps.723580245729-eonsk0ojhe1721ueq91cpokbqo466u6t");//"Google_ClientID以 . 分隔后反向拼接"
            //添加白名单 
            LSApplicationQueriesSchemes.AddString("com.google.gppconsent.2.4.1");
            LSApplicationQueriesSchemes.AddString("com.google.gppconsent.2.4.0");
            LSApplicationQueriesSchemes.AddString("com.google.gppconsent.2.3.0");
            LSApplicationQueriesSchemes.AddString("com.google.gppconsent.2.2.0");
            LSApplicationQueriesSchemes.AddString("com.google.gppconsent");
            LSApplicationQueriesSchemes.AddString("hasgplus4");
            LSApplicationQueriesSchemes.AddString("googlechrome-x-callback");
            LSApplicationQueriesSchemes.AddString("googlechrome");
            LSApplicationQueriesSchemes.AddString("fbapi20130214");
            LSApplicationQueriesSchemes.AddString("fbapi20130410");
            LSApplicationQueriesSchemes.AddString("fbapi20130702");
            LSApplicationQueriesSchemes.AddString("fbapi20131010");
            LSApplicationQueriesSchemes.AddString("fbapi20131219");
            LSApplicationQueriesSchemes.AddString("fbapi20140410");
            LSApplicationQueriesSchemes.AddString("fbapi20140116");
            LSApplicationQueriesSchemes.AddString("fbapi20150313");
            LSApplicationQueriesSchemes.AddString("fbapi20150629");
            LSApplicationQueriesSchemes.AddString("fbapi20160328");
            LSApplicationQueriesSchemes.AddString("fbauth");
            LSApplicationQueriesSchemes.AddString("fb-messenger-share-api");
        }
#endif

    }

    public static PlistElementArray GetPlistArray(PlistElementDict rootDic)
    {
        PlistElementArray LSApplicationQueriesSchemes = rootDic["LSApplicationQueriesSchemes"] as PlistElementArray;
        if (LSApplicationQueriesSchemes == null)
        {
            LSApplicationQueriesSchemes = rootDic.CreateArray("LSApplicationQueriesSchemes");
        }
        return LSApplicationQueriesSchemes;
    }

    public static bool IsEnableTwitter(Dictionary<string, object> gameConfig)
    {
        var channelTag = gameConfig["CHANNEL_TAG"];
        var tDataCfg = AssetDatabase.LoadAssetAtPath<editor.tools.asset.DataSetAsset>("Assets/Scripts/Editor/AssetTools/twitter_config.asset");
        var twitterV2ClientIDKey = tDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag },"TwitterV2ClientIDKey", RuntimePlatform.IPhonePlayer);
        if (string.IsNullOrEmpty(twitterV2ClientIDKey))
        {
            Debug.LogError($"Twitter, {channelTag} 未配置 TwitterV2ClientIDKey");
            return false;
        }
        else
        {
            return true;
        }
    }

    //新增谷歌配置info.plist
    static void ModifyGoogle(PlistElementDict rootDic,Dictionary<string, object> gameConfig)
    {
        var channelTag = gameConfig["CHANNEL_TAG"];
        var tDataCfg = AssetDatabase.LoadAssetAtPath<editor.tools.asset.DataSetAsset>("Assets/Scripts/Editor/AssetTools/ios_google_config.asset");
        var gIDClientID = tDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag },"GIDClientID", RuntimePlatform.IPhonePlayer);
        if(string.IsNullOrEmpty(gIDClientID))
        {
            gIDClientID = "360387125224-l5l922m99ad9oqi5sm9ekv243dqm3spg.apps.googleusercontent.com";
            Debug.LogError($"IOSGoogle, {channelTag} 未配置 GIDClientID");
            return;
        }
        var googleAppKey = tDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag }, "GoogleAppKey", RuntimePlatform.IPhonePlayer);
        if(string.IsNullOrEmpty(googleAppKey))
        {
            googleAppKey = "com.googleusercontent.apps.360387125224-l5l922m99ad9oqi5sm9ekv243dqm3spg";
            Debug.LogError($"IOSGoogle, {channelTag} 未配置 GoogleAppKey");
            return;
        }

        var urlTypes = rootDic["CFBundleURLTypes"] as PlistElementArray;
        if(urlTypes == null)
        {
            urlTypes = rootDic.CreateArray("CFBundleURLTypes");
        }
        //修改 CFBundleURLTypes
        PlistElementDict urlDic = urlTypes.AddDict();
        //修改 Identify
        urlDic.SetString("CFBundleURLName", "google");
        //修改 Role
        urlDic.SetString("CFBundleTypeRole", "Editor");
        //修改 URLSchemes
        PlistElementArray urlSchemes = urlDic.CreateArray("CFBundleURLSchemes");
        urlSchemes.AddString(googleAppKey);
        //配置GIDClientID
        rootDic.SetString("GIDClientID", gIDClientID);
    }

    //新增推特配置info.plist
    static void ModifyTwitter(PlistElementDict rootDic,Dictionary<string, object> gameConfig)
    {
        var channelTag = gameConfig["CHANNEL_TAG"];
        var tDataCfg = AssetDatabase.LoadAssetAtPath<editor.tools.asset.DataSetAsset>("Assets/Scripts/Editor/AssetTools/twitter_config.asset");
        var twitterV2ClientIDKey = tDataCfg.Get(new editor.tools.asset.DataSetAsset.KeyValuePair() { Key = "channelTag", Value = (string)channelTag },"TwitterV2ClientIDKey", RuntimePlatform.IPhonePlayer);
        if(string.IsNullOrEmpty(twitterV2ClientIDKey))
        {
            twitterV2ClientIDKey = "WHNWQkFNQ0w3Vk9uMDdMR0c3ajQ6MTpjaQ";
            Debug.LogError($"Twitter, {channelTag} 未配置 TwitterV2ClientIDKey");
        }
        var schemesTwitterConsumerKey = "bctw";

        var urlTypes = rootDic["CFBundleURLTypes"] as PlistElementArray;
        if(urlTypes == null)
        {
            urlTypes = rootDic.CreateArray("CFBundleURLTypes");
        }
        //修改 CFBundleURLTypes
        PlistElementDict urlDic = urlTypes.AddDict();
        //修改 Identify
        urlDic.SetString("CFBundleURLName", "twitterV2");
        //修改 Role
        urlDic.SetString("CFBundleTypeRole", "Editor");
        //修改 URLSchemes
        PlistElementArray urlSchemes = urlDic.CreateArray("CFBundleURLSchemes");
        urlSchemes.AddString(schemesTwitterConsumerKey);
        //修改 LSApplicationQueriesSchemes
        //Queried URL Schemes 和 LSApplicationQueriesSchemes是同一个
        PlistElementArray LSApplicationQueriesSchemes = GetPlistArray(rootDic);
        LSApplicationQueriesSchemes.AddString("twitterauth");
        LSApplicationQueriesSchemes.AddString("twitter");
        //配置推特SDK初始化所需CTwitterV2ClientIDKey
        rootDic.SetString("TwitterV2ClientIDKey", twitterV2ClientIDKey);
    }

    static void ModifyXCScheme(string pathToBuiltProject)
    {
        string schemePath = pathToBuiltProject + "/Unity-iPhone.xcodeproj/xcshareddata/xcschemes/Unity-iPhone.xcscheme";
        if(!File.Exists(schemePath))
        {
            Debug.LogError("xcscheme 文件不存在:" + schemePath);
            throw new Exception("xcscheme 文件不存在:" + schemePath);
        }
        XmlDocument schemeDoc = new XmlDocument();
        schemeDoc.Load(schemePath);

        XmlNode launch = schemeDoc.SelectSingleNode("/Scheme/LaunchAction");
        if(launch != null)
        {
            //创建属性
            XmlAttribute nameAttribute = schemeDoc.CreateAttribute("disableMainThreadChecker");
            nameAttribute.Value = "YES";
            //xml节点附件属性
            launch.Attributes.Append(nameAttribute);
        }
        else
        {
            Debug.LogError("工程不支持 xcscheme 配置:" + schemePath);
            throw new Exception("工程不支持 xcscheme 配置:" + schemePath);
        }
        schemeDoc.Save(schemePath);
    }

    [NUnit.Framework.Test]
    public static void TestModifyCustomURIScheme()
    {
        var pathToBuiltProject = "./XCode/";
        string plistPath = pathToBuiltProject + "/Info.plist";
        Debug.LogWarning("XCodePostProcess ModifyPlist path:" + plistPath);

        PlistDocument plist = new PlistDocument();
        plist.ReadFromFile(plistPath);

        PlistElementDict rootDic = plist.root;

        ModifyCustomURIScheme(rootDic);

        plist.WriteToFile(plistPath);
    }
#endif //UNITY_IOS
#endif //UNITY_EDITOR

    public static void Log(string message)
    {
        UnityEngine.Debug.Log("PostProcess: " + message);
    }

    [NUnit.Framework.Test]
    public static void TestModifyXCWorkspace()
    {
        Debug.LogWarning("current dir:" + Directory.GetCurrentDirectory());
        string xcsettingPath = "./XCode/WorkspaceSettings.xcsettings";
        ModifyXCWorkspace("", xcsettingPath);
    }

    /// <summary>
    /// 修改 xcworkspace 内容
    /// </summary>
    /// <param name="pathToBuiltProject">e.g. /Volumes/Samsung_T5/.jenkins/workspace/T5_iOS_trunk/XHero </param>
    static void ModifyXCWorkspace(string pathToBuiltProject, string relativeXcsettingPath)
    {
        string xcsettingPath;
        if (string.IsNullOrEmpty(relativeXcsettingPath))
        {
            xcsettingPath = pathToBuiltProject + "/Unity-iPhone.xcworkspace/xcshareddata/WorkspaceSettings.xcsettings";
        }
        else
        {
            xcsettingPath = pathToBuiltProject + relativeXcsettingPath;
        }
        XmlDocument schemeDoc = new XmlDocument();

        if (!File.Exists(xcsettingPath))
        {
            Debug.LogWarning("xcsetting is not exist:" + xcsettingPath);
            var xcsettingDirPath = Path.GetDirectoryName(xcsettingPath);
            if (!Directory.Exists(xcsettingDirPath))
            {
                Directory.CreateDirectory(xcsettingDirPath);
            }
            schemeDoc = new XmlDocument();
            //创建Xml声明部分，即<?xml version="1.0" encoding="utf-8" ?>
            XmlDeclaration declaration = schemeDoc.CreateXmlDeclaration("1.0", "utf-8", null);
            schemeDoc.AppendChild(declaration);

            var docType = schemeDoc.CreateDocumentType("plist", "-//Apple//DTD PLIST 1.0//EN", "http://www.apple.com/DTDs/PropertyList-1.0.dtd", null);
            schemeDoc.AppendChild(docType);

            XmlNode rootNode = schemeDoc.CreateElement("plist");
            XmlAttribute plistAt = schemeDoc.CreateAttribute("version");
            plistAt.Value = "1.0";
            rootNode.Attributes.Append(plistAt);
            schemeDoc.AppendChild(rootNode);

            var _dict = schemeDoc.CreateElement("dict");

            var systemType = schemeDoc.CreateNode("element", "key", "");
            systemType.InnerText = "BuildSystemType";
            _dict.AppendChild(systemType);

            systemType = schemeDoc.CreateNode("element", "string", "");
            systemType.InnerText = "Original";
            _dict.AppendChild(systemType);

            systemType = schemeDoc.CreateNode("element", "key", "");
            systemType.InnerText = "PreviewsEnabled";
            _dict.AppendChild(systemType);

            systemType = schemeDoc.CreateNode("element", "false", "");
            _dict.AppendChild(systemType);

            rootNode.AppendChild(_dict);

            schemeDoc.Save(xcsettingPath);
            return;
        }
        schemeDoc.Load(xcsettingPath);

        XmlNode dict = schemeDoc.SelectSingleNode("/plist/dict");
        if (dict != null)
        {
            var child = dict.FirstChild;
            while (child != null)
            {
                if (child.InnerText == "BuildSystemType")
                {
                    child = child.NextSibling;
                    if (child.InnerText == "Original")
                    {
                        Debug.LogWarning("已存在 BuildSystemType 设置");
                        return;
                    }
                    break;
                }
                child = child.NextSibling;
            }

            //创建属性
            XmlNode systemType = schemeDoc.CreateNode("element", "key", "");
            systemType.InnerText = "BuildSystemType";
            dict.AppendChild(systemType);

            systemType = schemeDoc.CreateNode("element", "string", "");
            systemType.InnerText = "Original";
            dict.AppendChild(systemType);
        }
        else
        {
            Debug.LogError("工程不支持 xcsetting 配置:" + xcsettingPath);
            throw new Exception("工程不支持 xcsetting 配置:" + xcsettingPath);
        }
        schemeDoc.Save(xcsettingPath);
    }
}
