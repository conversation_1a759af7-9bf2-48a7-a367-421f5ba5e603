using System.Collections;
using System.Collections.Generic;
using System.Net;
using UnityEditor;
using UnityEngine;
 
public class CacheServerSetter : Editor
{
    private const string ignoreIPPostfix = "172.18.0."; //忽略上传事件的打包机ip前缀
    const string cacherPath = "***********:9177";
    [InitializeOnLoadMethod]
    static void Init()
    { 
        if (EditorSettings.cacheServerMode != CacheServerMode.Enabled)
        {
            EditorSettings.cacheServerMode = CacheServerMode.Enabled;
            EditorSettings.cacheServerEnableDownload = true;
            EditorSettings.cacheServerEnableUpload = true;
            Debug.LogError("CacheServer 开启");
        }
        if (EditorSettings.cacheServerEndpoint != cacherPath)
        {
            EditorSettings.cacheServerEndpoint = cacherPath;
            Debug.Log("Cacher Enable,target cacherIp is:" + cacherPath);
        }

        if (IsPackMachine() && EditorSettings.cacheServerMode == CacheServerMode.Enabled) //打包机禁止上传cacheserver信息，因为可能有打包后的大量ab更新的上传，只允许下载
        {
            EditorSettings.cacheServerEnableUpload = false;
            Debug.LogError("CacheServer 打包机禁止上传");
        }
        else
        {
            EditorSettings.cacheServerEnableUpload = true;
        }
    }

    /// <summary>
    /// 默认 172.18.0 开头的都是打包机
    /// </summary>
    /// <returns></returns>
    private static bool IsPackMachine()
    {
        var host = Dns.GetHostEntry(Dns.GetHostName());
        foreach (var ip in host.AddressList)
        {
            if (ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
            {
                string curip = ip.ToString();
                if (curip.StartsWith(ignoreIPPostfix))
                {

                    return true;
                }
                break;
            }
        }
        return false;
    }
}
