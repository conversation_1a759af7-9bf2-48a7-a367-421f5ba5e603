/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using System.Text;
using EditorUtilitys;
using System;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 英雄资源初始内存占用
    /// </summary>
    public class HeroMemoryInitInfo
    {
        /// <summary>
        /// 英雄名字
        /// </summary>
        public int heroId;
        /// <summary>
        /// 英雄名字
        /// </summary>
        public string heroName;
        /// <summary>
        /// 编辑器下占用内存
        /// </summary>
        public long editorMemoryBytes;
        /// <summary>
        /// 动画占用内存
        /// </summary>
        public long animMemoryBytes;
        /// <summary>
        /// 动画占用内存详情
        /// </summary>
        public string animMemoryDesc;
        /// <summary>
        /// 角色贴图占用内存
        /// </summary>
        public long charaTexMemoryBytes;
        /// <summary>
        /// 角色贴图占用内存详情
        /// </summary>
        public string charaTexMemoryDesc;
        /// <summary>
        /// 角色特效贴图占用内存
        /// </summary>
        public long effTexMemoryBytes;
        /// <summary>
        /// 角色特效贴图占用内存详情
        /// </summary>
        public string effTexMemoryDesc;
        /// <summary>
        /// shader占用内存
        /// </summary>
        public long shaderMemoryBytes;
        /// <summary>
        /// shader占用内存详情
        /// </summary>
        public string shaderMemoryDesc;
        /// <summary>
        /// 其他占用内存
        /// </summary>
        public long otherMemoryBytes;
    }

    /// <summary>
    /// 文件内存信息
    /// </summary>
    abstract class FileMemoryInfo
    {
        /// <summary>
        /// 文件名
        /// </summary>
        public string fileName;
        /// <summary>
        /// 文件大小
        /// </summary>
        public long fileSize;
        /// <summary>
        /// 额外信息描述
        /// </summary>
        public string extraDesc;
    }

    /// <summary>
    /// 动画内存信息
    /// </summary>
    class AnimFileMemoryInfo : FileMemoryInfo
    {

    }

    /// <summary>
    /// 角色纹理内存信息
    /// </summary>
    class CharacterTexMemoryInfo : FileMemoryInfo
    {

    }

    /// <summary>
    /// 特效纹理内存信息
    /// </summary>
    class EffectTexMemoryInfo : FileMemoryInfo
    {

    }

    /// <summary>
    /// shader内存信息
    /// </summary>
    class ShaderMemoryInfo : FileMemoryInfo
    {

    }

    /// <summary>
    /// 内存信息类型
    /// </summary>
    enum EMemoryInfo
    {
        /// <summary>
        /// 动画
        /// </summary>
        Anim,
        /// <summary>
        /// 角色纹理
        /// </summary>
        CharacterTex,
        /// <summary>
        /// 特效纹理
        /// </summary>
        EffectTex,
        /// <summary>
        /// shader
        /// </summary>
        Shader,
    }

    /// <summary>
    /// 获取行内容子串
    /// </summary>
    /// <param name="strContent"></param>
    /// <param name="index"></param>
    /// <returns></returns>
    static string GetLineSubContent(string strContent, int index)
    {
        string strSubContent = string.Empty;
        if (!string.IsNullOrEmpty(strContent) && index >= 0)
        {
            var tArr = strContent.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            if (tArr != null && index <= tArr.Length - 1)
            {
                strSubContent = tArr[index];
            }
        }

        return strSubContent;
    }

    /// <summary>
    /// 获取csv内容
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static List<string> GetCsvContent(string path)
    {
        List<string> contents = new List<string>();

        if (string.IsNullOrEmpty(path) || !File.Exists(path))
        {
            return contents;
        }

        using (StreamReader ReaderObject = new StreamReader(path))
        {
            string line;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                contents.Add(line);
            }
        }

        return contents;
    }

    /// <summary>
    /// 返回内存占用简化信息
    /// </summary>
    /// <param name="bUseSortByInit"></param>
    /// <param name="bUseInitMemory"></param>
    /// <param name="heroInitInfoList"></param>
    /// <param name="heroList"></param>
    /// <returns></returns>
    static string ReturnMemorySimplyDesc(bool bUseSortByInit, bool bUseInitMemory, List<KeyValuePair<int, long>> heroInitInfoList, List<HeroMemoryInitInfo> heroList)
    {
        StringBuilder sb = new StringBuilder();
        sb.Append("heroId").Append(",").Append("size").Append(",").Append("").Append(",").Append("heroName").Append(",");
        if(animFbxNeedOptimal)
        {
            sb.Append("animSize").Append(",");
            sb.Append("").Append(",");
        }
        sb.Append("charaTexSize").Append(",").Append("").Append(",");
        sb.Append("effectTexSize").Append(",").Append("");

        if (!bUseSortByInit)
        {
            foreach (var t in heroList)
            {
                sb.Append("\n");
                sb.Append(t.heroId).Append(",").Append(EditorUtility.FormatBytes(t.editorMemoryBytes)).Append(",").Append(t.editorMemoryBytes).Append(",").Append(t.heroName).Append(",");
                if (animFbxNeedOptimal)
                {
                    sb.Append(EditorUtility.FormatBytes(t.animMemoryBytes)).Append(",");
                    sb.Append(t.animMemoryBytes).Append(",");
                }
                sb.Append(EditorUtility.FormatBytes(t.charaTexMemoryBytes)).Append(",").Append(t.charaTexMemoryBytes).Append(",").Append(EditorUtility.FormatBytes(t.effTexMemoryBytes)).Append(",").Append(t.effTexMemoryBytes);
            }
        }
        else
        {
            foreach (var heroInit in heroInitInfoList)
            {
                var t = heroList.Find((heroInfo) => { return heroInfo.heroId == heroInit.Key; });
                if (t != null)
                {
                    var otherMemSize = t.editorMemoryBytes - t.animMemoryBytes - t.charaTexMemoryBytes - t.effTexMemoryBytes - t.shaderMemoryBytes;
                    var nowMemSize = heroInit.Value;
                    if (!bUseInitMemory)
                    {
                        nowMemSize = t.editorMemoryBytes;
                    }

                    sb.Append("\n");
                    sb.Append(heroInit.Key).Append(",").Append(EditorUtility.FormatBytes(nowMemSize)).Append(",").Append(nowMemSize).Append(",").Append(t.heroName).Append(",");
                    if (animFbxNeedOptimal)
                    {
                        sb.Append(EditorUtility.FormatBytes(t.animMemoryBytes)).Append(",");
                        sb.Append(t.animMemoryBytes).Append(",");
                    }
                    sb.Append(EditorUtility.FormatBytes(t.charaTexMemoryBytes)).Append(",").Append(t.charaTexMemoryBytes).Append(",").Append(EditorUtility.FormatBytes(t.effTexMemoryBytes)).Append(",").Append(t.effTexMemoryBytes);
                }
            }
        }

        return sb.ToString();
    }

    /// <summary>
    /// 生成英雄当前内存比对信息
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroMemoryNowInfoCompare(生成英雄当前内存比对信息)")]
    static void DoGenerateAllHeroNowMemoryInfoCompareFile()
    {
        var fileInitPath = FileUtilitys.GetCurFullPathOnAssets(CHeroMemoryInitSimplePath);
        var fileNowPath = FileUtilitys.GetCurFullPathOnAssets(CHeroMemoryNowSimplePath);

        if(!File.Exists(fileInitPath) || !File.Exists(fileNowPath))
        {
            return;
        }

        var initContentList = GetCsvContent(fileInitPath);
        var nowContentList = GetCsvContent(fileNowPath);
        var nowCnt = nowContentList.Count;
        StringBuilder sb = new StringBuilder();

        for (int i = 0, j = initContentList.Count; i < j; i++)
        {
            if(i <= nowCnt - 1)
            {
                if(i == 0)
                {
                    //sb.Append(initContentList[i]);
                    sb.Append("heroId,").Append("size,sizeBytes,heroName,");
                    if(animFbxNeedOptimal)
                    {
                        sb.Append("animSize,animSizeBytes,");
                    }
                    sb.Append("charaTexSize,charaTexSizeBytes,effectTexSize,effectTexSizeBytes");
                }
                else
                {
                    sb.Append("\n");
                    var iContent = initContentList[i];
                    var iContent0 = GetLineSubContent(iContent, 0);
                    var iContent1 = GetLineSubContent(iContent, 1);
                    var iContent2 = GetLineSubContent(iContent, 2);
                    var iContent3 = GetLineSubContent(iContent, 3);
                    var iContent4 = GetLineSubContent(iContent, 4);
                    var nContent = nowContentList[i];
                    var nContent1 = GetLineSubContent(nContent, 1);
                    var nContent2 = GetLineSubContent(nContent, 2);
                    var nContent4 = GetLineSubContent(nContent, 4);
                    var nContent5 = GetLineSubContent(nContent, 5);
                    var nContent6 = GetLineSubContent(nContent, 6);
                    var nContent7 = GetLineSubContent(nContent, 7);

                    sb.Append(iContent0).Append(",");
                    sb.Append(iContent1).Append("  -->  ").Append(nContent1).Append(",");
                    sb.Append(nContent2).Append(",");
                    sb.Append(iContent2).Append(",");
                    sb.Append(iContent3).Append("  -->  ").Append(nContent4).Append(",");
                    sb.Append(nContent5).Append(",");
                    sb.Append(iContent4).Append("  -->  ").Append(nContent6).Append(",");
                    sb.Append(nContent7);

                    if (animFbxNeedOptimal)
                    {
                        var iContent5 = GetLineSubContent(iContent, 5);
                        var nContent8 = GetLineSubContent(nContent, 8);
                        var nContent9 = GetLineSubContent(nContent, 9);
                        sb.Append(",");
                        sb.Append(iContent5).Append("  -->  ").Append(nContent8).Append(",");
                        sb.Append(nContent9);
                    }
                }
            }
        }

        if(sb.Length > 0)
        {
            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroMemoryComparePath);
            File.WriteAllText(filePath, sb.ToString());
        }
    }

    /// <summary>
    /// 生成英雄当前内存占用简化信息
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroMemoryNowSimpleInfo(生成英雄当前内存占用简化信息)")]
    static void DoGenerateAllHeroNowMemorySimpleInfoFile()
    {
        DoGenerateAllHeroMemoryInfoFile(CHeroMemoryNowSimplePath, true, false, true);
    }

    /// <summary>
    /// 生成英雄当前内存占用信息
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroMemoryNowInfo(生成英雄当前内存占用信息)")]
    static void DoGenerateAllHeroNowMemoryInfoFile()
    {
        DoGenerateAllHeroMemoryInfoFile(CHeroMemoryNowPath, true, false, false);
    }

    /// <summary>
    /// 生成HeroInitInfo文件
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroInitInfoFile(生成HeroInitInfo文件)")]
    static void DoGenerateAllHeroInitInfoFile()
    {
        DoCheckConfig();
        List<HeroMemoryInitInfo> heroList = GetAllHeroMemoryInfo();
        if (heroList != null && heroList.Count > 0)
        {
            heroList.Sort((l, r) =>
            {
                var sortSeqCom = l.editorMemoryBytes - r.editorMemoryBytes;
                if (sortSeqCom > 0)
                {
                    return -1;
                }
                else if (sortSeqCom == 0)
                {
                    return 0;
                }
                else
                {
                    return 1;
                }
            });

            StringBuilder sb = new StringBuilder();
            int heroCount = heroList.Count;
            int heroAddIndex = 0;
            foreach (var t in heroList)
            {
                sb.Append(t.heroId).Append(",").Append(t.editorMemoryBytes);
                if(heroAddIndex ++ != heroCount - 1)
                {
                    sb.Append("\n");
                }
            }

            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroInitInfoPath);
            File.WriteAllText(filePath, sb.ToString());
        }
    }

    /// <summary>
    /// 生成英雄初始内存占用信息
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroMemoryInitInfo(生成英雄初始内存占用信息)")]
    static void DoGenerateAllHeroInitMemoryInfoFile()
    {
        List<HeroMemoryInitInfo> heroList = DoGenerateAllHeroMemoryInfoFile(CHeroMemoryInitPath, true, true, false);
        DoGenerateAllHeroInitMemoryInfoSimpleFile(heroList);
        //DoGenerateAllHeroModelTexInfoFile();

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    /// <summary>
    /// 生成英雄初始内存占用简化信息
    /// </summary>
    /// <param name="heroList"></param>
    static void DoGenerateAllHeroInitMemoryInfoSimpleFile(List<HeroMemoryInitInfo> heroList)
    {
        if(heroList != null && heroList.Count > 0)
        {
            List<KeyValuePair<int, long>> heroInitInfoList = new List<KeyValuePair<int, long>>();
            heroInitInfoList = GetHeroInitInfo();
            var strContent = ReturnMemorySimplyDesc(true, true, heroInitInfoList, heroList);

            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroMemoryInitSimplePath);
            File.WriteAllText(filePath, strContent);
        }
    }

    /// <summary>
    /// 生成英雄内存占用信息
    /// </summary>
    /// <param name="exportPath"></param>
    /// <param name="bSortByInit"></param>
    /// <param name="bUseInitMemory"></param>
    /// <param name="bUserSimpleInfo"></param>
    static List<HeroMemoryInitInfo> DoGenerateAllHeroMemoryInfoFile(string exportPath, bool bSortByInit, bool bUseInitMemory, bool bUserSimpleInfo)
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();

        ProcessConfigFullPath();
        ProcessHeroConst();
        ProcessCsvFullPath();
        DoCheckConfig();
        //if (!ProcessLangCsvFullPath())
        //{
        //    return;
        //}

        List<HeroMemoryInitInfo> heroList = GetAllHeroMemoryInfo();
        if (heroList != null && heroList.Count > 0)
        {
            bool bUseSortByInit = false;
            List<KeyValuePair<int, long>> heroInitInfoList = new List<KeyValuePair<int, long>>();
            if(bSortByInit)
            {
                heroInitInfoList = GetHeroInitInfo();
                bUseSortByInit = heroInitInfoList != null && heroInitInfoList.Count > 0;
            }

            if(!bUseSortByInit)
            {
                heroList.Sort((l, r) =>
                {
                    var sortSeqCom = l.editorMemoryBytes - r.editorMemoryBytes;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });
            }

            string strContent = string.Empty;
            if(!bUserSimpleInfo)
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("heroId").Append(",").Append("size").Append(",").Append("heroName").Append(",").Append("animSize").Append(",").Append("animDesc").Append(",").Append("charaTexSize").Append(",").Append("charaTexDesc").Append(",").
                    Append("effectTexSize").Append(",").Append("shaderSize").Append(",").Append("other");

                if (!bUseSortByInit)
                {
                    foreach (var t in heroList)
                    {
                        sb.Append("\n");
                        sb.Append(t.heroId).Append(",").Append(EditorUtility.FormatBytes(t.editorMemoryBytes)).Append(",").Append(t.heroName).Append(",").Append(EditorUtility.FormatBytes(t.animMemoryBytes)).Append(",").Append(t.animMemoryDesc).Append(",")
                            .Append(EditorUtility.FormatBytes(t.charaTexMemoryBytes)).Append(",").Append(t.charaTexMemoryDesc).Append(",").Append(EditorUtility.FormatBytes(t.effTexMemoryBytes)).Append(",")
                            .Append(EditorUtility.FormatBytes(t.shaderMemoryBytes)).Append(",").Append(EditorUtility.FormatBytes(t.otherMemoryBytes));
                    }
                }
                else
                {
                    foreach (var heroInit in heroInitInfoList)
                    {
                        var t = heroList.Find((heroInfo) => { return heroInfo.heroId == heroInit.Key; });
                        if (t != null)
                        {
                            var otherMemSize = t.editorMemoryBytes - t.animMemoryBytes - t.charaTexMemoryBytes - t.effTexMemoryBytes - t.shaderMemoryBytes;
                            var nowMemSize = heroInit.Value;
                            if (!bUseInitMemory)
                            {
                                nowMemSize = t.editorMemoryBytes;
                            }

                            sb.Append("\n");
                            sb.Append(heroInit.Key).Append(",").Append(EditorUtility.FormatBytes(nowMemSize)).Append(",").Append(t.heroName).Append(",").Append(EditorUtility.FormatBytes(t.animMemoryBytes)).Append(",").Append(t.animMemoryDesc).Append(",")
                                .Append(EditorUtility.FormatBytes(t.charaTexMemoryBytes)).Append(",").Append(t.charaTexMemoryDesc).Append(",").Append(EditorUtility.FormatBytes(t.effTexMemoryBytes)).Append(",")
                                .Append(EditorUtility.FormatBytes(t.shaderMemoryBytes)).Append(",").Append(EditorUtility.FormatBytes(t.otherMemoryBytes));
                        }
                    }
                }

                strContent = sb.ToString();
            }
            else
            {
                strContent = ReturnMemorySimplyDesc(bUseSortByInit, bUseInitMemory, heroInitInfoList, heroList);
            }

            var filePath = FileUtilitys.GetCurFullPathOnAssets(exportPath);
            File.WriteAllText(filePath, strContent);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();

        return heroList;
    }

    /// <summary>
    /// 获取所有英雄内存信息
    /// </summary>
    /// <returns></returns>
    static List<HeroMemoryInitInfo> GetAllHeroMemoryInfo()
    {
        var heroContens = GetHeroResDependContent();
        List<HeroMemoryInitInfo> heroList = new List<HeroMemoryInitInfo>();
        Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic = new Dictionary<string, List<FileMemoryInfo>>();
        foreach (var t in heroContens)
        {
            var heroId = t.Key;
            var heroName = t.Value.heroName;
            var heroDepends = t.Value.heroDepends;
            heroMemoryTypeInfoDic.Clear();

            if (heroDepends.Count > 0)
            {
                var memSize = GetAllDepsMemorySize(heroDepends, heroMemoryTypeInfoDic);
                long aniMemSize = 0;
                var animKey = EMemoryInfo.Anim.ToString();
                var animDesc = string.Empty;
                aniMemSize = GetMemoryInfo(heroMemoryTypeInfoDic, animKey, ref animDesc);
                long charaTexMemSize = 0;
                var charaTexKey = EMemoryInfo.CharacterTex.ToString();
                var charaTexDesc = string.Empty;
                charaTexMemSize = GetMemoryInfo(heroMemoryTypeInfoDic, charaTexKey, ref charaTexDesc);
                long effectTexMemSize = 0;
                var effectTexKey = EMemoryInfo.EffectTex.ToString();
                var effectTexDesc = string.Empty;
                effectTexMemSize = GetMemoryInfo(heroMemoryTypeInfoDic, effectTexKey, ref effectTexDesc);
                long shaderMemSize = 0;
                var shaderKey = EMemoryInfo.Shader.ToString();
                var shaderDesc = string.Empty;
                shaderMemSize = GetMemoryInfo(heroMemoryTypeInfoDic, shaderKey, ref shaderDesc);
                var otherMemSize = memSize - aniMemSize - charaTexMemSize - effectTexMemSize - shaderMemSize;

                heroList.Add(new HeroMemoryInitInfo() { heroId = heroId, heroName = heroName, editorMemoryBytes = memSize, animMemoryBytes = aniMemSize, animMemoryDesc = animDesc, charaTexMemoryBytes = charaTexMemSize, charaTexMemoryDesc = charaTexDesc, effTexMemoryBytes = effectTexMemSize, effTexMemoryDesc = effectTexDesc, shaderMemoryBytes = shaderMemSize, shaderMemoryDesc = shaderDesc, otherMemoryBytes = otherMemSize });
            }
        }

        return heroList;
    }

    /// <summary>
    /// 获取内存信息
    /// </summary>
    /// <param name="heroMemoryTypeInfoDic"></param>
    /// <param name="strKey"></param>
    /// <param name="strDesc"></param>
    /// <returns></returns>
    static long GetMemoryInfo(Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic, string strKey, ref string strDesc)
    {
        long memSize = 0;
        strDesc = string.Empty;
        StringBuilder sb = new StringBuilder();
        if (heroMemoryTypeInfoDic.TryGetValue(strKey, out var resultList))
        {
            resultList.Sort((l, r) =>
            {
                var sortSeqCom = l.fileSize - r.fileSize;
                if (sortSeqCom > 0)
                {
                    return -1;
                }
                else if (sortSeqCom == 0)
                {
                    return 0;
                }
                else
                {
                    return 1;
                }
            });

            var resultCount = resultList.Count;
            var addIndex = 0;
            sb.Append("\"");
            foreach (var item in resultList)
            {
                addIndex++;
                var fileSize = item.fileSize;
                memSize += fileSize;
                sb.Append(item.fileName).Append(":").Append(EditorUtility.FormatBytes(fileSize)).Append(item.extraDesc);
                if (addIndex != resultCount)
                {
                    sb.Append("\n");
                }
            }

            sb.Append("\"");
            strDesc = sb.ToString();
        }

        return memSize;
    }

    /// <summary>
    /// 获取所有依赖内存占用大小
    /// </summary>
    /// <param name="resList"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    /// <returns></returns>
    static long GetAllDepsMemorySize(List<string> resList, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        long memorySize = 0;
        List<string> resDeresList = new List<string>();
        foreach (var path in resList)
        {
            memorySize += GetAllDepsMemorySize(resDeresList, ("Assets/" + path).Replace("\\", "/"), heroMemoryTypeInfoDic);
        }

        return memorySize;
    }

    /// <summary>
    /// 获取所有依赖内存占用大小
    /// </summary>
    /// <param name="resDepends"></param>
    /// <param name="path"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    /// <returns></returns>
    static long GetAllDepsMemorySize(List<string> resDepends, string path, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        long memorySize = 0;
        UnityEngine.Object[] objects = EditorUtility.CollectDependencies(AssetDatabase.LoadAllAssetsAtPath(path));
        if(objects != null)
        {
            for (int i = 0; i < objects.Length; i++)
            {
                if(objects[i] != null)
                {
                    string assetPath = AssetDatabase.GetAssetPath(objects[i]);
                    if (!string.IsNullOrEmpty(assetPath) && !resDepends.Contains(assetPath))
                    {
                        var cMemorySize = EditorUtilitys.FileUtilitys.GetRuntimeMemorySizeLong(objects[i], calRuntimeMemoryType);
                        memorySize += cMemorySize;
                        resDepends.Add(assetPath);

                        ProcessHeroMemoryTypeInfo(assetPath, cMemorySize, heroMemoryTypeInfoDic);
                    }
                }
            }
        }

        return memorySize;
    }

    /// <summary>
    /// 处理英雄内存类型信息
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="memorySize"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    static void ProcessHeroMemoryTypeInfo(string assetPath, long memorySize, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        ProcessHeroAnimMemoryTypeInfo(assetPath, memorySize, heroMemoryTypeInfoDic);
        ProcessHeroModelTexMemoryTypeInfo(assetPath, memorySize, heroMemoryTypeInfoDic);
        ProcessHeroEffectTexMemoryTypeInfo(assetPath, memorySize, heroMemoryTypeInfoDic);
        ProcessHeroShaderMemoryTypeInfo(assetPath, memorySize, heroMemoryTypeInfoDic);
    }

    /// <summary>
    /// 处理英雄动画类型内存信息
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="memorySize"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    static void ProcessHeroAnimMemoryTypeInfo(string assetPath, long memorySize, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        if (assetPath.EndsWith(CAnimationExtension))
        {
            if (IsCharacterAnimPath(assetPath)/*&& !IsFilterAniName(assetPath)*/)
            {
                var animKey = EMemoryInfo.Anim.ToString();
                if (!heroMemoryTypeInfoDic.TryGetValue(animKey, out var result))
                {
                    heroMemoryTypeInfoDic[animKey] = new List<FileMemoryInfo>();
                }

                heroMemoryTypeInfoDic[animKey].Add(new AnimFileMemoryInfo() { fileName = Path.GetFileNameWithoutExtension(assetPath), fileSize = memorySize });
            }
        }
    }

    /// <summary>
    /// 处理英雄角色贴图类型内存信息
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="memorySize"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    static void ProcessHeroModelTexMemoryTypeInfo(string assetPath, long memorySize, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        if (FileUtilitys.IsTexturePath(assetPath))
        {
            if (IsCharacterModelTexturePath(assetPath))
            {
                var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
                var charaTexKey = EMemoryInfo.CharacterTex.ToString();
                if (!heroMemoryTypeInfoDic.TryGetValue(charaTexKey, out var result))
                {
                    heroMemoryTypeInfoDic[charaTexKey] = new List<FileMemoryInfo>();
                }

                heroMemoryTypeInfoDic[charaTexKey].Add(new CharacterTexMemoryInfo() { fileName = Path.GetFileNameWithoutExtension(assetPath), fileSize = memorySize, extraDesc = $"({t2d.width}x{t2d.height})"});
            }
        }
    }

    /// <summary>
    /// 处理英雄技能特效贴图类型内存信息
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="memorySize"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    static void ProcessHeroEffectTexMemoryTypeInfo(string assetPath, long memorySize, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        if (IsEffectTexPath(assetPath))
        {
            var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
            var effectTexKey = EMemoryInfo.EffectTex.ToString();
            if (!heroMemoryTypeInfoDic.TryGetValue(effectTexKey, out var result))
            {
                heroMemoryTypeInfoDic[effectTexKey] = new List<FileMemoryInfo>();
            }

            heroMemoryTypeInfoDic[effectTexKey].Add(new CharacterTexMemoryInfo() { fileName = Path.GetFileNameWithoutExtension(assetPath), fileSize = memorySize, extraDesc = $"({t2d.width}x{t2d.height})" });
        }
    }

    /// <summary>
    /// 处理英雄Shader类型内存信息
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="memorySize"></param>
    /// <param name="heroMemoryTypeInfoDic"></param>
    static void ProcessHeroShaderMemoryTypeInfo(string assetPath, long memorySize, Dictionary<string, List<FileMemoryInfo>> heroMemoryTypeInfoDic)
    {
        if (IsShaderPath(assetPath))
        {
            var shaderKey = EMemoryInfo.Shader.ToString();
            if (!heroMemoryTypeInfoDic.TryGetValue(shaderKey, out var result))
            {
                heroMemoryTypeInfoDic[shaderKey] = new List<FileMemoryInfo>();
            }

            heroMemoryTypeInfoDic[shaderKey].Add(new ShaderMemoryInfo() { fileName = Path.GetFileNameWithoutExtension(assetPath), fileSize = memorySize });
        }
    }

    /// <summary>
    /// 获取英雄初始化信息
    /// </summary>
    /// <returns></returns>
    static List<KeyValuePair<int, long>> GetHeroInitInfo()
    {
        List<KeyValuePair<int, long>> heroInitInfoList = new List<KeyValuePair<int, long>>();

        var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroInitInfoPath);
        using (StreamReader ReaderObject = new StreamReader(filePath))
        {
            string line;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                if (line.StartsWith("#"))
                {//注释行
                    continue;
                }

                var contents = line.Split(',');
                if (contents != null && contents.Length >= 2)
                {
                    var strKey = contents[0];
                    var strContent = contents[1];
                    try
                    {
                        if (!string.IsNullOrEmpty(strKey) && !string.IsNullOrEmpty(strContent))
                        {
                            if(int.TryParse(strKey, out var heroId) && long.TryParse(strContent, out var memSize))
                            {
                                heroInitInfoList.Add(new KeyValuePair<int, long>(heroId, memSize));
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError("HeroResOptimal.GetHeroInitInfo()捕获到异常:" + e.ToString());
                    }
                }
            }
        }

        return heroInitInfoList;
    }
}

