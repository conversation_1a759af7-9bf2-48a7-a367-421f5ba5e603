/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.IO;
using System;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 检测项目标识
    /// </summary>
    /// <param name="strContent"></param>
    static void CheckProjectMarkConfig(string strContent)
    {
        if(int.TryParse(strContent, out var result))
        {
            bool isDefined = Enum.IsDefined(typeof(EProjectMark), result);
            if (isDefined)
            {
                projectMarkE = (EProjectMark)result;
            }
        }
    }

    /// <summary>
    /// 检测计算内存类型
    /// </summary>
    /// <param name="strContent"></param>
    static void CheckCalMemoryTypeConfig(string strContent)
    {
        if (int.TryParse(strContent, out var result))
        {
            bool isDefined = Enum.IsDefined(typeof(EditorUtilitys.FileUtilitys.ECalRuntimeMemoryType), result);
            if (isDefined)
            {
                calRuntimeMemoryType = (EditorUtilitys.FileUtilitys.ECalRuntimeMemoryType)result;
            }
        }
    }

    /// <summary>
    /// 检测特殊特效目录
    /// </summary>
    /// <param name="strContent"></param>
    static void CheckEffectDirSpecial(string strContent)
    {
        heroSpeEffResOptimalList.Clear();
        var cContent = strContent.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
        if(cContent != null)
        {
            foreach(var t in cContent)
            {
                var tArr = t.Split(new char[] {','}, StringSplitOptions.RemoveEmptyEntries);
                if(tArr != null && tArr.Length >= 2)
                {
                    var path = tArr[0];
                    var subContent = t.Substring(t.IndexOf(',') + 1);
                    var subContentArr = subContent.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    if(subContentArr != null)
                    {
                        HeroEffResOptimalInfo vo = new HeroEffResOptimalInfo();
                        vo.effectDir = path;
                        bool bFindValid = false;
                        foreach (var g in subContentArr)
                        {
                            var gContentArr = g.Split(new char[] { '&' }, StringSplitOptions.RemoveEmptyEntries);
                            if(gContentArr != null && gContentArr.Length >= 2)
                            {
                                if(int.TryParse(gContentArr[1], out var result))
                                {
                                    bFindValid = true;
                                    vo.effectItems.Add(new HeroEffResOptimalItem() { effectNamePrefix = gContentArr[0], effectTexSize = result });
                                }
                            }
                        }

                        if(bFindValid)
                        {
                            heroSpeEffResOptimalList.Add(vo);
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 是否是角色特效贴图特殊路径
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static bool IsCharacterEffectTextureSpecialPath(string path, ref int texSize)
    {
        var pathL = path.ToLower();
        var fileNameL = Path.GetFileName(pathL);
        texSize = 0;

        foreach (var t in heroSpeEffResOptimalList)
        {
            var lDir = t.effectDir.ToLower();
            if(pathL.Contains(lDir))
            {
                var effectItems = t.effectItems;
                if(effectItems != null)
                {
                    foreach(var g in effectItems)
                    {
                        if(fileNameL.Contains(g.effectNamePrefix.ToLower()))
                        {
                            texSize = g.effectTexSize;
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }
}

