/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using System.Text;
using static CSVParser;
using EditorUtilitys;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 处理LangCsv文件路径
    /// </summary>
    static bool ProcessLangCsvFullPath()
    {
        var cList = GetReadFileContentOnKey(configFullPath, CLangCsvPathKey);
        if (cList != null && cList.Count > 0)
        {
            var strContent = cList[0];
            if (!string.IsNullOrEmpty(strContent))
            {
                var strDir = Path.GetFullPath(strContent).Replace("\\", "/");
                if (File.Exists(strDir))
                {
                    csvLangFullPath = strDir;
                    return true;
                }
            }
        }

        UnityEngine.Debug.LogError("【英雄资源优化】,langCsv路径不存在!!!!!!");
        return false;
    }

    /// <summary>
    /// 获取多语言配置
    /// </summary>
    /// <param name="rc"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    static List<RecordData> GetLangConfig(RecordFile rc, int id)
    {
        List<RecordData> cfgs = new List<RecordData>();
        if(rc != null)
        {
            cfgs = rc.GetRecord("nKey", id.ToString());
        }

        return cfgs;
    }

    /// <summary>
    /// 获取中文多语言描述
    /// </summary>
    /// <param name="rc"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    static string GetChLangDesc(RecordFile rc, int id)
    {
        List<RecordData> cfgs = GetLangConfig(rc, id);
        if (cfgs != null && cfgs.Count > 0)
        {
            return cfgs[0]["zh"];
        }

        return string.Empty;
    }

    /// <summary>
    /// 生成英雄名字文件
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/GenerateHeroName(英雄名字文件生成)")]
    static void DoGenerateAllHeroNamesFile()
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();

        ProcessConfigFullPath();
        ProcessHeroConst();
        ProcessCsvFullPath();
        if(!ProcessLangCsvFullPath())
        {
            return;
        }

        //加载多语言表
        var csvLangRc = CSVParser.Load(csvLangFullPath);
        if(csvLangRc == null)
        {
            return;
        }

        List<int> heroIDList = new List<int>();
        StringBuilder sb = new StringBuilder();
        int addIndex = 0;
        for (int i = 1, j = heroIDMax; i <= j; i++)
        {
            var heroCfg = GetHeroConfig(i);
            if (heroCfg != null && heroCfg.Count > 0)
            {
                if(!heroIDList.Contains(i))
                {
                    AddUniqueInt(heroIDList, i);

                    if (addIndex++ != 0)
                    {
                        sb.Append("\n");
                    }

                    int heroNameID = 0;
                    if (int.TryParse(heroCfg[0]["HeroNameID"], out var heroNameIDResult))
                    {
                        heroNameID = heroNameIDResult;
                    }

                    var heroName = GetChLangDesc(csvLangRc, heroNameID);
                    sb.Append(i + "=").Append(heroName);

                    var rcList = GetSkinConfig(i);
                    if (rcList != null)
                    {
                        int idx = 1;
                        foreach (var t in rcList)
                        {
                            var strID = t["ID"];
                            if (!string.IsNullOrEmpty(strID) && int.TryParse(strID, out var IdResult) && int.TryParse(t["SkinNameID"], out var skinNameID))
                            {
                                sb.Append("\n");
                                sb.Append(IdResult + "=").Append(heroName + "(皮肤" + idx + "_" + GetChLangDesc(csvLangRc, skinNameID) + ")");
                                idx++;
                            }
                        }
                    }
                }
            }
        }

        if(heroIDList.Count > 0)
        {
            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroNamePath);
            File.WriteAllText(filePath, sb.ToString());
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    ///// <summary>
    ///// 刷新英雄皮肤名字
    ///// </summary>
    //[MenuItem("Tool/HeroResOptimal/RefreshHeroSkinName(刷新英雄皮肤名字)")]
    //static void DoRefreshAllHeroSkinNames()
    //{
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();

    //    ProcessConfigFullPath();
    //    ProcessHeroConst();
    //    ProcessCsvFullPath();
    //    if (!ProcessLangCsvFullPath())
    //    {
    //        return;
    //    }

    //    //加载多语言表
    //    var csvLangRc = CSVParser.Load(csvLangFullPath);
    //    if (csvLangRc == null)
    //    {
    //        return;
    //    }

    //    var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroNamePath);
    //    if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
    //    {
    //        return;
    //    }

    //    StringBuilder sb = new StringBuilder();
    //    int addIndex = 0;
    //    using (StreamReader ReaderObject = new StreamReader(filePath))
    //    {
    //        string line;
    //        while ((line = ReaderObject.ReadLine()) != null)
    //        {
    //            if (line.StartsWith("#"))
    //            {//注释行
    //                continue;
    //            }

    //            var contents = line.Split('=');
    //            if (contents != null && contents.Length >= 2)
    //            {
    //                var strKey = contents[0];
    //                var strContent = contents[1];
    //                try
    //                {
    //                    if (!string.IsNullOrEmpty(strKey) && int.TryParse(strKey, out var heroID))
    //                    {
    //                        string content = strContent;

    //                        if (heroID >= 10000)
    //                        {//皮肤
    //                            var rcList = GetSkinConfigOnID(heroID);
    //                            if(rcList != null)
    //                            {
    //                                foreach (var t in rcList)
    //                                {
    //                                    var strID = t["ID"];
    //                                    var strHeroID = t["HeroID"];
    //                                    if (!string.IsNullOrEmpty(strID) && int.TryParse(strID, out var IdResult)
    //                                        && !string.IsNullOrEmpty(strHeroID) && int.TryParse(strHeroID, out var heroIdResult))
    //                                    {

    //                                    }
    //                                }
    //                            }
    //                        }

    //                        if (addIndex++ != 0)
    //                        {
    //                            var rcList = sb.Append("\n");
    //                        }
    //                    }
    //                }
    //                catch (System.Exception e)
    //                {
    //                    Debug.LogError("HeroResOptimal.DoRefreshAllHeroSkinNames()捕获到异常:" + e.ToString());
    //                }
    //            }
    //        }
    //    }

    //    AssetDatabase.SaveAssets();
    //    AssetDatabase.Refresh();
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();
    //}
}

