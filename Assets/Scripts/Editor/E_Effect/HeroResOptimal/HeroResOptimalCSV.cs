/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;
using System.IO;
using static CSVParser;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 英雄资源相关csv表
    /// </summary>
    static List<string> heroResRelatedCsvs = new List<string>() { CHeroCsvName, CHeroUpgradeCsvName, CHeroSkillCsvName, CSkillCsvName, CBuffCsvName, CParticleCsvName, CEffectCsvName, CSkinCsvName, CModulCsvName, CSkinSkillCsvName };
    /// <summary>
    /// 英雄表数据
    /// </summary>
    static CSVParser.RecordFile heroRecordFile;
    /// <summary>
    /// 英雄升级表数据
    /// </summary>
    static CSVParser.RecordFile heroUpgradeRecordFile;
    /// <summary>
    /// 英雄技能表数据
    /// </summary>
    static CSVParser.RecordFile heroSkillRecordFile;
    /// <summary>
    /// 技能表数据
    /// </summary>
    static CSVParser.RecordFile skillRecordFile;
    /// <summary>
    /// buff数据
    /// </summary>
    static CSVParser.RecordFile buffRecordFile;
    /// <summary>
    /// 特效表数据
    /// </summary>
    static CSVParser.RecordFile particleRecordFile;
    /// <summary>
    /// 效果表数据
    /// </summary>
    static CSVParser.RecordFile effectRecordFile;
    /// <summary>
    /// 皮肤表数据
    /// </summary>
    static CSVParser.RecordFile skinRecordFile;
    /// <summary>
    /// 模型表数据
    /// </summary>
    static CSVParser.RecordFile modulRecordFile;
    /// <summary>
    /// 皮肤技能表数据
    /// </summary>
    static CSVParser.RecordFile skinSkillRecordFile;

    /// <summary>
    /// svn更新csv目录
    /// </summary>
    static void SvnUpdateCsvFullPath()
    {
        if(!EditorUtilitys.SvnUtilitys.DoSvnUpdate(csvFullPath))
        {
            UnityEngine.Debug.LogError($"【英雄资源优化】svn更新{csvFullPath}异常!!!!!!");
        }
    }

    /// <summary>
    /// 检测英雄相关csv表
    /// </summary>
    static bool CheckHeroCsvs()
    {
        foreach(var t in heroResRelatedCsvs)
        {
            var csvPath = (csvFullPath + t).Replace("\\", "/");
            if(!File.Exists(csvPath))
            {
                UnityEngine.Debug.LogError($"【英雄资源优化】{csvPath}不存在!!!!!!");
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 检测配置
    /// </summary>
    /// <param name="csvName"></param>
    /// <param name="csvFile"></param>
    static void CheckConfig(string csvName, ref RecordFile csvFile)
    {
        if (csvFile == null)
        {
            //加载英雄表
            var csvFullPathTmp = Path.GetFullPath(csvFullPath + csvName).Replace("\\", "/");
            csvFile = CSVParser.Load(csvFullPathTmp);
        }
    }

    /// <summary>
    /// 获取英雄配置
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static List<RecordData> GetHeroConfig(int heroID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CHeroCsvName, ref heroRecordFile);

        if (heroRecordFile == null)
        {
            return cfgs;
        }

        return heroRecordFile.GetRecord("heroID", heroID.ToString());
    }

    /// <summary>
    /// 获取英雄升级配置
    /// </summary>
    /// <param name="heroLev"></param>
    /// <returns></returns>
    static List<RecordData> GetHeroUpgradeConfig(int heroLev)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CHeroUpgradeCsvName, ref heroUpgradeRecordFile);

        if (heroUpgradeRecordFile == null)
        {
            return cfgs;
        }

        return heroUpgradeRecordFile.GetRecord("heroLv", heroLev.ToString());
    }

    /// <summary>
    /// 获取英雄技能配置
    /// </summary>
    /// <param name="heroSkillID"></param>
    /// <returns></returns>
    static List<RecordData> GetHeroSkillConfig(int heroSkillID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CHeroSkillCsvName, ref heroSkillRecordFile);

        if (heroSkillRecordFile == null)
        {
            return cfgs;
        }

        return heroSkillRecordFile.GetRecord("heroSkillID", heroSkillID.ToString());
    }

    /// <summary>
    /// 获取英雄技能配置
    /// </summary>
    /// <param name="heroSkillID"></param>
    /// <param name="skillLev"></param>
    /// <returns></returns>
    static List<RecordData> GetHeroSkillConfig(int heroSkillID, int skillLev)
    {
        List<RecordData> cfgs = new List<RecordData>();
        var cfgDatas = GetHeroSkillConfig(heroSkillID);

        var strSkillLev = skillLev.ToString();
        if (cfgDatas != null)
        {
            foreach(var t in cfgDatas)
            {
                if (t["Lv"] == strSkillLev)
                {
                    cfgs.Add(t);
                }
            }
        }

        return cfgs;
    }

    /// <summary>
    /// 获取buff配置
    /// </summary>
    /// <param name="buffID"></param>
    /// <returns></returns>
    static List<RecordData> GetBuffConfig(int buffID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CBuffCsvName, ref buffRecordFile);

        if (buffRecordFile == null)
        {
            return cfgs;
        }

        return buffRecordFile.GetRecord("unBuffID", buffID.ToString());
    }

    /// <summary>
    /// 获取buff配置
    /// </summary>
    /// <param name="buffID"></param>
    /// <param name="buffLev"></param>
    /// <returns></returns>
    static List<RecordData> GetBuffConfig(int buffID, int buffLev)
    {
        List<RecordData> cfgs = new List<RecordData>();
        var cfgDatas = GetBuffConfig(buffID);

        var strBuffLev = buffLev.ToString();
        if (cfgDatas != null)
        {
            foreach (var t in cfgDatas)
            {
                if (t["unBuffLevel"] == strBuffLev)
                {
                    cfgs.Add(t);
                }
            }
        }

        return cfgs;
    }

    /// <summary>
    /// 获取技能配置
    /// </summary>
    /// <param name="skillID"></param>
    /// <returns></returns>
    static List<RecordData> GetSkillConfig(int skillID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CSkillCsvName, ref skillRecordFile);

        if (skillRecordFile == null)
        {
            return cfgs;
        }

        return skillRecordFile.GetRecord("nSkillID", skillID.ToString());
    }

    /// <summary>
    /// 获取英雄技能
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static List<int> GetHeroSkillsID(int heroID)
    {
        List<int> skillList = new List<int>();

        var rcList = GetHeroConfig(heroID);
        if(rcList != null)
        {
            foreach(var t in rcList)
            {
                var heroSkillIds = t["heroSkillId"];
                if (string.IsNullOrEmpty(heroSkillIds))
                {
                    continue;
                }

                var heroSkillArr = heroSkillIds.Split('#');
                if(heroSkillArr != null)
                {
                    foreach (var g in heroSkillArr)
                    {
                        if(!string.IsNullOrEmpty(g) && int.TryParse(g, out var result))
                        {
                            skillList.Add(result);
                        }
                    }
                }
            }
        }

        return skillList;
    }

    /// <summary>
    /// 获取当前英雄等级的技能等级
    /// </summary>
    /// <param name="heroID"></param>
    /// <param name="heroLv"></param>
    /// <returns></returns>
    static List<int> GetHeroSkillsLv(int heroID, int heroLv)
    {
        List<int> skillsLv = new List<int>();

        int quality = GetHeroHandBook(heroID);
        var heroUpgradeCfg = GetHeroUpgradeConfig(heroLv);
        if(heroUpgradeCfg != null && heroUpgradeCfg.Count > 0)
        {
            var strBornSkillLv = heroUpgradeCfg[0]["strBornSkillLv"];
            if (!string.IsNullOrEmpty(strBornSkillLv))
            {
                var arrProp = strBornSkillLv.Split(';');
                if(arrProp != null && arrProp.Length - 1 >= quality)
                {
                    var strProp = arrProp[quality];
                    if(!string.IsNullOrEmpty(strProp))
                    {
                        var IdArr = strProp.Split('#');
                        if (IdArr != null)
                        {
                            foreach (var g in IdArr)
                            {
                                if (!string.IsNullOrEmpty(g) && int.TryParse(g, out var result))
                                {
                                    skillsLv.Add(result);
                                }
                            }
                        }
                    }
                }
            }
        }

        return skillsLv;
    }

    /// <summary>
    /// 根据英雄ID获取英雄品质，分为三种 普通、传说、神话
    /// 12-16修改handBook改为tFlag  1~3:取第三列  4、5：取第二列 6：取第一列
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static int GetHeroHandBook(int heroID)
    {
        int index = 1;
        var rcList = GetHeroConfig(heroID);
        if (rcList != null && rcList.Count > 0)
        {
            var strtFlag = rcList[0]["tFlag"];
            if (!string.IsNullOrEmpty(strtFlag))
            {
                if(int.TryParse(strtFlag, out var result))
                {
                    if (result == 6)
                    {
                        index = 1;
                    }
                    else if (result == 4 || result == 5)
                    {
                        index = 2;
                    }
                    else if (result <= 3)
                    {
                        index = 3;
                    }
                }
            }
        }

        return index - 1;
    }

    /// <summary>
    /// 获取特效配置
    /// </summary>
    /// <param name="particleID"></param>
    /// <returns></returns>
    static List<RecordData> GetParticleConfig(int particleID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CParticleCsvName, ref particleRecordFile);

        if (particleRecordFile == null)
        {
            return cfgs;
        }

        return particleRecordFile.GetRecord("nParticleID", particleID.ToString());
    }

    /// <summary>
    /// 获取效果配置
    /// </summary>
    /// <param name="effectID"></param>
    /// <returns></returns>
    static List<RecordData> GetEffectConfig(int effectID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CEffectCsvName, ref effectRecordFile);

        if (effectRecordFile == null)
        {
            return cfgs;
        }

        return effectRecordFile.GetRecord("nEffectID", effectID.ToString());
    }

    /// <summary>
    /// 获取buff特效资源
    /// </summary>
    /// <param name="buffCfg"></param>
    /// <param name="particleType"></param>
    /// <param name="particleRes"></param>
    static void GetBuffFxData(RecordData buffCfg, out int particleType, out string particleRes)
    {
        particleType = 0;
        particleRes = string.Empty;

        if (buffCfg != null)
        {
            var unLightEfficiencyID = buffCfg["unLightEfficiencyID"];
            if(!string.IsNullOrEmpty(unLightEfficiencyID))
            {
                if(int.TryParse(unLightEfficiencyID, out var result))
                {
                    if(result != 0)
                    {
                        GetParticle(result, out particleType, out particleRes);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取特效
    /// </summary>
    /// <param name="id"></param>
    /// <param name="particleType"></param>
    /// <param name="particleRes"></param>
    static void GetParticle(int id, out int particleType, out string particleRes)
    {
        particleType = 0;
        particleRes = string.Empty;

        var cfgs = GetParticleConfig(id);
        if(cfgs != null && cfgs.Count > 0)
        {
            var particle = cfgs[0];
            var strResPath = particle["strResPath"];
            if(!string.IsNullOrEmpty(strResPath))
            {
                particleRes = strResPath;
                particleType = int.Parse(particle["nType"]);
            }
        }
    }

    /// <summary>
    /// 获取皮肤配置
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static List<RecordData> GetSkinConfig(int heroID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CSkinCsvName, ref skinRecordFile);

        if (skinRecordFile == null)
        {
            return cfgs;
        }

        return skinRecordFile.GetRecord("HeroID", heroID.ToString());
    }

    /// <summary>
    /// 获取皮肤配置
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static List<RecordData> GetSkinConfig(int heroID, int skinIndex)
    {
        List<RecordData> cfgs = new List<RecordData>();
        var cfgDatas = GetSkinConfig(heroID);

        if (cfgDatas != null && cfgDatas.Count - 1 >= skinIndex)
        {
            cfgs.Add(cfgDatas[skinIndex]);
        }

        return cfgs;
    }

    /// <summary>
    /// 获取皮肤配置
    /// </summary>
    /// <param name="heroID"></param>
    /// <returns></returns>
    static List<RecordData> GetSkinConfigOnID(int ID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CSkinCsvName, ref skinRecordFile);

        if (skinRecordFile == null)
        {
            return cfgs;
        }

        return skinRecordFile.GetRecord("ID", ID.ToString());
    }

    /// <summary>
    /// 获取皮肤配置
    /// </summary>
    /// <param name="skinIndex"></param>
    /// <returns></returns>
    static List<RecordData> GetSkinConfigOnIndex(int skinIndex)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CSkinCsvName, ref skinRecordFile);

        if (skinRecordFile == null)
        {
            return cfgs;
        }

        return skinRecordFile.GetRecord("Index", skinIndex.ToString());
    }

    /// <summary>
    /// 获取皮肤配置
    /// </summary>
    /// <param name="heroID"></param>
    /// <param name="heroSkinID"></param>
    /// <returns></returns>
    static RecordData GetSkinConfigOnHeroSkinID(int heroID, int heroSkinID)
    {
        RecordData cfg = null;
        CheckConfig(CSkinCsvName, ref skinRecordFile);

        if (skinRecordFile == null)
        {
            return cfg;
        }

        var rcList = skinRecordFile.GetRecord("HeroID", heroID.ToString());
        if (rcList != null && rcList.Count > 0)
        {
            foreach (var t in rcList)
            {
                var strID = t["ID"];
                if (int.TryParse(strID, out var ID))
                {
                    if (ID == heroSkinID)
                    {
                        cfg = t;
                        break;
                    }
                }
            }
        }

        return cfg;
    }

    /// <summary>
    /// 变更英雄模型
    /// </summary>
    /// <param name="heroID"></param>
    /// <param name="starLv"></param>
    /// <param name="ignoreSkin"></param>
    /// <param name="skinIndex"></param>
    /// <returns></returns>
    static string ChangeHeroModel(int heroID, int starLv, bool ignoreSkin, int skinIndex)
    {
        string currentModel = string.Empty;
        var cfgheros = GetHeroConfig(heroID);
        if(!ignoreSkin)
        {
            //var skinConfigs = GetSkinConfig(heroID, skinIndex);
            var skinConfigs = GetSkinConfigOnIndex(skinIndex);
            if (skinConfigs != null && skinConfigs.Count > 0)
            {
                var skinConfig = skinConfigs[0];
                string strModelID = skinConfig["ModelID"];
                if(!string.IsNullOrEmpty(strModelID))
                {
                    currentModel = strModelID;
                }
            }
        }

        if (string.IsNullOrEmpty(currentModel) && cfgheros != null && cfgheros.Count > 0)
        {
            var strModelID = cfgheros[0]["modelID"];
            if (!string.IsNullOrEmpty(strModelID))
            {
                var dataArr = strModelID.Split('#');
                if (dataArr != null && dataArr.Length >= 3)
                {
                    if (starLv < (int)Hero_Star.Yellow)
                    {
                        currentModel = dataArr[0];
                    }
                    else if (starLv >= (int)Hero_Star.Yellow && starLv >= (int)Hero_Star.RedPlus)
                    {
                        currentModel = dataArr[1];
                    }
                    else
                    {
                        currentModel = dataArr[2];
                    }
                }
            }
        }

        return currentModel;
    }

    /// <summary>
    /// 获取模型配置
    /// </summary>
    /// <param name="modelID"></param>
    /// <returns></returns>
    static List<RecordData> GetModulConfig(int modelID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CModulCsvName, ref modulRecordFile);

        if (modulRecordFile == null)
        {
            return cfgs;
        }

        return modulRecordFile.GetRecord("modelID", modelID.ToString());
    }

    /// <summary>
    /// 获取模型配置
    /// </summary>
    /// <param name="modelID"></param>
    /// <returns></returns>
    static List<RecordData> GetSkinSkillConfig(int skillID)
    {
        List<RecordData> cfgs = new List<RecordData>();
        CheckConfig(CSkinSkillCsvName, ref skinSkillRecordFile);

        if (skinSkillRecordFile == null)
        {
            return cfgs;
        }

        return skinSkillRecordFile.GetRecord("nSkinSkillID", skillID.ToString());
    }
}

