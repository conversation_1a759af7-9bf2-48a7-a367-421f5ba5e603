/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;
using System;
using Sirenix.OdinInspector.Demos;
using System.Text;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 英雄模型贴图信息
    /// </summary>
    public class HeroModelTexInfo
    {
        /// <summary>
        /// 资源路径
        /// </summary>
        public string assetPath;
        ///// <summary>
        ///// 优化标志
        ///// </summary>
        //public bool optimalFlag;
        ///// <summary>
        ///// 优化贴图大小
        ///// </summary>
        //public int optimalTexSize;
    }

    /// <summary>
    /// 英雄动画资源压缩
    /// </summary>
    public class HeroAniCompressInfo
    {
        /// <summary>
        /// 资源路径
        /// </summary>
        public Dictionary<string, string[]> list;
    }

    /// <summary>
    /// 英雄动画导出大小信息
    /// </summary>
    public class HeroAniExportSizeInfo
    {
        /// <summary>
        /// asset路径
        /// </summary>
        public string assetPath;
        /// <summary>
        /// 文件占用大小
        /// </summary>
        public long fileSize;
        /// <summary>
        /// 内存大小
        /// </summary>
        public long memorySize;
        /// <summary>
        /// 文件占用大小字符串
        /// </summary>
        public string fileSizeStr;
        /// <summary>
        /// 内存大小字符串
        /// </summary>
        public string memorySizeStr;
    }

    /// <summary>
    /// 角色贴图模型优化信息
    /// </summary>
    static Dictionary<int, List<HeroModelTexInfo>> tHeroTexOptInfoDic = null;
    /// <summary>
    /// 角色贴图模型优化路径列表信息
    /// </summary>
    static Dictionary<string, HeroModelTexInfo> heroTexOptInfo = null;
    /// <summary>
    /// 英雄动画资源压缩信息
    /// </summary>
    static Dictionary<string, string[]> tHeroAnimCompressInfoDic = new Dictionary<string, string[]>();
    ///// <summary>
    ///// 生成英雄模型贴图优化信息
    ///// </summary>
    //[MenuItem("Tool/HeroResOptimal/GenerateHeroModelTexOptimalInfo(生成英雄模型贴图信息)")]
    //static void DoGenerateAllHeroModelTexInfoFile()
    //{
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();

    //    DoCheckConfig();

    //    var heroContens = GetHeroResDependContent();
    //    Dictionary<int, List<HeroModelTexInfo>> heroModelTexInfoDic = new Dictionary<int, List<HeroModelTexInfo>>();
    //    foreach (var t in heroContens)
    //    {
    //        var heroId = t.Key;
    //        var heroDepends = t.Value.heroDepends;
    //        if (heroDepends.Count > 0)
    //        {
    //            var depends = GetAllDepsAssetPath(heroDepends);
    //            var modelTexList = GetHeroModelTex(depends);
    //            if (modelTexList.Count > 0)
    //            {
    //                List<HeroModelTexInfo> heroModelTexInfoList = new List<HeroModelTexInfo>();
    //                foreach (var assetpath in modelTexList)
    //                {
    //                    heroModelTexInfoList.Add(new HeroModelTexInfo() { assetPath = assetpath, optimalFlag = false, optimalTexSize = 1024 });
    //                }

    //                heroModelTexInfoDic[heroId] = heroModelTexInfoList;
    //            }
    //        }
    //    }

    //    if (heroModelTexInfoDic != null && heroModelTexInfoDic.Count > 0)
    //    {
    //        //写入文件
    //        string output = Newtonsoft.Json.JsonConvert.SerializeObject(heroModelTexInfoDic, Newtonsoft.Json.Formatting.Indented);
    //        File.WriteAllText(FileUtilitys.GetCurFullPathOnAssets(CHeroModelTexOptimalInfoPath), output);
    //    }

    //    AssetDatabase.SaveAssets();
    //    AssetDatabase.Refresh();
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();
    //}

    ///// <summary>
    ///// 刷新英雄模型贴图优化信息
    ///// </summary>
    //[MenuItem("Tool/HeroResOptimal/RefreshHeroModelTexOptimalInfo(刷新英雄模型贴图信息)")]
    //static void DoRefreshAllHeroModelTexInfoFile()
    //{
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();

    //    DoCheckConfig();

    //    var rawData = GetHeroModelTexOptimalContent();
    //    var heroContens = GetHeroResDependContent();
    //    Dictionary<int, List<HeroModelTexInfo>> heroModelTexInfoDic = new Dictionary<int, List<HeroModelTexInfo>>();
    //    foreach (var t in heroContens)
    //    {
    //        var heroId = t.Key;
    //        var heroDepends = t.Value.heroDepends;
    //        if (heroDepends.Count > 0)
    //        {
    //            var depends = GetAllDepsAssetPath(heroDepends);
    //            var modelTexList = GetHeroModelTex(depends);
    //            if (modelTexList.Count > 0)
    //            {
    //                List<HeroModelTexInfo> heroModelTexInfoList = new List<HeroModelTexInfo>();
    //                foreach (var assetpath in modelTexList)
    //                {
    //                    heroModelTexInfoList.Add(new HeroModelTexInfo() { assetPath = assetpath, optimalFlag = false, optimalTexSize = 1024 });
    //                }

    //                heroModelTexInfoDic[heroId] = heroModelTexInfoList;
    //            }
    //        }
    //    }

    //    RefreshNewInfoData(rawData, heroModelTexInfoDic);
    //    if (heroModelTexInfoDic != null && heroModelTexInfoDic.Count > 0)
    //    {
    //        //写入文件
    //        string output = Newtonsoft.Json.JsonConvert.SerializeObject(heroModelTexInfoDic, Newtonsoft.Json.Formatting.Indented);
    //        File.WriteAllText(FileUtilitys.GetCurFullPathOnAssets(CHeroModelTexOptimalInfoPath), output);
    //    }

    //    AssetDatabase.SaveAssets();
    //    AssetDatabase.Refresh();
    //    System.GC.Collect();
    //    Resources.UnloadUnusedAssets();
    //}

    /// <summary>
    /// 导出英雄动画资源前后比对信息
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/ExportHeroAnimSizeCompareInfo(导出英雄动画资源前后比对信息)")]
    static void DoExportHeroAnimSizeCompareInfo()
    {
        var cList = ParseHeroAniExportSizeInfoContent();
        if (cList == null || cList.Count <= 0)
        {
            return;
        }

        var nowList = GetHeroAniExportSizeInfo();
        if (nowList == null || nowList.Count <= 0)
        {
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.Append("aniPath").Append(",").Append("fileSize").Append(",").Append("memorySize");
        bool bProcessed = false;
        long fileSizeAllBefore = 0;
        long memorySizeAllBefore = 0;
        long fileSizeAllNow = 0;
        long memorySizeAllNow = 0;
        foreach (var t in nowList)
        {
            var aniAssetPathL = t.assetPath.ToLower().Replace("\\", "/");
            var vo = cList.Find((item) => { return item.assetPath.ToLower() == aniAssetPathL; });
            if (vo != null)
            {
                bProcessed = true;
                var fileMbSizeBeforeStr = FormatBytes(vo.fileSize);
                var memoryMbSizeBeforeStr = FormatBytes(vo.memorySize);
                var fileMbSizeNowStr = FormatBytes(t.fileSize);
                var memoryMbSizeNowStr = FormatBytes(t.memorySize);
                fileSizeAllBefore += vo.fileSize;
                memorySizeAllBefore += vo.memorySize;
                fileSizeAllNow += t.fileSize;
                memorySizeAllNow += t.memorySize;

                sb.Append("\n").Append(t.assetPath.Replace("\\", "/")).Append(",").Append(fileMbSizeBeforeStr + "  -->  " + fileMbSizeNowStr).Append(",").Append(memoryMbSizeBeforeStr + "  -->  " + memoryMbSizeNowStr);
            }
        }

        if (bProcessed)
        {
            var fileSizeAllBeforeStr = FormatBytes(fileSizeAllBefore);
            var fileSizeAllNowStr = FormatBytes(fileSizeAllNow);
            var memorySizeAllBeforeStr = FormatBytes(memorySizeAllBefore);
            var memorySizeAllNowStr = FormatBytes(memorySizeAllNow);
            sb.Append("\n").Append("").Append(",").Append(fileSizeAllBeforeStr + "  -->  " + fileSizeAllNowStr).Append(",").Append(memorySizeAllBeforeStr + "  -->  " + memorySizeAllNowStr);

            var filePath = FileUtilitys.GetCurFullPathOnAssets(CWeaponAnimCompareCompressInfoPath);
            File.WriteAllText(filePath, sb.ToString());
        }
    }

    /// <summary>
    /// 导出英雄动画资源压缩信息前
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/ExportHeroAnimCompressInfoBefore(导出英雄动画资源压缩信息前)")]
    static void DoExportHeroAnimSizeInfoBefore()
    {
        var tList = GetHeroAniExportSizeInfo();
        if (tList != null && tList.Count > 0)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("aniPath").Append(",").Append("fileSize").Append(",").Append("memorySize");
            foreach (var t in tList)
            {
                var aniAssetPath = t.assetPath;
                sb.Append("\n").Append(aniAssetPath.Replace("\\", "/")).Append(",").Append(t.fileSize).Append(",").Append(t.memorySize);
            }

            var filePath = FileUtilitys.GetCurFullPathOnAssets(CDeadWeaponAnimCompressInfoPath);
            File.WriteAllText(filePath, sb.ToString());
        }
    }

    /// <summary>
    /// 获取英雄动画大小信息
    /// </summary>
    /// <returns></returns>
    static List<HeroAniExportSizeInfo> GetHeroAniExportSizeInfo()
    {
        List<HeroAniExportSizeInfo> list = new List<HeroAniExportSizeInfo>();
        OnEnable();

        if (tHeroAnimCompressInfoDic == null || tHeroAnimCompressInfoDic.Count <= 0)
        {
            return list;
        }

        foreach (var t in tHeroAnimCompressInfoDic.Keys)
        {
            var fileName = Path.GetFileNameWithoutExtension(t);
            var DirName = Path.GetDirectoryName(t);
            if (!string.IsNullOrEmpty(fileName) && fileName.Contains("@"))
            {
                int splitCharIndex = fileName.IndexOf("@");
                if (splitCharIndex >= 0)
                {
                    var aniName = fileName.Substring(splitCharIndex + 1) + ".anim";
                    var aniAssetPath = DirName + "/" + aniName;
                    var ac = AssetDatabase.LoadAssetAtPath<AnimationClip>(aniAssetPath);
                    if (ac != null)
                    {
                        var filePath = FileUtilitys.GetCurFullPathOnAssets(aniAssetPath);
                        FileInfo fileInfo = new FileInfo(filePath);
                        long fileSize = fileInfo.Length;
                        var memorySize = FileUtilitys.GetAnimationClipInspectorSize(ac);

                        list.Add(new HeroAniExportSizeInfo() { assetPath = aniAssetPath, fileSize = fileSize, memorySize = memorySize });
                    }
                }
            }
        }

        return list;
    }

    /// <summary>
    /// 格式化字节大小
    /// </summary>
    /// <param name="bytes"></param>
    /// <returns></returns>
    public static string FormatBytes(long bytes)
    {
        if (bytes >= 1024 * 1024)
        {
            return string.Format("{0:F2} MB", (double)bytes / (1024 * 1024));
        }
        else if (bytes >= 1024)
        {
            return string.Format("{0:F2} KB", (double)bytes / 1024);
        }
        else
        {
            return string.Format("{0} bytes", bytes);
        }
    }

    /// <summary>
    /// 英雄动画资源压缩信息设置
    /// </summary>
    [MenuItem("Tool/HeroResOptimal/SetHeroAnimCompressInfo(英雄动画资源压缩信息设置)")]
    static void DoSetHeroAnimCompressInfo()
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();

        OnEnable();
        List<string> filesList = new List<string>();
        FileUtilitys.GetFilesOnDir("Assets/Art/Characters", filesList, ".FBX,.fbx");
        foreach (var t in filesList)
        {
            var assetPath = t.Replace(Application.dataPath, "Assets").Replace("\\", "/");
            if (tHeroAnimCompressInfoDic.TryGetValue(assetPath, out var vArr) && vArr != null && vArr.Length >= 2)
            {
                ModelImporter modelImporter = AssetImporter.GetAtPath(assetPath) as ModelImporter;
                float animCompressRatio = 0f;
                bool animNeedSetCompress = true;
                bool bNeedRefresh = false;
                if (modelImporter != null && float.TryParse(vArr[0], out animCompressRatio) && bool.TryParse(vArr[1], out animNeedSetCompress))
                {
                    if(!animNeedSetCompress)
                    {
                        continue;
                    }

                    var animationCompression = modelImporter.animationCompression;
                    if(animationCompression == ModelImporterAnimationCompression.Optimal)
                    {
                        if(animCompressRatio <= modelImporter.animationPositionError || animCompressRatio <= modelImporter.animationRotationError ||
                            animCompressRatio <= modelImporter.animationScaleError)

                        continue;
                    }

                    if (modelImporter.animationCompression != ModelImporterAnimationCompression.Optimal)
                    {
                        modelImporter.animationCompression = ModelImporterAnimationCompression.Optimal;
                        bNeedRefresh = true;
                    }

                    if (!HeroResOptimal.AreEqual(modelImporter.animationPositionError, animCompressRatio))
                    {
                        modelImporter.animationPositionError = animCompressRatio;
                        bNeedRefresh = true;
                    }

                    if (!HeroResOptimal.AreEqual(modelImporter.animationRotationError, animCompressRatio))
                    {
                        modelImporter.animationRotationError = animCompressRatio;
                        bNeedRefresh = true;
                    }

                    if (!HeroResOptimal.AreEqual(modelImporter.animationScaleError, animCompressRatio))
                    {
                        modelImporter.animationScaleError = animCompressRatio;
                        bNeedRefresh = true;
                    }

                    if(bNeedRefresh)
                    {
                        modelImporter.SaveAndReimport();
                    }
                }

                //记录ab的名字
                Func<string, Dictionary<string, string>> recordABNames = (assetPath) =>
                {
                    Dictionary<string, string> animABNameDic = new Dictionary<string, string>();
                    if (!string.IsNullOrEmpty(assetPath))
                    {
                        var animDir = Path.GetDirectoryName(assetPath);
                        string[] files = Directory.GetFiles(animDir, $"*{CAnimationExtension}", SearchOption.AllDirectories);
                        if (files != null)
                        {
                            foreach (var t in files)
                            {
                                var assetPathTmp = FileUtilitys.GetAssetPathOnFullPath(t);
                                if (IsFilterAniName(assetPathTmp))
                                {
                                    continue;
                                }

                                var assetImporter = AssetImporter.GetAtPath(assetPathTmp);
                                if (assetImporter != null)
                                {
                                    var assetBundleName = assetImporter.assetBundleName;
                                    if (!string.IsNullOrEmpty(assetBundleName))
                                    {
                                        animABNameDic[t.Replace("\\", "/")] = assetBundleName;
                                    }
                                }
                            }
                        }
                    }

                    return animABNameDic;
                };

                //设置ab的名字
                Action<Dictionary<string, string>, string> setABName = (abNamesDic, assetPath) =>
                {
                    var animPath = assetPath.Replace("\\", "/");
                    if (abNamesDic.TryGetValue(animPath, out var result))
                    {
                        var assetImporter = AssetImporter.GetAtPath(assetPath);
                        if (assetImporter != null)
                        {
                            assetImporter.assetBundleName = result;
                        }
                    }
                };

                //设置动画scale曲线保持小数点3位
                Action<string> setAnimationFloat3OnDir = (assetPath) =>
                {
                    AnimationOpt animopt = _GetNewAOpt(assetPath);
                    if (animopt != null)
                    {
                        animopt.Optimize_Scale_Float3(false, 3);
                    }
                };

                if(bNeedRefresh)
                {
                    var abNamesDic = recordABNames(assetPath);
                    var directoryPath = Path.GetDirectoryName(assetPath);
                    FileUtilitys.DelDir((directoryPath + "/" + CAnimTmpDirName).Replace("\\", "/"));
                    var animPath = CopyAnimationsFromFBX(assetPath, directoryPath);
                    FileUtilitys.DelDir((directoryPath + "/" + CAnimTmpDirName).Replace("\\", "/"));
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                    setAnimationFloat3OnDir(animPath);
                    setABName(abNamesDic, animPath);
                }
            }
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    ///// <summary>
    ///// 刷新新信息数据
    ///// </summary>
    ///// <param name="rawData"></param>
    ///// <param name="nowData"></param>
    //static void RefreshNewInfoData(Dictionary<int, List<HeroModelTexInfo>> rawData, Dictionary<int, List<HeroModelTexInfo>> nowData)
    //{
    //    if (rawData == null || rawData.Count <= 0)
    //    {
    //        return;
    //    }

    //    foreach (var t in rawData)
    //    {
    //        if (nowData.TryGetValue(t.Key, out var resultList))
    //        {
    //            var rawItemsList = t.Value;
    //            for (int i = 0, j = resultList.Count; i < j; i++)
    //            {
    //                var g = rawItemsList.Find((item) => { return item.assetPath == resultList[i].assetPath; });
    //                if (g != null)
    //                {
    //                    resultList[i].optimalFlag = g.optimalFlag;
    //                    resultList[i].optimalTexSize = g.optimalTexSize;
    //                }
    //            }
    //        }
    //    }
    //}

    /// <summary>
    /// 获取英雄模型贴图
    /// </summary>
    /// <param name="resList"></param>
    /// <returns></returns>
    static List<string> GetHeroModelTex(List<string> resList)
    {
        List<string> retList = new List<string>();
        foreach (var t in resList)
        {
            if (FileUtilitys.IsTexturePath(t))
            {
                if (IsCharacterModelTexturePath(t))
                {
                    AddUniqueString(retList, t);
                }
            }
        }

        return retList;
    }

    /// <summary>
    /// 获取所有依赖AssetPath
    /// </summary>
    /// <param name="resList"></param>
    /// <returns></returns>
    static List<string> GetAllDepsAssetPath(List<string> resList)
    {
        List<string> resDeresList = new List<string>();
        foreach (var path in resList)
        {
            GetAllDepsAssetPath(resDeresList, (CAsset + "/" + path).Replace("\\", "/"));
        }

        return resDeresList;
    }

    /// <summary>
    /// 获取所有依赖AssetPath
    /// </summary>
    /// <param name="resDepends"></param>
    /// <param name="path"></param>
    static void GetAllDepsAssetPath(List<string> resDepends, string path)
    {
        UnityEngine.Object[] objects = EditorUtility.CollectDependencies(AssetDatabase.LoadAllAssetsAtPath(path));
        if (objects != null)
        {
            for (int i = 0; i < objects.Length; i++)
            {
                if (objects[i] != null)
                {
                    string assetPath = AssetDatabase.GetAssetPath(objects[i]);
                    if (!string.IsNullOrEmpty(assetPath) && !resDepends.Contains(assetPath))
                    {
                        resDepends.Add(assetPath.Replace("\\", "/"));
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取英雄贴图优化信息
    /// </summary>
    public static Dictionary<int, List<HeroModelTexInfo>> GetHeroModelTexOptimalContent()
    {
        Dictionary<int, List<HeroModelTexInfo>> tHeroResDic = new Dictionary<int, List<HeroModelTexInfo>>();

        try
        {
            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroModelTexOptimalInfoPath);
            if (File.Exists(filePath))
            {
                using (StreamReader ReaderObject = new StreamReader(filePath))
                {
                    string line;
                    int heroId = 0;
                    while ((line = ReaderObject.ReadLine()) != null)
                    {
                        if (line.StartsWith("#"))
                        {
                            if(int.TryParse(line.Substring(1), out heroId))
                            {
                                tHeroResDic[heroId] = new List<HeroModelTexInfo>();
                            }
                            else
                            {
                                heroId = 0;
                            }
                        }
                        else
                        {
                            if (heroId != 0 && tHeroResDic.TryGetValue(heroId, out var list))
                            {
                                list.Add(new HeroModelTexInfo() { assetPath = line });
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            UnityEngine.Debug.LogError($"【英雄资源优化】GetHeroModelTexOptimalContent()发生错误:{e.ToString()}");
        }

        return tHeroResDic;
    }

    /// <summary>
    /// Window.OnEnable时调用
    /// </summary>
    public static void OnEnable()
    {
        if (HeroResOptimal.charaTexOptimalPoly == ECharaTexOptimalPoly.UseTheFile)
        {
            tHeroTexOptInfoDic = HeroResOptimal.GetHeroModelTexOptimalContent();
            if(tHeroTexOptInfoDic != null)
            {
                heroTexOptInfo = new Dictionary<string, HeroModelTexInfo>();
                foreach(var t in tHeroTexOptInfoDic)
                {
                    var assetInfolist = t.Value;
                    if(assetInfolist != null)
                    {
                        foreach(var assetInfo in assetInfolist)
                        {
                            var assetPath = assetInfo.assetPath;
                            if(!heroTexOptInfo.TryGetValue(assetPath, out var value))
                            {
                                heroTexOptInfo[assetPath] = assetInfo;
                            }
                        }
                    }
                }
            }
        }

        tHeroAnimCompressInfoDic?.Clear();
        if(applyCharaAnimCompressInfo)
        {
            tHeroAnimCompressInfoDic = GetHeroAniCompressContent();
        }
    }

    /// <summary>
    /// 英雄贴图资源是否需要优化
    /// </summary>
    /// <param name="heroID"></param>
    /// <param name="path"></param>
    /// <returns></returns>
    public static bool IsHeroModelTexNeedOptimal(int heroID, string path)
    {
        if (HeroResOptimal.charaTexOptimalPoly == ECharaTexOptimalPoly.UseTheFile)
        {
            if (!string.IsNullOrEmpty(path) && heroTexOptInfo != null)
            {
                var tPath = path.Replace("\\", "/");
                if (heroTexOptInfo.TryGetValue(tPath, out var result))
                {
                    return true;
                }
            }

            return false;
        }

        return true;
    }

    /// <summary>
    /// 英雄动画是否需要压缩
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    static bool IsHeroAniNeedCompress(string assetPath)
    {
        if(!string.IsNullOrEmpty(assetPath) && tHeroAnimCompressInfoDic != null && tHeroAnimCompressInfoDic.Count > 0)
        {
            if (tHeroAnimCompressInfoDic.TryGetValue(assetPath, out var vArr))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 获取英雄动画资源压缩信息
    /// </summary>
    static Dictionary<string, string[]> GetHeroAniCompressContent()
    {
        Dictionary<string, string[]> tList = new Dictionary<string, string[]>();

        try
        {
            var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroAniCompressPath);
            if (File.Exists(filePath))
            {
                string input = File.ReadAllText(filePath);
                var info = Newtonsoft.Json.JsonConvert.DeserializeObject<HeroAniCompressInfo>(input);
                if (info != null)
                {
                    tList = info.list;
                }
            }
        }
        catch (Exception e)
        {
            UnityEngine.Debug.LogError($"【英雄资源优化】GetHeroAniNotCompressContent()发生错误:{e.ToString()}");
        }

        return tList;
    }

    /// <summary>
    /// 解析导出英雄动画大小信息内容
    /// </summary>
    public static List<HeroAniExportSizeInfo> ParseHeroAniExportSizeInfoContent()
    {
        List<HeroAniExportSizeInfo> list = new List<HeroAniExportSizeInfo>();
        var filePath = FileUtilitys.GetCurFullPathOnAssets(CDeadWeaponAnimCompressInfoPath);
        if (!File.Exists(filePath))
        {
            return list;
        }

        using (StreamReader ReaderObject = new StreamReader(filePath))
        {
            string line;
            int lineIndex = 0;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                var contents = line.Split(',');
                if (lineIndex != 0 && contents != null && contents.Length >= 3)
                {
                    list.Add(new HeroAniExportSizeInfo() { assetPath = contents[0], fileSize = long.Parse(contents[1]), memorySize = long.Parse(contents[2]) }); ;
                }

                lineIndex++;
            }
        }

        return list;
    }

    /// <summary>
    /// 检测应用动画文件使用压缩信息
    /// </summary>
    /// <param name="strContent"></param>
    static void CheckCharaAnimCompressInfo(string strContent)
    {
        if(string.IsNullOrEmpty(strContent))
        {
            return;
        }

        if (int.TryParse(strContent, out var result))
        {
            applyCharaAnimCompressInfo = result > 0;
        }
    }
}

