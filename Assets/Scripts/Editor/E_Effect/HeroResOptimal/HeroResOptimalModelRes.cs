/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    static Dictionary<string, string> modelResConifg = new Dictionary<string, string>()
    {
        ["animations/characters/01_xingqiwuxiansheng/01_xingqiwuxiansheng.prefab"] = "animations/characters/01_xingqiwuxiansheng/edit_01_xingqiwuxiansheng.prefab",

        ["animations/characters/02_tiankongzhishen/02_tiankongzhishen.prefab"] = "animations/characters/02_tiankongzhishen/edit_02_tiankongzhishen.prefab",

        ["animations/characters/03_chuziyingxiong/03_chuziyingxiong.prefab"] = "animations/characters/03_chuziyingxiong/edit_03_chuziyingxiong.prefab",

        ["animations/characters/04_t_2080/04_t_2080.prefab"] = "animations/characters/04_t_2080/edit_04_t_2080.prefab",

        ["animations/characters/05_hundun/05_hundun.prefab"] = "animations/characters/05_hundun/edit_05_hundun.prefab",

        ["animations/characters/06_pikemao/06_pikemao.prefab"] = "animations/characters/06_pikemao/edit_06_pikemao.prefab",

        ["animations/characters/07_cibaozhanxiong/07_cibaozhanxiong.prefab"] = "animations/characters/07_cibaozhanxiong/edit_07_cibaozhanxiong.prefab",

        ["animations/characters/08_zhixu/08_zhixu.prefab"] = "animations/characters/08_zhixu/edit_08_zhixu.prefab",

        ["animations/characters/09_shenghualangren/09_shenghualangren.prefab"] = "animations/characters/09_shenghualangren/edit_09_shenghualangren.prefab",

        ["animations/characters/10_lingshi/10_lingshi.prefab"] = "animations/characters/10_lingshi/edit_10_lingshi.prefab",

        ["animations/characters/12_baolongwushi/12_baolongwushi.prefab"] = "animations/characters/12_baolongwushi/edit_12_baolongwushi.prefab",

        ["animations/characters/13_xingjiyouxia/13_xingjiyouxia.prefab"] = "animations/characters/13_xingjiyouxia/edit_13_xingjiyouxia.prefab",

        ["animations/characters/14_jiguangyeren/14_jiguangyeren.prefab"] = "animations/characters/14_jiguangyeren/edit_14_jiguangyeren.prefab",

        ["animations/characters/15_shangjinlieren/15_shangjinlieren.prefab"] = "animations/characters/15_shangjinlieren/edit_15_shangjinlieren.prefab",

        ["animations/characters/16_saibojianxin/16_saibojianxin.prefab"] = "animations/characters/16_saibojianxin/edit_16_saibojianxin.prefab",

        ["animations/characters/17_shikongzhizhe/17_shikongzhizhe.prefab"] = "animations/characters/17_shikongzhizhe/edit_17_shikongzhizhe.prefab",

        ["animations/characters/18_xiaomianke/18_xiaomianke.prefab"] = "animations/characters/18_xiaomianke/edit_18_xiaomianke.prefab",

        ["animations/characters/19_jijiabawanglong/19_jijiabawanglong.prefab"] = "animations/characters/19_jijiabawanglong/edit_19_jijiabawanglong.prefab",

        ["animations/characters/20_aiguozhe/20_aiguozhe.prefab"] = "animations/characters/20_aiguozhe/edit_20_aiguozhe.prefab",

        ["animations/characters/21_renxingzhunao/21_renxingzhunao.prefab"] = "animations/characters/21_renxingzhunao/edit_21_renxingzhunao.prefab",

        ["animations/characters/22_feituzhanshi/22_feituzhanshi.prefab"] = "animations/characters/22_feituzhanshi/edit_22_feituzhanshi.prefab",

        ["animations/characters/23_shouxuekuangtu/23_shouxuekuangtu.prefab"] = "animations/characters/23_shouxuekuangtu/edit_23_shouxuekuangtu.prefab",

        ["animations/characters/24_xieduti/24_xieduti.prefab"] = "animations/characters/24_xieduti/edit_24_xieduti.prefab",

        ["animations/characters/25_meiguoxiaojie/25_meiguoxiaojie.prefab"] = "animations/characters/25_meiguoxiaojie/edit_25_meiguoxiaojie.prefab",

        ["animations/characters/26_juejingzhiya/26_juejingzhiya.prefab"] = "animations/characters/26_juejingzhiya/edit_26_juejingzhiya.prefab",

        ["animations/characters/27_nvwushen/27_nvwushen.prefab"] = "animations/characters/27_nvwushen/edit_27_nvwushen.prefab",

        ["animations/characters/28_juedouzhanhun/28_juedouzhanhun.prefab"] = "animations/characters/28_juedouzhanhun/edit_28_juedouzhanhun.prefab",

        ["animations/characters/29_dianrongjuxing/29_dianrongjuxing.prefab"] = "animations/characters/29_dianrongjuxing/edit_29_dianrongjuxing.prefab",

        ["animations/characters/30_chengjiezhiquan/30_chengjiezhiquan.prefab"] = "animations/characters/30_chengjiezhiquan/edit_30_chengjiezhiquan.prefab",

        ["animations/characters/31_eyuntuzhu/31_eyuntuzhu.prefab"] = "animations/characters/31_eyuntuzhu/edit_31_eyuntuzhu.prefab",

        ["animations/characters/32_nuhaizhixin/32_nuhaizhixin.prefab"] = "animations/characters/32_nuhaizhixin/edit_32_nuhaizhixin.prefab",

        ["animations/characters/33_liemoren/33_liemoren.prefab"] = "animations/characters/33_liemoren/edit_33_liemoren.prefab",

        ["animations/characters/34_zhanditianshi/34_zhanditianshi.prefab"] = "animations/characters/34_zhanditianshi/edit_34_zhanditianshi.prefab",

        ["animations/characters/35_baozoushaonv/35_baozoushaonv.prefab"] = "animations/characters/35_baozoushaonv/edit_35_baozoushaonv.prefab",

        ["animations/characters/36_jinhuazhiqu/36_jinhuazhiqu.prefab"] = "animations/characters/36_jinhuazhiqu/edit_36_jinhuazhiqu.prefab",

        ["animations/characters/37_longwukuangquan/37_longwukuangquan.prefab"] = "animations/characters/37_longwukuangquan/edit_37_longwukuangquan.prefab",

        ["animations/characters/38_shengtu/38_shengtu.prefab"] = "animations/characters/38_shengtu/edit_38_shengtu.prefab",

        ["animations/characters/39_shihuozhe/39_shihuozhe.prefab"] = "animations/characters/39_shihuozhe/edit_39_shihuozhe.prefab",

        ["animations/characters/40_jixiejiaofu/40_jixiejiaofu.prefab"] = "animations/characters/40_jixiejiaofu/edit_40_jixiejiaofu.prefab",

        ["animations/characters/41_tengmanzhishang/41_tengmanzhishang.prefab"] = "animations/characters/41_tengmanzhishang/edit_41_tengmanzhishang.prefab",

        ["animations/characters/42_buxiangzhiyin/42_buxiangzhiyin.prefab"] = "animations/characters/42_buxiangzhiyin/edit_42_buxiangzhiyin.prefab",

        ["animations/characters/43_chensizhe/43_chensizhe.prefab"] = "animations/characters/43_chensizhe/edit_43_chensizhe.prefab",

        ["animations/characters/44_juexingkuangniu/44_juexingkuangniu.prefab"] = "animations/characters/44_juexingkuangniu/edit_44_juexingkuangniu.prefab",

        ["animations/characters/45_jidongqixia/45_jidongqixia.prefab"] = "animations/characters/45_jidongqixia/edit_45_jidongqixia.prefab",

        ["animations/characters/46_xiayingzhijian/46_xiayingzhijian.prefab"] = "animations/characters/46_xiayingzhijian/edit_46_xiayingzhijian.prefab",

        ["animations/characters/47_zhihuinvshen/47_zhihuinvshen.prefab"] = "animations/characters/47_zhihuinvshen/edit_47_zhihuinvshen.prefab",

        ["animations/characters/48_langkeshaonv/48_langkeshaonv.prefab"] = "animations/characters/48_langkeshaonv/edit_48_langkeshaonv.prefab",

        ["animations/characters/49_xiaoaishen/49_xiaoaishen.prefab"] = "animations/characters/49_xiaoaishen/edit_49_xiaoaishen.prefab",

        ["animations/characters/50_kumujianshi/50_kumujianshi.prefab"] = "animations/characters/50_kumujianshi/edit_50_kumujianshi.prefab",

        ["animations/characters/51_shenhaijubo/51_shenhaijubo.prefab"] = "animations/characters/51_shenhaijubo/edit_51_shenhaijubo.prefab",

        ["animations/characters/52_yezhimonv/52_yezhimonv.prefab"] = "animations/characters/52_yezhimonv/edit_52_yezhimonv.prefab",

        ["animations/characters/53_touyingzhiying/53_touyingzhiying.prefab"] = "animations/characters/53_touyingzhiying/edit_53_touyingzhiying.prefab",

        ["animations/characters/54_zhendangdianhu/54_zhendangdianhu.prefab"] = "animations/characters/54_zhendangdianhu/edit_54_zhendangdianhu.prefab",

        ["animations/characters/55_lanhuoshaxing/55_lanhuoshaxing.prefab"] = "animations/characters/55_lanhuoshaxing/edit_55_lanhuoshaxing.prefab",

        ["animations/characters/56_kexueguairen/56_kexueguairen.prefab"] = "animations/characters/56_kexueguairen/edit_56_kexueguairen.prefab",

        ["animations/characters/57_shefanvyao/57_shefanvyao.prefab"] = "animations/characters/57_shefanvyao/edit_57_shefanvyao.prefab",

        ["animations/characters/58_gangquan/58_gangquan.prefab"] = "animations/characters/58_gangquan/edit_58_gangquan.prefab",

        ["animations/characters/59_weidupaozhe/59_weidupaozhe.prefab"] = "animations/characters/59_weidupaozhe/edit_59_weidupaozhe.prefab",

        ["animations/characters/60_gangtiedashi/60_gangtiedashi.prefab"] = "animations/characters/60_gangtiedashi/edit_60_gangtiedashi.prefab",

        ["animations/characters/61_gangzhilangzhe/61_gangzhilangzhe.prefab"] = "animations/characters/61_gangzhilangzhe/edit_61_gangzhilangzhe.prefab",

        ["animations/characters/62_liebaopaofeng/62_liebaopaofeng.prefab"] = "animations/characters/62_liebaopaofeng/edit_62_liebaopaofeng.prefab",

        ["animations/characters/63_mayaleishen/63_mayaleishen.prefab"] = "animations/characters/63_mayaleishen/edit_63_mayaleishen.prefab",

        ["animations/characters/64_zhanzhengzhishen/64_zhanzhengzhishen.prefab"] = "animations/characters/64_zhanzhengzhishen/edit_64_zhanzhengzhishen.prefab",

        ["animations/characters/65_emengyanhun/65_emengyanhun.prefab"] = "animations/characters/65_emengyanhun/edit_65_emengyanhun.prefab",

        ["animations/characters/65_emengyanhun_01/65_emengyanhun_01.prefab"] = "animations/characters/65_emengyanhun_01/edit_65_emengyanhun_01.prefab",

        ["animations/characters/66_huixingshangjiang/66_huixingshangjiang.prefab"] = "animations/characters/66_huixingshangjiang/edit_66_huixingshangjiang.prefab",

        ["animations/characters/67_baofengxingzhe/67_baofengxingzhe.prefab"] = "animations/characters/67_baofengxingzhe/edit_67_baofengxingzhe.prefab",

        ["animations/characters/68_shi/68_shi.prefab"] = "animations/characters/68_shi/edit_68_shi.prefab",

        ["animations/characters/69_mianbaoniuniu/69_mianbaoniuniu.prefab"] = "animations/characters/69_mianbaoniuniu/edit_69_mianbaoniuniu.prefab",

        ["animations/characters/70_shang/70_shang.prefab"] = "animations/characters/70_shang/edit_70_shang.prefab",

        ["animations/characters/71_haiyangzhishen/71_haiyangzhishen.prefab"] = "animations/characters/71_haiyangzhishen/edit_71_haiyangzhishen.prefab",

        ["animations/characters/72_guang/72_guang.prefab"] = "animations/characters/72_guang/edit_72_guang.prefab",

        ["animations/characters/73_shamosishen/73_shamosishen.prefab"] = "animations/characters/73_shamosishen/edit_73_shamosishen.prefab",

        ["animations/characters/74_xinyang/74_xinyang.prefab"] = "animations/characters/74_xinyang/edit_74_xinyang.prefab",

        ["animations/characters/75_daqian/75_daqian.prefab"] = "animations/characters/75_daqian/edit_75_daqian.prefab",

        ["animations/characters/76_ziran/76_ziran.prefab"] = "animations/characters/76_ziran/edit_76_ziran.prefab",

        ["animations/characters/77_taiyangshen/77_taiyangshen.prefab"] = "animations/characters/77_taiyangshen/edit_77_taiyangshen.prefab",

        ["animations/characters/78_diyuzaibian/78_diyuzaibian.prefab"] = "animations/characters/78_diyuzaibian/edit_78_diyuzaibian.prefab",

        ["animations/characters/79_sheng/79_sheng.prefab"] = "animations/characters/79_sheng/edit_79_sheng.prefab",

        ["animations/characters/80_tiexuelieshou/80_tiexuelieshou.prefab"] = "animations/characters/80_tiexuelieshou/edit_80_tiexuelieshou.prefab",

        ["animations/characters/81_chongqunzhimu/81_chongqunzhimu.prefab"] = "animations/characters/81_chongqunzhimu/edit_81_chongqunzhimu.prefab",

        ["animations/characters/82_xieezhouxin/82_xieezhouxin.prefab"] = "animations/characters/82_xieezhouxin/edit_82_xieezhouxin.prefab",

        ["animations/characters/83_diyuzhiwang/83_diyuzhiwang.prefab"] = "animations/characters/83_diyuzhiwang/edit_83_diyuzhiwang.prefab",

        ["animations/characters/84_xuanyingrenma/84_xuanyingrenma.prefab"] = "animations/characters/84_xuanyingrenma/edit_84_xuanyingrenma.prefab",

        ["animations/characters/85_baqidashe/85_baqidashe.prefab"] = "animations/characters/85_baqidashe/edit_85_baqidashe.prefab",

        ["animations/characters/86_zhandouxingsuti/86_zhandouxingsuti.prefab"] = "animations/characters/86_zhandouxingsuti/edit_86_zhandouxingsuti.prefab",

        ["animations/characters/87_meiyinjiuwei/87_meiyinjiuwei.prefab"] = "animations/characters/87_meiyinjiuwei/edit_87_meiyinjiuwei.prefab",

        ["animations/characters/88_shuanghaijulong/88_shuanghaijulong.prefab"] = "animations/characters/88_shuanghaijulong/edit_88_shuanghaijulong.prefab",

        ["animations/characters/89_juese_nv/89_juese_nv.prefab"] = "animations/characters/89_juese_nv/edit_89_juese_nv.prefab",

        ["animations/characters/91_xiaefensuizhe/91_xiaefensuizhe.prefab"] = "animations/characters/91_xiaefensuizhe/edit_91_xiaefensuizhe.prefab",

        ["animations/characters/95_siwang/95_siwang.prefab"] = "animations/characters/95_siwang/edit_95_siwang.prefab",

        ["animations/characters/96_juese_nan/96_juese_nan.prefab"] = "animations/characters/96_juese_nan/edit_96_juese_nan.prefab",

        ["animations/characters/97_shengdanxiaojie/97_shengdanxiaojie.prefab"] = "animations/characters/97_shengdanxiaojie/edit_97_shengdanxiaojie.prefab",

        ["animations/characters/98_aidisheng/98_aidisheng.prefab"] = "animations/characters/98_aidisheng/edit_98_aidisheng.prefab",

        ["animations/characters/99_zhixu/99_zhixu.prefab"] = "animations/characters/99_zhixu/edit_99_zhixu.prefab",

        ["animations/characters/100_anyelizhua/100_anyelizhua.prefab"] = "animations/characters/100_anyelizhua/edit_100_anyelizhua.prefab",

        ["animations/characters/102_tiankongzhishen/102_tiankongzhishen.prefab"] = "animations/characters/102_tiankongzhishen/edit_102_tiankongzhishen.prefab",

        ["animations/characters/108_tielinfeiying/108_tielinfeiying.prefab"] = "animations/characters/108_tielinfeiying/edit_108_tielinfeiying.prefab",

        ["animations/characters/cj_baoxiang01/cj_baoxiang01.prefab"] = "animations/characters/cj_baoxiang01/edit_cj_baoxiang01.prefab",

        ["animations/characters/cj_baoxiang02/cj_baoxiang02.prefab"] = "animations/characters/cj_baoxiang02/edit_cj_baoxiang02.prefab",

        ["animations/characters/cj_baoxiang03/cj_baoxiang03.prefab"] = "animations/characters/cj_baoxiang03/edit_cj_baoxiang03.prefab",

        ["animations/characters/cj_baoxiang04/cj_baoxiang04.prefab"] = "animations/characters/cj_baoxiang04/edit_cj_baoxiang04.prefab",

        ["animations/characters/fuyoupao/fuyoupao.prefab"] = "animations/characters/fuyoupao/edit_fuyoupao.prefab",

        ["animations/characters/lanjinushizhiqiang/lanjinushizhiqiang.prefab"] = "animations/characters/lanjinushizhiqiang/edit_lanjinushizhiqiang.prefab",

        ["animations/characters/nishangzhidun/nishangzhidun.prefab"] = "animations/characters/nishangzhidun/edit_nishangzhidun.prefab",

        ["animations/characters/texiusizhijian/texiusizhijian.prefab"] = "animations/characters/texiusizhijian/edit_texiusizhijian.prefab",

        ["animations/characters/105_shamei/105_shamei.prefab"] = "animations/characters/105_shamei/edit_105_shamei.prefab",


        ["art/maps/jiuba/jiuba.prefab"]
= "art/maps/jiuba/edit_jiuba.prefab"
    };

    /// <summary>
    /// 获取模型资源路径
    /// </summary>
    /// <param name="res"></param>
    /// <returns></returns>
    static string GetModelResPath(string res)
    {
        if(!string.IsNullOrEmpty(res))
        {
            if(modelResConifg.TryGetValue(res, out var result))
            {
                return result;
            }
        }

        return res;
    }
}

