/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using UnityEngine;
using System.IO;
using UnityEditor;
using System;
using System.Collections.Generic;
using Sirenix.OdinInspector.Demos;
using EditorUtilitys;
using System.Text;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 英雄优化信息
    /// </summary>
    public class HeroResInfoOptimal
    {
        /// <summary>
        /// 英雄名字
        /// </summary>
        public string heroName;
        /// <summary>
        /// 英雄依赖
        /// </summary>
        public List<string> heroDepends;
    }

    /// <summary>
    /// 优化英雄ID列表
    /// </summary>
    static List<int> opHeroIDList = new List<int>();
    /// <summary>
    /// 英雄ID对应的依赖
    /// </summary>
    static Dictionary<int, HeroResInfoOptimal> heroDependsDic = new Dictionary<int, HeroResInfoOptimal>();
    /// <summary>
    /// 配置文件全路径
    /// </summary>
    static string configFullPath = string.Empty;
    /// <summary>
    /// 英雄ID最大值
    /// </summary>
    static int heroIDMax = 200;
    /// <summary>
    /// 英雄最大等级
    /// </summary>
    static int heroMaxLev = 60;
    /// <summary>
    /// 英雄最大星级
    /// </summary>
    static int heroMaxStar = 15;
    /// <summary>
    /// 英雄最大皮肤
    /// </summary>
    static int heroMaxSkin = 1;
    /// <summary>
    /// CSV全路径
    /// </summary>
    static string csvFullPath = "../../Tools/csv_script/";
    /// <summary>
    /// langCSV全路径
    /// </summary>
    static string csvLangFullPath = "../../Tools/csv_script/Lang.csv";
    /// <summary>
    /// 已经处理过的资源
    /// </summary>
    static List<string> processedAssetsList = new List<string>();
    /// <summary>
    /// anim动画文件的abName
    /// </summary>
    static Dictionary<string, string> animABNameDic = new Dictionary<string, string>();
    /// <summary>
    /// 英雄资源优化参数
    /// </summary>
    static HeroResOptimalOption heroResOptimalOption;
    /// <summary>
    /// 贴图设置尺寸
    /// </summary>
    static Dictionary<string, int> texSetSizeDic = new Dictionary<string, int>();
    /// <summary>
    /// 优化stand,show动画
    /// </summary>
    static bool optimalStandShowAni = false;

    /// <summary>
    /// 设置优化stand,show动画
    /// </summary>
    /// <param name="bOptimal"></param>
    public static void SetOptimalStandShowAni(bool bOptimal)
    {
        optimalStandShowAni = bOptimal;
    }

    /// <summary>
    /// 获取英雄名字内容
    /// </summary>
    /// <returns></returns>
    static Dictionary<int, string> GetReadHeroNameContent()
    {
        Dictionary<int, string> contentsDic = new Dictionary<int, string>();

        var filePath = FileUtilitys.GetCurFullPathOnAssets(CHeroNamePath);
        if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
        {
            return contentsDic;
        }

        using (StreamReader ReaderObject = new StreamReader(filePath))
        {
            string line;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                if (line.StartsWith("#"))
                {//注释行
                    continue;
                }

                var contents = line.Split('=');
                if (contents != null && contents.Length >= 2)
                {
                    var strKey = contents[0];
                    var strContent = contents[1];
                    try
                    {
                        if (!string.IsNullOrEmpty(strKey) && int.TryParse(strKey, out var heroID))
                        {
                            contentsDic[heroID] = strContent;
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError("HeroResOptimal.GetReadHeroNameContent()捕获到异常:" + e.ToString());
                    }
                }
            }
        }

        return contentsDic;
    }

    /// <summary>
    /// 获取文件内容根据键
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="key"></param>
    /// <returns></returns>
    static List<string> GetReadFileContentOnKey(string filePath, string key)
    {
        List<string> contentsList = new List<string>();
        if (string.IsNullOrEmpty(filePath) || string.IsNullOrEmpty(key) || !File.Exists(filePath))
        {
            return contentsList;
        }

        using (StreamReader ReaderObject = new StreamReader(filePath))
        {
            string line;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                if (line.StartsWith("#"))
                {//注释行
                    continue;
                }

                var contents = line.Split('=');
                if (contents != null && contents.Length >= 2)
                {
                    var strKey = contents[0];
                    var strContent = contents[1];
                    try
                    {
                        if (!string.IsNullOrEmpty(strKey))
                        {
                            if (strKey.ToLower().Trim() == key.ToLower().Trim())
                            {
                                contentsList.Add(strContent);
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError("HeroResOptimal.GetReadFileContentOnKey()捕获到异常:" + e.ToString());
                    }
                }
            }
        }

        return contentsList;
    }

    /// <summary>
    /// 处理配置文件路径
    /// </summary>
    static void ProcessConfigFullPath()
    {
        configFullPath = FileUtilitys.GetCurFullPathOnAssets(CConfigPath);
    }

    /// <summary>
    /// 处理csv文件路径
    /// </summary>
    static bool ProcessCsvFullPath()
    {
        var cList = GetReadFileContentOnKey(configFullPath, CCSVDirKey);
        if (cList != null && cList.Count > 0)
        {
            var strContent = cList[0];
            if (!string.IsNullOrEmpty(strContent))
            {
                var strDir = Path.GetFullPath(strContent).Replace("\\", "/");
                if (Directory.Exists(strDir))
                {
                    csvFullPath = strDir;
                    return true;
                }
            }
        }

        UnityEngine.Debug.LogError("【英雄资源优化】,csv路径不存在!!!!!!");
        return false;
    }

    /// <summary>
    /// 处理英雄优化ID列表
    /// </summary>
    static void ProcessOptimalHeroIDs(List<int> _opHeroIDList)
    {
        opHeroIDList = _opHeroIDList;
    }

    /// <summary>
    /// 获取配置内容
    /// </summary>
    /// <param name="strKey"></param>
    /// <returns></returns>
    static List<string> GetConfigContentsOnKey(string strKey)
    {
        List<string> strCfgContents = new List<string>();
        var cList = GetReadFileContentOnKey(configFullPath, strKey);

        if (cList != null && cList.Count > 0)
        {
            var strContent = cList[0];
            if (!string.IsNullOrEmpty(strContent))
            {
                var strIdArr = strContent.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                if (strIdArr != null)
                {
                    foreach (var t in strIdArr)
                    {
                        strCfgContents.Add(t);
                    }
                }
            }
        }

        return strCfgContents;
    }

    /// <summary>
    /// 处理英雄常量值
    /// </summary>
    static bool ProcessHeroConst()
    {
        ProcessHeroIDMax();
        ProcessHeroMaxLev();
        ProcessHeroMaxStar();
        ProcessHeroMaxSkin();

        return true;
    }

    /// <summary>
    /// 英雄ID最大值
    /// </summary>
    /// <returns></returns>
    static bool ProcessHeroIDMax()
    {
        var cList = GetConfigContentsOnKey(CHeroIDMaxKey);
        if (cList != null && cList.Count > 0)
        {
            var c = cList[0];
            if (!string.IsNullOrEmpty(c) && int.TryParse(c, out var result))
            {
                heroIDMax = result;
            }
        }

        return true;
    }

    /// <summary>
    /// 英雄最大等级
    /// </summary>
    /// <returns></returns>
    static bool ProcessHeroMaxLev()
    {
        var cList = GetConfigContentsOnKey(CHeroMaxLevKey);
        if (cList != null && cList.Count > 0)
        {
            var c = cList[0];
            if (!string.IsNullOrEmpty(c) && int.TryParse(c, out var result))
            {
                heroMaxLev = result;
            }
        }

        return true;
    }

    /// <summary>
    /// 英雄最大星级
    /// </summary>
    /// <returns></returns>
    static bool ProcessHeroMaxStar()
    {
        var cList = GetConfigContentsOnKey(CHeroMaxStarKey);
        if (cList != null && cList.Count > 0)
        {
            var c = cList[0];
            if (!string.IsNullOrEmpty(c) && int.TryParse(c, out var result))
            {
                heroMaxStar = result;
            }
        }

        return true;
    }

    /// <summary>
    /// 英雄最大皮肤数
    /// </summary>
    /// <returns></returns>
    static bool ProcessHeroMaxSkin()
    {
        var cList = GetConfigContentsOnKey(CHeroMaxSkinKey);
        if (cList != null && cList.Count > 0)
        {
            var c = cList[0];
            if (!string.IsNullOrEmpty(c) && int.TryParse(c, out var result))
            {
                heroMaxSkin = result;
            }
        }

        return true;
    }

    /// <summary>
    /// 设置贴图maxSize
    /// </summary>
    /// <param name="strAssetPath"></param>
    /// <param name="maxTextureSize"></param>
    static void SetTextureMaxSize(string strAssetPath, int maxTextureSize)
    {
        if (string.IsNullOrEmpty(strAssetPath))
        {
            return;
        }

        var ti = AssetImporter.GetAtPath(strAssetPath) as TextureImporter;
        if (ti != null)
        {
            TextureImporterPlatformSettings windowsSettings = ti.GetPlatformTextureSettings(BuildTarget.StandaloneWindows.ToString());
            TextureImporterPlatformSettings windows64Settings = ti.GetPlatformTextureSettings(BuildTarget.StandaloneWindows64.ToString());
            TextureImporterPlatformSettings androidSettings = ti.GetPlatformTextureSettings(BuildTarget.Android.ToString());
            TextureImporterPlatformSettings iosSettings = ti.GetPlatformTextureSettings(BuildTarget.iOS.ToString());

            SetPlatformTextureSettings(ti, windowsSettings, maxTextureSize);
            SetPlatformTextureSettings(ti, windows64Settings, maxTextureSize);
            SetPlatformTextureSettings(ti, androidSettings, maxTextureSize);
            SetPlatformTextureSettings(ti, iosSettings, maxTextureSize);
        }
    }

    /// <summary>
    /// 设置对应平台贴图maxSize
    /// </summary>
    /// <param name="ti"></param>
    /// <param name="tPlatSetting"></param>
    /// <param name="maxTextureSize"></param>
    static void SetPlatformTextureSettings(TextureImporter ti, TextureImporterPlatformSettings tPlatSetting, int maxTextureSize)
    {
        if (ti != null && tPlatSetting != null)
        {
            tPlatSetting.overridden = true;
            tPlatSetting.maxTextureSize = maxTextureSize;
            ti.SetPlatformTextureSettings(tPlatSetting);
            EditorUtility.SetDirty(ti);
            ti.SaveAndReimport();
        }
    }

    /// <summary>
    /// 优化资源
    /// </summary>
    /// <param name="assetsPath"></param>
    static void OptimalAssets(List<string> assetsPath)
    {
        foreach (var assetPath in assetsPath)
        {
            ProcessAssetDepends(assetPath.Replace("\\", "/"));
        }

        if(CEffectSourceDirPrefix != null && CEffectSourceDirPrefix.Count > 0)
        {
            var effectsSource = ProcessEffectSource(processedAssetsList);
            if (effectsSource.Count > 0)
            {
                foreach (var t in effectsSource)
                {
                    ProcessAssetDepends(t.Replace("\\", "/"));
                }
            }
        }

        ApplySetTextureMaxSize();
    }

    /// <summary>
    /// 优化英雄
    /// </summary>
    static void OptimalHeros()
    {
        foreach (var heroId in opHeroIDList)
        {
            if(heroDependsDic.TryGetValue(heroId, out var heroInfo))
            {
                var dependList = heroInfo.heroDepends;
                if (dependList != null)
                {
                    foreach (var t in dependList)
                    {
                        ProcessHeroDepends((uint)heroId, t, true);
                    }
                }
            }
        }

        if(CEffectSourceDirPrefix != null && CEffectSourceDirPrefix.Count > 0)
        {
            var effectsSource = ProcessEffectSource(processedAssetsList);
            if (effectsSource.Count > 0)
            {
                foreach (var t in effectsSource)
                {
                    ProcessHeroDepends(0, t, false);
                }
            }
        }

        ApplySetTextureMaxSize();
    }

    /// <summary>
    /// 处理英雄资源依赖逻辑
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="path"></param>
    /// <param name="appendAssetPrefix"></param>
    static void ProcessHeroDepends(uint heroId, string path, bool appendAssetPrefix)
    {
        string heroAssetPath = path;
        if(appendAssetPrefix)
        {
            heroAssetPath = CAsset + "/" + path;  //ab依赖路径是去除asset下的路径,且全部是小写
        }

        heroAssetPath = heroAssetPath.Replace("\\", "/");
        var assets = AssetDatabase.LoadAllAssetsAtPath(heroAssetPath);
        if (assets != null)
        {
            UnityEngine.Object[] objects = EditorUtility.CollectDependencies(assets);
            if (objects != null)
            {
                for (int i = 0; i < objects.Length; i++)
                {
                    if (objects[i] != null)
                    {
                        string assetPath = AssetDatabase.GetAssetPath(objects[i]);
                        if (!string.IsNullOrEmpty(assetPath) && !processedAssetsList.Contains(assetPath))
                        {
                            processedAssetsList.Add(assetPath);

                            if(heroResOptimalOption.optimalChaAnim)
                            {
                                OptimalHeroAnimation(heroId, assetPath);
                            }

                            if (heroResOptimalOption.optimalChaTex)
                            {
                                OptimalHeroModelTexture(heroId, assetPath, heroResOptimalOption, true);
                            }

                            if (heroResOptimalOption.optimalChaEffectTex)
                            {
                                OptimalHeroEffectTexture(heroId, assetPath, heroResOptimalOption, true);
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 处理资源依赖
    /// </summary>
    /// <param name="path"></param>
    static void ProcessAssetDepends(string assetPath)
    {
        assetPath = assetPath.Replace("\\", "/");
        var assets = AssetDatabase.LoadAllAssetsAtPath(assetPath);
        if (assets != null)
        {
            UnityEngine.Object[] objects = EditorUtility.CollectDependencies(assets);
            if (objects != null && objects.Length >= 1)
            {
                for (int i = 0; i < objects.Length; i++)
                {
                    if (objects[i] != null)
                    {
                        string assetPathT = AssetDatabase.GetAssetPath(objects[i]);
                        assetPathT = assetPathT.Replace("\\", "/");
                        ProcessAssetLogic(assetPathT);
                    }
                }
            }
            else
            {
                ProcessAssetLogic(assetPath);
            }
        }
    }

    /// <summary>
    /// 处理Asset逻辑
    /// </summary>
    /// <param name="assetPathT"></param>
    static void ProcessAssetLogic(string assetPathT)
    {
        if (!string.IsNullOrEmpty(assetPathT) && !processedAssetsList.Contains(assetPathT))
        {
            processedAssetsList.Add(assetPathT);

            var isFbxAnimPath = HeroResOptimal.IsHeroFbxAnimPath(assetPathT);
            var isHeroAnimPath = HeroResOptimal.IsHeroAnimPath(assetPathT);
            var isHeroTexPath = HeroResOptimal.IsHeroTexPath(assetPathT);
            var isEffectTexPath = HeroResOptimal.IsEffectTexPath(assetPathT);

            if (!isFbxAnimPath && !isHeroAnimPath && !isHeroTexPath && !isEffectTexPath)
            {//路径不满足
                return;
            }

            if (isFbxAnimPath)
            {//角色动画fbx路径
                ProcessAssetFbxAnim(assetPathT);
            }
            else if (isHeroAnimPath)
            {//角色动画anim路径
                ProcessAssetAnim(assetPathT);
            }
            else if (isHeroTexPath)
            {//角色贴图路径
                OptimalHeroModelTexture(0, assetPathT, heroResOptimalOption, true);
            }
            else if (isEffectTexPath)
            {//特效贴图路径
                OptimalHeroEffectTexture(0, assetPathT, heroResOptimalOption, true);
            }
        }
    }

    /// <summary>
    /// 处理asset fbx anim信息
    /// </summary>
    /// <param name="assetPath"></param>
    static void ProcessAssetFbxAnim(string assetPath)
    {
        ModifyAnimationCompressionToOptimal(assetPath);
        //string directoryPath = Path.GetDirectoryName(assetPath).Replace("\\", "/");
        //var animNameList = GetAnimNamesFromFBXAnim(assetPath);
        //if (animNameList != null && animNameList.Count > 0)
        //{
        //    var animName = animNameList[0] + CAnimationExtension;
        //    string[] findFilePaths = null;
        //    if (FileUtilitys.GetFilePathByFileName(directoryPath, animName, ref findFilePaths))
        //    {
        //        var findFilePath = findFilePaths[0].Replace("\\", "/");
        //        var assetBundleName = GetAssetBundleName(findFilePath);
        //        CopyAnimationsFromFBX(assetPath, directoryPath);
        //        SetAnimationFloat3OnPath(findFilePath);
        //        SetAssetBundleName(findFilePath, assetBundleName);
        //    }
        //}
    }

    /// <summary>
    /// 处理asset anim信息
    /// </summary>
    /// <param name="assetPath"></param>
    static void ProcessAssetAnim(string assetPath)
    {
        assetPath = assetPath.Replace("\\", "/");
        SetAnimationFloat3OnPath(assetPath);
    }

    /// <summary>
    /// 获取AssetBudleName
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    static string GetAssetBundleName(string assetPath)
    {
        assetPath = assetPath.Replace("\\", "/");
        var assetImporter = AssetImporter.GetAtPath(assetPath);
        if (assetImporter != null)
        {
            return assetImporter.assetBundleName;
        }

        return string.Empty;
    }

    /// <summary>
    /// 设置AssetBudleName
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="assetBundleName"></param>
    static void SetAssetBundleName(string assetPath, string assetBundleName)
    {
        assetPath = assetPath.Replace("\\", "/");
        var assetImporter = AssetImporter.GetAtPath(assetPath);
        if (assetImporter != null)
        {
            assetImporter.assetBundleName = assetBundleName;
        }
    }

    /// <summary>
    /// 从fbx anim获取动画名字列表
    /// </summary>
    /// <param name="fbxPath"></param>
    /// <returns></returns>
    static List<string> GetAnimNamesFromFBXAnim(string fbxPath)
    {
        List<string> animNames = new List<string>();
        UnityEngine.Object[] assets = AssetDatabase.LoadAllAssetsAtPath(fbxPath);
        if (assets != null)
        {
            foreach (var t in assets)
            {
                var tAnimClip = t as AnimationClip;
                if (tAnimClip != null)
                {
                    var animClipName = tAnimClip.name;
                    if (!animClipName.Contains(fbxAnimWithPreview))
                    {
                        animNames.Add(animClipName);
                    }
                }
            }
        }

        return animNames;
    }

    #region 资源优化路径判断

    /// <summary>
    /// 是否是角色动画fbx路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsHeroFbxAnimPath(string assetPath)
    {
        if(!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (assetPath.EndsWith(".fbx") || assetPath.EndsWith(".FBX"))
            {
                if (HeroResOptimal.IsCharacterAnimPath(assetPath))
                {
                    if (!HeroResOptimal.IsFilterAniName(assetPath))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是角色动画anim路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsHeroAnimPath(string assetPath)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (assetPath.EndsWith(HeroResOptimal.CAnimationExtension))
            {
                if (HeroResOptimal.IsCharacterAnimPath(assetPath))
                {
                    if (!HeroResOptimal.IsFilterAniName(assetPath))
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是角色Prefab路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsHeroPrefabPath(string assetPath)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (assetPath.EndsWith(".prefab"))
            {
                if (HeroResOptimal.IsCharacterPrefabPath(assetPath))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是角色贴图路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsHeroTexPath(string assetPath)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (FileUtilitys.IsTexturePath(assetPath))
            {
                if (HeroResOptimal.IsCharacterModelTexturePath(assetPath))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是特效贴图路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsEffectTexPath(string assetPath)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (FileUtilitys.IsTexturePath(assetPath))
            {
                int texSize = 0;
                if (HeroResOptimal.IsCharacterEffectTexturePath(assetPath) || HeroResOptimal.IsSpecialEffectTexPath(assetPath, ref texSize))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是特殊特效贴图路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsSpecialEffectTexPath(string assetPath, ref int texSize)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            if (FileUtilitys.IsTexturePath(assetPath))
            {
                if (HeroResOptimal.IsCharacterEffectTextureSpecialPath(assetPath, ref texSize))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是shader路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static bool IsShaderPath(string assetPath)
    {
        if (!string.IsNullOrEmpty(assetPath))
        {
            assetPath = assetPath.Replace("\\", "/");
            return assetPath.EndsWith(".shader");
        }

        return false;
    }

    #endregion 资源优化路径判断

    #region 动画优化处理

    /// <summary>
    /// 优化英雄动画
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="path"></param>
    static void OptimalHeroAnimation(uint heroId, string path)
    {
        if(!animFileNeedOptimal)
        {
            return;
        }

        if(animOptimalPoly == EAnimOptimalPoly.Dir)
        {
            string directoryPath = Path.GetDirectoryName(path);
            if (processedAssetsList.Contains(directoryPath))
            {//已处理过该目录
                return;
            }

            if (path.EndsWith(CAnimationExtension))
            {
                if (IsCharacterAnimPath(path))
                {
                    processedAssetsList.Add(directoryPath);
                    List<string> fbxList = new List<string>();
                    FileUtilitys.GetFilesOnDir(directoryPath, fbxList, CFbxExtension);

                    if (fbxList != null && fbxList.Count > 0)
                    {
                        animABNameDic.Clear();
                        RecordAnimationABName(directoryPath);
                        FileUtilitys.DelDir((directoryPath + "/" + CAnimTmpDirName).Replace("\\", "/"));
                        foreach (var t in fbxList)
                        {
                            var fbxPath = FileUtilitys.GetAssetPathOnFullPath(t);
                            if (IsFilterAniName(fbxPath))
                            {
                                continue;
                            }

                            ModifyAnimationCompressionToOptimal(fbxPath);
                            CopyAnimationsFromFBX(fbxPath, directoryPath);
                        }

                        FileUtilitys.DelDir((directoryPath + "/" + CAnimTmpDirName).Replace("\\", "/"));
                        AssetDatabase.SaveAssets();
                        AssetDatabase.Refresh();
                        Resources.UnloadUnusedAssets();
                        System.GC.Collect();

                        SetAnimationFloat3OnDir(directoryPath);
                        SetAnimationABName(directoryPath);
                    }
                }
            }

            animABNameDic.Clear();
        }
        else if(animOptimalPoly == EAnimOptimalPoly.Self)
        {
            OptimalHeroAnimationBySelf(heroId, path);
        }
    }

    /// <summary>
    /// 动画文件自身优化
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="path"></param>
    static void OptimalHeroAnimationBySelf(uint heroId, string path)
    {
        if (path.EndsWith(CAnimationExtension))
        {
            if (IsCharacterAnimPath(path))
            {
                if (IsFilterAniName(path))
                {
                    return;
                }

                SetAnimationFloat3OnPath(path);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                SetAnimationFloat3OnPath(path);
                AssetDatabase.Refresh();
            }
        }
    }

    /// <summary>
    /// 是否是角色Prefab路径
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    public static bool IsCharacterPrefabPath(string path)
    {
        var charactersDirL = charactersDir.ToLower();
        var charactersPrefabDirL = charactersPrefabDir.ToLower();
        var pathL = path.ToLower();

        if (pathL.Contains(charactersDirL) && pathL.Contains(charactersPrefabDirL))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 是否是角色动画路径
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    public static bool IsCharacterAnimPath(string path)
    {
        var charactersDirL = charactersDir.ToLower();
        var charactersAnimDirL = charactersAnimDir.ToLower();
        var pathL = path.ToLower();

        if (pathL.Contains(charactersDirL) && pathL.Contains(charactersAnimDirL))
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 两个浮点数是否相等
    /// </summary>
    /// <param name="a"></param>
    /// <param name="b"></param>
    /// <param name="tolerance"></param>
    /// <returns></returns>
    public static bool AreEqual(float a, float b, float tolerance = 0.001f)
    {
        return Math.Abs(a - b) <= Math.Abs(tolerance);
    }

    /// <summary>
    /// 修改动画压缩方式为Optimal
    /// </summary>
    /// <param name="fbxPath"></param>
    static void ModifyAnimationCompressionToOptimal(string fbxPath)
    {
        ModelImporter modelImporter = AssetImporter.GetAtPath(fbxPath) as ModelImporter;
        if (modelImporter != null)
        {
            bool bRefresh = false;
            if(modelImporter.animationCompression != heroResOptimalOption.animCompression)
            {
                modelImporter.animationCompression = heroResOptimalOption.animCompression;
                bRefresh = true;
            }

            var animThresold = heroResOptimalOption.animThresold;
            if(IsSSAniName(fbxPath))
            {
                animThresold = heroResOptimalOption.ssAnimThreshold;
            }

            if (!HeroResOptimal.AreEqual(modelImporter.animationRotationError, animThresold) && 
                modelImporter.animationRotationError < animThresold)
            {
                modelImporter.animationRotationError = animThresold;
                bRefresh = true;
            }

            if (!HeroResOptimal.AreEqual(modelImporter.animationPositionError, animThresold) &&
                modelImporter.animationPositionError < animThresold)
            {
                modelImporter.animationPositionError = animThresold;
                bRefresh = true;
            }

            if (!HeroResOptimal.AreEqual(modelImporter.animationScaleError, animThresold) &&
                modelImporter.animationScaleError < animThresold)
            {
                modelImporter.animationScaleError = animThresold;
                bRefresh = true;
            }

            if(bRefresh)
            {
                modelImporter.SaveAndReimport();
            }
        }
    }

    /// <summary>
    /// 是否是需要过滤的动画名
    /// </summary>
    /// <param name="strPath"></param>
    /// <returns></returns>
    public static bool IsFilterAniName(string strPath)
    {
        if (!string.IsNullOrEmpty(strPath))
        {//动画压缩信息json配置表
            var pathTmp = strPath.Replace("\\", "/");
            pathTmp = FileUtilitys.GetAssetPathOnFullPath(pathTmp);
            if (!IsHeroAniNeedCompress(pathTmp))
            {
                return true;
            }
        }

        if (optimalStandShowAni)
        {
            return false;
        }

        if(!string.IsNullOrEmpty(strPath))
        {
            var strPathL = strPath.ToLower();
            foreach(var t in oPFilterAniNames)
            {
                if(strPathL.Contains(t.ToLower()))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是stand,show动画
    /// </summary>
    /// <param name="strPath"></param>
    /// <returns></returns>
    public static bool IsSSAniName(string strPath)
    {
        if (!string.IsNullOrEmpty(strPath))
        {
            var strPathL = strPath.ToLower();
            foreach (var t in oPFilterAniNames)
            {
                if (strPathL.Contains(t.ToLower()))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 从fbx拷贝动画
    /// </summary>
    /// <param name="fbxPath"></param>
    /// <param name="animDir"></param>
    static string CopyAnimationsFromFBX(string fbxPath, string animDir)
    {
        string strAnimClipPath = "";
        UnityEngine.Object[] assets = AssetDatabase.LoadAllAssetsAtPath(fbxPath);
        string strAnimDir = Path.GetDirectoryName(fbxPath);
        if (assets != null)
        {
            foreach (var t in assets)
            {
                var tAnimClip = t as AnimationClip;
                if (tAnimClip != null)
                {
                    var animClipName = tAnimClip.name;
                    string[] findFilePath = null;
                    if (!animClipName.Contains(fbxAnimWithPreview) && FileUtilitys.GetFilePathByFileName(animDir.Replace("\\", "/"), animClipName + CAnimationExtension, ref findFilePath))
                    {
                        strAnimClipPath = strAnimDir + "/" + animClipName + CAnimationExtension;
                        strAnimClipPath = strAnimClipPath.Replace("\\", "/");
                        var gh = GameObject.Instantiate(tAnimClip);
                        string targetAnimationPath = (animDir + "/" + animClipName + CAnimationExtension).Replace("\\", "/");
                        string targetAnimationTmpPath = (animDir + "/" + CAnimTmpDirName + "/" + animClipName + CAnimationExtension).Replace("\\", "/");

                        FileUtilitys.CheckDirectory(targetAnimationTmpPath);

                        AssetDatabase.CreateAsset(gh, targetAnimationTmpPath);
                        File.Copy(targetAnimationTmpPath, targetAnimationPath, true);
                    }
                }
            }
        }

        return strAnimClipPath;
    }

    /// <summary>
    /// 记录动画文件的ABName
    /// </summary>
    /// <param name="animDir"></param>
    static void RecordAnimationABName(string animDir)
    {
        string[] files = Directory.GetFiles(animDir, $"*{CAnimationExtension}", SearchOption.AllDirectories);
        if (files != null)
        {
            foreach (var t in files)
            {
                var assetPath = FileUtilitys.GetAssetPathOnFullPath(t);
                if (IsFilterAniName(assetPath))
                {
                    continue;
                }

                var assetImporter = AssetImporter.GetAtPath(assetPath);
                if (assetImporter != null)
                {
                    var assetBundleName = assetImporter.assetBundleName;
                    if (!string.IsNullOrEmpty(assetBundleName))
                    {
                        animABNameDic[t.Replace("\\", "/")] = assetBundleName;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 设置动画文件的ABName
    /// </summary>
    /// <param name="animDir"></param>
    static void SetAnimationABName(string animDir)
    {
        string[] files = Directory.GetFiles(animDir, $"*{CAnimationExtension}", SearchOption.AllDirectories);
        if (files != null)
        {
            foreach (var t in files)
            {
                var animPath = t.Replace("\\", "/");
                if (animABNameDic.TryGetValue(animPath, out var result))
                {
                    var assetPath = FileUtilitys.GetAssetPathOnFullPath(t);
                    if (IsFilterAniName(assetPath))
                    {
                        continue;
                    }

                    var assetImporter = AssetImporter.GetAtPath(assetPath);
                    if (assetImporter != null)
                    {
                        assetImporter.assetBundleName = result;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 设置路径动画scale曲线保持小数点3位
    /// </summary>
    /// <param name="animPath"></param>
    static void SetAnimationFloat3OnPath(string animPath)
    {
        var animOpt = _GetNewAOpt(animPath);
        if (animOpt != null)
        {
            animOpt?.Optimize_Scale_Float3(false, 3);  
        }
    }

    /// <summary>
    /// 设置目录下动画scale曲线保持小数点3位
    /// </summary>
    /// <param name="animDir"></param>
    static void SetAnimationFloat3OnDir(string animDir)
    {
        var animList = FindAnims(animDir);
        if (animList != null)
        {
            foreach (var t in animList)
            {
                if(IsFilterAniName(t.path.Replace("\\", "/")))
                {
                    continue;
                }

                t?.Optimize_Scale_Float3(false, 3);
            }
        }
    }

    /// <summary>
    /// 生成AnimationOpt结构
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static AnimationOpt _GetNewAOpt(string path)
    {
        AnimationOpt opt = null;
        AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(path);
        if (clip != null)
        {
            opt = new AnimationOpt(path, clip);
        }

        return opt;
    }

    /// <summary>
    /// 查找某个路径下的AnimationOpt结构列表
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static List<AnimationOpt> FindAnims(string path)
    {
        List<AnimationOpt> assets = new List<AnimationOpt>();
        string[] files = Directory.GetFiles(path, $"*{CAnimationExtension}", SearchOption.AllDirectories);
        if (files != null)
        {
            for (int j = 0; j < files.Length; j++)
            {
                string assetPath = files[j];
                if (IsFilterAniName(assetPath))
                {
                    continue;
                }

                AnimationOpt animopt = _GetNewAOpt(assetPath);
                if (animopt != null)
                {
                    assets.Add(animopt);
                }
            }
        }

        return assets;
    }

    #endregion 动画优化处理

    #region 模型贴图优化

    /// <summary>
    /// 优化英雄模型贴图
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="path"></param>
    /// <param name="heroResOptimalOption"></param>
    /// <param name="addToSet"></param>
    /// <returns></returns>
    public static bool OptimalHeroModelTexture(uint heroId, string path, HeroResOptimalOption heroResOptimalOption, bool addToSet)
    {
        bool bRet = false;
        if (FileUtilitys.IsTexturePath(path))
        {
            if (IsCharacterModelTexturePath(path))
            {
                bool bSatisfiy = false;
                if(charaTexOptimalPoly == ECharaTexOptimalPoly.Dir)
                {
                    bSatisfiy = true;
                }
                else if (charaTexOptimalPoly == ECharaTexOptimalPoly.UseTheFile)
                {
                    bSatisfiy = IsHeroModelTexNeedOptimal((int)heroId, path);
                }

                if(bSatisfiy)
                {
                    var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    if (t2d != null)
                    {
                        var nowWidth = t2d.width;
                        var nowHeight = t2d.height;
                        var size = (int)heroResOptimalOption.chaTexMax;

                        if (nowWidth > size || nowHeight > size)
                        {
                            bRet = true;
                            if (addToSet)
                            {
                                AddSetTextureMaxSize(path, size);
                            }
                        }
                    }
                }
            }
        }

        return bRet;
    }

    /// <summary>
    /// 是否是角色模型贴图路径
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static bool IsCharacterModelTexturePath(string path)
    {
        var charactersDirL = charactersDir.ToLower();
        var charactersTextureDirL = charactersTextureDir.ToLower();
        var pathL = path.ToLower();

        if (pathL.Contains(charactersDirL) && pathL.Contains(charactersTextureDirL))
        {
            return true;
        }

        return false;
    }

    #endregion 模型贴图优化

    #region 特效贴图优化

    /// <summary>
    /// 英雄特效贴图
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="assetPath"></param>
    /// <param name="heroResOptimalOption"></param>
    /// <param name="addToSet"></param>
    /// <returns></returns>
    public static bool OptimalHeroEffectTexture(uint heroId, string assetPath, HeroResOptimalOption heroResOptimalOption, bool addToSet)
    {
        bool bRet = false;
        if (FileUtilitys.IsTexturePath(assetPath))
        {
            int texSize = 0;
            if (IsCharacterEffectTexturePath(assetPath))
            {
                var effectTexNorW = (int)heroResOptimalOption.genEffMax;
                var effectTexNorH = effectTexNorW;
                var effectTexSeqW = (int)heroResOptimalOption.xuEffMax;
                var effectTexSeqH = effectTexSeqW;
                var effectTexNoiseW = (int)heroResOptimalOption.noiseEffMax;
                var effectTexNoiseH = effectTexNoiseW;
                var effectTexGlowW = (int)heroResOptimalOption.guangyunEffMax;
                var effectTexGlowH = effectTexGlowW;
                var effectTexMaskW = (int)heroResOptimalOption.maskEffMax;
                var effectTexMaskH = effectTexMaskW;

                int width = effectTexNorW;
                int height = effectTexNorH;
                var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
                var assetPathL = assetPath.ToLower();
                var nowWidth = t2d.width;
                var nowHeight = t2d.height;

                if (assetPathL.Contains(effectXuLie_))
                {
                    width = effectTexSeqW;
                    height = effectTexSeqH;
                }
                else if (assetPathL.Contains(effectNoise))
                {
                    width = effectTexNoiseW;
                    height = effectTexNoiseH;
                }
                else if (assetPathL.Contains(effectGlow))
                {
                    width = effectTexGlowW;
                    height = effectTexGlowH;
                }
                else if (assetPathL.Contains(effectMask))
                {
                    width = effectTexMaskW;
                    height = effectTexMaskH;
                }

                bRet = AddSetTextureMaxSize(assetPath, nowWidth, nowHeight, width, height, addToSet);
            }
            else if (HeroResOptimal.IsCharacterEffectTextureSpecialPath(assetPath, ref texSize))
            {
                var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
                var nowWidth = t2d.width;
                var nowHeight = t2d.height;

                bRet = AddSetTextureMaxSize(assetPath, nowWidth, nowHeight, texSize, texSize, addToSet);
            }
        }

        return bRet;
    }

    /// <summary>
    /// 添加贴图设置大小路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="nowWidth"></param>
    /// <param name="nowHeight"></param>
    /// <param name="width"></param>
    /// <param name="height"></param>
    /// <param name="addToSet"></param>
    /// <returns></returns>
    static bool AddSetTextureMaxSize(string assetPath, int nowWidth, int nowHeight, int width, int height, bool addToSet)
    {
        bool bRet = false;
        if (nowWidth > width || nowHeight > height)
        {
            bRet = true;
            var tWidth = nowWidth > width ? width : nowWidth;
            var tHeight = nowHeight > height ? height : nowHeight;

            var mW = tWidth >= tHeight ? tWidth : tHeight;
            if (addToSet)
            {
                AddSetTextureMaxSize(assetPath, mW);
            }
        }

        return bRet;
    }

    /// <summary>
    /// 添加贴图设置大小路径
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="size"></param>
    static void AddSetTextureMaxSize(string assetPath, int size)
    {
        texSetSizeDic[assetPath] = size;
    }

    /// <summary>
    /// 设置贴图尺寸
    /// </summary>
    static void ApplySetTextureMaxSize()
    {
        if(texSetSizeDic != null)
        {
            foreach(var t in texSetSizeDic)
            {
                SetTextureMaxSize(t.Key, t.Value);
            }
        }
    }

    /// <summary>
    /// 是否是角色特效贴图路径
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static bool IsCharacterEffectTexturePath(string path)
    {
        var pathL = path.ToLower();
        var fileNameL = Path.GetFileName(pathL);
        var cEffectNamePrefixL = CEffectNamePrefix.ToLower();

        if (fileNameL.Contains(cEffectNamePrefixL))
        {
            if(CEffectDirPrefix == null || CEffectDirPrefix.Count <= 0)
            {
                return true;
            }

            foreach(var t in CEffectDirPrefix)
            {
                if(pathL.Contains(t.ToLower()))
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是特效目录
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static bool IsCharacterEffectTextureDir(string path)
    {
        var pathL = path.ToLower();
        if (CEffectDirPrefix == null || CEffectDirPrefix.Count <= 0)
        {
            return true;
        }

        foreach (var t in CEffectDirPrefix)
        {
            if (pathL.Contains(t.ToLower()))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 是否是特效源目录
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    static bool IsCharacterSourceEffectTextureDir(string path)
    {
        var pathL = path.ToLower();
        foreach (var t in CEffectSourceDirPrefix)
        {
            if (pathL.Contains(t.ToLower()))
            {
                return true;
            }
        }

        return false;
    }

    #endregion 特效贴图优化

    /// <summary>
    /// 添加数据
    /// </summary>
    /// <param name="list"></param>
    /// <param name="iValue"></param>
    /// <returns></returns>
    static bool AddUniqueInt(List<int> list, int iValue)
    {
        if (list != null)
        {
            if (!list.Contains(iValue))
            {
                list.Add(iValue);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 添加数据
    /// </summary>
    /// <param name="list"></param>
    /// <param name="strValue"></param>
    /// <returns></returns>
    static bool AddUniqueString(List<string> list, string strValue)
    {
        if (list != null)
        {
            if (!list.Contains(strValue))
            {
                list.Add(strValue);
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 处理英雄资源优化参数
    /// </summary>
    /// <param name="_optimalOption"></param>
    /// <returns></returns>
    static bool ProcessHeroResOptimalOption(HeroResOptimalOption _optimalOption)
    {
        heroResOptimalOption = _optimalOption;
        return true;
    }

    /// <summary>
    /// 所有英雄资源优化入口
    /// </summary>
    /// <param name="optimalOption"></param>
    public static void DoHeroAllResOptimal(HeroResOptimalOption _optimalOption)
    {
        DoHeroResOptimal(GetAllHeroIds(), _optimalOption);
    }

    /// <summary>
    /// 处理Effect_Source
    /// </summary>
    /// <param name="assetList"></param>
    /// <returns></returns>
    static List<string> ProcessEffectSource(List<string> assetList)
    {
        List<string> effectsSource = new List<string>();
        if (assetList == null || assetList.Count <= 0)
        {
            return effectsSource;
        }

        var prefabPaths = CollectPrefabPath(assetList);
        if(prefabPaths == null || prefabPaths.Count <= 0)
        {
            return effectsSource;
        }

        foreach(var t in prefabPaths)
        {
            var tPath = t.Replace("\\", "/");
            if(IsCharacterEffectTextureDir(tPath))
            {//如果是特效目录
                if(IsCharacterSourceEffectTextureDir(tPath))
                {
                    AddUniqueString(effectsSource, tPath);
                }
                else
                {
                    var fileName = Path.GetFileName(tPath);
                    if(!string.IsNullOrEmpty(fileName))
                    {
                        string[] findFilePaths = null;
                        foreach(var tPathDir in CEffectSourceDirPrefix)
                        {
                            if (FileUtilitys.GetFilePathByFileName(tPathDir, fileName, ref findFilePaths))
                            {
                                if (findFilePaths.Length >= 2)
                                {
                                    UnityEngine.Debug.LogError($"【英雄资源优化】查找定位文件名:{fileName}在路径:{CEffectSourceDirPrefix}下数量大于1!!!!!!");
                                }

                                var filePath = findFilePaths[0].Replace("\\", "/");
                                AddUniqueString(effectsSource, filePath);
                            }
                        }
                    }
                }
            }
        }

        return effectsSource;
    }

    /// <summary>
    /// 收集prefab路径
    /// </summary>
    /// <param name="assetList"></param>
    /// <returns></returns>
    static List<string> CollectPrefabPath(List<string> assetList)
    {
        List<string> prefabsList = new List<string>();
        if(assetList != null)
        {
            foreach(var t in assetList)
            {
                if(t.EndsWith(".prefab"))
                {
                    prefabsList.Add(t);
                }
            }
        }

        return prefabsList;
    }

    //[MenuItem("Tools/HeroResOptimal/HeroResOptimal(英雄资源优化) &q")]
    /// <summary>
    /// 英雄资源优化入口
    /// </summary>
    /// <param name="_opHeroIDList"></param>
    /// <param name="optimalOption"></param>
    public static void DoHeroResOptimal(List<int> _opHeroIDList, HeroResOptimalOption _optimalOption)
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
        processedAssetsList.Clear();
        texSetSizeDic.Clear();
        ProcessOptimalHeroIDs(_opHeroIDList);
        heroDependsDic = GetHeroResDependContent();
        ProcessHeroResOptimalOption(_optimalOption);
        DoCheckConfig();

        if (opHeroIDList.Count <= 0)
        {
            UnityEngine.Debug.LogError("【英雄资源优化】需要优化的英雄ID列表数量小于0!!!!!!");
            return;
        }

        if (heroDependsDic.Count <= 0)
        {
            UnityEngine.Debug.LogError("【英雄资源优化】需要优化的英雄的依赖数量小于0!!!!!!");
            return;
        }

        OptimalHeros();

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    /// <summary>
    /// 对资源进行优化
    /// </summary>
    /// <param name="_assetsPath"></param>
    /// <param name="_optimalOption"></param>
    public static void DoAssetOptimal(List<string> _assetsPath, HeroResOptimalOption _optimalOption)
    {
        if(_assetsPath == null || _assetsPath.Count <= 0)
        {
            return;
        }

        System.GC.Collect();
        Resources.UnloadUnusedAssets();
        processedAssetsList.Clear();
        texSetSizeDic.Clear();
        ProcessHeroResOptimalOption(_optimalOption);
        DoCheckConfig();

        OptimalAssets(_assetsPath);

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    /// <summary>
    /// 生成英雄资源依赖文件
    /// </summary>
    //[MenuItem("Tools/HeroResOptimal/HeroResOptimal(英雄资源优化) &q")]
    public static void DoGenerateAllHeroResDependFile()
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();

        ProcessConfigFullPath();
        ProcessHeroConst();
        ProcessCsvFullPath();
        CheckStandEffectField();
        if (!CheckHeroCsvs())
        {
            return;
        }

        SvnUpdateCsvFullPath();

        var heroNameDic = GetReadHeroNameContent();
        Dictionary<int, int> heroSkinDic = new Dictionary<int, int>();
        Dictionary<int, HeroResInfoOptimal> tHeroResDic = new Dictionary<int, HeroResInfoOptimal>();
        List<int> heroIDList = new List<int>();
        for (int i = 1, j = heroIDMax; i <= j; i++)
        {
            var heroCfg = GetHeroConfig(i);
            if (heroCfg != null && heroCfg.Count > 0)
            {
                AddUniqueInt(heroIDList, i);

                var rcList = GetSkinConfig(i);
                if (rcList != null)
                {
                    foreach (var t in rcList)
                    {
                        var strID = t["ID"];
                        if (!string.IsNullOrEmpty(strID) && int.TryParse(strID, out var result))
                        {
                            if (AddUniqueInt(heroIDList, result))
                            {
                                heroSkinDic[result] = i;
                            }
                        }
                    }
                }
            }
        }

        int idMax = heroIDList.Count;
        for (int i = 0, j = idMax; i < j; i++)
        {
            var heroID = heroIDList[i];
            int heroSkinID = 0;
            if (heroSkinDic.TryGetValue(heroID, out var heroIDTmp))
            {
                heroSkinID = heroID;
                heroID = heroIDTmp;
                var skinCfg = GetSkinConfigOnHeroSkinID(heroID, heroSkinID);
                if (skinCfg == null)
                {
                    continue;
                }

                heroSkinID = int.Parse(skinCfg["Index"]);
            }

            var heroCfg = GetHeroConfig(heroID);
            if (heroCfg != null && heroCfg.Count > 0)
            {
                bool bModelPathEmpty = false;
                var tList = GetHeroRes(heroID, heroMaxLev, heroMaxStar, heroSkinID, out bModelPathEmpty);
                if (tList != null && tList.Count > 0 && !bModelPathEmpty)
                {
                    if (heroSkinID > 0)
                    {
                        heroID = heroIDList[i];
                    }

                    HeroResInfoOptimal heroResInfoOptimal = new HeroResInfoOptimal();
                    heroResInfoOptimal.heroDepends = tList;
                    if (heroNameDic.TryGetValue(heroID, out var heroName))
                    {
                        heroResInfoOptimal.heroName = heroName;
                    }

                    tHeroResDic[heroID] = heroResInfoOptimal;
                }
            }
        }

        //CorrectHeroSkinRes(heroSkinDic, tHeroResDic);

        if (tHeroResDic != null && tHeroResDic.Count > 0)
        {
            //写入文件
            string output = Newtonsoft.Json.JsonConvert.SerializeObject(tHeroResDic, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(FileUtilitys.GetCurFullPathOnAssets(CHeroResDependPath), output);
        }

        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    /// <summary>
    /// 矫正英雄皮肤资源
    /// </summary>
    /// <param name="heroSkinDic"></param>
    /// <param name="tHeroResDic"></param>
    static void CorrectHeroSkinRes(Dictionary<int, int> heroSkinDic, Dictionary<int, HeroResInfoOptimal> tHeroResDic)
    {
        if(heroSkinDic != null && tHeroResDic != null)
        {
            string hit = "art/skill/common/common_hit.playable";
            string die = "art/skill/common/common_die.playable";
            string fakeDeath = "art/skill/common/common_fake_die.playable";

            List<int> heroResWarnList = new List<int>();
            foreach (var kv in heroSkinDic)
            {
                var heroSkinID = kv.Key;
                var heroBaseID = kv.Value; 
                if(tHeroResDic.TryGetValue(heroSkinID, out var heroRes) && tHeroResDic.TryGetValue(heroBaseID, out var heroBaseRes))
                {
                    var heroResList = heroRes.heroDepends;
                    var heroBaseResList = heroBaseRes.heroDepends;
                    List<string> heroResListNow = new List<string>();
                    string strSkinModel = "";
                    foreach (var t in heroResList)
                    {
                        if(t.Contains(hit) || t.Contains(die) || t.Contains(fakeDeath) || t.EndsWith(".prefab"))
                        {
                            if(t.EndsWith(".prefab"))
                            {
                                var index = t.LastIndexOf('/');
                                if (index >= 0)
                                {
                                    var strPrefab = t.Substring(index + 1);
                                    var strPrefabArr = strPrefab.Split('_');
                                    if(strPrefabArr != null && strPrefabArr.Length <= 1)
                                    {
                                        strSkinModel = strPrefab.Substring(0, strPrefab.LastIndexOf('.'));
                                    }
                                }
                            }

                            heroResListNow.Add(t);
                        }
                    }

                    if(string.IsNullOrEmpty(strSkinModel))
                    {
                        heroResWarnList.Add(heroSkinID);
                        UnityEngine.Debug.LogWarning($"【英雄资源优化】生成英雄资源依赖文件原英雄ID:{heroBaseID}对应的皮肤ID:{heroSkinID}配置有问题");
                    }

                    foreach (var t in heroBaseResList)
                    {
                        if (t.Contains(hit) || t.Contains(die) || t.Contains(fakeDeath) || t.EndsWith(".prefab"))
                        {
                            continue;
                        }

                        var index = t.LastIndexOf('/');
                        if (index >= 0)
                        {
                            var strPrefabLeft = t.Substring(0, index + 1);
                            var strPrefab = t.Substring(index + 1);
                            var strPrefabArr = strPrefab.Split('_');
                            if (strPrefabArr != null && strPrefabArr.Length > 1)
                            {
                                strPrefabArr[0] = strSkinModel;
                                StringBuilder sb = new StringBuilder();
                                sb.Append(strPrefabLeft);
                                int len = strPrefabArr.Length;
                                int cIndex = 0;
                                foreach (var a in strPrefabArr)
                                {
                                    sb.Append(a);
                                    cIndex++;

                                    if(cIndex < len)
                                    {
                                        sb.Append("_");
                                    }
                                }

                                heroResListNow.Add(sb.ToString());
                            }
                        }
                    }

                    tHeroResDic[heroSkinID].heroDepends = heroResListNow;
                }
            }

            foreach(var t in heroResWarnList)
            {
                if(tHeroResDic.TryGetValue(t, out var result))
                {
                    tHeroResDic.Remove(t);
                }
            }
        }
    }

    /// <summary>
    /// 获取英雄资源依赖字典信息
    /// </summary>
    public static Dictionary<int, HeroResInfoOptimal> GetHeroResDependContent()
    {
        Dictionary<int, HeroResInfoOptimal> tHeroResDic = new Dictionary<int, HeroResInfoOptimal>();

        try
        {
            string input = File.ReadAllText(FileUtilitys.GetCurFullPathOnAssets(CHeroResDependPath));
            tHeroResDic = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<int, HeroResInfoOptimal>>(input);
        }
        catch(Exception e)
        {
            UnityEngine.Debug.LogError($"【英雄资源优化】GetHeroResDependContent()发生错误:{e.ToString()}");
        }

        return tHeroResDic;
    }

    /// <summary>
    /// 获取所有英雄ID
    /// </summary>
    /// <returns></returns>
    public static List<int> GetAllHeroIds()
    {
        List<int> heroIds = new List<int>();
        var heroIDsDic = GetHeroResDependContent();
        foreach(var g in heroIDsDic)
        {
            heroIds.Add(g.Key);
        }

        return heroIds;
    }

    /// <summary>
    /// 获取特效类型
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    public static EffectTypeE GetEffectType(string assetPath)
    {
        EffectTypeE effectTypeE = EffectTypeE.General;
        var assetPathL = assetPath.ToLower();

        if (assetPathL.Contains(effectXuLie_))
        {
            effectTypeE = EffectTypeE.Seq;
        }
        else if (assetPathL.Contains(effectNoise))
        {
            effectTypeE = EffectTypeE.Noise;
        }
        else if (assetPathL.Contains(effectGlow))
        {
            effectTypeE = EffectTypeE.glow;
        }
        else if (assetPathL.Contains(effectMask))
        {
            effectTypeE = EffectTypeE.mask;
        }

        return effectTypeE;
    }

    /// <summary>
    /// 获取特效类型描述
    /// </summary>
    /// <param name="effectTypeE"></param>
    /// <returns></returns>
    public static string GetEffectTypeDesc(EffectTypeE effectTypeE)
    {
        string strEffectDesc = "常规";

        if (effectTypeE.Equals(EffectTypeE.Seq))
        {
            strEffectDesc = "序列";
        }
        else if (effectTypeE.Equals(EffectTypeE.Noise))
        {
            strEffectDesc = "噪声";
        }
        else if (effectTypeE.Equals(EffectTypeE.glow))
        {
            strEffectDesc = "光晕";
        }
        else if (effectTypeE.Equals(EffectTypeE.mask))
        {
            strEffectDesc = "遮罩";
        }

        return strEffectDesc;
    }

    /// <summary>
    /// 检测设置配置
    /// </summary>
    public static void DoCheckConfig()
    {
        var filePath = FileUtilitys.GetCurFullPathOnAssets(CConfigPath);
        using (StreamReader ReaderObject = new StreamReader(filePath))
        {
            string line;
            while ((line = ReaderObject.ReadLine()) != null)
            {
                if (line.StartsWith("#"))
                {//注释行
                    continue;
                }

                var contents = line.Split('=');
                if (contents != null && contents.Length >= 2)
                {
                    var strKey = contents[0];
                    var strContent = contents[1];
                    try
                    {
                        if (!string.IsNullOrEmpty(strKey) && !string.IsNullOrEmpty(strContent))
                        {
                            if (strKey == CCharaDirKey)
                            {//角色目录
                                charactersDir = strContent;
                            }
                            if (strKey == CCharaPrefabDirKey)
                            {//角色Prefab目录
                                charactersPrefabDir = strContent;
                            }
                            else if (strKey == CCharaAnimDirKey)
                            {//角色动画目录
                                charactersAnimDir = strContent;
                            }
                            else if (strKey == CCharactersTextureDirKey)
                            {//角色贴图目录
                                charactersTextureDir = strContent;
                            }
                            else if (strKey == CEffectNamePrefixKey)
                            {//特效贴图名字前缀
                                CEffectNamePrefix = strContent;
                            }
                            else if (strKey == CEffectSourceDirPrefixKey)
                            {//特效源目录前缀
                                CEffectSourceDirPrefix.Clear();
                                var contentsArr = strContent.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
                                if (contentsArr != null)
                                {
                                    foreach (var t in contentsArr)
                                    {
                                        CEffectSourceDirPrefix.Add(t);
                                    }
                                }
                            }
                            else if (strKey == CEffectDirPrefixKey)
                            {//特效贴图目录
                                CEffectDirPrefix.Clear();
                                var contentsArr = strContent.Split(new string[] { "||" }, StringSplitOptions.RemoveEmptyEntries);
                                if(contentsArr != null)
                                {
                                    foreach(var t in contentsArr)
                                    {
                                        CEffectDirPrefix.Add(t);
                                    }
                                }
                            }
                            else if (strKey == CAnimOptimalPolyKey)
                            {//动画优化策略
                                if(int.TryParse(strContent, out var result))
                                {
                                    bool isDefined = Enum.IsDefined(typeof(EAnimOptimalPoly), result);
                                    if (isDefined)
                                    {
                                        animOptimalPoly = (EAnimOptimalPoly)result;
                                    }
                                }
                            }
                            else if (strKey == CAnimFbxNeedOptimalKey)
                            {//动画Fbx是否需要优化
                                if (int.TryParse(strContent, out var result))
                                {
                                    animFbxNeedOptimal = result > 0;
                                }
                            }
                            else if (strKey == CAnimFileNeedOptimalKey)
                            {//动画anim文件是否需要优化
                                if (int.TryParse(strContent, out var result))
                                {
                                    animFileNeedOptimal = result > 0;
                                }
                            }
                            else if (strKey == CCharaTexOptimalPolyKey)
                            {//角色贴图优化策略类型
                                if (int.TryParse(strContent, out var result))
                                {
                                    bool isDefined = Enum.IsDefined(typeof(ECharaTexOptimalPoly), result);
                                    if (isDefined)
                                    {
                                        charaTexOptimalPoly = (ECharaTexOptimalPoly)result;
                                    }
                                }
                            }
                            else if (strKey == CEffectDirSpecialPrefix)
                            {//角色特殊目录特效贴图优化
                                CheckEffectDirSpecial(strContent);
                            }
                            else if (strKey == CProjectMarkKey)
                            {//项目标识
                                CheckProjectMarkConfig(strContent);
                            }
                            else if (strKey == CCalMemoryTypeKey)
                            {//统计内存方式
                                CheckCalMemoryTypeConfig(strContent);
                            }
                            else if (strKey == CCharaAnimCompressInfoKey)
                            {//应用动画文件压缩信息
                                CheckCharaAnimCompressInfo(strContent);
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError("HeroResOptimal.DoCheckConfig()捕获到异常:" + e.ToString());
                    }
                }
            }
        }
    }
}

