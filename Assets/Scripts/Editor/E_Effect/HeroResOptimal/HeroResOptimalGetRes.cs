/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化
* 包含功能:1.英雄动画优化
*          2.英雄模型贴图优化
*          3.英雄技能所使用特效贴图优化
*
*=================================================================================*/
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using static CSVParser;
using War.Battle;
using System;
using System.Reflection;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    //-- 具体参数设置见 Effect.csv
    static Dictionary<int, List<string>> effectGroupBuffInfo = new Dictionary<int, List<string>>()
    {
        [2] = new List<string>() { "strParam4", "strParam5" },//--效果触发增加buff
        [6] = new List<string>() { "strParam8", "strParam9" },//--受到攻击使用技能
        [8] = new List<string>() { "strParam3", "strParam4" },//--死亡增加buff
        [11] = new List<string>() { "strParam5", "strParam6" },//--攻击增加BUFF
        [12] = new List<string>() { "strParam5", "strParam6" },//--检测目标Buff类型增加Buff
        [13] = new List<string>() { "strParam4", "strParam5" },//--检测当前生命增加Buff
        [17] = new List<string>() { "strParam4", "strParam5" },//-复活
        [18] = new List<string>() { "strParam2", "strParam3", "strParam4", "strParam5" },//--每回合增加buff
        [22] = new List<string>() { "strParam5", "strParam6" },//--检测攻击目标数量增加buff
        [23] = new List<string>() { "strParam4", "strParam5" },//--击杀获得buff
        [27] = new List<string>() { "strParam2", "strParam3" },//--生命回复增加buff
        [28] = new List<string>() { "strParam2", "strParam3" },//--增加buff
        [31] = new List<string>() { "strParam2", "strParam3" },//--攻击前增加BUFF
        [32] = new List<string>() { "strParam2", "strParam3" },//--优先攻击指定目标
        [33] = new List<string>() { "strParam4", "strParam5" },//--抵挡伤害
        [38] = new List<string>() { "strParam2", "strParam3", "strParam5", "strParam6" },//--随机增加BUFF
    };

    /// <summary>
    /// 站立特效字段信息
    /// </summary>
    static FieldInfo standEffectPathFI;

    /// <summary>
    /// 资源是否存在
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    static bool IsAssetExist(string assetPath)
    {
        if(string.IsNullOrEmpty(assetPath))
        {
            return false;
        }

        var objects = AssetDatabase.LoadAllAssetsAtPath(assetPath);
        return objects != null && objects.Length >= 1;
    }


    /// <summary>
    /// 获取英雄全部资源（技能、buff、模型）
    /// </summary>
    /// <param name="heroID"></param>
    /// <param name="heroLev"></param>
    /// <param name="heroStar"></param>
    /// <param name="skinIndex"></param>
    /// <param name="modelPathEmpty"></param>
    /// <returns></returns>
    static List<string> GetHeroRes(int heroID, int heroLev, int heroStar, int skinIndex, out bool modelPathEmpty)
    {
        modelPathEmpty = true;
        List<string> resList = new List<string>();

        ParseHeroSkillResPath(resList, heroID, heroLev, skinIndex);

        string hit = (string)GetCfgField(null, "hit", "art/skill/common/common_hit.playable");
        string die = (string)GetCfgField(null, "die", "art/skill/common/common_die.playable");
        string fakeDeath = "art/skill/common/common_fake_die.playable";

        if(IsAssetExist("Assets/" + hit))
        {
            resList.Add(hit);
        }

        if (IsAssetExist("Assets/" + die))
        {
            resList.Add(die);
        }

        if (IsAssetExist("Assets/" + fakeDeath))
        {
            resList.Add(fakeDeath);
        }

        int star = heroStar;
        int starsLv = star;

        var currentModel = ChangeHeroModel(heroID, starsLv, false, skinIndex);
        if (!string.IsNullOrEmpty(currentModel) && int.TryParse(currentModel, out var result))
        {
            var modulCfgs = GetModulConfig(result);
            if (modulCfgs != null && modulCfgs.Count > 0)
            {
                var modulCfg = modulCfgs[0];
                string standEffectPath = string.Empty;
                var modelPath = GetModelResPath(modulCfg["modelPath"]);
                if (!string.IsNullOrEmpty(modelPath)/*&& !IsSkinPath(modelPath)*/)
                {
                    modelPathEmpty = false;
                    AddUniqueString(resList, modelPath);
                    if (HasStandEffectPath(modelPath, ref standEffectPath))
                    {
                        AddUniqueString(resList, standEffectPath);
                    }
                }
            }
        }

        return resList;
    }

    /// <summary>
    /// 是否是皮肤路径
    /// </summary>
    /// <param name="modelPath"></param>
    /// <returns></returns>
    static bool IsSkinPath(string modelPath)
    {
        if(modelPath.EndsWith(".prefab"))
        {
            var modelPathTmp = modelPath.Replace("\\", "/");
            var sIndex = modelPathTmp.LastIndexOf('/');
            if(sIndex >= 0)
            {
                var fileName = modelPathTmp.Substring(sIndex + 1);
                return Regex.IsMatch(fileName, @"\d");
            }
        }

        return false;
    }

    /// <summary>
    /// 是否有站立特效
    /// </summary>
    /// <param name="modelPath"></param>
    /// <returns></returns>
    static bool HasStandEffectPath(string modelPath, ref string standEffectPath)
    {
        standEffectPath = string.Empty;
        if (modelPath.EndsWith(".prefab"))
        {
            var modelPathReal = (CAsset + "/" + modelPath).Replace("\\", "/");
            var gameObj = AssetDatabase.LoadMainAssetAtPath(modelPathReal) as GameObject;
            if(gameObj != null)
            {
                var tScript = gameObj.GetComponent<CardConfig>();
                if(tScript != null && standEffectPathFI != null)
                {
                    var objV = GetFieldValue(standEffectPathFI, tScript);
                    if(objV != null)
                    {
                        var strStandEffectPath = (string)objV;
                        if (!string.IsNullOrEmpty(strStandEffectPath))
                        {
                            standEffectPath = strStandEffectPath;
                            return true;
                        }
                    }
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 解析英雄技能资源
    /// </summary>
    /// <param name="resList"></param>
    /// <param name="heroId"></param>
    /// <param name="level"></param>
    /// <param name="skinIndex"></param>
    static void ParseHeroSkillResPath(List<string> resList, int heroId, int level, int skinIndex)
    {
        var skillsID = GetHeroSkillsID(heroId);
        if (skillsID == null || skillsID.Count <= 0)
        {
            return;
        }

        int skillNum = 1;
        var skinCfgs = GetSkinConfigOnIndex(skinIndex);
        if (skinCfgs != null && skinCfgs.Count > 0)
        {
            if (int.TryParse(skinCfgs[0]["SkillNum"], out var skillNumTmp))
            {
                skillNum = skillNumTmp;
            }
        }
        var skillLvs = GetHeroSkillsLv(heroId, level);
        var skillLvsCount = skillLvs.Count;
        var processedBuffList = new List<int>();

        for (int i = 0, j = skillsID.Count; i < j; i++)
        {
            if( i <= skillLvsCount - 1)
            {
                var cfgs = GetHeroSkillConfig(skillsID[i], skillLvs[i]);
                if(cfgs != null && cfgs.Count > 0)
                {
                    var cfg_skill = cfgs[0];
                    if (cfg_skill != null)
                    {
                        var paramGroup = cfg_skill["paramGroup"];
                        var effectGroup = cfg_skill["effectGroup"];
                        if (!string.IsNullOrEmpty(paramGroup) && !string.IsNullOrEmpty(effectGroup))
                        {
                            var paramGroupArr = paramGroup.Split(';');
                            var effectGroupArr = effectGroup.Split('#');
                            if(paramGroupArr != null && effectGroupArr != null)
                            {
                                var paramGroupArrLen = paramGroupArr.Length;
                                var effectGroupArrLen = effectGroupArr.Length;

                                for(int k = 0, m = effectGroupArrLen; k < m; k++)
                                {
                                    if(k <= paramGroupArrLen - 1)
                                    {
                                        var property = paramGroupArr[k];
                                        var v = effectGroupArr[k];
                                        if(!string.IsNullOrEmpty(property))
                                        {
                                            if (v == "1")//-- 1技能2加BUFF3加属性
                                            {
                                                //-- 技能id#权重
                                                var skillProperty = property.Split('#');
                                                if(skillProperty != null && skillProperty.Length > 0)
                                                {
                                                    if(int.TryParse(skillProperty[0], out var result))
                                                    {
                                                        var strPath = "strPath";
                                                        var skillCfg = GetSkillConfig(result);
                                                        if (skinIndex > 0)
                                                        {
                                                            skillCfg = GetSkinSkillConfig(result);
                                                            strPath = strPath + skillNum;
                                                        }

                                                        if (skillCfg != null && skillCfg.Count > 0)
                                                        {
                                                            var skillResPath = GetCfgField(skillCfg[0], strPath, "art/skill/common/common_atk.playable");
                                                            //--公共攻击
                                                            AddUniqueString(resList, (string)skillResPath);
                                                        }
                                                    }
                                                }
                                            }
                                            else if (v == "2")
                                            {//-- buff id#等级
                                                var buffProperty = property.Split('#');
                                                if(buffProperty != null && buffProperty.Length >= 2)
                                                {
                                                    GetBuffFxResource(int.Parse(buffProperty[0]), int.Parse(buffProperty[1]), resList, processedBuffList);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取Buff特效资源
    /// </summary>
    /// <param name="buffId"></param>
    /// <param name="buffLevel"></param>
    /// <param name="resList"></param>
    /// <param name="processedBuffList"></param>
    static void GetBuffFxResource(int buffId, int buffLevel, List<string> resList, List<int> processedBuffList)
    {
        //--防止buff与effect之间循环引用
        if(processedBuffList.Contains(buffId))
        {
            return;
        }

        processedBuffList.Add(buffId);

        var cfgs = GetBuffConfig(buffId, buffLevel);
        if(cfgs == null || cfgs.Count <= 0)
        {
            return;
        }

        var buffCfg = cfgs[0];
        //--根据buffid，取得buff abname
        int particleType = 0;
        string particleRes = string.Empty;
        GetBuffFxData(buffCfg, out particleType, out particleRes);
        if(!string.IsNullOrEmpty(particleRes))
        {
            AddUniqueString(resList, particleRes);
        }

        //--是否多个特效组成，递归加载全部
        var strEffectParam = buffCfg["effectParam"];
        if(string.IsNullOrEmpty(strEffectParam))
        {
            return;
        }

        var effectParam = strEffectParam.Split('#');
        if (effectParam == null || effectParam.Length <= 0)
        {
            return;
        }

        for(int i = 0, j = effectParam.Length; i < j; i++)
        {
            var effectID = effectParam[i];
            if(!string.IsNullOrEmpty(effectID) && int.TryParse(effectID, out var result))
            {
                var cfgsTmp = GetEffectConfig(result);
                if(cfgsTmp != null && cfgsTmp.Count > 0)
                {
                    var effectCfg = cfgsTmp[0];
                    if(effectCfg != null)
                    {
                        var strGroupID = effectCfg["nGroupID"];
                        if(!string.IsNullOrEmpty(strGroupID) && int.TryParse(strGroupID, out var GroupID))
                        {
                            if(effectGroupBuffInfo.TryGetValue(GroupID, out var buffInfo))
                            {
                                var buffCount = buffInfo.Count / 2;
                                int buffBaseIdx = 0;
                                for(int k = 0, m = buffCount; k < m; k++)
                                {
                                    buffBaseIdx = k * 2;
                                    GetBuffByEffect(effectCfg[buffInfo[buffBaseIdx]], effectCfg[buffInfo[buffBaseIdx + 1]], resList, processedBuffList);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取buff特效
    /// </summary>
    /// <param name="strBuffId"></param>
    /// <param name="strBuffLevel"></param>
    /// <param name="resList"></param>
    /// <param name="processedBuffList"></param>
    static void GetBuffByEffect(string strBuffId, string strBuffLevel, List<string>resList, List<int>processedBuffList)
    {
        if(string.IsNullOrEmpty(strBuffId) || string.IsNullOrEmpty(strBuffLevel))
        {
            return;
        }

        GetBuffFxResource(int.Parse(strBuffId), int.Parse(strBuffLevel), resList, processedBuffList);
    }

    /// <summary>
    /// 获取配置表的值
    /// </summary>
    /// <param name="cfg"></param>
    /// <param name="field"></param>
    /// <param name="def"></param>
    /// <returns></returns>
    static object GetCfgField(RecordData cfg, string field, object def)
    {
        if(cfg != null)
        {
            var cgv = cfg[field];
            if(!string.IsNullOrEmpty(cgv))
            {
                return cgv;
            }
        }

        return def;
    }

    /// <summary>
    /// 检测站立特效字段信息
    /// </summary>
    static void CheckStandEffectField()
    {
        standEffectPathFI = GetField(typeof(CardConfig), "standEffectPath");
    }

    /// <summary>
    /// 获取字段信息
    /// </summary>
    /// <param name="type"></param>
    /// <param name="fieldName"></param>
    /// <returns></returns>
    static FieldInfo GetField(Type type, string fieldName)
    {
        if (type == null || string.IsNullOrEmpty(fieldName))
        {
            return null;
        }

        FieldInfo field = type.GetField(fieldName, BindingFlags.Instance | BindingFlags.Public);
        return field;
    }

    /// <summary>
    /// 获取字段的值
    /// </summary>
    /// <param name="field"></param>
    /// <param name="obj"></param>
    /// <returns></returns>
    static object GetFieldValue(FieldInfo field, System.Object obj)
    {
        if (field == null || obj == null)
        {
            return null;
        }

        return field.GetValue(obj);
    }
}

