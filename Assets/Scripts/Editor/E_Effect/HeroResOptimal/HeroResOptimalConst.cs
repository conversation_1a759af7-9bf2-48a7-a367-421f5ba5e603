/*=================================================================================
* 创建者:刘军
* 功能描述:英雄资源优化常量定义
* 包含功能:1.英雄资源优化常量定义
*
*=================================================================================*/
using System.Collections.Generic;
using UnityEditor;

/// <summary>
/// 英雄资源优化
/// </summary>
public partial class HeroResOptimal
{
    /// <summary>
    /// 配置文件路径
    /// </summary>
    const string CConfigPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/config.txt";
    /// <summary>
    /// 英雄资源依赖文件路径
    /// </summary>
    const string CHeroResDependPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/HeroResDepend.json";
    /// <summary>
    /// 英雄动画资源压缩文件路径
    /// </summary>
    const string CHeroAniCompressPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/HeroAniCompress.json";
    /// <summary>
    /// 英雄模型贴图资源优化信息
    /// </summary>
    const string CHeroModelTexOptimalInfoPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/HeroModelTexOptimalInfo.txt";
    /// <summary>
    /// 英雄名字文件路径
    /// </summary>
    const string CHeroNamePath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroName.txt";
    /// <summary>
    /// 英雄初始内存占用文件路径
    /// </summary>
    const string CHeroMemoryInitPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroMemoryInitInfo.csv";
    /// <summary>
    /// 英雄当前内存占用文件路径
    /// </summary>
    const string CHeroMemoryNowPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroMemoryNowInfo.csv";
    /// <summary>
    /// 英雄当前内存占用简化信息文件路径
    /// </summary>
    const string CHeroMemoryNowSimplePath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroMemoryNowSimpleInfo.csv";
    /// <summary>
    /// 英雄当前内存比对文件路径
    /// </summary>
    const string CHeroMemoryComparePath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroMemoryCompareInfo.csv";
    /// <summary>
    /// 英雄初始内存简化信息路径
    /// </summary>
    const string CHeroMemoryInitSimplePath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroMemoryInitSimple.csv";
    /// <summary>
    /// 英雄初始信息路径
    /// </summary>
    const string CHeroInitInfoPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroInitInfo.txt";
    /// <summary>
    /// 死亡武器动画初始压缩信息路径
    /// </summary>
    const string CDeadWeaponAnimCompressInfoPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroDeadWeaponCompressInfo.csv";
    /// <summary>
    /// 死亡武器动画前后对比压缩信息路径
    /// </summary>
    const string CWeaponAnimCompareCompressInfoPath = "Assets/Scripts/Editor/E_Effect/HeroResOptimal/heroDeadWeaponCompareCompressInfo.csv";
    /// <summary>
    /// 英雄ID最大值
    /// </summary>
    const string CHeroIDMaxKey = "HeroIDMax";
    /// <summary>
    /// 英雄最大等级
    /// </summary>
    const string CHeroMaxLevKey = "HeroMaxLev";
    /// <summary>
    /// 英雄最大星级
    /// </summary>
    const string CHeroMaxStarKey = "HeroMaxStar";
    /// <summary>
    /// 英雄最大皮肤数
    /// </summary>
    const string CHeroMaxSkinKey = "HeroMaxSkin";
    /// <summary>
    /// CSV目录
    /// </summary>
    const string CCSVDirKey = "csvDir";
    /// <summary>
    /// langCsv路径
    /// </summary>
    const string CLangCsvPathKey = "langCsvPath";
    /// <summary>
    /// 角色预制目录key
    /// </summary>
    const string CCharaPrefabDirKey = "charactersPrefabDir";
    /// <summary>
    /// 角色预制目录
    /// </summary>
    static string charactersPrefabDir = "/Prefabs/";
    /// <summary>
    /// 角色目录key
    /// </summary>
    const string CCharaDirKey = "charactersDir";
    /// <summary>
    /// 角色目录
    /// </summary>
    static string charactersDir = "Assets/Art/Characters/";
    /// <summary>
    /// 角色动画目录key
    /// </summary>
    const string CCharaAnimDirKey = "charactersAnimDir";
    /// <summary>
    /// 角色动画目录
    /// </summary>
    static string charactersAnimDir = "/Animations/";
    /// <summary>
    /// 角色贴图目录key
    /// </summary>
    const string CCharactersTextureDirKey = "charactersTextureDir";
    /// <summary>
    /// 角色贴图目录
    /// </summary>
    static string charactersTextureDir = "/Texture/";
    /// <summary>
    /// 动画优化策略key
    /// </summary>
    const string CAnimOptimalPolyKey = "animOptimalPloy";
    /// <summary>
    /// 动画优化策略类型
    /// </summary>
    public static EAnimOptimalPoly animOptimalPoly = EAnimOptimalPoly.Dir;
    /// <summary>
    /// 角色贴图策略key
    /// </summary>
    const string CCharaTexOptimalPolyKey = "charaTexOptimalPloy";
    /// <summary>
    /// 项目标识
    /// </summary>
    public static EProjectMark projectMarkE = EProjectMark.ChaoNengGuoNei;
    /// <summary>
    /// 统计内存类型key
    /// </summary>
    const string CCalMemoryTypeKey = "calMemoryType";
    /// <summary>
    /// 统计内存类型
    /// </summary>
    public static EditorUtilitys.FileUtilitys.ECalRuntimeMemoryType calRuntimeMemoryType = EditorUtilitys.FileUtilitys.ECalRuntimeMemoryType.Normal;
    /// <summary>
    /// 项目标识key
    /// </summary>
    const string CProjectMarkKey = "projectMark";
    /// <summary>
    /// 角色贴图策略类型
    /// </summary>
    public static ECharaTexOptimalPoly charaTexOptimalPoly = ECharaTexOptimalPoly.Dir;
    /// <summary>
    /// 角色特效特殊目录下特效资源优化信息key
    /// </summary>
    const string CEffectDirSpecialPrefix = "effectDirSpecialPrefix";
    /// <summary>
    /// 角色特效特殊目录下特效资源优化信息
    /// </summary>
    public static List<HeroEffResOptimalInfo> heroSpeEffResOptimalList = new List<HeroEffResOptimalInfo>();
    /// <summary>
    /// 动画Fbx是否需要优化key
    /// </summary>
    const string CAnimFbxNeedOptimalKey = "animFbxNeedOptimal";
    /// <summary>
    /// 动画Fbx是否需要优化
    /// </summary>
    public static bool animFbxNeedOptimal = true;
    /// <summary>
    /// 动画anim是否需要优化key
    /// </summary>
    const string CAnimFileNeedOptimalKey = "animFileNeedOptimal";
    /// <summary>
    /// 动画anim是否需要优化
    /// </summary>
    public static bool animFileNeedOptimal = true;
    /// <summary>
    /// 应用动画文件压缩信息
    /// </summary>
    const string CCharaAnimCompressInfoKey = "characterAnimCompressType";
    /// <summary>
    /// 应用动画文件使用压缩信息
    /// </summary>
    public static bool applyCharaAnimCompressInfo = false;
    /// <summary>
    /// fbx自带默认动画
    /// </summary>
    const string fbxAnimWithPreview = "__preview__";
    /// <summary>
    /// Assets
    /// </summary>
    public const string CAsset = "Assets";
    /// <summary>
    /// 动画文件扩展名
    /// </summary>
    public const string CAnimationExtension = ".anim";
    /// <summary>
    /// fbx扩展名
    /// </summary>
    public const string CFbxExtension = ".fbx,.FBX";
    /// <summary>
    /// 特效贴图名字前缀key
    /// </summary>
    const string CEffectNamePrefixKey = "effectNamePrefix";
    /// <summary>
    /// 特效贴图名字前缀
    /// </summary>
    static string CEffectNamePrefix = "f_";
    /// <summary>
    /// 特效贴图目录前缀key
    /// </summary>
    const string CEffectDirPrefixKey = "effectDirPrefix";
    /// <summary>
    /// 特效贴图目录前缀
    /// </summary>
    static List<string> CEffectDirPrefix = new List<string>() { "Assets/Art/Effects/", "Assets/Art/Effects_Source/" };
    /// <summary>
    /// 特效源目录前缀key
    /// </summary>
    const string CEffectSourceDirPrefixKey = "effectSourceDirPrefix";
    /// <summary>
    /// 特效源目录前缀
    /// </summary>
    static List<string> CEffectSourceDirPrefix = new List<string>() { "Assets/Art/Effects/Common/", "Assets/Art/Effects_Source_01/" };
    /// <summary>
    /// 英雄csv表
    /// </summary>
    const string CHeroCsvName = "Hero.csv";
    /// <summary>
    /// 英雄升级csv表
    /// </summary>
    const string CHeroUpgradeCsvName = "HeroUpgrade.csv";
    /// <summary>
    /// 英雄技能csv表
    /// </summary>
    const string CHeroSkillCsvName = "HeroSkill.csv";
    /// <summary>
    /// 技能csv表
    /// </summary>
    const string CSkillCsvName = "Skill.csv";
    /// <summary>
    /// Buff csv表
    /// </summary>
    const string CBuffCsvName = "Buff.csv";
    /// <summary>
    /// 特效csv表
    /// </summary>
    const string CParticleCsvName = "Particle.csv";
    /// <summary>
    /// 效果csv表
    /// </summary>
    const string CEffectCsvName = "Effect.csv";
    /// <summary>
    /// 皮肤csv表
    /// </summary>
    const string CSkinCsvName = "Skin.csv";
    /// <summary>
    /// 模型csv表
    /// </summary>
    const string CModulCsvName = "Modul.csv";
    /// <summary>
    /// 皮肤技能csv表
    /// </summary>
    const string CSkinSkillCsvName = "SkinSkill.csv";
    /// <summary>
    /// 动画临时目录名
    /// </summary>
    const string CAnimTmpDirName = "animTmp";

    /// <summary>
    /// 英雄星级
    /// </summary>
    public enum Hero_Star 
    {
        Green = 1,
        Blue = 2,
        BluePlus = 3,
        Purple = 4,
        PurplePlus = 5,
        Yellow = 6,
        YellowPlus = 7,
        Red = 8,
        RedPlus = 9,
        White = 10,
        White_1 = 11,
        White_2 = 12,
        White_3 = 13,
        White_4 = 14,
        White_5 = 15,
    }

    /// <summary>
    /// 特效类型
    /// </summary>
    public enum EffectTypeE
    {
        /// <summary>
        /// 通用
        /// </summary>
        General,
        /// <summary>
        /// 序列
        /// </summary>
        Seq,
        /// <summary>
        /// 噪声
        /// </summary>
        Noise,
        /// <summary>
        /// 光晕
        /// </summary>
        glow,
        /// <summary>
        /// 遮罩
        /// </summary>
        mask,
    }

    /// <summary>
    /// 动画文件优化策略
    /// </summary>
    public enum EAnimOptimalPoly
    {
        /// <summary>
        /// 按文件夹
        /// </summary>
        Dir,
        /// <summary>
        /// 自己文件优化
        /// </summary>
        Self,
    }

    /// <summary>
    /// 项目标识
    /// </summary>
    public enum EProjectMark
    {
        /// <summary>
        /// 超能国内主干
        /// </summary>
        ChaoNengGuoNei,
        /// <summary>
        /// 位面国内主干
        /// </summary>
        WeimianZhugan,
        /// <summary>
        /// 苍穹国内主干
        /// </summary>
        CangQiongZhugan,
    }

    /// <summary>
    /// 角色贴图优化策略
    /// </summary>
    public enum ECharaTexOptimalPoly
    {
        /// <summary>
        /// 按文件夹
        /// </summary>
        Dir,
        /// <summary>
        /// 指定文件
        /// </summary>
        UseTheFile,
    }

    #region 特效常量相关

    /// <summary>
    /// 特效序列图名
    /// </summary>
    const string effectXuLie_ = "xulie_";
    /// <summary>
    /// 特效噪声图名
    /// </summary>
    const string effectNoise = "noise_";
    /// <summary>
    /// 特效光晕图名
    /// </summary>
    const string effectGlow = "glow_";
    /// <summary>
    /// 特效遮罩图名
    /// </summary>
    const string effectMask = "mask_";
    /// <summary>
    /// 动画操作过滤动画名(白名单)
    /// </summary>
    static List<string> oPFilterAniNames = new List<string>() { "show", "stand" };

    /// <summary>
    /// 英雄特效资源优化信息
    /// </summary>
    public class HeroEffResOptimalInfo
    {
        /// <summary>
        /// 特效目录
        /// </summary>
        public string effectDir;
        /// <summary>
        /// 特效items
        /// </summary>
        public List<HeroEffResOptimalItem> effectItems = new List<HeroEffResOptimalItem>();
    }

    /// <summary>
    /// 英雄资源优化选项Item
    /// </summary>
    public class HeroEffResOptimalItem
    {
        /// <summary>
        /// 名字前缀
        /// </summary>
        public string effectNamePrefix;
        /// <summary>
        /// 大小
        /// </summary>
        public int effectTexSize;
    }

    #endregion 特效常量相关

    /// <summary>
    /// 英雄资源优化选项
    /// </summary>
    public struct HeroResOptimalOption
    {
        /// <summary>
        /// 动画压缩格式
        /// </summary>
        public ModelImporterAnimationCompression animCompression;
        /// <summary>
        /// 动画压缩极限值(animationRotationError,animationPositionError,animationScaleError)
        /// </summary>
        public float animThresold;
        /// <summary>
        /// 模型贴图最大值
        /// </summary>
        public float chaTexMax;
        /// <summary>
        /// 常规特效贴图最大值
        /// </summary>
        public float genEffMax;
        /// <summary>
        /// 序列特效贴图最大值
        /// </summary>
        public float xuEffMax;
        /// <summary>
        /// 噪声特效贴图最大值
        /// </summary>
        public float noiseEffMax;
        /// <summary>
        /// 光晕特效贴图最大值
        /// </summary>
        public float guangyunEffMax;
        /// <summary>
        /// 遮罩特效贴图最大值
        /// </summary>
        public float maskEffMax;
        /// <summary>
        /// 优化角色贴图
        /// </summary>
        public bool optimalChaTex;
        /// <summary>
        /// 优化角色动画
        /// </summary>
        public bool optimalChaAnim;
        /// <summary>
        /// 优化角色技能特效贴图
        /// </summary>
        public bool optimalChaEffectTex;
        /// <summary>
        /// 优化show,stand动画设置
        /// </summary>
        public bool optimalStandShowAni;
        /// <summary>
        /// stand show动画压缩阈值
        /// </summary>
        public float ssAnimThreshold;
    }
}

