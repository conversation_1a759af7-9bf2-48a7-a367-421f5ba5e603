/*=================================================================================
* 创建者:刘军
* 功能描述:资源回溯
* 包含功能:1.导出统计详细文本文件
*          2.导出统计CSV文件
*
*=================================================================================*/
using System.IO;
using System.Collections.Generic;
using Sirenix.OdinInspector.Demos;
using System.Text;
using System;

namespace ResToolsCheck
{

    /// <summary>
    /// 资源回溯
    /// </summary>
    public partial class ResBackTracker
    {
        /// <summary>
        /// 打包文件所关联的所有作者
        /// </summary>
        class FilePackedReleatedAuthor
        {
            /// <summary>
            /// 修改人
            /// </summary>
            public string modfiyauthor;
            /// <summary>
            /// 打包文件详情
            /// </summary>
            public List<FilePackedDetail> fpdList = new List<FilePackedDetail>();
            /// <summary>
            /// 修改文件大小统计
            /// </summary>
            public long modifyFileLengthTotal;
        }

        /// <summary>
        /// 结果导出简化
        /// </summary>
        public class ResultExportSimplyClear
        {
            /// <summary>
            /// 统计描述
            /// </summary>
            public string statisticssDesc;
            /// <summary>
            /// 新增列表
            /// </summary>
            public Dictionary<string, string> listAdd = new Dictionary<string, string>();
            /// <summary>
            /// 修改列表
            /// </summary>
            public Dictionary<string, string> listModify = new Dictionary<string, string>();
            /// <summary>
            /// 删除列表
            /// </summary>
            public Dictionary<string, string> listDel = new Dictionary<string, string>();
        }

        #region 导出信息简化可读性

        /// <summary>
        /// 导出信息简化可读性
        /// </summary>
        /// <param name="fileList"></param>
        /// <param name="saveFilePath"></param>
        static void ExportSimplyClear(List<FilePackedDetail> fileList, string saveFilePath)
        {
            if (fileList != null)
            {
                nDependFilesCount = 0;

                fileList.Sort((l, r) => { return (int)l.fileDiffPacked.eFileCompareResult - (int)r.fileDiffPacked.eFileCompareResult; });

                ResultExportSimplyClear vo = new ResultExportSimplyClear();
                foreach (var t in fileList)
                {
                    SimplyClearDiffFileInfo(t.fileDiffPacked, vo);
                }

                StatisticsFileModifyDesc(vo);

                //重新输出
                //写入文件
                string output = Newtonsoft.Json.JsonConvert.SerializeObject(vo, Newtonsoft.Json.Formatting.Indented);

                try
                {
                    MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(output));
                    StreamReader reader = new StreamReader(stream);
                    string line;
                    StringBuilder sb = new StringBuilder();
                    while ((line = reader.ReadLine()) != null)
                    {
                        var lineArr = line.Split(',');
                        bool lastItemList = false;
                        if (lineArr != null && lineArr.Length >= 6)
                        {
                            if (!line.EndsWith(","))
                            {
                                line += ",";
                                lineArr = line.Split(',');
                                lastItemList = true;
                            }
                        }

                        if (lineArr != null && lineArr.Length >= 7)
                        {
                            sb.Append(lineArr[0]).Append(",");
                            sb.Append(lineArr[1]).Append(",");
                            sb.Append(lineArr[2]).Append(",");
                            sb.Append(lineArr[3]).Append(",");
                            sb.Append(lineArr[4] + "\"").Append(",").Append("\n");

                            var lineArr2 = lineArr[5].Split('&');
                            if (lineArr2 != null && lineArr2.Length > 0)
                            {
                                sb.Append("    ");
                                sb.Append("\"");
                                sb.Append("Dependencies(");
                                sb.Append("n");
                                sb.Append(nDependFilesCount++).Append(":").Append(lineArr2.Length);
                                sb.Append(")");
                                sb.Append("\"");
                                sb.Append(": {");

                                sb.Append("\n");

                                for (int i = 0, j = lineArr2.Length; i < j; i++)
                                {
                                    sb.Append("    ");
                                    sb.Append(lineArr2[i].Replace("#", ",").Replace("\\", ""));
                                    if (i != lineArr2.Length - 1)
                                    {
                                        sb.Append(",");
                                    }

                                    sb.Append("\n");
                                }

                                sb.Append("    }").Append(lastItemList ? "" : ",").Append("\n");
                            }
                        }
                        else
                        {
                            sb.Append(line).Append("\n");
                        }
                    }

                    File.WriteAllText(saveFilePath, sb.ToString());
                    reader.Close();
                }
                catch (Exception e)
                {
                    UnityEngine.Debug.Log($"【ResBackTracker.ExportSimplyClear,捕获到异常:{e.ToString()}】");
                }
            }
        }

        /// <summary>
        /// 差异文件简化信息
        /// </summary>
        /// <param name="fdd"></param>
        /// <param name="resc"></param>
        static void SimplyClearDiffFileInfo(FileDiffDetail fdd, ResultExportSimplyClear resc)
        {
            if (resc != null && fdd != null)
            {
                var strFilePathDesc = fdd.strFilePathDesc;
                var eFileCompareResult = fdd.eFileCompareResult;
                Dictionary<string, string> procList = null;

                if (eFileCompareResult == EFileCompareResult.Add)
                {
                    procList = resc.listAdd;
                }
                else if (eFileCompareResult == EFileCompareResult.Modify)
                {
                    procList = resc.listModify;
                }
                else if (eFileCompareResult == EFileCompareResult.Del)
                {
                    procList = resc.listDel;
                }

                if (procList == null)
                {
                    return;
                }

                var date = EditorUtilitys.SvnUtilitys.FormatSvnInfoCmdDate(fdd.strLatestFileSvnDate);
                sbForSimplyClear.Clear();
                sbForSimplyClear.Append(fdd.strLatestSvnFilePath).Append(",");
                sbForSimplyClear.Append(fdd.strLatestFileSvnAuthor).Append(",");
                var strDate = date;
                if (!string.IsNullOrEmpty(strDate))
                {
                    strDate = strDate.TrimEnd();
                }

                sbForSimplyClear.Append(strDate).Append(",");
                sbForSimplyClear.Append(fdd.strLatestFileSvnRevision).Append(",");

                string localFilePrefix = "原始文件";
                string strLatestLocalFilePath = fdd.strLatestLocalFilePath;
                if (!string.IsNullOrEmpty(strLatestLocalFilePath) && strLatestLocalFilePath.Contains(".meta"))
                {
                    localFilePrefix = "meta文件";
                }

                sbForSimplyClear.Append($" {localFilePrefix}本地最后修改时间:").Append(fdd.strLatestFileLocalDate);
                SimplyClearDependFileInfo(fdd, resc, sbForSimplyClear);

                procList[strFilePathDesc] = sbForSimplyClear.ToString();
                sbForSimplyClear.Clear();
            }
        }

        /// <summary>
        /// 依赖文件简化信息
        /// </summary>
        /// <param name="fdd"></param>
        /// <param name="resc"></param>
        /// <param name="sb"></param>
        static void SimplyClearDependFileInfo(FileDiffDetail fdd, ResultExportSimplyClear resc, StringBuilder sb)
        {
            if (fdd.fileDependDetailList.Count > 0)
            {
                sb.Append(",");

                int fileCount = fdd.fileDependDetailList.Count;
                int fileAdded = 0;
                foreach (var t in fdd.fileDependDetailList)
                {
                    fileAdded++;

                    var date = EditorUtilitys.SvnUtilitys.FormatSvnInfoCmdDate(t.strSvnDate);
                    var strDate = date;
                    if (!string.IsNullOrEmpty(strDate))
                    {
                        strDate = strDate.TrimEnd();
                    }
                    sbForSimplyClear.Append("\"----");
                    sbForSimplyClear.Append(t.strFilePath).Append("\"").Append(":");
                    sb.Append(" ");
                    sbForSimplyClear.Append("\"");
                    //sbForSimplyClear.Append(t.strFileSize).Append("#");
                    sbForSimplyClear.Append(t.strSvnAuthor).Append("#");
                    sbForSimplyClear.Append(strDate).Append("#");
                    sbForSimplyClear.Append(t.strSvnRevision).Append("#");
                    sbForSimplyClear.Append(" 本地最后修改时间:").Append(t.strFileLocalDate);

                    if (fileAdded != fileCount)
                    {
                        sbForSimplyClear.Append("\"");
                        sbForSimplyClear.Append("&");
                    }
                }
            }
        }

        /// <summary>
        /// 统计文件更新信息
        /// </summary>
        /// <param name="resc"></param>
        static void StatisticsFileModifyDesc(ResultExportSimplyClear resc)
        {
            int numAdded = resc.listAdd.Count;
            int numModify = resc.listModify.Count;
            int numDel = resc.listDel.Count;
            int numAll = numAdded + numModify + numDel;

            StringBuilder sb = new StringBuilder();
            sb.Append("fileUrl最后修改时间:" + filesUrlLastModifyShow).Append("  ");
            sb.Append("本次对比共发生改变文件数量:" + numAll).Append("  ");
            sb.Append("本次对比新增文件数量:" + numAdded).Append("  ");
            sb.Append("本次对比修改文件数量:" + numModify).Append("  ");
            sb.Append("本次对比删除文件数量:" + numDel);

            resc.statisticssDesc = sb.ToString();
            sb.Clear();
        }

        #endregion 导出信息简化可读性


        #region 导出CSV统计信息

        /// <summary>
        /// 导出CSV信息
        /// </summary>
        /// <param name="fileList"></param>
        /// <param name="saveFilePath"></param>
        static void ExportCSVStatistics(List<FilePackedDetail> fileList, string saveFilePath)
        {
            Dictionary<string, FilePackedReleatedAuthor> authorDic = new Dictionary<string, FilePackedReleatedAuthor>();

            if (fileList != null)
            {
                foreach (var t in fileList)
                {
                    var eFileCompareResult = t.fileDiffPacked.eFileCompareResult;

                    if (eFileCompareResult == EFileCompareResult.Add || eFileCompareResult == EFileCompareResult.Modify)
                    {//如果是新增和修改
                        var fileDependDetailList = t.fileDiffPacked.fileDependDetailList;
                        if (fileDependDetailList != null && fileDependDetailList.Count > 0)
                        {
                            ConstuctFilePackedReleatedAuthor(t, authorDic);
                        }
                    }
                }
            }

            if (authorDic.Count > 0)
            {
                List<FilePackedReleatedAuthor> fPRAuthorList = new List<FilePackedReleatedAuthor>();
                var enu = authorDic.GetEnumerator();
                while (enu.MoveNext())
                {
                    fPRAuthorList.Add(enu.Current.Value);
                }

                fPRAuthorList.Sort((l, r) => { return (int)(r.modifyFileLengthTotal - l.modifyFileLengthTotal); });

                //打包差异文件根据文件大小倒序排序
                foreach (var t in fPRAuthorList)
                {
                    t.fpdList.Sort((l, r) => { return (int)(r.fileDiffPacked.lFileLength - l.fileDiffPacked.lFileLength); });
                }

                ExportCSVStatisticsImp(fPRAuthorList, saveFilePath);
            }
            else
            {//无内容,导出空的CSV文件
                StreamWriter sw = new StreamWriter(new FileStream(saveFilePath, FileMode.CreateNew), Encoding.Default);
            }
        }

        /// <summary>
        /// 构造打包文件作者信息
        /// </summary>
        /// <param name="fpd"></param>
        /// <param name="authorDic"></param>
        static void ConstuctFilePackedReleatedAuthor(FilePackedDetail fpd, Dictionary<string, FilePackedReleatedAuthor> authorDic)
        {
            List<string> authorList = new List<string>();
            var fileDependDetailList = fpd.fileDiffPacked.fileDependDetailList;
            foreach (var t in fileDependDetailList)
            {
                var svnAuthor = t.strSvnAuthor;
                if (!string.IsNullOrEmpty(svnAuthor))
                {//如果作者信息不为空
                    svnAuthor = svnAuthor.Trim();

                    if (!authorList.Contains(svnAuthor))
                    {
                        authorList.Add(svnAuthor);
                    }
                }
            }

            if (authorList.Count > 0)
            {
                foreach (var t in authorList)
                {
                    if (!authorDic.TryGetValue(t, out var result))
                    {
                        result = new FilePackedReleatedAuthor();
                    }

                    result.modfiyauthor = t;
                    result.modifyFileLengthTotal += fpd.fileDiffPacked.lFileLength;
                    result.fpdList.Add(fpd);

                    authorDic[t] = result;
                }
            }
        }

        /// <summary>
        /// 导出CSV信息实现
        /// </summary>
        /// <param name="fPRAuthorList"></param>
        /// <param name="saveFilePath"></param>
        static void ExportCSVStatisticsImp(List<FilePackedReleatedAuthor> fPRAuthorList, string saveFilePath)
        {
            //csv 表头
            string csvFileHead = "svn提交人,资源大小总计,资源大小,ab资源,svn资源路径,svn最后一次提交时间,本地最后修改时间";
            List<EditorUtilitys.CSVUtilitys.CSVOutputData> csvContentList = new List<EditorUtilitys.CSVUtilitys.CSVOutputData>();
            int writeCount = 0;

            foreach (var t in fPRAuthorList)
            {
                var fpdList = t.fpdList;
                if (fpdList != null && fpdList.Count > 0)
                {
                    EditorUtilitys.CSVUtilitys.CSVOutputData vo = new EditorUtilitys.CSVUtilitys.CSVOutputData();
                    vo.datas.Add(t.modfiyauthor);
                    vo.datas.Add(HotFixFilesDiffDetails.SizeSuffix(t.modifyFileLengthTotal, 2));
                    csvContentList.Add(vo);

                    foreach (var fpd in fpdList)
                    {
                        if (writeCount > 0)
                        {
                            AddFilePackedHeadContentToCSV(csvContentList); //打包文件标题
                        }

                        writeCount++;

                        AddFilePackedContentToCSV(csvContentList, fpd);

                        //依赖文件标题
                        AddDependFileHeadContentToCSV(csvContentList);

                        //该资源的依赖资源
                        foreach (var depend in fpd.fileDiffPacked.fileDependDetailList)
                        {
                            var tPath = depend.strFilePath.ToLower();
                            if (!tPath.Contains(".dll"))
                            {//排除dll
                                AddDependFileContentToCSV(csvContentList, depend, t.modfiyauthor);
                            }
                        }
                    }
                }
            }

            EditorUtilitys.CSVUtilitys.ExportCSVDatas(csvContentList, saveFilePath, csvFileHead);
        }

        /// <summary>
        /// 添加打包差异文件内容到CSV
        /// </summary>
        /// <param name="csvContentList"></param>
        /// <param name="fpd"></param>
        static void AddFilePackedContentToCSV(List<EditorUtilitys.CSVUtilitys.CSVOutputData> csvContentList, FilePackedDetail fpd)
        {
            var date = EditorUtilitys.SvnUtilitys.FormatSvnInfoCmdDate(fpd.fileDiffPacked.strLatestFileSvnDate);
            if (!string.IsNullOrEmpty(date))
            {
                date = date.TrimEnd();
            }

            EditorUtilitys.CSVUtilitys.CSVOutputData vofpd = new EditorUtilitys.CSVUtilitys.CSVOutputData();
            vofpd.datas.Add("");
            vofpd.datas.Add("");
            vofpd.datas.Add(HotFixFilesDiffDetails.SizeSuffix(fpd.fileDiffPacked.lFileLength, 2));
            vofpd.datas.Add(fpd.fileDiffPacked.strFilePath);
            vofpd.datas.Add(fpd.fileDiffPacked.strLatestSvnFilePath);
            vofpd.datas.Add(date);
            vofpd.datas.Add(" " + fpd.fileDiffPacked.strLatestFileLocalDate);

            csvContentList.Add(vofpd);
        }

        /// <summary>
        /// 打包资源标题
        /// </summary>
        /// <param name="csvContentList"></param>
        static void AddFilePackedHeadContentToCSV(List<EditorUtilitys.CSVUtilitys.CSVOutputData> csvContentList)
        {
            //依赖文件标题
            EditorUtilitys.CSVUtilitys.CSVOutputData filePackHead = new EditorUtilitys.CSVUtilitys.CSVOutputData();
            filePackHead.datas.Add("");
            filePackHead.datas.Add("");
            filePackHead.datas.Add("");
            filePackHead.datas.Add("");
            filePackHead.datas.Add("svn资源路径");
            filePackHead.datas.Add("svn最后一次提交时间");
            filePackHead.datas.Add("本地最后修改时间");

            csvContentList.Add(filePackHead);
        }

        /// <summary>
        /// 依赖资源标题
        /// </summary>
        /// <param name="csvContentList"></param>
        static void AddDependFileHeadContentToCSV(List<EditorUtilitys.CSVUtilitys.CSVOutputData> csvContentList)
        {
            //依赖文件标题
            EditorUtilitys.CSVUtilitys.CSVOutputData denpendHead = new EditorUtilitys.CSVUtilitys.CSVOutputData();
            denpendHead.datas.Add("");
            denpendHead.datas.Add("");
            denpendHead.datas.Add("");
            denpendHead.datas.Add("");
            denpendHead.datas.Add("依赖资源路径");
            denpendHead.datas.Add("依赖资源svn最后一次提交时间");
            denpendHead.datas.Add("依赖资源本地最后修改时间");

            csvContentList.Add(denpendHead);
        }

        /// <summary>
        /// 依赖资源
        /// </summary>
        /// <param name="csvContentList"></param>
        /// <param name="depend"></param>
        /// <param name="author"></param>
        static void AddDependFileContentToCSV(List<EditorUtilitys.CSVUtilitys.CSVOutputData> csvContentList, FileDependDetail depend, string author)
        {
            var svnAuthor = depend.strSvnAuthor;
            if (!string.IsNullOrEmpty(svnAuthor))
            {//如果作者信息不为空
                svnAuthor = svnAuthor.Trim();
            }

            if (string.Compare(author, svnAuthor) != 0)
            {
                return;
            }

            var date = EditorUtilitys.SvnUtilitys.FormatSvnInfoCmdDate(depend.strSvnDate);
            if (!string.IsNullOrEmpty(date))
            {
                date = date.TrimEnd();
            }

            EditorUtilitys.CSVUtilitys.CSVOutputData voDepend = new EditorUtilitys.CSVUtilitys.CSVOutputData();
            voDepend.datas.Add("");
            voDepend.datas.Add("");
            voDepend.datas.Add("");
            voDepend.datas.Add("");
            voDepend.datas.Add(depend.strFilePath);
            voDepend.datas.Add(date);
            voDepend.datas.Add(" " + depend.strFileLocalDate);
            csvContentList.Add(voDepend);
        }

        #endregion 导出CSV统计信息
    }
}

