/*=================================================================================
* 创建者:刘军
* 功能描述:资源回溯
* 包含功能:1.检测指定时间戳对比本地打包文件目录的所有修改文件
*
*=================================================================================*/
using UnityEngine;
using System.IO;
using War.Base;
using System.Collections.Generic;
using Sirenix.OdinInspector.Demos;
using System.Text;
using UnityEditor;
using System;

namespace ResToolsCheck
{
    /// <summary>
    /// 资源回溯
    /// </summary>
    public partial class ResBackTracker
    {
        /// <summary>
        /// 打包文件详情
        /// </summary>
        class FilePackedDetail
        {
            /// <summary>
            /// 差异打包文件
            /// </summary>
            public FileDiffDetail fileDiffPacked = new FileDiffDetail();
        }

        /// <summary>
        /// 差异打包文件详情
        /// </summary>
        class FileDiffDetail
        {
            /// <summary>
            /// 差异文件路径
            /// </summary>
            public string strFilePath;
            /// <summary>
            /// 差异文件路径描述信息
            /// </summary>
            public string strFilePathDesc;
            /// <summary>
            /// 差异文件大小
            /// </summary>
            public long lFileLength;
            /// <summary>
            /// 比较出来的最新的(svn记录比较原文件和.meta文件最后提交时间)svn文件路径
            /// </summary>
            public string strLatestSvnFilePath;
            /// <summary>
            /// 比较出来的最新的(svn记录比较原文件和.meta文件最后提交时间)svn提交姓名
            /// </summary>
            public string strLatestFileSvnAuthor;
            /// <summary>
            /// 比较出来的最新的(svn记录比较原文件和.meta文件最后提交时间)svn提交日期
            /// </summary>
            public string strLatestFileSvnDate;
            /// <summary>
            /// 比较出来的最新的(svn记录比较原文件和.meta文件最后提交时间)svn提交版本
            /// </summary>
            public string strLatestFileSvnRevision;
            /// <summary>
            /// 比较出来的最新的本地文件路径
            /// </summary>
            public string strLatestLocalFilePath;
            /// <summary>
            /// 比较出来的最新的本地文件日期
            /// </summary>
            public string strLatestFileLocalDate;
            /// <summary>
            /// 文件比对结果
            /// </summary>
            public EFileCompareResult eFileCompareResult;
            /// <summary>
            /// 打包文件依赖详情
            /// </summary>
            public List<FileDependDetail> fileDependDetailList = new List<FileDependDetail>();
        }

        /// <summary>
        /// 依赖文件详情
        /// </summary>
        class FileDependDetail
        {
            /// <summary>
            /// 文件路径
            /// </summary>
            public string strFilePath;
            /// <summary>
            /// 文件大小
            /// </summary>
            public string strFileSize;
            /// <summary>
            /// svn提交姓名
            /// </summary>
            public string strSvnAuthor;
            /// <summary>
            /// svn提交日期
            /// </summary>
            public string strSvnDate;
            /// <summary>
            /// svn提交版本
            /// </summary>
            public string strSvnRevision;
            /// <summary>
            /// 文件本地修改日期
            /// </summary>
            public string strFileLocalDate;
        }

        /// <summary>
        /// 钉钉群通知token
        /// </summary>
        static string dingTalkToken = "6c915d2439ec7c4c0854d0e369be5432342604b8cd049aaf072d3fd39e5134fd";
        /// <summary>
        /// 项目名称
        /// </summary>
        private static string projectName = "位面国内";
        /// <summary>
        /// 更新文件url
        /// </summary>
        private static string updateFileUrl = "http://wm2dl-ea.q1.com/mob_control/Android/update.json";
        /// <summary>
        /// filesurl json key
        /// </summary>
        private static string filesUrlJsonKey = "files_url";
        /// <summary>
        /// 要比对的新的打包files路径
        /// </summary>
        private static string comparedNowFileDir = "E:/Roguelike_Projects/zHero_domestic_android_q1_pc108_Release/BinClient/Client/AssetBundles/Android/";
        //private static string comparedNowFileDir = "E:/yHero220726/BinClient/Client/AssetBundles/Android/";
        /// <summary>
        /// svn url
        /// </summary>
        //private static string svnRepositoryUrl = "http://172.16.0.198/res_svn7/MGame/yHero220726/BinClient/Client/";
        private static string svnRepositoryUrl = "http://172.16.0.198/res_svn7/MGame/zHero211113/BinClient/Client/";
        /// <summary>
        /// svn 安装路径
        /// </summary>
        private static string svnExePath = "C:/Program Files/TortoiseSVN/bin/svn.exe";
        /// <summary>
        /// 文件写入上限
        /// </summary>
        private static int limitWriteFileNums = -1;
        /// <summary>
        /// 要比对的新的打包files文件名
        /// </summary>
        private static string comparedNowFilesName = "files.txt";
        /// <summary>
        /// 唯一标识
        /// </summary>
        private static string uniqueIdentity = "122";
        /// <summary>
        /// 获取最后提交信息类型
        /// </summary>
        static EditorUtilitys.SvnUtilitys.GetLastCommitCmdType useSvnCmdType = EditorUtilitys.SvnUtilitys.GetLastCommitCmdType.SvnLog;
        /// <summary>
        /// 比对差异委托
        /// </summary>
        /// <param name="aaa"></param>
        /// <param name="fileCompareResult"></param>
        public delegate void OnDiff(KeyValuePair<string, string[]> aaa, EFileCompareResult fileCompareResult);
        /// <summary>
        /// 上传文件日志目录
        /// </summary>
        static string upFileLogDir = "E:/Roguelike_Projects/HServer/ResBackTracker/";
        /// <summary>
        /// 上传文件日志目录url(钉钉链接用)
        /// </summary>
        static string upFileLogDirUrl = "http://172.18.0.138:8001/ResBackTracker/";
        /// <summary>
        /// filesUrl展示
        /// </summary>
        static string filesUrlShow = "";
        /// <summary>
        /// filesUrl最后修改时间
        /// </summary>
        static string filesUrlLastModifyShow = "";
        /// <summary>
        /// StringBuilder
        /// </summary>
        static StringBuilder sbForSimplyClear = new StringBuilder();
        /// <summary>
        /// 依赖文件计数
        /// </summary>
        static int nDependFilesCount;
        /// <summary>
        /// svn log命令耗时(毫秒)
        /// </summary>
        static float svnLogCmdCostMsTime = 0f;
        /// <summary>
        /// svn info命令耗时(毫秒)
        /// </summary>
        static float svnInfoCmdCostMsTime = 0f;

        //[MenuItem("Tool/ResSettingChecker/ResBackTrackerTest(资源回溯测试)")]
        //public static void ResBackTrackerTest()
        //{
        //    //DownloadUpdateFile(updateFileUrl);
        //}

        ///// <summary>
        ///// 使用指定FilesUrl
        ///// </summary>
        //static string HackTheFilesUrl()
        //{
        //    return "http://wm2dl-ea.q1.com/stickman_clash/Android/resource/303/files.txt";
        //}

        ///// <summary>
        ///// 使用指定时间测试
        ///// </summary>
        //static long HackTheUseTimeTest()
        //{
        //    string strDate = "2023年7月21日 12:39:13";
        //    long lastModifyTimeTicks = -1;
        //    if (DateTime.TryParse(strDate, out var result))
        //    {
        //        Debug.Log($"【ResBackTracker.DownloadUpdateFile.最后一次修改时间:{result}】");
        //        lastModifyTimeTicks = result.Ticks;
        //        filesUrlLastModifyShow = string.Format("{0:F}", result);
        //    }

        //    return lastModifyTimeTicks;
        //}

        /// <summary>
        /// 资源回溯检测入口
        /// </summary>
        public static void ResBackTrackerCheck()
        {
            projectName = GetProjectNameParam();
            updateFileUrl = GetUpdateFileUrlParam();
            filesUrlJsonKey = GetFilesUrlJsonKeyParam();
            comparedNowFileDir = GetComparedNowFileDirParam();
            svnRepositoryUrl = GetSvnRepositoryUrlParam();
            svnExePath = GetSvnExePathParam();
            limitWriteFileNums = GetLimitWriteFileNumsParam();
            upFileLogDir = GetUpFileLogDirParam();
            upFileLogDirUrl = GetUpFileLogDirUrlParam();
            useSvnCmdType = GetUseSvnCmdTypeParam();
            uniqueIdentity = GetUniqueIdentityParam();

            StringBuilder sb = new StringBuilder();
            sb.Append("projectName:").Append(projectName).Append("\n");
            sb.Append("UpdateFileUrl:").Append(updateFileUrl).Append("\n");
            sb.Append("FilesUrlJsonKey:").Append(filesUrlJsonKey).Append("\n");
            sb.Append("ComparedNowFileDir:").Append(comparedNowFileDir).Append("\n");
            sb.Append("SvnRepositoryUrl:").Append(svnRepositoryUrl).Append("\n");
            sb.Append("SvnExePath:").Append(svnExePath).Append("\n");
            sb.Append("LimitWriteFileNums:").Append(limitWriteFileNums).Append("\n");
            sb.Append("UpFileLogDir:").Append(upFileLogDir).Append("\n");
            sb.Append("UpFileLogDirUrl:").Append(upFileLogDirUrl).Append("\n");
            sb.Append("UseSvnCmdType:").Append(useSvnCmdType).Append("\n");
            sb.Append("UniqueIdentity:").Append(uniqueIdentity);

            Debug.Log($"【ResBackTracker.ResBackTrackerCheck】解析Svn参数:\n{sb.ToString()}");
            sb.Clear();

            if (string.IsNullOrEmpty(updateFileUrl) || string.IsNullOrEmpty(filesUrlJsonKey) || string.IsNullOrEmpty(comparedNowFileDir)
                || string.IsNullOrEmpty(svnRepositoryUrl) || string.IsNullOrEmpty(svnExePath) || string.IsNullOrEmpty(upFileLogDir) || string.IsNullOrEmpty(upFileLogDirUrl))
            {
                Debug.Log("【ResBackTracker.ResBackTrackerCheck(有变量的值为空)】错误返回");
                return;
            }

            ResetStatisticsSvnCmdTimeCost();
            DownloadUpdateFile(updateFileUrl);
            LogStatisticsSvnCmdTimeCost();
        }

        #region 获取jenkins参数相关

        /// <summary>
        /// 获取配置参数
        /// </summary>
        /// <returns></returns>
        static string GetParams(string strParam)
        {
            return EditorUtilitys.JenkinsUtilitys.GetParams(strParam);
        }

        /// <summary>
        /// 获取项目名称
        /// </summary>
        /// <returns></returns>
        static string GetProjectNameParam()
        {
            return GetParams(ResBackTracker.JPProjectName);
        }

        /// <summary>
        /// 给定最新的update.json文件http地址
        /// </summary>
        /// <returns></returns>
        static string GetUpdateFileUrlParam()
        {
            return GetParams(ResBackTracker.JPUpdateFileUrl);
        }

        /// <summary>
        /// 给定最新的update.json文件files键
        /// </summary>
        /// <returns></returns>
        static string GetFilesUrlJsonKeyParam()
        {
            return GetParams(ResBackTracker.JPFilesUrlJsonKey);
        }

        /// <summary>
        /// 当前比较的目录
        /// </summary>
        /// <returns></returns>
        static string GetComparedNowFileDirParam()
        {
            return GetParams(ResBackTracker.JPComparedNowFileDir);
        }

        /// <summary>
        /// 项目工程svn url
        /// </summary>
        /// <returns></returns>
        static string GetSvnRepositoryUrlParam()
        {
            return GetParams(ResBackTracker.JPSvnRepositoryUrl);
        }

        /// <summary>
        /// svn安装路径
        /// </summary>
        /// <returns></returns>
        static string GetSvnExePathParam()
        {
            var strParam = GetParams(ResBackTracker.JPSvnExePath);
            if (!string.IsNullOrEmpty(strParam))
            {
                return strParam.Replace("@", " ");
            }

            return strParam;
        }

        /// <summary>
        /// 获取文件写入上限配置参数
        /// </summary>
        /// <returns></returns>
        static int GetLimitWriteFileNumsParam()
        {
            return JenkinsEnv.Instance.GetInt(ResBackTracker.JPLimitWriteFileNums);
        }

        /// <summary>
        /// 获取上传文件目录
        /// </summary>
        /// <returns></returns>
        static string GetUpFileLogDirParam()
        {
            return GetParams(ResBackTracker.JPUpFileLogDir);
        }

        /// <summary>
        /// 获取上传文件目录url(钉钉链接用)
        /// </summary>
        /// <returns></returns>
        static string GetUpFileLogDirUrlParam()
        {
            return GetParams(ResBackTracker.JPUpFileLogDirUrl);
        }

        /// <summary>
        /// 获取唯一标识
        /// </summary>
        /// <returns></returns>
        static string GetUniqueIdentityParam()
        {
            return GetParams(ResBackTracker.JPUniqueIdentity);
        }

        /// <summary>
        /// 获取要比较的files路径
        /// </summary>
        /// <returns></returns>
        static string GetComparedNowFilesPath()
        {
            //return "E:/files.txt";
            return (comparedNowFileDir + comparedNowFilesName).Replace("\\", "/");
        }

        /// <summary>
        /// 获取获取最后提交信息枚举
        /// </summary>
        /// <returns></returns>
        static EditorUtilitys.SvnUtilitys.GetLastCommitCmdType GetUseSvnCmdTypeParam()
        {
            EditorUtilitys.SvnUtilitys.GetLastCommitCmdType retCmdType = EditorUtilitys.SvnUtilitys.GetLastCommitCmdType.SvnLog;
            var retString = GetParams(ResBackTracker.JPUseSvnCmdType);
            if (!string.IsNullOrEmpty(retString))
            {
                if (retString == ResBackTracker.JPUseSvnLogCmd)
                {
                    retCmdType = EditorUtilitys.SvnUtilitys.GetLastCommitCmdType.SvnLog;
                }
                else if (retString == ResBackTracker.JPUseSvnInfoCmd)
                {
                    retCmdType = EditorUtilitys.SvnUtilitys.GetLastCommitCmdType.SvnInfo;
                }
            }

            return retCmdType;
        }

        #endregion 获取jenkins参数相关

        /// <summary>
        /// 获取ab Manifest路径
        /// </summary>
        /// <param name="strName"></param>
        /// <returns></returns>
        static string GetABFileManifestPath(string strName)
        {
            return (comparedNowFileDir + strName + ".manifest").Replace("\\", "/");
        }

        /// <summary>
        /// 获取ab路径
        /// </summary>
        /// <param name="strName"></param>
        /// <returns></returns>
        static string GetABFilePath(string strName)
        {
            return (comparedNowFileDir + strName).Replace("\\", "/");
        }

        /// <summary>
        /// http下载更新文件
        /// </summary>
        /// <param name="updateFileUrl"></param>
        /// <returns></returns>
        static void DownloadUpdateFile(string updateFileUrl)
        {
            Debug.Log("【ResBackTracker.DownloadUpdateFile(start)】");
            var jsonStr = HotFixFilesDiffDetails.Instance.ReadUrlText(updateFileUrl);

            if (!string.IsNullOrEmpty(jsonStr))
            {
                Debug.Log("【ResBackTracker.DownloadUpdateFile(update.json)】成功");
                LitJson.JsonData jsonData = LitJson.JsonMapper.ToObject(jsonStr);

                //最后修改时间
                long lastModifyTimeTicks = -1;
                string filesurl = string.Empty;
                if (jsonData != null && jsonData.Keys != null && jsonData.Keys.Contains(filesUrlJsonKey))
                {
                    filesurl = (string)jsonData[filesUrlJsonKey];
                }

                //filesurl = HackTheFilesUrl();

                if (!string.IsNullOrEmpty(filesurl))
                {
                    filesUrlShow = filesurl;
                    jsonStr = HotFixFilesDiffDetails.Instance.ReadUrlText(filesurl);
                    if (!string.IsNullOrEmpty(jsonStr))
                    {
                        Debug.Log("【ResBackTracker.DownloadUpdateFile(files.txt)】成功");

                        var strLastModifyTime = EditorUtilitys.FileUtilitys.ReadUrlLastModify(filesurl);
                        if (!string.IsNullOrEmpty(strLastModifyTime))
                        {
                            if (DateTime.TryParse(strLastModifyTime, out var result))
                            {
                                Debug.Log($"【ResBackTracker.DownloadUpdateFile.最后一次修改时间:{result}】");
                                lastModifyTimeTicks = result.Ticks;
                                filesUrlLastModifyShow = string.Format("{0:F}", result);
                            }

                            //lastModifyTimeTicks = HackTheUseTimeTest();
                        }

                        CompareFilesContent(jsonStr, GetComparedNowFilesPath(), lastModifyTimeTicks);
                        //CompareFilesContent(HotFixFilesDiffDetails.Instance.ReadUrlText("E:/Roguelike_Projects/HServer/ResBackTracker/files1.json"), GetComparedNowFilesPath(), lastModifyTimeTicks);
                    }
                }
                else
                {
                    Debug.Log($"【ResBackTracker.DownloadUpdateFile,json文件键{filesUrlJsonKey}】不存在");
                }
            }
            else
            {
                //Post的请求失败
                Debug.Log("【ResBackTracker.DownloadUpdateFile】失败");
            }
        }

        /// <summary>
        /// 比较文件内容
        /// </summary>
        /// <param name="strFileJson"></param>
        /// <param name="strComparedFilePath"></param>
        /// <param name="lastModifyTimeTicks"></param>
        static void CompareFilesContent(string strFileJson, string strComparedFilePath, long lastModifyTimeTicks)
        {
            if (string.IsNullOrEmpty(strFileJson))
            {//源文件的内容为空
             //Debug.Log("【ResBackTracker.CompareFilesContent(源文件的内容为空)】");
                return;
            }

            if (string.IsNullOrEmpty(strComparedFilePath))
            {//当前要比对的文件路径为空
             //Debug.Log("【ResBackTracker.CompareFilesContent(当前要比对的文件路径为空)】");
                return;
            }

            var fileContent = HotFixFilesDiffDetails.Instance.ReadUrlText(strComparedFilePath);

            if (string.IsNullOrEmpty(fileContent))
            {
                //Debug.Log("【ResBackTracker.CompareFilesContent(当前要比对的文件json内容为空)】");
                return;
            }

            var oldFile = Newtonsoft.Json.JsonConvert.DeserializeObject<hashCheck>(strFileJson);
            var newFile = Newtonsoft.Json.JsonConvert.DeserializeObject<hashCheck>(fileContent);

            List<FilePackedDetail> diffFileList = new List<FilePackedDetail>();

            int writeFileCnt = 0;
            CompareHashCheck(oldFile, newFile, (fileInfo, fileCompareResult) =>
            {
                string lastModifyTime = string.Empty;
                string strModFileTips = "";
                if (fileCompareResult == EFileCompareResult.Add)
                {//新增
                    strModFileTips = "(新增:AB最后修改时间:";
                }
                else if (fileCompareResult == EFileCompareResult.Modify)
                {//修改
                    strModFileTips = "(修改:AB最后修改时间:";
                }
                else if (fileCompareResult == EFileCompareResult.Del)
                {//删除
                    strModFileTips = "(删除";
                }

                if (limitWriteFileNums == -1 || writeFileCnt < limitWriteFileNums)
                {
                    if (CheckDiffFileModifyTimeAfterTheTicks(fileInfo.Key, fileCompareResult, lastModifyTimeTicks, ref lastModifyTime))
                    {
                        FilePackedDetail fpd = new FilePackedDetail();
                        fpd.fileDiffPacked.strFilePath = fileInfo.Key;
                        fpd.fileDiffPacked.strFilePathDesc = fileInfo.Key + strModFileTips + lastModifyTime + ")";
                        fpd.fileDiffPacked.eFileCompareResult = fileCompareResult;

                        if (fileCompareResult == EFileCompareResult.Add || fileCompareResult == EFileCompareResult.Modify)
                        {//新增或者修改的资源
                            var manifestPath = GetABFileManifestPath(fileInfo.Key);
                            var diffFileValList = ParseManifestValue(manifestPath, manifestAssetKey);
                            if (diffFileValList.Count > 0)
                            {
                                ProcessDiffSvnLogInfo(fileInfo.Key, fpd, diffFileValList, lastModifyTimeTicks);
                            }

                            var abFilePath = GetABFilePath(fileInfo.Key);
                            if (File.Exists(abFilePath))
                            {
                                FileInfo fi = new FileInfo(abFilePath);
                                fpd.fileDiffPacked.lFileLength = fi.Length;
                            }

                            //var valList = ParseManifestValue(manifestPath, manifestDependenciesKey);
                            //if (valList.Count > 0)
                            //{
                            //    ProcessDependFileSvnLogInfo(fpd, valList, lastModifyTimeTicks);
                            //}
                        }
                        else if (fileCompareResult == EFileCompareResult.Del)
                        {//已删除

                        }

                        diffFileList.Add(fpd);
                        writeFileCnt++;
                    }
                }
            });

            //Debug.Log($"【ResBackTracker.CompareFilesContent,写入文件上限:{limitWriteFileNums},检测到差异文件数:{writeFileCnt}】");
            OutputReports(diffFileList);
        }

        /// <summary>
        /// 获取保存文件路径
        /// </summary>
        /// <param name="upFileLogDir"></param>
        /// <param name="upFileName"></param>
        /// <returns></returns>
        static string GetSaveFilePath(string upFileLogDir, string upFileName)
        {
            string saveFileName = upFileLogDir + upFileName;
            saveFileName = saveFileName.Replace("\\", "/");

            return saveFileName;
        }

        /// <summary>
        /// 输出报告
        /// </summary>
        /// <param name="diffFileList"></param>
        static void OutputReports(List<FilePackedDetail> diffFileList)
        {
            EditorUtilitys.FileUtilitys.CheckDirectory(upFileLogDir);
            var upFileNameTmp = string.Format(upFileName, uniqueIdentity);
            var upCSVFileNameTmp = string.Format(upCSVFileName, uniqueIdentity);

            string saveNowFileName = EditorUtilitys.FileUtilitys.CombineFileNameWithCurTime(upFileNameTmp);
            var saveFileName = GetSaveFilePath(upFileLogDir, saveNowFileName);
            var saveFileNameUrl = GetSaveFilePath(upFileLogDirUrl, saveNowFileName);

            string upCSVNowFileName = EditorUtilitys.FileUtilitys.CombineFileNameWithCurTime(upCSVFileNameTmp);
            string saveCSVFileName = GetSaveFilePath(upFileLogDir, upCSVNowFileName);
            string saveCSVFileNameUrl = GetSaveFilePath(upFileLogDirUrl, upCSVNowFileName);

            //删除目录下的所有.csv文件
            EditorUtilitys.FileUtilitys.DelFilesOnDir(upFileLogDir, ".csv");
            //删除目录下的所有.json文件
            EditorUtilitys.FileUtilitys.DelFilesOnDir(upFileLogDir, ".json");

            //写入文件
            //string output = Newtonsoft.Json.JsonConvert.SerializeObject(diffFileList, Newtonsoft.Json.Formatting.Indented);
            //File.WriteAllText(saveFileName, output);
            ExportSimplyClear(diffFileList, saveFileName);
            ExportCSVStatistics(diffFileList, saveCSVFileName);

            //上传钉钉
            System.Text.StringBuilder sb = new StringBuilder();
            sb.Append(dingTalkkeyWord).Append("\n");
            sb.Append("对应项目:").Append(projectName).Append("\n");
            sb.Append("update.json:").Append(updateFileUrl).Append("\n");
            sb.Append("fileUrl:").Append(filesUrlShow).Append("\n");
            sb.Append("fileUrl最后修改时间:").Append(filesUrlLastModifyShow).Append("\n");
            sb.Append("SVN地址:").Append(svnRepositoryUrl).Append("\n");
            sb.Append("比对的目录:").Append(comparedNowFileDir).Append("\n");
            sb.Append("结果日志:").Append(saveFileNameUrl).Append("\n");
            sb.Append("结果统计CSV:").Append(saveCSVFileNameUrl).Append("\n");

            EditorUtilitys.DingDingUtilitys.SendToDingDingByTextType(dingTalkToken, "检测输出结果", sb.ToString(), false);
        }

        /// <summary>
        /// 检测差异文件在指定时间后(大于)
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="fileCompareResult"></param>
        /// <param name="lastModifyTimeTicks"></param>
        /// <param name="diffFileModifyTime"></param>
        /// <returns></returns>
        static bool CheckDiffFileModifyTimeAfterTheTicks(string filePath, EFileCompareResult fileCompareResult, long lastModifyTimeTicks, ref string diffFileModifyTime)
        {
            diffFileModifyTime = string.Empty;

            if (fileCompareResult == EFileCompareResult.Del)
            {
                return true;
            }
            else if (fileCompareResult == EFileCompareResult.Add || fileCompareResult == EFileCompareResult.Modify)
            {
                if (lastModifyTimeTicks <= -1)
                {
                    return true;
                }

                var abFilePath = GetABFilePath(filePath);
                diffFileModifyTime = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTime(abFilePath);
                if (File.Exists(abFilePath))
                {
                    FileInfo fileInfo = new FileInfo(abFilePath);
                    return fileInfo.LastWriteTime.Ticks > lastModifyTimeTicks;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取svn最后提交信息Ticks
        /// </summary>
        /// <param name="diffFilePath"></param>
        /// <returns></returns>
        static long GetSvnLastCommitTicks(string diffFilePath)
        {
            //float logCmdUseMsTime = 0f;
            //float infoCmdUseMsTime = 0f;
            var info = EditorUtilitys.SvnUtilitys.GetLastCommitInfo(useSvnCmdType, svnExePath, svnRepositoryUrl, diffFilePath/*, ref logCmdUseMsTime, ref infoCmdUseMsTime*/);
            if (info != null)
            {
                var strDate = info.date;
                if (!string.IsNullOrEmpty(strDate))
                {
                    if (DateTime.TryParse(strDate, out var result))
                    {
                        return result.Ticks;
                    }
                }
            }

            return -1;
        }

        /// <summary>
        /// 选择一个最新的svn提交信息
        /// </summary>
        /// <param name="diffFilePath"></param>
        /// <returns></returns>
        static string ChooseLatestDateOnSvnInfo(string diffFilePath)
        {
            string diffMetaFilePath = diffFilePath + ".meta";

            long diffFileTick = GetSvnLastCommitTicks(diffFilePath);
            long diffMetaFileTick = GetSvnLastCommitTicks(diffMetaFilePath);

            if (diffMetaFileTick >= diffFileTick)
            {
                return diffMetaFilePath;
            }
            else
            {
                return diffFilePath;
            }
        }

        /// <summary>
        /// 选择一个最新的本地最后修改时间
        /// </summary>
        /// <param name="diffFilePath"></param>
        /// <returns></returns>
        static string ChooseLatestDateOnLocal(string diffFilePath)
        {
            string diffMetaFilePath = diffFilePath + ".meta";

            long diffFileTick = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTimeTicks(diffFilePath);
            long diffMetaFileTick = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTimeTicks(diffMetaFilePath);

            if (diffMetaFileTick >= diffFileTick)
            {
                return diffMetaFilePath;
            }
            else
            {
                return diffFilePath;
            }
        }

        /// <summary>
        /// 处理差异文件的log信息
        /// </summary>
        /// <param name="diffFilePath"></param>
        /// <param name="fpd"></param>
        /// <param name="valList"></param>
        /// <param name="lastModifyTime"></param>
        static void ProcessDiffSvnLogInfo(string diffFilePath, FilePackedDetail fpd, List<string> valList, long lastModifyTime)
        {
            bool bFind = false;
            string findedPath = "";
            foreach (var t in valList)
            {
                var dPath = t.Replace("Assets/", "").ToLower();
                if (diffFilePath.Contains(dPath))
                {
                    findedPath = t;
                    bFind = true;
                }
            }

            if (bFind)
            {
                var findedPathFinal = ChooseLatestDateOnSvnInfo(findedPath);
                bool bFileExist = File.Exists(EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(findedPathFinal));
                if (bFileExist)
                {
                    float logCmdUseMsTime = 0f;
                    float infoCmdUseMsTime = 0f;
                    var info = EditorUtilitys.SvnUtilitys.GetLastCommitInfo(useSvnCmdType, svnExePath, svnRepositoryUrl, findedPathFinal/*, ref logCmdUseMsTime, ref infoCmdUseMsTime*/);
                    if (info != null)
                    {
                        fpd.fileDiffPacked.strLatestSvnFilePath = findedPathFinal;
                        fpd.fileDiffPacked.strLatestFileSvnAuthor = info.author;
                        fpd.fileDiffPacked.strLatestFileSvnDate = info.date;
                        fpd.fileDiffPacked.strLatestFileSvnRevision = info.revision;
                    }
                    else
                    {//svn 日志信息获取失败
                        fpd.fileDiffPacked.strLatestSvnFilePath = findedPathFinal;
                    }

                    var findedLocalPathFinal = ChooseLatestDateOnLocal(EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(findedPath));
                    fpd.fileDiffPacked.strLatestFileLocalDate = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTime(findedLocalPathFinal);
                    fpd.fileDiffPacked.strLatestLocalFilePath = findedLocalPathFinal;

                    StatisticsSvnCmdTimeCost(logCmdUseMsTime, infoCmdUseMsTime);
                }
                else
                {
                    fpd.fileDiffPacked.strLatestSvnFilePath = diffFilePath + "(文件不存在)";
                }

                //处理依赖文件
                var tList = AssetDatabase.GetDependencies(findedPath);
                if (tList != null)
                {
                    List<string> dependList = new List<string>();

                    foreach (var t in tList)
                    {
                        //if (!t.ToLower().Contains("scripts"))
                        {//排除脚本文件
                            dependList.Add(t);
                        }
                    }

                    ProcessDependFileSvnLogInfo(fpd, dependList, lastModifyTime);
                }
            }
            else
            {//这是个整包 shader或者图片等......的集合
                fpd.fileDiffPacked.strLatestSvnFilePath = diffFilePath + "(整包)";
                ProcessDiffFileOnePackInfo(fpd, valList, lastModifyTime);
            }
        }

        /// <summary>
        /// 处理差异文件整包文件信息
        /// </summary>
        /// <param name="fpd"></param>
        /// <param name="valList"></param>
        /// <param name="lastModifyTimeTicks"></param>
        static void ProcessDiffFileOnePackInfo(FilePackedDetail fpd, List<string> valList, long lastModifyTimeTicks)
        {
            foreach (var tPath in valList)
            {
                var fullPath = EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(tPath);
                if (!CheckFileLocalDateAterTicks(fullPath, lastModifyTimeTicks) && !CheckFileLocalDateAterTicks(fullPath + ".meta", lastModifyTimeTicks))
                {//如果依赖的文件不在指定时间后
                    continue;
                }

                var findedLocalPathFinal = ChooseLatestDateOnLocal(fullPath);
                if (!string.IsNullOrEmpty(findedLocalPathFinal))
                {
                    if (File.Exists(findedLocalPathFinal))
                    {
                        FileInfo fileInfo = new FileInfo(findedLocalPathFinal);
                        var fileSizeStr = HotFixFilesDiffDetails.SizeSuffix(fileInfo == null ? 0 : fileInfo.Length, 2);

                        AddFileDependDetail(findedLocalPathFinal.Replace(Application.dataPath, "Assets"), fileSizeStr, fpd);
                    }
                }
            }
        }

        /// <summary>
        /// 处理svn依赖文件日志信息
        /// </summary>
        /// <param name="fpd"></param>
        /// <param name="valList"></param>
        static void ProcessDependFileSvnLogInfo(FilePackedDetail fpd, List<string> valList, long lastModifyTimeTicks)
        {
            foreach (var t in valList)
            {
                var fullPath = EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(t);
                FileInfo fileInfo = new FileInfo(fullPath);
                bool bFind = File.Exists(fullPath);
                var fileSizeStr = HotFixFilesDiffDetails.SizeSuffix(fileInfo == null ? 0 : fileInfo.Length, 2);

                if (bFind)
                {
                    if (!CheckFileLocalDateAterTicks(fullPath, lastModifyTimeTicks) && !CheckFileLocalDateAterTicks(fullPath + ".meta", lastModifyTimeTicks))
                    {//如果依赖的文件不在指定时间后
                        continue;
                    }

                    var findedLocalPathFinal = ChooseLatestDateOnLocal(fullPath);
                    AddFileDependDetail(findedLocalPathFinal.Replace(Application.dataPath, "Assets"), findedLocalPathFinal.EndsWith(".meta") ? "" : fileSizeStr, fpd);
                }
                else
                {//这是个整包 shader或者图片等......的集合

                    FileDependDetail vo = new FileDependDetail();
                    vo.strFilePath = t + "(整包)";
                    vo.strFileSize = fileSizeStr;

                    fpd.fileDiffPacked.fileDependDetailList.Add(vo);
                }
            }
        }

        /// <summary>
        /// 检测文件本地修改时间在指定Tick后
        /// </summary>
        /// <returns></returns>
        static bool CheckFileLocalDateAterTicks(string filePath, long lastModifyTimeTicks)
        {
            if (lastModifyTimeTicks <= 0)
            {
                return true;
            }

            return EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTimeTicks(filePath) > lastModifyTimeTicks;
        }

        /// <summary>
        /// 依赖文件日志信息
        /// </summary>
        /// <param name="findedPath"></param>
        /// <param name="fileSizeStr"></param>
        /// <param name="fpd"></param>
        static void AddFileDependDetail(string findedPath, string fileSizeStr, FilePackedDetail fpd)
        {
            float logCmdUseMsTime = 0f;
            float infoCmdUseMsTime = 0f;
            var info = EditorUtilitys.SvnUtilitys.GetLastCommitInfo(useSvnCmdType, svnExePath, svnRepositoryUrl, findedPath/*, ref logCmdUseMsTime, ref infoCmdUseMsTime*/);
            if (info != null)
            {
                FileDependDetail vo = new FileDependDetail();
                vo.strFilePath = findedPath;
                vo.strFileSize = fileSizeStr;
                vo.strSvnAuthor = info.author;
                vo.strSvnDate = info.date;
                vo.strSvnRevision = info.revision;
                vo.strFileLocalDate = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTime(EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(findedPath));

                fpd.fileDiffPacked.fileDependDetailList.Add(vo);
            }
            else
            {//svn 日志信息获取失败
                FileDependDetail vo = new FileDependDetail();
                vo.strFilePath = findedPath;
                vo.strFileSize = fileSizeStr;
                vo.strFileLocalDate = EditorUtilitys.FileUtilitys.GetFileLocalLastModifyTime(EditorUtilitys.FileUtilitys.GetCurFullPathOnAssets(findedPath));

                fpd.fileDiffPacked.fileDependDetailList.Add(vo);
            }

            StatisticsSvnCmdTimeCost(logCmdUseMsTime, infoCmdUseMsTime);
        }

        /// <summary>
        /// 比对Hash检测
        /// </summary>
        /// <param name="oldHashCheck"></param>
        /// <param name="newHashCheck"></param>
        /// <param name="onDiff"></param>
        public static void CompareHashCheck(hashCheck oldHashCheck, hashCheck newHashCheck, OnDiff onDiff)
        {
            if (oldHashCheck != null && newHashCheck != null)
            {
                foreach (var newInfo in newHashCheck.list)
                {//遍历新的文件内容
                 //两个文件同时存在对应资源
                    if (oldHashCheck.list.ContainsKey(newInfo.Key))
                    {
                        var oldInfo = oldHashCheck.list[newInfo.Key];
                        if (HotFixFilesDiffDetails.IsArrAble(oldInfo) && HotFixFilesDiffDetails.IsArrAble(newInfo.Value))
                        {
                            if (oldInfo[1] != newInfo.Value[1])
                            {
                                onDiff(newInfo, EFileCompareResult.Modify);
                            }
                        }
                    }
                    else
                    {
                        onDiff(newInfo, EFileCompareResult.Add);
                    }
                }

                foreach (var oldInfo in oldHashCheck.list)
                {//遍历旧的文件内容
                    if (!newHashCheck.list.ContainsKey(oldInfo.Key))
                    {
                        onDiff(oldInfo, EFileCompareResult.Del);
                    }
                }
            }
        }

        /// <summary>
        /// 解析Manifest文件
        /// </summary>
        /// <param name="strFilePath"></param>
        /// <param name="strKey"></param>
        /// <returns></returns>
        static List<string> ParseManifestValue(string strFilePath, string strKey)
        {
            List<string> retList = new List<string>();

            if (!File.Exists(strFilePath) || string.IsNullOrEmpty(strKey))
            {
                //Debug.Log($"【ResBackTracker.ParseManifest(路径不存在){strFilePath}】");
                return retList;
            }

            StreamReader reader = new StreamReader(strFilePath);
            string line;
            string valueline = string.Empty;
            bool bFindedKey = false;
            while ((line = reader.ReadLine()) != null)
            {
                if (line.StartsWith(strKey))
                {
                    bFindedKey = true;
                    if (line.Contains(manifestNullValue))
                    {
                        break;
                    }

                    continue;
                }

                if (bFindedKey)
                {
                    if (!ParseManifestValueEnd(line, strKey, ref valueline))
                    {
                        if (!string.IsNullOrEmpty(valueline))
                        {
                            retList.Add(valueline);
                        }
                    }
                    else
                    {
                        break;
                    }
                }
            }

            reader.Close();

            return retList;
        }

        /// <summary>
        /// Manifest解析是否完成
        /// </summary>
        /// <param name="strContent"></param>
        /// <param name="strKey"></param>
        /// <param name="retString"></param>
        /// <returns></returns>
        static bool ParseManifestValueEnd(string strContent, string strKey, ref string retString)
        {
            retString = string.Empty;
            if (strKey == manifestAssetKey || strKey == manifestDependenciesKey)
            {
                if (strContent.StartsWith(manifestAssetsValueStart))
                {
                    retString = strContent.Substring(manifestAssetsValueStart.Length).Trim();

                    return false;
                }
                else
                {
                    return true;
                }
            }

            return true;
        }

        /// <summary>
        /// 统计svn命令耗时
        /// </summary>
        static void StatisticsSvnCmdTimeCost(float _svnLogCmdCostTime, float _svnInfoCmdCostTime)
        {
            svnLogCmdCostMsTime += _svnLogCmdCostTime;
            svnInfoCmdCostMsTime += _svnInfoCmdCostTime;
        }

        /// <summary>
        /// 统计svn命令耗时重置
        /// </summary>
        static void ResetStatisticsSvnCmdTimeCost()
        {
            svnLogCmdCostMsTime = 0f;
            svnInfoCmdCostMsTime = 0f;
        }

        /// <summary>
        /// 打印统计svn命令耗时
        /// </summary>
        static void LogStatisticsSvnCmdTimeCost()
        {
            Debug.Log($"【ResBackTracker.LogStatisticsSvnCmdTimeCost(统计命令耗时)svn log耗时:{svnLogCmdCostMsTime / 1000f}s,svn info耗时:{svnInfoCmdCostMsTime / 1000f}s】");
        }
    }
}

