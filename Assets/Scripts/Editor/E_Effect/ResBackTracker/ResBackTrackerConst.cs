/*=================================================================================
* 创建者:刘军
* 功能描述:资源回溯常量定义
* 包含功能:1.资源回溯常量定义,枚举
*=================================================================================*/

namespace ResToolsCheck
{
    /// <summary>
    /// 资源回溯
    /// </summary>
    public partial class ResBackTracker
    {
        /// <summary>
        /// 文件比对结果
        /// </summary>
        public enum EFileCompareResult
        {
            /// <summary>
            /// 新增
            /// </summary>
            Add,
            /// <summary>
            /// 修改
            /// </summary>
            Modify,
            /// <summary>
            /// 删除
            /// </summary>
            Del,
        }

        /// <summary>
        /// 钉钉群通知关键字
        /// </summary>
        const string dingTalkkeyWord = "BackTrack";
        /// <summary>
        /// 空值
        /// </summary>
        const string manifestNullValue = "[]";
        /// <summary>
        /// Asset键
        /// </summary>
        const string manifestAssetKey = "Assets:";
        /// <summary>
        /// Dependencies键
        /// </summary>
        const string manifestDependenciesKey = "Dependencies:";
        /// <summary>
        /// Asset键对应的值字符串StartsWith
        /// </summary>
        const string manifestAssetsValueStart = "- ";
        /// <summary>
        /// 上传文件名字
        /// </summary>
        const string upFileName = "output({0}).json";
        /// <summary>
        /// 上传CSV文件名字
        /// </summary>
        const string upCSVFileName = "output({0}).csv";

        #region jenkins参数常量

        /// <summary>
        /// 项目名字
        /// </summary>
        const string JPProjectName = "ProjectName";
        /// <summary>
        /// 给定最新的update.json文件http地址
        /// </summary>
        const string JPUpdateFileUrl = "UpdateFileUrl";
        /// <summary>
        /// 给定最新的update.json文件files键
        /// </summary>
        const string JPFilesUrlJsonKey = "FilesUrlJsonKey";
        /// <summary>
        /// 当前比较的目录
        /// </summary>
        const string JPComparedNowFileDir = "ComparedNowFileDir";
        /// <summary>
        /// 项目工程svn url
        /// </summary>
        const string JPSvnRepositoryUrl = "SvnRepositoryUrl";
        /// <summary>
        /// svn安装路径
        /// </summary>
        const string JPSvnExePath = "SvnExePath";
        /// <summary>
        /// 获取文件写入上限配置参数
        /// </summary>
        const string JPLimitWriteFileNums = "LimitWriteFileNums";
        /// <summary>
        /// 获取上传文件目录
        /// </summary>
        const string JPUpFileLogDir = "UpFileLogDir";
        /// <summary>
        /// 获取上传文件目录url(钉钉链接用)
        /// </summary>
        const string JPUpFileLogDirUrl = "UpFileLogDirUrl";
        /// <summary>
        /// 获取获取最后提交信息枚举
        /// </summary>
        const string JPUseSvnCmdType = "UseSvnCmdType";
        /// <summary>
        /// 使用svnLog命令
        /// </summary>
        const string JPUseSvnLogCmd = "UseSvnLog";
        /// <summary>
        /// 使用svnInfo命令
        /// </summary>
        const string JPUseSvnInfoCmd = "UseSvnInfo";
        /// <summary>
        /// 唯一标识
        /// </summary>
        const string JPUniqueIdentity = "UniqueIdentity";

        #endregion jenkins参数常量
    }
}

