/*=================================================================================
* 创建者:刘军
* 功能描述:CSV工具类
* 包含功能:1.导出文件内容到CSV
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;

namespace EditorUtilitys
{
    /// <summary>
    /// CSV工具类
    /// </summary>
    public class CSVUtilitys
    {
        /// <summary>
        /// CSV导出数据
        /// </summary>
        public class CSVOutputData
        {
            /// <summary>
            /// 数据
            /// </summary>
            public List<string> datas = new List<string>();
        }

        /// <summary>
        /// 导出CSV数据
        /// </summary>
        /// <param name="datas"></param>
        /// <param name="targetPath"></param>
        /// <param name="headStr">表头(,分割好的字符串)</param>
        /// <returns></returns>
        public static bool ExportCSVDatas(List<CSVOutputData> datas, string targetPath, string headStr)
        {
            try
            {
                FileUtilitys.DeleteFile(targetPath);

                StringBuilder strColu = new StringBuilder();
                StringBuilder strValue = new StringBuilder();
                StreamWriter sw = new StreamWriter(new FileStream(targetPath, FileMode.CreateNew), Encoding.Default);
                strColu.Append(headStr); //添加表头
                sw.WriteLine(strColu);

                if (datas != null)
                {
                    foreach (var dr in datas)
                    {
                        strValue.Remove(0, strValue.Length);//移出
                        for (int i = 0; i < dr.datas.Count; i++)
                        {
                            if (dr.datas.Count - 1 > i)
                            {
                                strValue.Append(dr.datas[i] + ",");
                            }
                            else
                            {
                                strValue.Append(dr.datas[i]);
                            }
                        }

                        sw.WriteLine(strValue);
                    }
                }

                sw.Close();
                return true;

            }
            catch (Exception e)
            {
                Debug.LogError("CSVUtilitys.ExportCSVDetails:" + e);
                return false;
            }
        }
    }
}

