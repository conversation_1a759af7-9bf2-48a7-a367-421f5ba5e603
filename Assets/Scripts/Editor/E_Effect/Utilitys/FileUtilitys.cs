/*=================================================================================
* 创建者:刘军
* 功能描述:文件工具类
* 包含功能:1.检测目录不存在就创建
*          2.删除文件
*          3.获取url文件信息的最后一次修改时间
*          4.获取文件名
*          5.删除某个目录下某类型的文件
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using UnityEngine;
using UnityEditor;
using System.Reflection;
using System.Text;
using UnityEngine.Profiling;

namespace EditorUtilitys
{
    /// <summary>
    /// 文件工具类
    /// </summary>
    public class FileUtilitys
    {
        /// <summary>
        /// 计算运行内存类型
        /// </summary>
        public enum ECalRuntimeMemoryType
        {
            /// <summary>
            /// 普通
            /// </summary>
            Normal,
            /// <summary>
            /// Inspector面板显示的内存
            /// </summary>
            Inspector,
        }

        /// <summary>
        /// 图片检测格式
        /// </summary>
        static string[] CPicCheckTypeFilter = new string[4] { ".png", ".jpg", ".jpeg", ".tga" };
        /// <summary>
        /// 获取Texture硬盘占用大小(反射)
        /// </summary>
        static MethodInfo getTextureStorageMemorySize = null;
        /// <summary>
        /// 获取AnimationClip信息
        /// </summary>
        static MethodInfo getAnimationClipStats;
        /// <summary>
        /// 动画AnimationClip size字段
        /// </summary>
        static FieldInfo animClipSizeInfo;
        /// <summary>
        /// 动画AnimationClip size
        /// </summary>
        static object[] _animClipSizeParam = new object[1];

        /// <summary>
        /// 检测目录,如果不存在就创建
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        public static void CheckDirectory(string directoryPath)
        {
            //如果目录不存在则创建该目录
            if (!Directory.Exists(directoryPath))
            {
                try
                {
                    Directory.CreateDirectory(directoryPath);
                }
                catch(Exception e)
                {
                    UnityEngine.Debug.Log($"【FileUtilitys.CheckDirectory,捕获到异常:{e.ToString()}】");
                }
            }
        }

        /// <summary>
        /// 删除某个目录下的指定格式的所有文件
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="strFileType"></param>
        public static void DelFilesOnDir(string dir, string strFileType)
        {
            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            if (Directory.Exists(dir))
            {
                List<string> fileList = new List<string>();
                GetFilesOnDir(dir, fileList, strFileType);

                foreach (var t in fileList)
                {
                    try
                    {
                        DeleteFile(t);
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【FileUtilitys.DelFilesOnDir,捕获到异常:{e.ToString()}】");
                    }
                }
            }
        }

        /// <summary>
        /// 删除目录
        /// </summary>
        /// <param name="dir"></param>
        public static void DelDir(string dir)
        {
            DelFilesOnDir(dir, string.Empty);

            if(Directory.Exists(dir))
            {
                Directory.Delete(dir, true);
            }

            var dirMeta = dir + ".meta";
            DeleteFile(dirMeta);
        }

        /// <summary>
        /// 文件名加上当前系统时间
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="timeFormat"></param>
        /// <returns></returns>
        public static string CombineFileNameWithCurTime(string fileName, string timeFormat = "yyyy-MM-dd-HH-mm-ss")
        {
            if (string.IsNullOrEmpty(fileName))
            {
                return fileName;
            }

            fileName = fileName.Replace("\\", "/");
            if (fileName.Contains("/"))
            {
                return fileName;
            }

            var strFileName = fileName;
            var strFileExtension = "";
            var fileExtensionIndex = fileName.LastIndexOf(".");
            if (fileExtensionIndex >= 0)
            {
                strFileExtension = fileName.Substring(fileExtensionIndex);
                strFileName = fileName.Substring(0, fileExtensionIndex);
            }

            var strNowTime = DateTime.Now.ToString(timeFormat, System.Globalization.DateTimeFormatInfo.InvariantInfo);
            var retStr = string.Format("{0}-{1}{2}", strFileName, strNowTime, strFileExtension);

            return retStr;
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        public static void DeleteFile(string targetPath)
        {
            if (File.Exists(targetPath))
            {
                File.Delete(targetPath);
            }
        }

        /// <summary>
        /// 获取文件本地最后一次修改时间
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string GetFileLocalLastModifyTime(string filePath)
        {
            if (File.Exists(filePath))
            {
                FileInfo fileInfo = new FileInfo(filePath);
                return fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss");
            }

            return string.Empty;
        }

        /// <summary>
        /// 获取文件本地最后一次修改时间戳
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static long GetFileLocalLastModifyTimeTicks(string filePath)
        {
            if (File.Exists(filePath))
            {
                FileInfo fileInfo = new FileInfo(filePath);
                return fileInfo.LastWriteTime.Ticks;
            }

            return -1;
        }

        /// <summary>
        /// 获取文件最后修改时间
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string ReadUrlLastModify(string url)
        {
            HttpWebResponse response = (HttpWebResponse)System.Net.WebRequest.Create(new Uri(url)).GetResponse();
            string lastModified = response.Headers["Last-Modified"];

            return lastModified;
        }

        /// <summary>
        /// 获取某个目录下的所有文件
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="filesList"></param>
        /// <param name="strFileType">使用,号分割,使用扩展名</param>
        public static void GetFilesOnDir(string dir, List<string> filesList, string strFileType)
        {
            if (string.IsNullOrEmpty(dir) || !Directory.Exists(dir))
            {
                return;
            }

            DirectoryInfo folder = new DirectoryInfo(dir);
            string[] fileTypes = null;
            if (!string.IsNullOrEmpty(strFileType))
            {
                fileTypes = strFileType.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
            }

            foreach (FileInfo file in folder.GetFiles("*.*", SearchOption.AllDirectories))
            {
                //处理每个文件
                if (string.IsNullOrEmpty(strFileType))
                {
                    filesList.Add(file.FullName.Replace("\\", "/"));
                }
                else
                {
                    var strExtension = file.Extension;
                    if (fileTypes != null)
                    {
                        foreach (var t in fileTypes)
                        {
                            if (strExtension.Equals(t))
                            {
                                filesList.Add(file.FullName.Replace("\\", "/"));
                                break;
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取指定目录下指定文件名的路径
        /// </summary>
        /// <param name="dir"></param>
        /// <param name="theFileName"></param>
        /// <param name="findFilePath"></param>
        /// <returns></returns>
        public static bool GetFilePathByFileName(string dir, string theFileName, ref string[] findFilePath)
        {
            findFilePath = null;
            if (string.IsNullOrEmpty(dir) || !Directory.Exists(dir) || string.IsNullOrEmpty(theFileName))
            {
                return false;
            }

            string[] filePaths = Directory.GetFiles(dir, theFileName, SearchOption.AllDirectories);
            if(filePaths != null && filePaths.Length >= 1)
            {
                Array.Sort(filePaths, (l, r) => { return l.Length.CompareTo(r.Length); });
                findFilePath = filePaths;

                return true;
            }

            return false;
        }

        /// <summary>
        /// 获取当前Assets完整路径
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        public static string GetCurFullPathOnAssets(string strAssetsPath)
        {
            var dataPath = Application.dataPath.Replace("Assets", "");
            dataPath = (dataPath + strAssetsPath).Replace("\\", "/");
            return dataPath;
        }

        /// <summary>
        /// 获取Assets路径
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        public static string GetAssetPathOnFullPath(string strFullPath)
        {
            if (string.IsNullOrEmpty(strFullPath))
            {
                return strFullPath;
            }

            var dataPath = strFullPath.Replace(Application.dataPath, "Assets");
            dataPath = dataPath.Replace("\\", "/");

            return dataPath;
        }

        /// <summary>
        /// 获取文件名
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        public static string GetFileName(string strPath)
        {
            if (string.IsNullOrEmpty(strPath))
            {
                return strPath;
            }

            strPath = strPath.Replace("\\", "/");
            var index = strPath.LastIndexOf("/");
            if (index >= 0)
            {
                return strPath.Substring(index + 1);
            }

            return strPath;
        }

        /// <summary>
        /// 获取Texture硬盘占用大小
        /// 返回bytes 除以1024就为kb
        /// </summary>
        /// <param name="texture"></param>
        /// <returns></returns>
        public static long GetTextureStorageMemorySize(Texture2D texture)
        {
            long texSize = 0;

            if (getTextureStorageMemorySize == null)
            {
                //使用反射获取UnityEditor.TextureUtil类的Type
                Type textureUtilType = typeof(TextureImporter).Assembly.GetType("UnityEditor.TextureUtil");
                //使用反射获取UnityEditor.TextureUtil类的GetStorageMemorySizeLong方法
                getTextureStorageMemorySize = textureUtilType.GetMethod("GetStorageMemorySizeLong", BindingFlags.Static | BindingFlags.Public);
            }

            if (getTextureStorageMemorySize != null)
            {
                //调用GetStorageMemorySizeLong方法获取存储内存大小
                texSize = (long)getTextureStorageMemorySize.Invoke(null, new object[] { texture });
            }

            return texSize;
        }

        /// 获取文件MD5值
        /// </summary>
        /// <param name="fileName">文件绝对路径</param>
        /// <returns>MD5值</returns>
        public static string GetMD5HashFromFile(string fileName)
        {
            try
            {
                if (string.IsNullOrEmpty(fileName) || !File.Exists(fileName))
                {
                    return string.Empty;
                }

                FileStream file = new FileStream(fileName, FileMode.Open);
                System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                byte[] retVal = md5.ComputeHash(file);
                file.Close();

                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < retVal.Length; i++)
                {
                    sb.Append(retVal[i].ToString("x2"));
                }

                return sb.ToString();
            }
            catch (Exception ex)
            {
                throw new Exception("GetMD5HashFromFile() fail,error:" + ex.Message);
            }
        }

        /// <summary>
        /// 检测并返回文件编码
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="withBom"></param>
        /// <returns></returns>
        public static Encoding DetectEncode(string fileName, ref bool withBom)
        {
            TextEncodingDetect detect = new TextEncodingDetect();
            var encoding = detect.GetEncoding(File.ReadAllBytes(fileName));
            withBom = detect.hasBom;

            return encoding;
        }

        /// <summary>
        /// 检测并返回文件编码
        /// </summary>
        /// <param name="bytes"></param>
        /// <param name="withBom"></param>
        /// <returns></returns>
        public static Encoding DetectEncode(byte[] bytes, ref bool withBom)
        {
            TextEncodingDetect detect = new TextEncodingDetect();
            var encoding = detect.GetEncoding(bytes);
            withBom = detect.hasBom;

            return encoding;
        }

        /// <summary>
        /// 是否是图片路径
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static bool IsTexturePath(string filePath)
        {
            if (!string.IsNullOrEmpty(filePath))
            {
                foreach (var t in CPicCheckTypeFilter)
                {
                    if (filePath.EndsWith(t))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 获取动画文件编辑器面板显示大小
        /// </summary>
        /// <param name="clip"></param>
        /// <returns></returns>
        public static int GetAnimationClipInspectorSize(AnimationClip clip)
        {
            if(clip == null)
            {
                return 0;
            }

            _animClipSizeParam[0] = clip;

            if (getAnimationClipStats == null)
            {
                Assembly asm = Assembly.GetAssembly(typeof(Editor));
                getAnimationClipStats = typeof(AnimationUtility).GetMethod("GetAnimationClipStats", BindingFlags.Static | BindingFlags.NonPublic);
                Type aniclipstats = asm.GetType("UnityEditor.AnimationClipStats");
                animClipSizeInfo = aniclipstats.GetField("size", BindingFlags.Public | BindingFlags.Instance);
            }

            if (getAnimationClipStats != null && animClipSizeInfo != null)
            {
                var stats = getAnimationClipStats.Invoke(null, _animClipSizeParam);
                return (int)animClipSizeInfo.GetValue(stats);
            }
           
            return 0;
        }

        /// <summary>
        /// 获取资源内存大小
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public static long GetRuntimeMemorySizeLong(UnityEngine.Object obj, ECalRuntimeMemoryType runtimeMemory)
        {
            long cMemorySize = 0;

            if (obj != null)
            {
                if(runtimeMemory == ECalRuntimeMemoryType.Normal)
                {
                    cMemorySize = Profiler.GetRuntimeMemorySizeLong(obj);
                }
                else if (runtimeMemory == ECalRuntimeMemoryType.Inspector)
                {
                    var objType = obj.GetType();
                    if (objType == typeof(UnityEngine.Texture2D))
                    {
                        cMemorySize = GetTextureStorageMemorySize(obj as Texture2D);
                    }
                    else if (objType == typeof(UnityEngine.AnimationClip))
                    {
                        cMemorySize = GetAnimationClipInspectorSize(obj as AnimationClip);
                    }
                    else
                    {
                        cMemorySize = Profiler.GetRuntimeMemorySizeLong(obj);
                    }
                }
            }

            return cMemorySize;
        }
    }
}

