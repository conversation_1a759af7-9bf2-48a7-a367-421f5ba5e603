/*=================================================================================
* 创建者:刘军
* 功能描述:jenkins工具类
* 包含功能:1.获取jenkins参数
*=================================================================================*/
using UnityEngine;

namespace EditorUtilitys
{
    /// <summary>
    /// jenkins工具类
    /// </summary>
    public class JenkinsUtilitys
    {
        /// <summary>
        /// 获取配置参数
        /// </summary>
        /// <returns></returns>
        public static string GetParams(string strParam)
        {
            string jeParam = JenkinsEnv.Instance.Get(strParam);
            if (string.IsNullOrEmpty(jeParam))
            {
                Debug.LogError($"svn参数:{strParam}获取出错!!!!!!");
                return null;
            }

            return jeParam;
        }
    }
}

