/*=================================================================================
* 创建者:刘军
* 功能描述:GameObj工具类
* 包含功能:1.获取子物体在父物体下的路径
*=================================================================================*/
using System.Collections.Generic;
using UnityEngine;

namespace EditorUtilitys
{
    /// <summary>
    /// GameObj工具类
    /// </summary>
    public class GameObjUtilitys
    {
        static List<string> parentNameList = new List<string>();
        /// <summary>
        /// 获取子物体在父物体下的路径
        /// </summary>
        /// <param name="parent"></param>
        /// <param name="child"></param>
        /// <returns></returns>
        public static string GetChildPath(GameObject parent, GameObject child)
        {
            string strChildPath = string.Empty;
            if (child == null)
            {
                return strChildPath;
            }

            if (child == parent)
            {//如果是自己
                return child.name;
            }

            parentNameList.Clear();
            parentNameList.Add(child.name);

            int findNum = 0;
            var childParent = child.transform.parent;
            while (childParent != parent && childParent != null && findNum <= 1000)
            {
                if (childParent != null)
                {
                    parentNameList.Add(childParent.name);
                    childParent = childParent.parent;
                }

                findNum++;
            }

            for (int i = parentNameList.Count - 1; i >= 0; i--)
            {
                strChildPath += parentNameList[i];
                if (i != 0)
                {
                    strChildPath += "/";
                }
            }

            parentNameList.Clear();

            return strChildPath;
        }
    }
}
