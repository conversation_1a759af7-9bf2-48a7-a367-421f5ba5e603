/*=================================================================================
* 创建者:刘军
* 功能描述:封装一些常用的svn命令
* 包含功能:1.获取svn日志信息
*          2.获取svn文件状态信息(新增,修改,不在版本控制)
*=================================================================================*/
using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Xml;
using UnityEditor;
using UnityEngine;

namespace EditorUtilitys
{
    /// <summary>
    /// svn工具类
    /// </summary>
    public class SvnUtilitys
    {
        private static string svnExeCmd = "svn";
        private static string svnExePath = "C:/Program Files/TortoiseSVN/bin/svn.exe";
        private static string svnRepositoryUrl = "http://************/res_svn7/MGame/yHero220726/BinClient/Client/";
        //private static string svnRepositoryUrl = "http://************/res_svn7/MGame/yHero221031/BinClient/Client/";
        private static string strFilePath = "Assets/Scripts/SkillEditor/BattleLayoutPreview.cs";
        //private static string strFilePath = "Assets/Scripts/GameUpdate/AssetsSync.cs.cs";

        /// <summary>
        /// 获取最后提交命令类型
        /// </summary>
        public enum GetLastCommitCmdType
        {
            SvnLog,
            SvnInfo
        }

        /// <summary>
        /// svn记录
        /// </summary>
        public class SvnLogEntry
        {
            /// <summary>
            /// 版本
            /// </summary>
            public string revision;
            /// <summary>
            /// 作者
            /// </summary>
            public string author;
            /// <summary>
            /// 日期
            /// </summary>
            public string date;
            /// <summary>
            /// 提交信息
            /// </summary>
            public string message;
        }

        /// <summary>
        /// 文件状态
        /// </summary>
        public enum EFileStatus
        {
            /// <summary>
            /// 错误提示
            /// </summary>
            Error,
            /// <summary>
            /// 不存在
            /// </summary>
            NotExist,
            /// <summary>
            /// 正常
            /// </summary>
            Normal,
            /// <summary>
            /// 修改
            /// </summary>
            Modify,
            /// <summary>
            /// 未在版本控制
            Unversioned,
        }

        //[MenuItem("Tool/ResSettingChecker/SvnUtilitysTest(SVN测试)")]
        //public static void SvnUtilitysTest()
        //{
        //    GetFileStatus(svnExePath, strFilePath);
        //    //UnityEngine.Debug.LogError(Newtonsoft.Json.JsonConvert.SerializeObject(GetLastCommitInfo(svnExePath, svnRepositoryUrl, strFilePath)));
        //}

        /// <summary>
        /// svn更新
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        public static bool DoSvnUpdate(string strPath)
        {
            bool bSucess = false;

            if (!string.IsNullOrEmpty(strPath))
            {
                strPath = strPath.Replace("\\", "/");
                if (Directory.Exists(strPath) || File.Exists(strPath))
                {
                    var strOutPut = GetSvnCmdOutput(svnExeCmd, $"update {strPath}");
                    bSucess = !string.IsNullOrEmpty(strOutPut);
                }
            }

            return bSucess;
        }

        /// <summary>
        /// 获取svn命令输出结果
        /// </summary>
        /// <param name="_svnExePath"></param>
        /// <param name="_strCommand"></param>
        /// <returns></returns>
        public static string GetSvnCmdOutput(string _svnExePath, string _strCommand)
        {
            string svnExePath = _svnExePath;
            string strOutput = string.Empty;
            //创建进程对象
            Process process = new Process();

            try
            {
                process.StartInfo.FileName = svnExePath;
                process.StartInfo.Arguments = _strCommand;
                process.StartInfo.UseShellExecute = false;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.CreateNoWindow = true;

                //启动并等待命令执行完成
                process.Start();
                process.WaitForExit(10000);

                if (process != null)
                {
                    if (process.HasExited)
                    {
                        //读取输出结果
                        strOutput = process.StandardOutput.ReadToEnd();
                    }
                    else
                    {
                        process.Close();
                        process.Kill();
                    }
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.GetSvnCmdOutput,捕获到异常:{e.ToString()}】");
            }
            finally
            {
                if (process != null)
                {
                    process.Close();
                }
            }

            return strOutput;
        }

        /// <summary>
        /// 获取日志信息
        /// </summary>
        /// <param name="strOutput"></param>
        /// <returns></returns>
        static SvnLogEntry GetSvnLogInfo(string strOutput)
        {
            SvnLogEntry svnLogEntry = new SvnLogEntry();

            try
            {
                XmlDocument xml = new XmlDocument();
                byte[] encodedString = Encoding.UTF8.GetBytes(strOutput);
                System.IO.MemoryStream ms = new System.IO.MemoryStream(encodedString);
                ms.Flush();
                ms.Position = 0;
                xml.Load(ms);

                //获取根元素
                XmlElement root = xml.DocumentElement;
                //获取日志条目节点
                XmlNode logEntryNode = root.SelectSingleNode("logentry");

                //获取日志条目的属性
                string revision = logEntryNode.Attributes["revision"].Value;

                //获取日志条目的子节点
                XmlNode authorNode = logEntryNode.SelectSingleNode("author");
                XmlNode dateNode = logEntryNode.SelectSingleNode("date");
                XmlNode messageNode = logEntryNode.SelectSingleNode("msg");

                //输出日志条目的属性和内容
                string author = authorNode?.InnerText;
                string date = dateNode?.InnerText;
                var dt = Convert.ToDateTime(date);
                date = string.Format("{0:F}", dt);
                string message = messageNode?.InnerText;

                svnLogEntry.revision = revision;
                svnLogEntry.author = author;
                svnLogEntry.date = date;
                svnLogEntry.message = message;
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.GetSvnLogInfo,捕获到异常:{e.ToString()}】");
            }

            return svnLogEntry;
        }

        /// <summary>
        /// 获取最后一次提交信息
        /// </summary>
        /// <param name="_svnExePath"></param>
        /// <param name="_svnRepositoryUrl"></param>
        /// <param name="_strPath"></param>
        /// <returns></returns>
        public static SvnLogEntry GetLastCommitInfo(GetLastCommitCmdType cmdType, string _svnExePath, string _svnRepositoryUrl, string _strPath/*, ref float logCmdUseMsTime, ref float infoCmdUseMsTime*/)
        {
            SvnLogEntry retEntry = null;

            //logCmdUseMsTime = 0f;
            //infoCmdUseMsTime = 0f;

            if (cmdType == GetLastCommitCmdType.SvnLog)
            {
                //var watchLog = Stopwatch.StartNew();
                retEntry = GetLastCommitInfoBySvnLog(_svnExePath, _svnRepositoryUrl, _strPath);
                //watchLog.Stop();
                //logCmdUseMsTime = watchLog.ElapsedMilliseconds;
            }
            else if (cmdType == GetLastCommitCmdType.SvnInfo)
            {
                //var watchInfo = Stopwatch.StartNew();
                retEntry = GetLastCommitInfoBySvnInfo(_svnExePath, _svnRepositoryUrl, _strPath);
                //watchInfo.Stop();
                //infoCmdUseMsTime = watchInfo.ElapsedMilliseconds;
            }

            return retEntry;
        }

        /// <summary>
        /// 获取最后一次提交信息
        /// </summary>
        /// <param name="_svnExePath"></param>
        /// <param name="_svnRepositoryUrl"></param>
        /// <param name="_strPath"></param>
        /// <returns></returns>
        public static SvnLogEntry GetLastCommitInfoBySvnLog(string _svnExePath, string _svnRepositoryUrl, string _strPath)
        {
            try
            {
                //UnityEngine.Debug.Log($"【SvnUtilitys.GetLastCommitInfo:{_strPath}】");
                //构建svn log命令
                string command = $"log {_svnRepositoryUrl}{_strPath} -l 1 --xml";
                //UnityEngine.Debug.Log($"【SvnUtilitys.GetLastCommitInfo:{command}】");
                var strOutput = GetSvnCmdOutput(_svnExePath, command);
                if (!string.IsNullOrEmpty(strOutput))
                {
                    return GetSvnLogInfo(strOutput);
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.GetLastCommitInfoByLog,捕获到异常:{e.ToString()}】");
            }

            return null;
        }

        /// <summary>
        /// 获取最后一次提交信息
        /// </summary>
        /// <param name="_svnExePath"></param>
        /// <param name="_svnRepositoryUrl"></param>
        /// <param name="_strPath"></param>
        /// <returns></returns>
        public static SvnLogEntry GetLastCommitInfoBySvnInfo(string _svnExePath, string _svnRepositoryUrl, string _strPath)
        {
            try
            {
                //构建svn info命令
                string command = $"info {_svnRepositoryUrl}{_strPath}";
                var strOutput = GetSvnCmdOutput(_svnExePath, command);
                if (!string.IsNullOrEmpty(strOutput))
                {
                    return ProcessSvnLastCommitInfo(strOutput);
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.GetLastCommitInfoBySvnInfo,捕获到异常:{e.ToString()}】");
            }

            return null;
        }

        /// <summary>
        /// 获取最后一次提交信息
        /// </summary>
        /// <param name="strOutput"></param>
        /// <returns></returns>
        static SvnLogEntry ProcessSvnLastCommitInfo(string strOutput)
        {
            SvnLogEntry svnLogEntry = new SvnLogEntry();

            try
            {
                MemoryStream stream = new MemoryStream(Encoding.UTF8.GetBytes(strOutput));
                StreamReader reader = new StreamReader(stream);
                string line;
                var lastChangedAuthor = "Last Changed Author:";
                var lastChangedRev = "Last Changed Rev:";
                var lastChangedDate = "Last Changed Date:";
                while ((line = reader.ReadLine()) != null)
                {
                    if (line.StartsWith(lastChangedAuthor))
                    {
                        svnLogEntry.author = line.Substring(lastChangedAuthor.Length);
                    }
                    else if (line.StartsWith(lastChangedRev))
                    {
                        svnLogEntry.revision = line.Substring(lastChangedRev.Length);
                    }
                    else if (line.StartsWith(lastChangedDate))
                    {
                        svnLogEntry.date = line.Substring(lastChangedDate.Length);
                    }
                }

                reader.Close();

            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.ProcessSvnLastCommitInfo,捕获到异常:{e.ToString()}】");
            }

            return svnLogEntry;
        }

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="_svnExePath"></param>
        /// <param name="_svnRepositoryUrl"></param>
        /// <param name="_strPath"></param>
        /// <returns></returns>
        public static EFileStatus GetFileStatus(string _svnExePath, string _strPath)
        {
            EFileStatus eFileStatus = EFileStatus.Error;

            try
            {
                if (!string.IsNullOrEmpty(_strPath))
                {
                    var retPath = _strPath.Replace("\\", "/");
                    var appDataPath = Application.dataPath.Replace("\\", "/");
                    if (!_strPath.Contains(appDataPath))
                    {
                        retPath = Application.dataPath.Replace("\\", "/").Replace("Assets", "") + _strPath;
                        retPath.Replace("\\", "/");
                    }

                    if (File.Exists(retPath))
                    {
                        //构建svn status命令
                        string command = $"status {retPath}";
                        var strOutput = GetSvnCmdOutput(_svnExePath, command);
                        eFileStatus = ProcessFileStatus(strOutput, eFileStatus);

                    }
                    else
                    {
                        eFileStatus = EFileStatus.NotExist;
                    }
                }
            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.GetFileStatus,捕获到异常:{e.ToString()}】");
            }

            return eFileStatus;
        }

        /// <summary>
        /// 格式化日期通过Svn Info命令获取的日期
        /// </summary>
        /// <param name="strDate"></param>
        /// <returns></returns>
        public static string FormatSvnInfoCmdDate(string strDate)
        {
            var index = -1;
            var date = strDate;
            if (!string.IsNullOrEmpty(date))
            {//2023-04-12 15:45:25 +0800 (����, 12 4�� 2023)
                index = strDate.IndexOf("+");
            }

            if (index >= 0)
            {
                date = date.Substring(0, index);
            }

            return date;
        }

        /// <summary>
        /// 处理文件状态
        /// </summary>
        /// <param name="strOutput"></param>
        /// <param name="eFileStatus"></param>
        /// <returns></returns>
        static EFileStatus ProcessFileStatus(string strOutput, EFileStatus eFileStatus)
        {
            try
            {
                if (string.IsNullOrEmpty(strOutput))
                {
                    return EFileStatus.Normal;
                }

                if (strOutput.StartsWith("?"))
                {
                    return EFileStatus.Unversioned;
                }
                else if (strOutput.StartsWith("M"))
                {
                    return EFileStatus.Modify;
                }

            }
            catch (Exception e)
            {
                UnityEngine.Debug.Log($"【SvnUtilitys.ProcessFileStatus,捕获到异常:{e.ToString()}】");
            }

            return eFileStatus;
        }
    }
}

