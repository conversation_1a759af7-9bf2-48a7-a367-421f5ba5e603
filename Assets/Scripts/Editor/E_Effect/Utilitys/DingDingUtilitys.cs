/*=================================================================================
* 创建者:刘军
* 功能描述:钉钉工具类
* 包含功能:1.发送钉钉消息到群使用ActionCard类型
*          2.发送钉钉消息到群使用Text类型          
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;

namespace EditorUtilitys
{
    /// <summary>
    /// 钉钉工具类
    /// </summary>
    public class DingDingUtilitys
    {
        /// <summary>
        /// 发送钉钉通知使用ActionCard类型(独立跳转,标题,文字信息加按钮)
        /// </summary>
        /// <param name="keyWord"></param>
        /// <param name="strToken"></param>
        /// <param name="text"></param>
        /// <param name="btnTitle"></param>
        /// <param name="btnUrl"></param>
        /// <param name="btnOrientation"></param>
        public static void SendToDingDingByActionCardType(string keyWord, string strToken, string text, string btnTitle, string btnUrl, string btnOrientation)
        {
            var jsonStringBuilder = new StringBuilder();
            jsonStringBuilder.Clear();
            jsonStringBuilder.Append($"{{\"msgtype\":\"actionCard\"," +
                                     $"\"actionCard\":{{\"title\":\"{keyWord}\"," +
                                     $"\"text\":\"{text}\"," +
                                     $"\"btnOrientation\":\"{btnOrientation}\"," +
                                     $"\"btns\":[{{\"title\":\"{btnTitle}\"," +
                                             $"\"actionURL\":\"{btnUrl}\"}}]}}}}");
            DingDingPostInternal(jsonStringBuilder.ToString(), strToken);
            jsonStringBuilder.Clear();
        }

        /// <summary>
        /// 钉钉post上传
        /// </summary>
        /// <param name="data"></param>
        /// <param name="strToken"></param>
        /// <returns></returns>
        private static String DingDingPostInternal(string data, string strToken)
        {
            string WebHookUrl = "https://oapi.dingtalk.com/robot/send?access_token=" + strToken;
            var httpWebRequest = HttpWebRequest.Create(WebHookUrl);
            httpWebRequest.ContentType = "application/json";
            httpWebRequest.Method = WebRequestMethods.Http.Post;
            if (data != null)
            {
                byte[] dataBytes = Encoding.UTF8.GetBytes(data);
                httpWebRequest.ContentLength = dataBytes.Length;
                using (var reqStream = httpWebRequest.GetRequestStream())
                {
                    reqStream.Write(dataBytes, 0, dataBytes.Length);
                }
            }

            using (var httpWebResponse = httpWebRequest.GetResponse())
            {
                var responseStream = httpWebResponse.GetResponseStream();
                return responseStream == null ? null : new StreamReader(responseStream, Encoding.UTF8).ReadToEnd();
            }
        }

        /// <summary>
        /// 发送钉钉通知使用文本方式
        /// </summary>
        /// <param name="strToken"></param>
        /// <param name="strTitle"></param>
        /// <param name="strContent"></param>
        /// <param name="isAtAll"></param>
        public static void SendToDingDingByTextType(string strToken, string strTitle, string strContent, bool isAtAll)
        {
            var atMsg = new Dictionary<string, System.Object>()
        {
            {"msgtype", "text"},
            {"at",new Dictionary<string,System.Object>
            {
                {"isAtAll", isAtAll},  //@所有人强制弹窗提醒
            }},
            {"text",new Dictionary<string,System.Object>
            {
                {"title", $"{strTitle}, {DateTime.Now.ToString()}"},
                {"content", $"{strContent}"}
            }}
        };

            DingDingPostInternal(Newtonsoft.Json.JsonConvert.SerializeObject(atMsg), strToken);
        }
    }
}

