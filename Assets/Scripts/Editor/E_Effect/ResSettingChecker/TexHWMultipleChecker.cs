/*=================================================================================
* 创建者:刘军
* 功能描述:图片宽高倍数检测(排除UI图集里的图)
* 包含功能:1.图片宽高倍数检测(排除UI图集里的图)
*=================================================================================*/
using Sirenix.OdinInspector.Demos;
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

/// <summary>
/// 图片宽高倍数检测(排除UI图集里的图)
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片宽高倍数检测(排除UI图集里的图)
    /// 参数说明:包含两个参数 eg:Assets/UI,4
    /// 参数1为string类型,目录
    /// 参数2为int类型,倍数
    /// </summary>
    class TexHWMultipleChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 倍数
        /// </summary>
        public int multiple = 4;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + texHWMulKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexHWMulCheck; } }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 2)
                {
                    try
                    {
                        dir = paramsArr[0];
                        multiple = int.Parse(paramsArr[1]);
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexHWMultipleChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexHWMultipleChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            var textureGuids = AssetDatabase.FindAssets("t:texture2D", new string[] { dir });
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string strReasonTips = $"不为{multiple}的倍数";

            if (textureGuids != null)
            {
                foreach (var t in textureGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                    var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
                    if (textureImporter != null && texture != null)
                    {
                        bool ignore = textureImporter.textureType == TextureImporterType.Sprite
                                     && !textureImporter.spritePackingTag.Equals("");

                        if (ignore)
                        {//忽略图集里的图
                            continue;
                        }

                        var widthMulValid = texture.width % multiple == 0;
                        var heightMulValid = texture.height % multiple == 0;
                        TextureImporterPlatformSettings androidSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
                        TextureImporterPlatformSettings iosSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
                        if (androidSettings != null)
                        {
                            if (androidSettings.maxTextureSize == maxOldTextureSize)
                            {//忽略maxsize为32的
                                continue;
                            }
                        }

                        if (iosSettings != null)
                        {
                            if (iosSettings.maxTextureSize == maxOldTextureSize)
                            {//忽略maxsize为32的
                                continue;
                            }
                        }

                        if (!widthMulValid || !heightMulValid)
                        {
                            var retTexSize = UnityEngine.Profiling.Profiler.GetRuntimeMemorySizeLong(texture);
                            var nowfileSizeStr = HotFixFilesDiffDetails.SizeSuffix(retTexSize, 2);
                            var ftd = new FileTipsDetail() { strTips = $"TexHWMultipleChecker({strReasonTips},编辑器下所占内存大小为{nowfileSizeStr})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = retTexSize;

                            checkList.Add(ftd);
                        }
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

