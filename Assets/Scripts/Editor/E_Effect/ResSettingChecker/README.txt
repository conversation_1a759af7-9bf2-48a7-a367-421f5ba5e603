资源设置说明
所有设置都是一个键加参数组合而成,参数以,分割,参数内部分割以||分割
eg:Mipmap:Enable,TRUE,Assets/Art/Characters/3d,""
Mipmap:Enable为键后面部分为参数

检测类型键
Texture:Mipmap  图片mipmapEnabled设置检测
Texture:Readable  图片Read/Write Enable设置检测
Texture:PkgSeparate 图片单独打包检测(UI大图打包检测,排除UI图集里的图)
Texture:ABNameRepeat 图片ABName冗余(排除UI图集里的图)设置检测
Texture:HWMultiple 图片宽高倍数检测(排除UI图集里的图)
Texture:HW 图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
Texture:Size 图片占用大小检测(可用作大图检测,排除UI图集里的图)
Atlas:SpriteMulABName 同一个图集里Sprite是否存在多个ABName
Atlas:SpriteNullABName Sprite的ABName为空的检测
Atlas:SpriteRepeat 图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
ReSkin:UI 检查换皮ui旧图(判断maxTextureSize==32)
Prefab:ScriptMissing 检查prefab上是否有脚本miss
Prefab:ControllerMissing 检查prefab上是否有Controller miss
Prefab:CardScriptMissing 检查prefab上是否有Card Script脚本miss
Prefab:HasSprIns 检查prefab是否存在SpriteMeshInstance组件
Audio:Setting 音效设置检测
Config:HeroSkill 英雄技能配置检测
File:Encode 文件编码格式检测
File:NamingNorm 文件命名规范检测
Texture:Type图片类型设置检测
Animation:Compression动画压缩类型设置检测
Animation:CurveFloatAccu动画曲线浮点数精度

Texture:Mipmap  图片mipmapEnabled设置检测
Texture:Mipmap 参数说明
参数说明:包含三个参数 eg:TRUE,Assets/Art/Characters/3d,""
参数1为bool类型mipmapEnabled的设置开启或关闭检测
参数2为string类型,目录
参数3为DateTime类型,指定检测开始的时间,填""(空)就不指定检测时间,否则按照C#的DateTime类型字符串填入

Texture:Readable  图片Read/Write Enable设置检测
图片Read/Write Enable设置检测
Texture:Readable 参数说明
参数说明:包含两个参数 eg:TRUE,Assets/UI
参数1为bool类型Read/Write Enable的设置开启或关闭检测
参数2为string类型,目录

Texture:PkgSeparate 图片单独打包检测(UI大图打包检测,排除UI图集里的图)
图片单独打包检测(UI大图打包检测)
Texture:PkgSeparate 参数说明
参数说明:包含两个参数 eg:Assets/UI,409600
参数1为string类型,目录
参数2为long类型,大小(单位为字节)

Texture:ABNameRepeat 图片ABName冗余(排除UI图集里的图)设置检测
图片ABName冗余(排除UI图集里的图)设置检测
Texture:ABNameRepeat 参数说明
参数说明:包含一个参数 eg:Assets/Art
参数1为string类型,目录

Texture:HWMultiple 图片宽高倍数检测(排除UI图集里的图)
Texture:HWMultiple 参数说明
参数说明:包含两个参数 eg:Assets/UI,4
参数1为string类型,目录
参数2为int类型,倍数

Texture:HW 图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
Texture:HW 参数说明
参数说明:包含五个参数 eg:Texture:HW,Assets/Art/Effects,1,"normal_",256,256
参数1为string类型,目录
参数2为检测类型,0为常规1为特效类型
参数3为检测参数,根据参数2来决定,如果参数2为特效类型,该参数表示以粒子特效命名规范检测
///粒子特效命名规范
///常规图<=256*256 配置为normal_
///序列帧尺寸<=512*512 命名带xulie_
///噪声图<=64*64  命名带noise_
///光晕图<=128*128  命名带glow_  blur没有用到
///遮罩图<=256*256 命名带mask_
参数4为int类型,宽
参数5为int类型,高

Texture:Size 图片占用大小检测(可用作大图检测,排除UI图集里的图)
Texture:Size 参数说明
参数说明:包含两个参数 eg:Assets/UI,409600
参数1为string类型,目录
参数2为long类型,指定检测大小(字节)

Atlas:SpriteMulABName 同一个图集里Sprite是否存在多个ABName
Atlas:SpriteMulABName 参数说明
参数说明:包含一个参数 eg:Assets/UI
参数1为string类型,目录

Atlas:SpriteNullABName Sprite的ABName为空的检测
Atlas:SpriteNullABName 参数说明
参数说明:包含一个参数 eg:Assets/UI
参数1为string类型,目录

Atlas:SpriteRepeat 图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
Atlas:SpriteRepeat 参数说明
参数说明:包含三个参数 eg:Assets/UI,ui.common,Assets/UI
参数1为string类型,给定目录
参数2为string类型,给定图集,如果填入为""则表示所有图集
参数3为string类型,指定目录

ReSkin:UI 检查换皮ui旧图(判断maxTextureSize==32)
ReSkin:UI 参数说明
参数说明:包含三个参数 eg:Assets/UI,Assets/UI/Prefabs,.prefab||.asset
参数1为string类型,给定旧图片资源目录
参数2为string类型,指定目录
参数3为string类型,指定检测资源类型(.prefab.asset),参数填""就指定类型为.prefab||.asset,多个用||分割

Prefab:ScriptMissing 检查prefab上是否有脚本miss
Prefab:ScriptMissing 参数说明
参数说明:包含一个参数 eg:Assets/UI
参数1为string类型,指定目录

Prefab:ControllerMissing 检查prefab上是否有Controller miss
Prefab:ControllerMissing 参数说明
参数说明:包含一个参数 eg:Assets/Art/Characters
参数1为string类型,指定目录

Prefab:CardScriptMissing 检查prefab上是否有Card Script脚本miss
Prefab:CardScriptMissing 参数说明
参数说明:包含一个参数 eg:Assets/Animations/Characters
参数1为string类型,指定目录

Prefab:HasSprIns 检查prefab是否存在SpriteMeshInstance组件
Prefab:HasSprIns 参数说明
参数说明:包含一个参数 eg:Assets/Animations/Characters
参数1为string类型,指定目录

Audio:Setting 音效设置检测
Audio:Setting 参数说明
参数说明:包含一个参数 eg:Assets/Art/Sound
参数1为string类型,指定目录
具体检测设置如下:
1、Force To Mono：		勾选，强制单声道。（减少音效文件的内存占用）
2、Normalize：    			勾选，引擎内部会选择最合适的方式来强行转换单声道。
3、Load In Background: 	勾选，在后台加载异步，这样做可以使得声音的加载不阻塞主线程。如果文件过大就会导致加载时间过长;介于我们游戏没有同步声音的需要，可以全部勾选；
4、Ambisonic：		    	不勾选，这个是环境音，一般手机项目都不勾选的。
5、Load Type：  			加载音频的方式
	Decompress On Load   		表示加载完音频文件之后，无压缩的释放到内存内，这样做的好处是播放的时候无需解压，速度快，减少CPU的开销，坏处是占用较多的内存。(webgl平台时音频改成decompres on load)
	Compress In Memory   		表示加载完音频文件之后，以压缩的方式放到内存中，这样做的好处是节省了内存，坏处是播放的时候会消耗CPU进行解压处理。
	Streaming 		  **选用此选项，播放音频的时候流式加载，好处是文件不占用内存，坏处是加载的时候对IO、CPU都会有开销。
6、Preload Audio Data   	不勾选（使用Streaming时不可以勾选）；预加载音效数据，这个是在进入场景的时候进行预加载的，会占用内存; 但是如果知道这个文件在这个场景内肯定会用到，需要提前进行预加载。
7、Compression Format   压缩格式，这是指音频文件的压缩格式。
	PCM  						  最高质量和最大文件的方式。
	Vorbis        		**选用此选项， 低质量的，压缩更小的文件，压缩率可以在Quality调整
	Quality             			建议:50（需根据实际情况设定） 值越小，压缩越厉害，文件也越小。
	ADPCM               				是介于PCM和Vorbis之间的压缩格式，官方推荐一些包含噪声且被多次播放的音效文件例如脚步声、打击声、武器碰撞声等可以选择.（对常用的短声音使用 ADPCM（如脚步声、枪声）。相比于未压缩的 PCM，这样可以减小文件大小，在播放时又可以很快解码。）	
8、Sample Rate Setting 采样率设置
	Preserve Sample Rate   			保持原来的默认采样率
	Optimize Sample Rate   		官方通过最高频率分析自动进行优化。
	Override Sample Rate   	**选用此选项，采样率44100Hz。自定义的采样率的值。（移动设备上的音效最高为 22,050 Hz。使用较低设置通常对最终质量影响很小，）

Config:HeroSkill 英雄技能配置检测
Config:HeroSkill 参数说明
参数说明:包含两个参数 eg:../../Tools/csv_script/,2000
参数1为string类型,指定目录
参数2为int类型,检测条数,每次检测最大条数最大为2000条
具体检测设置如下:
检测英雄技能配置是否正确,如:
配置表中配置的多段伤害数量与 Timeline 中是否一致，若不一致将导致死亡时血量不归0
若缺少最后一段伤害，可能不触发死亡表现
OnNext 是否在 OnHit之前，将导致多段伤害未能表现
OnHit 与 多段伤害是否一致，若 OnHit 指定 segment 重复，可能影响受击表现，血量，死亡表现等

File:Encode 文件编码格式检测
File:Encode 参数说明
参数说明:包含四个参数 eg:Assets/Lua/ads,.txt,0,utf-8
参数1为string类型,指定目录
参数2为string类型,指定后缀扩展名的文件,多个扩展名用||分割
参数3为bool类型,等于或者不等于指定编码
参数4为string类型,指定编码,如果带bom在后面加入eg:utf-8-bom

File:NamingNorm 文件命名规范检测
File:NamingNorm 参数说明
参数说明:包含五个参数 eg:Assets/Art,0,.meta||.cs,1,1,0
参数1为string类型,指定目录
参数2为string类型,等于或者不等于指定后缀扩展名的文件
参数3为string类型,指定后缀扩展名的文件,多个扩展名用||分割
参数4为bool类型,包含或者不包含字符列表(中文字符)
参数5为bool类型,检测中文字符
参数6为string类型,字符列表,多个字符用||分割,数字0代表空格

Texture:Type 图片类型设置检测
Texture:Type 参数说明
参数说明:包含三个参数 eg:Assets/Art/Characters,0,_N
参数1为string类型,指定目录
参数2为int类型,贴图类型 0法线贴图
参数3为string类型,参数字符串,视不同情况扩展
当参数2为0时,"_N(法线贴图命名)"表示检测文件名以"_N"结尾的文件

Animation:Compression动画压缩类型设置检测
Animation:Compression动画压缩类型设置检测 参数说明
参数说明:包含五个参数 eg:Assets/Art/Characters,/Animations,Stand||Show,0,3
参数1为string类型,指定目录
参数2为string类型,动画目录路径
参数3为string类型,过滤动画名列表
参数4为bool类型,是,否等于压缩类型
参数5为string类型,压缩类型 Off = 0,KeyFrameReduction = 1,KeyframeReductionAndCompression = 2,Optimal = 3,

Animation:CurveFloatAccu动画曲线浮点数精度检测
Animation:CurveFloatAccu动画曲线浮点数精度检测 参数说明
参数说明:包含五个参数 eg:Assets/Art/Characters,/Animations,Stand||Show,3,3
参数1为string类型,指定目录
参数2为string类型,动画目录路径
参数3为string类型,过滤动画名列表
参数4为int类型,逻辑操作符类型 0大于 1大于等于 2小于 3小于等于 4等于 5不等于
参数5为int类型,浮点数小数点位数