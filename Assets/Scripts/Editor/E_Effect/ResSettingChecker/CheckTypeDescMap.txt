#以"#"开头的为注释行
#映射检测对应的描述 枚举类型enum ResSetingsCheckType
TextureMipmapCheck=图片mipmapEnabled设置检测
TextureReadableCheck=图片Read/Write Enable设置检测
TexPkgSeparateCheck=图片单独打包检测(UI大图打包检测,排除UI图集里的图)
TexABNameRepeatCheck=图片ABName冗余(排除UI图集里的图)设置检测
TexHWMulCheck=图片宽高倍数检测(排除UI图集里的图)
TexHWCheck=图片宽高限制检测(排除UI图集里的图,对特效的贴图检测有说明)
TexSizeCheck=图片占用大小检测(可用作大图检测,排除UI图集里的图)
AtlasSpriteMulABNameInPkg=同一个图集里Sprite是否存在多个ABName
AtlasSpriteNullABName=Sprite的ABName为空的检测
AtlasSpriteRepeat=图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
ReSkinUI=检查换皮ui旧图(判断maxTextureSize==32)
PrefabScriptMissing=检查prefab上是否有脚本miss
PrefabControllerMissing=检查prefab上是否有Controller miss
PrefabCardScriptMissing=检查prefab上是否有Card Script脚本miss
PrefabHasSpriteMeshInstance=检查prefab是否存在SpriteMeshInstance组件
AudioSetting=音效设置检测
ConfigHeroSkill=英雄技能配置检测
FileEncoding=文件编码格式检测
FileNamingNorm=文件命名规范检测
TexType=贴图类型设置检测
AnimCompression=动画压缩类型检测
AnimCurveFloatAccu=动画曲线浮点数精度检测

