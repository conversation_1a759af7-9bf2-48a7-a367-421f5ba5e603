#以"#"开头的为注释行
#Texture:MipMap 图片Mipmap资源设置检测
Texture:Mipmap,TRUE,Assets/Art/Characters/3d,""
Texture:Mipmap,TRUE,Assets/Art/Characters/2d,""
#Texture:Readable 图片Read/Write Enable资源设置检测
Texture:Readable,TRUE,Assets/UI
#Texture:PkgSeparate 图片单独打包检测(大图打包检测)
Texture:PkgSeparate,Assets/UI,409600
#Texture:ABNameRepeat 图片ABName冗余(排除UI图集里的图)设置检测
Texture:ABNameRepeat,Assets/Art
#Texture HWMultiple 图片宽高倍数检测
Texture:HWMultiple,Assets/UI,4
#Texture HW 图片宽高限制检测
Texture:HW,Assets/Art/Effects,1,normal_,256,256
Texture:HW,Assets/Art/Effects,1,xulie_,512,512
Texture:HW,Assets/Art/Effects,1,noise_,64,64
Texture:HW,Assets/Art/Effects,1,glow_,128,128
Texture:HW,Assets/Art/Effects,1,mask_,256,256
Texture:HW,Assets/UI,0,"",256,256
#Texture:Size 图片占用大小检测(可用作大图检测,排除UI图集里的图)
Texture:Size,Assets/UI,409600
#Atlas SpriteMulABName 同一个图集里Sprite存在多个ABName
Atlas:SpriteMulABName,Assets/UI
Atlas:SpriteMulABName,Assets/CasualGame
#Atlas:SpriteNullABName Sprite的ABName为空的检测
Atlas:SpriteNullABName,Assets/Art
#Atlas SpriteRepeat 图集里的图片冗余检测(给定目录下给定图集的图片在指定目录下的冗余检测)
Atlas:SpriteRepeat,Assets/UI,"",Assets/UI
#ReSkin:UI 检查换皮ui旧图(判断maxTextureSize==32)
#ReSkin:UI,Assets/UI,Assets/UI/Prefabs,
#Prefab:ScriptMissing 检查prefab上是否有脚本miss
Prefab:ScriptMissing,Assets/UI
Prefab:ScriptMissing,Assets/Art
#Prefab:ControllerMissing 检查prefab上是否有Controller miss
Prefab:ControllerMissing,Assets/Art/Characters
#Prefab:CardScriptMissing 检查prefab上是否有Card Script脚本miss
Prefab:CardScriptMissing,Assets/Animations/Characters
#Prefab:HasSprIns 检查prefab是否存在SpriteMeshInstance组件
Prefab:HasSprIns,Assets/Animations/Characters
#Audio:Setting 音效设置检测
Audio:Setting,Assets/Art/Sound
#Config:HeroSkill 英雄技能配置
#Config:HeroSkill,../../Tools/csv_script/,2000
#File:Encode文件编码格式设置检测
File:Encode,Assets/Lua,.txt,0,utf-8
#File:NamingNorm文件命名规范检测
File:NamingNorm,Assets/Art,0,.meta||.cs,1,1,0
#Texture:Type图片类型设置检测
Texture:Type,Assets/Art,0,_N
#Animation:Compression动画压缩类型设置检测
Animation:Compression,Assets/Art/Characters,/Animations,Stand||Show,0,3
#Animation:CurveFloatAccu动画曲线浮点数精度
Animation:CurveFloatAccu,Assets/Art/Characters,/Animations,Stand||Show,3,3