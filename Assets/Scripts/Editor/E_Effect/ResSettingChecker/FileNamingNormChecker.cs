/*=================================================================================
* 创建者:刘军
* 功能描述:文件命名规范设置检测
* 包含功能:1.文件命名规范设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using System.Text.RegularExpressions;
using EditorUtilitys;

/// <summary>
/// 文件命名规范设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 文件命名规范设置检测
    /// 参数说明:包含五个参数 eg:Assets/Art,0,.meta||.cs,1,1,0
    /// 参数1为string类型,指定目录
    /// 参数2为string类型,等于或者不等于指定后缀扩展名的文件
    /// 参数3为string类型,指定后缀扩展名的文件,多个扩展名用||分割
    /// 参数4为bool类型,包含或者不包含字符列表(中文字符)
    /// 参数5为bool类型,检测中文字符
    /// 参数6为string类型,字符列表,多个字符用||分割,数字0代表空格
    /// </summary>
    class FileNamingNormChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 是否包含文件扩展名
        /// </summary>
        public bool matchFileExtension;
        /// <summary>
        /// 文件扩展名列表
        /// </summary>
        public string fileExtensionList;
        /// <summary>
        /// 是,否包含字符
        /// </summary>
        public bool matchSymbols;
        /// <summary>
        /// 检测中文字符
        /// </summary>
        public bool matchCnSymbols;
        /// <summary>
        /// 字符列表
        /// </summary>
        public string symbols;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return fileKeyString + resSettingCheckKeySplit + namingNormString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.FileNamingNorm; } }
        /// <summary>
        /// 文件扩展名
        /// </summary>
        string[] fileExtensionArr = null;
        /// <summary>
        /// 字符列表
        /// </summary>
        string[] symbolsArr = null;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 6)
                {
                    try
                    {
                        dir = paramsArr[0];
                        matchFileExtension = int.Parse(paramsArr[1]) > 0;
                        fileExtensionList = paramsArr[2];
                        matchSymbols = int.Parse(paramsArr[3]) > 0;
                        matchCnSymbols = int.Parse(paramsArr[4]) > 0;
                        symbols = paramsArr[5];
                        bParamValid = true;
                        ProcessFileExtension();
                        ProcessSymbols();
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【FileNamingNormChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"FileNamingNormChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 处理文件扩展名
        /// </summary>
        void ProcessFileExtension()
        {
            if(string.IsNullOrEmpty(fileExtensionList))
            {
                bParamValid = false;
            }
            else
            {
                fileExtensionArr = fileExtensionList.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);

                if (fileExtensionArr == null || fileExtensionArr.Length <= 0)
                {
                    bParamValid = false;
                }
            }
        }

        /// <summary>
        /// 处理字符列表
        /// </summary>
        void ProcessSymbols()
        {
            if (string.IsNullOrEmpty(symbols))
            {
                bParamValid = false;
            }
            else
            {
                var symbolsTmp = symbols.Replace("0", " ");
                symbolsArr = symbolsTmp.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);

                if (symbolsArr == null || symbolsArr.Length <= 0)
                {
                    bParamValid = false;
                }
            }
        }

        /// <summary>
        /// 文件扩展名是否满足
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        bool SatisfyFileExtension(string filePath)
        {
            foreach(var t in fileExtensionArr)
            {
                if(filePath.EndsWith(t))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 处理检测文件列表
        /// </summary>
        /// <param name="filesListSrc"></param>
        /// <param name="filesListDest"></param>
        void ProcessCheckFileList(List<string> filesListSrc, List<string> filesListDest)
        {
            if(filesListSrc.Count > 0)
            {
                foreach(var t in filesListSrc)
                {
                    var satify = SatisfyFileExtension(t);
                    if(matchFileExtension == satify)
                    {
                        filesListDest.Add(t);
                    }
                }
            }
        }

        /// <summary>
        /// 文件命名是否包含字符
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="strChars"></param>
        /// <returns></returns>
        bool FileNamingContainsSymbol(string fileName, ref string strChars)
        {
            strChars = string.Empty;
            foreach (var t in symbolsArr)
            {
                if(fileName.Contains(t))
                {
                    strChars = t;
                    return true;
                }
            }

            strChars = symbolsArr[0];

            return false;
        }

        /// <summary>
        /// 文件扩展名是否满足
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="strChars"></param>
        /// <returns></returns>
        bool IsFileNamingSatisfy(string filePath, ref string strChars)
        {
            string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(filePath);
            if(!string.IsNullOrEmpty(fileNameWithoutExtension))
            {
                var contains = FileNamingContainsSymbol(fileNameWithoutExtension, ref strChars);
                if(contains == matchSymbols)
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 文件命名是否满足中文
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="fileNamingSatisfy"></param>
        /// <returns></returns>
        bool IsFileNamingContainsChineseSatisfy(string fileName, bool fileNamingSatisfy)
        {
            bool bContain = Regex.IsMatch(fileName, @"[\u4e00-\u9fa5]");
            if(matchCnSymbols)
            {
                return bContain == matchSymbols;
            }
            else
            {
                return fileNamingSatisfy;
            }
        }

        /// <summary>
        /// 检测文件命名条件是否满足
        /// </summary>
        /// <param name="fileNamingSatisfy"></param>
        /// <param name="fileNamingContainsChineseSatisfy"></param>
        /// <returns></returns>
        bool CheckFileNamingSatisfy(bool fileNamingSatisfy, bool fileNamingContainsChineseSatisfy)
        {
            if(matchSymbols)
            {
                return fileNamingSatisfy || fileNamingContainsChineseSatisfy;
            }
            else
            {
                return fileNamingSatisfy && fileNamingContainsChineseSatisfy;
            }
        }

        /// <summary>
        /// 描述
        /// </summary>
        /// <param name="fileNamingSatisfy"></param>
        /// <param name="fileNamingContainsChineseSatisfy"></param>
        /// <param name="strChars"></param>
        /// <returns></returns>
        string GetFileNamingSatisfyDesc(bool fileNamingSatisfy, bool fileNamingContainsChineseSatisfy, string strChars)
        {
            string strTip = "文件命名规范";

            if (matchSymbols)
            {
                strTip += "包含(";
            }
            else
            {
                strTip += "不包含(";
            }

            if (fileNamingSatisfy)
            {
                strTip += strChars;
            }

            if (fileNamingContainsChineseSatisfy)
            {
                if(fileNamingSatisfy)
                {
                    strTip += ",";
                }

                strTip += "中文";
            }

            strTip += ")";
            strTip = strTip.Replace(" ", "空格");

            return strTip;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            List<string> filesListTmp = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            FileUtilitys.GetFilesOnDir(dir, filesListTmp, "");
            ProcessCheckFileList(filesListTmp, filesList);

            foreach (var t in filesList)
            {
                string strChars = string.Empty;
                var fileNamingSatisfy = IsFileNamingSatisfy(t, ref strChars);
                var fileNamingContainsChineseSatisfy = IsFileNamingContainsChineseSatisfy(t, fileNamingSatisfy);
                if (CheckFileNamingSatisfy(fileNamingSatisfy, fileNamingContainsChineseSatisfy))
                {
                    var strTip = GetFileNamingSatisfyDesc(fileNamingSatisfy, fileNamingContainsChineseSatisfy, strChars);
                    var assetPath = t.Replace(Application.dataPath, "Assets");
                    var ftd = new FileTipsDetail() { strTips = $"{strTip}", strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                    SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                    SetFileTipsDetailJsonInfo(ftd);
                    ftd.sortSeq = ftd.svnDateTicks;

                    checkList.Add(ftd);
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) =>
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

