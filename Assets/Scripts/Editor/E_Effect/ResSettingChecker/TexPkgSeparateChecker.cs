/*=================================================================================
* 创建者:刘军
* 功能描述:图片单独打包检测(UI大图打包检测,排除UI图集里的图)
* 包含功能:1.图片单独打包检测(UI大图打包检测,排除UI图集里的图)
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 图片单独打包检测(UI大图打包检测,排除UI图集里的图)
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片单独打包检测(UI大图打包检测,排除UI图集里的图)
    /// 参数说明:两个参数eg:Assets/UI,409600
    /// 参数1为string类型,目录
    /// 参数2为long类型,大小(单位为字节)
    /// </summary>
    class TexPkgSeparateChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 图片大小(字节)
        /// </summary>
        public long texSize = 400 * 1024;  //400kb
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + texPkgSeparateKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexPkgSeparateCheck; } }

        ///1.特效打包记录  一个特效一个包,在Common目录下的图片会打包

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 2)
                {
                    try
                    {
                        dir = paramsArr[0];
                        texSize = long.Parse(paramsArr[1]);
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexPkgSeparateChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexPkgSeparateChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 图硬盘所占空间是否大于指定大小
        /// </summary>
        /// <param name="tex"></param>
        /// <returns></returns>
        bool IsTextureSizeGreater(Texture2D tex)
        {
            var retTexSize = FileUtilitys.GetTextureStorageMemorySize(tex);
            return retTexSize >= texSize;
        }

        /// <summary>
        /// 是否需要检测
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool NeedCheck(string assetPath, TextureImporter textureImporter, Texture2D texture, ref string strReasonTips)
        {
            strReasonTips = string.Empty;
            var spritePackingTag = textureImporter.spritePackingTag;
            var assetBundleName = textureImporter.assetBundleName;
            var sprite = textureImporter.textureType == TextureImporterType.Sprite;

            if (!sprite)
            {//非Sprite
                return false;
            }

            if (!IsTextureSizeGreater(texture))
            {
                return false;
            }

            if (sprite && !textureImporter.spritePackingTag.Equals(""))
            {//忽略图集里的图
                return false;
            }

            if (string.IsNullOrEmpty(assetBundleName))
            {//abName为空
                strReasonTips = "大图检测,ABName为空";
                return true;
            }

            if (!string.IsNullOrEmpty(spritePackingTag) && sprite)
            {//spritePackingTag不为空
                strReasonTips = "大图检测,spritePackingTag不为空";
                return true;
            }

            return false;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, picCheckTypeFilter);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string strReasonTips = string.Empty;

            foreach (var t in filesList)
            {
                var assetPath = t.Replace(Application.dataPath, "Assets");
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                Texture2D texture = (Texture2D)AssetDatabase.LoadAssetAtPath(assetPath, typeof(Texture2D));
                if (textureImporter != null && texture != null)
                {
                    if (NeedCheck(assetPath, textureImporter, texture, ref strReasonTips))
                    {
                        var ftd = new FileTipsDetail() { strTips = $"TexPkgSeparateChecker({strReasonTips})", strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                        SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                        SetFileTipsDetailJsonInfo(ftd);
                        ftd.sortSeq = ftd.svnDateTicks;

                        checkList.Add(ftd);
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

