/*=================================================================================
* 创建者:刘军
* 功能描述:动画压缩类型设置检测
* 包含功能:1.动画压缩类型设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 动画压缩类型设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 动画压缩类型设置检测
    /// 参数说明:包含五个参数 eg:Assets/Art/Characters,/Animations,Stand||Show,0,3
    /// 参数1为string类型,指定目录
    /// 参数2为string类型,动画目录路径
    /// 参数3为string类型,过滤动画名列表
    /// 参数4为bool类型,是,否等于压缩类型
    /// 参数5为string类型,压缩类型 Off = 0,KeyFrameReduction = 1,KeyframeReductionAndCompression = 2,Optimal = 3,
    /// </summary>
    class AnimCompressionChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 动画目录路径
        /// </summary>
        public string animDirPath;
        /// <summary>
        /// 过滤动画名列表
        /// </summary>
        public string filterAniNameList;
        /// <summary>
        /// 是,否等于压缩类型
        /// </summary>
        public bool matchCompressionType;
        /// <summary>
        /// 压缩类型
        /// </summary>
        public int compressionType;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return animKeyString + resSettingCheckKeySplit + compressionKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AnimCompression; } }
        /// <summary>
        /// 动画压缩方式
        /// </summary>
        AnimCompressionType animCompressionType;
        /// <summary>
        /// 动画文件扩展名
        /// </summary>
        string fileExtensionArr = ".fbx,.FBX";
        /// <summary>
        /// 过滤动画名数组
        /// </summary>
        string[] filterAniNameLowerArr;
        /// <summary>
        /// 动画路径是否为空
        /// </summary>
        bool bAnimDirPathNull;
        /// <summary>
        /// 动画路径
        /// </summary>
        string animDirPathLower;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 5)
                {
                    try
                    {
                        dir = paramsArr[0];
                        animDirPath = paramsArr[1];
                        filterAniNameList = paramsArr[2];
                        matchCompressionType = int.Parse(paramsArr[3]) > 0;
                        compressionType = int.Parse(paramsArr[4]);
                        bool isDefined = Enum.IsDefined(typeof(AnimCompressionType), compressionType);
                        if (!isDefined)
                        {
                            bParamValid = false;
                            return;
                        }
                        else
                        {
                            animCompressionType = (AnimCompressionType)compressionType;
                        }

                        bParamValid = true;
                        ProcessAnimDirPath();
                        ProcessFilterAnimNameList();
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AnimCompressionChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"AnimCompressionChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 处理动画目录路径
        /// </summary>
        void ProcessAnimDirPath()
        {
            bAnimDirPathNull = string.IsNullOrEmpty(animDirPath);

            if (!bAnimDirPathNull)
            {
                animDirPathLower = animDirPath.ToLower();
            }
        }

        /// <summary>
        /// 处理过滤动画名列表
        /// </summary>
        void ProcessFilterAnimNameList()
        {
            if (!string.IsNullOrEmpty(filterAniNameList))
            {
                filterAniNameLowerArr = filterAniNameList.Split(new string[] { oneParamSplit }, StringSplitOptions.RemoveEmptyEntries);
                if(filterAniNameLowerArr != null)
                {
                    for(int i = 0, j = filterAniNameLowerArr.Length; i < j; i++)
                    {
                        filterAniNameLowerArr[i] = filterAniNameLowerArr[i].ToLower();
                    }
                }
            }
        }

        /// <summary>
        /// 是否是动画目录
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsAnimDir(string strPath)
        {
            if (bAnimDirPathNull)
            {
                return true;
            }

            return strPath.ToLower().Contains(animDirPathLower);
        }

        /// <summary>
        /// 是否是过滤的动画名
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsFilterAnimNameList(string strPath)
        {
            string strPathLower = Path.GetFileNameWithoutExtension(strPath).ToLower();
            if(filterAniNameLowerArr != null)
            {
                foreach(var t in filterAniNameLowerArr)
                {
                    if(strPathLower.Contains(t))
                    {
                        return true;
                    }
                }
            }
            else
            {
                return false;
            }

            return false;
        }

        /// <summary>
        /// 是否需要继续处理
        /// </summary>
        /// <param name="strPath"></param>
        /// <returns></returns>
        bool IsNeedContinueProcess(string strPath)
        {
            var bAnimDir = IsAnimDir(strPath);
            var bFilterAnimName = IsFilterAnimNameList(strPath);

            return bAnimDir && !bFilterAnimName;
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, fileExtensionArr);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string strTip = "动画压缩类型";
            if (matchCompressionType)
            {
                strTip += "为";
            }
            else
            {
                strTip += "不为";
            }

            strTip += animCompressionType.ToString();
            foreach (var t in filesList)
            {
                if(!IsNeedContinueProcess(t))
                {
                    continue;
                }

                var assetPath = t.Replace(Application.dataPath, "Assets");
                ModelImporter modelImporter = AssetImporter.GetAtPath(assetPath) as ModelImporter;
                if (modelImporter != null)
                {
                    var animationCompression = (int)modelImporter.animationCompression;
                    var satisfyEqual = false;
                    satisfyEqual = compressionType == animationCompression;

                    if (satisfyEqual == matchCompressionType)
                    {
                        var ftd = new FileTipsDetail() { strTips = strTip, strFilePath = assetPath, checkType = CheckType, strGroupJsonValue = KeyDesc };
                        SetFileTipsDetailLastCommitInfo(assetPath, ftd);
                        SetFileTipsDetailJsonInfo(ftd);
                        ftd.sortSeq = GetSortSeq(assetPath);

                        checkList.Add(ftd);
                    }
                }
            }

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

