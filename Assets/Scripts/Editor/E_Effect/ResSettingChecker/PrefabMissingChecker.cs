/*=================================================================================
* 创建者:刘军
* 功能描述:检查prefab是否丢失某些部件
* 包含功能:1.检查prefab是否丢失某些部件
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 检查prefab是否丢失某些部件
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 检查prefab是否丢失某些部件
    /// 参数说明:包含一个参数 eg:Assets/UI
    /// 参数1为string类型,目录,参数1可以不填,不填就默认指定为Assets/目录
    /// </summary>
    class PrefabMissingChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return prefabKeyString + resSettingCheckKeySplit + missingKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.PrefabMissing ; } }
        /// <summary>
        /// prefab对应脚本丢失transform路径
        /// </summary>
        protected Dictionary<string, List<string>> prefabMissDic;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 1)
                {
                    try
                    {
                        dir = paramsArr[0];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【PrefabMissingChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }
        }

        /// <summary>
        /// 添加Prefab丢失记录
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tParent"></param>
        /// <param name="tChild"></param>
        protected void AddMissingTransform(string path, GameObject tParent, GameObject tChild)
        {
            if (prefabMissDic == null)
            {
                prefabMissDic = new Dictionary<string, List<string>>();
            }

            List<string> pathList = null;
            if (!prefabMissDic.TryGetValue(path, out pathList))
            {
                pathList = new List<string>();
            }

            var childPath = GameObjUtilitys.GetChildPath(tParent, tChild);
            if (!pathList.Contains(childPath))
            {
                pathList.Add(childPath);
            }

            prefabMissDic[path] = pathList;
        }

        /// <summary>
        /// 查找丢失
        /// </summary>
        /// <param name="path"></param>
        /// <param name="tGameObj"></param>
        protected virtual void FindPrefabMissing(string path, GameObject tGameObj)
        {

        }

        /// <summary>
        /// 处理Prefab丢失
        /// </summary>
        /// <param name="checkList"></param>
        /// <param name="strReasonTips"></param>
        /// <param name="strTips"></param>
        protected virtual void ProcessPrefabMiss(List<FileTipsDetail> checkList, string strReasonTips, string strTips)
        {
            if (prefabMissDic != null && prefabMissDic.Count > 0)
            {
                foreach (var t in prefabMissDic)
                {
                    var k = t.Key;
                    var tList = t.Value;

                    int addedIndex = 0;
                    bool bShowIndex = tList.Count > 1;
                    foreach (var g in tList)
                    {
                        var reasonTips = $"{strReasonTips}{g}";
                        string path = k;
                        if (bShowIndex)
                        {
                            path = k + "(" + addedIndex + ")";
                        }

                        var ftd = new FileTipsDetail() { strTips = $"{strTips}({reasonTips})", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                        SetFileTipsDetailLastCommitInfo(path, ftd);
                        SetFileTipsDetailJsonInfo(ftd);
                        ftd.sortSeq = ftd.svnDateTicks;
                        addedIndex++;

                        checkList.Add(ftd);
                    }
                }
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            var gameObjsGuids = AssetDatabase.FindAssets("t:GameObject", new string[] { dir });

            if (gameObjsGuids != null)
            {
                foreach (var t in gameObjsGuids)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var gameObj = AssetDatabase.LoadMainAssetAtPath(path) as GameObject;
                    if (gameObj != null)
                    {
                        FindPrefabMissing(path, gameObj);
                    }
                }
            }

            ProcessPrefabMiss(checkList, "", "");

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

