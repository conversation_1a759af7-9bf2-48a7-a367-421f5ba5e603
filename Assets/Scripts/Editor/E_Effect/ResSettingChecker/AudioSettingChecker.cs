/*=================================================================================
* 创建者:刘军
* 功能描述:音效设置检测
* 包含功能:1.音效设置检测
*=================================================================================*/
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.Text;
//using UnityEngine.Timeline;
using System;

/// <summary>
/// 音效设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 音效设置检测
    /// 参数说明:包含一个参数 eg:Assets/Art/Sound
    /// 参数1为string类型,目录,参数1可以不填,不填就默认指定为Assets/目录
    /// </summary>
    class AudioSettingChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return audioKeyString + resSettingCheckKeySplit + settingKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.AudioSetting ; } }

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 1)
                {
                    try
                    {
                        dir = paramsArr[0];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【AudioSettingChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"AudioSettingChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 检测音效导入配置
        /// </summary>
        /// <param name="audioArr"></param>
        /// <param name="checkList"></param>
        void CheckAudioImportSettings(string[] audioArr, List<FileTipsDetail> checkList)
        {
            if(audioArr != null)
            {
                foreach(var t in audioArr)
                {
                    var path = AssetDatabase.GUIDToAssetPath(t);
                    var audioImporter = AssetImporter.GetAtPath(path) as AudioImporter;
                    if (audioImporter != null)
                    {
                        var forceToMono = audioImporter.forceToMono;
                        var serializedObject = new UnityEditor.SerializedObject(audioImporter);
                        var normalize = serializedObject.FindProperty("m_Normalize");
                        var loadInBackground = audioImporter.loadInBackground;
                        var ambisonic = audioImporter.ambisonic;
                        var settingCur = audioImporter.GetOverrideSampleSettings(EditorUserBuildSettings.activeBuildTarget.ToString());
                        var loadType = settingCur.loadType;
#if !UNITY_2022_3_OR_NEWER
                        var preloadAudioData = audioImporter.preloadAudioData;
#else
                        var preloadAudioData = settingCur.preloadAudioData;
#endif
                        var compressionFormat = settingCur.compressionFormat;
                        var quality = settingCur.quality;
                        var sampleRateSetting = settingCur.sampleRateSetting;
                        var sampleRateOverride = settingCur.sampleRateOverride;
                        StringBuilder sb = new StringBuilder();

                        bool settingRight = true;
                        if (!forceToMono) 
                        {
                            settingRight = false;
                            sb.Append("forceToMono!=").Append(true);
                        }

                        if (!normalize.boolValue)
                        {
                            settingRight = false;
                            sb.Append(",").Append("normalize!=").Append(true);
                        }

                        if (!loadInBackground)
                        {
                            settingRight = false;
                            sb.Append(",").Append("loadInBackground!=").Append(true);
                        }

                        if (ambisonic)
                        {
                            settingRight = false;
                            sb.Append(",").Append("ambisonic!=").Append(false);
                        }

                        if (loadType != AudioClipLoadType.Streaming)
                        {
                            settingRight = false;
                            sb.Append(",").Append("loadType!=Streaming");
                        }

                        if (preloadAudioData && loadType != AudioClipLoadType.Streaming)
                        {//loadType != AudioClipLoadType.Streaming才检测此项
                            settingRight = false;
                            sb.Append(",").Append("preloadAudioData!=").Append(false);
                        }

                        if (compressionFormat != AudioCompressionFormat.Vorbis)
                        {
                            settingRight = false;
                            sb.Append(",").Append("compressionFormat!=Vorbis");
                        }

                        if (quality > 0.5f)
                        {
                            //settingRight = false;
                        }

                        if (sampleRateSetting != AudioSampleRateSetting.OverrideSampleRate)
                        {
                            settingRight = false;
                            sb.Append(",").Append("sampleRateSetting!=OverrideSampleRate");
                        }

                        if (sampleRateOverride > 44100)
                        {
                            settingRight = false;
                            sb.Append(",").Append("sampleRateOverride>44100");
                        }

                        if(!settingRight)
                        {
                            var ftd = new FileTipsDetail() { strTips = $"音效设置问题:{sb.ToString()}", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(path, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = GetSortSeq(path);

                            checkList.Add(ftd);
                        }
                    }
                }
            }
        }

        ///// <summary>
        ///// 检测Timeline资源设置
        ///// </summary>
        ///// <param name="checkList"></param>
        //void CheckTimelineAssets(List<FileTipsDetail> checkList)
        //{
        //    var guids = UnityEditor.AssetDatabase.FindAssets("t:TimelineAsset", new string[] { });
        //    if (guids != null)
        //    {
        //        foreach (var t in guids)
        //        {
        //            var path = AssetDatabase.GUIDToAssetPath(t);
        //            var timelineAsset = AssetDatabase.LoadMainAssetAtPath(path) as TimelineAsset;
        //            if (timelineAsset != null)
        //            {
        //                bool settingRight = true;
        //                var otrs = timelineAsset.GetOutputTracks();
        //                foreach (var ott in otrs)
        //                {
        //                    if (ott.GetType().Name == "MarkerTrack")
        //                    {
        //                        settingRight = false;
        //                        break;
        //                    }
        //                }

        //                if (!settingRight)
        //                {
        //                    var ftd = new FileTipsDetail() { strTips = $"TimelineAsset,GetOutputTracks()中含有MarkerTrack", strFilePath = path, checkType = CheckType, strGroupJsonValue = KeyDesc };
        //                    SetFileTipsDetailLastCommitInfo(path, ftd);
        //                    SetFileTipsDetailJsonInfo(ftd);
        //                    ftd.sortSeq = ftd.svnDateTicks;

        //                    checkList.Add(ftd);
        //                }
        //            }
        //        }
        //    }
        //}

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            var audioObjsGuids = AssetDatabase.FindAssets("t:audioclip", new string[] { dir });

            CheckAudioImportSettings(audioObjsGuids, checkList);
            //CheckTimelineAssets(checkList);

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

