/*=================================================================================
* 创建者:刘军
* 功能描述:图片ABName冗余(排除UI图集里的图)设置检测
* 包含功能:1.图片ABName冗余(排除UI图集里的图)设置检测
*=================================================================================*/
using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using EditorUtilitys;

/// <summary>
/// 图片ABName冗余(排除UI图集里的图)设置检测
/// </summary>
public partial class ResSettingChecker
{
    /// <summary>
    /// 图片ABName冗余(排除UI图集里的图)设置检测
    /// 参数说明:两个参数eg:Assets/Art
    /// 参数1为string类型,目录
    /// </summary>
    class TexABNameRepeatChecker : ResSetingsCheckerBase
    {
        /// <summary>
        /// 目录
        /// </summary>
        public string dir;
        /// <summary>
        /// 键
        /// </summary>
        public override string KeyDesc { get { return texKeyString + resSettingCheckKeySplit + abNameRepeatKeyString; } }
        /// <summary>
        /// 检测类型
        /// </summary>
        public override ResSetingsCheckType CheckType { get { return ResSetingsCheckType.TexABNameRepeatCheck; } }
        /// <summary>
        /// abNameList
        /// </summary>
        Dictionary<string, List<string>> abNameDic = null;

        /// <summary>
        /// 初始化
        /// </summary>
        /// <param name="strParam"></param>
        /// <param name="lineNum"></param>
        public override void Init(string strParam, int lineNum)
        {
            base.Init(strParam, lineNum);

            if (!string.IsNullOrEmpty(strParam))
            {
                var paramsArr = strParam.Split(',');
                if (paramsArr != null && paramsArr.Length >= 1)
                {
                    try
                    {
                        dir = paramsArr[0];
                        bParamValid = true;
                    }
                    catch (Exception e)
                    {
                        UnityEngine.Debug.Log($"【TexABNameRepeatChecker.Init,捕获到异常:{e.ToString()}】");
                    }
                }
            }

            if (!bParamValid)
            {
                Debug.LogError($"TexABNameRepeatChecker类型参数配置错误:{strParam}");
            }
        }

        /// <summary>
        /// 检测设置
        /// </summary>
        /// <param name="assetPath"></param>
        /// <param name="textureImporter"></param>
        /// <returns></returns>
        bool CheckSetting(string assetPath, TextureImporter textureImporter, Texture2D texture, ref string strReasonTips)
        {
            strReasonTips = string.Empty;
            var assetBundleName = textureImporter.assetBundleName;
            var sprite = textureImporter.textureType == TextureImporterType.Sprite;

            if (!sprite && !string.IsNullOrEmpty(assetBundleName))
            {
                if (abNameDic == null)
                {
                    abNameDic = new Dictionary<string, List<string>>();
                }

                List<string> pathList = null;
                if (!abNameDic.TryGetValue(assetBundleName, out pathList))
                {
                    pathList = new List<string>();
                }

                pathList.Add(assetPath);
                abNameDic[assetBundleName] = pathList;

                return true;
            }

            return false;
        }

        /// <summary>
        /// 检测ABName冗余
        /// </summary>
        /// <param name="checkList"></param>
        void CheckABNameRepeat(List<FileTipsDetail> checkList)
        {
            if (abNameDic != null && abNameDic.Count > 0)
            {
                var enu = abNameDic.GetEnumerator();
                while (enu.MoveNext())
                {
                    var k = enu.Current.Key;
                    var v = enu.Current.Value;
                    if (v != null && v.Count > 1)
                    {
                        foreach (var t in v)
                        {
                            var strReasonTips = $"ABName冗余,ABName:{k}";
                            var ftd = new FileTipsDetail() { strTips = $"TexABNameRepeatChecker检测({strReasonTips})", strFilePath = t, checkType = CheckType, strGroupJsonValue = KeyDesc };
                            SetFileTipsDetailLastCommitInfo(t, ftd);
                            SetFileTipsDetailJsonInfo(ftd);
                            ftd.sortSeq = GetSortSeq(ftd.strTips);

                            checkList.Add(ftd);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 执行检测
        /// </summary>
        /// <param name="tipsFiles"></param>
        public override void ExecuteCheck(List<FileTipsDetail> tipsFiles)
        {
            base.ExecuteCheck(tipsFiles);

            if (string.IsNullOrEmpty(dir))
            {
                return;
            }

            List<string> filesList = new List<string>();
            FileUtilitys.GetFilesOnDir(dir, filesList, picCheckTypeFilter);
            List<FileTipsDetail> checkList = new List<FileTipsDetail>();
            string strReasonTips = string.Empty;

            foreach (var t in filesList)
            {
                var assetPath = t.Replace(Application.dataPath, "Assets");
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                Texture2D texture = (Texture2D)AssetDatabase.LoadAssetAtPath(assetPath, typeof(Texture2D));
                if (textureImporter != null && texture != null)
                {
                    CheckSetting(assetPath, textureImporter, texture, ref strReasonTips);
                }
            }

            CheckABNameRepeat(checkList);

            if (checkList != null && checkList.Count > 0)
            {
                checkList.Sort((l, r) => 
                {
                    var sortSeqCom = l.sortSeq - r.sortSeq;
                    if (sortSeqCom > 0)
                    {
                        return -1;
                    }
                    else if (sortSeqCom == 0)
                    {
                        return 0;
                    }
                    else
                    {
                        return 1;
                    }
                });

                tipsFiles.AddRange(checkList);
            }
        }
    }
}

