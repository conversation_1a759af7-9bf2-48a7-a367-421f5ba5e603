using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;
using System.Linq;

public class ShowChildWindos:EditorWindow
{
    public static List<GameObject> glist = new List<GameObject>();
    public static GameObject root;
	public static Texture sp;
    static bool bNext = false;
	public static void OpenWidows(GameObject go, Texture spite,bool bbNext = false)
    {
        bNext = bbNext;
        root = go;
        sp = spite;
        glist.Clear();
        Ondo(go);
        GetWindow<ShowChildWindos>();
    }

    void OnGUI()
    {
		if(root!=null)
		{
			EditorGUILayout.BeginHorizontal();
			GUILayout.Label(root.name);
			EditorGUILayout.EndHorizontal();
		}

        foreach (GameObject go in glist)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.ObjectField(go, typeof(GameObject), true);

			if(GUILayout.Button ("替换为RawImage")){
				var image = go.GetComponent <Image>();
                var ray = image.raycastTarget;
                var c = image.color;  
                if (image)
				{
					GameObject.DestroyImmediate (image);
				}
				var ri = go.AddComponent <RawImage> ();
                ri.raycastTarget = ray;
                ri.color = c;  
                ri.texture = sp;
			}
            EditorGUILayout.EndHorizontal();
        }
    }

    public static void Ondo(GameObject prefab)
    {
		var parentC = GameObject.FindObjectOfType<UnityEngine.Canvas> ();
		if(null == parentC)
        {
            EditorUtility.DisplayDialog("提示", "请打开ui场景在进行处理","ok","cancel");
            return;
        }
		Transform parent = parentC.transform;


        GameObject PrefabObject = GameObject.Find(prefab.name);
        if(null == PrefabObject)
        {
            PrefabObject = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
        }


        PrefabObject.transform.SetParent(parent, false);

        if (null == PrefabObject) { return; }
        Component[] tras = PrefabObject.GetComponentsInChildren(typeof(Transform), true);
        foreach (Component com in tras)
		{ 

			var comTf = com as Transform;

			Graphic ig = comTf.GetComponent<Graphic>();
			SpriteRenderer spr = comTf.GetComponent<SpriteRenderer>();
            Button btn = comTf.GetComponent<Button>();
            Toggle tog = comTf.GetComponent<Toggle>();

            if (null != ig)
            {
                if (ig.mainTexture == sp)
                {
                    glist.Add(comTf.gameObject); continue;
                }
            }
			if (null != spr)
			{
				if (CompareSpriteAndTex (spr.sprite ,sp))
				{
					glist.Add(comTf.gameObject); continue;
				}
			}
            if (null != btn)
            {
                SpriteState state = btn.spriteState;
				if (CompareSpriteAndTex (state.disabledSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }
				if (CompareSpriteAndTex (state.highlightedSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }
				if (CompareSpriteAndTex (state.pressedSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }  
			}
			if (null != tog)
			{
				SpriteState state = tog.spriteState;
				if (CompareSpriteAndTex (state.disabledSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }
				if (CompareSpriteAndTex (state.highlightedSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }
				if (CompareSpriteAndTex (state.pressedSprite ,sp) ) { glist.Add(comTf.gameObject); continue; }   
			}
        }
    }
	static bool CompareSpriteAndTex(Sprite s,Texture tex)
	{
		if(s==null||tex==null)
		{
			return false;
		}
		return s.texture == tex;
	}
}

public class ReplaceTextStyle : EditorWindow
{
    public static List<GameObject> glist = new List<GameObject>();
    public static GameObject root;
    public static Texture sp;
    static bool bNext = false;

    public static GameObject PrefabObject;
    public static Dictionary<GameObject ,Color> textColor = new Dictionary<GameObject, Color>();
    public static Dictionary<GameObject, Color> outlineColor = new Dictionary<GameObject, Color>();

    public static Color globalTextColor = Color.white;
    public static Color globalOutlineColor = Color.white;


    public static void OpenWidows(GameObject go, Texture spite, bool bbNext = false)
    {
        bNext = bbNext;
        root = go;
        sp = spite;
        glist.Clear();
        Ondo(go);
        GetWindow<ReplaceTextStyle>();
    }

    void OnGUI()
    {
        if (root != null)
        {
            EditorGUILayout.BeginHorizontal();
            GUILayout.Label(root.name);
            EditorGUILayout.EndHorizontal();
        }

        EditorGUILayout.BeginVertical();
        globalOutlineColor = EditorGUILayout.ColorField("全局描边颜色", globalOutlineColor);
        if (GUILayout.Button("一键替换描边颜色"))
        {
            GameObject[] keys = outlineColor.Keys.ToArray();
            for (int i=0; i<keys.Length; ++i)
            {
                 outlineColor[keys[i]] = globalOutlineColor;
            }
            foreach(GameObject go in glist)
            {
                if (go == null || root == null)
                {
                    continue;
                }

                var outline = go.GetComponentsInChildren<Outline>();
                if (null == outline || outline.Length == 0)
                {
                    continue;
                }
                for (int i = 0; i < outline.Length; ++i)
                {
                    outline[i].effectColor = new Color(outlineColor[go].r, outlineColor[go].g, outlineColor[go].b, outlineColor[go].a);
                }
                EditorUtility.SetDirty(go);
            }
        }
        globalTextColor = EditorGUILayout.ColorField("全局文本颜色", globalTextColor);
        if (GUILayout.Button("一键替换文本颜色"))
        {
            GameObject[] keys = textColor.Keys.ToArray();
            for (int i = 0; i < keys.Length; ++i)
            {
                textColor[keys[i]] = globalTextColor;
            }
            foreach(GameObject go in glist)
            {
                if (go == null || root == null)
                {
                    continue;
                }

                var text = go.GetComponentInChildren<Text>();
                if (null == text)
                {
                    continue;
                }
                text.color = new Color(textColor[go].r, textColor[go].g, textColor[go].b, textColor[go].a);
                EditorUtility.SetDirty(go);
            }
        }

        foreach (GameObject go in glist)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.ObjectField(go, typeof(GameObject), true);
            if (!outlineColor.ContainsKey(go))
            {
                outlineColor.Add(go, new Color(1, 242f / 255f, 175f / 255f, 1));
            }
            outlineColor[go] = EditorGUILayout.ColorField("描边颜色", outlineColor[go]);
            if (GUILayout.Button("替换文本描边颜色"))
            {
                if (go == null || root == null)
                {
                    return;
                }

                var outline = go.GetComponentsInChildren<Outline>();
                if (null == outline || outline.Length == 0)
                {
                    EditorUtility.DisplayDialog("提示", "按钮文本没有描边", "ok", "cancel");
                    return;
                }

                for (int i = 0; i < outline.Length; ++i)
                {
                    outline[i].effectColor = new Color(outlineColor[go].r, outlineColor[go].g, outlineColor[go].b, outlineColor[go].a);
                }
                EditorUtility.SetDirty(go);
            }

            if (!textColor.ContainsKey(go))
            {
                textColor.Add(go, new Color(103f / 255f, 53f / 255f, 6f / 255f, 1));
            }
            textColor[go] = EditorGUILayout.ColorField("文本颜色", textColor[go]);
            if (GUILayout.Button("替换文本颜色"))
            {
                if (go == null || root == null)
                {
                    return;
                }

                var text = go.GetComponentInChildren<Text>();
                if (null == text)
                {
                    EditorUtility.DisplayDialog("提示", "按钮没有文本", "ok", "cancel");
                    return;
                }
                text.color = new Color(textColor[go].r, textColor[go].g, textColor[go].b, textColor[go].a);
                EditorUtility.SetDirty(go);
            }
            EditorGUILayout.EndHorizontal();
        }
        if (GUILayout.Button("保存预制体"))
        {
            if (root == null)
            {
                return;
            }

            PrefabUtility.ReplacePrefab(PrefabObject, root, ReplacePrefabOptions.ConnectToPrefab);
        }
        EditorGUILayout.EndVertical();
    }

    public static void Ondo(GameObject prefab)
    {
        var parentC = GameObject.Find("UIRoot/CanvasWithMesh");
        if (null == parentC)
        {
            EditorUtility.DisplayDialog("提示", "请打开ui场景在进行处理", "ok", "cancel");
            return;
        }
        Transform parent = parentC.transform;


        PrefabObject = GameObject.Find(prefab.name);
        if (null == PrefabObject)
        {
            PrefabObject = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
        }


        PrefabObject.transform.SetParent(parent, false);

        if (null == PrefabObject) { return; }
        Component[] tras = PrefabObject.GetComponentsInChildren(typeof(Transform), true);
        foreach (Component com in tras)
        {

            var comTf = com as Transform;

            Graphic ig = comTf.GetComponent<Graphic>();
            SpriteRenderer spr = comTf.GetComponent<SpriteRenderer>();
            Button btn = comTf.GetComponent<Button>();
            Toggle tog = comTf.GetComponent<Toggle>();

            if (null != ig)
            {
                if (ig.mainTexture == sp)
                {
                    glist.Add(comTf.gameObject); continue;
                }
            }
            if (null != spr)
            {
                if (CompareSpriteAndTex(spr.sprite, sp))
                {
                    glist.Add(comTf.gameObject); continue;
                }
            }
            if (null != btn)
            {
                SpriteState state = btn.spriteState;
                if (CompareSpriteAndTex(state.disabledSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                if (CompareSpriteAndTex(state.highlightedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                if (CompareSpriteAndTex(state.pressedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
            }
            if (null != tog)
            {
                SpriteState state = tog.spriteState;
                if (CompareSpriteAndTex(state.disabledSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                if (CompareSpriteAndTex(state.highlightedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                if (CompareSpriteAndTex(state.pressedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
            }
        }
    }
    static bool CompareSpriteAndTex(Sprite s, Texture tex)
    {
        if (s == null || tex == null)
        {
            return false;
        }
        return s.texture == tex;
    }
}


public class SelectImageShowPrefab : EditorWindow
{
    [MenuItem("Tools/select image show ui prefab")]
    static void Open()
    {
        GetWindow<SelectImageShowPrefab>();
    }

    private List<Object> ui = new List<Object>();

    //private Dictionary<GameObject, List<GameObject>> useImageDi = new Dictionary<GameObject, List<GameObject>>();

    private static Object m_CurSelectObject = null;

    private Vector2 scellPos;

    private static Texture m_Sprite;
    static bool bNext = false;
    void OnGUI()
    {
        scellPos = EditorGUILayout.BeginScrollView(scellPos);
        for (int i = 0, uiCount = ui.Count; i < uiCount; i++) {
			var s = ui [i];
			EditorGUILayout.BeginHorizontal ();
			EditorGUILayout.ObjectField (s, typeof(GameObject), true);
			if ((s is GameObject) && GUILayout.Button ("显示子对象")) {
                ShowChildWindos.OpenWidows ((GameObject)s, m_Sprite, bNext);
            }
            if ((s is GameObject) && GUILayout.Button("替换字体风格"))
            {
                ReplaceTextStyle.OpenWidows((GameObject)s, m_Sprite, bNext);
            }
			EditorGUILayout.EndHorizontal ();
        } 
        EditorGUILayout.EndScrollView();

        
        if (Selection.objects.Length > 0)
        {
            if (Selection.objects[0] == m_CurSelectObject)
            {
                return;
            }
            m_CurSelectObject = Selection.objects[0];
            if (Selection.objects[0].GetType() == typeof(Texture2D))
            {
                m_Sprite = AssetDatabase.LoadAssetAtPath(AssetDatabase.GetAssetPath(m_CurSelectObject), typeof(Texture2D)) as Texture2D;
                FindPlayerUseImage(m_Sprite);
            }
            else if(Selection.objects[0].GetType() == typeof(RenderTexture))
            {
                m_Sprite = AssetDatabase.LoadAssetAtPath(AssetDatabase.GetAssetPath(m_CurSelectObject), typeof(RenderTexture)) as RenderTexture;
                FindPlayerUseImage(m_Sprite);
            }
        } 
    }



	public void FindPlayerUseImage(Texture sp)
    {
        ui.Clear();
        int index = 0;
        var paths = Directory.GetFiles(Application.dataPath+"/UI", "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab") || s.EndsWith(".asset")).ToArray();
		var spPath = AssetDatabase.GetAssetPath (sp);
        for (int i = 0; i < paths.Length; i++)
        {
			ShowProgress(index, paths.Length,paths[i]);
            index++;
            if(index == paths.Length)
            {
                EditorUtility.ClearProgressBar();
            }
            string path = paths[i].Replace('\\', '/');
            path = path.Substring(path.IndexOf("Assets/"));

            Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(Object));
            var PrefabObject = prefab as Object;
            if(null == PrefabObject) { continue; }

			var findassets = AssetDatabase.GetDependencies (path);
			foreach (var item in findassets) {
				if(item == spPath)
				{
					ui.Add(PrefabObject); break;
				}
			}
//            Component[] tras = PrefabObject.GetComponentsInChildren(typeof(Transform), true);
//            foreach (Component com in tras)
//            {
//                Image ig = (com as Transform).GetComponent<Image>();
//                Button btn = (com as Transform).GetComponent<Button>();
//                Toggle tog = (com as Transform).GetComponent<Toggle>();
//
//                if (null != ig)
//                {
//                    if (ig.sprite == sp)
//                    {
//                        ui.Add(PrefabObject); break;
//                    }
//                }
//                if (null != btn)
//                {
//                    SpriteState state = btn.spriteState;
//                    if (state.disabledSprite == sp) { ui.Add(PrefabObject); break; }
//                    if (state.highlightedSprite == sp) { ui.Add(PrefabObject); break; }
//                    if (state.pressedSprite == sp) { ui.Add(PrefabObject); break; }
//                }
//                if (null != tog)
//                {
//                    SpriteState state = tog.spriteState;
//                    if (state.disabledSprite == sp) { ui.Add(PrefabObject); break; }
//                    if (state.highlightedSprite == sp) { ui.Add(PrefabObject); break; }
//                    if (state.pressedSprite == sp) { ui.Add(PrefabObject); break; }
//                }
//            }
        }
    }


	static void ShowProgress(float progress, float total,string title="")
    {
		EditorUtility.DisplayProgressBar("搜索中:", title, progress / total);
    }
}


public class SelectImageSetUseSpriteMesh
{
    [MenuItem("Tools/select image set use sprite mesh")]
    static void Open()
    {
        if (Selection.objects.Length > 0)
        {
            if (Selection.objects[0] == m_CurSelectObject)
            {
                return;
            }
            m_CurSelectObject = Selection.objects[0];
            if (Selection.objects[0].GetType() == typeof(Texture2D))
            {
                m_Sprite = AssetDatabase.LoadAssetAtPath(AssetDatabase.GetAssetPath(m_CurSelectObject), typeof(Texture2D)) as Texture2D;
                FindPlayerUseImage(m_Sprite);
            }
            else if (Selection.objects[0].GetType() == typeof(RenderTexture))
            {
                m_Sprite = AssetDatabase.LoadAssetAtPath(AssetDatabase.GetAssetPath(m_CurSelectObject), typeof(RenderTexture)) as RenderTexture;
                FindPlayerUseImage(m_Sprite);
            }
        }
    }

    private static  List<string> ui = new List<string>();

    private static Object m_CurSelectObject = null;

    private Vector2 scellPos;

    private static Texture m_Sprite;
    static bool bNext = false;


    public static void FindPlayerUseImage(Texture sp)
    {
        ui.Clear();
        int index = 0;
        var paths = Directory.GetFiles(Application.dataPath + "/UI", "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab") || s.EndsWith(".asset")).ToArray();
        var spPath = AssetDatabase.GetAssetPath(sp);
        for (int i = 0; i < paths.Length; i++)
        {
            ShowProgress(index, paths.Length, paths[i]);
            index++;
            if (index == paths.Length)
            {
                EditorUtility.ClearProgressBar();
            }
            string path = paths[i].Replace('\\', '/');
            path = path.Substring(path.IndexOf("Assets/"));

            Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(Object));
            var PrefabObject = prefab as Object;
            if (null == PrefabObject) { continue; }

            var findassets = AssetDatabase.GetDependencies(path);
            foreach (var item in findassets)
            {
                if (item == spPath)
                {
                    ui.Add(path); 
                    break;
                }
            }
        }

        foreach(var assetPath in ui)
        {
            using (var editScope = new EditPrefabAssetScope(assetPath))
            {
                var prefab = editScope.prefabRoot;
                var mCount = 0;
                Graphic[] graphics = prefab.GetComponentsInChildren<Graphic>(true);
                for (int j = 0; j < graphics.Length; ++j)
                {
                    Graphic graphic = graphics[j];

                    if (graphic.mainTexture == sp)
                    {
                        Image img = graphic.GetComponent<Image>();
                        if (img != null)
                        {
                            img.useSpriteMesh = true;
                            mCount++;
                        }
                    }
                }
                if (mCount > 0)
                {
                    editScope.SetDirty(true);
                    Debug.Log("modify prefab:" + assetPath);
                }
            }
        }
    }


    static void ShowProgress(float progress, float total, string title = "")
    {
        EditorUtility.DisplayProgressBar("搜索中:", title, progress / total);
    }
}

public class FindUserOtherFront : EditorWindow
{
    [MenuItem("Tools/字体替换工具")]
    static void Open()
    {
        GetWindow<FindUserOtherFront>();
    }

    static Font m_Font;

    static Font m_UseFont;

    static Font m_AirlFont;

    static GameObject m_Prefab;

    void OnGUI()
    {
        GUILayout.Label("需要被替换的字体");
        m_Font = EditorGUILayout.ObjectField(m_Font, typeof(Font), true) as Font;
        GUILayout.Label("需要被替换的字体");
        m_AirlFont = EditorGUILayout.ObjectField(m_AirlFont, typeof(Font), true) as Font;
        if (GUILayout.Button("查找使用字体的对象"))
        {
            FindPlayerUseImage();
        }
        GUILayout.Label("目标字体");
        m_UseFont = EditorGUILayout.ObjectField(m_UseFont, typeof(Font), true) as Font;
        GUILayout.Label("ui预设");
        m_Prefab = EditorGUILayout.ObjectField(m_Prefab, typeof(GameObject), true) as GameObject;
        if (GUILayout.Button("替换预设字体"))
        {
            ChangeFont(m_Prefab);
        }
    }

    public void FindPlayerUseImage()
    {
        int index = 0;
        string[] paths = Directory.GetFiles(Application.dataPath, "*.prefab", SearchOption.AllDirectories);

        for (int i = 0; i < paths.Length; i++)
        {
            index++;
            if (index == paths.Length)
            {
                EditorUtility.ClearProgressBar();
            }
            string path = paths[i].Replace('\\', '/');
            path = path.Substring(path.IndexOf("Assets/"));

            Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(Object));
            GameObject PrefabObject = prefab as GameObject;
            if (null == PrefabObject) { continue; }
            Component[] tras = PrefabObject.GetComponentsInChildren(typeof(Transform), true);
            foreach (Component com in tras)
            {
                Text ig = (com as Transform).GetComponent<Text>();
                if(null != ig)
                {
                    if(ig.font == m_Font || ig.font == m_AirlFont)
                    {
                        //Debug.LogError(PrefabObject.name);
                        Debug.LogError(PrefabObject.name);
                        break;
                    }
                }
                //ig.f
                //if (null != ig)
                //{
                //    if (ig.sprite == sp)
                //    {
                //        ui.Add(PrefabObject); break;
                //    }
                //}
            }
        }
    }

    public void ChangeFont(GameObject prefab)
    {
        if(null == prefab) { return; }
        Component[] tras = prefab.GetComponentsInChildren(typeof(Transform), true);
        foreach (Component com in tras)
        {
            Text ig = (com as Transform).GetComponent<Text>();
            if (null != ig)
            {
                ig.font = m_UseFont;
            }
            //ig.f
            //if (null != ig)
            //{
            //    if (ig.sprite == sp)
            //    {
            //        ui.Add(PrefabObject); break;
            //    }
            //}
        }
        EditorUtility.SetDirty(prefab);
    }
}
