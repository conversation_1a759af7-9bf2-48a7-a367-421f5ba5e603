using UnityEngine;  
using UnityEngine.UI;
using UnityEditor; 
 
public class ReplaceImageStatic
{
    [MenuItem("GameObject/SavePrefab", false, -1)]
    static void SavePrefab()
    {
        string localPath = "Assets/UIResources/Prefab/HudNumber.prefab";
        Object prefab = EditorUtility.CreateEmptyPrefab(localPath);
        EditorUtility.ReplacePrefab(Selection.activeGameObject, prefab);
        AssetDatabase.Refresh();

        //GameObject source = PrefabUtility.GetPrefabObject(Selection.activeGameObject) as GameObject;
        //if (source == null) return;
        //string prefabPath = AssetDatabase.GetAssetPath(source).ToLower();
        //if (prefabPath.EndsWith(".prefab") == false) return;
        //PrefabUtility.ReplacePrefab(Selection.activeGameObject, source, ReplacePrefabOptions.ConnectToPrefab | ReplacePrefabOptions.ReplaceNameBased);
    }
        

    static Font GetFont()
    {
        //var fontId = isChinese ? "PINGFANG REGULAR.TTF" : "Michroma.ttf";
        var path2 = "Assets/UIResources/Font/Michroma.ttf";// string.Format("Assets/UIResources/Font/Michroma.ttf", fontId);
        Debug.LogWarning(path2);
#if UNITY_EDITOR_WIN
        path2 = path2.Replace("/", "\\");
#endif
        var obj2 = UnityEditor.AssetDatabase.LoadMainAssetAtPath(path2);
        var font = obj2 as Font;
        return font;
    }
}