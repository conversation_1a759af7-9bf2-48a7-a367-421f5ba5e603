using UnityEngine;  
using UnityEngine.UI;

public class ReplaceStatic{
	
	[UnityEditor.MenuItem("Assets/ReplaceSprite",false,0)]
	static void ReplaceSprite()
	{
		var rs = UnityEditor.AssetDatabase.LoadMainAssetAtPath("Assets/AllScene/ReplaceSprite/ReplaceSelector.prefab");
		if(rs==null)
		{
			return;
		}
		var rr = (rs as GameObject).GetComponent<ReplaceSprite>();
		if(
			rr.layouts==null
			||rr.layouts.Length==0
			||rr.oldSp == null
			||rr.newSp ==null
			)
		{
			return ;
		}
		SwapSprite(rr.layouts,rr.oldSp,rr.newSp);
	}
	public static void SwapSprite(Object[] layouts,Sprite old,Sprite newO)
	{ 
		foreach (var item in layouts) {
			var go = item as GameObject;
			var cl = UnityEditor.EditorUtility.CollectDependencies(new Object[]{item});
			if(UnityEditor.ArrayUtility.Contains<Object>(cl,old))
			{
				var b = false; 
				var images = go.GetComponentsInChildren<Image>(true);
				foreach (var img in images) {
					if(img.sprite == old)
					{
						img.sprite = newO;
						b |= true;
						Debug.Log("swap in " + GetPath(img.transform));
						
					}
				}
				
				var btns = go.GetComponentsInChildren<Button>(true);
				
				foreach (var btn in btns) {
					var bs = btn.spriteState;
					var bb = false;
					if(bs.pressedSprite == old)
					{
						bs.pressedSprite = newO;
						bb |= true;
					}
					if(bs.disabledSprite == old)
					{
						bs.disabledSprite = newO;
						bb |= true;
					}
					if(bs.highlightedSprite == old)
					{
						bs.highlightedSprite = newO;
						bb |= true;
					}
					if(bb)
					{
						btn.spriteState = bs;
						b |= true; 
						Debug.Log("swap in " + GetPath(btn.transform));
					}
				} 
				
				var toggles = go.GetComponentsInChildren<Toggle>(true);
				
				foreach (var btn in toggles) {
					var bs = btn.spriteState;
					var bb = false;
					if(bs.pressedSprite == old)
					{
						bs.pressedSprite = newO;
						bb |= true;
					}
					if(bs.disabledSprite == old)
					{
						bs.disabledSprite = newO;
						bb |= true;
					}
					if(bs.highlightedSprite == old)
					{
						bs.highlightedSprite = newO;
						bb |= true;
					}
					if(bb)
					{
						btn.spriteState = bs;
						b |= true; 
						Debug.Log("swap in " + GetPath(btn.transform));
					}
				} 
				
				if(b)
				{
					UnityEditor.EditorUtility.SetDirty(go);
					UnityEditor.AssetDatabase.SaveAssets();
					Debug.Log("swap in " + go.name);
				}
			}
		}
		Debug.Log("swap end");
	} 
	public static string GetPath(Transform t ,Transform root=null){ 
		//    GameObject sel = Selection.activeGameObject;
		//    var t = sel.transform;
		var str = "";
		var count=20000;
		do {
			count--;
			if(count<0)break;
			str=t.name+"/"+str;
			t=t.parent;
		} while (t!=null&&t!=root);
		str = str.TrimEnd('/');
		//    CombatUtl.clipboard = str;
		//    Debug.LogError(str);
		return str;
	}
}