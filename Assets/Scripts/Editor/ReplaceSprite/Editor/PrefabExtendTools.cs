using UnityEngine;  
using UnityEngine.UI;
using UnityEditor;
 
public class PrefabExtendTools
{ 
    [UnityEditor.MenuItem("Assets/Save Prefab")]
    static public void SavePrefab()
    {
        GameObject source = PrefabUtility.GetPrefabParent(Selection.activeGameObject) as GameObject;
        if (source == null) return;
        string prefabPath = AssetDatabase.GetAssetPath(source).ToLower();
        if (prefabPath.EndsWith(".prefab") == false) return;
        PrefabUtility.ReplacePrefab(Selection.activeGameObject, source, ReplacePrefabOptions.ConnectToPrefab | ReplacePrefabOptions.ReplaceNameBased);
    } 
}