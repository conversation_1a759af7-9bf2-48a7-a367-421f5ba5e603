using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;

public class ReplaceImageShowPrefab : EditorWindow
{
	[MenuItem("Tools/Replace Image Show Prefab")]
	static void Open()
	{
		GetWindow<ReplaceImageShowPrefab>();
	}

	private List<GameObject> ui = new List<GameObject>();

	//private Dictionary<GameObject, List<GameObject>> useImageDi = new Dictionary<GameObject, List<GameObject>>();

	private static Object m_CurSelectObject = null;

	private Vector2 scellPos;

	private static Sprite m_Sprite;

	static Sprite m_OldSp;
	static Sprite m_NewSp;
	public Object[] Objlayouts; 
	void OnGUI()
	{

		scellPos = EditorGUILayout.BeginScrollView(scellPos);

		GUILayout.Label("需要被替换的界面Sprite");
		m_OldSp = EditorGUILayout.ObjectField(m_OldSp, typeof(Sprite), true) as Sprite;		

		GUILayout.Label("新的界面Sprite");
		m_NewSp = EditorGUILayout.ObjectField(m_NewSp, typeof(Sprite), true) as Sprite;		

		if (GUILayout.Button("替换旧的预设"))
		{
			
			if(m_OldSp==null || m_NewSp==null)
			{
				return;
			}
			if(m_OldSp!=null)
			{
				FindPlayerUseImage(m_OldSp);
			}
			ReplaceStatic.SwapSprite(Objlayouts,m_OldSp,m_NewSp);

		}

		EditorGUILayout.EndScrollView();
	}

	public void FindPlayerUseImage(Sprite sp)
	{
		ui.Clear();
		int index = 0;
		string[] paths = Directory.GetFiles(Application.dataPath, "*.prefab", SearchOption.AllDirectories);

		Objlayouts=new Object[paths.Length];

		for (int i = 0; i < paths.Length; i++)
		{
			ShowProgress(index, paths.Length);
			index++;
			if(index == paths.Length)
			{
				EditorUtility.ClearProgressBar();
			}
			string path = paths[i].Replace('\\', '/');
			path = path.Substring(path.IndexOf("Assets/"));

			Objlayouts[i] = AssetDatabase.LoadAssetAtPath(path, typeof(Object));
	
		}
	}

	static void ShowProgress(float progress, float total)
	{
		EditorUtility.DisplayProgressBar("搜索中", "", progress / total);
	}
}
