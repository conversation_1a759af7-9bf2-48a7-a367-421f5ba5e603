using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.U2D;
using UnityEditor;
using UnityEditor.Sprites;
using UnityEditor.Callbacks;
using System.Text; 

public class CreateFont{

	//本方法是通过裁切的sprite导出字体文件，裁切使用的是unity自带的sprite editor，方便操作。
	//另外，裁切之后，每个sprite的名字的最后一个字符对应了ascii码的编码，比如：
	//0： 我们只要将sprite的名字命名成xxx0，就可以了！
	//由于使用到的了sprite加载，所以字体图片请放在Resources目录下面，等制作完毕，再把他们放到fonts文件夹或者其他文件夹中即可。
	//[MenuItem("Assets/UI/CreateMyFontSprite")]
	static void CreateMyFontSprite(){
		if (Selection.objects == null) return;
		if (Selection.objects.Length == 0) {
			Debug.LogWarning("没有选中Sprite文件，需要将Sprite Mode设置成Multiple，切分好，并且以以名字的最后一个字符当做ascii码");
			return;
		}
		//		string resoursePath = "Resources";
		UnityEngine.Object o = Selection.objects[0];
		if (o.GetType() != typeof(Texture2D)){
			Debug.LogWarning("选中的并不是图片文件");
			return;
		}
		string selectionPath = AssetDatabase.GetAssetPath(o);
		//		if (selectionPath.Contains(resoursePath)){
		string selectionExt = Path.GetExtension(selectionPath);
		if (selectionExt.Length == 0){
			return;
		}
		string loadPath = selectionPath.Remove(selectionPath.Length - selectionExt.Length);
		string fontPathName = loadPath + ".fontsettings";
		string matPathName = loadPath + ".mat";
		float lineSpace = 0.1f;//字体行间距，下面会根据最高的字体得到行间距，如果是固定高度，可以在这里自行调整
		//			loadPath = Path.GetFileNameWithoutExtension(selectionPath);
		//			Sprite[] sprites = Resources.LoadAll<Sprite>(loadPath);
		var sprites = AssetDatabase.LoadAllAssetsAtPath (selectionPath);
		var sppsss = new List<Sprite> ();
		foreach (var item in sprites) {
			if(item is Sprite)
			{
				sppsss.Add (item as Sprite);
			}
		}
		var spriteArr = sppsss.ToArray ();
		if (spriteArr.Length>0){
			//以textrue方式获得该资源，可以设置到创建的材质中去
			Texture2D tex = o as Texture2D;
			//创建字体材质，并且将图片设置好
			Material mat = new Material(Shader.Find("GUI/Text Shader"));
			AssetDatabase.CreateAsset(mat, matPathName);
			mat.SetTexture("_MainTex", tex);
			//创建字体文件，设置字体文件的材质
			Font m_myFont = new Font();
			m_myFont.material = mat;
			AssetDatabase.CreateAsset(m_myFont, fontPathName);
			//创建字体中的字符集数组
			CharacterInfo[] characterInfo = new CharacterInfo[spriteArr.Length]; 
			//得到最高的高度，设置行高和进行偏移计算
			for(int i=0;i<spriteArr.Length;i++){
				var sp = spriteArr [i] as Sprite;
				if (sp.rect.height > lineSpace){
					lineSpace = sp.rect.height;
				}
			}
			for(int i=0;i<spriteArr.Length;i++){
				Sprite spr = spriteArr[i] as Sprite;
				CharacterInfo info = new CharacterInfo();
				//设置ascii码，使用切分sprite的最后一个字母
				info.index = (int)spr.name[spr.name.Length-1];
				Rect rect = spr.rect;
				//根据pivot设置字符的偏移，具体需要做成什么样的，可以根据自己需要修改公式
				float pivot = spr.pivot.y/rect.height - 0.5f;
				if (pivot > 0){
					pivot = -lineSpace/2 - spr.pivot.y;
				}else if (pivot < 0){
					pivot = -lineSpace/2 + rect.height - spr.pivot.y;
				}else{
					pivot = -lineSpace/2;
				}
				Debug.Log(pivot);
				int offsetY = (int)(pivot + (lineSpace - rect.height)/2);
				//设置字符映射到材质上的坐标
				info.uvBottomLeft = new Vector2((float)rect.x/tex.width, (float)(rect.y)/tex.height);
				info.uvBottomRight = new Vector2((float)(rect.x+rect.width)/tex.width, (float)(rect.y)/tex.height);
				info.uvTopLeft = new Vector2((float)rect.x/tex.width, (float)(rect.y+rect.height)/tex.height);
				info.uvTopRight = new Vector2((float)(rect.x+rect.width)/tex.width, (float)(rect.y+rect.height)/tex.height);
				//设置字符顶点的偏移位置和宽高
				info.minX = 0;
				info.minY = -(int)rect.height-offsetY;
				info.maxX = (int)rect.width;
				info.maxY = -offsetY;
				//设置字符的宽度
				info.advance = (int)rect.width;
				characterInfo[i] = info;
			}
			// lineSpace += 2;
			m_myFont.characterInfo = characterInfo;
			EditorUtility.SetDirty(m_myFont);//设置变更过的资源
			AssetDatabase.SaveAssets();//保存变更的资源
			AssetDatabase.Refresh();//刷新资源，貌似在Mac上不起作用

			ImportAssets (fontPathName);

//			//由于上面fresh之后在编辑器中依然没有刷新，所以暂时想到这个方法，
//			//先把生成的字体导出成一个包，然后再重新导入进来，这样就可以直接刷新了
//			//这是在Mac上遇到的，不知道Windows下面会不会出现，如果不出现可以把下面这一步注释掉
//			AssetDatabase.ExportPackage(fontPathName, "temp.unitypackage");
//			AssetDatabase.DeleteAsset(fontPathName);
//			AssetDatabase.ImportPackage("temp.unitypackage", true);
			AssetDatabase.Refresh();

			//最佳高度：上下各留一个像素的间距，如果不需要可以注释掉，根据需求更改
			//打印是为了使使用者方便填写行高，因为font不支持设置行高。
			Debug.Log("创建字体成功, 最大高度：" + lineSpace + ", 最佳高度：" + (lineSpace+2));
		}else{
			Debug.LogWarning("没有选中Sprite文件，需要将Sprite放到Resources文件夹下面，可以参考函数上方的说明操作");
		}
		//		}
	}

    [MenuItem("Assets/UI/CreateMyFontSprite")]
    static void CreateMultiFontSprite()
    {
        if (Selection.objects.Length == 0)
            return;

        Texture2D texture = Selection.objects[0] as Texture2D;
        if (texture == null)
        {
            Debug.LogWarning("选中的并不是图片文件");
            return;
        }

        string path = AssetDatabase.GetAssetPath(texture);
        string dir = Path.GetDirectoryName(path);
        string name = Path.GetFileNameWithoutExtension(path);


        Dictionary<string, List<Sprite>> data = new Dictionary<string, List<Sprite>>();
        foreach (UnityEngine.Object obj in AssetDatabase.LoadAllAssetsAtPath(path))
        {
            Sprite sprite = obj as Sprite;
            if (sprite)
            {
                string fontName = sprite.name.Substring(0, sprite.name.LastIndexOf('_'));
                List<Sprite> list = null;
                if (data.TryGetValue(fontName, out list) == false)
                {
                    list = new List<Sprite>();
                    data.Add(fontName, list);
                }
                list.Add(sprite);
            }
        }

        float lineSpace = 0.1f;
        foreach (KeyValuePair<string, List<Sprite>> kvp in data)
        {
            string fontPath = string.Format("{0}/{1}.fontsettings", dir, kvp.Key);
            string matPath = string.Format("{0}/{1}.mat", dir, kvp.Key);
            Material mat = new Material(Shader.Find("GUI/Text Shader"));
            mat.SetTexture("_MainTex", texture);
            AssetDatabase.CreateAsset(mat, matPath);

            Font font = new Font();
            font.material = mat;
            AssetDatabase.CreateAsset(font, fontPath);

            List<Sprite> sprites = kvp.Value;
            for (int i = 0; i < sprites.Count; i++)
            {
                var sp = sprites[i] as Sprite;
                if (sp.rect.height > lineSpace)
                {
                    lineSpace = sp.rect.height;
                }
            }

            CharacterInfo[] characterInfo = new CharacterInfo[sprites.Count];
            for (int i = 0; i < sprites.Count; i++)
            {
                Sprite sprite = sprites[i];
                CharacterInfo info = new CharacterInfo();
                //设置ascii码，使用切分sprite的最后一个字母
                info.index = (int)sprite.name[sprite.name.Length - 1];
                Rect rect = sprite.rect;
                //根据pivot设置字符的偏移，具体需要做成什么样的，可以根据自己需要修改公式
                float pivot = sprite.pivot.y / rect.height - 0.5f;
                if (pivot > 0)
                {
                    pivot = -lineSpace / 2 - sprite.pivot.y;
                }
                else if (pivot < 0)
                {
                    pivot = -lineSpace / 2 + rect.height - sprite.pivot.y;
                }
                else
                {
                    pivot = -lineSpace / 2;
                }
                int offsetY = (int)(pivot + (lineSpace - rect.height) / 2);
                //设置字符映射到材质上的坐标
                info.uvBottomLeft = new Vector2((float)rect.x / texture.width, (float)(rect.y) / texture.height);
                info.uvBottomRight = new Vector2((float)(rect.x + rect.width) / texture.width, (float)(rect.y) / texture.height);
                info.uvTopLeft = new Vector2((float)rect.x / texture.width, (float)(rect.y + rect.height) / texture.height);
                info.uvTopRight = new Vector2((float)(rect.x + rect.width) / texture.width, (float)(rect.y + rect.height) / texture.height);
                //设置字符顶点的偏移位置和宽高
                info.minX = 0;
                info.minY = -(int)rect.height - offsetY;
                info.maxX = (int)rect.width;
                info.maxY = -offsetY;
                //设置字符的宽度
                info.advance = (int)rect.width;
                characterInfo[i] = info;
            }

            font.characterInfo = characterInfo;
            EditorUtility.SetDirty(font);
            AssetDatabase.SaveAssets();
        }
        AssetDatabase.Refresh();
    }
	private static void ImportAssets(params string[] paths)
	{
		// When using the cache server we have to write all import settings to disk first.
		// Then perform the import (Otherwise the cache server will not be used for the import)
		foreach (string path in paths)
			AssetDatabase.WriteImportSettingsIfDirty(path);

		try
		{
			AssetDatabase.StartAssetEditing();
			foreach (string path in paths)
				AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
		}
		finally
		{
			AssetDatabase.StopAssetEditing();
		}
	}
}
