using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.U2D;
using UnityEditor;
using UnityEditor.Sprites;
using UnityEditor.Callbacks;
using System.Text; 

public class CreateAlphaAtlas
{
	class Entry
	{
		public Sprite sprite;
		public Texture2D texture;
		public Vector2[] uvs;
		public string atlasName;
		public Texture2D atlasTexture;
		public Vector2[] atlasUvs;
	}

	public const string TEXTURE_ALPHA_ATLAS_PATH = "Tmp/TextureAlphaAtlas/";
	static string STRICT_FOLDER = "{0}/UI";
	//	static string STRICT_FOLDER = "{0}/UI/Common";

	private const string SpritePackingSuffix = ".spritepack";
	const int const_loop_count = int.MaxValue;

	static string basePath;
	/// <summary>
	/// atlas cache path
	/// </summary>
	/// <value>The base path.</value>
	public static string BasePath {
		get {
			if(basePath==null)
			{
				basePath = Path.Combine (Application.dataPath, TEXTURE_ALPHA_ATLAS_PATH);
			}
			return basePath;
		}
	}
	public static void ClearABName()
	{
		var path = BasePath.TrimEnd ('/');
		var asses = AssetDatabase.FindAssets ("",new string[]{ToRelativePath (path)});

		for (int i = 0, assesLength = asses.Length; i < assesLength; i++) {
			var uid = asses [i];
			var ass = AssetImporter.GetAtPath (AssetDatabase.GUIDToAssetPath (uid));
			ass.SetAssetBundleNameAndVariant (null, null);
		}
	}
	/// <summary>
	/// 生成透明通道图集
	/// </summary>
//	[MenuItem("Tools/AlphaAltas/CreateAlphaAtlas")]
	public static void CreateAndSaveToDisk(string outpath, BuildTarget targetPlatform)
	{
//		CreateAlphaAtlas.PackAlphaAltasToAssetBoundles(outpath, BuildAssetBundleOptions.ChunkBasedCompression|BuildAssetBundleOptions.DeterministicAssetBundle, targetPlatform);		
//		return;
		try { 
			AssetDatabase.Refresh(); 
//			EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn;   
//			LogHelp.Instance.Log ("RebuildAtlasCacheIfNeeded_start");
//			Packer.RebuildAtlasCacheIfNeeded(BuildTarget.iOS, true, Packer.Execution.ForceRegroup);
//			LogHelp.Instance.Log ("RebuildAtlasCacheIfNeeded_end");

			if (!Directory.Exists(BasePath))
				Directory.CreateDirectory(BasePath);

			CreateAlphaAtlasTexture(BasePath,outpath); 
			AssetDatabase.SaveAssets ();
			AssetDatabase.Refresh();
			CreateAlphaAtlas.PackAlphaAltasToAssetBoundles(outpath, BuildAssetBundleOptions.ChunkBasedCompression|BuildAssetBundleOptions.DeterministicAssetBundle, targetPlatform);		
			LogHelp.Instance.Log ("PackAlphaAltasToAssetBoundles_end");

			CreateAlphaAtlas.ClearABName ();
		} catch (Exception ex) {
			Debug.LogError (ex.ToString ());
			CreateAlphaAtlas.ClearABName ();
		}
		LogHelp.Instance.Log ("CreateAndSaveToDisk_end");
	}


//	static Dictionary<string,string[]> formatABNames = new Dictionary<string,string[]> ();
	static Dictionary<string,string> formatABNameDic = new Dictionary<string,string> ();
	static string OutPath;
	public static void CreateAlphaAtlasTexture(string basePath,string outpath)
	{

		LogHelp.Instance.Log ("CreateAlphaAtlasTexture_start");

		var loop = 0;
		var time = System.DateTime.Now;

		OutPath = outpath;
		///准备abnames 为生成的texture 设置abname, 判断依据是texture名称包含abname
		UpdateABNames ();

		//		var atlasnames = new List<string>();
		var atsDic = new Dictionary<int,string> ();

		FetchAtlasNames (atsDic);

		LogHelp.Instance.Log ("atlasName_end");

		//		return;
//		string[] atlasnames = Packer.atlasNames;
		var atlasnames = atsDic.Keys.ToArray ();
		///运行时tex2d 缓存 256x256 512x512..
		var texIdCache = CreateAtlasTexture (basePath, ref loop, atlasnames,atsDic);


		SaveTexIdCache (texIdCache); 

		CheckUnusedTex (texIdCache);

		Debug.Log (string.Format ("texIdCache:{0}", texIdCache.Count)); 
		LogHelp.Instance.Log ("CreateAlphaAtlasTexture_end");

	}

	static Dictionary<int, string> CreateAtlasTexture (string basePath, ref int loop, int[] atlasnames,Dictionary<int,string> atsDic)
	{
		var texCache = new Dictionary<string, Texture2D> ();
		///运行时tex2d color array 缓存 length 256x256 512x512..
		var texColorCache = new Dictionary<string, Color32[]> ();
		///拆分通道了的图集id -- 贴图路径 缓存, 之后存在本地文件texid里面,供设置sprite检测依据 id(图集名_pageindex)
		var texIdCache = new Dictionary<int, string> ();
		Texture2D tex2;
		Color32[] newColors;
		var t = new Texture2D (1, 1);
		byte[] bytesDefault = t.EncodeToPNG ();
		byte[] bytes = null;
		try {
			for (int i = 0, atlasnamesLength = atlasnames.Length; i < atlasnamesLength; i++) {
				if (++loop > const_loop_count) {
					break;
				}
				var atl = atlasnames [i];
				var atlname = atsDic[atl];

				if(atsDic.ContainsKey(atl)==false){
					continue;
				}
				var texs = Packer.GetTexturesForAtlas (atlname);
				//			Debug.Log (texs.Length);
				string outPath;
				string path;
				for (int j = 0, texsLength = texs.Length; j < texsLength; j++) {
					var tex = texs [j];
					//图集资源hashcode 
					if(tex.GetHashCode()!=atl){
						continue;
					}
					var bSplitAlpha = false;
					if (tex.format == TextureFormat.PVRTC_RGBA4) {
						//						if (tex.format != TextureFormat.PVRTC_RGB4) {
						//							continue;
						//						}
						if(atsDic.ContainsKey(atl)){
							bSplitAlpha = true;
						}
					}
					string key = string.Format ("{0}_{1}", tex.width, tex.height);
					if (texCache.ContainsKey (key) && texCache [key] != null) {
						tex2 = texCache [key];
					}
					else {
						tex2 = new Texture2D (tex.width, tex.height, TextureFormat.RGBA32, false);
						texCache [key] = tex2;
					}
					///从packer 获得图集数据
					ApplyUnReadTex (tex, tex2);
					var colors = tex2.GetPixels32 ();
					int count = colors.Length;
					string texName = tex.name;
					//				Debug.Log(texName);
					texName = string.Format ("{0}-fmt32", tex.name.Substring (0, texName.LastIndexOf ("-")));
					texIdCache [tex.GetHashCode ()] = string.Format ("{0}_{1}", texName, j);
					///存图集图片
					path = string.Format ("{0}_{1}.png", texName, j);
					outPath = Path.Combine (basePath, path);
					bytes = tex2.EncodeToPNG ();
					var formatStr = tex.format.ToString ();
					var shouldImportAlpha = (!bSplitAlpha) && (formatStr.Contains ("RGBA") || formatStr.Contains ("ARGB"));
					CheckTexExist (outPath, bytesDefault, shouldImportAlpha);
					WriteAllBytes (bytes, outPath);

					if(bSplitAlpha){
						if (texColorCache.ContainsKey (key)) {
							newColors = texColorCache [key];
						}
						else {
							newColors = new Color32[count];
							texColorCache [key] = newColors;
						}
						byte alpha;
						byte lastalpha = colors [0].a;
						var bSameAlpha = true;
						for (int k = 0; k < count; k++) {
							alpha = colors [k].a;
							bSameAlpha &= (alpha == lastalpha);
							newColors [k] = new Color32 (alpha, alpha, alpha, 255);
						}
						if (tex2 == null) {
							Debug.LogError ("tex2  is null!!!");
							if (texCache.ContainsKey (key) && texCache [key] != null) {
								tex2 = texCache [key];
							}
							else {
								tex2 = new Texture2D (tex.width, tex.height, TextureFormat.RGBA32, false);
								texCache [key] = tex2;
							}
						}
						tex2.SetPixels32 (newColors);
						tex2.Apply ();
						///存图集alpha通道图片
						path = string.Format ("{0}_{1}_alpha.png", texName, j);
						bytes = tex2.EncodeToPNG ();
						if (bSameAlpha) {
							Debug.Log (string.Format ("{0} SameAlpha", path));
							t.SetPixel (0, 0, new Color32 (lastalpha, lastalpha, lastalpha, 255));
							t.Apply ();
							bytes = t.EncodeToPNG ();
						}
						outPath = Path.Combine (basePath, path);
						CheckTexExist (outPath, bytesDefault);
						WriteAllBytes (bytes, outPath);
					}
				}
			}
		}
		catch (Exception e) {
			Debug.LogError (e.ToString ());
		}
		finally {
			foreach (var item in texCache) {
				GameObject.DestroyImmediate (item.Value);
			}
			texCache.Clear ();
		}
		return texIdCache;
	}

	static void CheckUnusedTex (Dictionary<int, string> texIdCache)
	{
		var objs = Directory.GetFiles (BasePath, "*.png", SearchOption.AllDirectories);

		var list = new List<string> (objs);

		foreach (var item in texIdCache.Values) {
			for (int j = list.Count-1 ; j >=0; j--) {
				var p = list [j];
				if(p.Contains (string.Format ("{0}_alpha", item))){
					list.RemoveAt (j);
					break;
				} 
			}
			for (int j = list.Count-1 ; j >=0; j--) {
				var p = list [j]; 
				if(p.Contains (item)){
					list.RemoveAt (j);
					break;
				}
			}
		}
		Debug.Log (string.Format ("Unused Tex:{0} {1}", list.Count,ToJson (list)));
		for (int i = 0; i < list.Count; i++) {
			var item = list [i];
			if (File.Exists (item)) {
				File.Delete (item);
				Debug.Log (string.Format ("delete!!! {0}", item));
			}
		}
	}

	public static string GetPlatformFolderForAssetBundles(BuildTarget target)
	{
		switch (target)
		{
		case BuildTarget.Android:
			return "Android";
		case BuildTarget.iOS:
			return "iOS";
		case BuildTarget.StandaloneWindows:
		case BuildTarget.StandaloneWindows64:
			return "Windows";

#if UNITY_2017_3_OR_NEWER
        case BuildTarget.StandaloneOSX:
#else
        case BuildTarget.StandaloneOSXIntel:
		case BuildTarget.StandaloneOSXIntel64:
        case BuildTarget.StandaloneOSXUniversal:
#endif

                return "OSX";
			// Add more build targets for your own.
			// If you add more targets, don't forget to add the same platforms to GetPlatformFolderForAssetBundles(RuntimePlatform) function.
		default:
			return null;
		}
	}
	static void BuildAtlasRes(string outputPath, BuildAssetBundleOptions assetBundleOptions, BuildTarget buildTarget){
		
		string outputPath_atlas = outputPath;
//		string outputPath_atlas = outputPath.Replace (AssetBundlesOutputPath.Substring (0,AssetBundlesOutputPath.Length-1),AssetBundlesOutputPath);


		if(Directory.Exists (outputPath_atlas)==false)
		{
			Directory.CreateDirectory (outputPath_atlas);
		}
		AssetDatabase.Refresh ();
		var abnames = AssetDatabase.GetAllAssetBundleNames ();
		var abpaths = AssetDatabase.GetAllAssetPaths ();
		var buildmap = new AssetBundleBuild[1] ;
		var assNames = new List<string> (); 

		UpdateABNames (); 
		var texIdCache = ReadTexIdCache ();
		var listAtlases = texIdCache.Values.ToArray ();
		var abd = new AssetBundleBuild ();


		int execount = 0;


		var basePath2 = BasePath;
		var basePathRel = ToRelativePath (basePath2);    

		EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn; 

		for (int i = 0, listAtlasesLength = listAtlases.Length; i < listAtlasesLength; i++) {
			execount = 0;


			var atl = listAtlases [i];
			assNames.Clear ();
			var abn = GetAssociateABName (atl);
			if (string.IsNullOrEmpty (abn)) {
				continue;
			}
			for (int j = 0, abpathsLength = abpaths.Length; j < abpathsLength; j++) {
				var path = abpaths [j];
				if(path.StartsWith ("Assets/")==false)
				{
					continue;
				}
				var abnN = AssetDatabase.GetImplicitAssetBundleName (path);
				if (abn == abnN) {
					assNames.Add (path);

					//set sprite

					var spp = AssetDatabase.LoadAllAssetsAtPath (path);
					var bDirty = false;
					//			Debug.Log (np);
					for (int k = 0, sppLength = spp.Length; k < sppLength; k++) {
						var item = spp [k];
						if (item is Sprite) {
							var sprite = item as Sprite; 
							if(sprite.packed == false){
								continue;
							}

							Texture2D atlasTexture;
							string atlasName;
							Packer.GetAtlasDataForSprite (sprite, out atlasName, out atlasTexture);
							if (string.IsNullOrEmpty (atlasName) == false) {
								SerializedObject so = new SerializedObject (sprite);
								var tId = atlasTexture.GetInstanceID ();
								if (texIdCache.ContainsKey (tId)) {
									if (atlasTexture != null) {

										var texPath = "";
										texIdCache.TryGetValue (atlasTexture.GetHashCode (), out texPath);
										if(string.IsNullOrEmpty (texPath))
										{ 
											continue;
										}

										execount++;

										so.FindProperty ("m_RD.textureRect").rectValue = GetAltasTextureRect (sprite, atlasTexture);
										var pp = string.Format ("{2}{0}{1}.png", texPath, "", basePathRel);


										var texture2D = AssetDatabase.LoadAssetAtPath<Texture2D> (pp);
										so.FindProperty ("m_RD.texture").objectReferenceValue = texture2D;
										var alphaPath = string.Format ("{2}{0}{1}.png", texPath, "_alpha", basePath2);
										Texture2D alphaTexture=null;
										//									Debug.Log (alphaPath);
										if (File.Exists (alphaPath)) {

											alphaPath = string.Format ("{2}{0}{1}.png", texPath, "_alpha", basePathRel);

											alphaTexture = AssetDatabase.LoadAssetAtPath<Texture2D> (alphaPath); 
											//										Debug.Log (alphaTexture.name);
										}
										so.FindProperty ("m_RD.alphaTexture").objectReferenceValue = alphaTexture;
										so.ApplyModifiedProperties ();

//										if(pp.Contains ("monster")){
//										Debug.Log (string.Format ("{3}------{0} texture2D:{1} alphaTexture:{2}", pp, (texture2D != null), (alphaTexture != null), sprite.name));
//										}
									}
									bDirty = true; 
								} 
								so.SetIsDifferentCacheDirty ();
								so.UpdateIfRequiredOrScript ();
							}
						}
					}

				}
			}

			Debug.Log (string.Format ("execount:{0}", execount));

			abd.assetBundleName = abn;
			abd.assetNames = assNames.ToArray ();
			buildmap [0] = abd;
			AssetDatabase.Refresh ();
			EditorSettings.spritePackerMode = SpritePackerMode.Disabled; 

			BuildPipeline.BuildAssetBundles (outputPath_atlas, buildmap, BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle, buildTarget);

			EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn; 
		}

		Debug.LogError (outputPath_atlas);
		Debug.LogError (outputPath);
		var files = Directory.GetFiles (outputPath_atlas, "*", SearchOption.AllDirectories);
		for (int i = 0, filesLength = files.Length; i < filesLength; i++) {
			var item = files [i];
			if (item.EndsWith (".manifest")) {
//				File.Delete (item);
				continue;
			}
//			File.Copy (item,item.Replace (outputPath_atlas,outputPath),true);
		}
//		FileUtil.CopyFileOrDirectory(outputPath_atlas,outputPath);
	}
	 


	static void AddAlphaTextureEachSprite(bool bStart){
		var loop = 0;
		LogHelp.Instance.Log ("AddAlphaTextureEachSprite-"+bStart);

		var texIdCache = ReadTexIdCache ();

		//		string build_path = string.Format (STRICT_FOLDER, Application.dataPath);
		//		var objs = Directory.GetFiles (build_path, "*.*", SearchOption.AllDirectories);

		var objs = AssetDatabase.FindAssets ("t:sprite");


		if(bStart==false)
		{
			EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn;   
		} 
		Debug.Log (string.Format ("spritePackerMode:{0}", EditorSettings.spritePackerMode.ToString ()));
		Debug.Log (string.Format ("texIdCache:{0}", texIdCache.Count));
		int execount = 0;
		//		var list2import = new List<string> ();

		var basePath2 = BasePath;
		var basePathRel = ToRelativePath (basePath2); 
		for (int i = 0, objsLength = objs.Length; i < objsLength; i++) {
			//			if (++loop > 34)
			//			{
			//				break;
			//			}
			var o = objs [i];
			o = AssetDatabase.GUIDToAssetPath (o);
			var np = ToRelativePath (o);
			var spp = AssetDatabase.LoadAllAssetsAtPath (np);
			var bDirty = false;
			//			Debug.Log (np);
			for (int j = 0, sppLength = spp.Length; j < sppLength; j++) {
				var item = spp [j];
				if (item is Sprite) {
					var sprite = item as Sprite;
					//					if (sprite.texture.format == TextureFormat.PVRTC_RGB4) {
					//
					//					}
					if(sprite.packed == false){
						continue;
					}

					Texture2D atlasTexture;
					string atlasName;
					Packer.GetAtlasDataForSprite (sprite, out atlasName, out atlasTexture);
					if (string.IsNullOrEmpty (atlasName) == false) {
						var tId = atlasTexture.GetInstanceID ();
						if (texIdCache.ContainsKey (tId)) {
							SerializedObject so = new SerializedObject (sprite);
							if (atlasTexture != null) {

								var texPath = "";
								texIdCache.TryGetValue (atlasTexture.GetHashCode (), out texPath);
								if(string.IsNullOrEmpty (texPath))
								{ 
									continue;
								}

								execount++;

								if (bStart) {
									var rect = GetAltasTextureRect (sprite, atlasTexture);
									so.FindProperty ("m_RD.textureRect").rectValue = rect;
									var pp = string.Format ("{2}{0}{1}.png", texPath, "", basePathRel);


									var texture2D = AssetDatabase.LoadAssetAtPath<Texture2D> (pp);
									so.FindProperty ("m_RD.texture").objectReferenceValue = texture2D;
									var alphaPath = string.Format ("{2}{0}{1}.png", texPath, "_alpha", basePath2);
									Texture2D alphaTexture=null;
									//									Debug.Log (alphaPath);
									if (File.Exists (alphaPath)) {

										alphaPath = string.Format ("{2}{0}{1}.png", texPath, "_alpha", basePathRel);

										alphaTexture = AssetDatabase.LoadAssetAtPath<Texture2D> (alphaPath); 
										//										Debug.Log (alphaTexture.name);
									}
									else{
										Debug.LogError (string.Format ("{0} ::{1}", sprite.name, rect));
									}
									so.FindProperty ("m_RD.alphaTexture").objectReferenceValue = alphaTexture;
									so.ApplyModifiedProperties ();

//									if(pp.Contains ("monster")){
//										Debug.Log (string.Format ("monster------{0} texture2D:{1} alphaTexture:{2}", pp, (texture2D != null), (alphaTexture != null)));
//									}
								}
								else {
									so.FindProperty ("m_RD.textureRect").rectValue = sprite.textureRect;
									so.FindProperty ("m_RD.texture").objectReferenceValue = SpriteUtility.GetSpriteTexture (sprite, false);
									so.FindProperty ("m_RD.alphaTexture").objectReferenceValue = null;
									so.ApplyModifiedProperties ();
//									Debug.LogError (string.Format ("{0} ::{1}", sprite.name,sprite.textureRect));
								}
//								EditorUtility.SetDirty (sprite);
								bDirty = true; 
							}
							so.SetIsDifferentCacheDirty ();
							so.UpdateIfRequiredOrScript ();
						}
					}
				}
			} 
		}
		if(bStart)
		{
			EditorSettings.spritePackerMode =SpritePackerMode.Disabled;   
		}
		AssetDatabase.Refresh ();
		//		Debug.Log (string.Format ("spritePackerMode:{0}", EditorSettings.spritePackerMode.ToString ()));
		Debug.Log (string.Format ("execount:{0}", execount));
		LogHelp.Instance.Log ("AddAlphaTextureEachSprite_end");
		return; 
	}

	static void FetchAtlasNames (Dictionary<int, string> atsDic)
	{
		string build_path = string.Format (STRICT_FOLDER, Application.dataPath);
		var objs = Directory.GetFiles (build_path, "*.*", SearchOption.AllDirectories);
		//		Debug.Log (objs.Length);
		/// sieve atlasnames of sprites
		for (int i = 0, objsLength = objs.Length; i < objsLength; i++) {
			//			if (++loop > 34)
			//			{
			//				break;
			//			}
			var o = objs [i];
			var np = ToRelativePath (o);
			var spp = AssetDatabase.LoadAllAssetsAtPath (np);
			//			Debug.Log (np);
			for (int j = 0, sppLength = spp.Length; j < sppLength; j++) {
				var item = spp [j];
				if (item is Sprite) {
					var sp = item as Sprite;
					Texture2D atlasTexture;
					string atlasName;
					Packer.GetAtlasDataForSprite (sp, out atlasName, out atlasTexture);
					if (atlasTexture != null) {
//					if (string.IsNullOrEmpty (atlasName) == false) {
						//					atlasnames.Add (atlasName);
						atsDic [atlasTexture.GetHashCode ()] = atlasName;
					}
				}
			}
		}
	}
	//
	static void UpdateABNames ()
	{
		formatABNameDic.Clear ();
		var dict = CacheDict.Instance.Dict;
		Debug.Log ("CacheDict:"+dict.Count);
		foreach (var item in dict) {
			var v = item.Value;
			var abn = AssetDatabase.GetImplicitAssetBundleName (v);
			formatABNameDic [string.Format ("-{0} ", item.Key)] = abn;
			formatABNameDic [string.Format ("-{0}-", item.Key)] = abn; 
		}

//		formatABNames.Clear ();
//		var abnames = AssetDatabase.GetAllAssetBundleNames ();
//		for (int i = 0, maxLength = abnames.Length; i < maxLength; i++) {
//			var abname = abnames [i];
//			var formatV = abname.Replace (SpritePackingSuffix, "").Replace ("/", ".");
//			formatABNames [abname] = new string[] {
//				(string.Format ("-{0} ", formatV)),
//				(string.Format ("-{0}-", formatV)),
//			};
//		}
	}

	private static void ImportAssets(List<string> paths)
	{
		// When using the cache server we have to write all import settings to disk first.
		// Then perform the import (Otherwise the cache server will not be used for the import)
		foreach (string path in paths)
			AssetDatabase.WriteImportSettingsIfDirty(path);

		try
		{
			AssetDatabase.StartAssetEditing();
			foreach (string path in paths)
				AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
		}
		finally
		{
			AssetDatabase.StopAssetEditing();
		}
	}

	public static void PackAlphaAltasToAssetBoundles(string outputPath, BuildAssetBundleOptions assetBundleOptions, BuildTarget targetPlatform)//PackAlphaAltasToAssetBoundles(System.Action build)
	{
#if !UNITY_IOS
				return;
#endif
#if !UNITY_2022_3_OR_NEWER
		EditorSettings.spritePackerMode = SpritePackerMode.Disabled;
		Packer.SelectedPolicy = typeof(CustomPackerPolicy).Name;  
#endif
		BuildAtlasRes (outputPath,assetBundleOptions,targetPlatform); 
		AssetDatabase.Refresh();
		AddAlphaTextureEachSprite (false);
		AssetDatabase.Refresh();
	}
	static Rect GetAltasTextureRect2(Sprite sp, Texture2D atlasTexture)
	{
		Rect textureRect = new Rect();
		Vector2[] uvs = SpriteUtility.GetSpriteUVs(sp, false);
		Vector2[] altasUvs = SpriteUtility.GetSpriteUVs(sp, true);
		int count = uvs.Length;
		int compare = 0;
		var bndUV = new Bounds ();
		for (int i = 1;i < count;i++)
		{
			bndUV.Encapsulate (uvs[i]);

//			if (uvs[i].x != uvs[0].x && uvs[i].y != uvs[0].y)
//			{
//				compare = i;
//				break;
//			}
		}
 

		var bndAtlas = new Bounds ();
		for (int i = 1;i < count;i++)
		{
			bndAtlas.Encapsulate (altasUvs[i]);

			//			if (uvs[i].x != uvs[0].x && uvs[i].y != uvs[0].y)
			//			{
			//				compare = i;
			//				break;
			//			}
		}
		textureRect.width = bndAtlas.extents.x / bndUV.extents.x;
		textureRect.height = bndAtlas.extents.y / bndUV.extents.y;
		textureRect.x = altasUvs[0].x - textureRect.width * uvs[0].x;
		textureRect.y = altasUvs[0].y - textureRect.height * uvs[0].y;
		textureRect.x *= atlasTexture.width;
		textureRect.y *= atlasTexture.height;
		textureRect.width *= atlasTexture.width;
		textureRect.height *= atlasTexture.height;
		return textureRect;
	}
	static Rect GetAltasTextureRect(Sprite sp, Texture2D atlasTexture)
	{
		Rect textureRect = new Rect();
		Vector2[] uvs = SpriteUtility.GetSpriteUVs(sp, false);
		Vector2[] altasUvs = SpriteUtility.GetSpriteUVs(sp, true);
		int count = uvs.Length;
		int compare = 0;
		for (int i = 1;i < count;i++)
		{
			if (uvs[i].x != uvs[0].x && uvs[i].y != uvs[0].y)
			{
				compare = i;
				break;
			}
		}
		textureRect.width = (altasUvs[0].x - altasUvs[compare].x) / (uvs[0].x - uvs[compare].x);
		textureRect.height = (altasUvs[0].y - altasUvs[compare].y) / (uvs[0].y - uvs[compare].y);
		textureRect.x = altasUvs[0].x - textureRect.width * uvs[0].x;
		textureRect.y = altasUvs[0].y - textureRect.height * uvs[0].y;
		textureRect.x *= atlasTexture.width;
		textureRect.y *= atlasTexture.height;
		textureRect.width *= atlasTexture.width;
		textureRect.height *= atlasTexture.height;
		return textureRect;
	}


#region Uti

	static void CheckTexExist(string path,byte[] bytes,bool importalpha=false)
	{
		if(File.Exists (path+".meta")==false){
			File.WriteAllBytes (path, bytes);  
			AssetDatabase.Refresh ();
//			var ti2 = AssetImporter.GetAtPath (ToRelativePath (path));
//			var texi2 = ti2 as TextureImporter;
//			if(texi2!=null){
//				texi2.mipmapEnabled = false;
//				var abName = GetAssociateABName (path);
//				texi2.SetAssetBundleNameAndVariant (abName, null); 
//
//			}
//			if (importalpha) {
//				texi2.alphaSource = TextureImporterAlphaSource.FromInput;
//				texi2.alphaIsTransparency = true;
//			} else {
//
//				texi2.alphaSource = TextureImporterAlphaSource.None;
//				texi2.alphaIsTransparency = false;
//			}


		}
		var ti = AssetImporter.GetAtPath (ToRelativePath (path));
		var texi = ti as TextureImporter;
		if(texi!=null)
		{ 
			if (importalpha) {
				texi.alphaSource = TextureImporterAlphaSource.FromInput;
				texi.alphaIsTransparency = true;
			} else {

				texi.alphaSource = TextureImporterAlphaSource.None;
				texi.alphaIsTransparency = false;
			}
			texi.mipmapEnabled = false;


			var abName = GetAssociateABName (path);
			texi.SetAssetBundleNameAndVariant (abName, null);
			texi.SaveAndReimport ();

//			var fPath = OutPath + "/" + abName;
//			fPath = Path.GetFullPath (fPath);
//			if(File.Exists (fPath)){
//				Debug.Log ("delete!!!  "+fPath);
//				File.Delete (fPath);
//			}
//			if(File.Exists (fPath+".manifest")){
//				Debug.Log ("delete!!!  "+fPath);
//				File.Delete (fPath+".manifest");
//			}

			//			EditorUtility.SetDirty (texi);
		}
	}

	static void WriteAllBytes (byte[] bytes, string outPath)
	{
		if(bytes==null)
		{
			return;
		}
		string md5now = GetMD5HashFromFile (outPath);

		if(string.IsNullOrEmpty (md5now)==false){
			var md5new = GetMD5HashFromFile (bytes);
			if(md5now==md5new)
			{
				Debug.Log (outPath +" has same md5:"+md5new + "=="+md5now);
				return;
			}
		} 


		var abName = GetAssociateABName (outPath);
//		texi.SetAssetBundleNameAndVariant (abName, null);
//		texi.SaveAndReimport ();

		var fPath = OutPath.Replace ("AssetBundles","AssetBundles2") + "/" + abName;
		fPath = Path.GetFullPath (fPath);
		if(File.Exists (fPath)){
			Debug.Log ("delete!!!  "+fPath);
			File.Delete (fPath);
		}
		if(File.Exists (fPath+".manifest")){
			Debug.Log ("delete!!!  "+fPath);
			File.Delete (fPath+".manifest");
		}


		File.WriteAllBytes (outPath, bytes);  
	}

	static string GetAssociateABName(string path){

		//		string[] abnames = AssetDatabase.GetAllAssetBundleNames ();
		if (formatABNameDic == null)
			return null;
		
		foreach (var item in formatABNameDic) { 
			if (path.Contains (item.Key)) {
				return item.Value;
			}
		}

		return null;
	}

	/// <summary>
	/// 从packer 图集获得可读写图集数据
	/// </summary>
	/// <param name="tex">Tex.</param>
	/// <param name="target">Target.</param>
	public static void ApplyUnReadTex(Texture2D tex,Texture2D target)
	{ 
		Material mat = new Material(Shader.Find("Unlit/Transparent"));
		RenderTexture rt = RenderTexture.GetTemporary(tex.width, tex.height, 0, RenderTextureFormat.ARGB32);
		Graphics.SetRenderTarget(rt);
		GL.Clear(true, true, Color.clear);
		GL.PushMatrix();
		GL.LoadOrtho(); 
		mat.mainTexture = tex;
		mat.SetPass(0);
		GL.Begin(GL.TRIANGLES);
		var tris = new int[]{
			0,1,2,0,2,3
		};
		var uvs =  new Vector3[]{
			new Vector3(0,0),
			new Vector3(0,1),
			new Vector3(1,1),
			new Vector3(1,0), 
		};
		var atlasUvs =  new Vector3[]{
			new Vector3(0,0),
			new Vector3(0,1),
			new Vector3(1,1),
			new Vector3(1,0), 
		};
		foreach (int index in tris)
		{
			GL.TexCoord(uvs [index]);
			GL.Vertex(atlasUvs [index]);
		}
		GL.End(); 
		GL.PopMatrix();

		target.ReadPixels(new Rect(0, 0, tex.width, tex.height), 0, 0);
		target.Apply();
		RenderTexture.ReleaseTemporary(rt);
	}

	public static string ToJson (object o)
	{
		return "";
//		return UltimateJson.JsonObject.Serialise(o);
	}	
	public static T ToObj<T> (string json) where T : class
	{
		return default(T);
//		return UltimateJson.JsonObject.Deserialise<T>(json);
	} 

	static Dictionary<int, string> ReadTexIdCache ()
	{
		Dictionary<int, string> texIdCache = new Dictionary<int, string> ();
		var jsons = File.ReadAllLines (BasePath + "/texid.txt");
		for (int i = 0, jsonsLength = jsons.Length; i < jsonsLength; i++) {
			var line = jsons [i];
			var ind = line.IndexOf (":");
			if (ind == -1) {
				continue;
			}
			try {
				texIdCache [int.Parse (line.Substring (0, ind))] = line.Substring (ind + 1);
			}
			catch (Exception ex) {
				Debug.Log (ex.ToString ());
				continue;
			}
		}
		return texIdCache;
	}

	static void SaveTexIdCache (Dictionary<int, string> texIdCache)
	{
		string basePath = BasePath;
		var strb = new StringBuilder ();
		foreach (var item in texIdCache) {
			strb.AppendFormat ("{0}:{1}\n", item.Key, item.Value);
		}
		File.WriteAllText (basePath + "/texid.txt", strb.ToString ());
	}

	static string ToRelativePath (string o)
	{
		return o.Replace (Application.dataPath, "Assets");
	}

	static string GetMD5HashFromFile(Byte[] bs)
	{
		try
		{  
			System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
			byte[] retVal = md5.ComputeHash(bs);

			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < retVal.Length; i++)
			{
				sb.Append(retVal[i].ToString("x2"));
			}
			return sb.ToString();
		}
		catch (Exception ex)
		{
			return "";
		}
	}
	static string GetMD5HashFromFile(string fileName)
	{
		if(File.Exists (fileName)==false)
		{
			return "";
		}
		try
		{
			FileStream file = new FileStream(fileName, FileMode.Open);
			System.Security.Cryptography.MD5 md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
			byte[] retVal = md5.ComputeHash(file);
			file.Close();

			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < retVal.Length; i++)
			{
				sb.Append(retVal[i].ToString("x2"));
			}
			return sb.ToString();
		}
		catch (Exception ex)
		{
			return "";
		}
	}
#endregion

}



public class CacheDict{
	static CacheDict instance;

	public static CacheDict Instance {
		get {
			if(instance==null)
			{
				instance = new CacheDict ();
			}
			return instance;
		}
	}

	Dictionary<string,string> dict=new Dictionary<string, string>();

	public Dictionary<string, string> Dict {
		get {
			return dict;
		}
	}

	public void Cache(string key ,string value)
	{
		dict [key] = value;
	}

	public string Fetch(string key)
	{
		string v = null;
		dict.TryGetValue (key, out v);
		return v;
	}
	public void Clear(){
		dict.Clear ();
	}

}


class TestAL{
	public static void Debug(params object[] args){ 
		var list = new List<string> ();
		foreach (var item in args) {
			list.Add (item.ToString ());
		}
		UnityEngine.Debug.Log (string.Join (" :: ", list.ToArray ()));
	}
}