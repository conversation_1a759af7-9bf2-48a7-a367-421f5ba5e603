using UnityEditor;
using System.Collections.Generic;
using UnityEngine;

class SpriteSplitAlpha : AssetPostprocessor
{
#if SPRITE_SPLITE
    static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets,
                                            string[] movedAssets, string[] movedFromAssetPaths)
    {
		iOS_Config.Instance.Reload ();

        List<string> assetsToImport = new List<string>();
        foreach (var str in importedAssets)
        {
            if (OnProcessTexture(str))
            {
                assetsToImport.Add(str);
            }
        }

        ImportAssets(assetsToImport.ToArray()); 
    }

    private static bool OnProcessTexture(string assetPath)
    {
        var textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
		bool dirty = false;

         if (textureImporter != null && textureImporter.textureType == TextureImporterType.Sprite && (assetPath.Contains("Assets/Special") == false))
         {

//			#if UNITY_IOS
			if(assetPath.StartsWith ("Assets/UI/")){
//				dirty = SetPlatformSplitAlpha (textureImporter, "Android", TextureImporterFormat.ETC_RGB4);

				var format = TextureImporterFormat.PVRTC_RGBA4;
				if(iOS_Config.Instance.data_rgb16.ContainsKey (assetPath)){
					format = TextureImporterFormat.RGB16;
				}
				dirty |= SetPlatformSplitAlphaIOS (textureImporter, BuildTarget.iOS.ToString (), format, SpriteMeshType.Tight);
			}
//			#endif
            if (dirty)
            {
                return true;
            }
         }

        return false;
    }

    private static bool SetPlatformSplitAlpha(TextureImporter textureImporter, string targetName, TextureImporterFormat desirFormat)
    {
        if (textureImporter.textureType == TextureImporterType.Sprite)
        {
            TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(targetName);
			if ((textureImporter.alphaIsTransparency&&!textureImporterPlatformSettings.allowsAlphaSplitting)
                || !textureImporterPlatformSettings.overridden
                || textureImporterPlatformSettings.format != desirFormat)
            {
                textureImporterPlatformSettings.overridden = true;
                textureImporterPlatformSettings.allowsAlphaSplitting = true;
                textureImporterPlatformSettings.format = desirFormat;
                textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                return true;
            }
        }

        return false;
    }

	private static bool SetPlatformSplitAlphaIOS(TextureImporter textureImporter, string targetName, TextureImporterFormat desirFormat,SpriteMeshType meshType )
	{
		if (textureImporter.textureType == TextureImporterType.Sprite)
		{ 
			TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(targetName);

			TextureImporterSettings tis = new TextureImporterSettings();
			textureImporter.ReadTextureSettings(tis);
			if ( !textureImporterPlatformSettings.overridden
				|| textureImporterPlatformSettings.format != desirFormat
				// ||tis.spriteMeshType!=meshType
			)
			{ 
				textureImporterPlatformSettings.overridden = true; 
				textureImporterPlatformSettings.format = desirFormat;
 
//				SpriteMeshType meshType = UnityEngine.SpriteMeshType.FullRect;
				// tis.spriteMeshType = meshType;


				textureImporter.SetTextureSettings (tis); 

				textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings); 
  
				return true;
			}
		}

		return false;
	}


	void OnPreprocessTexture()
	{
		var importer = (assetImporter as TextureImporter);

//		importer.textureType = TextureImporterType.GUI;
		if(EditorUserBuildSettings.activeBuildTarget == BuildTarget.iOS)
		{
			if(iOS_Config.Instance.data_rgb16.ContainsKey (assetPath))
			{
				importer.textureFormat = TextureImporterFormat.RGBA32;
//				Debug.LogError ("OnPreprocessTexture:"+assetPath);
			}
		}
	}

	void OnPostprocessTexture (Texture2D texture)
	{

		if(EditorUserBuildSettings.activeBuildTarget == BuildTarget.iOS)
		{
			if(iOS_Config.Instance.data_rgb16.ContainsKey (assetPath))
			{
				UIHelper.ModifyDither (texture);
//				Debug.LogError ("OnPostprocessTexture:"+texture.name);
			}
		}

	}

    private static void ImportAssets(string[] paths)
    { 
        // When using the cache server we have to write all import settings to disk first.
        // Then perform the import (Otherwise the cache server will not be used for the import)
        foreach (string path in paths)
            AssetDatabase.WriteImportSettingsIfDirty(path);

        try
        {
            AssetDatabase.StartAssetEditing();
            foreach (string path in paths)
                AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }  
    }
#endif
}
