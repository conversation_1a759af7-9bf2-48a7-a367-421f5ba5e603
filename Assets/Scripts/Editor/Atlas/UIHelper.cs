using System;
using System.Linq;
using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEngine.U2D;
using UnityEditor;
using UnityEditor.Sprites;
using UnityEditor.Callbacks;
using System.Text;
using Newtonsoft.Json;
using System.Text.RegularExpressions;
using War.Base;
using War.UI;

public class UIHelper{
	public static void FixUI(){
		MoveOutOfUI ();
		MoveFromUI2 ();
		AssetDatabase.Refresh ();
    }

    [MenuItem("Assets/UI/ClearCaching")]
    public static void ClearCaching()
    {
        //var list = new List<string>();
        //Caching.GetAllCachePaths(list);
        //foreach (var item in list)
        //{
        //    var c = Caching.GetCacheByPath(item);
        //    Debug.LogFormat("{0} cache",item);

        //}

        string PersistentRelativeUrl = BaseLoader.GetPersistentRelativeUrl();
        PersistentRelativeUrl = PersistentRelativeUrl.Replace("file://", "");
        var cachingPath = PersistentRelativeUrl + "/Caching";
        var c = Caching.GetCacheByPath(cachingPath);
        if (c.valid == false)
        {
            c = Caching.AddCache(cachingPath);
        }
        if (c.valid)
        {
            var bc = c.ClearCache();
            Caching.RemoveCache(c);
            Debug.Log("Clear:" + bc);

        }
    }

    [MenuItem("Assets/UI/CheckUUID")]
    public static void CheckUUID()
    {
        var guid = "307348180d4519947afec4c94dd21ff7";
        var a = AssetDatabase.GUIDToAssetPath(guid);
        Debug.Log(guid + "|" + a);

    }

    [MenuItem("Assets/UI/CopyNames")]
    public static void CopyNames()
    {
        var sels = Selection.gameObjects;
        List<string> list = new List<string>();
        foreach (var sel in sels)
        {
            list.Add(sel.name);
        }
        LogHelp.clipboard = string.Join("\n", list.ToArray());
    }

    [MenuItem("Assets/UI/BuildResSizeConfig")]
    public static void BuildResSizeConfig()
    {
        var path = EditorUtility.OpenFolderPanel("选择AssetBundlePath", "AssetBundles/", "*.*");
        var dic = new Dictionary<string, long>();
        if (string.IsNullOrEmpty(path) == false)
        {
            DirectoryInfo di = new DirectoryInfo(path);
            var files = di.GetFiles("*.*", SearchOption.AllDirectories);
            foreach (var f in files)
            {
                dic[f.FullName.Replace("\\", "/").Replace(path, "").Trim('/')] = f.Length;
            }
        }
        File.WriteAllText("Assets/Resources/ABSize.json", UIHelper.ToJson(dic));
        AssetDatabase.Refresh();
    }

    [MenuItem("Assets/UI/BuildResSizeConfig2")]
    public static void BuildResSizeConfig2()
    {
        var path = EditorUtility.OpenFilePanel("选择AssetBundlePath", "AssetBundles/", "*.txt");
        var dic = new Dictionary<string, long>();
        if (string.IsNullOrEmpty(path) == false)
        {
            string files = File.ReadAllText(path);
            var hashC = hashCheck.Parse(files);

            foreach (var abname in hashC.list.Keys)
            {
                dic[abname] = hashC.GetSize(abname);
            }

        }
        File.WriteAllText("Assets/Resources/ABSize.json", UIHelper.ToJson(dic));
        AssetDatabase.Refresh();
    }
    [MenuItem("Assets/UI/GetCharacterRes")]
    public static List<string> GetCharacterRes()
    {
        var path = "Assets/Animations/Characters";
        var dirs = Directory.GetDirectories(path);
        var match = new Regex(@"[0-9]+_(.*)", RegexOptions.Singleline);
        var listChname = new List<string>(dirs.Length);
        foreach (var d in dirs)
        {
            var m = match.Match(d);
            if (m.Success)
            {
                var chname = m.Groups[1];
                listChname.Add(chname.ToString());
            }
        }
        var dic = new Dictionary<string, List<string>>();

        var abnames = AssetDatabase.GetAllAssetBundleNames();
        foreach (var chname in listChname)
        {
            if (dic.ContainsKey(chname) == false)
            {
                dic[chname] = new List<string>();
            }
        }
        foreach (var abn in abnames)
        {
            if (abn.EndsWith(".prefab") && (abn.Contains("edit_") == false)) continue;
            if (!(abn.EndsWith(".playable") || abn.EndsWith(".prefab"))) continue;

            var split = abn.Split('_', '.');
            foreach (var sp in split)
            {
                if (dic.ContainsKey(sp))
                {
                    if (dic[sp].Contains(abn) == false)
                    {
                        dic[sp].Add(abn);
                    }
                }
            }
        }


        var listAll = new List<string>();
        var listA = new List<string>();
        foreach (var chname in dic.Keys)
        {
            listA.Clear();
            var v = dic[chname];
            foreach (var vv in v)
            {
                listA.AddRange(AssetDatabase.GetAssetPathsFromAssetBundle(vv));
            }
            var ds = AssetDatabase.GetDependencies(listA.ToArray(), true);
             
            foreach (var p in ds)
            {
                var ab = AssetDatabase.GetImplicitAssetBundleName(p);
                if (string.IsNullOrEmpty(ab)) continue; 
                if (listAll.Contains(ab) == false)
                {
                    listAll.Add(ab);
                }

            }

        }
        return listAll;
    }
    [MenuItem("Assets/UI/CharacterResConfig")]
    public static void CharacterResConfig()
    {
        var path = "Assets/Animations/Characters";
        var dirs = Directory.GetDirectories(path);
        var match = new Regex(@"[0-9]+_(.*)", RegexOptions.Singleline);
        var listChname = new List<string>(dirs.Length);
        foreach (var d in dirs)
        {
            var m = match.Match(d);
            if(m.Success)
            {
                var chname = m.Groups[1];
                listChname.Add(chname.ToString());
            }
        }
        var dic = new Dictionary<string, List<string>>();

        var abnames = AssetDatabase.GetAllAssetBundleNames();
        foreach (var chname in listChname)
        {
            if(dic.ContainsKey(chname)==false)
            {
                dic[chname] = new List<string>();
            }
        }
        foreach (var abn in abnames)
        {
            if (abn.EndsWith(".prefab") && (abn.Contains("edit_") == false)) continue;
            if (!(abn.EndsWith(".playable") || abn.EndsWith(".prefab"))) continue;

            var split = abn.Split('_','.');
            foreach (var sp in split)
            {
                if(dic.ContainsKey(sp))
                {
                    if(dic[sp].Contains(abn)==false)
                    {
                        dic[sp].Add(abn);
                    }
                }
            }
        }
        var textAsset = Resources.Load<TextAsset>("ABSize");

        var sizeConfig = ToObj<Dictionary<string, int>>(textAsset.text);

        var listAll = new List<List<object>>();


        foreach (var chname in dic.Keys)
        {
            var v = dic[chname];
            var listA = new List<string>();
            foreach (var vv in v)
            {
                listA.AddRange(AssetDatabase.GetAssetPathsFromAssetBundle(vv));
            }
            var ds = AssetDatabase.GetDependencies(listA.ToArray(),true);
            var total = 0;
            v.Add(total.ToString());
            listA.Clear();
            foreach (var p in ds)
            {
                var ab = AssetDatabase.GetImplicitAssetBundleName(p);
                if (string.IsNullOrEmpty(ab)) continue;
                if(listA.Contains(ab)==false)
                {
                    listA.Add(ab);
                }
                var size = 0;
                if (sizeConfig.ContainsKey(ab))
                {
                    size = sizeConfig[ab];
                }
                v.Add(p+"|"+ size);
            }
            foreach (var ab in listA)
            {
                if (ab.Contains("shaders_for_warmup")) continue;
                if (sizeConfig.ContainsKey(ab))
                {
                    total += sizeConfig[ab];
                }
            }
            listAll.Add(new List<object>() { chname,total, v });

            v.Add(total.ToString());
        }
        listAll.Sort((a, b) => (int)b[1] - (int)a[1]);
        File.WriteAllText("Assets/EditorConfig/ChRes.json", UIHelper.ToJson(listAll));
        AssetDatabase.Refresh();
    }

    /// <summary>
    /// Moves from U i . 遍历UI目录的资源,对于没有被ui目录下资源引用的sprite,移动到art下特定文件夹下
    /// Moves the out of U.
    /// </summary>
    [MenuItem("Assets/UI/MoveOutOfUI")]
	public static void MoveOutOfUI(){
//		var dependences = AssetDatabase.GetDependencies ("Assets/UI/battle",true);
		var checkPath = "Assets/UI/";
        var newPath = "UnusedImage/";

		var allSet = new Dictionary<string,bool> ();
		var usedSet = new Dictionary<string,bool> ();
		var findassets = AssetDatabase.FindAssets ("", new string[]{
            "Assets/UI",
            "Assets/Art/Maps/Battle"
        });
        Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase);


        int startIndex = 0;
        EditorApplication.update = delegate ()
        {
            try
            {
                for (int i = 0; i < 1; i++)
                {

                    string uid = findassets[startIndex];
                    var ob = AssetDatabase.GUIDToAssetPath(uid);


                    bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", ob, (float)startIndex / (float)findassets.Length);

                    if (isPng(ob))
                    {
                        allSet[ob] = true;
                    }
                    else
                    {
                        var dependences = AssetDatabase.GetDependencies(ob, true);
                        foreach (var dep in dependences)
                        {
                            if (isPng(dep))
                            {
                                usedSet[dep] = true;
                            }
                        }
                    }

                    startIndex++;
                    if (isCancel || startIndex >= findassets.Length)
                    {
                        EditorUtility.ClearProgressBar();
                        EditorApplication.update = null;
                        startIndex = 0;

                        Debug.LogError(usedSet.Count);
                        Debug.LogError(allSet.Count);
                        var sett = new HashSet<string>(allSet.Keys);
                        sett.ExceptWith(usedSet.Keys);
                        Debug.LogError(sett.Count);
                        Debug.LogError(ToJson(sett));
                        LogHelp.clipboard = ToJson(sett);
                        Debug.Log("匹配结束");
                        foreach (var item in sett)
                        {
                            var target = item.Replace(checkPath, newPath);
                            EditorHelp.CheckDir(target);
                            EditorHelp.CopyFile(item, target);
                            EditorHelp.CopyFile(item + ".meta", target + ".meta");
                            File.Delete(item);
                            File.Delete(item + ".meta");
                        }
                        return;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError(e.ToString());
                EditorApplication.update = null;
                EditorUtility.ClearProgressBar();
            }

        };

        //int count = 2;
        //var sett1 = new HashSet<string>(allSet.Keys);
        //foreach (var item in sett1)
        //{
        //    Debug.LogError("1===============================");
        //    Debug.LogError(count);
        //    if (--count < 0)
        //        break;
        //    Debug.LogError("2===============================");
        //    Debug.LogError(count);
        //    var target = item.Replace(checkPath + "/", item);
        //    Debug.LogError(item);
        //    Debug.LogError(target);
        //    EditorHelp.CheckDir(target);
        //    EditorHelp.CopyFile(item, target);
        //    EditorHelp.CopyFile(item + ".meta", target + ".meta");
        //    File.Delete(item);
        //    File.Delete(item + ".meta");
        //}
    }
	/// <summary>
	/// Moves from U i2. 遍历Art目录的资源,筛选引用到ui文件夹下的sprite,并移动到art下特定文件夹下
	/// </summary>
	[MenuItem("Assets/UI/MoveFromUI")]
	public static void MoveFromUI2(){
		//		var dependences = AssetDatabase.GetDependencies ("Assets/UI/battle",true);
		LogHelp.Instance.Log ("MoveFromUI2_start");
		var checkPath = "Assets/Art";
		var fail_checkPath = "Assets/UI/";

		var allSet = new Dictionary<string,bool> ();
		var usedSet = new Dictionary<string,bool> ();
		var findassets = AssetDatabase.FindAssets ("", new string[]{ checkPath });
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase);
		Func<string,bool> isExclude = (s) => s.StartsWith (fail_checkPath);

		for (int i = 0, findassetsLength = findassets.Length; i < findassetsLength; i++) {
			var uid = findassets [i];
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if (isPng (ob)) {
				continue;
			}
			allSet [ob] = true;
		}
		Debug.LogError (allSet.Count);

		var dependences = AssetDatabase.GetDependencies (allSet.Keys.ToArray (), true);
		for (int j = 0, dependencesLength = dependences.Length; j < dependencesLength; j++) {
			var dep = dependences [j];
			//			if (isPng (ob)) {
			//				//				allSet [ob] = true;
			//				continue;
			//			}
			if (allSet.ContainsKey (dep))
				continue;
			allSet [dep] = true;
			if (isPng (dep) && isExclude (dep)) {
				usedSet [dep] = true;
			}
		}
//		} 
		Debug.LogError (usedSet.Count);
		Debug.LogError (allSet.Count);
//		var sett = new HashSet<string> (allSet.Keys);
//		sett.ExceptWith(usedSet.Keys);
//		Debug.LogError (sett.Count); 
		Debug.LogError (ToJson (usedSet.Keys)); 
//
		int count = 1;
		var arr = usedSet.Keys.ToArray ();
		for (int i = 0, arrLength = arr.Length; i < arrLength; i++) {
//			if (--count < 0)
//				break;
			var item = arr [i];
			var target = item.Replace (fail_checkPath, "Assets/Art/Sprites/Battle/");
			EditorHelp.CheckDir (target);
			EditorHelp.CopyFile (item,target);
			EditorHelp.CopyFile (item+".meta",target+".meta");
			File.Delete(item);
			File.Delete (item + ".meta");
		}
		LogHelp.Instance.Log ("MoveFromUI2_end");

	}

	/// <summary>
	/// Referenceses the sprite.获取粘贴板里面的文件列表的所有图片资源引用
	/// </summary>
	[MenuItem("Assets/UI/ReferencesSprite", false, 10)]
	static private void ReferencesSprite()
	{ 
		var fail_checkPath = "Assets/UI/";
		var list = Newtonsoft.Json.JsonConvert.DeserializeObject <List<string>> (LogHelp.clipboard);
		var dicSprite = new Dictionary<string,bool> ();
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase)||s.EndsWith (".tga", StringComparison.CurrentCultureIgnoreCase);
//		System.Func<string,bool> isExclude = (s) => s.StartsWith (fail_checkPath);
		foreach (var item in list) {
			var deps = AssetDatabase.GetDependencies (item,true);
			foreach (var dep in deps) {
				if(isPng(dep))
				{
					dicSprite [dep] = true;
				}
			}
		}
		var str = ToJson (dicSprite.Keys);
		LogHelp.clipboard = str;
		Debug.LogError (str);
	}
	/// <summary>
	/// Checks the sprite AB name ex.检查同个spritepackingtag 是否存在多个abname当中
	/// </summary>
	[MenuItem("Assets/UI/CheckSpriteMulABNameInPackTag")]
	public static void CheckSpriteMulABNameInPackTag(){ 
 
		var findassets = AssetDatabase.FindAssets ("t:sprite");
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase)||s.EndsWith (".tga", StringComparison.CurrentCultureIgnoreCase);
		List<string> list = new List<string> ();
		foreach (var uid in findassets) {
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if(isPng(ob)){
				var ai = AssetImporter.GetAtPath (ob)as TextureImporter;
				if(ai&&!string.IsNullOrEmpty (ai.spritePackingTag))
				{
					list.Add (ai.spritePackingTag+"||"+ai.assetBundleName+"||"+ob);
				}
			}  
		} 
		list.Sort (string.Compare);

		Func<string,string,int> isFilter = (s1,s2) =>{
			var maxI = Mathf.Min (s1.Length,s2.Length); 
			var ind = 0;
			for (int i = 0; i < maxI; i++) {
				
				if(s1[i] == (s2[i]))continue;
				ind = i;
				break;
			}
			return ind;
		};
		List<string> list2 = new List<string> ();

		for (int i = 0; i < list.Count-1; i++) {
			var item = list [i];
			var item2 = list [i+1];

			var markInd = item.IndexOf ("||");
			var lmarkInd = item.LastIndexOf("||");

			var ind = isFilter (item, item2);
			if(markInd>-1)
			if(ind>markInd)
			{
				if(ind<lmarkInd)
				{
					Debug.LogError ("warning!!!");
					Debug.LogError (item);
					Debug.LogError (item2);
					list2.Add ("");
					list2.Add (item);
					list2.Add (item2);
				}
			}
		} 
		LogHelp.clipboard = ToJson (list2);
	}
	/// <summary>
	/// Checks the name of the sprite AB. 遍历所有目录下的sprite,筛选abname为空的资源,并对其reimport
	/// </summary>
	[MenuItem("Assets/UI/CheckSpriteNullABNameAndImport")]
	public static void CheckSpriteNullABNameAndImport(){ 
		LogHelp.Instance.Log ("CheckSpriteABName_start");
		var checkPath = "Assets";

		var allSet = new Dictionary<string,bool> ();
		var usedSet = new Dictionary<string,bool> ();
		var findassets = AssetDatabase.FindAssets ("t:sprite");
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase)||s.EndsWith (".tga", StringComparison.CurrentCultureIgnoreCase);
		Func<string,bool> isFilter = (s) =>{
			var abname = AssetDatabase.GetImplicitAssetBundleName (s);

			return string.IsNullOrEmpty (abname);
		};

		foreach (var uid in findassets) {
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if(isPng(ob)){
				allSet [ob] = true;
//				continue;
			} 
			if(isFilter(ob)){
				usedSet [ob] = true;
			} 
		} 
		Debug.LogError (usedSet.Count); 
		var keys = usedSet.Keys.ToArray ();
		var clip = ToJson (keys);
//		LogHelp.clipboard = clip;
		Debug.LogError (clip); 

//		for (int i = 0; i < keys.Length; i++) {
//			var item = keys[i];
//			var ai = AssetImporter.GetAtPath (item);
//			if(ai)
//			{
//				ai.SaveAndReimport ();
//			}
//		}
		ImportAssets (keys);
		LogHelp.Instance.Log ("CheckSpriteABName_end");

    }
    [MenuItem("Assets/UI/RecordPackingTag")]
    public static void RecordPackingTag()
    {
        var findassets = AssetDatabase.FindAssets("t:Sprite", new string[] { "Assets/UI" });
        var list = new List<string>();
        foreach (var gid in findassets)
        {
            var path = AssetDatabase.GUIDToAssetPath(gid);
            var ai = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ai)
            {
                list.Add(string.Format("{0},{1},{2}", path, ai.spritePackingTag, ai.wrapMode));
            }
        }
        var str = ToJson(list);
        File.WriteAllText("Assets/UI/packtag.txt", str);
    }
    [MenuItem("Assets/Character/TurnOffMinMap")]
    public static void TurnOffMinMap()
    {
        var folder = "Assets/Animations/Characters";
        var findassets = AssetDatabase.FindAssets("t:Texture2D", new string[] { folder });
        var list = new List<string>();
        foreach (var gid in findassets)
        {
            var path = AssetDatabase.GUIDToAssetPath(gid);
            var dir = Path.GetFileNameWithoutExtension(Path.GetDirectoryName(path));
            //Debug.Log(dir);
            if (dir != "Ex") continue;
            var ai = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ai)
            {
                TextureImporterSettings textureImporterSettings = new TextureImporterSettings();
                ai.ReadTextureSettings(textureImporterSettings);

                if(textureImporterSettings.mipmapEnabled)
                {
                    textureImporterSettings.mipmapEnabled = false;
                    ai.SetTextureSettings(textureImporterSettings);
                    list.Add(path);
                }

                //list.Add(string.Format("{0},{1},{2}", path, ai.spritePackingTag, ai.wrapMode));
            }
        }
        ImportAssets(list.ToArray());
        AssetDatabase.Refresh();
        var str = ToJson(list);
        Debug.Log(str);

        //File.WriteAllText("Assets/UI/packtag.txt", str);
    }
    /// <summary>
    /// 遍历UI文件夹下的sprite,筛选width or height 大于512的,设置专门的packingtag
    /// </summary>
    [MenuItem("Assets/UI/CheckBigPicAndPack")]
    public static void CheckBigPicAndPack()
    {
        //LogHelp.Instance.Log("CheckBigPic_start");
        var checkPath = "Assets/UI";
        var allSet = new Dictionary<string, bool>();
        var usedSet = new Dictionary<string, bool>();
        var findspriteassets = AssetDatabase.FindAssets("t:sprite", new string[] { checkPath });
        var findassets = AssetDatabase.FindAssets("", new string[] { checkPath });
        Func<string, bool> isGo = (s) => s.EndsWith(".prefab", StringComparison.CurrentCultureIgnoreCase);
        Func<string, bool> isAss = (s) => s.EndsWith(".asset", StringComparison.CurrentCultureIgnoreCase); //剔除包含在SpriteAssets中的图片
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase);

        foreach (var uid in findassets)
        {
            var ob = AssetDatabase.GUIDToAssetPath(uid);
            if (isAss(ob) == false)
            {
                continue;
            }
            var dependences = AssetDatabase.GetDependencies(ob, true);
            foreach (var dep in dependences)
            {
                var a = string.Compare(checkPath, dep);
                if (isPng(dep) && a<0)
                {
                    usedSet[dep] = true;
                  }
            }
        }

        foreach (var uid in findassets)
        {
            var ob = AssetDatabase.GUIDToAssetPath(uid);
            if (isGo(ob) == false)
            {
                continue;
            }
            var dependences = AssetDatabase.GetDependencies(ob, true);
            foreach (var dep in dependences)
            {
                var a = string.Compare(checkPath, dep);
                if (isPng(dep) && a < 0)
                {
                    var ti = AssetDatabase.LoadAssetAtPath<Texture2D>(dep) as Texture2D;
                    if (ti == null)
                    {
                        continue;
                    }
                    var b = Mathf.Max(ti.width, ti.height) > 512;
                    if (b)
                    {
                        allSet[dep] = true;
                    }
                }
            }
        }
        //Debug.LogError(allSet.Count);
        //Debug.LogError(ToJson(allSet.Keys.ToArray()));
        //Debug.LogError(usedSet.Count);
        //Debug.LogError(ToJson(usedSet.Keys.ToArray()));

        var sett = new HashSet<string>(allSet.Keys);
        sett.ExceptWith(usedSet.Keys);
        Debug.LogError(sett.Count);
        Debug.LogError(ToJson(sett));
        LogHelp.clipboard = ToJson(sett);
        var count = 1;
        foreach (var item in sett)
        {
            var ai = AssetImporter.GetAtPath(item) as TextureImporter;
             if (ai)
            {
                var ti = AssetDatabase.LoadAssetAtPath<Sprite>(item) as Sprite;
                if (ti)
                {
                    //Debug.LogError(ti.name);
                    ai.spritePackingTag = "ui.bigPic" + count + ti.name;
                    count = count + 1;
                    ai.SaveAndReimport();
                }  
            }
        }
        //ImportAssets();
        //LogHelp.Instance.Log("CheckBigPic_end");
    }

    /// <summary>
    /// 检查sprite 的read write enabled 设置
    /// </summary>
    [MenuItem("Assets/UI/CheckSpriteReadWriteEnabled")]
	public static void CheckSpriteReadWriteEnabled(){ 

		var findassets = AssetDatabase.FindAssets ("t:sprite");
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase)||s.EndsWith (".tga", StringComparison.CurrentCultureIgnoreCase);
		List<string> list = new List<string> ();
		for (int i = 0, findassetsLength = findassets.Length; i < findassetsLength; i++) {
			var uid = findassets [i];
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if (isPng (ob)) {
				var ai = AssetImporter.GetAtPath (ob) as TextureImporter;
				if (ai && ai.isReadable) {
					list.Add (ob);
				}
			}
			bool isCancel = EditorUtility.DisplayCancelableProgressBar ("匹配资源中", ob,  (float)i / (float)findassetsLength);
			if(isCancel)
			{
				break;
			}
		} 
		EditorUtility.ClearProgressBar ();
		list.Sort (string.Compare);
		 
		foreach (var f in list) {

			var ai = AssetImporter.GetAtPath (f) as TextureImporter;
			if(ai)
			{
				ai.isReadable = false;
			}
		}
		ImportAssets (list.ToArray ());
		LogHelp.clipboard = ToJson (list);
	}
	/// <summary>
	/// Filters the U i2.筛选选择目录下的图片总数
	/// </summary>
	[MenuItem("Assets/UI/FilterTexList")]
	public static void FilterUI2(){ 
		LogHelp.Instance.Log ("FilterUI_start");
		var checkPath = AssetDatabase.GetAssetPath (Selection.activeObject);

		var allSet = new Dictionary<string,bool> ();
		var usedSet = new Dictionary<string,bool> ();
		var findassets = AssetDatabase.FindAssets ("", new string[]{ checkPath });
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase)||s.EndsWith (".tga", StringComparison.CurrentCultureIgnoreCase);
		Func<string,bool> isFilter = (s) =>{return true;
		};

		foreach (var uid in findassets) {
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if(isPng(ob)){
				allSet [ob] = true;
				continue;
			} 
			if(isFilter(ob)){
				usedSet [ob] = true;
			} 
		} 
		Debug.LogError (allSet.Count); 
		var clip = ToJson (allSet.Keys);
		LogHelp.clipboard = clip;
		Debug.LogError (clip); 
		LogHelp.Instance.Log ("FilterUI_end");

	}

	[MenuItem("Assets/UI/CacheGO2Atals")]
	public static void CacheGO2Atals(){ 
		LogHelp.Instance.Log ("CacheGO2Atals_start");
		var checkPath = AssetDatabase.GetAssetPath (Selection.activeObject);

		var allSet = new Dictionary<string,Dictionary<string,string>> ();
		var findassets = AssetDatabase.FindAssets ("", new string[]{ checkPath });
		Func<string,bool> isGo = (s) => s.EndsWith (".prefab", StringComparison.CurrentCultureIgnoreCase);
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase);
		Func<string,bool> isFilter = (s) =>{return true;
		};

		foreach (var uid in findassets) {
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if(isGo(ob)==false){
				continue;
			} 
			var obAbname = AssetDatabase.GetImplicitAssetBundleName (ob);
			Dictionary<string, string> dic=null;
			var dependences = AssetDatabase.GetDependencies(ob,true);
			foreach (var dep in dependences) {
				if(isPng(dep)){
					var abname = AssetDatabase.GetImplicitAssetBundleName (dep);
					if(abname.Contains ("spritepack"))
					if(string.IsNullOrEmpty (abname)==false){
						if(allSet.ContainsKey (obAbname)==false)
						{
							allSet [obAbname] =  new Dictionary<string,string>(); 
						}
						dic = allSet [obAbname];
						dic [abname] = obAbname;
					}
				}
			}
		} 
		Debug.LogError (allSet.Count); 
		var clip = ToJson (allSet);
		File.WriteAllText (Application.dataPath + "/Resources/go2atlas.json", clip);
		LogHelp.clipboard = clip;
		Debug.LogError (clip); 
		AssetDatabase.Refresh ();
		LogHelp.Instance.Log ("CacheGO2Atals_end");

	}

	/// <summary>
	/// Filters the U.筛选ui文件夹尺寸大于1024的物体
	/// </summary>
	[MenuItem("Assets/UI/FilterUI")]
	public static void FilterUI(){ 
		LogHelp.Instance.Log ("FilterUI_start");
		var checkPath = "Assets/UI";

		var allSet = new Dictionary<string,string> ();
		var usedSet = new Dictionary<string,string> ();
		var findassets = AssetDatabase.FindAssets ("", new string[]{ checkPath });
		Func<string,bool> isPng = (s) => s.EndsWith (".png", StringComparison.CurrentCultureIgnoreCase);
 

		foreach (var uid in findassets) {
			var ob = AssetDatabase.GUIDToAssetPath (uid);
			if(isPng(ob)){  
				allSet[ob]="";
				var ti = AssetDatabase.LoadAssetAtPath<Texture2D> (ob) as Texture2D;
				if(ti==null){
					continue;
				} 
				var b = Mathf.Max (ti.width, ti.height) > 400;
				if(b)
				{
                    var ai = AssetImporter.GetAtPath(ob) as TextureImporter;
					usedSet [ob] = ti.width + "_"+ti.height+"_"+ ai.spritePackingTag; 
				}
				continue;
			} 
		} 
		Debug.LogError (allSet.Count); 
		Debug.LogError (usedSet.Count); 	
		allSet.Clear (); 
		foreach (var ob in usedSet.Keys.ToList ()) {
//			var ob = p.Key;
//			var ob = AssetDatabase.GUIDToAssetPath (uid); 
			var ti = AssetDatabase.LoadAssetAtPath<Texture2D> (ob) as Texture2D;
			if (ti == null) {
				continue;
			} 
			var rate = Mathf.Min (ti.width, ti.height) / (float)Mathf.Max (ti.width, ti.height);
			if (rate < 0.3f) {
                    var ai = AssetImporter.GetAtPath(ob) as TextureImporter;
				allSet [ob] = ti.width + "_" + ti.height +"_"+ ai.spritePackingTag; 
				usedSet.Remove (ob);
			} 
		} 
		Debug.LogError (allSet.Count); 	
		var list = usedSet.ToList ();
		list.Sort (delegate (KeyValuePair<string, string> l1, KeyValuePair<string, string> l2) {
			var ti = AssetDatabase.LoadAssetAtPath<Texture2D> (l1.Key) as Texture2D;
			var ti2 = AssetDatabase.LoadAssetAtPath<Texture2D> (l2.Key) as Texture2D;
			var m1 = Mathf.Max (ti.width, ti.height);
			var m2 = Mathf.Max (ti2.width, ti2.height);
			if (m1 == m2) {
				m1 = Mathf.Min (ti.width, ti.height);
				m2 = Mathf.Min (ti2.width, ti2.height);
			}
			return m2 - m1;
		});
		var clip = ToJson (list);

		list = allSet.ToList ();
		list.Sort (delegate (KeyValuePair<string, string> l1, KeyValuePair<string, string> l2) {
			var ti = AssetDatabase.LoadAssetAtPath<Texture2D> (l1.Key) as Texture2D;
			var ti2 = AssetDatabase.LoadAssetAtPath<Texture2D> (l2.Key) as Texture2D;
			var m1 = Mathf.Max (ti.width, ti.height);
			var m2 = Mathf.Max (ti2.width, ti2.height);
			if (m1 == m2) {
				m1 = Mathf.Min (ti.width, ti.height);
				m2 = Mathf.Min (ti2.width, ti2.height);
			}
			return m2 - m1;
		});
		var clip2 = ToJson (list);
		LogHelp.clipboard = clip + "\n\n" + clip2;
		Debug.LogError (clip); 
		LogHelp.Instance.Log ("FilterUI_end");
 
	}

	/// <summary>
	/// 重置大尺寸UI的导入配置（改为独立的ab,避免合并到图集）
	/// </summary>
	[MenuItem("Assets/UI/ResetBigPicPackingTagAndAbName")]
	public static void ResetBigTexturePackingTagAndAbName()
	{
        foreach (var obj in Selection.objects)
        {
			string path = AssetDatabase.GetAssetPath (obj);
			TextureImporter assetImporter = AssetImporter.GetAtPath (path) as TextureImporter;

			// 添加NonPacktag 标签
			List<string> lables = new List<string>();
			lables.AddRange(AssetDatabase.GetLabels(assetImporter));
			if (lables.IndexOf("NonPacktag") == -1)
			{
				lables.Add("NonPacktag");
			}
			AssetDatabase.SetLabels(assetImporter, lables.ToArray());
			//清空spritePackingTag
			assetImporter.spritePackingTag = "";

			//设置assetBundleName为path
			string assetBundleName = path.Substring(path.IndexOf("/") + 1);
			assetImporter.assetBundleName = assetBundleName;
			LogHelp.clipboard = assetBundleName.ToLower();
		}
    }

    /// <summary>
    /// 获取预制体依赖的资源
    /// </summary>
    [MenuItem("Assets/UI/GetPrefabeDependencies")]
    public static void GetPrefabeDependencies()
    {
        foreach (var obj in Selection.objects)
        {
            string path = AssetDatabase.GetAssetPath(obj);
			GetPrefabeDependenciesBy(path);
        }
    }

    public static void GetPrefabeDependenciesBy(string path)
    {
#if UNITY_EDITOR
        var go = AssetDatabase.LoadMainAssetAtPath(path) as GameObject;
        if (go == null)
        {
            return;
        }

        var dic = new Dictionary<string, List<string>>();
        List<string> atlasItems;
        var lImgs = go.GetComponentsInChildren(typeof(UnityEngine.UI.Image), true);
        string atlasName = "";
        foreach (var img in lImgs)
        {
            var spri = (img as UnityEngine.UI.Image).sprite;
            var spr = "empty";
            if (spri == null)
            {
                continue;
            }

            spr = AssetDatabase.GetAssetPath(spri);
            string itemPath = ToolUti.CopyPathTo(img.transform, img.transform.root);
            var ai = AssetImporter.GetAtPath(spr) as TextureImporter;

            if (ai)
            {
                atlasName = ai.textureType == TextureImporterType.Sprite ? ai.spritePackingTag : spr;
            }

            dic.TryGetValue(atlasName, out atlasItems);
            if (atlasItems == null)
            {
                //Debug.Log("创建图集数组:" + atlasName);
                dic[atlasName] = new List<string>();
                atlasItems = dic[atlasName];
            }
            atlasItems.Add(itemPath);
        }
        string outPutString = "";
        int atlasCount = 0;
        foreach (var item in dic.Keys)
        {
            List<string> list = dic[item];
            if (string.IsNullOrEmpty(item) == false)
            {
                atlasCount++;
                outPutString = outPutString + "index:" + atlasCount + " 依赖图集:" + item + "\n列表项:\n" + string.Join("\n", list.ToArray()) + "\n\n";
            }
        }

        int rawImageCount = 0;
        var rawImgs = go.GetComponentsInChildren(typeof(UnityEngine.UI.RawImage), true);
        foreach (var img in rawImgs)
        {
            var spri = (img as UnityEngine.UI.RawImage).texture;
            var spr = "empty";
            if (spri == null)
            {
                continue;
            }

            spr = AssetDatabase.GetAssetPath(spri);
            string itemPath = ToolUti.CopyPathTo(img.transform, img.transform.root);

            rawImageCount++;
            outPutString += "index:" + rawImageCount + " 依赖RawImage:" + spr + " 预制结构路径:" + itemPath + "\n";
        }

        outPutString = "资源依赖检测：" + path + " 引用图集数：" + atlasCount + " 引用贴图数：" + rawImageCount + "\n" + outPutString;
        Debug.Log(outPutString);
#endif
    }


    public static string ToJson(object o, Formatting f = Formatting.Indented)
    {
        return Newtonsoft.Json.JsonConvert.SerializeObject(o, f);
    }
    public static T ToObj<T>(string o)
    {
        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(o);
    }

	static int GetClipIntValue (string key,int configMaxSize)
	{
		try {
			var dic = ToObj<Dictionary<string, string>> (LogHelp.clipboard);
			if (dic != null) {
				//			var key = "maxsize";
				if (dic.ContainsKey (key)) {
					var vstr = dic [key];
					int v = configMaxSize;
					if (int.TryParse (vstr, out v)) {
						return v;
					}
					return configMaxSize;
				}
			} 
		} catch (Exception ex) {
			
		}
		return configMaxSize;
	}

	/// <summary>
	/// Fixs the size of the tex.对已选物体判断大于1024的图片处理,按比例缩小到最大尺寸为960
	/// {"maxsize":"512","resize":"512","checkmax":"0"}
	/// </summary>
	[UnityEditor.MenuItem("Assets/UI/FixTexSize960")]
	static void FixTexSize960()
	{
        var configMaxSize = 1000;
        var reSize = 960;
  //      var configMaxSize = 1024;
		//var reSize = 1024; 
		var checkmax = 1; 
		configMaxSize = GetClipIntValue ("maxsize",configMaxSize);
		reSize = GetClipIntValue ("resize",reSize);
		checkmax = GetClipIntValue ("checkmax",checkmax);

		Debug.LogError (configMaxSize);
		Debug.LogError (reSize); 
		UnityEngine.Object[] sels = UnityEditor.Selection.GetFiltered (typeof(UnityEngine.Object), SelectionMode.DeepAssets);
		//		var sels = Selection.objects;
		var texCache = new Dictionary<string, Texture2D> ();
		foreach (var sel in sels) {
			if(sel is Texture2D)
			{
				var t2d = sel as Texture2D;
				var checkSize = (checkmax==1)?Mathf.Max (t2d.width, t2d.height):Mathf.Min (t2d.width,t2d.height);
				var bShould = checkSize > configMaxSize;
				if(bShould)
				{
					var rate = (reSize /(float) checkSize);
					var wid = (int)(t2d.width * rate); 
					var hei = (int)(t2d.height * rate); 

					var p = AssetDatabase.GetAssetPath (t2d);
					var ip = AssetImporter.GetAtPath (p) as TextureImporter;
					if(wid!=t2d.width || hei!=t2d.height)
					{
						string key = string.Format ("{0}_{1}",wid, hei);
						Texture2D tTex = null;
						if (texCache.ContainsKey (key) && texCache [key] != null) {
							tTex = texCache [key];
						}
						else {
							tTex = new Texture2D (wid, hei, TextureFormat.RGBA32, false);
							texCache [key] = tTex;
						}

						EditorHelp.ApplyUnReadTex (t2d, tTex);

						var bs = p.ToLower ().Contains (".jpg") ? tTex.EncodeToJPG () : tTex.EncodeToPNG ();
						File.WriteAllBytes (p, bs);
						ip.SaveAndReimport ();
					} 
				}
			}
		}
	}
	[UnityEditor.MenuItem("Assets/UI/FixTexSizeX4")]
	static void FixTexSizeX4()
	{
		var alpha0 = new Color (0, 0, 0, 0);
		UnityEngine.Object[] sels = UnityEditor.Selection.GetFiltered (typeof(UnityEngine.Object), SelectionMode.DeepAssets);
		//		var sels = Selection.objects;
		foreach (var sel in sels) {
			if(sel is Texture2D)
			{
				var t2d = sel as Texture2D;
				var maxSize = Mathf.Max (t2d.width, t2d.height);
 
				var wid = (int)(Mathf.RoundToInt (t2d.width /4)*4); 
				var hei = (int)(Mathf.RoundToInt (t2d.height /4)*4);

				var p = AssetDatabase.GetAssetPath (t2d);
				var ip = AssetImporter.GetAtPath (p) as TextureImporter;
				if(wid!=t2d.width || hei!=t2d.height)
				{
					Texture2D tTex = null;
					tTex = TexCache.Instance.CacheTex (wid, hei); 

					EditorHelp.ApplyUnReadTex (t2d, tTex);

					var bs = p.ToLower ().Contains (".jpg") ? tTex.EncodeToJPG () : tTex.EncodeToPNG ();
					File.WriteAllBytes (p, bs);
					ip.SaveAndReimport ();
				}  
			}
		}
	}
	[UnityEditor.MenuItem("Assets/UI/ios_rgb16")]
	static void ios_rgb16()
	{ 
		var sels = Selection.GetFiltered<UnityEngine.Object> (SelectionMode.DeepAssets);
		Dictionary<string,bool> dic = new Dictionary<string, bool> ();
		foreach (var sel in sels) {
			var p = AssetDatabase.GetAssetPath (sel);
			var sps = AssetDatabase.LoadAllAssetRepresentationsAtPath (p);
			foreach (var sp in sps) {
				if(sp is Sprite)
				{
					iOS_Config.Instance.Add (p);
					dic [p] = true;
					break;
				}
			}
		} 
		ImportAssets (dic.Keys.ToArray ()); 

		AssetDatabase.Refresh ();
	}
	[UnityEditor.MenuItem("Assets/UI/remove_ios_rgb16")]
	static void remove_ios_rgb16()
	{ 
		var sels = Selection.GetFiltered<UnityEngine.Object> (SelectionMode.DeepAssets);
		Dictionary<string,bool> dic = new Dictionary<string, bool> ();
		foreach (var sel in sels) {
			var p = AssetDatabase.GetAssetPath (sel);
			var sps = AssetDatabase.LoadAllAssetRepresentationsAtPath (p);
			foreach (var sp in sps) {
				if(sp is Sprite)
				{
					iOS_Config.Instance.Remove (p);
					dic [p] = true;
					break;
				}
			}
		} 
		ImportAssets (dic.Keys.ToArray ()); 
		AssetDatabase.Refresh ();
	}
	/// <summary>
	/// Locals the atlas tex.缓存打包好的图集到本地文件夹atlas
	/// </summary>
	[UnityEditor.MenuItem("Assets/UI/local_atlas_tex")]
	public static void local_atlas_tex()
	{
#if !UNITY_2022_3_OR_NEWER
		LogHelp.Instance.Log ("local_atlas_tex:start");
		Packer.RebuildAtlasCacheIfNeeded (EditorUserBuildSettings.activeBuildTarget, true, Packer.Execution.Normal);
		LogHelp.Instance.Log ("local_atlas_tex:Packer");
#endif
		var texCache = new Dictionary<string, Texture2D> ();
		var atlas = Packer.atlasNames;
		foreach (var item in atlas) {
			var texs = Packer.GetTexturesForAtlas (item);
			for (int i = 0; i < texs.Length; i++) {
				var t = texs [i];
				if(t==null){
					continue;
				}
				Texture2D tTex = null;
				var wid = t.width;
				var hei = t.height;
				string key = string.Format ("{0}_{1}", wid, hei);
				if (texCache.ContainsKey (key) && texCache [key] != null) {
					tTex = texCache [key];
				}
				else {
					tTex = new Texture2D (wid, hei, TextureFormat.RGBA32, false);
					texCache [key] = tTex;
				}
				EditorHelp.ApplyUnReadTex (t, tTex);
				var p = string.Format ("Atlas/{0}_{1}.png", t.name,i);
				EditorHelp.CheckDir (p);
				var bs = tTex.EncodeToPNG ();
				File.WriteAllBytes (p, bs);
				LogHelp.Instance.Log ("local_atlas_tex:" + p);
			}
			texs = Packer.GetAlphaTexturesForAtlas (item);
			for (int i = 0; i < texs.Length; i++) {
				var t = texs [i];
				if(t==null){
					continue;
				}
				Texture2D tTex = null;
				var wid = t.width;
				var hei = t.height;
				string key = string.Format ("{0}_{1}", wid, hei);
				if (texCache.ContainsKey (key) && texCache [key] != null) {
					tTex = texCache [key];
				}
				else {
					tTex = new Texture2D (wid, hei, TextureFormat.RGBA32, false);
					texCache [key] = tTex;
				}
				EditorHelp.ApplyUnReadTex (t, tTex);
				var p = string.Format ("Atlas/{0}_{1}.png", t.name,i);
				EditorHelp.CheckDir (p);
				var bs = tTex.EncodeToPNG ();
				File.WriteAllBytes (p, bs);
				LogHelp.Instance.Log ("local_atlas_tex:" + p);
			}
		}
		LogHelp.Instance.Log ("local_atlas_tex:end");
		AssetDatabase.Refresh ();
	}
	[MenuItem("GameObject/Help/CopyPath",false,1)]
	static void CopyPath()
    {
        var sel = Selection.activeGameObject;
        var t = sel.transform;
        string str = CopyPath(t);
        LogHelp.clipboard = str;
        Debug.LogError(str);
    }

    public static string CopyPath(Transform t)
    {
        var str = "";
        var count = 20000;
        do
        {
            count--;
            if (count < 0) break;
            str = t.name + "/" + str;
            t = t.parent;
        } while (t != null);
        str = str.TrimEnd('/');
        return str;
    }

    [MenuItem("GameObject/Help/auto_lua",false,1)]
	static void UI_auto(){ 
		var sels= Selection.gameObjects;
		foreach (var sel in sels) {
			var t = sel.transform;
			if(t.name.StartsWith ("Auto_")==false){
				t.name = "Auto_" + t.name;
			} 
		}
	}
	[MenuItem("GameObject/Help/FixSeq",false,1)]
	static void FixSeq(){ 
		var sels= Selection.gameObjects;
		foreach (var sel in sels) {
			Undo.RecordObject (sel, sel.GetHashCode ().ToString ());
			var t = sel.transform;

			var aName = t.name;
			aName = aName ?? "";
			aName = aName.Trim(' ','(',')');
			aName = aName.Replace (" ", "");
			aName = aName.Replace ("(", "");
			aName = aName.Replace (")", "");

			var sibIndex = t.GetSiblingIndex ().ToString ();
			if(aName.EndsWith (sibIndex)==false)
				{
				aName = aName + sibIndex;
				}
			t.name = aName;
		}
    }
    [MenuItem("GameObject/Help/SetOrder", false, 1)]
    static void SetOrder()
    {
        var sels = Selection.gameObjects;
        foreach (var sel in sels)
        {
            Undo.RecordObject(sel, sel.GetHashCode().ToString());
            var t = sel.transform;

            var renderers = t.GetComponentsInChildren<MeshRenderer>();
            foreach (var renderer in renderers)
            {
                if (renderer != null)
                {
                    renderer.sortingOrder = -10;
                }
            }
        }
    }
    [MenuItem("Assets/UI/copyabname", false, 1)]
    static void copyabname()
    {
        var sel = Selection.activeObject;
        var abname = AssetDatabase.GetImplicitAssetBundleName(AssetDatabase.GetAssetPath(sel));
        LogHelp.clipboard = abname;
    }
    [MenuItem("Assets/Ani/AllStar", false, 1)]
    static void AllStar()
    {
        var obs = AssetDatabase.FindAssets("t:Prefab", new string[] { "Assets/Animations/Characters" }); 

        var root = GameObject.Find("/AllStar") ?? new GameObject("AllStar");

        for (int i = 0; i < root.transform.childCount; i++)
        {
            GameObject.DestroyImmediate(root.transform.GetChild(0).gameObject);
        }
        Debug.Log(obs.Length);

        var rList = RandomList(obs.Length,12);
        int startIndex = 0; 
        for (int i = 0; i < 6; i++)
        {
            var ind = rList[startIndex];
            string gid = obs[ind];
            var path = AssetDatabase.GUIDToAssetPath(gid);
            if (path.Contains("edit_")) continue;
            var go = PrefabUtility.InstantiatePrefab(AssetDatabase.LoadAssetAtPath<GameObject>(path)) as GameObject;
            go.transform.SetParent(root.transform);
            go.transform.position = new Vector3(startIndex / 3 * 12, startIndex % 3 * 12, 0);
             
            startIndex++;  
        }
    }
    public static List<int> RandomList(int length,int size, int start = 0, List<int> list = null)
    {
        //		List<int> list = new List<int>();
        if (list == null)
        {
            list = new List<int>();
        }
        else
        {
            list.Clear();
        }

        for (int i = start; i < start + length; i++)
        {
            list.Add(i);
        }
        int index = Mathf.Min(size,length);
        int rand;
        int cur;
        while (--index > 1)
        {
            rand = UnityEngine.Random.Range(0, index + 1);
            cur = list[rand];
            list[rand] = list[index];
            list[index] = cur;
        }
        return list;
    }
    [MenuItem("Assets/Ani/FilterChTexSize", false, 1)]
    static void FilterSize()
    {

        var obs = AssetDatabase.FindAssets("t:Texture2D", new string[] { "Assets/Animations/Characters" });
        var list = new List<string>();
        foreach (var gid in obs)
        {
            var path = AssetDatabase.GUIDToAssetPath(gid);
            var ob = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ob)
            {
                var msize = Mathf.Min(ob.maxTextureSize, 1024);
                if (msize != ob.maxTextureSize)
                {
                    ob.maxTextureSize = msize;
                    list.Add(path);
                }
                TextureImporterPlatformSettings textureImporterPlatformSettings = ob.GetPlatformTextureSettings("Android");
                if (textureImporterPlatformSettings.overridden)
                {
                    Debug.Log(path);
                }
                //textureImporterPlatformSettings.overridden = true;
                //    textureImporterPlatformSettings.allowsAlphaSplitting = true;
                //    textureImporterPlatformSettings.format = desirFormat;
                //    textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings); 

            }
        }

        ImportAssets(list.ToArray());
        Debug.Log(ToJson(list));
    }

    public static void ModifyDither (Texture2D texture)
	{
		var texw = texture.width;
		var texh = texture.height;
		var pixels = texture.GetPixels ();
		var offs = 0;
		var k1Per15 = 1.0f / 15.0f;
		var k1Per16 = 1.0f / 16.0f;
		var k3Per16 = 3.0f / 16.0f;
		var k5Per16 = 5.0f / 16.0f;
		var k7Per16 = 7.0f / 16.0f;
		for (var y = 0; y < texh; y++) {
			for (var x = 0; x < texw; x++) {
				float a = pixels [offs].a;
				float r = pixels [offs].r;
				float g = pixels [offs].g;
				float b = pixels [offs].b;
				var a2 = Mathf.Clamp01 (Mathf.Floor (a * 16) * k1Per15);
				var r2 = Mathf.Clamp01 (Mathf.Floor (r * 16) * k1Per15);
				var g2 = Mathf.Clamp01 (Mathf.Floor (g * 16) * k1Per15);
				var b2 = Mathf.Clamp01 (Mathf.Floor (b * 16) * k1Per15);
				var ae = a - a2;
				var re = r - r2;
				var ge = g - g2;
				var be = b - b2;
				pixels [offs].a = a2;
				pixels [offs].r = r2;
				pixels [offs].g = g2;
				pixels [offs].b = b2;
				var n1 = offs + 1;
				var n2 = offs + texw - 1;
				var n3 = offs + texw;
				var n4 = offs + texw + 1;
				if (x < texw - 1) {
					pixels [n1].a += ae * k7Per16;
					pixels [n1].r += re * k7Per16;
					pixels [n1].g += ge * k7Per16;
					pixels [n1].b += be * k7Per16;
				}
				if (y < texh - 1) {
					pixels [n3].a += ae * k5Per16;
					pixels [n3].r += re * k5Per16;
					pixels [n3].g += ge * k5Per16;
					pixels [n3].b += be * k5Per16;
					if (x > 0) {
						pixels [n2].a += ae * k3Per16;
						pixels [n2].r += re * k3Per16;
						pixels [n2].g += ge * k3Per16;
						pixels [n2].b += be * k3Per16;
					}
					if (x < texw - 1) {
						pixels [n4].a += ae * k1Per16;
						pixels [n4].r += re * k1Per16;
						pixels [n4].g += ge * k1Per16;
						pixels [n4].b += be * k1Per16;
					}
				}
				offs++;
			}
		}
		texture.SetPixels (pixels);
//		EditorUtility.CompressTexture (texture, TextureFormat.RGBA4444, TextureCompressionQuality.Best);
	}


	public static void ImportAssets(params string[] paths)
	{
		// When using the cache server we have to write all import settings to disk first.
		// Then perform the import (Otherwise the cache server will not be used for the import)
		foreach (string path in paths)
			AssetDatabase.WriteImportSettingsIfDirty(path);

		try
		{
			AssetDatabase.StartAssetEditing();
			foreach (string path in paths)
				AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
		}
		finally
		{
			AssetDatabase.StopAssetEditing();
		}
	}

    //for extractScriptTool
    public static void ImportAssetsFast(List<string> paths)
    {
        try
        {
            //System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            //sw.Start();
            AssetDatabase.StartAssetEditing();
            foreach (string path in paths)
            {
                // 只有当设置“脏”且已被更新时才需要强制更新导入
                if (AssetDatabase.IsOpenForEdit(path) && AssetDatabase.WriteImportSettingsIfDirty(path))
                {
                    AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
                }
            }
            //sw.Stop();
            //Debug.Log($"ImportAssetsFast_use_time:{sw.Elapsed.TotalSeconds}");
        }
        finally
        {
            AssetDatabase.StopAssetEditing();
        }
    }


    static DateTime lastDate;
    [MenuItem("GameObject/DiveInto", false, 0)]
    public static void Envelop()
    {
        var date = System.DateTime.Now;
        if ((date - lastDate).TotalSeconds < 2)
        {
            return;
        }
        lastDate = date;
        var sels = Selection.gameObjects;
        var selss = new List<GameObject>(sels);
        Debug.LogError(selss.Count);
        for (int i = 0, selssCount = selss.Count; i < selssCount; i++)
        {
            var sel = selss[i];
            var parent = new GameObject(sel.name);
            parent.transform.SetParent(sel.transform.parent, false);
            parent.transform.localPosition = sel.transform.localPosition;
            parent.transform.localRotation = sel.transform.localRotation;
            parent.transform.localScale = sel.transform.localScale;
            sel.transform.SetParent(parent.transform, true);
        }
    }

    [MenuItem("Assets/ClearNullOfAssets", false, 0)]
    public static void ClearNullOfAssets()
    {
        var obj = Selection.activeObject;
        if (obj != null)
        {
            List<Sprite> m_SpriteList = (obj as SpriteAssets).m_SpriteList;
            for (int i = m_SpriteList.Count - 1; i >= 0 ; i--)
            {
                if (m_SpriteList[i] == null)
                {
                    m_SpriteList.Remove(m_SpriteList[i]);
                }
            }
            EditorUtility.SetDirty(obj);
            string checkPath = AssetDatabase.GetAssetPath(Selection.activeObject);
            ImportAssets(new string[] { checkPath });
        }
        else
        {
            Debug.LogError("No SpriteAssets Exists");
        }
    }
}
public class iOS_Config{
	static iOS_Config instance;

	public static iOS_Config Instance {
		get {
			if(instance==null)
			{
				instance = new iOS_Config ();
			}
			return instance;
		}
	}
	string cfgPath = Application.dataPath + "/UI/iOS16.txt";
	Dictionary<string,string> cache;
	public Dictionary<string, string> data_rgb16 {
		get {
			if(cache==null)
			{
				if(File.Exists (cfgPath)==false){
					File.WriteAllText (cfgPath,"{}");
				}
				var cfgText = File.ReadAllText (cfgPath);
				cache = ToObj<Dictionary<string,string>> (cfgText); 
			}
			return cache;
		}
	} 
	public void Reload(){
		cache = null;
	}
	public void Add(params string[] args){
		cache = data_rgb16;
		if (cache==null) {
			cache = new Dictionary<string, string> ();
		}
		foreach (var item in args) {
			cache[item]="";
		}
		File.WriteAllText (cfgPath, ToJson (cache));
	}

	public void Remove(params string[] args){
		cache = data_rgb16;
		if (cache==null) {
			cache = new Dictionary<string, string> ();
		}
		foreach (var item in args) {
			cache.Remove (item);
		}
		File.WriteAllText (cfgPath, ToJson (cache));
	}
	public static string ToJson(object o)
	{
		return Newtonsoft.Json.JsonConvert.SerializeObject (o,Formatting.Indented);
	}
	public static T ToObj<T>(string o)
	{
		return Newtonsoft.Json.JsonConvert.DeserializeObject<T> (o);
	}
}

public class TexCache{
	static TexCache instance;

	public static TexCache Instance {
		get {
			if(instance==null)
			{
				instance = new TexCache ();
			}
			return instance;
		}
	}
	Dictionary<string, Texture2D> texCache = new Dictionary<string, Texture2D> ();

	public Texture2D CacheTex(int wid,int hei)
	{

		string key = string.Format ("{0}_{1}", wid, hei);
		Texture2D tTex = null;
		if (texCache.ContainsKey (key) && texCache [key] != null) {
			tTex = texCache [key];
		}
		else {
			tTex = new Texture2D (wid, hei, TextureFormat.RGBA32, false);
			texCache [key] = tTex;
		}
		return tTex;
	}
	public void Clear()
	{
		texCache.Clear ();
		EditorUtility.UnloadUnusedAssetsImmediate ();
		GC.Collect ();
	}
}