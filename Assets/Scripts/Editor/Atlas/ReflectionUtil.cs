using System;
using System.Reflection;

public static class ReflectionUtil
{
    public static object CallStatic<T>(string methodName, params object[] args)
    {
        return typeof(T).GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Static).Invoke(null, args);
    }

    public static object CallStatic(Type t, string methodName, params object[] args)
    {
        var method = t.GetMethod(methodName, BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static);
        return method.Invoke(null, args);
    }
}