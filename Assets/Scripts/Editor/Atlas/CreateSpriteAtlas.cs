using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text;
using UnityEditor;
using UnityEditor.Sprites;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;

public class CreateSpriteAtlas
{

    private static string sptDesDir = Application.dataPath + "/Resources";
    private static string sptSrcDir = Application.dataPath + "/UI";
    public static string ui_atlas_root = "Assets/UI/0SpriteAtlas";

    //[MenuItem("Assets/SpriteAtlas/NewAtlasMaker By Folders")]

    //public static void CreateAtlasByFolders()
    //{
    //    string sDir = sptSrcDir;
    //    CreateAtlasFolders(sDir);

    //    //add texture by your self
    //}

    //private static void CreateAtlasFolders(string sDir)
    //{
    //    DirectoryInfo rootDirInfo = new DirectoryInfo(sDir);
    //    //add folders
    //    List<Object> folders = new List<Object>();
    //    foreach (DirectoryInfo dirInfo in rootDirInfo.GetDirectories())
    //    {
    //        folders.Clear();
    //        if (dirInfo != null)
    //        {
    //            string assetPath = dirInfo.FullName.Substring(dirInfo.FullName.IndexOf("Assets"));
    //            var o = AssetDatabase.LoadAssetAtPath<DefaultAsset>(assetPath);
    //            if (IsPackable(o))
    //                folders.Add(o);
    //        }

    //        string atlasPath = GetAtlasPath(Path.GetFileNameWithoutExtension(dirInfo.Name));
    //        CreateAtlas(atlasPath);
    //        SpriteAtlas sptAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
    //        Debug.Log(sptAtlas.tag);
    //        AddPackAtlas(sptAtlas, folders.ToArray());
    //    }
    //}
     
    public static string GetAtlasPath(string tag)
    {
            string atlasName = string.Format("{0}/{1}.spriteatlas", ui_atlas_root,tag) ;
        return atlasName;
    }

    [MenuItem("Assets/UI/RemoveSpriteTag")]
    public static void RemoveSpriteTag()
    {
        var sels = Selection.objects;
        foreach (var sel in sels)
        {
            if (sel is Texture2D)
            {
                var ai = AssetImporter.GetAtPath(AssetDatabase.GetAssetPath(sel));
                if (ai)
                {
                    var aai = ai as TextureImporter;
                    aai.spritePackingTag = "";
                    aai.SaveAndReimport();
                }
            }
        }
        AssetDatabase.Refresh();
    }

    [MenuItem("Assets/SpriteAtlas/DealUIFolderAtlasBySpriteTag")]
    public static void CreateAtlasBySpriteTag()
    {
        var ui_root = "Assets/UI";
        CreateAtlasWholeDir(ui_root);
    }
    [MenuItem("Assets/SpriteAtlas/CreateAtlasWithSelection")]
    public static void CreateAtlasWithSelection()
    {
        var sel = Selection.activeObject;
        var ui_root = AssetDatabase.GetAssetPath(sel);
        if(ProjectWindowUtil.IsFolder(sel.GetInstanceID()))
        {
            CreateAtlasWholeDir(ui_root);
        }
        else
        {
            ui_root = Path.GetDirectoryName(ui_root).Replace("\\", "/");
            var sels = Selection.objects; 
            var listguids = new List<string>();
            foreach (var s in sels)
            {
                string apath = AssetDatabase.GetAssetPath(s);
                //if (apath.StartsWith(ui_root) == false) continue;
                var g = AssetDatabase.AssetPathToGUID(apath);
                listguids.Add(g);
            }
            "".Print("Add:", UIHelper.ToJson(listguids));

            CreateAtlasWithSprites( listguids.ToArray());
        }
    }
    /// <summary>
    /// 对文件夹sprite操作，以spritetag为图集
    /// </summary>
    /// <param name="ui_root"></param>
    private static void CreateAtlasWholeDir(string ui_root)
    {
        var guids = AssetDatabase.FindAssets("t:Sprite", new string[] { ui_root });
        CreateAtlasWithSprites( guids);
    }

    private static void CreateAtlasWithSprites( string[] guids)
    {
        var dic = new Dictionary<string, string>();
        var i = 0;
        ToolUti.IterUpdateFinish(guids, (guid) =>
        {
            //i++;if (i > 10) return guid;
            var path = AssetDatabase.GUIDToAssetPath(guid);
            var ai = AssetImporter.GetAtPath(path) as TextureImporter;
            if (ai == null) return path;

            var tag = ai.spritePackingTag;
            if (string.IsNullOrEmpty(tag) == false)
            {
                dic[path] = tag;
            }

            return path;
        }, () =>
        {
            var dictags = new Dictionary<string, List<string>>();
            List<string> list = null;
            System.Action<string, string> addtag = (s, tag) =>
            {
                if (dictags.TryGetValue(tag, out list) == false)
                {
                    dictags[tag] = new List<string>();
                    list = dictags[tag];
                }
                list.Add(s);
            };
            foreach (var k in dic.Keys)
            {
                var tag = dic[k];
                addtag(k, tag);
            }
            ToolUti.IterUpdateFinish(new List<string>(dictags.Keys).ToArray(), (tag) =>
            {
                List<string> list1 = dictags[tag];
                if(File.Exists(GetAtlasPath(tag))==false)
                if (list1.Count < 2) return tag;
                CreateAtlasBySprites( tag, list1);
                return tag;
            }, () =>
            {

            }, "CreateAtlasBySprites");


        }, "CreateAtlasBySpriteTag", 1000);
    }

    public static SpriteAtlas CreateAtlasBySprites(string tag,List<string> list,bool resetatlas = false)
    {
       var filePath = CreateAtlas(GetAtlasPath(tag), resetatlas);

        List<Sprite> spts = new List<Sprite>();
        foreach (var assetPath in list)
        {
            Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath);
            if (IsPackable(sprite))
                spts.Add(sprite);
        }

        SpriteAtlas sptAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(filePath);
        var format = PlatformSettingCfg.Texture_IOS_Default_ImporterFormat;
        string platformString = "iPhone";
        TextureImporterPlatformSettings textureImporterPlatformSettings = sptAtlas.GetPlatformSettings(platformString);
        if (!textureImporterPlatformSettings.overridden
                || textureImporterPlatformSettings.format != format
            )
        {
            textureImporterPlatformSettings.overridden = true;
            textureImporterPlatformSettings.format = format;

            sptAtlas.SetPlatformSettings(textureImporterPlatformSettings);
        }
        Debug.Log(sptAtlas.tag);
        AddPackAtlas(sptAtlas, spts.ToArray());
        return sptAtlas;
    }

    [MenuItem("Assets/SpriteAtlas/DealUIFolderAtlasByFolder")]
    public static void CreateAtlasAllUI()
    {
        string sDir = sptSrcDir;
        CreateAtlasByFolder(sDir);
        //add texture by your self
    }

    [MenuItem("Assets/SpriteAtlas/CreateAtlas4Sprites")]
    private static void CreateAtlas4Sprites()
    {
        var sels = Selection.objects;
        var selsps = ArrayUtility.FindAll(sels, (s) => s is Texture2D);
        var sel = Selection.activeObject;
        if (!sel) return;
        var ap = AssetDatabase.GetAssetPath(sel);
        var dir = Path.GetDirectoryName(ap);
        var tag = "0art/" + Path.GetFileNameWithoutExtension(dir).ToLower();
        var atlas = CreateAtlasBySprites(tag, selsps.ConvertAll((s) => { return AssetDatabase.GetAssetPath(s); }), true);
        ToolUti.SaveSpriteAtlasTexture2d(atlas);
    }
    [MenuItem("Assets/SpriteAtlas/SaveSpriteAtlasTexture2d")]
    private static void SaveSpriteAtlasTexture2d()
    { 
        var atlas = Selection.activeObject as SpriteAtlas;
        if (!atlas) return; 
        ToolUti.SaveSpriteAtlasTexture2d(atlas);


    }

    private static void CreateAtlasByFolder(string sDir)
    {
        DirectoryInfo rootDirInfo = new DirectoryInfo(sDir);

        //add sprite

        List<Sprite> spts = new List<Sprite>();
        foreach (DirectoryInfo dirInfo in rootDirInfo.GetDirectories())
        {
            spts.Clear();
            foreach (FileInfo pngFile in dirInfo.GetFiles("*.png", SearchOption.AllDirectories))
            {
                string allPath = pngFile.FullName;
                string assetPath = allPath.Substring(allPath.IndexOf("Assets"));
                Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath);
                if (IsPackable(sprite))
                    spts.Add(sprite);
            }
            string atlasPath = GetAtlasPath(Path.GetFileNameWithoutExtension(dirInfo.Name));
            CreateAtlas(atlasPath);
            SpriteAtlas sptAtlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
            //SpriteAtlas sptAtlas = Resources.Load<SpriteAtlas>(dirInfo.Name);
            Debug.Log(sptAtlas.tag);
            AddPackAtlas(sptAtlas, spts.ToArray());
        }
    }

    static bool IsPackable(Object o)
    {
        return o != null && (o.GetType() == typeof(Sprite) || o.GetType() == typeof(Texture2D) || (o.GetType() == typeof(DefaultAsset) && ProjectWindowUtil.IsFolder(o.GetInstanceID())));
    }

    static void AddPackAtlas(SpriteAtlas atlas, Object[] spt)
    {
        //MethodInfo methodInfo = System.Type
        //     .GetType("UnityEditor.U2D.SpriteAtlasExtensions, UnityEditor")
        //     .GetMethod("Add", BindingFlags.Public | BindingFlags.Static);
        //if (methodInfo != null)
        //    methodInfo.Invoke(null, new object[] { atlas, spt });
        //else
        //    Debug.Log("methodInfo is null");

        SpriteAtlasExtensions.Add(atlas, spt);

        //PackAtlas(atlas);
    }

    static void PackAtlas(SpriteAtlas atlas)
    {
        SpriteAtlasUtility.PackAtlases(new[] { atlas }, EditorUserBuildSettings.activeBuildTarget);
    }



    public static string CreateAtlas(string  atlasPath,bool reset = false)
    {
        string yaml = @"%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!687078895 &4343727234628468602
SpriteAtlas:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: New Sprite Atlas
  m_EditorData:
    textureSettings:
      serializedVersion: 2
      anisoLevel: 1
      compressionQuality: 50
      maxTextureSize: 2048
      textureCompression: 1
      filterMode: 1
      generateMipMaps: 0
      readable: 0
      crunchedCompression: 0
      sRGB: 1
    platformSettings: []
    packingParameters:
      serializedVersion: 2
      padding: 4
      blockOffset: 1
      allowAlphaSplitting: 0
      enableRotation: 1
      enableTightPacking: 0
    variantMultiplier: 1
    packables: []
    bindAsDefault: 1
  m_MasterAtlas: {fileID: 0}
  m_PackedSprites: []
  m_PackedSpriteNamesToIndex: []
  m_Tag: New Sprite Atlas
  m_IsVariant: 0
";
        //AssetDatabase.Refresh();



        if (File.Exists(atlasPath))
        {
            if(reset)
            {
                File.Delete(atlasPath);
                //AssetDatabase.Refresh();
            }
            else
            {
                "".Print("CreateAtlas", atlasPath, "exist");
                return atlasPath;
            }
        }

        ToolUti.CheckDir(atlasPath);
        FileStream fs = new FileStream(atlasPath, FileMode.OpenOrCreate);
        byte[] bytes = new UTF8Encoding().GetBytes(yaml);
        fs.Write(bytes, 0, bytes.Length);
        fs.Close();
        AssetDatabase.Refresh();
        return atlasPath;
    }

} 
