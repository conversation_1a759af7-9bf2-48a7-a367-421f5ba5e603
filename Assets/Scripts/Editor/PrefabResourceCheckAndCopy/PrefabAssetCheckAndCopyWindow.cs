using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using UnityEditor.Animations;
using Object = UnityEngine.Object;

namespace PrefabResourceCheckAndCopy
{
    class PrefabAssetCheckAndCopyWindow:EditorWindow{
        private static Rect windowRect = new Rect(0, 0, 480, 550);
        private static string NAME_windowTitle="prefab导出";
        
        private static GameObject currSelectObj;
        private static GameObject oldObj;
        private static string curAssetPath;
        private static string newPath;
        private static string newMeshPath;
        private static string newmaterialPath;
        private static string newTexturePath;
        private static string newAnimationPath;
        private static string oldPath;
        private static string prefabName;
        private static string prefabFullName;
        private static string newParentPath;
        private static string firstPath;
        private static string newAssetParentPath;
        private static Dictionary<string,AssetGuidSt> AssetGuidStDic;
        
        //
        // List<Mesh> meshes ;
        // List<Material> materials ;
        // List<Texture> textures ;
        // List<AnimationClip> animations ;

        enum PrefabResType
        {
            material,
            mesh,
            anim,
            tex
        }
  
        [MenuItem("Assets/Copy prefab and Check/配置数据数据",false,101)]
        // Use this for initialization
        public static  void Init()
        {
            PrefabAssetCheckAndCopyWindow window = (PrefabAssetCheckAndCopyWindow)EditorWindow.GetWindowWithRect<PrefabAssetCheckAndCopyWindow>(windowRect);
            window.titleContent = new GUIContent(NAME_windowTitle);
            window.Show();
        }

        [MenuItem("Assets/Copy prefab and Check/直接复制资源", false, 102)]
        // Use this for initialization
        public static  void CopyAssets()
        {
            CheckPath();
            GameObject[] arr = Selection.gameObjects;
            int cou = arr.Length;
            for (int i = 0; i < cou; i++)
            {
                currSelectObj = arr[i];
                GetPathByPrefab();
                StartSavePrefab(currSelectObj);
            }
        }

        private  void OnEnable()
        {
            if (EditorPrefs.HasKey("_prefabCopyPath"))
            {
                newPath = EditorPrefs.GetString( "_prefabCopyPath");
                oldPath = newPath;
            }

            AssetGuidStDic=new Dictionary<string, AssetGuidSt>();
            // meshes = new List<Mesh>();
            // materials = new List<Material>();
            // textures = new List<Texture>();
            // animations = new List<AnimationClip>();
            
            // var lst = Selection.assetGUIDs;
            currSelectObj = Selection.activeGameObject;
            
            if (currSelectObj != null)
            {
                oldObj = currSelectObj;
                
                GetPathByPrefab();
                
                if (string.IsNullOrEmpty(curAssetPath))
                {
                    Debug.LogError($"没有找到当前的资源在unity的路径，检查资源是否在asset中，path={currSelectObj.name}");
                }
            }
        }

        private  void OnDisable()
        {
            if (!string.IsNullOrEmpty(newPath))
            {
                EditorPrefs.SetString("_prefabCopyPath",newPath);
            }
        }

        static void GetPathByPrefab()
        {
            curAssetPath = AssetDatabase.GetAssetPath(currSelectObj);
            if (!string.IsNullOrEmpty(curAssetPath))
            {
                int startIndex = curAssetPath.LastIndexOf("/") + 1;
                int endIndex = curAssetPath.LastIndexOf(".")-1;
                prefabFullName = curAssetPath.Substring(startIndex);
                prefabName = curAssetPath.Substring(startIndex,endIndex-startIndex+1);
                curAssetPath = curAssetPath.Substring(0, curAssetPath.LastIndexOf("/") - 1);
                
                if (!string.IsNullOrEmpty(newPath))
                {
                    newParentPath = newPath + "/" +prefabName;
                    newAssetParentPath= GetAssetPath(newParentPath);
                    newmaterialPath = newAssetParentPath + "/Material";
                    newAnimationPath = newAssetParentPath + "/Animation";
                    newMeshPath = newAssetParentPath + "/Mesh";
                    newTexturePath = newAssetParentPath + "/Texture";
                    int indexx = newParentPath.IndexOf("Asset");
                    firstPath = newParentPath.Substring(0, indexx - 1);
                }
            }
        }

         void OnGUI()
        {
            {
                EditorGUILayout.LabelField("需要赋值的资源：",GUILayout.Width(100),GUILayout.Height(30));
                currSelectObj = EditorGUILayout.ObjectField("资源：",currSelectObj,typeof(GameObject) ,true,GUILayout.Width(300), GUILayout.Height(30)) as GameObject;
                if (oldObj != currSelectObj)
                {
                    oldObj = currSelectObj;
                    GetPathByPrefab();
                }
            }
            
            {//新的路径
                EditorGUILayout.LabelField("保存文件的路径：",GUILayout.Width(100),GUILayout.Height(30));
                GUILayout.BeginHorizontal();
                if (GUILayout.Button("...", GUILayout.Width(100),GUILayout.Height(30)))
                {
                    newPath = EditorUtility.OpenFolderPanel("", "", "");
                    if (!string.Equals(oldPath, newPath))
                    {
                        oldPath = newPath;
                        newParentPath = newPath + "/" + prefabName;
                    }
                }
                EditorGUILayout.LabelField(newPath, GUILayout.Width(300),GUILayout.Height(30));
                GUILayout.EndHorizontal();
            }
            if (GUILayout.Button("保存prefab", GUILayout.Width(100),GUILayout.Height(30)))
            {
                CheckPath();
                StartSavePrefab(currSelectObj);
            }
        }

         static void CheckPath()
         {
             //string path= Application.dataPath;
             if (string.IsNullOrEmpty(newPath))
             {
                 if (EditorPrefs.HasKey("_prefabCopyPath"))
                 {
                     newPath = EditorPrefs.GetString( "_prefabCopyPath");

                     if (newPath.IndexOf(Application.dataPath) == -1)
                     {
                         newPath = Application.dataPath;
                         Debug.Log($"之前保存的路径和当前的项目不一致，采用当前项目={newPath}");
                     }
                     
                     oldPath = newPath;
                 }
                 else
                 {
                     newPath = Application.dataPath;
                 }
             }
         }

        static void StartSavePrefab(GameObject obj)
        {
            if (AssetGuidStDic == null)
            {
                AssetGuidStDic=new Dictionary<string, AssetGuidSt>();
            }
            AssetGuidStDic.Clear();
            CreateNewResPath();//创建所有新资源的目录文件夹
            SavePrefab(obj);
            CheckMaterial();//材质单独处理，
            ReplacePrefabUseAssetsGUID();//替换prefab的所有引用的asset
            SaveFile();
            AssetDatabase.Refresh();
            HighLightDirectory();
        }

        static void SaveFile()
        {
            string str = "";
            foreach (var value in AssetGuidStDic)
            {
                str += "Before:"+value.Value.assetPath + "\n";
                str += "After:"+value.Value.newAssetPath + "\n";
            }
            
            File.WriteAllText(newParentPath+"/assetPathFile.txt",str);
        }

        static void CheckMaterial()
        {
            string newResPath = GetAssetSavePath(currSelectObj.GetType());
            string assetPath = AssetDatabase.GetAssetPath(currSelectObj);
            string name = assetPath.Substring(assetPath.LastIndexOf("/") + 1);
            string newAssetPath = newResPath + "/" + name;

            var obj = AssetDatabase.LoadAssetAtPath<GameObject>(newAssetPath);

            var lst = obj.GetComponentsInChildren<SkinnedMeshRenderer>();
            for (int i = 0; i < lst.Length; i++)
            {
                var mats = lst[i].sharedMaterials;
                lst[i].sharedMaterials=RefreshMaterialsInMesgRender(mats.ToList());
            }
            
            var meshLst = obj.GetComponentsInChildren<MeshRenderer>();
            for (int i = 0; i < meshLst.Length; i++)
            {
                var mats = meshLst[i].sharedMaterials;
                meshLst[i].sharedMaterials=RefreshMaterialsInMesgRender(mats.ToList());
            }

            PrefabUtility.SavePrefabAsset(obj);
        }

        static Material[] RefreshMaterialsInMesgRender(List<Material> mats)
        {           
            List<Material>matLst=new List<Material>();
            for (int j = 0; j < mats.Count; j++)
            {
                string path = AssetDatabase.GetAssetPath(mats[j]);
                string guid = AssetDatabase.AssetPathToGUID(path);
                AssetGuidSt data;
                bool hasValue= AssetGuidStDic.TryGetValue(guid,out data);
                if (hasValue)
                {
                    var mat = AssetDatabase.LoadAssetAtPath<Material>(data.newAssetPath);
                    matLst.Add(mat);
                }
            }

            return matLst.ToArray();  
        }

        static void HighLightDirectory()
        {
            Object obj = AssetDatabase.LoadAssetAtPath<Object>(GetAssetPath(newParentPath));
            EditorGUIUtility.PingObject(obj);
        }

        static void SavePrefab(Object obj)
        {
            if (obj == null)
            {
                return;
            }
            
            string path1 = AssetDatabase.GetAssetPath(obj);
           
            if (path1.IndexOf(".")==-1 || path1.IndexOf("Assets")==-1)
            {
                Debug.Log($"默认资源={obj.name},不进行复制");
                return;
            }
            
            string guid = AssetDatabase.AssetPathToGUID(path1);
            if (AssetGuidStDic.ContainsKey(guid))
            {
                return;
            }

            CollectAllDependenceRes(obj);//搜集所有依赖资源

            //复制新资源
            string newResPath = GetAssetSavePath(obj.GetType());
            string assetPath = AssetDatabase.GetAssetPath(obj);
            string name = assetPath.Substring(assetPath.LastIndexOf("/") + 1);
            string newAssetPath = newResPath + "/" + name;

            CopyAssets(path1,newAssetPath);
            
            if ( name.IndexOf(".mat") != -1|| name.IndexOf(".controller") != -1)
            {
                //替换新的prefab引用资源的guid
                ReplaceGUID(firstPath+"/"+newAssetPath);
            }
        }

        static void ReplacePrefabUseAssetsGUID()
        {
            string newResPath = GetAssetSavePath(currSelectObj.GetType());
            string assetPath = AssetDatabase.GetAssetPath(currSelectObj);
            string name = assetPath.Substring(assetPath.LastIndexOf("/") + 1);
            string newAssetPath = newResPath + "/" + name;
            if (name.IndexOf(".prefab") != -1)
            {
                //替换新的prefab引用资源的guid
                ReplaceGUID(firstPath+"/"+newAssetPath);
            }
        }

        static void CollectAllDependenceRes(Object obj)
        {
            Object[] objects = EditorUtility.CollectDependencies(new Object[]{obj});
            if (objects.Length <= 1)
            {
                return;
            }
            
            //var lsst = AssetDatabase.GetDependencies(AssetDatabase.GetAssetPath(currSelectObj),false);

            foreach (var value in objects)
            {
                if (value == obj)
                {
                    continue;
                }
                
                Type type = value.GetType();

                
                if (type== typeof(Texture) || type==typeof(Texture2D))
                {
                    SavePrefab(value);
                }else if (type== typeof(Material))
                {
                    SavePrefab(value);
                }else if (type== typeof(Mesh))
                {
                    SavePrefab(value);
                }else if (type== typeof(AnimatorController)||type== typeof(Animation)||type== typeof(AnimationClip))
                {
                    SavePrefab(value);
                }
            }
        }

        static void CreateNewResPath()
        {
            CreateFolder(newPath ,prefabName);
            CreateFolder(newParentPath,"Mesh");
            CreateFolder(newParentPath,"Material");
            CreateFolder(newParentPath,"Animation");
            CreateFolder(newParentPath,"Texture");
        }

        static void CreateFolder(string parentPath,string folderName)
        {
            if (!Directory.Exists(parentPath+"/"+folderName))
            {
                AssetDatabase.CreateFolder(GetAssetPath(parentPath), folderName);
            }
        }

        static void CopyAssets(string assetPath,string newAssetsPath)
        {
            //CheckAssetPath(assetPath);
            Debug.Log($"提示：复制的资源路径={newAssetsPath}");
            AssetDatabase.CopyAsset(assetPath, newAssetsPath);
            CreateGUIDStruce(assetPath, newAssetsPath);
        }
        
        static void CreateGUIDStruce(string assetPath,string newAssetPath)
        {
            string guid = AssetDatabase.AssetPathToGUID(assetPath);
            string newGuid = AssetDatabase.AssetPathToGUID(newAssetPath);
            if (AssetGuidStDic.ContainsKey(guid))
            {
                var assetGuidSt = AssetGuidStDic[guid];
                assetGuidSt.newAssetGuid = newGuid;
                assetGuidSt.newAssetPath = newAssetPath;
            }
            else
            {
                AssetGuidStDic[guid]=new AssetGuidSt()
                {
                    assetGuid = guid,
                    assetPath = assetPath,
                    newAssetGuid = newGuid,
                    newAssetPath= newAssetPath,
                };
            }
        }

        static string  CreateFilePath(string fDirectory,PrefabResType type)
        {
            if (!Directory.Exists(fDirectory))
            {
                Debug.LogError($"保存资源的文件夹不存在，检查下，path={fDirectory}");

                return "";
            }
            
            string newDirectory=GetResDirectory(fDirectory,type);

            return newDirectory;
        }

        static string GetResDirectory(string fDirectory,PrefabResType type)
        {
            switch (type)
            {
                case PrefabResType.anim:
                    return fDirectory + "/" + "Animation";
                case PrefabResType.material:
                    return fDirectory + "/" + "Material";
                case PrefabResType.mesh:
                    return fDirectory + "/" + "Mesh";
                case PrefabResType.tex:
                    return fDirectory + "/" + "Texture2D";
                default:
                    return fDirectory;
            }
        }

        static void ReplaceGUID(string filePath)
        {
            string fileContent = File.ReadAllText(filePath);

            foreach (var value in AssetGuidStDic)
            {
                if (fileContent.IndexOf("guid: " + value.Value.assetGuid) != -1)
                {
                    fileContent= fileContent.Replace("guid: " + value.Value.assetGuid, "guid: " + value.Value.newAssetGuid);
                }
            }
            
            File.WriteAllText(filePath,fileContent);
        }

        static string GetAssetSavePath(Type type)
        {
            if (type == typeof(Mesh))
            {
                return newMeshPath;
            }else if (type == typeof(Animation)||type==typeof(AnimatorController)||type==typeof(AnimationClip))
            {
                return newAnimationPath;
            }else if (type == typeof(Material))
            {
                return newmaterialPath;
            }else if (type == typeof(Texture)||type == typeof(Texture2D))
            {
                return newTexturePath;
            }

            return newAssetParentPath;
        }
        static string  GetAssetPath(string path)
        {
            int index = path.IndexOf("Assets");
            if (index != -1)
            {
                return  path.Substring(index);
            }

            return "";
        }
        
        private  List<string> GetGuids(string text)
        {
            const string guidStart = "guid: ";
            const int guidLength = 32;
            int textLength = text.Length;
            int guidStartLength = guidStart.Length;
            List<string> guids = new List<string>();
 
            int index = 0;
            while (index + guidStartLength + guidLength < textLength)
            {
                index = text.IndexOf(guidStart, index, StringComparison.Ordinal);
                if (index == -1)
                    break;
 
                index += guidStartLength;
                string guid = text.Substring(index, guidLength);
                index += guidLength;
 
                if (IsGuid(guid)&&!guids.Contains(guid))
                {
                    guids.Add(guid);
                }
            }
            return guids;
        }
        private  bool IsGuid(string text)
        {
            for (int i = 0; i < text.Length; i++)
            {
                char c = text[i];
                if (
                    !((c >= '0' && c <= '9') ||
                      (c >= 'a' && c <= 'z'))
                )
                    return false;
            }
 
            return true;
        }

 
    }

    struct AssetGuidSt
    {
        public string assetPath;
        public string assetGuid;
        public string newAssetPath;
        public string newAssetGuid;
    }
}