/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：StyleTextTools
 *  作者：wjy
 *  日期：2022-03-02 02:24
 *  功能：
*****************************************************/


#if UNITY_EDITOR
using System;
using UnityEditor;
using UnityEngine;

public class StyleTextTools
{
    [MenuItem("GameObject/样式设置/StyleTextBodyMain", false, 1)]
    static void StyleTextBodyMain()
    {
        SetSelectGameObjects<StyleTextBodyMain>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonBlueBig", false, 1)]
    static void StyleTextButtonBlueBig()
    {
        SetSelectGameObjects<StyleTextButtonBlueBig>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonBlueSmall", false, 1)]
    static void StyleTextButtonBlueSmall()
    {
        SetSelectGameObjects<StyleTextButtonBlueSmall>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonGreenBig", false, 1)]
    static void StyleTextButtonGreenBig()
    {
        SetSelectGameObjects<StyleTextButtonGreenBig>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonGreenSmall", false, 1)]
    static void StyleTextButtonGreenSmall()
    {
        SetSelectGameObjects<StyleTextButtonGreenSmall>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonGreyBig", false, 1)]
    static void StyleTextButtonGreyBig()
    {
        SetSelectGameObjects<StyleTextButtonGreyBig>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonGreySmall", false, 1)]
    static void StyleTextButtonGreySmall()
    {
        SetSelectGameObjects<StyleTextButtonGreySmall>();
    }

    [MenuItem("GameObject/样式设置/StyleTextButtonOrganeBig", false, 1)]
    static void StyleTextButtonOrganeBig()
    {
        SetSelectGameObjects<StyleTextButtonOrganeBig>();
    }

    [MenuItem("GameObject/样式设置/StyleTextModuleTitle", false, 1)]
    static void StyleTextModuleTitle()
    {
        SetSelectGameObjects<StyleTextModuleTitle>();
    }

    [MenuItem("GameObject/样式设置/StyleTextProp1", false, 1)]
    static void StyleTextProp1()
    {
        SetSelectGameObjects<StyleTextProp1>();
    }

    [MenuItem("GameObject/样式设置/StyleTextProp2", false, 1)]
    static void StyleTextProp2()
    {
        SetSelectGameObjects<StyleTextProp2>();
    }

    [MenuItem("GameObject/样式设置/StyleTextProp3", false, 1)]
    static void StyleTextProp3()
    {
        SetSelectGameObjects<StyleTextProp3>();
    }

    [MenuItem("GameObject/样式设置/StyleTextProp4", false, 1)]
    static void StyleTextProp4()
    {
        SetSelectGameObjects<StyleTextProp4>();
    }

    [MenuItem("GameObject/样式设置/StyleTextTipOnBlack", false, 1)]
    static void StyleTextTipOnBlack()
    {
        SetSelectGameObjects<StyleTextTipOnBlack>();
    }
    [MenuItem("GameObject/样式设置/StyleTextToggleOff", false, 500)]
    static void StyleTextToggleOff()
    {
        SetSelectGameObjects<StyleTextToggleOff>();
    }
    [MenuItem("GameObject/样式设置/StyleTextToggleOn", false, 500)]
    static void StyleTextToggleOn()
    {
        SetSelectGameObjects<StyleTextToggleOn>();
    }
    
    [MenuItem("GameObject/样式设置/StyleTextToggle1Off", false, 500)]
    static void StyleTextToggle1Off()
    {
        SetSelectGameObjects<StyleTextToggle1Off>();
    }
    [MenuItem("GameObject/样式设置/StyleTextToggle1On", false, 500)]
    static void StyleTextToggle1On()
    {
        SetSelectGameObjects<StyleTextToggle1On>();
    }


    static void SetSelectGameObjects<T>() where T : StyleTextBase
    {
        foreach (var gameObject in Selection.gameObjects)
        {
            SetStyleForGameobject<T>(gameObject);
        }
    }


    static void SetStyleForGameobject<T>(GameObject go) where T : StyleTextBase
    {
        var style = go.AddComponent<T>();
        try
        {
            style.Reset();
            style.SetupFormat();
        }
        catch (Exception e)
        {
            Debug.Log("样式格式化出问题");
        }

        GameObject.DestroyImmediate(style);
    }
}
#endif