/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：TextsVerify
 *  作者：wjy
 *  日期：2022-03-02 11:02
 *  功能：排查工具
*****************************************************/

#if UNITY_EDITOR
using System;
using TMPro;
using UnityEditor;
using UnityEditorInternal;
using UnityEngine;
using UnityEngine.UI;
using War.UI;
using Object = UnityEngine.Object;

public class UISkinTools
{
    [MenuItem("GameObject/排查工具/挑出所有text", false, 1)]
    public static void VerfyAll()
    {
        Text[] texts = Selection.activeGameObject.GetComponentsInChildren<Text>(true);
        foreach (var text in texts)
        {
            Debug.Log(text.font.name, text);
        }
    }

    [MenuItem("GameObject/排查工具/挑出所有未被设置过的text", false, 1)]
    public static void PickOldTexts()
    {
        Text[] texts = Selection.activeGameObject.GetComponentsInChildren<Text>(true);
        foreach (var text in texts)
        {
            if (!text.font.name.Contains("FZY4JW") && !text.font.name.Contains("ClashRoyale"))
            {
                Debug.Log(text.font.name, text);
            }
        }
    }


    [MenuItem("GameObject/排查工具/挑出所有特效组件", false, 1)]
    public static void PickAllEffects()
    {
        ParticleSystem[] pss = Selection.activeGameObject.GetComponentsInChildren<ParticleSystem>(true);
        foreach (var ps in pss)
        {
            Debug.Log(ps.name, ps);
        }
    }

    [MenuItem("GameObject/转换工具/Text2TMP（UGUI）", false, 1)]
    public static void Text2TMPGUI()
    {
        foreach (var gameObject in Selection.gameObjects)
        {
            if (gameObject.name.Contains("_old_text_origin"))
            {
                continue;
            }

            //获取原来组件的信息
            var text = gameObject.GetComponent<Text>();


            if (text == null)
            {
                Debug.LogWarning("该组件查找不到Text，将不做任何操作", gameObject);
                continue;
            }

            var textstring = text.text;
            string tmpFontName = text.font.name + "_SDF";

            float getWidth()
            {
                if (tmpFontName.ToLower().StartsWith("clash"))
                {
                    return 0.4f;
                }

                return 0.287f;
            }

            bool wrapping = text.horizontalOverflow == HorizontalWrapMode.Wrap;

            // Vector2 sizedata = text.rectTransform.sizeDelta;
            int fontSize = text.fontSize;
            bool isBestFit = text.resizeTextForBestFit;
            var maxFitSize = text.resizeTextMaxSize;
            var minFitSize = text.resizeTextMinSize;
            bool isRaycast = text.raycastTarget;


            var alimentAnchord = text.alignment;
            var textcolor = text.color;
            var outLineColor = Color.white;
            var outlineDistance = Vector2.zero;
            bool getOutlineColor = false;
            var outlineC = gameObject.GetComponent<Outline>();
            if (outlineC != null)
            {
                getOutlineColor = true;
                outLineColor = outlineC.effectColor;
                outlineDistance = outlineC.effectDistance;
            }

            
            Undo.RegisterCompleteObjectUndo(gameObject,"old_text");

            //曲线救国，克隆另外一个预设体来避免 b通道渲染异常
            GameObject rectGo =
                AssetDatabase.LoadAssetAtPath<GameObject>("Assets/UI/0TemplatePrefab/TMPTextUGUI.prefab");
            GameObject tmpNewGo = GameObject.Instantiate(rectGo, gameObject.transform.position,
                gameObject.transform.rotation, gameObject.transform.parent);
            Undo.RegisterCreatedObjectUndo(tmpNewGo,"Text2TMPGUI");
            tmpNewGo.name = gameObject.name;
            tmpNewGo.transform.SetSiblingIndex(gameObject.transform.GetSiblingIndex());
            gameObject.name = gameObject.name + "_old_text_origin";

            //还原text的基础信息
            var tmpgui = tmpNewGo.GetComponent<TextMeshProUGUI>();
            tmpgui.text = textstring;
            tmpgui.fontSize = fontSize;
            tmpgui.raycastTarget = isRaycast;
            tmpgui.font = AssetDatabase.LoadAssetAtPath<TMP_FontAsset>($"Assets/UI/Fonts/{tmpFontName}.asset");
            tmpgui.UpdateFontAsset();
            tmpgui.color = textcolor;
            tmpgui.enableWordWrapping = wrapping;
            tmpgui.alignment = TextAnchorToTextAlignmentOptions(alimentAnchord);
            ComponentUtility.CopyComponent(gameObject.transform);
            ComponentUtility.PasteComponentValues(tmpNewGo.transform);
            // tmpgui.rectTransform.sizeDelta = sizedata;
            if (isBestFit)
            {
                tmpgui.autoSizeTextContainer = isBestFit;
                tmpgui.fontSizeMax = maxFitSize;
                tmpgui.fontSizeMin = minFitSize;
            }

            //还原outline 信息 ps: curOutlineWidth 上限是1
            if (getOutlineColor)
            {
                ///tmpgui.EnableOutLine = true;
               /// tmpgui.curOutlineColor = outLineColor;
               /// tmpgui.curOutlineWidth = outlineDistance.x * Mathf.Clamp01(getWidth());
            }
            else
            {
                Debug.LogWarning("获取不到outline颜色");
            }


            //同步渐变
            var graCom = gameObject.GetComponent<War.UI.Gradient>();

            bool hadGradient = graCom != null;
            Debug.Log(hadGradient);

            tmpgui.enableVertexGradient = hadGradient;

            if (hadGradient)
            {
                tmpgui.color = Color.white;
                ///tmpgui.SetGradientMode(ColorMode.VerticalGradient);
               /// tmpgui.SetGradientVerticesColor(graCom.TopColor, graCom.BottomColor);
            }


            //同步gameobject上的其他组件
            CopyOtherComponents(gameObject, tmpNewGo);
            //还原组件的transform
            MoveAllChild(gameObject.transform, tmpNewGo.transform);
            Undo.SetTransformParent(gameObject.transform, tmpNewGo.transform, "Text2TMPGUI Set Parent");
            // gameObject.transform.SetParent(tmpNewGo.transform);
            gameObject.transform.SetAsLastSibling();
            gameObject.SetActive(false);
        }
    }

    public static void CopyOtherComponents(GameObject src, GameObject dst)
    {
        var orginList = src.GetComponents<Component>();

        foreach (var component in orginList)
        {
            var typeName = component.GetType().Name.ToLower();

            if (typeName == "outline"
                || typeName == "text"
                || typeName == "shadow"
                || typeName == "transform"
                || typeName == "recttransform"
                || typeName == "canvasrenderer"
                || typeName == "outline"
                || typeName == "gradient")
            {
                continue;
            }

            var desCom = dst.GetComponent(component.GetType());

            if (desCom == null)
            {
                try
                {
                    UnityEditorInternal.ComponentUtility.CopyComponent(component);
                    UnityEditorInternal.ComponentUtility.PasteComponentAsNew(dst);
                }
                catch (Exception e)
                {
                    Debug.LogError($"复制源组件{desCom.GetType().Name}到{dst.name}上失败，errormsg={e.ToString()}",
                        dst);
                }
            }
            else
            {
                Debug.LogWarning($"改组件已经存在type={typeName}", dst);
            }

            Debug.LogWarning(typeName);
        }
    }


    public static void MoveAllChild(Transform src, Transform dst)
    {
        for (int i = src.childCount - 1; i >= 0; i--)
        {
            src.GetChild(i).SetParent(dst);
        }
    }

    static TextAlignmentOptions TextAnchorToTextAlignmentOptions(TextAnchor textAnchor)
    {
        switch (textAnchor)
        {
            case TextAnchor.UpperLeft:
                return TextAlignmentOptions.TopLeft;

            case TextAnchor.UpperCenter:
                return TextAlignmentOptions.Top;

            case TextAnchor.UpperRight:
                return TextAlignmentOptions.TopRight;

            case TextAnchor.MiddleLeft:
                return TextAlignmentOptions.Left;

            case TextAnchor.MiddleCenter:
                return TextAlignmentOptions.Center;

            case TextAnchor.MiddleRight:
                return TextAlignmentOptions.Right;

            case TextAnchor.LowerLeft:
                return TextAlignmentOptions.BottomLeft;

            case TextAnchor.LowerCenter:
                return TextAlignmentOptions.Bottom;

            case TextAnchor.LowerRight:
                return TextAlignmentOptions.BottomRight;
        }

        Debug.LogWarning("Unhandled text anchor " + textAnchor);
        return TextAlignmentOptions.TopLeft;
    }


    [MenuItem("GameObject/排查工具/挑出tmpcurwidth==1", false, 1)]
    public static void Verfytmpcurwidth()
    {
        TextMeshProUGUI[] texts = Selection.activeGameObject.GetComponentsInChildren<TextMeshProUGUI>(true);
        foreach (var text in texts)
        {
            ///if (text.curOutlineWidth > 0.8)
            ///{
           ///     Debug.Log(text.font.name, text);
           // }
        }
    }
}
#endif