/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：UISkinToolForLanhu
 *  作者：wjy
 *  日期：2022-03-05 04:02
 *  功能：蓝湖右键工具，一键设置text
*****************************************************/

#if UNITY_EDITOR
using System;
using TMPro;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;


public class UISkinToolForLanhu
{
    [MenuItem("GameObject/蓝湖/UpdateText", false, 1)]
    public static void UpdateText()
    {
        ReadCopyBoard(out int fontSize, out string fontName, out string color, out int distance,
            out string outlineColor);
        foreach (var gameObject in Selection.gameObjects)
        {
            try
            {
                //设置text
                Text textComponent = gameObject.GetComponent<Text>();
                if (textComponent == null)
                {
                    Debug.LogWarning($"选择的gameobject不带{typeof(Text).Assembly.Location}脚本，不适合此方法");
                    continue;
                }

                Text text = gameObject.GetComponent<Text>();
                text.fontStyle = FontStyle.Normal;
                text.fontSize = fontSize;
                if (text.resizeTextForBestFit)
                {
                    text.resizeTextMaxSize = fontSize;
                }

                bool isClashFont = fontName.ToLower().Contains("clash");
                string path =
                    isClashFont ? "Assets/UI/Fonts/ClashRoyale.ttf" : "Assets/UI/Fonts/FZY4JW.TTF";
                text.font = AssetDatabase.LoadAssetAtPath<Font>(path);

                if (ColorUtility.TryParseHtmlString(color, out var tarColor))
                {
                    text.color = tarColor;
                }

                Outline outline = gameObject.GetComponent<Outline>();

                if (!string.IsNullOrEmpty(outlineColor))
                {
                    if (outline == null)
                    {
                        outline = gameObject.AddComponent<Outline>();
                    }

                    if (ColorUtility.TryParseHtmlString(outlineColor, out var outLineColor))
                    {
                        outline.effectColor = outLineColor;
                        outline.enabled = true;
 
                        if (distance > 0)
                        {
                            outline.effectDistance = Vector2.one * distance;
                        }
                        else
                        {
                            Debug.LogWarning("distance 解析失败，请注意");
                        }
                    }
                    else
                    {
                        Debug.LogWarning($"outline 颜色解析失败或没有");
                    }
                }
                else
                {
                    if (outline != null)
                    {
                        outline.enabled = false;
                    }
                }


                War.UI.Gradient gradient = gameObject.GetComponent<War.UI.Gradient>();
                if (gradient != null)
                {
                    gradient.enabled = false;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"设置蓝湖text中途有异常，error={e}");
            }
        }
    }

    [MenuItem("GameObject/蓝湖/UpdateTextsWithOutLineAlpha62", false, 1)]
    public static void UpdateTextsWithOutLineAlpha62()
    {
        UpdateText();
        foreach (var gameObject in Selection.gameObjects)
        {
            Outline outline = gameObject.GetComponent<Outline>();

            if (outline != null)
            {
                var col = outline.effectColor;
                col.a = 62f / 255f;
                outline.effectColor = col;
            }
        }
    }


    public static void ReadCopyBoard(out int fontSize, out string fontName, out string color, out int distance,
        out string outlineColor)
    {
        string buffer = GUIUtility.systemCopyBuffer;
        Debug.Log($"systemCopyBuffer==\n{buffer}");
        ParserForLanHu.TryMathTextSize(buffer, out fontSize);
        ParserForLanHu.TryMathFontName(buffer, out fontName);
        ParserForLanHu.TryMathTextColor(buffer, out color);
        ParserForLanHu.TryMathOutLine(buffer, out distance, out outlineColor);
        Debug.Log(
            $"result:\n  size={fontSize}\nfontName={fontName}\ncolor={color}\ndistance={distance}\noutlineColor={outlineColor}");
    }

    //
    // [MenuItem("GameObject/蓝湖/Change2TMP", false, 1)]
    // public static void Change2TMP()
    // {
    //     foreach (var gameObject in Selection.gameObjects)
    //     {
    //         // var origin = GameObject.Instantiate(gameObject, gameObject.transform.parent);
    //
    //         // gameOb
    //         // TestLocal local = gameObject.GetComponent<TestLocal>();
    //         // int id = local.localID;
    //         // GameObject.DestroyImmediate(local);
    //         //
    //         // Text text = gameObject.GetComponent<Text>();
    //         // GameObject go = new GameObject("originText");
    //         // go.transform.SetParent(gameObject.transform);
    //         // var newText = CopyComponent(text, go);
    //         // GameObject.DestroyImmediate(text);
    //         //
    //         //
    //         // var tmp = gameObject.AddComponent<TextMeshProUGUI>();
    //     }
    // }


    // static T CopyComponent<T>(T original, GameObject destination) where T : Component
    // {
    //     Type type = original.GetType();
    //     Component copy = destination.AddComponent(type);
    //     System.Reflection.FieldInfo[] fields = type.GetFields();
    //     foreach (System.Reflection.FieldInfo field in fields)
    //     {
    //         field.SetValue(copy, field.GetValue(original));
    //     }
    //
    //     return copy as T;
    // }
    
    
}
#endif