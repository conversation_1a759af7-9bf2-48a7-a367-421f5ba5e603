/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：ParserForLanHu
 *  作者：wjy
 *  日期：2022-03-05 04:26
 *  功能：蓝湖字符串的解析
*****************************************************/

using System.Text.RegularExpressions;
using UnityEngine;

#if UNITY_EDITOR

public class ParserForLanHu
{
    public static bool TryMathTextSize(string origin, out int fontSize)
    {
        var result = GetReadTargetString(origin, @"font-size:(.*)px;", 1);
        int.TryParse(result, out fontSize);
        return fontSize > 0;
    }

    public static bool TryMathFontName(string origin, out string fontName)
    {
        fontName = GetReadTargetString(origin, @"font-family:(.*);", 1);
        return !string.IsNullOrEmpty(fontName);
    }

    public static bool TryMathTextColor(string origin, out string result)
    {
        result = GetReadTargetString(origin, @"color:(.*);", 1, true);
        return !string.IsNullOrEmpty(result);
    }

    public static bool TryMathOutLine(string origin, out int outLineDistance, out string outLineColor)
    {
        Regex pp = new Regex("text-stroke:(.*)px(.*);",
            RegexOptions.IgnoreCase | RegexOptions.Multiline | RegexOptions.IgnorePatternWhitespace);
        var result = pp.Match(origin);
        outLineDistance = 0;
        outLineColor = string.Empty;
        if (result.Length > 2)
        {
            int.TryParse(result.Groups[1].Value.Trim(), out outLineDistance);
            outLineColor = result.Groups[2].Value.Trim();
            return true;
        }

        return false;
    }

    static void DebugGroups(Match match)
    {
        for (int i = 0; i < match.Groups.Count; i++)
        {
            Debug.Log($"i={i},value={match.Groups[i]}");
        }
    }

    public static string GetReadTargetString(string origin, string pattern, int targetIndex, bool logResult = false)
    {
        Regex regex = new Regex(pattern,
            RegexOptions.IgnoreCase | RegexOptions.Multiline | RegexOptions.IgnorePatternWhitespace);
        var result = regex.Match(origin);
        if (logResult)
        {
            DebugGroups(result);
        }

        if (targetIndex > 0 && result.Length > targetIndex)
        {
            return result.Groups[targetIndex].Value.Trim();
        }

        Debug.Log(
            $"解析失败，origin={origin},pattern=={pattern},targetIndex={targetIndex},result group count={result.Groups.Count}");
        return string.Empty;
    }
    

}
#endif