using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Sirenix.OdinInspector.Demos;

public class ParticleStatisticWindow : EditorWindow
{
    public class ParticleInfo
    {
        public string assetPath;
        public GameObject instance;
        public double duration;
        public double start_time;
        public double end_time;
        public float max_triangles;
        public float max_vertices;
        public ParticleSystem[] particleSet;
        public Animation[] animationSet;
        //同屏最大的粒子激活数量
        public int maxActivePSCount;
        //同屏最大的粒子数量
        public int maxPSCount;

        public int drawcall;

        //贴图规范信息警告
        public List<string> warningTextureLogs;

        //单个粒子系统引用贴图数量警告
        public List<string> warningMaterialsLogs;
    }

    Rect pathRect;
    string prefabPath;
    //过滤string
    string filtersStr;
    //是否开启过滤
    private bool isOpenFilters;
    private Stack<string> checkAssetList;
    int totalCount;
    readonly Dictionary<string, ParticleInfo> particleInfoSet = new Dictionary<string, ParticleInfo>();
    ParticleInfo currentParticleInfo;
    private ExportCSV exportCSV = new ExportCSV();
    float maxSimulatTime = 20;
    StreamWriter logWriter;
    int uiLayout = -1;
    Transform uiRootTransform;
    Camera sceneCamera;
    private List<ExportCSV.PatientStatisticsOutputDto> saveDeleteImgPTList = new List<ExportCSV.PatientStatisticsOutputDto>();

    [MenuItem("Tools/Statistics/Particle Stattistics", false, 50)]
    public static void Open()
    {
        EditorWindow.GetWindow<ParticleStatisticWindow>("Particle Statistic");
    }

    void OnDisable()
    {
        EditorApplication.update -= OnUpdate;
    }

    private void OnDestroy()
    {
        Resources.UnloadUnusedAssets();
    }

    bool bTestParticles = false;
    int testMaxCount = 20;
    int currTestIdx = 0;
    void OnUpdate()
    {
        if (currentParticleInfo != null && currentParticleInfo.duration > currentParticleInfo.end_time)
        {
            //Debug.Log(currentParticleInfo.assetPath + " max triangles:" + currentParticleInfo.max_triangles);
            if (logWriter != null)
            {
                logWriter.WriteLine($"{currentParticleInfo.assetPath} ,max triangles: {currentParticleInfo.max_triangles}, max vertices:{currentParticleInfo.max_vertices}");
            }

            GameObject.DestroyImmediate(currentParticleInfo.instance);
            currentParticleInfo.instance = null;
            currentParticleInfo = null;
            Resources.UnloadUnusedAssets();

            if (checkAssetList.Count == 0)
            {
                PrintParticleInfo();
                EditorUtility.ClearProgressBar();
                if (logWriter != null)
                {
                    logWriter.Dispose();
                    logWriter = null;
                }
            }

            System.GC.Collect();

            // 延迟一帧，释放资源
            return;
        }

        if (checkAssetList.Count > 0 && currentParticleInfo == null)
        {
            var guid = checkAssetList.Pop();
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);
            GameObject instance;
            using (var prefabAssetScope = new EditPrefabAssetScope(assetPath))
            {
                try
                {
                    instance = GameObject.Instantiate(prefabAssetScope.prefabRoot);
                    instance.hideFlags = HideFlags.DontSaveInEditor | HideFlags.DontSaveInBuild;
                }
                catch (System.Exception)
                {
                    Debug.LogError(assetPath);
                    throw;
                }
            }
            var particles = instance.GetComponentsInChildren<ParticleSystem>(true);
            var max_duration = 0f;
            var percent = 1 - checkAssetList.Count / (float)totalCount;
            EditorUtility.DisplayProgressBar("ParticleSystem Statistics", assetPath, percent);

            var isParticleUI = true;
            foreach (var particle in particles)
            {
                particle.Stop(true, ParticleSystemStopBehavior.StopEmittingAndClear);
                particle.Play(true);

                if (particle.gameObject.layer != uiLayout)
                {
                    isParticleUI = false;
                }

                if (particle.main.duration > max_duration)
                {
                    max_duration = particle.main.duration;
                }
            }
            if (isParticleUI && uiRootTransform)
            {
                instance.transform.SetParent(uiRootTransform, false);
            }

            var animatorSet = instance.GetComponentsInChildren<Animator>(true);
            if (animatorSet.Length == 0)
            {
                var animationSet = instance.GetComponentsInChildren<Animation>(true);
                foreach (var animation in animationSet)
                {
                    if (animation.clip == null)
                    {
                        continue;
                    }
                    animation.Stop();
                    animation.Play();
                    if (animation.clip.length > max_duration)
                    {
                        max_duration = animation.clip.length;
                    }
                }
            }

            if (max_duration > maxSimulatTime)
            {
                max_duration = maxSimulatTime;
            }

            if (sceneCamera != null)
            {
                var camera = instance.GetComponentInChildren<Camera>();
                sceneCamera.enabled = (camera == null);
            }


            List<string> warningMaterialsLogs = GetWarningMaterialsLogs(instance);
            List<string> warningTextureLogs = GetWarningTextureLogs(instance);

            currentParticleInfo = new ParticleInfo()
            {
                assetPath = assetPath,
                duration = 0,
                start_time = EditorApplication.timeSinceStartup,
                end_time = max_duration,
                max_triangles = 0f,
                max_vertices = 0f,
                instance = instance,
                warningTextureLogs = warningTextureLogs,
                warningMaterialsLogs = warningMaterialsLogs,
            };

            particleInfoSet[assetPath] = currentParticleInfo;

            if (bTestParticles)
            {
                currTestIdx++;
                if (currTestIdx > testMaxCount)
                {
                    checkAssetList.Clear();
                }
            }
        }
        if (currentParticleInfo == null)
        {
            EditorApplication.update -= OnUpdate;
        }
        double simulateTime = 0;
        if (currentParticleInfo != null)
        {
            currentParticleInfo.duration = (EditorApplication.timeSinceStartup - currentParticleInfo.start_time) * Time.timeScale;
            simulateTime = currentParticleInfo.duration;
            if (simulateTime > currentParticleInfo.end_time)
            {
                simulateTime = currentParticleInfo.end_time;
            }
        }
        if (currentParticleInfo != null)
        {
            UnityEditorInternal.InternalEditorUtility.RepaintAllViews();

            if (UnityStats.triangles > currentParticleInfo.max_triangles)
            {
                currentParticleInfo.max_triangles = UnityStats.triangles;
            }
            if (UnityStats.vertices > currentParticleInfo.max_vertices)
            {
                currentParticleInfo.max_vertices = UnityStats.vertices;
            }
        }

        if (currentParticleInfo != null)
        {
            var particles = currentParticleInfo.instance.GetComponentsInChildren<ParticleSystem>(true);
            int activeParticleSystemCount = 0;
            int allParticleSystemCount = particles.Length;
            foreach (var particle in particles)
            {
                if (particle.gameObject.activeSelf)
                {
                    activeParticleSystemCount++;
                }
            }

            if (activeParticleSystemCount > currentParticleInfo.maxActivePSCount)
            {
                currentParticleInfo.maxActivePSCount = activeParticleSystemCount;
            }

            if (allParticleSystemCount > currentParticleInfo.maxPSCount)
            {
                currentParticleInfo.maxPSCount = allParticleSystemCount;
            }

            if (UnityStats.drawCalls > currentParticleInfo.drawcall)
            {
                currentParticleInfo.drawcall = UnityStats.drawCalls;
            }


        }



    }

    private List<string> GetWarningMaterialsLogs(GameObject instance)
    {
        List<Material> materials = GetPrefabDepe<Material>(instance);
        List<string> warningLogs = new List<string>();
        List<Texture2D> textures = new List<Texture2D>();
        foreach (var materialItem in materials)
        {
            textures = GetPrefabDepe<Texture2D>(materialItem);

            if (textures.Count >= 5)
            {
                warningLogs.Add(materialItem.name + ":" + textures.Count);
            }
        }
        return warningLogs;
    }

    private List<string> GetWarningTextureLogs(GameObject instance)
    {
        List<Texture2D> textures = GetPrefabDepe<Texture2D>(instance);
        List<string> warningTextureLogs = new List<string>();
        int maxWidth = 0;
        int maxHeight = 0;
        string textureType = "";
        foreach (var textureItem in textures)
        {
            //Debug.LogError("textureItem.name:" + textureItem.name);
            //序列帧图
            if (textureItem.name.IndexOf("xulie") > 0)
            {
                maxWidth = 512;
                maxHeight = 512;
                textureType = "序列图";
            }
            //噪声贴图
            else if (textureItem.name.IndexOf("niose") > 0)
            {
                maxWidth = 64;
                maxHeight = 64;
                textureType = "噪声图";
            }
            //光晕贴图
            else if (textureItem.name.IndexOf("glow") > 0)
            {
                maxWidth = 128;
                maxHeight = 128;
                textureType = "光晕图";
            }
            //遮罩贴图
            else if (textureItem.name.IndexOf("mask") > 0)
            {
                maxWidth = 256;
                maxHeight = 256;
                textureType = "遮罩图";
            }
            //常规粒子贴图
            else
            {
                maxWidth = 256;
                maxHeight = 256;
                textureType = "常规图";
            }

            if (textureItem.width > maxWidth || textureItem.height > maxHeight)
            {
                warningTextureLogs.Add(textureType + "--" + textureItem.name + ":" + textureItem.width + "X" + textureItem.height);
            }
        }
        return warningTextureLogs;
    }

    /// 获取预制件依赖 <summary>
    /// 
    /// </summary>
    /// <typeparam name="T">欲获取的类型</typeparam>
    /// <param name="go"></param>
    /// <returns></returns>
    private List<T> GetPrefabDepe<T>(UnityEngine.Object go)
    {
        List<T> results = new List<T>();
        UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
        UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
        foreach (UnityEngine.Object dependObj in dependObjs)
        {
            //Debug.LogError("dependObj: " + dependObj.name + dependObj.GetType());
            if (dependObj != null && dependObj.GetType() == typeof(T))
            {
                results.Add((T)System.Convert.ChangeType(dependObj, typeof(T)));
            }
        }

        return results;
    }

    void OnGUI()
    {
        OnGUIStatisticParticle();
    }

    void PrintParticleInfo()
    {
        var dicSort = particleInfoSet.OrderBy(i => i.Value.max_triangles);
        foreach (var particleInfo in dicSort)
        {
            var des = $"path:{particleInfo.Value.assetPath}, max triangles:{particleInfo.Value.max_triangles}, max vertices:{particleInfo.Value.max_vertices}";
            if (logWriter != null)
            {
                logWriter.WriteLine(des);
            }
            Debug.Log(des);
        }
    }

    void SaveCsv()
    {
        saveDeleteImgPTList.Clear();
        var dicSort = particleInfoSet.OrderBy(i => i.Value.max_triangles);
        foreach (var particleInfo in dicSort)
        {
            ParticleInfo info = particleInfo.Value;

            ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
            patientStatisticsOutputDto.datas.Add(info.assetPath);
            patientStatisticsOutputDto.datas.Add(info.duration.ToString());
            patientStatisticsOutputDto.datas.Add(info.max_triangles.ToString());
            patientStatisticsOutputDto.datas.Add(info.max_vertices.ToString());

            patientStatisticsOutputDto.datas.Add(info.maxActivePSCount.ToString());
            patientStatisticsOutputDto.datas.Add(info.maxPSCount.ToString());
            patientStatisticsOutputDto.datas.Add(info.drawcall.ToString());
            string combinedStr = string.Join("\n", info.warningTextureLogs.ToArray());
            patientStatisticsOutputDto.datas.Add("\"" + combinedStr + "\"");
            combinedStr = string.Join("\n", info.warningMaterialsLogs.ToArray());
            patientStatisticsOutputDto.datas.Add("\"" + combinedStr + "\"");
            saveDeleteImgPTList.Add(patientStatisticsOutputDto);
        }
        var logFolder = $"{Application.dataPath}/../ResourceStatistic/";
        var logPath = $"{logFolder}/particleSystemCheckList.csv";
        Debug.Log(logPath);
        exportCSV.ExportPatientStatisticsDetails(saveDeleteImgPTList, logPath,
                "路径,最大时长(duration),最多面数,最多顶点数,最大同屏激活粒子数,最大同屏粒子数,最大drawcall, " +
                "贴图警告(建议：序列图<=512*512 噪声图<=64*64 光晕图<=128*128 遮罩图<=256*256 常规图<=256*256), 单个粒子系统引用贴图数警告（建议：小于5）");
    }

    void OnGUIStatisticParticle()
    {
        EditorGUILayout.BeginHorizontal();
        //获得一个长300的框
        pathRect = EditorGUILayout.GetControlRect(GUILayout.Width(300));
        //将上面的框作为文本输入框
        prefabPath = EditorGUI.TextField(pathRect, prefabPath);
        EditorGUILayout.EndHorizontal();

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && pathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                prefabPath = DragAndDrop.paths[0];
            }
        }
        EditorGUILayout.BeginHorizontal();
        //获得一个长240的框
        pathRect = EditorGUILayout.GetControlRect(GUILayout.Width(240));
        //将上面的框作为文本输入框
        filtersStr = EditorGUI.TextField(pathRect, filtersStr);
        isOpenFilters = EditorGUILayout.ToggleLeft("开启过滤", isOpenFilters);
        EditorGUILayout.EndHorizontal();

        if (GUILayout.Button("开始"))
        {
            if (string.IsNullOrEmpty(prefabPath))
            {
                Debug.LogError("输入路径错误");
                return;
            }

            var sceneName = "ParticleStatistics";
            var scene = UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene();
            if (scene.name != sceneName)
            {
                UnityEditor.SceneManagement.EditorSceneManager.OpenScene(sceneName);
            }

            if (uiLayout == -1)
            {
                uiLayout = LayerMask.NameToLayer("UI");
            }

            EditorApplication.update -= OnUpdate;
            EditorApplication.update += OnUpdate;

            string prefabFolder = prefabPath;
            var extension = Path.GetExtension(prefabPath);
            if (string.IsNullOrEmpty(extension))
            {
                //处理文件夹
                string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });

                //checkAssetList = new Stack<string>(allPath);
                checkAssetList = new Stack<string>();
                for (int i = 0; i < allPath.Length; i++)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(allPath[i]);
                    if (isOpenFilters && assetPath.IndexOf(filtersStr, StringComparison.OrdinalIgnoreCase) >= 0)
                        checkAssetList.Push(allPath[i]);
                    else if (!isOpenFilters)
                        checkAssetList.Push(allPath[i]);
                }
                totalCount = checkAssetList.Count;
            }
            else
            {
                //处理文件
                checkAssetList = new Stack<string>();
                checkAssetList.Push(AssetDatabase.AssetPathToGUID(prefabFolder));
                totalCount = 0;
            }

            if (logWriter != null)
            {
                logWriter.Dispose();
                logWriter = null;
            }
            var logFolder = $"{Application.dataPath}/../ResourceStatistic/";
            var logPath = $"{logFolder}/{DateTime.Now.ToString("yyyy-MM-dd-hh-mm-ss")}.txt";
            if (File.Exists(logPath))
            {
                File.Delete(logPath);
            }

            if (logWriter == null)
            {
                logFolder = Path.GetFullPath(logFolder);
                logPath = Path.GetFullPath(logPath);
                if (!Directory.Exists(logFolder))
                {
                    Directory.CreateDirectory(logFolder);
                }
                logWriter = new StreamWriter(logPath)
                {
                    AutoFlush = true
                };

                Debug.Log($"开始将日志写入文件:{logPath}");

                if (uiRootTransform == null)
                {
                    var canvas = GameObject.Find("Canvas");
                    if (canvas)
                    {
                        uiRootTransform = canvas.transform;
                    }
                }
                if (sceneCamera == null)
                {
                    var sceneCameraObj = GameObject.Find("3DCamera");
                    sceneCamera = sceneCameraObj?.GetComponent<Camera>();
                }
            }

            EditorApplication.isPlaying = true;
        }

        if (GUILayout.Button("停止"))
        {
            EditorApplication.update -= OnUpdate;
            if (currentParticleInfo != null && currentParticleInfo.instance != null)
            {
                GameObject.DestroyImmediate(currentParticleInfo.instance);
            }
            PrintParticleInfo();
            SaveCsv();
            EditorUtility.ClearProgressBar();
            if (checkAssetList != null)
                checkAssetList.Clear();
            if (logWriter != null)
            {
                logWriter.Dispose();
            }
            totalCount = 0;
            particleInfoSet.Clear();

            EditorApplication.isPlaying = false;
        }
    }
}
