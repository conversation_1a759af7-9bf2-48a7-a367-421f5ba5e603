using UnityEngine;
using UnityEditor;
using System.IO;
using System.Text;
using System.Reflection;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace War.Base
{
    public class AssetbundlesMenuItems
    {
        const string kSimulateAssetBundlesMenu = "AssetBundles/Simulate AssetBundles";
        const string kSimulateAssetBundleUpdateMenu = "AssetBundles/Simulate AssetBundles Update";
        const string uiTextureBundlePrefix = "ui/spritepacking/";
        const string kUseAotObjectContainerMenu = "AssetBundles/UseAotObjectContainer";
        
        protected static Regex ms_RemoveCommentRegex = new Regex(@"--\[\[(\s|.)*?\]\]");
        protected static Regex ms_CommentLogRegex = new Regex(@"(\n[\t ]*?)(print|log\.Log|Debug\.Log|log\.LogFormat)( *?)(\([^\(\)]*\))");
        //protected static Regex ms_CommentLogRegex = new Regex(@"(\n[\t ]*?)(print|log.Log|log.LogFormat|log.WarningFormat) *?([^\(\)]|(?<open>\()|(?<-open>\)))*?(?(open)(?!))");
        // protected static Regex ms_CommentLogRegex = new Regex(@"([\t ]*?)(print|log.Log|log.LogFormat)( *?)(\([^\(\)]*\))");




        //屏蔽工具类和系统文件
        public static Dictionary<string, int> ms_CommentLogFileNameDic = new Dictionary<string, int> {
            {"extern.txt",1},
            {"mobdebug.txt",1},
            {"util.txt",1},
            {"sm_system.txt",1},
            {"q1sdk.txt",1},
            {"google.txt",1},
            {"bugly.txt",1},
            {"appsflyer.txt",1},
            {"adjust.txt",1},
            {"scene_mgr.txt",1},
            {"run_script.txt",1},
            {"idle.txt",1},
            {"hook_load.txt",1},
            {"hook_count.txt",1},
            {"download_mgr.txt",1},
            {"asset_load_mgr.txt",1},
            {"quality.txt",1},
            {"text_format.txt",1},
            {"preload_resources.txt",1},
            {"new_scene_mgr.txt",1},
            {"net_recorder.txt",1},
            {"net_sociaty_war.txt",1},
            {"net_recharge_module.txt",1},
            {"net_login_module.txt",1},
            {"net_gateway_module.txt",1},
            {"net_feedback_module.txt",1},
            {"net_script.txt",1},
            {"net_route.txt",1},
            {"net.txt",1},
            {"music_contorller.txt",1},
            {"logwriter.txt",1},
            {"log_debug.txt",1},
            {"lobby_mgr.txt",1},
            {"init.txt",1},
            {"cs_hotfix.txt",1},
            {"hotfix.txt",1},
            {"DataCenter.txt",1},
            {"game_config.txt",1},
            {"game.txt",1},
            {"device_param_util.txt",1},
        };
        //protected static Regex ms_CommentLogRegex = new Regex(@"(\n[\t ]*?)(print|log.Log|log.LogFormat|log.Warning|log.WarningFormat)( *?)(\([^\(\)]*\))");

        [MenuItem(kSimulateAssetBundlesMenu)]
        public static void ToggleSimulateAssetBundle()
        {
            // AssetBundleManager.SimulateAssetBundleInEditor = !AssetBundleManager.SimulateAssetBundleInEditor;
            PropertyInfo property = typeof(AssetBundleManager).GetProperty("SimulateAssetBundleInEditor",
                                                                    BindingFlags.Public | BindingFlags.Static);
            if (property != null)
            {
                bool simulate = (bool)property.GetValue(null, null);
                property.SetValue(null, !simulate, null);
            }
        }

        [MenuItem(kSimulateAssetBundlesMenu, true)]
        public static bool ToggleSimulateAssetBundleValidate()
        {
            // Menu.SetChecked(kSimulateAssetBundlesMenu, AssetBundleManager.SimulateAssetBundleInEditor);
            PropertyInfo property = typeof(AssetBundleManager).GetProperty("SimulateAssetBundleInEditor",
                                                                    BindingFlags.Public | BindingFlags.Static);
            if (property != null)
            {
                bool simulate = (bool)property.GetValue(null, null);

                Menu.SetChecked(kSimulateAssetBundlesMenu, simulate);
            }
            return true;
        }
        
        [MenuItem(kUseAotObjectContainerMenu)]
        public static void ToggleUseAotObjectContainer()
        {
            PropertyInfo property = typeof(AssetBundleManager).GetProperty("UseAotObjectContainerInEditor",
                BindingFlags.Public | BindingFlags.Static);
            if (property != null)
            {
                bool isUse = (bool)property.GetValue(null, null);
                property.SetValue(null, !isUse, null);
            }
        }
        
        [MenuItem(kUseAotObjectContainerMenu, true)]
        public static bool ToggleUseAotObjectContainerValidate()
        {
            PropertyInfo property = typeof(AssetBundleManager).GetProperty("UseAotObjectContainerInEditor",
                BindingFlags.Public | BindingFlags.Static);
            if (property != null)
            {
                bool isUse = (bool)property.GetValue(null, null);
                Menu.SetChecked(kUseAotObjectContainerMenu, isUse);
            }
            return true;
        }

        [MenuItem("AssetBundles/Build AssetBundles")]
        static public void BuildAssetBundles()
        {
            BuildScript.BuildAssetBundles();
        }

        static public void CheckAndHandleAbVaild(int mode = 0)
        {
            var stopwatch = new System.Diagnostics.Stopwatch(); stopwatch.Start();
            JenkinsEnv.Instance.Set("build_ab_optimize", "true");
            FileCheck.CheckAndHandleAbVaild("AssetBundles/Android", mode);
            stopwatch.Stop();//CheckFiles2 time=49119ms
            UnityEngine.Debug.Log($"CheckAndHandleAbVaild{mode} time={stopwatch.ElapsedMilliseconds * 0.001}s");
        }
        /*//[MenuItem("AssetBundles/CheckAndHandleAbVaild1")]
        static public void CheckAndHandleAbVaild1() { CheckAndHandleAbVaild(1); }
        //[MenuItem("AssetBundles/CheckAndHandleAbVaild2")]
        static public void CheckAndHandleAbVaild2() { CheckAndHandleAbVaild(2); }
        //[MenuItem("AssetBundles/CheckAndHandleAbVaild3")]
        static public void CheckAndHandleAbVaild3() { CheckAndHandleAbVaild(3); }
        //[MenuItem("AssetBundles/CheckAndHandleAbVaild4")]
        static public void CheckAndHandleAbVaild4() { CheckAndHandleAbVaild(4); }*/

        [MenuItem("AssetBundles/Build Player")]
        static void BuildPlayer()
        {
            BuildScript.BuildPlayer();
        }

        [MenuItem("AssetBundles/RemoveABS")]
        static void RemoveABS()
        {
            AssetBundle.UnloadAllAssetBundles(true);
            var removelist = new List<string>()
            {
                "AssetBundles",
                "NewCaching",
                "Caching",
            };
            foreach (var p in removelist)
            {
                if (System.IO.Directory.Exists(p))
                {
                    try
                    {
                        System.IO.Directory.Delete(p, true);
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogError(e);
                    }
                }
                Debug.Log(string.Format("{0}:{1}", p, System.IO.Directory.Exists(p)));
            }
            Resources.UnloadUnusedAssets();
            System.GC.Collect();
        }
        [MenuItem("AssetBundles/GcCollect")]
        static void GcCollect()
        {
            Resources.UnloadUnusedAssets();
            System.GC.Collect();
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/CreatePatch")]
        static void CreatePatch()
        {

            var fpath = "forcerp/combiner/BConfig.cs";
            var ccc = File.ReadAllText(fpath);
            File.WriteAllText("Assets/Scripts/CommonTools/BConfig.cs", ccc);
            //BuildScript.CreatePatch();
        }

        [MenuItem("AssetBundles/CreateFilesTxt")]
        static void CreateFilesTxt()
        {
            BuildScript.CreatePatch();
        }
        [MenuItem("AssetBundles/Decrypt Asset Bundles")]
        static void DecryptAssetBundles()
        {
            BuildScript.DecryptAssetBundles();
        }

        [MenuItem("Assets/AssetBundle/Set AssetBundle By Path")]
        static public void SetAssetBundleByPath()
        {
            foreach (var obj in Selection.objects)
            {
                var assetPath = AssetDatabase.GetAssetPath(obj);
                
                if (!Path.HasExtension(assetPath))
                {
                    Debug.Log("选择到了文件夹"+assetPath);
                
                }else{
                    var assetImporter = AssetImporter.GetAtPath(assetPath);
                    string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                    assetImporter.assetBundleName = assetBundleName;
                    LogHelp.clipboard = assetBundleName.ToLower();
                }
                    
            }
        }

        [MenuItem("Assets/AssetBundle/Set AssetBundle By Directory")]
        static public void SetAssetBundleByDir()
        {
            foreach (var obj in Selection.objects)
            {
                var assetPath = AssetDatabase.GetAssetPath(obj);
                if (!Path.HasExtension(assetPath))
                {
                    Debug.Log("选择到了文件夹"+assetPath);
                
                }else{
                    var assetImporter = AssetImporter.GetAtPath(assetPath);
                    var dirName = Path.GetDirectoryName(assetPath);
                    string assetBundleName = dirName.Substring(dirName.IndexOf("/") + 1);
                    assetImporter.assetBundleName = assetBundleName;
                }
            }
        }

        [MenuItem("Assets/AssetBundle/Set AssetBundle By PackingTag")]
        static public void SetAssetBundleByPackingTag()
        {
            foreach (var obj in Selection.objects)
            {
                var assetPath = AssetDatabase.GetAssetPath(obj);
                var textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                if (textureImporter != null)
                {
                    if (!string.IsNullOrEmpty(textureImporter.spritePackingTag))
                    {
                        if (assetPath.StartsWith("assets/ui", System.StringComparison.OrdinalIgnoreCase))
                        {
                            textureImporter.assetBundleName = uiTextureBundlePrefix + textureImporter.spritePackingTag;
                        }
                        else
                        {

                            string assetBundleName = Path.GetDirectoryName(assetPath).Substring(assetPath.IndexOf("/") + 1);
                            textureImporter.assetBundleName = assetBundleName + "/" + textureImporter.spritePackingTag + ".spritepack";
                        }
                    }
                    else
                    {
                        string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                        textureImporter.assetBundleName = assetBundleName;
                    }
                }
            }
        }

        [MenuItem("Assets/Shader/Replace Shader By Name")]
        static public void ReplaceShaderByName()
        {
            foreach (var obj in Selection.objects)
            {
                var mat = obj as Material;
                if (mat != null)
                {
                    mat.shader = Shader.Find(mat.shader.name);
                    EditorUtility.SetDirty(obj);
                }
            }
        }

        [MenuItem("Assets/Shader/Find Standard Shader In Use")]
        static public void FindStandardShaderInUse()
        {
            //Object[] selected = 
            List<Object> selects = new List<Object>();
            foreach (var obj in Selection.objects)
            {
                var mat = obj as Material;
                if (mat != null)
                {
                    if (mat.shader.name.Contains("Standard"))
                    {
                        selects.Add(obj);
                        //Debug.LogFormat("use standard material: {0}", AssetDatabase.GetAssetPath(mat));
                    }
                }
            }

            Selection.objects = selects.ToArray();
        }

        private static readonly Dictionary<string, string> MobileShaderMap = new Dictionary<string, string>()
        {
            /*{"Particles/Additive", "Mobile/Particles/Additive"},
            {"Particles/Alpha Blended", "Mobile/Particles/Alpha Blended"},
            {"Particles/Multiply", "Mobile/Particles/Multiply"},*/
            {"Legacy Shaders/Bumped Specular","War/Character/BumpedSpecular"},
            //{"Skybox/6 Sided","Mobile/Skybox" },
            {"Legacy Shaders/Diffuse", "War/Mobile/Diffuse"},
            {"Mobile/Diffuse", "War/Mobile/Diffuse" },
            {"Effects/FPS_Pack/AlphaBlended","Particles/Alpha Blended"},
            {"FORGE3D/Additive","Particles/Additive" }
        };

        [MenuItem("Assets/Shader/Replace Shader To Mobile")]
        static public void ReplaceShaderToMobile()
        {
            //Object[] selected = 
            List<Object> selects = new List<Object>();
            foreach (var obj in Selection.objects)
            {
                var mat = obj as Material;
                if (mat != null)
                {
                    string mobileShaderName;
                    MobileShaderMap.TryGetValue(mat.shader.name, out mobileShaderName);
                    if (!string.IsNullOrEmpty(mobileShaderName))
                    {
                        mat.shader = Shader.Find(mobileShaderName);
                        EditorUtility.SetDirty(obj);
                        selects.Add(obj);
                    }
                }
            }

            Selection.objects = selects.ToArray();
        }

        [MenuItem("Assets/Shader/Find Material Use Shader")]
        static public void FindMaterialUseShader()
        {
            List<Object> selects = new List<Object>();
            foreach (var obj in Selection.objects)
            {
                var mat = obj as Material;
                if (mat != null)
                {
                    if (mat.shader.name == "Mobile/Particles/Alpha Blended")
                    {
                        selects.Add(obj);
                    }
                }
            }

            Selection.objects = selects.ToArray();
        }

        [MenuItem("Assets/Material/Replace Sprites-Default")]
        static public void ReplaceSpritesDefaultMaterial()
        {
            Material spritesDefaultMat = AssetDatabase.LoadAssetAtPath<Material>("Assets/Art/Common/Materials/Sprites-Default.mat");

            int replaceCount = 0;

            List<Object> selects = new List<Object>();
            foreach (var obj in Selection.objects)
            {
                var go = obj as GameObject;
                var spriteRenderers = go.GetComponentsInChildren<SpriteRenderer>(true);
                foreach (var renderer in spriteRenderers)
                {
                    if (renderer.sharedMaterial.name == "Sprites-Default" && renderer.sharedMaterial != spritesDefaultMat)
                    {
                        renderer.sharedMaterial = spritesDefaultMat;

                        selects.Add(obj);
                        replaceCount++;
                    }
                }
            }

            Selection.objects = selects.ToArray();
            Debug.LogFormat("总共替换了{0}个组件的材质！", replaceCount);
        }

        [MenuItem("AssetBundles/Comment Lua Print")]
        public static void CommentLuaPrintMenu()
        {
            Dictionary<string, byte[]> a = null;
            CommentLuaPrint(out a);
        }

        public static Dictionary<string, byte[]> CommentLuaPrint(out Dictionary<string, byte[]> cullLuas)
        {
            cullLuas = new Dictionary<string, byte[]>();
            var normal = new Regex(@"[^\r\n]*");
            Dictionary<string, byte[]> originLuaContents = new Dictionary<string, byte[]>();
            var LuaPath = "Assets/Lua";
            string[] luaFiles = Directory.GetFiles(LuaPath, "*.txt", SearchOption.AllDirectories);
            //string[] luaFiles = AssetDatabase.GetAssetPathsFromAssetBundle(War.Script.LuaManager.LuaScriptAssetBundleName);



            foreach (var luaFile in luaFiles)
            {
                bool isHave = false;
                //for (int i = 0; i < AssetbundlesMenuItems.ms_CommentLogFileNameDic.Length; ++i)
                //{
                //    if (luaFile.EndsWith(AssetbundlesMenuItems.ms_CommentLogFileNameDic[i]))
                //    {
                //        isHave = true;
                //    }
                //}

                //lang表的lua不需要加进二进制
                if (BuildLuaScriptPatch.IsLangLuaRemoveable(luaFile))
                {
                    continue;
                }
                var fileName = Path.GetFileName(luaFile);
                isHave = ms_CommentLogFileNameDic.ContainsKey(fileName);

                var bytes = File.ReadAllBytes(luaFile);
                originLuaContents.Add(luaFile, bytes);
                string content = Encoding.UTF8.GetString(bytes);

                //过滤工具类
                if (!isHave)
                {
                    // 保留换行符
                    content = ms_RemoveCommentRegex.Replace(content, (e) => { return normal.Replace(e.Value, ""); });
                    content = ms_CommentLogRegex.Replace(content, "");
                    // content = ms_CommentLogRegex.Replace(content, @"--$&");
                }
                byte[] bytes1 = Encoding.UTF8.GetBytes(content);
                cullLuas[luaFile] = bytes1;
                File.WriteAllBytes(luaFile, bytes1);
            }

            return originLuaContents;
        }

        [MenuItem("AssetBundles/Dll/ChooseActiveDll")]
        public static void ChooseDll()
        {
            var dll_folder = JenkinsEnv.Instance.Get("dll_folder");

            string lower_platform_str = EditorUserBuildSettings.activeBuildTarget.ToString().ToLower();
            var fromPath = "dll/" + dll_folder + lower_platform_str;
            AutoBuildDLL.CheckDllFolder(ref fromPath);
            if (!Directory.Exists(fromPath))
            {
                Debug.Log("ChooseDll skip:" + fromPath);
                return;
            }
            var toPath = Application.dataPath + "/Scripts";

            "".Print("ChooseDll", "dll_folder", dll_folder, fromPath, toPath);

            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/Dll/ChooseDebugDll")]
        public static void ChooseDebugDll()
        {
            var fromPath = "dll/debug";
            var toPath = Application.dataPath + "/Scripts";
            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/Dll/CacheActiveDll")]
        public static void CacheActiveDll()
        {
            var toPath = "dll/" + EditorUserBuildSettings.activeBuildTarget.ToString().ToLower();
            var fromPath = Application.dataPath + "/Scripts";
            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/Dll/CacheDebugDll")]
        public static void CacheDebugDll()
        {
            var toPath = "dll/debug";
            var fromPath = Application.dataPath + "/Scripts";
            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/Dll/CacheIOSDll")]
        public static void CacheIOSDll()
        {
            var toPath = "dll/ios";
            var fromPath = Application.dataPath + "/Scripts";
            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        [MenuItem("AssetBundles/Dll/CacheAndroidDll")]
        public static void CacheAndroidDll()
        {
            var toPath = "dll/android";
            var fromPath = Application.dataPath + "/Scripts";
            CopyDir(fromPath, toPath);
            AssetDatabase.Refresh();
        }
        static void CopyDir(string fromPath, string toPath)
        {
            fromPath = Path.GetFullPath(fromPath);
            toPath = Path.GetFullPath(toPath);
            var files = Directory.GetFiles(fromPath, "*.dll");
            foreach (var f in files)
            {
                EditorHelp.CopyFile(f, f.Replace(fromPath, toPath));
            }
        }
        [MenuItem("Assets/Ani/CheckShader", false, 1)]
        static void CheckShader()
        {
            LogHelp.Instance.Log("start CheckShader");
            var abs = AssetDatabase.GetAllAssetBundleNames();
            var listA = new List<string>();
            var dicAll = new Dictionary<string, bool>();
            var dicAb = new Dictionary<string, bool>();


            int startIndex = 0;

            var list = new List<string>();
            EditorApplication.update = delegate ()
            {
                string v = abs[startIndex];
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("Dealing..", v, (float)startIndex / (float)abs.Length);
                listA.Clear();
                listA.AddRange(AssetDatabase.GetAssetPathsFromAssetBundle(v));

                var ds = AssetDatabase.GetDependencies(listA.ToArray(), true);

                foreach (var p in ds)
                {
                    if (p.EndsWith(".shader"))
                    {
                        if (dicAll.ContainsKey(p) == false)
                        {
                            dicAll[p] = true;
                            var ab = AssetDatabase.GetImplicitAssetBundleName(p);
                            dicAb[ab] = true;
                        }
                    }
                }
                startIndex++;
                if (isCancel || startIndex >= abs.Length)
                {
                    packing = false;
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update = null;
                    startIndex = 0;
                    Debug.Log("End");
                    LogHelp.Instance.Log("end CheckShader");
                    Debug.Log(UIHelper.ToJson(dicAll.Keys));
                    Debug.Log(UIHelper.ToJson(dicAb.Keys));

                    foreach (var item in dicAll.Keys)
                    {
                        SetABName(item, "shaders_for_warmup");
                    }
                    return;
                }
            };
        }
        static void SetABName(string oPath, string assetBundleName)
        {
            if (File.Exists(oPath) == false) return;
            var assetPath = oPath;
            if (!Path.HasExtension(assetPath))return;
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            assetImporter.assetBundleName = assetBundleName;
        }
        static bool packing = false;
        [MenuItem("Assets/Ani/SetUpPackingTag", false, 1)]
        static void SetUpPackingTag()
        {
            if (packing)
            {
                return;
            }
            packing = true;
            LogHelp.Instance.Log("SetUpPackingTag");

            var objs = Selection.objects;

            var files = new List<string>();
            foreach (var f in objs)
            {
                files.Add(AssetDatabase.GetAssetPath(f));
            }

            int startIndex = 0;

            var list = new List<string>();
            EditorApplication.update = delegate ()
            {
                string file = files[startIndex].Replace("\\", "/");
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("Dealing..", file, (float)startIndex / (float)files.Count);

                var cards = AssetDatabase.FindAssets("t:Prefab", new string[] { files[startIndex] });
                foreach (var c in cards)
                {
                    CardConfigEditor.SetupPackingTagOne(AssetDatabase.GUIDToAssetPath(c));
                }
                startIndex++;
                if (isCancel || startIndex >= files.Count)
                {
                    packing = false;
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update = null;
                    startIndex = 0;
                    LogHelp.clipboard = Newtonsoft.Json.JsonConvert.SerializeObject(list);
                    Debug.Log("End");
                    LogHelp.Instance.Log("end SetUpPackingTag");
                    return;
                }
            };

        }
    }
}