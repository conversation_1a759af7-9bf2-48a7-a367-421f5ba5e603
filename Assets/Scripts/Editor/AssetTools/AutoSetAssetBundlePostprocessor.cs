using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Xml;
using System.IO;
using System.Globalization;
using War.Base;

namespace War
{


	class AutoSetAssetBundlePostprocessor : AssetPostprocessor
	{
		public struct cache_vo
		{
			public string path;
			public string abname;
			public double time;
		}
		static Dictionary<string, cache_vo> lastAbCache = new Dictionary<string, cache_vo>();
		public static bool TempCloseCheck = false;
		static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets,
											string[] movedAssets, string[] movedFromAssetPaths)
		{
			if (TempCloseCheck)
			{
				return;
			}
			//"".Print(1,ToolUti.ToJson(importedAssets), ToolUti.To<PERSON>son(deletedAssets), ToolUti.ToJson(movedAssets), ToolUti.ToJson(movedFromAssetPaths));
			
			//return;
			bInit = false;
			configList.Clear();
			//			return;
			SetABNameCacheDic.Clear();
			SetSpriteTagCacheDic.Clear();
			//			 Debug.LogError ("import count:"+importedAssets.Length);
			List<string> assetsToImport = new List<string>();
			foreach (var str in importedAssets)
			{
				OnPostprocessAsset(str, assetsToImport);
			}

			foreach (var str in movedAssets)
			{
				OnPostprocessAsset(str, assetsToImport);
			}

			//			Debug.LogError ("SetSpriteTagCacheDic:"+SetSpriteTagCacheDic.Count);
			foreach (var item in SetSpriteTagCacheDic)
			{
				//				item.Key
				var ai = AssetImporter.GetAtPath(item.Key) as TextureImporter;
				if ((ai != null) && !string.Equals(ai.spritePackingTag, item.Value))
				{
					ai.spritePackingTag = item.Value;
					assetsToImport.Add(item.Key);
				}
			}

			//			Debug.LogError ("SetABNameCacheDic:"+SetABNameCacheDic.Count);
			foreach (var item in SetABNameCacheDic)
			{
				//				item.Key
				var ai = AssetImporter.GetAtPath(item.Key);
				string toAbname = item.Value.ToLower();
				if ((ai != null) && !string.Equals(ai.assetBundleName, toAbname))
				{
					bool bAvail = AddAbnameCache(item.Key, toAbname);
					"".PrintError(toAbname, bAvail);

					if (bAvail == false) continue;
					ai.assetBundleName = toAbname;
					EditorUtility.SetDirty(ai);
					ai.SaveAndReimport();
				}
			}
			//"".PrintError(SetABNameCacheDic.Count, ToolUti.ToJson(assetsToImport.Count));

			//ImportAssets(assetsToImport.ToArray());
			if (assetsToImport.Count > 0)
			{
				//AssetDatabase.Refresh();
			}
			SetABNameCacheDic.Clear();
			SetSpriteTagCacheDic.Clear();
			//"".Print(2,ToolUti.ToJson(importedAssets), ToolUti.ToJson(deletedAssets), ToolUti.ToJson(movedAssets), ToolUti.ToJson(movedFromAssetPaths));

		}
		static cache_vo tmp_vo = default(cache_vo);

		public static bool AddAbnameCache(string path, string abname)
		{
			//"".Print("AddAbnameCache", path, abname);

			if (lastAbCache.TryGetValue(path, out tmp_vo))
			{
				if (abname != tmp_vo.abname)
					if (EditorApplication.timeSinceStartup - tmp_vo.time < 1)
					{
						"".PrintError("AddAbnameCache error, multi abname", path, tmp_vo.abname, abname);
						return false;
					}
					else
					{
						tmp_vo.time = EditorApplication.timeSinceStartup;
						lastAbCache[path] = tmp_vo;
					}
			}
			else
			{
				lastAbCache[path] = new cache_vo()
				{
					path = path,
					abname = abname,
					time = EditorApplication.timeSinceStartup
				};
			}
			return true;
		}

		public enum Strategy
		{
			Path = 0,
			Dir,
			Tag,
			Customer,
			SetTagByPath,
		}

		public struct AssetBundleConfig
		{
			public string path;
			public string postfix;
			public Strategy strategy;
			public string name;
			public int priority;
			public bool forceUpdateTag;
		}
		private static List<AssetBundleConfig> configList = new List<AssetBundleConfig>();
		static List<string> banList = new List<string>();
		private static string xmlpath = "Assets/Scripts/Editor/AssetBundleConfig.xml";
		private static bool bInit = false;

		private const string UITextureBundlePrefix = "ui/spritepacking/";
		private const string SpritePackingSuffix = ".spritepack";
		/*
		 *  1. LUA 脚本						.txt
		 *  2. 策划配置的数据文件			.bytes
		 *  3. 美术资源
		 * 			场景					.unity 怎么切分的
		 * 			
		 * 			人物
		 * 			
		 * 			模型
		 * 			
		 * 			特效
		 * 	
		 * 			UI
		 * 			美术资源就涉及 Shader, Material, Mesh, Texture
		 * 
		 *  门槛，依赖关系的解决, 小包和大包的综合
		 *  ASSETBUNDLE 的打包策略，文件？路径？文件夹？ 特殊的TAG？
		 *
		 */
		private static void LoadXMLConfig()
		{
			configList.Clear();
			#region XMLCONFIG
			bool bConflic = false;
			XmlDocument xmlDoc = new XmlDocument();
			try
			{
				string fullpath = Application.dataPath.Substring(0, Application.dataPath.LastIndexOf('/')) + "/" + xmlpath;
				xmlDoc.Load(fullpath);
				XmlNodeList abList = xmlDoc.SelectSingleNode("AssetBundleConfig").SelectNodes("AssetBundle");
				banList.Clear();
				for (int i = 0; i < abList.Count; i++)
				{
					XmlNode ab = abList.Item(i);
					string name = ab.Attributes["name"].Value;
					bool forceUpdateTag = true;
					if (ab.Attributes["forceUpdateTag"] != null && ab.Attributes["forceUpdateTag"].Value.ToLower() == "false")
					{
						forceUpdateTag = false;
					}
					XmlNode path = ab.SelectSingleNode("AssetPath");
					XmlNode postfix = ab.SelectSingleNode("PostFix");
					XmlNode strategy = ab.SelectSingleNode("Strategy");
					var priority = (ab.Attributes["priority"] != null) ? ab.Attributes["priority"].Value : "0";
					var iPriority = 0;
					int.TryParse(priority, out iPriority);
					if (ab.Attributes["subdir"] != null && ab.Attributes["subdir"].Value.ToLower() == "true")
					{
						//						Debug.LogError (path.InnerText);
						//						AddToConfigList(path.InnerText, postfix.InnerText, strategy.InnerText, name, forceUpdateTag);
						if (Directory.Exists(path.InnerText) == false)
						{
							continue;
						}
						string[] dirs = Directory.GetDirectories(path.InnerText);
						foreach (var dir in dirs)
						{
							string subPath = dir.Replace("\\", "/");
							AddToConfigList(subPath, postfix.InnerText, strategy.InnerText, name, forceUpdateTag, iPriority);

						}
					}
					else
					{
						AddToConfigList(path.InnerText, postfix.InnerText, strategy.InnerText, name, forceUpdateTag, iPriority);
					}

				}
				configList.Sort((c1, c2) => string.Compare(c1.path, c2.path));

				//				LogHelp.clipboard = LitJson.JsonMapper.ToJson (configList);
				//				 Debug.LogError ();
				//检查配置合法性
				for (int i = configList.Count - 2; i >= 0; i--)
				{
					var abc = configList[i + 1];
					for (int j = 0; j < i; j++)
					{
						var c = configList[j];
						var bLConflic = SearchSameConfig(c, abc);
						bConflic |= bLConflic;
						if (bLConflic)
						{
							//						configList.RemoveAt (i+1);
							//Debug.LogError ("LoadXMLConfig conflic:\n"+LitJson.JsonMapper.ToJson (abc)+"\n"+LitJson.JsonMapper.ToJson (c));
						}
					}
				}
				if (bConflic)
				{
					//Debug.LogError (LitJson.JsonMapper.ToJson (configList));
					Debug.LogError("导入资源时，发现配置错误，请联系程序员！");
					throw new System.Exception();
				}
				bInit = true;
			}
			catch (System.Exception e)
			{
				Debug.LogError("导入资源时，发现配置错误，请联系程序员！");
				Debug.LogError(e);
				configList.Clear();
			}
			//			configList.Clear();
			#endregion
		}


		static bool SearchSameConfig(AssetBundleConfig c, AssetBundleConfig abc)
		{
			var set1 = new HashSet<string>(abc.postfix.Split(','));
			set1.IntersectWith(c.postfix.Split(','));
			if (0 == set1.Count)
			{
				return false;
			}

			var len = Mathf.Max(c.path.Length, abc.path.Length);

			return (c.path.Length == len) ? c.path.Contains(abc.path + "/") : abc.path.Contains(c.path + "/");
		}
		static char[] TrimEnd = new char[] {
			'/',
			' '
		};

		static string GetKey(AssetBundleConfig abc)
		{
			var key = string.Format("{0}|{1}|{2}", abc.postfix, abc.strategy, abc.path);
			return key;
		}

		private static void AddToConfigList(string path, string postfix, string strategy, string name, bool forceUpdateTag, int priority = 0)
		{
			AssetBundleConfig abc;
			abc = new AssetBundleConfig();
			abc.path = path.TrimEnd(TrimEnd);
			abc.postfix = postfix;
			switch (strategy.ToLower())
			{
				case "path":
					abc.strategy = Strategy.Path;
					break;
				case "dir":
					abc.strategy = Strategy.Dir;
					break;
				case "tag":
					abc.strategy = Strategy.Tag;
					break;
				case "settagbypath":
					abc.strategy = Strategy.SetTagByPath;
					break;
				case "customer":
					abc.strategy = Strategy.Customer;
					break;
			}
			abc.name = name;
			abc.forceUpdateTag = forceUpdateTag;
			abc.priority = priority;

			for (int i = configList.Count - 1; i >= 0; i--)
			{
				var c = configList[i];
				if (SearchSameConfig(c, abc))
				{
					if (abc.priority > c.priority)
					{
						configList[i] = abc;
					}
					else
					{
						//Debug.LogWarning (string.Format ("LoadXMLConfig conflic:key: {1}:\n{0}\n {2}", LitJson.JsonMapper.ToJson (abc), LitJson.JsonMapper.ToJson (c), LitJson.JsonMapper.ToJson (configList)));
						return;
					}
				}
			}

			configList.Add(abc);
			//			Debug.LogError ("LoadXMLConfig ss:\n"+LitJson.JsonMapper.ToJson (abc));
		}

		private static void SetUpAssetBundleName(string path, string configPath, Strategy strategy,
			string name, bool forceUpdateTag, List<string> assetsToImport)
		{
			var assetImporter = AssetImporter.GetAtPath(path);
			switch (strategy)
			{
				case Strategy.Path:
					//					assetImporter.assetBundleName = path.Substring(path.IndexOf("/") + 1);
					SetABName(assetImporter, path.Substring(path.IndexOf("/") + 1));
					break;
				case Strategy.Dir:
					//					assetImporter.assetBundleName = System.IO.Path.GetDirectoryName(path).Substring(path.IndexOf("/") + 1);
					SetABName(assetImporter, System.IO.Path.GetDirectoryName(path).Substring(path.IndexOf("/") + 1));
					break;
				case Strategy.Tag:
					var textureImporter = assetImporter as TextureImporter;
					if (textureImporter != null)
					{
						if (!string.IsNullOrEmpty(textureImporter.spritePackingTag))
						{
							if (path.StartsWith("assets/ui", System.StringComparison.OrdinalIgnoreCase))
							{
								//                                textureImporter.assetBundleName = UITextureBundlePrefix + textureImporter.spritePackingTag;
								SetABName(assetImporter, UITextureBundlePrefix + textureImporter.spritePackingTag);
							}
						}
						else
						{
							string assetBundleName = path.Substring(path.IndexOf("/") + 1);
							//							textureImporter.assetBundleName = assetBundleName;
							SetABName(assetImporter, assetBundleName);
						}
					}
					break;
				case Strategy.SetTagByPath:
					textureImporter = assetImporter as TextureImporter;
					if (textureImporter != null
						&& textureImporter.textureType == TextureImporterType.Sprite)
					{
						// 设置了NonPacktag标签的图片不默认设置packingTag
						bool needSetTag = true;
						string[] lables = AssetDatabase.GetLabels(textureImporter);
						for (int i = 0; i < lables.Length; i++)
						{
							if (lables[i].Equals("NonPacktag"))
							{
								needSetTag = false;
								break;
							}
						}
						if (!needSetTag)
						{
							SetSpriteTag(textureImporter, "");
							break;
						}

						if (!forceUpdateTag && !string.IsNullOrEmpty(textureImporter.spritePackingTag))
						{
							string bundlePath2 = textureImporter.spritePackingTag.ToLower().Replace(".", "/");
							string assetBundleName2 = (bundlePath2 + SpritePackingSuffix).ToLower();
							SetABName(assetImporter, assetBundleName2);
							break;
						}

						string bundlePath = configPath.Substring(configPath.IndexOf("/") + 1);
						string tagName = bundlePath.ToLower().Replace("/", ".");
						string assetBundleName = (bundlePath + SpritePackingSuffix).ToLower();
						if (textureImporter.assetBundleName != assetBundleName)
						{
							//                            textureImporter.assetBundleName = assetBundleName;
							SetABName(assetImporter, assetBundleName);
						}

						SetSpriteTag(textureImporter, tagName);
						//                        if (tagName != textureImporter.spritePackingTag)
						//                        {
						//                            textureImporter.spritePackingTag = tagName;
						//
						////                            if (assetsToImport != null)
						////                            {
						////                                assetsToImport.Add(path);
						////                            }
						//                        }
					}
					break;
				case Strategy.Customer:
					//					assetImporter.assetBundleName = name;
					SetABName(assetImporter, name);
					// Debug.LogFormat("new asset as {0}: {1}", name, path);
					break;
				default:
					break;
			}
		}
		public static void ForceSetAssetBundleName(string assetPath)
		{
			if (!bInit)
			{
				LoadXMLConfig();
				bInit = true;
			}
			List<string> dummyImportList = new List<string>();
			OnPostprocessAsset(assetPath, dummyImportList);
		}
		private static void OnPostprocessAsset(string assetPath, List<string> assetsToImport)    //自动获取资源路径
		{
			if (!bInit)
			{
				bInit = true;
				LoadXMLConfig();
			}
			//			return;
			if (assetPath.EndsWith(".dds", System.StringComparison.OrdinalIgnoreCase))
			{
				AssetDatabase.DeleteAsset(assetPath);
				EditorUtility.DisplayDialog("警告！", "不支持dds格式的图片,请转成“png”或者“tga”格式", "确定", "");
				Debug.LogError("不能使用*.dds文件");
			}

			foreach (var config in configList)
			{
				bool bStartWith = assetPath.StartsWith(config.path + "/", System.StringComparison.OrdinalIgnoreCase);
				//				var dir = Path.GetDirectoryName (assetPath);
				//				bStartWith = (config.path == dir);

				bool bEndWith = false;
				foreach (var sub in config.postfix.Split(new char[] { ',' }))
				{
					bEndWith |= assetPath.EndsWith(sub, System.StringComparison.OrdinalIgnoreCase);
					if (bEndWith)
						break;
				}
				if (bStartWith && bEndWith)
				{
					SetUpAssetBundleName(assetPath, config.path, config.strategy, config.name, config.forceUpdateTag, assetsToImport);
				}
			}
		}

		[UnityEditor.MenuItem("AssetBundles/SyncAssetBundleConfig")]
		public static void DepolyAssetBundleConfig()
		{
			LoadXMLConfig();
			bInit = true;

			SetABNameCacheDic.Clear();
			SetSpriteTagCacheDic.Clear();

			List<string> assetsToImport = new List<string>();
			foreach (var config in configList)
			{
				string[] assets = AssetDatabase.FindAssets("", new string[] { config.path });
				foreach (var guid in assets)
				{
					string assetName = AssetDatabase.GUIDToAssetPath(guid);

					bool bEndWith = false;
					foreach (var sub in config.postfix.Split(new char[] { ',' }))
					{
						bEndWith |= assetName.EndsWith(sub, System.StringComparison.OrdinalIgnoreCase);
						if (bEndWith)
							break;
					}
					if (bEndWith)
					{
						SetUpAssetBundleName(assetName, config.path, config.strategy, config.name, config.forceUpdateTag, assetsToImport);
					}
				}
				AssetDatabase.SaveAssets();
			}
			Debug.LogError("SetABNameCacheDic:" + SetABNameCacheDic.Count);
			foreach (var item in SetABNameCacheDic)
			{
				//				item.Key
				var ai = AssetImporter.GetAtPath(item.Key);
				if (ai != null)
				{
					ai.assetBundleName = item.Value;
				}
			}
			Debug.LogError("SetSpriteTagCacheDic:" + SetSpriteTagCacheDic.Count);
			foreach (var item in SetSpriteTagCacheDic)
			{
				//				item.Key
				var ai = AssetImporter.GetAtPath(item.Key) as TextureImporter;
				if (ai != null)
				{
					ai.spritePackingTag = item.Value;
					assetsToImport.Add(item.Key);
				}
			}

			ImportAssets(assetsToImport.ToArray());
			SetABNameCacheDic.Clear();
			SetSpriteTagCacheDic.Clear();
		}

		private static void ImportAssets(string[] paths)
		{
			// When using the cache server we have to write all import settings to disk first.
			// Then perform the import (Otherwise the cache server will not be used for the import)
			foreach (string path in paths)
				AssetDatabase.WriteImportSettingsIfDirty(path);

			try
			{
				AssetDatabase.StartAssetEditing();
				foreach (string path in paths)
					AssetDatabase.ImportAsset(path);
			}
			finally
			{
				AssetDatabase.StopAssetEditing();
			}
		}


		static Dictionary<string, string> SetABNameCacheDic = new Dictionary<string, string>();
		static Dictionary<string, string> SetSpriteTagCacheDic = new Dictionary<string, string>();

		static void SetABName(AssetImporter ai, string abName)
		{
			var hCode = AssetDatabase.GetAssetPath(ai.GetInstanceID());
			string str = null;
			if (
				SetABNameCacheDic.TryGetValue(hCode, out str) == false
			)
			{
				str = ai.assetBundleName;
			}

			if (string.Compare(str, abName, true) != 0)
			{
				SetABNameCacheDic[hCode] = abName;
			}
			//			Debug.LogError ("SetABName Path:"+str + " "+abName);
		}
		static void SetSpriteTag(TextureImporter ai, string tag)
		{
			var hCode = AssetDatabase.GetAssetPath(ai.GetInstanceID());
			string str = null;
			if (
				SetSpriteTagCacheDic.TryGetValue(hCode, out str) == false
			)
			{
				str = ai.spritePackingTag;
			}
			if (tag.ToLower() != str)
			{
				SetSpriteTagCacheDic[hCode] = tag;
			}
			//			Debug.LogError ("SetSpriteTag Path:"+str + " "+tag);
		}
	}
}