using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using LitJson;
using Newtonsoft.Json;

public class CheckResultData
{
    /// <summary>
    /// �Ƿ����c#����
    /// </summary>
    public bool isExit { get; set; }
    /// <summary>
    /// ���ü����
    /// </summary>
    public List<(string, string, string)> resultList;
    /// <summary>
    /// ���ֲ�ͬ�����б�
    /// </summary>
    static Dictionary<string, List<string>> differentPairs;
    public CheckResultData(bool _isExit, List<(string, string, string)> _resultList, Dictionary<string, List<string>> _differentPairs)
    {
        isExit = _isExit;
        resultList = _resultList;
        differentPairs = _differentPairs;
    }
}
public class CheckCSharpMethodExist
{
    // ��ӵ�ǰ�������Ƶ�����
    static Dictionary<string, List<string>> methodPairs = new Dictionary<string, List<string>>();

    private static string luaFolderPath = Application.dataPath + "/Lua";

    private static string jsonFilePath = "/../methodData.json";

    private static string checkResultPath = "/../checkResult.json";

    static string[] dll =
    {
        "Base",
        "Battle",
        "Common",
        "Controller",
        "Game",
        "Render",
        "Scene",
        "Script",
        "UI",
        null,
    };
    static string[] unCheckList =
    {
        "proto",
        "Table"
    };
    static public void CheckDllPathMethod()
    {
        
        foreach (string dllName in dll)
        {
            string dllPath = null;
            if(dllName != null)
            {
                dllPath = Application.dataPath + "/Scripts/" + dllName + ".dll";
            }
            
            CheckCSharpMethodIsExist(dllPath);
        }
    }
    [MenuItem("Tools/ConductDllMethodData")]
    static public void ConductDllMethodData()
    {
        methodPairs.Clear();
        CheckDllPathMethod();
        try
        {
            string jsonString = JsonConvert.SerializeObject(methodPairs, Formatting.Indented);
            File.WriteAllText(jsonFilePath, jsonString);
        }
        catch (Exception ex)
        {
            Debug.LogError($"���� JSON �ļ�ʱ����: {ex.Message}");
        }
    }
    private static bool IsRuningTool()
    {
        DateTime today = DateTime.Now;
        if (today.DayOfWeek == DayOfWeek.Thursday)
        {
            return false;
        }

        if (File.Exists(checkResultPath))
        {
            string resultStr = File.ReadAllText(checkResultPath);
            CheckResultData resultData = JsonConvert.DeserializeObject<CheckResultData>(resultStr);
            if (resultData.resultList == null || resultData.resultList.Count < 0)
                return true;
            else
                return false;
        }
        else
        {
            return false;
        }
    }
    [MenuItem("Tools/CompareDllMethodData")]
    static public void CompareDllMethodData()
    {
        //����ǽ������Ľ�棬�����ϴμ���Ǵ������������м�⣬��û���⣬��ֻ����ÿ���Ľ��м�⣬�������ʱ�����ÿ������
        if (IsRuningTool())
        {
            return;
        }
        //����Ƿ����json�ļ�
        if (!File.Exists(jsonFilePath)) 
        {
            ConductDllMethodData();
        }
        string jsonString = File.ReadAllText(jsonFilePath);
        Dictionary<string, List<string>> oldMethodPairs = JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(jsonString);
        if (oldMethodPairs == null)
            return;
        //����Ȼ�󴴽��µķ����ֵ�
        methodPairs.Clear();
        CheckDllPathMethod();
        //�Ա��ֵ�Ĳ����ļ�
        Dictionary<string, List<string>> _diffrentMethod = new Dictionary<string, List<string>>();
        foreach (var item in methodPairs)
        {
            List<string> methods = item.Value;
            for (int i = 0; i < methods.Count; i++)
            {
                List<string> oldMethods = oldMethodPairs[item.Key];
                if(oldMethods != null)
                {
                    if (!oldMethods.Contains(methods[i]))
                    {
                        List<string> saveList = null;
                        if(!_diffrentMethod.TryGetValue(item.Key, out saveList))
                        {
                            saveList = new List<string>();
                            _diffrentMethod.Add(item.Key, saveList);
                        }
                        saveList.Add(methods[i]);
                    }
                }
                else
                {
                    _diffrentMethod.Add( item.Key, item.Value);
                }
            }
        }

        if(_diffrentMethod.Count > 0)
        {
            if(CheckIsUseCsharpMethod(_diffrentMethod))
            {
                //todo ֪ͨ���ɹ������� ����lua��ʹ����
                //Debug.LogError("����������");
            }
            else
            {
                //������������δ��lua������
                ConductDllMethodData();
            }
        }
        else
        {
            CheckResultData data = new CheckResultData(false, null, null);
            string resultString = JsonConvert.SerializeObject(data, Formatting.Indented);
            File.WriteAllText(checkResultPath, resultString);
            //����������
            ConductDllMethodData();
        }
    }
    static public void CheckCSharpMethodIsExist(string dllPath)
    {
        // ���� DLL
        Assembly assembly = null;
        if (dllPath == null)
        {
            assembly = Assembly.GetExecutingAssembly();
        }
        else
        {
            // ȷ�� DLL ����
            if (!File.Exists(dllPath))
            {
                Debug.LogError("The specified DLL does not exist.");
                return;
            }
            assembly = Assembly.LoadFrom(dllPath);
        }
        // ��ȡ��������
        Type[] types = assembly.GetTypes();
        foreach (Type type in types)
        {
            List<string> methodList = null; 
            if(!methodPairs.TryGetValue(type.Name,out methodList))
            {
                methodList = new List<string>();
                methodPairs.Add(type.Name, methodList);
            }
            // ��ȡ���з���
            MethodInfo[] methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.Static | BindingFlags.DeclaredOnly);
            foreach (MethodInfo method in methods)
            {
                if(!methodList.Contains(method.Name))
                    methodList.Add(method.Name);
            }
        }
    }
    private static bool IsContainMethod(string luaPath,string typeName,string methodName)
    {
        string filePath = luaPath; // �滻Ϊ���txt�ļ�·��
        string fileContent = File.ReadAllText(filePath);
        string patternA = Regex.Escape(typeName);
        string patternB = Regex.Escape(methodName);
        string combinedPattern = $"(?=.*{patternA})(?=.*{patternB})";
        bool containsBoth = Regex.IsMatch(fileContent, combinedPattern);
        return containsBoth;
    }
    public static bool CheckIsUseCsharpMethod(Dictionary<string, List<string>> _diffrentMethod)
    {
        EditorUtility.DisplayProgressBar("Processing", "Starting...", 0f);
        Dictionary<string,List<string>> diffrentMethod = _diffrentMethod;
        List<(string, string,string)> checkList = new List<(string, string, string)>();
        string folderName = luaFolderPath;
        bool isUsed = false;
        if (folderName != null)
        {
            List<FileInfo> files = new List<FileInfo>();
            List<FileInfo> filesList = GetFile(folderName, ".txt", files);
            for (int i = 0; i < filesList.Count; i++)
            {
                FileInfo info = filesList[i];
                bool isPass = false;
                for (int j = 0; j < unCheckList.Length; j++)
                {
                    if (info.DirectoryName.Contains(unCheckList[j]))
                    {
                        isPass = true;
                        break;
                    }
                }
                if (isPass)
                    continue;
                foreach (var item in diffrentMethod)
                {
                    List<string> methodList = item.Value;
                    for (int j = 0; j < methodList.Count; j++)
                    {
                        if (IsContainMethod(info.DirectoryName + "/" + info.Name, item.Key, methodList[j]))
                        {
                            isUsed = true;
                            if (!CheckLineIsContainMethod(info.DirectoryName + "/" + info.Name, item.Key, methodList[j]))
                                checkList.Add((info.DirectoryName, item.Key, methodList[j]));
                        }
                    }
                }
                // ���½�����
                float progress = (float)(i + 1) / filesList.Count;
                EditorUtility.DisplayProgressBar("Processing", $"Processing step {i + 1} of {filesList.Count}��fileName:{info.DirectoryName + "/" +info.Name}", progress);
            }
        }
        // ��ɺ�رս�����
        EditorUtility.ClearProgressBar();
        if (isUsed)
        {
            CheckResultData data;
            if (checkList.Count > 0)
            {
                //������������û�����пյĺ���
                data = new CheckResultData(true,checkList,diffrentMethod);
            }
            else
            {
                //���������������пգ���������һ�����ս����˹����
                //�����ɲ��ԣ�Ĭ�������пյľ�ͨ������ˢ�·�������
                data = new CheckResultData(true, null, diffrentMethod);
                ConductDllMethodData();
            }
            string jsonString = JsonConvert.SerializeObject(data, Formatting.Indented);
            File.WriteAllText(checkResultPath, jsonString);
            return true;
        }
        else
        {
            return false;
        }
    }
    /// <summary>
    /// ���luatxt���Ƿ����ĳ����ķ�������������ж�����txt���Ƿ���дif then���жϣ�ֻ��һ��ifthen�ж�Ҳ��ɹ�
    /// </summary>
    /// <param name="path">·��</param>
    /// <param name="typeName">����</param>
    /// <param name="methodName">�෽��</param>
    /// <returns></returns>
    private static bool CheckLineIsContainMethod(string path, string typeName, string methodName)
    {
        try
        {
            // ��ȡ������
            string[] lines = File.ReadAllLines(path);
            //��������ƺ��෽��
            string patternA = Regex.Escape(typeName);
            string patternB = Regex.Escape(methodName);
            string combinedPattern = $"(?=.*{patternA})(?=.*{patternB})";
            //���if then �Ƿ������п�
            string patternC = Regex.Escape("if");
            string patternD = Regex.Escape("then");
            string combinedDetermine = $"(?=.*{patternC})(?=.*{patternD})";
            // ��������Ƿ���Ч
            for (int i = 0; i < lines.Length; i++)
            {
                if (Regex.IsMatch(lines[i], combinedPattern) && Regex.IsMatch(lines[i], combinedDetermine))
                {
                    return true;
                }
                else
                {
                    Console.WriteLine("������������Χ��");
                }
            }
            
        }
        catch (IOException e)
        {
            Console.WriteLine($"��ȡ�ļ�ʱ����: {e.Message}");
        }
        return false;
    }
    /// <summary>
    /// ���Ŀ¼�������ļ���ָ���ļ������ļ�(�����������ļ���)
    /// </summary>
    /// <param name="path">�ļ���·��</param>
    /// <param name="extName">��չ�����Զ�� ���� .mp3.wma.rm</param>
    /// <returns>List<FileInfo></returns>
    public static List<FileInfo> GetFile(string path, string extName, List<FileInfo> lst)
    {
        try
        {
            string[] dir = Directory.GetDirectories(path); //�ļ����б�  
            DirectoryInfo fdir = new DirectoryInfo(path);
            FileInfo[] file = fdir.GetFiles();
            //FileInfo[] file = Directory.GetFiles(path); //�ļ��б�  
            if (file.Length != 0 || dir.Length != 0) //��ǰĿ¼�ļ����ļ��в�Ϊ��          
            {
                foreach (FileInfo f in file) //��ʾ��ǰĿ¼�����ļ�  
                {
                    if (extName.ToLower().IndexOf(f.Extension.ToLower()) >= 0)
                    {
                        lst.Add(f);
                    }
                }
                foreach (string d in dir)
                {
                    GetFile(d, extName, lst);//�ݹ�  
                }
            }
            return lst;
        }
        catch (Exception ex)
        {
            throw ex;
        }
    }
}
