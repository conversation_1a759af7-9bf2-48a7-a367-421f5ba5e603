using EditorUtilitys;
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public partial class HeroResPreview : EditorWindow
{
    /// <summary>
    /// 检测出来的不符合规范的资源字典
    /// </summary>
    Dictionary<int, string> resNotStdDic = new Dictionary<int, string>();
    /// <summary>
    /// 检测出来的不符合规范的资源
    /// </summary>
    List<UnityEngine.Object> resNotStdList = new List<UnityEngine.Object>();
    /// <summary>
    /// 不符合规范资源总大小
    /// </summary>
    long resNotSize = 0;

    /// <summary>
    /// 导出选择项设置
    /// </summary>
    void DrawExportOptionSetting()
    {
        EditorGUILayout.Space();
        EditorGUILayout.Separator();

        GUILayout.Label("资源设置优化选项", EditorStyles.boldLabel);

        optimalChaTex = EditorGUILayout.ToggleLeft("优化英雄贴图", optimalChaTex);
        optimalChaAnim = EditorGUILayout.ToggleLeft("优化英雄动画", optimalChaAnim);
        optimalChaEffectTex = EditorGUILayout.ToggleLeft("优化英雄技能特效贴图", optimalChaEffectTex);
    }

    /// <summary>
    /// Stand,Show动画压缩设置
    /// </summary>
    void DrawStandShowAniSetting()
    {
        EditorGUILayout.BeginHorizontal();
        optimalStandShowAni = EditorGUILayout.ToggleLeft("Show,Stand动画压缩", optimalStandShowAni);
        if(optimalStandShowAni)
        {
            GUI.SetNextControlName("inputSSAnimTextShow");
            inputSSAnimText = EditorGUILayout.TextField("动画压缩阈值", inputSSAnimText);
            if (!float.TryParse(inputSSAnimText, out ssAnimThreshold))
            {
                GUI.FocusControl("inputSSAnimTextShow");
                ssAnimThreshold = 0.1f;
                inputSSAnimText = ssAnimThreshold.ToString();
                GUI.FocusControl("");
            }
        }
        HeroResOptimal.SetOptimalStandShowAni(optimalStandShowAni);
        EditorGUILayout.EndHorizontal();
    }

    /// <summary>
    /// 清除信息
    /// </summary>
    void ClearHeroResNotStdInfo()
    {
        resNotStdDic.Clear();
        resNotStdList.Clear();
        resNotSize = 0;
    }

    /// <summary>
    /// 添加英雄不规范信息
    /// </summary>
    /// <param name="obj"></param>
    /// <param name="strTips"></param>
    void AddHeroResNotStdInfo(UnityEngine.Object obj, string strTips)
    {
        if(obj != null)
        {
            var instanceID = obj.GetInstanceID();
            resNotStdDic[instanceID] = strTips;
            resNotStdList.Add(obj);

            if(fileSizeDic.TryGetValue(instanceID, out var value))
            {
                resNotSize += value;
                resSizeDic[Page.Warn.ToString()] = resNotSize;
            }
        }
    }

    /// <summary>
    /// 计算不符合规范资源信息
    /// </summary>
    void CalHeroResNotStdInfo()
    {
        ClearHeroResNotStdInfo();
        if (resultList.Count < 1)
        {
            return;
        }

        CheckHeroResStandard();
    }

    /// <summary>
    /// 绘制英雄资源提示信息
    /// </summary>
    void DrawHeroResTipsInfo()
    {
        EditorGUILayout.Space();
        EditorGUILayout.Space();

        GUIStyle style = new GUIStyle(EditorStyles.helpBox);
        style.richText = true;
        style.fontSize = 18;

        if (resNotStdList.Count > 0)
        {
            EditorGUILayout.TextArea("<b><color=red>英雄资源规范异常(可以点击下方Warn栏,查看详细信息)</color></b>", style);
        }
        else
        {
            EditorGUILayout.TextArea("<b>英雄资源规范正常</b>", style);
        }

        EditorGUILayout.Space();
        EditorGUILayout.Space();
    }

    /// <summary>
    /// 绘制英雄资源不合规范信息描述
    /// </summary>
    /// <param name="obj"></param>
    void DrawHeroResNotStdDesc(UnityEngine.Object obj)
    {
        if(obj != null)
        {
            var instanceID = obj.GetInstanceID();
            if(resNotStdDic.TryGetValue(instanceID, out var desc))
            {
                GUIStyle redLabelStyle = new GUIStyle(EditorStyles.label);
                redLabelStyle.richText = true;
                var strContent = $"<color=red>{desc}</color>";
                EditorGUILayout.LabelField(strContent, redLabelStyle);
            }
            else
            {
                EditorGUILayout.LabelField("资源规范正常");
            }
        }
    }

    /// <summary>
    /// 检测英雄资源
    /// </summary>
    void CheckHeroResFinal()
    {
        if(resList != null)
        {
            if(HeroResOptimal.animFbxNeedOptimal)
            {
                CheckFbxAnimationPath();
            }
        }
    }

    /// <summary>
    /// 检测英雄动画fbx路径
    /// </summary>
    void CheckFbxAnimationPath()
    {
        List<string> processedAssetsList = new List<string>();
        List<string> heroAnimFbxList = new List<string>();
        foreach (var t in resList)
        {
            string directoryPath = Path.GetDirectoryName(t).Replace("\\", "/");
            if (processedAssetsList.Contains(directoryPath))
            {//已处理过该目录
                continue;
            }

            if (t.EndsWith(HeroResOptimal.CAnimationExtension))
            {
                if (HeroResOptimal.IsCharacterAnimPath(t))
                {
                    processedAssetsList.Add(directoryPath);
                    List<string> fbxList = new List<string>();
                    FileUtilitys.GetFilesOnDir(directoryPath, fbxList, HeroResOptimal.CFbxExtension);

                    foreach (var fbx in fbxList)
                    {
                        var fbxPath = FileUtilitys.GetAssetPathOnFullPath(fbx);
                        if (HeroResOptimal.IsFilterAniName(fbxPath))
                        {
                            continue;
                        }

                        heroAnimFbxList.Add(fbxPath);
                    }
                }
            }
        }

        if(heroAnimFbxList.Count > 0)
        {
            resList.AddRange(heroAnimFbxList);
        }
    }

    /// <summary>
    /// 检测英雄资源规范
    /// </summary>
    void CheckHeroResStandard()
    {
        HeroResOptimal.DoCheckConfig();
        CheckHeroAnimationFbx();
        CheckCharacterAnimCurveFloatAccu();
        CheckHeroModelTexture();
        CheckHeroEffectTexture();
    }

    /// <summary>
    /// 检测英雄动画fbx
    /// </summary>
    void CheckHeroAnimationFbx()
    {
        if (resultGoList != null && HeroResOptimal.animFbxNeedOptimal)
        {
            foreach (var t in resultGoList)
            {
                var instanceID = t.GetInstanceID();
                if (!resNotStdDic.TryGetValue(instanceID, out var result))
                {
                    var assetPath = AssetDatabase.GetAssetPath(t).Replace("\\", "/");
                    if(assetPath.EndsWith(".fbx") || assetPath.EndsWith(".FBX"))
                    {
                        if(HeroResOptimal.IsCharacterAnimPath(assetPath) && !HeroResOptimal.IsFilterAniName(assetPath))
                        {
                            ModelImporter modelImporter = AssetImporter.GetAtPath(assetPath) as ModelImporter;
                            if (modelImporter != null)
                            {
                                var animationCompression = modelImporter.animationCompression;
                                var animationRotationError = modelImporter.animationRotationError;
                                var animationPositionError = modelImporter.animationPositionError;
                                var animationScaleError = modelImporter.animationScaleError;

                                string strDesc = string.Empty;
                                if (animationCompression != animCompress)
                                {
                                    strDesc += $"动画压缩格式不为{animCompress}";
                                }

                                var animThresholdT = Mathf.Abs(animThreshold);
                                if(HeroResOptimal.IsSSAniName(assetPath))
                                {
                                    animThresholdT = Mathf.Abs(ssAnimThreshold);
                                }

                                if (!HeroResOptimal.AreEqual(animationRotationError, animThresholdT) || !HeroResOptimal.AreEqual(animationPositionError, animThresholdT) || 
                                    !HeroResOptimal.AreEqual(animationScaleError, animThresholdT))
                                {
                                    if(!string.IsNullOrEmpty(strDesc))
                                    {
                                        strDesc += ",";
                                    }

                                    strDesc += $"动画压缩阈值不为{animThresholdT}";
                                }

                                if(!string.IsNullOrEmpty(strDesc))
                                {
                                    AddHeroResNotStdInfo(t, strDesc);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 检测角色动画曲线浮点数精度
    /// </summary>
    /// <returns></returns>
    void CheckCharacterAnimCurveFloatAccu()
    {
        if(resultAnimationList != null && HeroResOptimal.animFbxNeedOptimal)
        {
            foreach(var t in resultAnimationList)
            {
                var instanceID = t.GetInstanceID();
                if(!resNotStdDic.TryGetValue(instanceID, out var result))
                {
                    var assetPath = AssetDatabase.GetAssetPath(t).Replace("\\", "/");
                    if (assetPath.EndsWith(HeroResOptimal.CAnimationExtension))
                    {
                        if (HeroResOptimal.IsCharacterAnimPath(assetPath))
                        {
                            if(!HeroResOptimal.IsFilterAniName(assetPath))
                            {
                                if(!CheckAnimCurveFloatAccuSatisfy(assetPath))
                                {
                                    AddHeroResNotStdInfo(t, "动画曲线的浮点数精度大于3位");
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取浮点数小数位数
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    int GetFloatDigitNum(float number)
    {
        int digitNum = 0;
        try
        {
            digitNum = BitConverter.GetBytes(decimal.GetBits((decimal)number)[3])[2];
        }
        catch (Exception)
        {

        }

        return digitNum;
    }

    /// <summary>
    /// 检测动画曲线浮点数精度是否满足
    /// </summary>
    /// <param name="assetPath"></param>
    /// <returns></returns>
    bool CheckAnimCurveFloatAccuSatisfy(string assetPath)
    {
        AssetDatabase.Refresh();
        Resources.UnloadUnusedAssets();
        System.GC.Collect();
        AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(assetPath);
        if (clip != null)
        {
            AnimationClipCurveData[] curves = null;
            curves = AnimationUtility.GetAllCurves(clip);
            Keyframe key;
            Keyframe[] keyFrames;

            if (curves != null && curves.Length > 0)
            {
                for (int ii = 0; ii < curves.Length; ++ii)
                {
                    AnimationClipCurveData curveDate = curves[ii];
                    if (curveDate.curve == null || curveDate.curve.keys == null)
                    {
                        continue;
                    }

                    keyFrames = curveDate.curve.keys;
                    for (int i = 0; i < keyFrames.Length; i++)
                    {
                        key = keyFrames[i];
                        if (!CompareDightNumerLessEqualSatisfy(GetFloatDigitNum(key.value), 3))
                        {
                            return false;
                        }

                        if (!CompareDightNumerLessEqualSatisfy(GetFloatDigitNum(key.inTangent), 3))
                        {
                            return false;
                        }

                        if (!CompareDightNumerLessEqualSatisfy(GetFloatDigitNum(key.outTangent), 3))
                        {
                            return false;
                        }

                        if (!CompareDightNumerLessEqualSatisfy(GetFloatDigitNum(key.inWeight), 3))
                        {
                            return false;
                        }

                        if (!CompareDightNumerLessEqualSatisfy(GetFloatDigitNum(key.outWeight), 3))
                        {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    /// <summary>
    /// 比较小数点位数
    /// </summary>
    /// <param name="sDigitNum"></param>
    /// <param name="digitNum"></param>
    /// <returns></returns>
    bool CompareDightNumerLessEqualSatisfy(int sDigitNum, int digitNum)
    {
        return sDigitNum <= digitNum;
    }

    /// <summary>
    /// 检测英雄模型贴图
    /// </summary>
    void CheckHeroModelTexture()
    {
        if (resultTextureList != null)
        {
            int heroId = ReadHeroData(inputText);
            foreach (var t in resultTextureList)
            {
                var instanceID = t.GetInstanceID();
                if (!resNotStdDic.TryGetValue(instanceID, out var result))
                {
                    var assetPath = AssetDatabase.GetAssetPath(t).Replace("\\", "/");
                    int texW = 0;
                    int texH = 0;
                    if (HeroResOptimal.OptimalHeroModelTexture((uint)heroId, assetPath, GetOptimalOption(), false))
                    {
                        GetTexWH(assetPath, ref texW, ref texH);
                        AddHeroResNotStdInfo(t, $"英雄贴图尺寸{texW}X{texH}大于{(int)characterMaxSize}X{(int)characterMaxSize}");
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取贴图长宽
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="texW"></param>
    /// <param name="texH"></param>
    void GetTexWH(string assetPath, ref int texW, ref int texH)
    {
        texW = 0;
        texH = 0;
        if(!string.IsNullOrEmpty(assetPath))
        {
            var t2d = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
            if (t2d != null)
            {
                texW = t2d.width;
                texH = t2d.height;
            }
        }
    }

    /// <summary>
    /// 检测英雄特效贴图
    /// </summary>
    void CheckHeroEffectTexture()
    {
        if (resultTextureList != null)
        {
            foreach (var t in resultTextureList)
            {
                var instanceID = t.GetInstanceID();
                if (!resNotStdDic.TryGetValue(instanceID, out var result))
                {
                    var assetPath = AssetDatabase.GetAssetPath(t).Replace("\\", "/");
                    int texW = 0;
                    int texH = 0;
                    if (HeroResOptimal.OptimalHeroEffectTexture(0, assetPath, GetOptimalOption(), false))
                    {
                        GetTexWH(assetPath, ref texW, ref texH);
                        AddHeroResNotStdInfo(t, GetEffectTextureDesc(assetPath, texW, texH));
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取特效贴图描述
    /// </summary>
    /// <param name="assetPath"></param>
    /// <param name="texW"></param>
    /// <param name="texH"></param>
    /// <returns></returns>
    string GetEffectTextureDesc(string assetPath, int texW, int texH)
    {
        string strEffectDesc = string.Empty;
        var effectTypeE = HeroResOptimal.GetEffectType(assetPath);
        var effectDesc = HeroResOptimal.GetEffectTypeDesc(effectTypeE);
        int width = (int)generalMaxSize;
        int height = width;
        if (effectTypeE.Equals(HeroResOptimal.EffectTypeE.Seq))
        {
            width = (int)xuliezhenMaxSize;
            height = width;
        }
        else if (effectTypeE.Equals(HeroResOptimal.EffectTypeE.Noise))
        {
            width = (int)noiseMaxSize;
            height = width;
        }
        else if (effectTypeE.Equals(HeroResOptimal.EffectTypeE.glow))
        {
            width = (int)guangyunMaxSize;
            height = width;
        }
        else if (effectTypeE.Equals(HeroResOptimal.EffectTypeE.mask))
        {
            width = (int)maskMaxSize;
            height = width;
        }

        strEffectDesc = $"特效{effectDesc}贴图尺寸{texW}X{texH}大于{(int)width}X{(int)height}";
        return strEffectDesc;
    }

    /// <summary>
    /// 对选定Asset资源路径判断
    /// </summary>
    /// <returns></returns>
    bool CheckTheAssetOptimizationPath()
    {
        if (targetObj == null)
        {
            EditorUtility.DisplayDialog("提示", "选定Asset资源为空!!!!!!", "确定");
            return false;
        }

        HeroResOptimal.DoCheckConfig();

        bool pathSatisfy = false;
        UnityEngine.Object[] objects = EditorUtility.CollectDependencies(new UnityEngine.Object[1] { targetObj });
        if (objects != null)
        {
            for (int i = 0; i < objects.Length; i++)
            {
                if (objects[i] != null)
                {
                    string targetPath = AssetDatabase.GetAssetPath(objects[i]);
                    var isFbxAnimPath = HeroResOptimal.IsHeroFbxAnimPath(targetPath);
                    var isHeroAnimPath = HeroResOptimal.IsHeroAnimPath(targetPath);
                    var isHeroTexPath = HeroResOptimal.IsHeroTexPath(targetPath);
                    var isEffectTexPath = HeroResOptimal.IsEffectTexPath(targetPath);
                    if (isFbxAnimPath || isHeroAnimPath || isHeroTexPath || isEffectTexPath)
                    {
                        pathSatisfy = true;
                        break;
                    }
                }
            }
        }

        if (!pathSatisfy)
        {
            EditorUtility.DisplayDialog("提示", "当前选中Asset资源以及它的依赖资源不满足以下优化路径:\n1.角色动画Fbx路径\n2.角色anim路径\n3.角色贴图路径\n4.特效贴图路径", "确定");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 绘制指定英雄ID列表优化
    /// </summary>
    void DrawDoHeroIDListOptimal()
    {
        inputHeroIdList = EditorGUILayout.TextField("输入英雄id列表(,分割)", inputHeroIdList);

        if (GUILayout.Button("指定英雄ID列表优化"))
        {
            if(string.IsNullOrEmpty(inputHeroIdList))
            {
                EditorUtility.DisplayDialog("提示", "英雄ID列表数据为空", "确定");
                return;
            }

            var heroIdArr = inputHeroIdList.Split(new char[] { ','});
            if(heroIdArr == null)
            {
                EditorUtility.DisplayDialog("提示", "英雄ID列表数据输入异常", "确定");
                return;
            }

            List<int> heroIdList = new List<int>();
            foreach(var t in heroIdArr)
            {
                if(!string.IsNullOrEmpty(t) && int.TryParse(t, out var result))
                {
                    heroIdList.Add(result);
                }
            }

            if(heroIdList.Count > 0)
            {
                DoSomeOptimal(heroIdList);
            }
            else
            {
                EditorUtility.DisplayDialog("提示", "英雄ID列表数据输入异常", "确定");
                return;
            }
        }
    }
}

