using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public partial class HeroResPreview : EditorWindow
{
    #region Params
    private List<string> resList = new List<string>();
    private List<Object> resultList = new List<Object>();
    private List<Object> resultTextureList = new List<Object>();
    private List<Object> resultAnimationList = new List<Object>();
    private List<Object> resultMeshList = new List<Object>();
    private List<Object> resultShaderList = new List<Object>();
    private List<Object> resultAduioClipList = new List<Object>();
    private List<Object> resultScriptList = new List<Object>();
    private List<Object> resultMaterialList = new List<Object>();
    private List<Object> resultOtherList = new List<Object>();
    private List<Object> resultGoList = new List<Object>();
    private Dictionary<int, long> fileSizeDic = new Dictionary<int, long>();
    private Dictionary<string, long> resSizeDic = new Dictionary<string, long>();
    private string displayText = "";
    private Vector2 scrollPosition;
    public Object targetObj;
    private Page childPage = Page.All;
    private string inputText = "";
    private string curHeroName;
    private string inputHeroIdList = "";

    private enum Page
    {
        All,
        Warn,
        Texture,
        Animation,
        Mesh,
        AudioClip,
        Script,
        Shader,
        Material,
        GameObject,
        Other
    }

    #region OpenParam

    private ModelImporterAnimationCompression animCompress = ModelImporterAnimationCompression.Optimal;
    private string inputAnimText = "2.0";
    private int animSelectedIndex = 0;
    private int characterTexSelectedIndex = 0;
    private int generalTexSelectedIndex = 1;
    private int xuliezhenTexSelectedIndex = 0;
    private int noiseTexSelectedIndex = 3;
    private int guangyunTexSelectedIndex = 2;
    private int maskTexSelectedIndex = 1;

    private string[] animOptions = { "Optimal", "KeyframeReduction", "Off" };
    private string[] textureOptions = { "512", "256", "128", "64", "1024", "2048" };
    private int[] textureOptionss = { 512, 256, 128, 64, 1024, 2048 };
    private float animThreshold = 2.0f;
    private float characterMaxSize = 512;
    private float generalMaxSize = 256;
    private float xuliezhenMaxSize = 512;
    private float noiseMaxSize = 64;
    private float guangyunMaxSize = 128;
    private float maskMaxSize = 256;
    bool showAnimContent = false;
    /// <summary>
    /// 显示英雄ID内容
    /// </summary>
    bool showHeroIDContent = false;
    /// <summary>
    /// 优化角色贴图
    /// </summary>
    bool optimalChaTex = true;
    /// <summary>
    /// 优化角色动画
    /// </summary>
    bool optimalChaAnim = true;
    /// <summary>
    /// 优化角色技能特效贴图
    /// </summary>
    bool optimalChaEffectTex = true;
    /// <summary>
    /// 优化角色贴图字符串
    /// </summary>
    string optimalChaTexStr = "optimalChaTexExport";
    /// <summary>
    /// 优化角色动画字符串
    /// </summary>
    string optimalChaAnimStr = "optimalChaAnimExport";
    /// <summary>
    /// 优化角色技能特效贴图字符串
    /// </summary>
    string optimalChaEffectTexStr = "optimalChaEffectTexExport";
    /// <summary>
    /// 优化show,stand动画设置
    /// </summary>
    bool optimalStandShowAni = false;
    /// <summary>
    /// stand show动画压缩阈值
    /// </summary>
    float ssAnimThreshold = 0.1f;
    /// <summary>
    ///  stand show动画压缩阈值字符串
    /// </summary>
    private string inputSSAnimText = "0.1";
    /// <summary>
    /// 优化角色show,stand动画字符串
    /// </summary>
    string optimalSSAniStr = "optimalStandShowAniExport";
    #endregion

    #endregion

    [MenuItem("Window/ResPreview/HeroResPreview")]
    public static void ShowWindow()
    {
        EditorWindow.GetWindow(typeof(HeroResPreview));
    }

    private void OnEnable()
    {
        HeroResOptimal.DoCheckConfig();
        HeroResOptimal.OnEnable();

        if(HeroResOptimal.projectMarkE == HeroResOptimal.EProjectMark.WeimianZhugan)
        {//如果是位面
            characterTexSelectedIndex = 4;
            characterMaxSize = 1024;
            noiseTexSelectedIndex = 0;
            noiseMaxSize = 512;
        }

        //资源优化导出选项
        optimalChaTexStr = (Application.dataPath + "/" + optimalChaTexStr).Replace("\\", "/");
        optimalChaAnimStr = (Application.dataPath + "/" + optimalChaAnimStr).Replace("\\", "/");
        optimalChaEffectTexStr = (Application.dataPath + "/" + optimalChaEffectTexStr).Replace("\\", "/");

        optimalChaTex = EditorPrefs.GetBool(optimalChaTexStr, true);
        optimalChaAnim = EditorPrefs.GetBool(optimalChaAnimStr, true);
        optimalChaEffectTex = EditorPrefs.GetBool(optimalChaEffectTexStr, true);

        //优化角色show,stand动画字符串
        optimalSSAniStr = (Application.dataPath + "/" + optimalSSAniStr).Replace("\\", "/");
        optimalStandShowAni = EditorPrefs.GetBool(optimalSSAniStr, false);
        HeroResOptimal.SetOptimalStandShowAni(optimalStandShowAni);
    }

    private void OnFocus()
    {
        SavePrefs();
    }

    void SavePrefs()
    {
        EditorPrefs.SetBool(optimalChaTexStr, optimalChaTex);
        EditorPrefs.SetBool(optimalChaAnimStr, optimalChaAnim);
        EditorPrefs.SetBool(optimalChaEffectTexStr, optimalChaEffectTex);
        EditorPrefs.SetBool(optimalSSAniStr, optimalStandShowAni);
    }

    private void OnDisable()
    {
        SavePrefs();
    }

    #region OnGUI

    private void OnGUI()
    {
        GUILayout.Label("HeroResPreview Window", EditorStyles.boldLabel);
        showAnimContent = EditorGUILayout.BeginFoldoutHeaderGroup(showAnimContent, "资源修改参数");
        if (showAnimContent)
        {
            DrawExportOptionSetting();
            DrawAnimSetting();
            DrawStandShowAniSetting();
            DrawTextureSetting();
            EditorGUILayout.BeginHorizontal();
            if(showHeroIDContent)
            {
                if (GUILayout.Button("单个英雄资源优化"))
                {
                    DoOptimal();
                }
            }
            else
            {
                if (GUILayout.Button("选定Asset资源优化"))
                {
                    if(targetObj == null)
                    {
                        EditorUtility.DisplayDialog("提示", "选定Asset资源为空!!!!!!", "确定");
                        return;
                    }

                    if(!CheckTheAssetOptimizationPath())
                    {
                        return;
                    }

                    DoSelAssetOptimal(AssetDatabase.GetAssetPath(targetObj));
                }
            }

            if (GUILayout.Button("所有英雄优化"))
            {
                DoAllOptimal();
            }
            EditorGUILayout.EndHorizontal();
            DrawDoHeroIDListOptimal();
        }
        EditorGUILayout.EndFoldoutHeaderGroup();
        EditorGUILayout.Space();
        EditorGUILayout.Space();
        GUI.SetNextControlName("ResetBtn");
        if (GUILayout.Button("Reset"))
        {
            GUI.FocusControl("ResetBtn");
            targetObj = null;
            inputText = "";
            inputHeroIdList = "";
            ClearData();
            Repaint();
            GUI.FocusControl("");
        }
        EditorGUILayout.Separator();

        if (!showHeroIDContent)
        {
            EditorGUILayout.LabelField("查询Asset资源");
            targetObj = EditorGUILayout.ObjectField(targetObj, typeof(Object), false);
        }

        if (targetObj != null)
        {
            showHeroIDContent = false;
            string targetPath = AssetDatabase.GetAssetPath(targetObj);

            if (GUILayout.Button("Calc"))
            {
                Repaint();
                ClearData();
                GetAllDep(targetPath);
                SetData();
                CalHeroResNotStdInfo();
            }
        }
        else
        {
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            if (GUILayout.Button("拉取所有英雄最新数据"))
            {
                PullLatestData();
            }
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            EditorGUILayout.Space();

            inputText = EditorGUILayout.TextField("输入英雄id", inputText);
            int heroId = ReadHeroData(inputText);
            string[] resArray = LoadHeroDataById(heroId);
            if (heroId == 0 || resArray == null)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("暂未查询到这个英雄id", heroId.ToString());
                EditorGUILayout.EndHorizontal();
                ClearData();
                return;
            }

            showHeroIDContent = true;
            targetObj = null;
            Repaint();
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("heroName:", curHeroName);
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Calc"))
            {
                ClearData();
                if (resArray.Length > 1)
                {
                    GetAllDep(resArray);
                }
                SetData();
                CalHeroResNotStdInfo();
            }
            EditorGUILayout.EndHorizontal();
        }

        DrawHeroResTipsInfo();
        DrawChildToolbar();
        DrawResult(childPage);
        if (GUILayout.Button("ExportInfos Text"))
        {
            SaveText();
        }
    }

    private void DrawChildToolbar()
    {
        if (resultList.Count < 1)
        {
            return;
        }
        GUILayout.BeginHorizontal();

        int btnSelFontSize = 20;
        GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);
        buttonStyle.fontStyle = FontStyle.Bold;
        buttonStyle.fontSize = btnSelFontSize;
        if (GUILayout.Button("All", childPage == Page.All ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.All;
        }

        GUIStyle redLabelStyle = new GUIStyle(GUI.skin.button);
        redLabelStyle.richText = true;
        redLabelStyle.fontStyle = FontStyle.Bold;
        redLabelStyle.fontSize = GUI.skin.button.fontSize;
        if (childPage == Page.Warn)
        {
            redLabelStyle.fontSize = btnSelFontSize;
        }

        var strWarnBtnDesc = string.Empty;
        if(resNotStdList.Count <= 0)
        {
            strWarnBtnDesc = $"Warn";
        }
        else
        {
            strWarnBtnDesc = $"<color=red>Warn({resNotStdList.Count})</color>";
        }

        if (GUILayout.Button(strWarnBtnDesc, redLabelStyle))
        {
            childPage = Page.Warn;
        }

        if (GUILayout.Button("Textures", childPage == Page.Texture ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Texture;
        }

        if (GUILayout.Button("Animation", childPage == Page.Animation ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Animation;
        }

        if (GUILayout.Button("Mesh", childPage == Page.Mesh ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Mesh;
        }

        if (GUILayout.Button("AudioClip", childPage == Page.AudioClip ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.AudioClip;
        }

        if (GUILayout.Button("Scripts", childPage == Page.Script ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Script;
        }

        if (GUILayout.Button("Shader", childPage == Page.Shader ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Shader;
        }

        if (GUILayout.Button("Material", childPage == Page.Material ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Material;
        }

        if (GUILayout.Button("GameObject", childPage == Page.GameObject ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.GameObject;
        }

        if (GUILayout.Button("Other", childPage == Page.Other ? buttonStyle : GUI.skin.button))
        {
            childPage = Page.Other;
        }
        GUILayout.EndHorizontal();
    }

    void DrawResult(Page childPage)
    {
        EditorGUILayout.Space();
        List<Object> showList;
        switch (childPage)
        {
            case Page.All:
                showList = resultList;
                break;
            case Page.Warn:
                showList = resNotStdList;
                break;
            case Page.Texture:
                showList = resultTextureList;
                break;
            case Page.Animation:
                showList = resultAnimationList;
                break;
            case Page.Mesh:
                showList = resultMeshList;
                break;
            case Page.Shader:
                showList = resultShaderList;
                break;
            case Page.Script:
                showList = resultScriptList;
                break;
            case Page.Material:
                showList = resultMaterialList;
                break;
            case Page.AudioClip:
                showList = resultAduioClipList;
                break;
            case Page.GameObject:
                showList = resultGoList;
                break;
            case Page.Other:
                showList = resultOtherList;
                break;
            default:
                showList = resultList;
                break;
        }

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        EditorGUILayout.LabelField($"count: {showList.Count}");
        bool hasPage = resSizeDic.ContainsKey(childPage.ToString());
        EditorGUILayout.LabelField($"mem: {FormatBytes(hasPage ? resSizeDic[childPage.ToString()] : 0)}");

        for (int i = 0; i < showList.Count; i++)
        {
            var showGo = showList[i];
            if (!fileSizeDic.ContainsKey(showGo.GetInstanceID()))
            {
                continue;
            }

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.ObjectField(showGo, typeof(Object), false);
            //TODO change extension show
            EditorGUILayout.LabelField($"{showGo.GetType()}");
            EditorGUILayout.LabelField($"{FormatBytes(fileSizeDic[showGo.GetInstanceID()])}");
            DrawHeroResNotStdDesc(showGo);
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();
    }

    void DrawAnimSetting()
    {
        var animFbxNeedOptimal = HeroResOptimal.animFbxNeedOptimal;
        if(animFbxNeedOptimal)
        {
            EditorGUILayout.Space();
            EditorGUILayout.Separator();

            GUILayout.Label("动画压缩格式(Show Stand动画可配置)", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            animSelectedIndex = EditorGUILayout.Popup("动画压缩格式", animSelectedIndex, animOptions);
            animCompress = GetAnimCompressType(animSelectedIndex);
            EditorGUILayout.Space();
            EditorGUILayout.Space();
            inputAnimText = EditorGUILayout.TextField("动画压缩阈值", inputAnimText);
            if (!float.TryParse(inputAnimText, out animThreshold))
            {
                animThreshold = 2.0f;
                GUIStyle redLabelStyle = new GUIStyle(EditorStyles.label);
                redLabelStyle.normal.textColor = Color.red;
                EditorGUILayout.LabelField("请输入动画压缩数值", redLabelStyle);
            }
            EditorGUILayout.EndHorizontal();
        }
    }

    void DrawTextureSetting()
    {
        EditorGUILayout.Space();
        EditorGUILayout.Separator();
        GUILayout.Label("纹理最大尺寸", EditorStyles.boldLabel);
        characterTexSelectedIndex = EditorGUILayout.Popup("角色纹理尺寸", characterTexSelectedIndex, textureOptions);
        characterMaxSize = GetTextureMaxSizeBySelectIndex(characterTexSelectedIndex);

        generalTexSelectedIndex = EditorGUILayout.Popup("特效常规纹理尺寸", generalTexSelectedIndex, textureOptions);
        generalMaxSize = GetTextureMaxSizeBySelectIndex(generalTexSelectedIndex);

        xuliezhenTexSelectedIndex = EditorGUILayout.Popup("特效序列帧纹理尺寸", xuliezhenTexSelectedIndex, textureOptions);
        xuliezhenMaxSize = GetTextureMaxSizeBySelectIndex(xuliezhenTexSelectedIndex);

        noiseTexSelectedIndex = EditorGUILayout.Popup("特效噪声纹理尺寸", noiseTexSelectedIndex, textureOptions);
        noiseMaxSize = GetTextureMaxSizeBySelectIndex(noiseTexSelectedIndex);

        guangyunTexSelectedIndex = EditorGUILayout.Popup("特效光晕纹理尺寸", guangyunTexSelectedIndex, textureOptions);
        guangyunMaxSize = GetTextureMaxSizeBySelectIndex(guangyunTexSelectedIndex);

        maskTexSelectedIndex = EditorGUILayout.Popup("特效遮罩纹理尺寸", maskTexSelectedIndex, textureOptions);
        maskMaxSize = GetTextureMaxSizeBySelectIndex(maskTexSelectedIndex);
    }

    #endregion

    #region Data
    private void SetData()
    {
        CheckHeroResFinal();
        SaveDep();
        resultList.Sort((a, b) =>
        {
            int hash1 = a.GetInstanceID();
            int hash2 = b.GetInstanceID();

            if (fileSizeDic.ContainsKey(hash1) && fileSizeDic.ContainsKey(hash2))
            {
                long size1 = fileSizeDic[hash1];
                long size2 = fileSizeDic[hash2];
                int result = size2.CompareTo(size1);
                if (result == 0)
                {
                    string name1 = a.name;
                    string name2 = b.name;
                    result = name1.CompareTo(name2);
                }

                return result;
            }

            return 0;
        });

        resSizeDic.Clear();
        for (int i = 0; i < resultList.Count; i++)
        {
            var go = resultList[i];
            var gpType = go.GetType();
            long singeSize = EditorUtilitys.FileUtilitys.GetRuntimeMemorySizeLong(go, HeroResOptimal.calRuntimeMemoryType);

            if (!resSizeDic.ContainsKey(Page.All.ToString()))
            {
                resSizeDic.Add(Page.All.ToString(), singeSize);
            }
            else
            {
                resSizeDic[Page.All.ToString()] += singeSize;
            }

            if (gpType == typeof(UnityEngine.Texture2D))
            {
                resultTextureList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Texture.ToString()))
                {
                    resSizeDic.Add(Page.Texture.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Texture.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.AnimationClip)
                     || gpType == typeof(UnityEditor.Animations.AnimatorController))
            {
                resultAnimationList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Animation.ToString()))
                {
                    resSizeDic.Add(Page.Animation.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Animation.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.Mesh))
            {
                resultMeshList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Mesh.ToString()))
                {
                    resSizeDic.Add(Page.Mesh.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Mesh.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.AudioClip))
            {
                resultAduioClipList.Add(go);
                if (!resSizeDic.ContainsKey(Page.AudioClip.ToString()))
                {
                    resSizeDic.Add(Page.AudioClip.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.AudioClip.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.Material))
            {
                resultMaterialList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Material.ToString()))
                {
                    resSizeDic.Add(Page.Material.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Material.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.Shader))
            {
                resultShaderList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Shader.ToString()))
                {
                    resSizeDic.Add(Page.Shader.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Shader.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEditor.MonoScript))
            {
                resultScriptList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Script.ToString()))
                {
                    resSizeDic.Add(Page.Script.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Script.ToString()] += singeSize;
                }
            }
            else if (gpType == typeof(UnityEngine.GameObject))
            {
                resultGoList.Add(go);
                if (!resSizeDic.ContainsKey(Page.GameObject.ToString()))
                {
                    resSizeDic.Add(Page.GameObject.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.GameObject.ToString()] += singeSize;
                }
            }
            else
            {
                resultOtherList.Add(go);
                if (!resSizeDic.ContainsKey(Page.Other.ToString()))
                {
                    resSizeDic.Add(Page.Other.ToString(), singeSize);
                }
                else
                {
                    resSizeDic[Page.Other.ToString()] += singeSize;
                }
            }
        }

        EditorGUILayout.Space();
        EditorGUILayout.Space();
        EditorGUILayout.Space();
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.EndHorizontal();
    }

    private void ClearData()
    {
        resList.Clear();
        fileSizeDic.Clear();
        resultList.Clear();
        resultTextureList.Clear();
        resultAnimationList.Clear();
        resultMaterialList.Clear();
        resultMeshList.Clear();
        resultScriptList.Clear();
        resultShaderList.Clear();
        resultAduioClipList.Clear();
        resultOtherList.Clear();
        resultGoList.Clear();
        resSizeDic.Clear();
        ClearHeroResNotStdInfo();
        showHeroIDContent = false;
    }

    void GetAllDep(string[] paths)
    {
        foreach (var path in paths)
        {
            GetAllDep("Assets/" + path);
        }
    }

    void GetAllDep(string path)
    {
        Object[] objects = EditorUtility.CollectDependencies(AssetDatabase.LoadAllAssetsAtPath(path));
        for (int i = 0; i < objects.Length; i++)
        {
            string assetPath = AssetDatabase.GetAssetPath(objects[i]);
            if (!string.IsNullOrEmpty(assetPath) && !resList.Contains(assetPath))
            {
                resList.Add(assetPath);
            }
        }
    }

    void SaveDep()
    {
        for (int i = 0; i < resList.Count; i++)
        {
            string assetPath = resList[i];
            Object go = AssetDatabase.LoadAssetAtPath<Object>(assetPath);
            if (go != null)
            {
                resultList.Add(go);
                long singeSize = EditorUtilitys.FileUtilitys.GetRuntimeMemorySizeLong(go, HeroResOptimal.calRuntimeMemoryType);
                if (!fileSizeDic.ContainsKey(go.GetInstanceID()))
                {
                    fileSizeDic.Add(go.GetInstanceID(), singeSize);
                }
            }
        }
    }

    string[] LoadHeroDataById(int heroId)
    {
        string assetPath = "Assets/Scripts/Editor/AssetTools/ResPreview/HeroData.asset";
        HeroDataSo heroDataSo = AssetDatabase.LoadAssetAtPath<HeroDataSo>(assetPath);
        if (heroDataSo != null)
        {
            // 遍历 dataList 中的 HeroData
            foreach (var heroData in heroDataSo.dataList)
            {
                if (heroData.heroId == heroId)
                {
                    curHeroName = heroData.name;
                    return heroData.resList.ToArray();
                }
            }
        }
        else
        {
            Debug.LogError("Failed to load HeroDataSo at path: " + assetPath);
        }
        return null;
    }

    string FormatBytes(long bytes)
    {
        if (bytes >= 1024 * 1024)
        {
            return string.Format("{0:F2}MB", (double)bytes / (1024 * 1024));
        }
        else if (bytes >= 1024)
        {
            return string.Format("{0:F2}kB", (double)bytes / 1024);
        }
        else
        {
            return string.Format("{0}bytes", bytes);
        }
    }

    int ReadHeroData(string idString)
    {
        int parsedId;
        if (int.TryParse(inputText, out parsedId))
        {
            return parsedId;
        }
        return 0;
    }


    ModelImporterAnimationCompression GetAnimCompressType(int animCompressId)
    {
        return animCompressId == 0 ? ModelImporterAnimationCompression.Optimal :
            animCompressId == 1 ? ModelImporterAnimationCompression.KeyframeReduction :
            ModelImporterAnimationCompression.Off;
    }

    int GetTextureMaxSizeBySelectIndex(int index)
    {
        return textureOptionss[index];
    }


    #endregion

    #region SaveFile
    private void SaveText()
    {
        if(resultList.Count <= 0)
        {
            EditorUtility.DisplayDialog("导出提示", "导出英雄资源信息数据为空!!!!!!", "确定");
            return;
        }

        string strSaveKey = "HeroResInfo";
        var appPath = Application.dataPath.Replace("\\", "/");
        var strSaveKeyF = appPath + "/" + strSaveKey;
        var defaultDir = appPath + "/Scripts/Editor/AssetTools/ResPreview/";
        var saveDir = EditorPrefs.GetString(strSaveKeyF, defaultDir);

        string savePath = EditorUtility.SaveFilePanel("Save Text", saveDir, "HeroResInfo", "txt");
        if(!string.IsNullOrEmpty(savePath))
        {
            using (StreamWriter writer = new StreamWriter(savePath))
            {
                EditorPrefs.SetString(strSaveKeyF, savePath.Replace("\\", "/"));
                for (int i = 0; i < resultList.Count; i++)
                {
                    var goObject = resultList[i];
                    writer.WriteLine(
                        $"{goObject.name}:{goObject.GetType()} {FormatBytes(fileSizeDic[goObject.GetInstanceID()])} {fileSizeDic[goObject.GetInstanceID()]}");
                }
            }
        }
    }

    #endregion

    #region 优化操作

    void DoAllOptimal()
    {
        HeroResOptimal.HeroResOptimalOption optimalOption = GetOptimalOption();
        EditorUtility.DisplayProgressBar("资源优化", "资源处理中...", 0.2f);
        HeroResOptimal.DoHeroAllResOptimal(optimalOption);
        EditorUtility.ClearProgressBar();
    }

    /// <summary>
    /// add list to optimal
    /// </summary>
    void DoSomeOptimal(List<int> optimalIdList)
    {
        if(optimalIdList == null || optimalIdList.Count <= 0)
        {
            return;
        }

        HeroResOptimal.HeroResOptimalOption optimalOption = GetOptimalOption();
        EditorUtility.DisplayProgressBar("资源优化", "资源处理中...", 0.2f);
        EditorUtility.DisplayProgressBar("资源优化", "资源处理中...", 0.6f);
        HeroResOptimal.DoHeroResOptimal(optimalIdList, optimalOption);
        EditorUtility.ClearProgressBar();
        EditorUtility.ClearProgressBar();
    }

    /// <summary>
    /// 选中asset资源优化
    /// </summary>
    /// <param name="assetPath"></param>
    void DoSelAssetOptimal(string assetPath)
    {
        HeroResOptimal.HeroResOptimalOption optimalOption = GetOptimalOption();
        EditorUtility.DisplayProgressBar("资源优化", "资源处理中...", 0.6f);
        HeroResOptimal.DoAssetOptimal(new List<string>() { assetPath }, optimalOption);
        EditorUtility.ClearProgressBar();
    }

    void DoOptimal()
    {
        int heroId = ReadHeroData(inputText);
        string[] resArray = LoadHeroDataById(heroId);
        if (heroId == 0 || resArray == null)
        {
            EditorGUILayout.BeginHorizontal();
            EditorUtility.DisplayDialog("提示", $"未找到 id = {heroId} 的英雄,请输入正确id", "确定");
            EditorGUILayout.EndHorizontal();
            return;
        }
        HeroResOptimal.HeroResOptimalOption optimalOption = GetOptimalOption();
        List<int> optimalIdList = new List<int>();
        optimalIdList.Add(heroId);
        EditorUtility.DisplayProgressBar("资源优化", "资源处理中...", 0.6f);
        HeroResOptimal.DoHeroResOptimal(optimalIdList, optimalOption);
        EditorUtility.ClearProgressBar();
    }

    public HeroResOptimal.HeroResOptimalOption GetOptimalOption()
    {
        HeroResOptimal.HeroResOptimalOption optimalOption = new HeroResOptimal.HeroResOptimalOption()
        {
            animCompression = animCompress,
            animThresold = Mathf.Abs(animThreshold),
            chaTexMax = characterMaxSize,
            genEffMax = generalMaxSize,
            xuEffMax = xuliezhenMaxSize,
            noiseEffMax = noiseMaxSize,
            guangyunEffMax = guangyunMaxSize,
            maskEffMax = maskMaxSize,
            optimalChaTex = optimalChaTex,
            optimalChaAnim = optimalChaAnim,
            optimalChaEffectTex = optimalChaEffectTex,
            optimalStandShowAni = optimalStandShowAni,
            ssAnimThreshold = ssAnimThreshold
        };

        return optimalOption;
    }

    private Dictionary<int, HeroResOptimal.HeroResInfoOptimal> readHeroResDic = new Dictionary<int, HeroResOptimal.HeroResInfoOptimal>();
    void PullLatestData()
    {
        EditorUtility.DisplayProgressBar("进度提示", "正在进行拉取...", 0.5f);
        HeroResOptimal.DoGenerateAllHeroResDependFile();
        EditorUtility.ClearProgressBar();
        readHeroResDic = HeroResOptimal.GetHeroResDependContent();
        EditorUtility.DisplayProgressBar("进度提示", "正在写入新数据...", 0.75f);
        UpdateHeroDataSo(readHeroResDic);
        EditorUtility.DisplayProgressBar("进度提示", "写入完毕...", 1f);
        EditorUtility.ClearProgressBar();
    }

    HeroDataSo ReadHeroDataSo()
    {
        string assetPath = "Assets/Scripts/Editor/AssetTools/ResPreview/HeroData.asset";
        HeroDataSo heroDataSo = AssetDatabase.LoadAssetAtPath<HeroDataSo>(assetPath);

        if (heroDataSo == null)
        {
            heroDataSo = ScriptableObject.CreateInstance<HeroDataSo>();
            AssetDatabase.CreateAsset(heroDataSo, assetPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        return heroDataSo;
    }

    void UpdateHeroDataSo(Dictionary<int, HeroResOptimal.HeroResInfoOptimal> data)
    {
        HeroDataSo heroDataSo = ReadHeroDataSo();
        heroDataSo.dataList.Clear();
        foreach (var kvp in data)
        {//新增
            heroDataSo.dataList.Add(new HeroDataSo.HeroData() { heroId = kvp.Key, name = kvp.Value.heroName, resList = kvp.Value.heroDepends });
        }

        EditorUtility.SetDirty(heroDataSo);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
    }
    #endregion
}

