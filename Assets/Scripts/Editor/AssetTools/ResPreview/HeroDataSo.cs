using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class HeroDataSo : ScriptableObject
{
    [SerializeField] public List<HeroData> dataList = new List<HeroData>();

    [System.Serializable]
    public class HeroData
    {
        public string name;
        public int heroId;
        public List<string> resList;
    }
    
    public bool HasDataById(int id)
    {
        if (dataList.Count < 1)
        {
            return false;
        }

        foreach (var heroData in dataList)
        {
            if (heroData.heroId == id)
            {
                return true;
            }
        }
        return false;
    }
}
