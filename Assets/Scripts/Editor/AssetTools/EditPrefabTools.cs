using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

public class EditPrefabTools
{
    [MenuItem("Assets/Prefab/ParticleSystem 2017 to 2018", false, 10)]
    static public void UpdateParticleSystemTo2018()
    {
        string[] guids = AssetDatabase.FindAssets("t:Prefab", null);

        var list = new List<string>();

        var totalCount = guids.Length;
        for (int guidIdx = 0; guidIdx < totalCount; guidIdx++)
        {
            var guid = guids[guidIdx];
            string assetPath = AssetDatabase.GUIDToAssetPath(guid);

            EditorUtility.DisplayProgressBar("Modify ParticleSystem", assetPath, guidIdx / (float)totalCount);

            using (var editScope = new EditPrefabAssetScope(assetPath))
            {
                var prefab = editScope.prefabRoot;
                var mCount = 0;
                ParticleSystem[] ps = prefab.GetComponentsInChildren<ParticleSystem>(true);
                for (int j = 0; j < ps.Length; ++j)
                {
                    ParticleSystem p = ps[j];

                    if (p.emission.enabled && p.emission.burstCount > 0)
                    {
                        for (int k = 0; k < p.emission.burstCount; k++)
                        {
                            var burst = p.emission.GetBurst(k);
                            if (burst.probability == 0)
                            {
                                burst.probability = 1.0f;
                                p.emission.SetBurst(k, burst);
                                mCount++;
                            }
                        }
                    }
                }
                if (mCount > 0)
                {
                    list.Add(assetPath);
                    editScope.SetDirty(true);
                    Debug.Log("modify prefab:" + assetPath);
                }
            }
        }
        EditorUtility.ClearProgressBar();
    }
}
