/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：MainClashByMGameHelper
 *  作者：wjy
 *  日期：20220514
 *  功能：排查我们xgame游戏主体错误依赖到mgame文件夹的预设体
*****************************************************/

using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public class MainClashByMGameHelper
{
    [MenuItem("Assets/mgame检查工具/排查主体游戏错误依赖到小游戏的预设体", false, 1)]
    static void copyabname()
    {
        Debug.LogWarning(Application.dataPath);
        var CasualGamePath = Path.Combine(Application.dataPath, "CasualGame");
        Debug.LogWarning(CasualGamePath);
        var list = Directory.GetFiles(Application.dataPath, "*.prefab", SearchOption.AllDirectories);
        Debug.LogWarning($"总共有多少个预设体{list.Length}");

        // var casualAbNameList = new List<string>(list.Length);

        var ablist = new List<string>(list.Length);

        var count = 0;
        foreach (var s in list)
        {
            var news = s.Replace(Application.dataPath, "Assets");
            // Debug.LogWarning($"当前路径s={news}");
            var abName = AssetDatabase.GetImplicitAssetBundleName(news);
            if (!string.IsNullOrEmpty(abName))
            {
                if (abName.StartsWith("casualgame"))
                {
                    // casualAbNameList.Add(abName);
                }
                else
                {
                    ablist.Add(abName);
                    var dplist = AssetDatabase.GetDependencies(news);
                    foreach (var s1 in dplist)
                    {
                        var target = s1.ToLower();
                        bool isUnable = false;
                        if (target.StartsWith("assets/casualgame"))
                        {
                            Debug.LogError($"预设体路径path={s}错误依赖于path={s1}");
                            isUnable = true;
                        }

                        if (isUnable)
                        {
                            count++;
                        }
                    }
                }
            }
        }

        Debug.LogError($"总共有{count}个预设体需要处理");
        // Debug.LogWarning($"有ab包名的预设体的数量{ablist.Count}");
        // Debug.LogWarning($"小游戏有ab包名预设体个数{casualAbNameList.Count}");
    }

    // [MenuItem("Assets/mgame检查工具/ChangeFixedLayout", false, 1)]
    // static void ChangeFixedLayout()
    // {
    //     string dir = "Assets/Art/Maps/Puzzles";
    //     var list = Directory.GetFiles(dir, "*.prefab", SearchOption.AllDirectories);
    //     foreach (var s in list)
    //     {
    //         var news = s.Replace(Application.dataPath, "Assets");
    //         var go = AssetDatabase.LoadAssetAtPath<GameObject>(news);
    //         var bg = go.transform.Find("bg") as RectTransform;
    //         bg.anchorMin = new Vector2(0, 1);
    //         bg.anchorMax = new Vector2(1, 1);
    //         // Debug.LogWarning($"max={bg.offsetMax} min={bg.offsetMin}");
    //         var oMax = bg.offsetMax;
    //         oMax.x = 0;
    //         bg.offsetMax = oMax;
    //
    //         var oMin = bg.offsetMin;
    //         oMin.x = 0;
    //         bg.offsetMin = oMin;
    //
    //         var bg1 = go.transform.Find("bg/bg1").GetComponent<AspectRatioFitter>();
    //         var rect1 = bg1.transform as RectTransform;
    //         rect1.sizeDelta = new Vector2(720, 720);
    //         bg1.enabled = false;
    //         var bg2 = go.transform.Find("bg/bg2").GetComponent<AspectRatioFitter>();
    //         var rect2 = bg2.transform as RectTransform;
    //         rect2.sizeDelta = new Vector2(720, 720 * 2);
    //
    //         bg2.enabled = false;
    //
    //
    //         var layout = bg.GetComponent<VerticalLayoutGroup>();
    //         layout.childForceExpandWidth = true;
    //         layout.childControlWidth = true;
    //         EditorUtility.SetDirty(go);
    //         AssetDatabase.SaveAssets();
    //     }
    //
    //     AssetDatabase.Refresh();
    //
    //     EditorUtility.UnloadUnusedAssetsImmediate();
    // }
}