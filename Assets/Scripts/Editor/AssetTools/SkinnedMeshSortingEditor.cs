using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

class SkinnedMeshSortingEditor : EditorWindow
{
    List<SkinnedMeshRenderer> skinnedMeshList = new List<SkinnedMeshRenderer>();
    List<SerializedObject> serializedSkinnedMeshList = new List<SerializedObject>();

    [MenuItem("Artist/SkinnedMesh Sorting Editor")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        SkinnedMeshSortingEditor window = (SkinnedMeshSortingEditor)EditorWindow.GetWindow(typeof(SkinnedMeshSortingEditor));
        window.Show();
    }
    
    private void RefreshSelection()
    {
        if (Selection.objects.Length > 0)
        {
            skinnedMeshList.Clear();
            serializedSkinnedMeshList.Clear();
            foreach (UnityEngine.Object obj in Selection.objects)
            {
                GameObject go = obj as GameObject;
                if (go)
                {
                    SkinnedMeshRenderer skinnedMesh = go.GetComponent<SkinnedMeshRenderer>();
                    if (skinnedMesh)
                    {
                        SerializedObject serializedSkinnedMesh = new SerializedObject(skinnedMesh);
                        skinnedMeshList.Add(skinnedMesh);
                        serializedSkinnedMeshList.Add(serializedSkinnedMesh);
                        Repaint();
                    }
                }
            }
        }
    }

    private void OnEnable()
    {
        Selection.selectionChanged += RefreshSelection;        
    }

    private void OnDestroy()
    {
        Selection.selectionChanged -= RefreshSelection;
    }

    private void OnGUI()
    {
        if (serializedSkinnedMeshList.Count > 0)
        {
            SerializedObject serializedSkinnedMesh = serializedSkinnedMeshList[0];
            SerializedProperty sortingOrder = serializedSkinnedMesh.FindProperty("m_SortingOrder");
            SerializedProperty sortingLayerID = serializedSkinnedMesh.FindProperty("m_SortingLayerID");

            EditorGUI.BeginChangeCheck();
           // Anima2D.EditorGUIExtra.SortingLayerField(new GUIContent("Sorting Layer"), sortingLayerID, EditorStyles.popup, EditorStyles.label);
            EditorGUILayout.PropertyField(sortingOrder, new GUIContent("Order in Layer"));

            if (EditorGUI.EndChangeCheck())
            {
                serializedSkinnedMesh.ApplyModifiedProperties();
                foreach (SerializedObject t in serializedSkinnedMeshList)
                {
                    if (t != serializedSkinnedMesh)
                    {
                        t.CopyFromSerializedProperty(sortingOrder);
                        t.CopyFromSerializedProperty(sortingLayerID);
                        t.ApplyModifiedProperties();
                    }
                }
            }
        }
    }



}
