using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using UnityEngine.UI;
using UnityEditor.Experimental.SceneManagement;
using System;
using UnityEditorInternal;
public class PrefabSyncEditor : Editor
{

    [MenuItem("CONTEXT/Text/同步到预制体")]
    [MenuItem("CONTEXT/Image/同步到预制体")]
    [MenuItem("CONTEXT/RawImage/同步到预制体")]
    [MenuItem("CONTEXT/Button/同步到预制体")]
    [MenuItem("CONTEXT/RectTransform/同步到预制体")]
    [MenuItem("CONTEXT/Outline/同步到预制体")]
    //[MenuItem("CONTEXT/ScrollRectTable/同步到预制体")]
    [MenuItem("CONTEXT/VerticalLayoutGroup/同步到预制体")]
    [MenuItem("CONTEXT/HorizontalLayoutGroup/同步到预制体")]
    [MenuItem("CONTEXT/Canvas/同步到预制体")]
    [MenuItem("CONTEXT/ContentSizeFitter/同步到预制体")]
    [MenuItem("CONTEXT/TextMeshProUGUI/同步到预制体")]
    [MenuItem("CONTEXT/Transform/同步到预制体")]
    public static void PrefabSync(MenuCommand menu)
    {
        string prefabResPath = "Assets/UI/Prefabs/";
        var go = Selection.activeGameObject;
        var compType = (Component)menu.context;
        ComponentUtility.CopyComponent(compType);

        if (go != null)
        {
            string compPath = go.name; //组件路径
            string prefabName = "";
            Transform parent = go.transform;

            if (compPath.Contains("(Clone)"))
            {
                compPath = compPath.Replace("(Clone)", "");
                prefabName = compPath;
            }
            else
            {
                while (parent.parent != null)
                {

                    parent = parent.parent;
                    string mName = parent.name;
                    if (mName.Contains("(Clone)"))
                    {
                        prefabName = mName.Replace("(Clone)", "");
                        break;
                    }
                    compPath = parent.name + "/" + compPath;

                }

            }

            if (!string.IsNullOrEmpty(prefabName))
            {

                string[] files = Directory.GetFiles(prefabResPath, "*.*", SearchOption.AllDirectories);
                string searchFile = prefabName + ".prefab";
                bool isFind = false; 
                foreach (string file in files)
                {
                    if (file.Contains(searchFile))
                    {
                        string path = file;
                        path = path.Replace("\\", "/");
                        Debug.Log("查找成功");
                        Debug.Log(path);
                        var prefabIns = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                        if(prefabIns != null)
                        {
                            Transform t = prefabIns.transform.Find(compPath);
                            if (t != null)
                            {
                                var comp = t.GetComponent(compType.GetType().Name);
                                if (comp != null)
                                {
                                    ComponentUtility.PasteComponentValues(comp);
                                }
                                else
                                {
                                    ComponentUtility.PasteComponentAsNew(t.gameObject);
                                }
                                //if (compType.GetType() == typeof(ScrollRectTable))
                                //{
                                //    var sc = comp as ScrollRectTable;
                                //    sc.Clear();

                                //}
                                EditorUtility.SetDirty(prefabIns);
                                EditorUtility.UnloadUnusedAssetsImmediate();
                                Debug.Log("同步成功");
                                isFind = true;
                                break;
                            }
                            else
                            {
                                Debug.LogError("预制体内路径不存在请检查：" + compPath);
                            }
                        }

                    }
                }
                if(!isFind)
                {
                    Debug.LogError("没有找到这个预制体");
                }
            }
        }




    }
}
