using UnityEngine;
using System.Collections;
using UnityEditor;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Generic;

/*
 *查找选择的图片在哪里被引用了
 */

public class FindReferences
{ 
	/// <summary>
	/// Find this instance. 由于该函数开始设置了serializeationmode,在目前项目中会导致所有资源重新import,不对外暴露这个命令
	/// </summary>
//	[MenuItem("Assets/Find References2", false, 10)]
	static private void Find()
	{
		EditorSettings.serializationMode = SerializationMode.ForceText;
		string path = AssetDatabase.GetAssetPath(Selection.activeObject);
		if (!string.IsNullOrEmpty(path))
		{
			string guid = AssetDatabase.AssetPathToGUID(path);
			List<string> extensionsInclude = new List<string>() { ".prefab", ".unity", ".mat", ".asset" };
			string dataPath = Application.dataPath.Replace("/", "\\");
			dataPath += "\\Art";
			string[] files = Directory.GetFiles(dataPath, "*.*", SearchOption.AllDirectories)
				//获取指定后缀的文件
				.Where(s => extensionsInclude.Contains(Path.GetExtension(s).ToLower())).ToArray();

			int startIndex = 0;
			EditorApplication.update = delegate ()
			{
				string file = files[startIndex].Replace("\\","/");
				bool isCancel = EditorUtility.DisplayCancelableProgressBar("匹配资源中", file, (float)startIndex / (float)files.Length);
				if (Regex.IsMatch(File.ReadAllText(file), guid))
				{
					Debug.Log(file, AssetDatabase.LoadAssetAtPath<Object>(GetRelativeAssetsPath(file)));
				}
				startIndex++;
				if (isCancel || startIndex >= files.Length)
				{
					EditorUtility.ClearProgressBar();
					EditorApplication.update = null;
					startIndex = 0;
					Debug.Log("匹配结束");
				}
			};
		}
	}

	[MenuItem("Assets/Find References", false, 10)]
	static private void FindDependencies()
	{ 
		string path = AssetDatabase.GetAssetPath(Selection.activeObject);
		if (!string.IsNullOrEmpty(path))
		{
			string guid = AssetDatabase.AssetPathToGUID(path);
			List<string> extensionsInclude = new List<string>() { ".prefab", ".unity", ".mat", ".asset" };
			string dataPath = Application.dataPath.Replace("/", "\\");
//			dataPath += "\\Art";
			string[] files = Directory.GetFiles(dataPath, "*.*", SearchOption.AllDirectories)
				//获取指定后缀的文件
				.Where(s => extensionsInclude.Contains(Path.GetExtension(s).ToLower())).ToArray();

			int startIndex = 0;
			Debug.LogError (files[0]);
			Debug.LogError (path);
			var list = new List<string> ();
			EditorApplication.update = delegate ()
			{
                for (int i = 0; i < 10; i++)
                {
                    string file = files[startIndex].Replace("\\", "/");
                    bool isCancel = EditorUtility.DisplayCancelableProgressBar("匹配资源中", file, (float)startIndex / (float)files.Length);
                    var dependences = AssetDatabase.GetDependencies(GetRelativeAssetsPath(file));
                    if (dependences.Length > 0)
                    {
                        // Debug.LogError (dependences[0]);
                    }
                    if (dependences.Contains(path))
                    {
                        Debug.Log(file, AssetDatabase.LoadAssetAtPath<Object>(GetRelativeAssetsPath(file)));
                        list.Add(file);
                    }
                    startIndex++;
                    if (isCancel || startIndex >= files.Length)
                    {
                        EditorUtility.ClearProgressBar();
                        EditorApplication.update = null;
                        startIndex = 0;
                        LogHelp.clipboard = Newtonsoft.Json.JsonConvert.SerializeObject(list);
                        Debug.Log("匹配结束");
                        return;
                    }
                }
			};
		}
	}

    /*
     * -path  Assets下面子目录名
     * -ext 资源扩展名
     */
    //[MenuItem("Assets/FindAssetsReferences", false, 16)]
    public static void FindAssetsReferences()
    {
        Dictionary<string, int> refCount = new Dictionary<string, int>();
        StringBuilder builder = new StringBuilder();
        System.Environment.GetCommandLineArgs().ForEach((str) => { builder.Append(str + " "); });
        string fullStr = builder.ToString();

        var subPath = @"\Art";
        var ext = ".mat";
        Match match = Regex.Match(fullStr, "-path +([^ ]+)");
        if (match.Success)
            subPath = match.Groups[1].Value;

        match = Regex.Match(fullStr, "-ext +([^ ]+)");
        if (match.Success)
            ext = match.Groups[1].Value;

        // begin with Assets
        var len = Application.dataPath.Length - 6;
        string dataPath = Application.dataPath.Replace("/", "\\");
        Debug.Log("path: " + dataPath);
        
        List<string> specs = new List<string>();
        // 获取指定后缀的文件
        Directory.GetFiles(dataPath + subPath, "*" + ext, SearchOption.AllDirectories).ForEach(s => specs.Add(s.Replace("\\", "/").Substring(len)))/*.Where(s => extensionsInclude.Contains(Path.GetExtension(s).ToLower())).ToArray()*/;

        List<string> extensionsInclude = new List<string>() { ".prefab", ".unity", ".mat", ".asset" };
        string[] files = Directory.GetFiles(dataPath, "*.*", SearchOption.AllDirectories)
            //获取指定后缀的文件
            .Where(s => extensionsInclude.Contains(Path.GetExtension(s).ToLower())).ToArray();

        int startIndex = 0;
        EditorApplication.update = delegate ()
        {
            //for (int i = 0; i < 10; i++)
            while (true)
            {
                string file = files[startIndex].Replace("\\", "/");
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("匹配资源中", file, (float)startIndex / (float)files.Length);
                var dependences = AssetDatabase.GetDependencies(GetRelativeAssetsPath(file));

                foreach (var spec in specs)
                {
                    if (dependences.Contains(spec))
                    {
                        refCount[spec] = refCount.ContainsKey(spec) ? (refCount[spec] + 1) : 1;
                        //Debug.Log(file, AssetDatabase.LoadAssetAtPath<Object>(GetRelativeAssetsPath(file)));
                        //list.Add(file);
                    }
                }

                startIndex++;
                if (isCancel || startIndex >= files.Length)
                {
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update = null;
                    startIndex = 0;

                    var str = Newtonsoft.Json.JsonConvert.SerializeObject(refCount);
                    using (StreamWriter sw = new StreamWriter(Application.dataPath + "out.txt"))
                    {
                        sw.Write(str);
                    }
                    //LogHelp.clipboard = str;
                    //Debug.Log(str);
                    //LogHelp.clipboard = Newtonsoft.Json.JsonConvert.SerializeObject(list);

                    Debug.Log("匹配结束");
                    break;
                }
            }
        };
    }

    //[MenuItem("Assets/Find References", true, 11)]
    static private bool VFind()
    {
        string path = AssetDatabase.GetAssetPath(Selection.activeObject);
        return (!string.IsNullOrEmpty(path));
    }

    static private string GetRelativeAssetsPath(string path)
    {
        return "Assets" + Path.GetFullPath(path).Replace(Path.GetFullPath(Application.dataPath), "").Replace('\\', '/');
    }

#if UNITY_EDITOR_OSX
	[MenuItem("Assets/mdfind Reference", false, 2000)]
	private static void FindProjectReferences()
	{
        string selectedAssetPath = AssetDatabase.GetAssetPath(Selection.activeObject);
		if (string.IsNullOrEmpty(selectedAssetPath))
            return;

        string guid = AssetDatabase.AssetPathToGUID(selectedAssetPath);
		string appDataPath = Application.dataPath;
		string output = "";
		
		List<string> references = new List<string>();
		
		var psi = new System.Diagnostics.ProcessStartInfo();
		psi.WindowStyle = System.Diagnostics.ProcessWindowStyle.Maximized;
		psi.FileName = "/usr/bin/mdfind";
		psi.Arguments = "-onlyin " + Application.dataPath + " " + guid;
		psi.UseShellExecute = false;
		psi.RedirectStandardOutput = true;
		psi.RedirectStandardError = true;
		
		System.Diagnostics.Process process = new System.Diagnostics.Process();
		process.StartInfo = psi;
		
		process.OutputDataReceived += (sender, e) => {
			if(string.IsNullOrEmpty(e.Data))
				return;
			
			string relativePath = "Assets" + e.Data.Replace(appDataPath, "");
			
			// skip the meta file of whatever we have selected
			if(relativePath == selectedAssetPath + ".meta")
				return;
			
			references.Add(relativePath);
			
		};
		process.ErrorDataReceived += (sender, e) => {
			if(string.IsNullOrEmpty(e.Data))
				return;
			
			output += "Error: " + e.Data + "\n";
		};
		process.Start();
		process.BeginOutputReadLine();
		process.BeginErrorReadLine();
		
		process.WaitForExit(2000);
		
		foreach(var file in references){
			output += file + "\n";
			Debug.Log(file, AssetDatabase.LoadMainAssetAtPath(file));
		}
		
		Debug.LogWarning(references.Count + " references found for object " + Selection.activeObject.name + "\n\n" + output);
	}
#endif
}