using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;
using UnityEditor.UI;
using System.Text.RegularExpressions;
using System.Linq;
using System.IO;

[CustomEditor(typeof(PrefabLightmapInfo), true)]
public class PrefabLightmapInfoEditor : Editor
{
    PrefabLightmapInfo prefabLightmapInfo;
    Rect lightingDataPathRect;
    string lightingDataPath;

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        EditorGUILayout.Space(20);

        var prefabLightmapObj = PrefabLightmapInfo;

        EditorGUILayout.BeginHorizontal();
        //获得一个长300的框
        lightingDataPathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        if (prefabLightmapObj != null && !string.IsNullOrEmpty(prefabLightmapObj.lightingDataPath) && string.IsNullOrEmpty(lightingDataPath))
        {
            lightingDataPath = prefabLightmapObj.lightingDataPath;
        }
        lightingDataPath = EditorGUI.TextField(lightingDataPathRect, "Lightmap 保存路径:", lightingDataPath);
        EditorGUILayout.EndHorizontal();

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && lightingDataPathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                lightingDataPath = DragAndDrop.paths[0];
            }
        }

        EditorGUILayout.Space(20);

        if (GUILayout.Button($"烘焙 Prefab"))
        {
            if (prefabLightmapObj == null)
            {
                return;
            }
            if (string.IsNullOrEmpty(lightingDataPath))
            {
                Debug.LogError("Lightmap 资源保存路径不能为空");
                return;
            }
            if (!Directory.Exists(lightingDataPath))
            {
                Debug.LogError($"路径不存在:{lightingDataPath}");
                return;
            }
            prefabLightmapObj.lightingDataPath = lightingDataPath;
            PrefabLightmapInfo.GenerateLightmapInfo(prefabLightmapObj, lightingDataPath);
        }

        if (!prefabLightmapObj.hasApplyLightmapData && GUILayout.Button($"加载烘焙数据"))
        {
            prefabLightmapObj.ApplyLightmapData();
        }
        if (prefabLightmapObj.hasApplyLightmapData && GUILayout.Button($"移除烘焙数据"))
        {
            prefabLightmapObj.RemoveLightmapData();
        }

        EditorGUILayout.Space(20);
    }

    public PrefabLightmapInfo PrefabLightmapInfo
    {
        get
        {
            if (prefabLightmapInfo == null)
            {
                prefabLightmapInfo = target as PrefabLightmapInfo;
            }
            return prefabLightmapInfo;
        }
    }
}
