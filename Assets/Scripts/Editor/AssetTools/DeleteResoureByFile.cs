using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Security.Cryptography;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System;
using UnityEngine.UI;

/*
批量删除文件，通过读取文件中的路径删除 
*/
public class DeleteResourcesByFile
{

    static private string szSelectedPath = null;
    [MenuItem("Assets/Delete Resources", true, 16)]
    static private bool Delete()
    {
        bool res = false;
        do
        {
            
            szSelectedPath = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(szSelectedPath) || !File.Exists(szSelectedPath))
            {
                Debug.Log("路径不存在或是文件夹");
                break;
            }
            FileStream frDel = new FileStream(szSelectedPath, FileMode.Open, FileAccess.Read);  //追加流，权限设置为可读
            StreamReader read = new StreamReader(frDel, Encoding.UTF8);
            string szDelPath = null;
            int count = 0;
            while (null != (szDelPath = read.ReadLine()))
            {
                
                if (File.Exists(szDelPath))
                {
                    AssetDatabase.DeleteAsset(szDelPath);
                    count++;
                }
                else
                    Debug.Log(szDelPath + "不存在");
                    
            }
            read.Close();
            read.Dispose();
            frDel.Close();
            frDel.Dispose();
            Debug.Log("删除:"+count);
            Debug.Log("删除结束");
        } while (false);

        return res;
    }

    [MenuItem("Assets/Delete Resources", true, 17)]
    static private bool VDelete()
    {
        szSelectedPath = AssetDatabase.GetAssetPath(Selection.activeObject);
        return (!string.IsNullOrEmpty(szSelectedPath));
    }

}
