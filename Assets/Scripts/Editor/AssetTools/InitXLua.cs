
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class EncryptorInitLua
{
    [MenuItem("XLua/InitXLua")]
    public static void EncryptorInitXLua()
    {
        var init_xlua = @" 
            local metatable = {}
            local rawget = rawget
            local setmetatable = setmetatable
            local import_type = xlua.import_type
            local import_generic_type = xlua.import_generic_type
            local load_assembly = xlua.load_assembly

            function metatable:__index(key) 
                local fqn = rawget(self,'.fqn')
                fqn = ((fqn and fqn .. '.') or '') .. key

                local obj = import_type(fqn)

                if obj == nil then
                    -- It might be an assembly, so we load it too.
                    obj = { ['.fqn'] = fqn }
                    setmetatable(obj, metatable)
                elseif obj == true then
                    return rawget(self, key)
                end

                -- Cache this lookup
                rawset(self, key, obj)
                return obj
            end

            -- A non-type has been called; e.g. foo = System.Foo()
            function metatable:__call(...)
                local n = select('#', ...)
                local fqn = rawget(self,'.fqn')
                if n > 0 then
                    local gt = import_generic_type(fqn, ...)
                    if gt then
                        return rawget(CS, gt)
                    end
                end
                error('No such type: ' .. fqn, 2)
            end

            CS = CS or {}
            setmetatable(CS, metatable)

            typeof = function(t) return t.UnderlyingSystemType end
            cast = xlua.cast
            if not setfenv or not getfenv then
                local function getfunction(level)
                    local info = debug.getinfo(level + 1, 'f')
                    return info and info.func
                end

                function setfenv(fn, env)
                  if type(fn) == 'number' then fn = getfunction(fn + 1) end
                  local i = 1
                  while true do
                    local name = debug.getupvalue(fn, i)
                    if name == '_ENV' then
                      debug.upvaluejoin(fn, i, (function()
                        return env
                      end), 1)
                      break
                    elseif not name then
                      break
                    end

                    i = i + 1
                  end

                  return fn
                end

                function getfenv(fn)
                  if type(fn) == 'number' then fn = getfunction(fn + 1) end
                  local i = 1
                  while true do
                    local name, val = debug.getupvalue(fn, i)
                    if name == '_ENV' then
                      return val
                    elseif not name then
                      break
                    end
                    i = i + 1
                  end
                end
            end

            xlua.hotfix = function(cs, field, func)
                if func == nil then func = false end
                local tbl = (type(field) == 'table') and field or {[field] = func}
                for k, v in pairs(tbl) do
                    local cflag = ''
                    if k == '.ctor' then
                        cflag = '_c'
                        k = 'ctor'
                    end
                    local f = type(v) == 'function' and v or nil
                    xlua.access(cs, cflag .. '__Makeup0_'..k, f) -- at least one
                    pcall(function()
                        for i = 1, 99 do
                            xlua.access(cs, cflag .. '__Makeup'..i..'_'..k, f)
                        end
                    end)
                end
                xlua.private_accessible(cs)
            end
            xlua.getmetatable = function(cs)
                return xlua.metatable_operation(cs)
            end
            xlua.setmetatable = function(cs, mt)
                return xlua.metatable_operation(cs, mt)
            end
            xlua.setclass = function(parent, name, impl)
                impl.UnderlyingSystemType = parent[name].UnderlyingSystemType
                rawset(parent, name, impl)
            end
            
            local base_mt = {
                __index = function(t, k)
                    local csobj = t['__csobj']
                    local func = csobj['<>xLuaBaseProxy_'..k]
                    return function(_, ...)
                         return func(csobj, ...)
                    end
                end
            }
            base = function(csobj)
                return setmetatable({__csobj = csobj}, base_mt)
            end
            ";
        var encryptStr = War.Script.Utility.Encryptor(init_xlua);
        File.WriteAllText(Path_res_lu, encryptStr);
        Debug.LogWarning("res_lu.txt 保存完成");
    }
    static string Path_res_lu="./Assets/Resources/res_lu.txt";
    [MenuItem("XLua/LoadXLua")]
    public static void DecryptorInitXLua()
    {
        var content = File.ReadAllText(Path_res_lu);
        var decryStr = War.Script.Utility.Decryptor(content);
        Debug.LogWarning(decryStr);
    }

    static string GetLettersStr(List<char> letters)
    {
        var str = "";
        var maxIdx = letters.Count;
        for (var i = 0; i < maxIdx; i++)
        {
            var let = letters[i];
            str += $"'{let}'";
            if (i < maxIdx - 1)
            {
                str += ",";
            }
        }
        return str;
    }

    [MenuItem("XLua/GenerateEnLettersShif")]
    public static void GenerateEnLettersShif()
    {
        List<char> letters = new List<char>()
        { '_', };
        for (char i = 'A'; i <= 'Z'; i++)
        {
            letters.Add(i);
        }

        for (char i = 'a'; i <= 'z'; i++)
        {
            letters.Add(i);
        }
        var oldEn = GetLettersStr(letters);

        var maxIdx = letters.Count;
        var randomCount = letters.Count * 2;
        int left = 0;
        int right;
        char temp;
        System.Random random = new System.Random((int)System.DateTime.Now.Ticks);
        for (var i = 0; i < randomCount; i++)
        {
            // 生成 i ~ maxIdx 之间的随机数，不包括 maxIdx
            right = random.Next(0, maxIdx);
            temp = letters[left];
            letters[left] = letters[right];
            letters[right] = temp;
        }
        var newEn = GetLettersStr(letters);
        Debug.LogWarning($"old letters:\n{oldEn}\nto new letters:\n{newEn}");
    }

    static List<char> orgLtSet = new List<char>() {'_','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z' };
    static List<char> newLtSet = new List<char>() { 'N','F','h','C','t','B','G','Q','x','n','a','M','L','Y','J','c','I','E','R','T','_','A','V','W','l','D','y','i','u','m','v','z','j','g','k','H','d','q','X','O','K','f','p','Z','b','o','P','s','U','w','S','r','e'};
    static Dictionary<char, char> encryMap,decryMap;
    public static string EncrytorLetters(string strOrg)
    {
        if(encryMap == null)
        {
            encryMap = new Dictionary<char, char>();
            for(var i = 0; i < orgLtSet.Count; i++)
            {
                encryMap[orgLtSet[i]] = newLtSet[i];
            }
        }

        var str = "";
        foreach(var lt in strOrg)
        {
            str += encryMap[lt];
        }
        return str;
    }

    public static string DecrytorLetters(string strOrg)
    {
        if (decryMap == null)
        {
            decryMap = new Dictionary<char, char>();
            for (var i = 0; i < orgLtSet.Count; i++)
            {
                decryMap[newLtSet[i]] = orgLtSet[i];
            }
        }

        var str = "";
        foreach (var lt in strOrg)
        {
            str += decryMap[lt];
        }
        return str;
    }

    static string MapLt(List<char> ltList)
    {
        var strLt = "";
        for (var i = ltList.Count - 1; i >= 0; i--)
        {
            strLt += ltList[i];
        }
        return strLt;
    }

    [MenuItem("XLua/TestMapLetters")]
    public static void TestMapLetters()
    {
        var orgStr = "__HotFix";
        var newStr = EncrytorLetters(orgStr);
        Debug.LogWarning($"org:{orgStr}, new:{newStr}");

        orgStr = DecrytorLetters(newStr);
        Debug.LogWarning($"org:{newStr}, new:{orgStr}");

        string strLt = MapLt(new List<char> { 'x', 'i', 'f', 't', 'o', 'H', '_', '_', });
        string strLt1 = MapLt(new List<char> { 'x', 'i', 'f', 't', 'o', 'H', '_', '_', 'c', '_', });
        Debug.LogWarning(strLt);
        Debug.LogWarning(strLt1);
    }
}