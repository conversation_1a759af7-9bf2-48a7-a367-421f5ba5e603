using UnityEngine;
using UnityEditor;
using System.IO;

/*
 * 查找UI文件夹下的png,tga图,是纯色的缩小成1*1;
 */

public class FindSingleColorTexNarrow
{
    [MenuItem("Assets/Find Single Color Texture Narrow")]
    static private void FindColor2()
    {
        FindColor(new DirectoryInfo(Application.dataPath + "/UI"));
    }
    static private void FindColor(DirectoryInfo dir)
    {
        {
            DirectoryInfo[] dii = dir.GetDirectories();
            FileInfo[] fil = dir.GetFiles();
            foreach (DirectoryInfo d in dii)
            {
                FindColor(d);
            }
            foreach (FileInfo f in fil)
            {
                string texName = "Assets" + f.FullName.Replace("\\", "/").Replace(Application.dataPath, "");
                if (texName.Contains("/Minimap") == true)
                { 
                }
                else
                {
                    string a = Path.GetExtension(f.FullName.ToLower());
                    if (a == ".tga" || a == ".png")
                    { 
                        Texture2D tex = AssetDatabase.LoadAssetAtPath<Texture2D>(texName);

                        // 创建一张和texture大小相等的临时RenderTexture
                        RenderTexture tmp = RenderTexture.GetTemporary(
                                        tex.width,
                                        tex.height,
                                        0,
                                        RenderTextureFormat.Default,
                                        RenderTextureReadWrite.Linear);

                        // 将texture的像素复制到RenderTexture
                        Graphics.Blit(tex, tmp);

                        // 备份当前设置的RenderTexture
                        RenderTexture previous = RenderTexture.active;

                        // 将创建的临时纹理tmp设置为当前RenderTexture
                        RenderTexture.active = tmp;

                        // 创建一张新的可读Texture2D，并拷贝像素值
                        Texture2D myTexture2D = new Texture2D(tex.width, tex.height);

                        // 将RenderTexture的像素值拷贝到新的纹理中
                        myTexture2D.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);
                        myTexture2D.Apply();

                        // 重置激活的RenderTexture
                        RenderTexture.active = previous;

                        // 释放临时RenderTexture
                        RenderTexture.ReleaseTemporary(tmp);

                        //"myTexture2D"是可读纹理，并且和”texture”拥有相同的像素值
                        //return myTexture2D.GetPixels32(miplevel);
                        Color32[] pixels = myTexture2D.GetPixels32(0);
                        bool allsame = true;
                        if (a == ".png")
                        {
                            for (int n = 0; n < pixels.Length; n++)
                            {
                                if (pixels[n].r != pixels[0].r|| pixels[n].g != pixels[0].g|| pixels[n].b != pixels[0].b|| pixels[n].a != pixels[0].a)
                                {
                                    allsame = false;
                                    break;
                                }
                            }

                            if (allsame)
                            {   
                                tex = new Texture2D(1, 1, TextureFormat.RGBA32, true);
                                Color32[] newPixels = new Color32[1];
                                newPixels[0] = new Color32(pixels[0].r, pixels[0].g, pixels[0].b, pixels[0].a);
                                tex.SetPixels32(newPixels);

                                byte[] bytes;
                                bytes = tex.EncodeToPNG();

                                System.IO.FileStream fileSave;
                                fileSave = new FileStream(f.FullName, FileMode.Truncate);
                                System.IO.BinaryWriter binary;
                                binary = new BinaryWriter(fileSave);
                                binary.Write(bytes);
                                fileSave.Close();
                                Debug.Log(f.FullName);
                               // AssetDatabase.ImportAsset("Assets" + f.FullName.Replace("\\", "/").Replace(Application.dataPath, ""));
                            }
                        }
                        else if (a == ".tga")
                        {
                            tex = new Texture2D(1, 1, TextureFormat.RGBA32, true);
                            Color32[] newPixels = new Color32[1];
                            newPixels[0] = new Color32(pixels[0].r, pixels[0].g, pixels[0].b, pixels[0].a);
                            tex.SetPixels32(newPixels);

                            ImageCodec codec = new TGACodec(); 
                            FileStream file = new FileStream(f.FullName, FileMode.Truncate);
                            codec.Encode(file, newPixels, 1, 1, TextureFormat.ARGB32);
                            file.Close();
                            Debug.Log(f.FullName);
                            // AssetDatabase.ImportAsset("Assets" + f.FullName.Replace("\\", "/").Replace(Application.dataPath, ""));
                        }
                    }
                    else
                    {
                        continue;
                    }
                }
            }
        }
    }
}


