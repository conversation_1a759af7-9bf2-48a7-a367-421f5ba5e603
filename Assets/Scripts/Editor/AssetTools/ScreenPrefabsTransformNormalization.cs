
using UnityEditor;
using UnityEngine;

namespace War.Base
{

	public class ScreenPrefabsTransformNormalization
	{


		[MenuItem("Tool/Screen Prefabs Transform")]

		static void ScreenPrefabsTransform()
		{
			foreach (GameObject rootObj in Object.FindObjectsOfType(typeof(GameObject)) as GameObject[])
			{
				PrefabType pt = PrefabUtility.GetPrefabType(rootObj);
				if (pt == PrefabType.PrefabInstance)
				{
					double Px = rootObj.transform.localScale.x;
					double Py = rootObj.transform.localScale.y;
					double Pz = rootObj.transform.localScale.z;

					if ((Px > 1 && Px < 1.01) || (Py > 1 && Py < 1.01) || (Pz > 1 && Pz < 1.01))
					{
						rootObj.transform.localScale = Vector3.one;
						UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(rootObj.scene);
					}
					else if ((Px < 1 && Px > 0.99) || (Py < 1 && Py > 0.99) || (Pz < 1 && Pz > 0.99))
					{
						rootObj.transform.localScale = Vector3.one;
						UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(rootObj.scene);
					}
				}
				else
				{
					return;
				}	
			}
		}
	}
}

