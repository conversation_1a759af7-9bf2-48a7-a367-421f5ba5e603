
using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

public class LuaVersion
{
    // [MenuItem("XLua/Use Lua5.3")]
    // public static void UseLua53()
    // {
    //     try
    //     {
    //         File.Copy($"{Application.dataPath}/../../../Src/Client/CPP/xLua/build/plugin_lua53/Plugins/x86_64/xlua.dll",
    //             $"{Application.dataPath}/Plugins/x86_64/xlua.dll", true);
    //         Debug.LogWarning($"Lua5.3 文件拷贝完成");
    //     }
    //     catch(Exception e)
    //     {
    //         Debug.LogError(e);
    //     }
    // }

    // [MenuItem("XLua/Use Lua5.3 RevertTo Lua5.1")]
    // public static void UseLua53_RevertTo51()
    // {
    //     try
    //     {
    //         string targetPath = $"{Application.dataPath}/Plugins/x86_64";
    //         RunBat("svn", $"revert -R {targetPath}");
    //         Debug.LogWarning($"Lua5.1 revert");
    //     }
    //     catch(Exception e)
    //     {
    //         Debug.LogError(e);
    //     }
    // }

    // private static void RunBat(string program, string parm)
    // {
    //     try
    //     {
    //         System.Diagnostics.Process proc = new System.Diagnostics.Process();
    //         proc.StartInfo.FileName = program;
    //         proc.StartInfo.Arguments = string.Format(parm);//this is argument
    //         proc.StartInfo.UseShellExecute = false;
    //         proc.StartInfo.CreateNoWindow = true;
    //         proc.Start();
    //         proc.WaitForExit();
    //     }
    //     catch (Exception ex)
    //     {
    //         Console.WriteLine("Exception Occurred :{0},{1}", ex.Message, ex.StackTrace.ToString());
    //     }
    // }

    static void CopyDirectory(string source, string target)
    {
        Directory.CreateDirectory(target);

        foreach (var file in Directory.GetFiles(source))
        {
            string fileName = Path.GetFileName(file);
            string targetPath = Path.Combine(target, fileName);
            File.Copy(file, targetPath, true);
        }

        foreach (var directory in Directory.GetDirectories(source))
        {
            string directoryName = Path.GetFileName(directory);
            string targetSubDirectory = Path.Combine(target, directoryName);

            CopyDirectory(directory, targetSubDirectory);
        }
    }

    [MenuItem("XLua/Import LuaProfiler for lua5.3")]
    public static void ImportLuaProfiler()
    {
        try
        {
            Debug.LogWarning($"若本地已安装过 LuaProfiler 插件，需要先卸载!");
            var otherLuaProfiler = $"{Application.dataPath}/Plugins/LuaProfiler/";
            if(Directory.Exists(otherLuaProfiler))
            {
                //删除再复制
                DeleteLuaProfilerWithBuild();
            }
            CopyDirectory($"{Application.dataPath}/../../../Tools/LuaProfiler/", $"{Application.dataPath}/Plugins/LuaProfiler/");
            Debug.LogWarning($"LuaProfiler for lua5.3 拷贝完成");
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }

    // [MenuItem("XLua/Lua Profiler/Import WebGL LuaProfiler for lua5.3")]
    // public static void ImportWebGLLuaProfilerLua53()
    // {
    //     try
    //     {
    //         Debug.LogWarning($"若本地已安装过 LuaProfiler 插件，需要先卸载!");
    //         var otherLuaProfiler = $"{Application.dataPath}/LuaProfiler/";
    //         if(Directory.Exists(otherLuaProfiler))
    //         {
    //             Debug.LogError($"检测到其它 LuaProfiler 版本，请先卸载:{otherLuaProfiler}");
    //             return;
    //         }
    //         var webLuaProfilerEditorPath = $"{Application.dataPath}/../../../Tools/WebLuaProfiler/Editor";
    //         var desWebLuaProfilerEditorPath = $"{Application.dataPath}/WebLuaProfiler/Editor";
    //         CopyDirectory(webLuaProfilerEditorPath, desWebLuaProfilerEditorPath);
    //         File.Copy($"{webLuaProfilerEditorPath}.meta", $"{desWebLuaProfilerEditorPath}.meta", true);

    //         var webLuaProfilerPath = $"{Application.dataPath}/../../../Tools/WebLuaProfiler/LuaProfiler";
    //         var desWebLuaProfilerPath = $"{Application.dataPath}/WebLuaProfiler/LuaProfiler";
    //         CopyDirectory(webLuaProfilerPath, desWebLuaProfilerPath);
    //         File.Copy($"{webLuaProfilerPath}.meta", $"{ desWebLuaProfilerPath}.meta", true);

    //         CopyDirectory($"{Application.dataPath}/../../../Tools/WebLuaProfiler/WebGLPlugins", $"{Application.dataPath}/../WebGLPlugins");
    //         Debug.LogWarning($"WebGL LuaProfiler for lua5.3 拷贝完成");
    //     }
    //     catch (Exception e)
    //     {
    //         Debug.LogError(e);
    //     }
    // }

    // [MenuItem("XLua/Lua Profiler/Delete WebGL LuaProfiler for lua5.3")]
    // public static void DeleteWebGLLuaProfilerLua53()
    // {
    //     try
    //     {
    //         var webLuaProfilerPath = $"{Application.dataPath}/WebLuaProfiler";
    //         bool result = EditorUtility.DisplayDialog("Confirmation", $"将删除目录:{webLuaProfilerPath}", "Yes", "No");
    //         if (result)
    //         {
    //             Directory.Delete(webLuaProfilerPath, true);
    //         }

    //         var webGLPluginsPath = $"{Application.dataPath}/../WebGLPlugins";
    //         result = EditorUtility.DisplayDialog("Confirmation", $"将还原目录修改:{webGLPluginsPath}", "Yes", "No");
    //         if (result)
    //         {
    //             var arg = $"/C svn.exe revert -R {webGLPluginsPath}";

    //             using (System.Diagnostics.Process process = new System.Diagnostics.Process())
    //             {
    //                 process.StartInfo.FileName = "cmd.exe";
    //                 process.StartInfo.Arguments = arg;
    //                 process.Start();
    //                 process.WaitForExit();
    //             }
    //         }

    //         Debug.LogWarning($"WebGL LuaProfiler for lua5.3 清理完成");
    //     }
    //     catch (Exception e)
    //     {
    //         Debug.LogError(e);
    //     }
    // }

    public static void DeleteLuaProfilerWithBuild()
    {
        try
        {
            var webLuaProfilerPath = $"{Application.dataPath}/Plugins/LuaProfiler";
            Directory.Delete(webLuaProfilerPath, true);

            // var webGLPluginsPath = $"{Application.dataPath}/../WebGLPlugins";
            // var arg = $"/C svn.exe revert -R {webGLPluginsPath}";

            // using (System.Diagnostics.Process process = new System.Diagnostics.Process())
            // {
            //     process.StartInfo.FileName = "cmd.exe";
            //     process.StartInfo.Arguments = arg;
            //     process.Start();
            //     process.WaitForExit();
            // }

            Debug.LogWarning($"WebGL LuaProfiler for lua5.3 清理完成");
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }

    [MenuItem("XLua/Lua Profiler/Enable WebGL LuaProfiler", false, 1)]
    public static void EnableLuaProfiler()
    {
        //var webglLuaProfiler = JenkinsEnv.Instance.GetBool("webglLProfiler", false);
        //Debug.Log("webglLProfiler : " + webglLuaProfiler);

        EnableLuaProfiler(true);
    }

    [MenuItem("XLua/Lua Profiler/Disable WebGL LuaProfiler", false, 1)]
    public static void DisableLuaProfiler()
    {
        EnableLuaProfiler(false);
    }

    public static void EnableLuaProfiler(bool bEnable)
    {
        try
        {
            var webglLuaProfiler = bEnable;

            // 开启通用 Lua Profiler 开关
            AutoBuildDLL.ModifyProjectSettingMacro(webglLuaProfiler, "USE_LUA_PROFILER", BuildTargetGroup.Android);
            // 开启 WebGL Lua Profiler 开关
            //AutoBuildDLL.ModifyProjectSettingMacro(webglLuaProfiler, "USE_LUA_PROFILER_WEBGL", BuildTargetGroup.WebGL);

            if (bEnable)
            {
                Debug.LogWarning($"Enable LuaProfiler 设置完成");
            }
            else
            {
                Debug.LogWarning($"Disable LuaProfiler 设置完成");
            }
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }
    }
}