using System;
using UnityEditor;
using UnityEngine;

public class EditPrefabAssetScope : IDisposable
{

    public readonly string assetPath;
    public readonly GameObject prefabRoot;
    public bool isDirty = false;

    public EditPrefabAssetScope(string assetPath)
    {
        this.assetPath = assetPath;
        prefabRoot = PrefabUtility.LoadPrefabContents(assetPath);
    }

    public void SetDirty(bool isDirty)
    {
        this.isDirty = isDirty;
    }

    public void Dispose()
    {
        if (isDirty)
        {
            PrefabUtility.SaveAsPrefabAsset(prefabRoot, assetPath);
        }
        PrefabUtility.UnloadPrefabContents(prefabRoot);
    }
}
