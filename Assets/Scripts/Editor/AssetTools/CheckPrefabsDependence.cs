using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;

public class CheckPrefabsDependence : Editor
{
    [MenuItem("Assets/CheckDependence")]
    private static void Check()
    {
        string pathPrefab = AssetDatabase.GetAssetPath(Selection.activeObject);
        CopyPrefabs(pathPrefab);
    }

    private static string rootPath = "C:/PrefabsDependence/";
    private static void CopyPrefabs(string path)
    {
        if (path.EndsWith("prefab") && !File.Exists(rootPath + path))
        {
            string copyPath = rootPath + GetDirectory(path);
            if (!Directory.Exists(copyPath))
            {
                Directory.CreateDirectory(copyPath);
            }
            CopyFile(path);
            string[] pathsDependence = AssetDatabase.GetDependencies(path);
            foreach (string subPath in pathsDependence)
            {
                CopyPrefabs(subPath);
            }
        }
        else
        {
            string copyPath = rootPath + GetDirectory(path);
            if (!Directory.Exists(copyPath))
            {
                Directory.CreateDirectory(copyPath);
            }
            CopyFile(path);
        }
    }

    private static void CopyFile(string path)
    {
        string pathMeta = path + ".meta";
        File.Copy(Application.dataPath + "/" + GetRelativeDir(path), rootPath + path, true);
        File.Copy(Application.dataPath + "/" + GetRelativeDir(pathMeta), rootPath + pathMeta, true);
    }
    private static string GetDirectory(string path)
    {
        string[] dir = path.Split('/');
        string path_1 = dir[0] + "/";
        for (int i = 1; i < dir.Length - 1; i++)
        {
            path_1 += dir[i] + "/";
        }
        return path_1;
    }

    private static string GetRelativeDir(string path)
    {
        string[] dir = path.Split('/');
        string path_1 = "/" + dir[1];
        for (int i = 2; i < dir.Length; i++)
        {
            path_1 += "/" + dir[i];
        }
        return path_1;
    }
}
