using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.IO;
using TMPro;

public class UIPrefabToolEditor : Editor
{
    private static string prefabPath = "Assets/UI/Prefabs";

    public static string[] GetAllPrefabPath()
    {
        var path = Directory.GetFiles(prefabPath, "*.prefab");

        return path;
    }

    /// <summary>
    /// 检查所有textmeshPro的描边大于1的资源
    /// </summary>
    [MenuItem("UIPrefabTool/查找预制体中width=1的texthmeshPro")]
    public static void CheckAllPrefabWithTextMeshPro()
    {
        var paths = GetAllPrefabPath();
        EditorUtility.DisplayProgressBar("查找中", 0 + "/" + paths.Length, 0);
        for (int i = 0; i < paths.Length; i++)
        {
            var go = AssetDatabase.LoadAssetAtPath<GameObject>(paths[i]);
            if (ContainsTmpComponent(go))
            {
                Debug.LogError(go.name, go);
                FormatPrefab(go);
                EditorUtility.SetDirty(go);
                // Debug.Log("设置了一个了");
                AssetDatabase.SaveAssets();
                // AssetDatabase.Refresh();
                // break;
            }

            EditorUtility.DisplayProgressBar("查找中", i + "/" + paths.Length, i * 1.0f / paths.Length);
        }
        AssetDatabase.Refresh();

        EditorUtility.UnloadUnusedAssetsImmediate();
        EditorUtility.ClearProgressBar();
    }

 

    public static bool ContainsTmpComponent(GameObject go)
    {
        if (go != null)
        {
            TextMeshProUGUI[] tmps = go.GetComponentsInChildren<TextMeshProUGUI>(true);

            if (tmps != null && tmps.Length > 1)
            {
                return true;
            }
        }

        return false;
    }


    public static void FormatPrefab(GameObject prefab)
    {
        TextMeshProUGUI[] tmps = prefab.GetComponentsInChildren<TextMeshProUGUI>(true);


        foreach (var tmp in tmps)
        {
            if (tmp.font!=null)
            {
                if (tmp.font.name.ToLower().Contains("fzy4jw"))
                {
                    ///if (tmp.curOutlineWidth > 0.4f)
                   /// {
                   ///     tmp.curOutlineWidth = 0.574f;
                  ///  }
                 ///   else
                 //   {
                 //       tmp.curOutlineWidth = 0.287f;
                 //   }
                    // EditorUtilit
                    Debug.Log("替换成功：" + tmp.gameObject.name, tmp.gameObject);
                }
                else if (tmp.font.name.ToLower().Contains("clashroyale"))
                {
                   /// if (tmp.curOutlineWidth > 0.6f)
                  ///  {
                   ///     tmp.curOutlineWidth = 0.8f;
                  ///  }
                  ///  else
                  ///  {
                  ///      tmp.curOutlineWidth = 0.4f;
                 ///   }
                    

                    Debug.Log("替换成功：" + tmp.gameObject.name, tmp.gameObject);
                }
                else
                {
                    Debug.LogError("找不到匹配的字体对应的描边大小" + tmp.font.name, tmp.gameObject);
                } 
            }
            else
            {
                Debug.LogError("该文件没有设置字体 跳过了",prefab);
            }
          
        }
    }
}