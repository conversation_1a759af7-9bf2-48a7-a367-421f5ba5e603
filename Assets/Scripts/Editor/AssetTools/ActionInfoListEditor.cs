using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEditor;
using UnityEditor.UI;
using System.Text.RegularExpressions;
using System.Linq;

[CustomEditor(typeof(ActionInfoList), true)]
public class ActionInfoListEditor : Editor
{
    ActionInfoList actionInfoList;
    string[] actionNames;
    int selectedIndex;

    Regex matchNumber = new Regex(@"\d+");
    GameObject filerActionGameObjs;

    public class ActionMeshInfo
    {
        public string name;
        public int index;
        public Object mesh;
    }

    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        EditorGUILayout.Space(20);

        if(actionNames == null)
        {
            UpdateActionNames();
        }

        if (selectedIndex >= actionNames.Length)
        {
            selectedIndex = 0;
        }
        var actionInfoSet = ActionInfoSet;
        filerActionGameObjs = EditorGUILayout.ObjectField($"动作节点:", filerActionGameObjs, typeof(GameObject), true) as GameObject;
        if(filerActionGameObjs == null)
        {
            return;
        }

        EditorGUILayout.BeginHorizontal();
        selectedIndex = EditorGUILayout.Popup($"当前动作:", selectedIndex, actionNames);
        EditorGUILayout.EndHorizontal();

        var currentActionName = actionNames[selectedIndex];
        if (GUILayout.Button($"Fill -> {currentActionName}") && actionInfoList)
        {
            var currentFrameList = GetFrameList(currentActionName);
            if(currentFrameList == null)
            {
                Debug.LogError($"未能找到指定的动作列表:{currentActionName}");
            }
            else
            {
                var chiledCount = filerActionGameObjs.transform.childCount;
                var meshInfoSet = new List<ActionMeshInfo>();
                for (int i = 0; i < chiledCount; i++)
                {
                    var obj = filerActionGameObjs.transform.GetChild(i);
                    var resultString = matchNumber.Match(obj.name).Value;
                    if (int.TryParse(resultString, out int number))
                    {
                        var mesh = obj.GetComponentInChildren<MeshFilter>();
                        if (mesh == null)
                        {
                            continue;
                        }
                        var actionMesh = new ActionMeshInfo()
                        {
                            name = obj.name,
                            index = number,
                            mesh = mesh.sharedMesh
                        };
                        meshInfoSet.Add(actionMesh);
                    }
                }
                meshInfoSet.Sort((x, y) => x.index < y.index ? -1 : 1);
                currentFrameList.Clear();

                foreach (var meshInfo in meshInfoSet)
                {
                    currentFrameList.Add(meshInfo.mesh);
                }
            }
        }
    }

    public List<Object> GetFrameList(string actionName)
    {
        ActionInfoList.ActionInfo actionInfo;
        var actionList = ActionInfoSet.actionList;
        for (int i = 0; i < actionList.Length; i++)
        {
            actionInfo = actionList[i];
            if(actionName == actionInfo.name)
            {
                return actionInfo.frameList;
            }
        }

        return null;
    }

    public void UpdateActionNames()
    {
        var actionList = ActionInfoSet.actionList;
        if (actionNames == null || actionNames.Length != actionList.Length)
        {
            actionNames = new string[actionList.Length];
        }
        ActionInfoList.ActionInfo actionInfo;
        for (int i = 0; i < actionList.Length; i++)
        {
            actionInfo = actionList[i];
            if (string.IsNullOrEmpty(actionInfo.name))
            {
                continue;
            }
            actionNames[i] = actionInfo.name;
        }
    }

    public ActionInfoList ActionInfoSet
    {
        get
        {
            if (actionInfoList == null)
            {
                actionInfoList = target as ActionInfoList;
            }
            return actionInfoList;
        }
    }

    public void OnValidate()
    {
        UpdateActionNames();
    }
}
