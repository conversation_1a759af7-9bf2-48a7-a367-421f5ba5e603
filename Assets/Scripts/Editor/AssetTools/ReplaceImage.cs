using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using System.Security.Cryptography;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Text;
using System;
using UnityEngine.UI;
using War.UI;
/*
读取json格式来替换预制体中的图片
*/
public class ReplaceImage
{

    static private string szSelectedPath = null;
    static private string withoutExtensions = "*.json";
    [MenuItem("Assets/Replace Image")]
    static private bool Replace()
    {
        bool res = false;
        do
        {

            szSelectedPath = AssetDatabase.GetAssetPath(Selection.activeObject);
            if (string.IsNullOrEmpty(szSelectedPath) || !File.Exists(szSelectedPath) || !withoutExtensions.Contains(Path.GetExtension(szSelectedPath).ToLower()))
            {
                Debug.Log("路径不存在或是文件夹或则不是json文件");
                break;
            }

            string szJsonData = File.ReadAllText(szSelectedPath);
            //key:被替换预制体的路径，value：被替换图片列表<被替换图片，替换图片>
            Dictionary<string, object> dicNeedModify = (Dictionary<string, object>)MiniJSON.Json.Deserialize(szJsonData);
            Replace(dicNeedModify);
            Debug.Log("替换完成");
        } while (false);
        return res;
    }

    static private bool Replace(Dictionary<string, object> dicNeedModify)
    {
        bool res = false;
        do
        {
            
            string szHasReplacePath = Path.GetDirectoryName(szSelectedPath) + "/HasReplace.txt";//已经替换图片的路径
            File.Delete(szHasReplacePath);
            
            string szNeedDelPath = Path.GetDirectoryName(szSelectedPath) + "/NeedDel.txt";//需要删除图片的路径
            File.Delete(szNeedDelPath);

            string szUseImgPath = Path.GetDirectoryName(szSelectedPath) + "/UseImg.txt";//使用图片的路径
            File.Delete(szUseImgPath);

            string szModifyPrefabPath = Path.GetDirectoryName(szSelectedPath) + "/ModifyPrefab.txt";//修改预制体的路径
            File.Delete(szModifyPrefabPath);

            if (null == dicNeedModify || 0 == dicNeedModify.Count)
                break;

            Dictionary<string, GameObject> dicPrefab = new Dictionary<string, GameObject>();
            foreach (KeyValuePair<string, object> kv in dicNeedModify)
            {
                GameObject prefab = null;
                if (!dicPrefab.TryGetValue(kv.Key, out prefab))//不存在
                {
                    prefab = CreateGameObject(kv.Key);
                    dicPrefab.Add(kv.Key, prefab);
                }

                List<object> listData = (List<object>)kv.Value;
                for (int nIndex = 0; nIndex < listData.Count; ++nIndex)
                {
                    Dictionary<string, object> dic = (Dictionary<string, object>)listData[nIndex];
                    foreach (KeyValuePair<string, object> kvImg in dic)
                    {
                        string oldImgPath = kvImg.Key;
                        string newImgPath = kvImg.Value.ToString();
                        if (File.Exists(oldImgPath) && File.Exists(newImgPath))
                        {
                            Sprite newSprite = CreateSprite(newImgPath);
                            Sprite oldSprite = CreateSprite(oldImgPath);
                            if (!oldSprite.Equals(newSprite) && (Replace(prefab, oldSprite, newSprite) || ReplaceSS(prefab, oldSprite, newSprite)))
                            {
                                string szContent = kv.Key + " 图片：" + oldImgPath + " 被替换成：" + newImgPath;
                                File.AppendAllText(szHasReplacePath, szContent + "\r\n");
                                File.AppendAllText(szNeedDelPath, oldImgPath + "\r\n");
                                File.AppendAllText(szUseImgPath, newImgPath + "\r\n");
                                File.AppendAllText(szModifyPrefabPath, kv.Key + "\r\n");
                                Debug.Log(szContent);
                            }
                            else
                            {
                                string szContent = kv.Key + " 图片：" + oldImgPath + " 要被替换成：" + newImgPath + " 替换失败";
                                Debug.Log(szContent);
                            }
                        }
                        else
                        {
                            string szContent =  " 图片：" + oldImgPath + " " + newImgPath + "不存在";
                            Debug.Log(szContent);
                        }
                    }

                }

            }
        } while (false);
        return res;
    }

    //修改预制体
    static private bool Replace(GameObject prefab, Sprite oldSprite, Sprite newSprite)
    {
        bool res = false;
        do
        {
            if (null == prefab || null == newSprite || null == oldSprite)
                break;
            Image[] arrComImg = prefab.GetComponentsInChildren<Image>(true);
            for (int index = 0; index < arrComImg.Length; index++)
            {
                if (null != arrComImg[index].mainTexture)
                {
                    Sprite sprite = arrComImg[index].sprite;
                    if (null != sprite && sprite.Equals(oldSprite))
                    {
                        arrComImg[index].sprite = newSprite;
                        EditorUtility.SetDirty(prefab);
                        AssetDatabase.SaveAssets();
                        AssetDatabase.Refresh();
                        res = true;
                    }
                }
            }
        } while (false);
        return res;
    }

    //修改预制体
    static private bool ReplaceSS(GameObject prefab, Sprite oldSprite, Sprite newSprite)
    {
        bool res = false;
        do
        {
            if (null == prefab || null == newSprite || null == oldSprite)
                break;
            SpriteSwitcher[] arrComSS = prefab.GetComponentsInChildren<SpriteSwitcher>(true);
            for (int nIndex = 0; nIndex < arrComSS.Length; nIndex++)
            {
                Sprite[] arrSprite = arrComSS[nIndex].SpriteList;
                if (null != arrSprite)
                {
                    for (int nIndexReplace = 0; nIndexReplace < arrSprite.Length; ++nIndexReplace)
                    {
                        if (null != arrSprite[nIndexReplace] && arrSprite[nIndexReplace].Equals(oldSprite))
                        {
                            arrComSS[nIndex].SpriteList[nIndexReplace] = newSprite;
                            EditorUtility.SetDirty(prefab);
                            AssetDatabase.SaveAssets();
                            AssetDatabase.Refresh();
                            res = true;
                        }
                    }
                }

            }
        } while (false);
        return res;
    }

    static private GameObject CreateGameObject(string objPath)
    {
        return string.IsNullOrEmpty(objPath) ? null : AssetDatabase.LoadAssetAtPath(objPath, typeof(GameObject)) as GameObject;
    }

    static private Sprite CreateSprite(string imgPath)
    {
        return string.IsNullOrEmpty(imgPath) ? null : (Sprite)AssetDatabase.LoadAssetAtPath(imgPath, typeof(Sprite));
    }

    [MenuItem("Assets/Replace Image", true)]
    static private bool VReplace()
    {
        szSelectedPath = AssetDatabase.GetAssetPath(Selection.activeObject);
        return (!string.IsNullOrEmpty(szSelectedPath));
    }

}
