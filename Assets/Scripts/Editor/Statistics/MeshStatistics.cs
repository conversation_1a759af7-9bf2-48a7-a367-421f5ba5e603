#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
#if WX_LOD_OPTIMIZE
using War.Battle;
#endif

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/网格统计", false, 1)]
        public static void MeshStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(MeshStatisticsCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool MeshStatisticsCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Characters");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Mesh", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,顶点数,面数,顶点属性大小(Byte),顶点缓冲区大小(KB)\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;
                ;

                Mesh asset = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                int vertexSize;
                decimal vertexMemory = AssetStatisticsUtility.GetMeshMemoryUsage(asset, out vertexSize);
                statistics.AppendLine(
                    $"{path}," +
                    $"{asset.vertexCount}," +
                    $"{asset.triangles.Length / 3}," +
                    $"{vertexSize}," +
                    $"{AssetStatisticsUtility.ByteToKB(vertexMemory, true)}");
            }

            File.WriteAllText($"{outputDir}/MeshStatistics.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }

#if WX_LOD_OPTIMIZE
        [MenuItem("Assets/MiniGameTool/同步英雄LOD网格统计", false, 1)]
        public static void SyncLodStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                CardConfigEditor.ReplaceCardMesh2ABName();
                ExecuteCommand(nameof(LodStatisticsCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        [MenuItem("Assets/MiniGameTool/LOD网格统计", false, 1)]
        public static void LodStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(LodStatisticsCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        public static bool LodStatisticsCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Characters");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder();
            statistics.AppendLine(
                "资源路径," +
                "原始顶点数," +
                "压缩顶点数," +
                "顶点压缩比," +
                "原始三角面数," +
                "压缩三角面数," +
                "三角面压缩比," +
                "原始顶点属性(Byte)," +
                "压缩后顶点属性(Byte)," +
                "原始顶点缓冲区(KB)," +
                "压缩后顶点缓冲区(KB)");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);

                // 判断Prefab是否带有CardMeshSimplify组件
                CardMeshSimplify[] simplifiers = asset.GetComponentsInChildren<CardMeshSimplify>();
                if (simplifiers != null && simplifiers.Length > 0)
                {
                    foreach (CardMeshSimplify simplifier in simplifiers)
                    {
                        string[] paths =
                            AssetDatabase.GetAssetPathsFromAssetBundle(simplifier.originalMeshABName ?? "");
                        if (!simplifier.originalMeshABName.EndsWith(".mesh"))
                        {
                            Debug.LogWarning(
                                $"{path}中{simplifier.gameObject.name}节点的CardMeshSimplify组件，originalMeshABName不正确：{simplifier.originalMeshABName}");
                            continue;
                        }

                        if (paths.Length > 0)
                        {
                            Mesh originalMesh = AssetDatabase.LoadAssetAtPath<Mesh>(paths[0]);
                            Mesh prefabMesh = simplifier.simplifyMesh;
                            int originalVertexCount = originalMesh.vertexCount;
                            int originalTriangleCount = originalMesh.triangles.Length / 3;
                            int originalVertexSize;
                            decimal originalVertexMemory =
                                AssetStatisticsUtility.GetMeshMemoryUsage(originalMesh, out originalVertexSize);
                            int prefabVertexCount = prefabMesh?.vertexCount ?? originalVertexCount;
                            int prefabTriangleCount = prefabMesh?.triangles.Length / 3 ?? originalTriangleCount;
                            int prefabVertexSize;
                            decimal prefabVertexMemory =
                                AssetStatisticsUtility.GetMeshMemoryUsage(prefabMesh, out prefabVertexSize);
                            statistics.Append(path);
                            statistics.Append(",");
                            statistics.Append(originalVertexCount);
                            statistics.Append(",");
                            statistics.Append(prefabVertexCount);
                            statistics.Append(",");
                            statistics.Append(String.Format("{0:N3}", (float)prefabVertexCount / originalVertexCount));
                            statistics.Append(",");
                            statistics.Append(originalTriangleCount);
                            statistics.Append(",");
                            statistics.Append(prefabTriangleCount);
                            statistics.Append(",");
                            statistics.Append(String.Format("{0:N3}",
                                (float)prefabTriangleCount / originalTriangleCount));
                            statistics.Append(",");
                            statistics.Append(originalVertexSize);
                            statistics.Append(",");
                            statistics.Append(prefabVertexSize);
                            statistics.Append(",");
                            statistics.Append(AssetStatisticsUtility.ByteToKB(originalVertexMemory, true));
                            statistics.Append(",");
                            statistics.Append(AssetStatisticsUtility.ByteToKB(prefabVertexMemory, true));
                            statistics.Append("\n");
                        }
                    }
                }
            }

            File.WriteAllText($"{outputDir}/LodStatistics.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
#endif

        [MenuItem("Assets/MiniGameTool/高模引用检查", false, 1)]
        public static void MeshCheck()
        {
            string output = EditorUtility.OpenFolderPanel("导出表格", Application.dataPath, "");

            try
            {
                ExecuteCommand(nameof(MeshCheckCommand), $"{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool MeshCheckCommand(string[] args)
        {
            if (args.Length < 1)
            {
                throw new Exception("Invalid Arguments!");
            }

            string outputDir = args[0];

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            StringBuilder statistics = new StringBuilder();
            string[] abNames = AssetDatabase.GetAllAssetBundleNames();
            statistics.AppendLine("引用源头,引用网格");
            bool success = true;
            foreach (string abName in abNames)
            {
                string[] dependencies = AssetDatabase.GetAssetBundleDependencies(abName, true);
                foreach (string dependABName in dependencies)
                {
                    if (dependABName.EndsWith(".mesh"))
                    {
                        statistics.AppendLine($"{abName},{dependABName}");
                        success = false;
                        break;
                    }
                }
            }

            File.WriteAllText($"{outputDir}/MeshCheck.csv", statistics.ToString(), Encoding.UTF8);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the MeshCheck.csv, Please open modify!");
            }

            return success;
        }
    }
}