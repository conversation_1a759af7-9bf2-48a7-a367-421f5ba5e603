#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using UnityEngine.UI;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Tools/Resources/Image点击处理", false, 1)]
        public static void CheckImageClickStatistics()
        {
            /*            string input = AssetStatisticsUtility.GetSelectionFolder();
                        string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

                        try
                        {
                            ExecuteCommand(nameof(CheckImageClickCommand), $"{input},{output}");
                        }
                        catch (Exception e)
                        {
                            Debug.LogError(e);
                        }*/
            CheckImageClickCommand(null);
        }
 


        private static bool CheckImageClickCommand(string[] args)
        {
            /*            if (args.Length < 1)
                        {
                            throw new Exception("Invalid Arguments!");
                        }

                        string outputDir = args[0];

                        if (outputDir.Contains("$Default"))
                        {
                            outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
                        }
                        if (!Directory.Exists(outputDir))
                        {
                            throw new Exception("Invalid Directory!");
                        }*/
            StringBuilder sb = new StringBuilder();
            string[] guids = AssetDatabase.FindAssets("t:prefab", new string[] { "Assets" });
            List<string> list1 = new List<string>();
            List<string> list2 = new List<string>();
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                bool isminiGame = path.Contains("Assets/CasualGame");

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab != null)
                {
                    Button[] buttons = prefab.GetComponentsInChildren<Button>(true);
                    foreach (Button btn in buttons)
                    {
                        var image = btn.targetGraphic as Image;
                        if (image != null) 
                        {
                            if (image.color.a == 0 && image.raycastTarget)
                            {
                                sb.AppendLine(path + "," + image.name);
                                if (isminiGame)
                                {
                                    if (!list1.Contains(path))
                                    {
                                        list1.Add(path);
                                    }
                                }
                                else
                                {
                                    if (!list2.Contains(path))
                                    {
                                        list2.Add(path);
                                    }
                                }
                            }
                        }

                        /*                   // 替换成自定义组件 Empty4Raycast
                                           Empty4Raycast empty4Raycast = image.gameObject.AddComponent<Empty4Raycast>();
                                           empty4Raycast.color = image.color;
                                           empty4Raycast.material = image.material;
                                           empty4Raycast.sprite = image.sprite;

                                           DestroyImmediate(image);*/
                    }
                }
            }
            Debug.LogError("小游戏统计个数:" + list1.Count + "个项目内统计个数:" + list2.Count + "个");

            File.WriteAllText($"{Application.dataPath}/aaa.csv", sb.ToString(), Encoding.UTF8);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the aaa.csv, Please open modify!");
            }
            return success;
        }
    }
}