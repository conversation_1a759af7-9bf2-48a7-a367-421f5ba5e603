#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif
using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Tools/Resources/Ab纹理资源格式检测", false, 1)]
        public static void CheckAbTextureFormatStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(CheckAbTextureFormatCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        private static bool CheckAbTextureFormatCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            Debug.Log($"CheckAbTextureFormatStatistics args.Length : {args.Length}");
            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }
            if (args.Length >= 3)
            {
                string checkAbPath = args[2];
                CheckTextureFormatTest(checkAbPath);
            }
            else 
            {
                Debug.LogError($"CheckAbTextureFormatStatistics args.Length : {args.Length}");
            }

            StringBuilder statistics = new StringBuilder();
            statistics.AppendLine("文件路径,错误码");
            bool success = CheckResultSaveFile(outputDir);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the CheckFormatResult.csv, Please open modify!");
            }
            return success;
        }

        public static void CheckTextureFormatTest(string checkAbPath)
        {
            var dxtSaveRoot = $"{Application.dataPath}/../AssetBundles/WebGL_Format/"; //未使用
            var suffix = JenkinsEnv.Instance.Get("wxReplaceBundleFileNames", "prefab;png;asset;spritepack;anim;mat;mesh;tga;fbx;jpg;playable;unity3d;spriteatlas;assets;atlas;obj;exr;psd;unity;");
            try
            {
                var bSuccess = CheckTextureFormatUtility.CheckTextureFolder(ref dxtSaveRoot, suffix, checkAbPath);
                if (!bSuccess)
                {
                    Debug.LogError($"Check Texture Ab Failed!");
                    //EditorApplication.Exit(1);
                }
                else
                {
                    Debug.LogWarning($"Check Texture Ab Success!");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Check Texture Ab  Failed!\n{e}");
                //EditorApplication.Exit(1);
            }
        }


        static bool CheckResultSaveFile(string outputDir)
        {
            var LogDic = CheckTextureFormatUtility.LogDic;
            bool success = true;
            if (LogDic.Count > 0)
            {
                foreach (var item in LogDic)
                {
                    if (item.Value!=null && item.Value.Count>0)
                        success = false;
                }
            }
            StringBuilder statistics = new StringBuilder();
            //总结放在最前面
            statistics.AppendLine("ab资源规范检测 .spritepack被标记了PackingTag的此工程Assetbundles下删除对应图集缓存可更新" );
            foreach (var item in LogDic)
            {
                statistics.AppendLine($"{item.Key}: <{item.Value.Count}>个");
                int index = 1;
                foreach (var item1 in item.Value)
                {
                    if (index < 10)
                    {
                        statistics.AppendLine($"  {item1}");
                        index++;
                    }
                }
            }
            statistics.AppendLine("\r\n \r\n");
            statistics.AppendLine("具体资源列表如下");
            foreach (var item in LogDic)
            {
                statistics.AppendLine(item.Key);
                foreach (var item1 in item.Value)
                {
                    statistics.AppendLine(item1);
                }
            }
            File.WriteAllText($"{outputDir}/CheckFormatResult.csv", statistics.ToString(), Encoding.UTF8);
            return success;
        }

    }
}
