using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Security.Cryptography;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using Object = UnityEngine.Object;

namespace StatisticsTools
{
    public static class AssetStatisticsUtility
    {
        public const string UNITY_DEFAULT_RESOURCES = "Library/unity default resources";

        public const string CUSTOM_PARTICLE_MATERIAL = "Assets/Art/Materials/Custom-Default-ParticleSystem.mat";
        
        /// <summary>
        /// 获取外部AssetBundle导出路径
        /// </summary>
        /// <returns></returns>
        public static string GetAssetBundlesRoot()
        {
            string output = Path.Combine(Path.GetDirectoryName(Application.dataPath),
                "AssetBundles",
                War.Base.BaseLoader.GetPlatformFolderForAssetBundles());
            if (!Directory.Exists(output))
            {
                Directory.CreateDirectory(output);
            }

            return output;
        }

        /// <summary>
        /// 获取默认输出路径
        /// </summary>
        /// <param name="version">版本号</param>
        /// <returns></returns>
        public static string GetDefaultOutput(int version)
        {
            string output = Path.Combine(Path.GetDirectoryName(Application.dataPath), "Statistics", $"{version}");
            if (!Directory.Exists(output))
            {
                Directory.CreateDirectory(output);
            }

            return output;
        }

        /// <summary>
        /// 获取最后输出的版本号
        /// </summary>
        /// <param name="maxVersion">最大版本号</param>
        /// <returns></returns>
        public static int GetLastVersion(int maxVersion = int.MaxValue)
        {
            int lastVersion = 0;
            string root = Path.Combine(Path.GetDirectoryName(Application.dataPath), "Statistics");
            if (Directory.Exists(root))
            {
                string[] paths = Directory.GetDirectories(root);
                if (paths != null)
                {
                    foreach (string path in paths)
                    {
                        int version;
                        if (int.TryParse(Path.GetFileName(path), out version) && version < maxVersion)
                        {
                            lastVersion = Math.Max(lastVersion, version);
                        }
                    }
                }
            }

            return lastVersion;
        }

        /// <summary>
        /// 获取Unity当前选中的文件夹路径
        /// </summary>
        /// <returns></returns>
        public static string GetSelectionFolder()
        {
            string folder = string.Empty;

            foreach (string selectGuid in Selection.assetGUIDs)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(selectGuid);
                if (AssetDatabase.IsValidFolder(assetPath))
                {
                    folder = assetPath;
                    break;
                }
            }

            return folder;
        }

        /// <summary>
        /// 文本格式转换，字节转MB
        /// </summary>
        /// <param name="byteLength">字节长度</param>
        /// <param name="ignoreUnit">是否忽略单位字符</param>
        /// <returns></returns>
        public static string ByteToMB(decimal byteLength, bool ignoreUnit = false)
        {
            string number = $"{string.Format("{0:F2}", byteLength / 1024 / 1024)}";
            if (!ignoreUnit)
            {
                number += "MB";
            }

            return number;
        }

        /// <summary>
        /// 文本格式转换，字节转KB
        /// </summary>
        /// <param name="byteLength">字节长度</param>
        /// <param name="ignoreUnit">是否忽略单位字符</param>
        /// <returns></returns>
        public static string ByteToKB(decimal byteLength, bool ignoreUnit = false)
        {
            string number = $"{string.Format("{0:F2}", byteLength / 1024)}";
            if (!ignoreUnit)
            {
                number += "KB";
            }

            return number;
        }

        /// <summary>
        /// 根据文件路径生成ABName
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="extension">AB扩展名</param>
        /// <returns></returns>
        public static string GetABName(string path, string extension)
        {
            string fileName = $"{Path.GetFileNameWithoutExtension(path)}{extension}".ToLower();
            string dir = Path.GetDirectoryName(path)
                .ToLower()
                .Replace(" ", "_")
                .Replace("\\", "/")
                .Replace("assets/", "");
            return $"{dir}/{fileName}";
        }
        
        /// <summary>
        /// 获取网格内存
        /// </summary>
        /// <param name="mesh">网格实例</param>
        /// <param name="vertexSize">输出网格顶点数</param>
        /// <returns></returns>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public static decimal GetMeshMemoryUsage(Mesh mesh, out int vertexSize)
        {
            VertexAttributeDescriptor[] descriptors = mesh.GetVertexAttributes();
            int vertexCount = mesh.vertexCount;
            vertexSize = 0;

            // 计算每个顶点大小
            foreach (VertexAttributeDescriptor descriptor in descriptors)
            {
                switch (descriptor.format)
                {
                    case VertexAttributeFormat.Float32:
                    case VertexAttributeFormat.UInt32:
                    case VertexAttributeFormat.SInt32:
                        vertexSize += 4 * descriptor.dimension;
                        break;
                    case VertexAttributeFormat.Float16:
                    case VertexAttributeFormat.UNorm16:
                    case VertexAttributeFormat.SNorm16:
                    case VertexAttributeFormat.UInt16:
                    case VertexAttributeFormat.SInt16:
                        vertexSize += 2 * descriptor.dimension;
                        break;
                    case VertexAttributeFormat.UNorm8:
                    case VertexAttributeFormat.SNorm8:
                    case VertexAttributeFormat.UInt8:
                    case VertexAttributeFormat.SInt8:
                        vertexSize += 1 * descriptor.dimension;
                        break;
                    default:
                        throw new ArgumentOutOfRangeException(nameof(descriptor.format), descriptor.format,
                            $"Unknown vertex format {descriptor.format}");
                }
            }

            // 计算所有顶点数据大小
            decimal totalVerticesMemorySize = (decimal)vertexCount * vertexSize;

            return totalVerticesMemorySize;
        }

        /// <summary>
        /// 获取原始图片宽和高，非导入大小
        /// </summary>
        /// <param name="importer">图片导入器</param>
        /// <param name="width">原始图片宽度</param>
        /// <param name="height">原始图片高度</param>
        public static void GetSourceTextureWidthAndHeight(TextureImporter importer, out int width, out int height)
        {
            if (importer != null)
            {
                object[] args = new object[2] { 0, 0 };
                MethodInfo mi = typeof(TextureImporter).GetMethod("GetWidthAndHeight", BindingFlags.NonPublic | BindingFlags.Instance);
                mi.Invoke(importer, args);

                width = (int)args[0];
                height = (int)args[1];
            }
            else
            {
                width = 0;
                height = 0;
            }
        }

        /// <summary>
        /// 获取文件MD5
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <param name="isUpper">是否大写</param>
        /// <returns></returns>
        public static string GetFileMD5(string path, bool isUpper = true)
        {
            using (var md5 = MD5.Create())
            {
                using (var stream = File.OpenRead(path))
                {
                    byte[] hash = md5.ComputeHash(stream);
                    string str = BitConverter.ToString(hash).Replace("-", "");
                    if (isUpper)
                    {
                        return str.ToUpperInvariant();
                    }
                    else
                    {
                        return str.ToLowerInvariant();
                    }
                }
            }
        }

        /// <summary>
        /// 替换资源引用
        /// </summary>
        /// <param name="assetPath">资源路径,从Assets/开始</param>
        /// <param name="oldRef">旧资源对象</param>
        /// <param name="newRef">新资源对象</param>
        /// <returns></returns>
        public static bool ReplaceAssetReference(string assetPath, Object oldRef, Object newRef)
        {
            var root = Directory.GetParent(Application.dataPath);
            string filePath = Path.Combine(root.FullName, assetPath);

            if (!File.Exists(filePath) || oldRef == null || newRef == null)
            {
                return false;
            }
            
            string old_guid;
            long old_localId;
            string old_reference = String.Empty;
            if (AssetDatabase.TryGetGUIDAndLocalFileIdentifier(oldRef, out old_guid, out old_localId))
            {
                old_reference = $"{{fileID: {old_localId}, guid: {old_guid}, type: 3}}";
            }
            
            string new_guid;
            long new_localId;
            string new_reference = String.Empty;
            if (AssetDatabase.TryGetGUIDAndLocalFileIdentifier(newRef, out new_guid, out new_localId))
            {
                new_reference = $"{{fileID: {new_localId}, guid: {new_guid}, type: 3}}";
            }
            
            if (string.IsNullOrEmpty(old_reference) || string.IsNullOrEmpty(new_reference))
            {
                return false;
            }

            string fileContent = File.ReadAllText(filePath);
            File.WriteAllText(filePath, fileContent.Replace(old_reference, new_reference));
            return true;
        }

        /// <summary>
        /// 清理粒子特效无效引用
        /// </summary>
        /// <param name="particleSystem">粒子特效组件</param>
        /// <param name="onlyCheck">只检查</param>
        /// <returns>是否包含无效引用</returns>
        public static bool CleanParticleSystem(GameObject root, ParticleSystem particleSystem, out List<Object> invalidReferences, bool onlyCheck = true)
        {
            invalidReferences = new List<Object>();
            
            if (particleSystem == null)
            {
                return false;
            }

            bool isDirty = false;
            
            ParticleSystem.ShapeModule shapeModule = particleSystem.shape;
            Mesh mesh = null;
            MeshRenderer meshRenderer = null;
            SkinnedMeshRenderer skinnedMeshRenderer = null;
            Sprite sprite = null;
            SpriteRenderer spriteRenderer = null;
            switch (shapeModule.shapeType)  
            {
                case ParticleSystemShapeType.Mesh:
                    mesh = shapeModule.mesh;
                    break;
                case ParticleSystemShapeType.MeshRenderer:
                    meshRenderer = shapeModule.meshRenderer;
                    break;
                case ParticleSystemShapeType.SkinnedMeshRenderer:
                    skinnedMeshRenderer = shapeModule.skinnedMeshRenderer;
                    break;
                case ParticleSystemShapeType.Sprite:
                    sprite = shapeModule.sprite;
                    break;
                case ParticleSystemShapeType.SpriteRenderer:
                    spriteRenderer = shapeModule.spriteRenderer;
                    break;
            }

            SetDirtyValue(shapeModule.mesh, mesh, invalidReferences, ref isDirty);
            SetDirtyValue(shapeModule.meshRenderer, meshRenderer, invalidReferences, ref isDirty);
            SetDirtyValue(shapeModule.skinnedMeshRenderer, skinnedMeshRenderer, invalidReferences, ref isDirty);
            SetDirtyValue(shapeModule.sprite, sprite, invalidReferences, ref isDirty);
            SetDirtyValue(shapeModule.spriteRenderer, spriteRenderer, invalidReferences, ref isDirty);
            if (!onlyCheck)
            {
                SerializedObject serializedObject = new SerializedObject(particleSystem, root);
                serializedObject.FindProperty("ShapeModule.m_Mesh").objectReferenceValue = mesh;
                serializedObject.FindProperty("ShapeModule.m_MeshRenderer").objectReferenceValue = meshRenderer;
                serializedObject.FindProperty("ShapeModule.m_SkinnedMeshRenderer").objectReferenceValue =
                    skinnedMeshRenderer;
                serializedObject.FindProperty("ShapeModule.m_Sprite").objectReferenceValue = sprite;
                serializedObject.FindProperty("ShapeModule.m_SpriteRenderer").objectReferenceValue = spriteRenderer;
                serializedObject.ApplyModifiedProperties();
            }

            ParticleSystemRenderer renderer = particleSystem.GetComponent<ParticleSystemRenderer>();
            Material[] materials = null;
            mesh = null;
            switch (renderer.renderMode)
            {
                case ParticleSystemRenderMode.Billboard:
                case ParticleSystemRenderMode.Stretch:
                case ParticleSystemRenderMode.HorizontalBillboard:
                case ParticleSystemRenderMode.VerticalBillboard:
                    materials = renderer.sharedMaterials;
                    break;
                case ParticleSystemRenderMode.Mesh:
                    mesh = renderer.mesh;
                    materials = renderer.sharedMaterials;
                    break;
            }

            Material oldMat = null;
            Material newMat = null;
            if (renderer.sharedMaterials != null && renderer.sharedMaterials.Length > 0)
            {
                oldMat = renderer.sharedMaterials[0];
            }
            if (materials != null && materials.Length > 0)
            {
                newMat = materials[0];
            }

            SetDirtyValue(renderer.mesh, mesh, invalidReferences, ref isDirty);
            SetDirtyValue(oldMat, newMat, invalidReferences, ref isDirty);
            if (!onlyCheck)
            {
                SerializedObject serializedObject = new SerializedObject(renderer, root);
                if (AssetDatabase.GetAssetPath(mesh) != UNITY_DEFAULT_RESOURCES)
                {
                    serializedObject.FindProperty("m_Mesh").objectReferenceValue = mesh;
                }

                if (materials == null || AssetDatabase.GetAssetPath(materials[0]) != UNITY_DEFAULT_RESOURCES)
                {
                    SerializedProperty property = serializedObject.FindProperty("m_Materials");
                    if (materials == null || materials.Length == 0)
                    {
                        property.ClearArray();
                    }
                    else
                    {
                        property.GetArrayElementAtIndex(0).objectReferenceValue = materials[0];
                    }
                }
                serializedObject.ApplyModifiedProperties();
            }

            return isDirty;
        }

        private static void SetDirtyValue(Object oldValue, Object newValue, List<Object> list, ref bool isDirty)
        {
            if (oldValue != newValue && AssetDatabase.GetAssetPath(oldValue) != UNITY_DEFAULT_RESOURCES)
            {
                if (oldValue != null)
                {
                    list.Add(oldValue);
                }

                if (!isDirty)
                {
                    isDirty = true;
                }
            }
        }
    }
}
