using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Net;
using System.Text;
using UnityEditor;
using UnityEngine;
#if WX_WECHAT
using WeChatWASM;
using static WeChatWASM.PluginUpdateManager;
#endif

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        private static bool WxPluginVersionCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string versionInfo = $"{GetCurVersion()},{GetNewVersion()}";
            File.WriteAllText($"{outputDir}/WxPluginVersionCheck.csv", versionInfo, Encoding.UTF8);

            return true;
        }


        public static string GetNewVersion()
        {
            string str = string.Empty;
#if WX_WECHAT
            HttpWebRequest httpWebRequest = (HttpWebRequest)WebRequest.Create(PluginUpdateManager.checkUrl);
            httpWebRequest.Timeout = 20000;
            HttpWebResponse httpWebResponse = (HttpWebResponse)httpWebRequest.GetResponse();
            StreamReader streamReader = new StreamReader(httpWebResponse.GetResponseStream());
            string text = streamReader.ReadToEnd();
            httpWebResponse.Close();
            streamReader.Close();
            VersionRes versionRes = JsonUtility.FromJson<VersionRes>(text);
            if (versionRes.errcode == 0)
            {
                str = versionRes.data.info.version;
            }
#endif
            return str;
        }

        public static string GetCurVersion()
        {
#if WX_WECHAT
            return WXExtEnvDef.pluginVersion;
#endif
            return null;
        }


    }
}