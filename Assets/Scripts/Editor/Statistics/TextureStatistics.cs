using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/纹理贴图统计", false, 2)]
        public static void TextureStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(TextureStatisticsCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool TextureStatisticsCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder();
#if UNITY_WEBGL
            statistics.AppendLine("资源路径,图片名称,分辨率大小,内存大小(KB),图片格式(WebGL),图片类型,大小限制(WebGL),开启Mipmap");
#else
            statistics.AppendLine("资源路径,图片名称,分辨率大小,图片格式(Android/IOS),图片类型,大小限制(Android/IOS),开启Mipmap");
#endif
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                string directory = Path.GetDirectoryName(path).Replace("\\", "/");
                string name = Path.GetFileNameWithoutExtension(path);
                TextureImporter importer = TextureImporter.GetAtPath(path) as TextureImporter;
                if (importer == null)
                {
                    continue;
                }
#if UNITY_WEBGL
                TextureImporterPlatformSettings webGLPlatformSettings = importer.GetPlatformTextureSettings("WebGL");
                TextureImporterFormat webGLTextureFormat = importer.GetAutomaticFormat("WebGL");
                if (webGLPlatformSettings.format != TextureImporterFormat.Automatic)
                {
                    webGLTextureFormat = webGLPlatformSettings.format;
                }
#else
                TextureImporterPlatformSettings androidPlatformSettings =
                    importer.GetPlatformTextureSettings("Android");
                TextureImporterPlatformSettings iosPlatformSettings = importer.GetPlatformTextureSettings("IOS");
                TextureImporterFormat androidTextureFormat = importer.GetAutomaticFormat("Android");
                TextureImporterFormat iosTextureFormat = importer.GetAutomaticFormat("IOS");
                if (androidPlatformSettings.format != TextureImporterFormat.Automatic)
                {
                    androidTextureFormat = androidPlatformSettings.format;
                }

                if (iosPlatformSettings.format != TextureImporterFormat.Automatic)
                {
                    iosTextureFormat = iosPlatformSettings.format;
                }
#endif

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;
                ;

                Texture2D asset = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(asset);
                statistics.AppendLine(
                    $"{directory}," +
                    $"{name}," +
                    $"{asset.width}x{asset.height}," +
#if UNITY_WEBGL
                    $"{AssetStatisticsUtility.ByteToKB(memorySize, true)}," +
                    $"{webGLTextureFormat}," +
#else
                    $"{androidTextureFormat}/{iosTextureFormat}," +
#endif
                    $"{importer.textureType}," +
#if UNITY_WEBGL
                    $"{webGLPlatformSettings.maxTextureSize}," +
#else
                    $"{androidPlatformSettings.maxTextureSize}/{iosPlatformSettings.maxTextureSize}," +
#endif
                    $"{importer.mipmapEnabled}");
            }

            File.WriteAllText($"{outputDir}/TextureStatistics.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
    }
}