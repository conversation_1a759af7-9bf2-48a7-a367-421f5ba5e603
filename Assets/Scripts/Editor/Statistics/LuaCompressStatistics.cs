#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/Lua/Lua文件压缩", false, 1)]
        public static void LuaCompressStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(LuaCompressCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        private static bool LuaCompressCommand(string[] args)
        {
            if (args.Length < 1)
            {
                throw new Exception("Invalid Arguments!");
            }

            string outputDir = args[0];

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            var LuaPath = "Assets/Lua";
            Dictionary<string, byte[]> DictLuaBs = null;
            // 如果数据为空，先获取所有 lua 文件
            if (DictLuaBs == null)
            {
                string[] luaFiles = Directory.GetFiles(LuaPath + "/", "*.txt", SearchOption.AllDirectories);
                DictLuaBs = new Dictionary<string, byte[]>();
                foreach (var luaFile in luaFiles)
                {
                    var fstr = File.ReadAllText(luaFile, Encoding.UTF8);
                    var bytes = Encoding.UTF8.GetBytes(fstr);
                    DictLuaBs.Add(luaFile, bytes);
                }
            }

             var logStrList = BuildSimplifyLua.ExecuteLuaSrcDietAll(DictLuaBs,true);
            StringBuilder statistics = new StringBuilder();
            statistics.AppendLine("文件路径,错误码");
            bool success = true;
            if (logStrList.Count > 0) 
            {
                success = false;
            }
            foreach (string log in logStrList)
            {
                statistics.AppendLine(log);
            }

            File.WriteAllText($"{outputDir}/LuaCompressErrorCheck.csv", statistics.ToString(), Encoding.UTF8);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the LuaCompressErrorCheck.csv, Please open modify!");
            }

            return success;
        }
    }
}