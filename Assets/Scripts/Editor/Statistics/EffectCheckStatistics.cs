using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEngine.Timeline;
using Object = UnityEngine.Object;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/特效图片冗余检查", false, 1)]
        public static void EffectTextureCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(EffectTextureCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool EffectTextureCheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,引用路径,冗余数量,图片尺寸,图片内存(KB),冗余内存(KB)\n");
            Dictionary<string, List<string>> filtered;
            Dictionary<string, List<string>> references;
            CheckEffectTextureRedundancy(guids, out filtered, out references);

            foreach (var texture in filtered.OrderByDescending(item => item.Value.Count))
            {
                string assetPath = texture.Value[0];
                string prefabPath = references[texture.Key][0];
                Texture rawTexture = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                string textureSize = $"{rawTexture.width}*{rawTexture.height}";
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(rawTexture);
                decimal totalMemorySize = memorySize * (texture.Value.Count - 1);

                statistics.AppendLine(
                    $"{assetPath},{prefabPath},{texture.Value.Count - 1},{textureSize},{AssetStatisticsUtility.ByteToKB(memorySize, true)},{AssetStatisticsUtility.ByteToKB(totalMemorySize, true)}");
            }

            File.WriteAllText($"{outputDir}/EffectTextureCheck.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return true;
        }
        
        [MenuItem("Assets/MiniGameTool/特效图片冗余修复", false, 1)]
        public static void EffectTextureFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(EffectTextureFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool EffectTextureFixCommand(string[] args)
        {
            const string EFFECT_COMMON_TEXTURE = "Assets/Art/Effects/Common/Textures";
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }
            
            Dictionary<string, List<string>> filtered;
            Dictionary<string, List<string>> references;
            CheckEffectTextureRedundancy(guids, out filtered, out references);
            StringBuilder statistics = new StringBuilder("特效路径,原始图片引用,替换图片引用,图片尺寸\n");
            foreach (var texture in filtered.OrderByDescending(item => item.Value.Count))
            {
                foreach (var reference in references[texture.Key])
                {
                    var effectDir = Path.GetDirectoryName(Path.GetDirectoryName(reference)).Replace("\\", "/");
                    string assetPath = texture.Value.FirstOrDefault(path => path.Contains(effectDir));
                    if (string.IsNullOrEmpty(assetPath) || assetPath.StartsWith(EFFECT_COMMON_TEXTURE))
                    {
                        continue;
                    }
                    
                    string commonPath = Path.Combine(EFFECT_COMMON_TEXTURE, Path.GetFileName(assetPath));
                    AssetImporter commonImporter = AssetImporter.GetAtPath(commonPath);
                    if (commonImporter == null)
                    {
                        AssetDatabase.CopyAsset(assetPath, commonPath);
                        commonImporter = AssetImporter.GetAtPath(commonPath);
                        commonImporter.assetBundleName =
                            AssetStatisticsUtility.GetABName(commonPath, Path.GetExtension(commonPath));
                    }

                    Texture effectTexture = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                    Texture commonTexture = AssetDatabase.LoadAssetAtPath<Texture>(commonPath);
                    foreach (var prefabGuid in AssetDatabase.FindAssets("t:Prefab", new string[]{ effectDir }))
                    {
                        string prefabPath = AssetDatabase.GUIDToAssetPath(prefabGuid);
                        if (!prefabPath.ToLower().EndsWith("fbx"))
                        {
                            AssetStatisticsUtility.ReplaceAssetReference(prefabPath, effectTexture, commonTexture);
                        }
                    }
                    foreach (var matGuid in AssetDatabase.FindAssets("t:Material", new string[]{ effectDir }))
                    {
                        string matPath = AssetDatabase.GUIDToAssetPath(matGuid);
                        if (!matPath.ToLower().EndsWith("fbx"))
                        {
                            AssetStatisticsUtility.ReplaceAssetReference(matPath, effectTexture, commonTexture);
                        }
                    }

                    statistics.AppendLine(
                        $"{effectDir},{assetPath},{commonPath},{commonTexture.width}*{commonTexture.height}");
                }
            }
            
            File.WriteAllText($"{outputDir}/EffectTextureFix.csv", statistics.ToString(), Encoding.UTF8);
            AssetDatabase.Refresh();
            Resources.UnloadUnusedAssets();
            return true;
        }

        private static void CheckEffectTextureRedundancy(string[] guids, out Dictionary<string, List<string>> filtered,
            out Dictionary<string, List<string>> references)
        {
            //过滤冗余低于1M的图片
            const decimal FILTER_THRESHOLD = 1024 * 1024;

            Dictionary<string, List<string>> textures = new Dictionary<string, List<string>>();
            Dictionary<string, string> md5s = new Dictionary<string, string>();
            Dictionary<string, long> memorys = new Dictionary<string, long>();
            filtered = new Dictionary<string, List<string>>();
            references = new Dictionary<string, List<string>>();
            var projectRoot = Directory.GetParent(Application.dataPath);
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                Object[] collectDependencies = EditorUtility.CollectDependencies(new Object[] { prefab });
                List<string> textureDependencies = new List<string>();
                foreach (Object dependObject in collectDependencies)
                {
                    if (dependObject is Texture)
                    {
                        string dependPath = AssetDatabase.GetAssetPath(dependObject);
                        AssetImporter dependImporter = AssetImporter.GetAtPath(dependPath);
                        if (dependImporter != null && !textureDependencies.Contains(dependPath))
                        {
                            textureDependencies.Add(dependPath);
                        }
                    }
                }

                foreach (string dependency in textureDependencies)
                {
                    TextureImporter textureImporter = AssetImporter.GetAtPath(dependency) as TextureImporter;
                    Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(dependency);
                    if (textureImporter != null && texture != null)
                    {
                        string filePath = Path.Combine(projectRoot.FullName, dependency);
                        string md5;
                        List<string> files;
                        List<string> prefabs;
                        if (!md5s.TryGetValue(filePath, out md5))
                        {
                            md5 = AssetStatisticsUtility.GetFileMD5(filePath);
                            md5 = $"{Path.GetFileName(filePath)}_{md5}";
                            md5s.Add(filePath, md5);
                        }

                        if (!textures.TryGetValue(md5, out files))
                        {
                            files = new List<string>();
                            textures.Add(md5, files);

                            long memory = EditorTextureUtil.GetStorageMemorySize(texture);
                            memorys.Add(md5, memory);
                        }

                        if (!references.TryGetValue(md5, out prefabs))
                        {
                            prefabs = new List<string>();
                            references.Add(md5, prefabs);
                        }

                        if (!files.Contains(dependency))
                        {
                            files.Add(dependency);
                        }

                        if (!prefabs.Contains(path))
                        {
                            prefabs.Add(path);
                        }
                    }
                }
            }

            foreach (var texture in textures)
            {
                if (texture.Value.Count > 1)
                {
                    decimal memorySize = memorys[texture.Key];
                    decimal totalMemorySize = memorySize * (texture.Value.Count - 1);

                    if (totalMemorySize < FILTER_THRESHOLD)
                    {
                        continue;
                    }

                    filtered.Add(texture.Key, texture.Value);
                }
            }
        }

        [MenuItem("Assets/MiniGameTool/特效动画冗余检查", false, 1)]
        public static void EffectAnimationCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(EffectAnimationCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool EffectAnimationCheckCommand(string[] args)
        {
            const string FILTER_TYPE = ".fbx";
            const string CHARATER_DIR = "assets/art/characters";

            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,引用FBX路径\n");
            Dictionary<string, List<string>> textures = new Dictionary<string, List<string>>();
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                string[] dependencies = AssetDatabase.GetDependencies(path);
                foreach (string dependency in dependencies)
                {
                    string lower = dependency.ToLower();
                    if (lower.StartsWith(CHARATER_DIR) && lower.EndsWith(FILTER_TYPE))
                    {
                        statistics.AppendLine($"{path},{dependency}");
                    }
                }
            }

            File.WriteAllText($"{outputDir}/EffectAnimationCheck.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
        
        [MenuItem("Assets/MiniGameTool/特效动画冗余修复", false, 1)]
        public static void EffectAnimationFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(EffectAnimationFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        
        private static bool EffectAnimationFixCommand(string[] args)
        {
            const string FILTER_TYPE = ".fbx";
            const string CHARATER_DIR = "assets/art/characters";
            
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,原始引用动画路径,修复后引用动画路径\n");
            Dictionary<string, List<string>> textures = new Dictionary<string, List<string>>();
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                Object[] dependencies = EditorUtility.CollectDependencies(new Object[] { prefab });
                foreach (Object dependency in dependencies)
                {
                    AnimationPlayableAsset asset = dependency as AnimationPlayableAsset;
                    if (asset != null && asset.clip != null)
                    {
                        string referencePath = AssetDatabase.GetAssetPath(asset.clip);
                        string lower = referencePath.ToLower();
                        if (lower.StartsWith(CHARATER_DIR) && lower.EndsWith(FILTER_TYPE))
                        {
                            string animPath = GetAnimationFBXToAnimPath(referencePath);
                            AnimationClip animClip = AssetDatabase.LoadAssetAtPath<AnimationClip>(animPath);
                            if (animClip != null)
                            {
                                asset.clip = animClip;
                                statistics.AppendLine($"{path},{referencePath},{animPath}");
                            }
                        }
                        break;
                    }
                }
            }
            
            AssetDatabase.SaveAssets();
            File.WriteAllText($"{outputDir}/EffectAnimationFix.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
        
        private static string GetAnimationFBXToAnimPath(string path)
        {
            var fileName = Path.GetFileNameWithoutExtension(path).Split('@')[1];
            return Path.Combine(Path.GetDirectoryName(path), $"{fileName}.anim");
        }
        
        [MenuItem("Assets/MiniGameTool/特效无效引用检查", false, 1)]
        public static void EffectInvalidReferenceCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");
            
            try
            {
                ExecuteCommand(nameof(EffectInvalidReferenceCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        
        private static bool EffectInvalidReferenceCheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,无效引用节点,无效引用路径\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
                List<Object> invalidReferences;
                foreach (var particleSystem in particleSystems)
                {
                    if (AssetStatisticsUtility.CleanParticleSystem(prefab, particleSystem, out invalidReferences, true))
                    {
                        string invalidReferencePath = AssetDatabase.GetAssetPath(invalidReferences[0]);
                        statistics.AppendLine($"{path},{particleSystem.name},{invalidReferencePath}");
                    }
                }
            }

            Resources.UnloadUnusedAssets();
            File.WriteAllText($"{outputDir}/EffectInvalidReferenceCheck.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
        
        [MenuItem("Assets/MiniGameTool/特效无效引用修复", false, 1)]
        public static void EffectInvalidReferenceFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");
            
            try
            {
                ExecuteCommand(nameof(EffectInvalidReferenceFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        
        private static bool EffectInvalidReferenceFixCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Effects");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,无效引用节点,无效引用路径\n");
            AssetDatabase.StartAssetEditing();
            try
            {
                foreach (string guid in guids)
                {
                    string path = AssetDatabase.GUIDToAssetPath(guid);
                    AssetImporter importer = AssetImporter.GetAtPath(path);

                    if (string.IsNullOrEmpty(importer.assetBundleName))
                        continue;
                    
                    GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    ParticleSystem[] particleSystems = prefab.GetComponentsInChildren<ParticleSystem>();
                    List<Object> invalidReferences;
                    foreach (var particleSystem in particleSystems)
                    {
                        if (AssetStatisticsUtility.CleanParticleSystem(prefab, particleSystem, out invalidReferences, false))
                        {
                            string invalidReferencePath = AssetDatabase.GetAssetPath(invalidReferences[0]);
                            statistics.AppendLine($"{path},{particleSystem.name},{invalidReferencePath}");
                        }
                    }
                }
                
                AssetDatabase.SaveAssets();
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }
            Resources.UnloadUnusedAssets();
            File.WriteAllText($"{outputDir}/EffectInvalidReferenceFix.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
    }
}