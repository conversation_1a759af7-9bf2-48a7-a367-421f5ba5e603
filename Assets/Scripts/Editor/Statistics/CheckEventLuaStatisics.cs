using Checker;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        public static bool CheckLuaEvenCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", $"{Application.dataPath}/Lua/");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            EventCheckAdaper eventCheckAdaper = new EventCheckAdaper("");
            eventCheckAdaper.PublicCheck_MenuItem(inputDir, $"{outputDir}/CheckLuaEventReport.txt");

            return true;
        }

        /// <summary>
        /// Jenkins ���ð�ť�ڴ�й©����
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static bool CheckLuaButtonAddIntervalListenerCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", $"{Application.dataPath}/Lua/");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            ButttonClickCheckAdaper eventCheckAdaper = new ButttonClickCheckAdaper("");

            eventCheckAdaper.PublicCheck_MenuItem(inputDir, 
                $"{outputDir}/CheckLuaButtonAddIntervalListenerReport.txt",
                $"{outputDir}/CheckLuaSelfRegisterEventReport.txt");

            return true;
        }



    }
}
