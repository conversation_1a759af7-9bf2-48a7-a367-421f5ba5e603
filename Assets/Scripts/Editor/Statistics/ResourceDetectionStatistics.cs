#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine.UI;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Tools/Resources/资源检测", false, 1)]
        public static void ResourcesDetectionStatistics()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(ResourcesDetectionCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        static bool success = true;
        static StringBuilder statistics = new StringBuilder();
        //
        static Dictionary<string, List<string>> checkInGame;// 游戏
        static Dictionary<string, List<string>> checkInMiniGame;//小游戏

        static string ignoreMiniGameCachePath = Path.Combine(Application.dataPath, "../ignoreMiniGameCache.json");
        static Dictionary<string, List<string>> checkInMiniGameCache;//小游戏缓存
        private static void ReadIgnoreCacheAsset()
        {
            checkInMiniGameCache = new Dictionary<string, List<string>>();
            if (File.Exists(ignoreMiniGameCachePath))
            {
                string content = File.ReadAllText(ignoreMiniGameCachePath);
                checkInMiniGameCache = UIHelper.ToObj<Dictionary<string, List<string>>>(content);
            }
            else
            {
                for (int i = 0; i < checkStr_miniGame.Length; i++)
                {
                    checkInMiniGameCache.Add(checkStr_miniGame[i], new List<string>());
                }
                for (int i = 0; i < checkStr_mesh.Length; i++)
                {
                    checkInMiniGameCache.Add(checkStr_mesh[i], new List<string>());
                }

                var content = UIHelper.ToJson(checkInMiniGameCache);
                File.WriteAllText(ignoreMiniGameCachePath, content, Encoding.UTF8);
            }
        }

        private static bool ResourcesDetectionCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }
            ReadIgnoreCacheAsset();
            success = DoCheckRes(inputDir, outputDir,0);

            return success;
        }

        private static bool DoCheckRes(string inputDir ,string outputDir,int type = 0) 
        {
            success = true;
            checkInGame = new Dictionary<string, List<string>>();
            checkInMiniGame = new Dictionary<string, List<string>>();
            switch (type)
            {
                case 0:
                    //1:纹理资源检测
                    CheckTextureResource(inputDir);

                    //2:网格资源的检测
                    CheckMeshResource(inputDir);

                    //3:字体资源的检测
                    CheckFontResource();
                    break;
                case 3:
                    CheckFontResource();
                break;
                case 4:
                    CheckRepeatResource();
                    break;
                default:
                    break;
            }
            if (type == 0) 
            {
            
            }
 

            //总结放在最前面
            statistics.AppendLine("游戏内资源规范检测");
            foreach (var item in checkInGame)
            {
                statistics.AppendLine($"{item.Key}: <{item.Value.Count}>个");
                int index = 1;
                foreach (var item1 in item.Value)
                {
                    if (index < 3)
                    {
                        var newPathSplit = item1.Split(',');
                        if (newPathSplit.Length > 0)
                        {
                            var value = newPathSplit[0].Replace("Assets/", "");
                            value = value.Replace("CasualGame/", "");
                            statistics.AppendLine($"  {value}");
                            index++;
                        }
                    }
                }
            }
            statistics.AppendLine("小游戏内资源规范检测");
            foreach (var item in checkInMiniGame)
            {
                statistics.AppendLine($"{item.Key}: <{item.Value.Count}>个");
                int index = 1;
                foreach (var item1 in item.Value)
                {
                    if (index < 3)
                    {
                        var newPathSplit = item1.Split(',');
                        if (newPathSplit.Length > 0)
                        {
                            var value = newPathSplit[0].Replace("Assets/", "");
                            value = value.Replace("CasualGame/", "");
                            statistics.AppendLine($"  {value}");
                            index++;
                        }
                    }
                }
            }
            statistics.AppendLine("\r\n \r\n");
            statistics.AppendLine("具体资源列表如下");
            foreach (var item in checkInGame)
            {
                statistics.AppendLine(item.Key);
                foreach (var item1 in item.Value)
                {
                    statistics.AppendLine(item1);
                }
            }
            statistics.AppendLine("项目内小游戏资源检测情况");
            foreach (var item in checkInMiniGame)
            {
                statistics.AppendLine(item.Key);
                foreach (var item1 in item.Value)
                {
                    statistics.AppendLine(item1);
                }
            }
            File.WriteAllText($"{outputDir}/ReferencesCheck.csv", statistics.ToString(), Encoding.UTF8);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the ReferencesCheck.csv, Please open modify!");
            }

            return success;
        }



        #region 纹理资源的检测

        //mipmap和readwrite受益不大时，不进行处理，防止出错
        static int minCheckSize = 5 * 1024;  //检测大小不低于这个数
        static int minCheckSize_minigame = 100 * 1024;  //检测大小不低于这个数(小游戏放宽处理!!!)
        static int minCheckSize_minigame_sizeX4 = 200 * 1024;  //检测大小不低于这个数(小游戏放宽处理!!!)

        //冗余量大于200KB并且引用个数大于1,单个资源大于 5KB
        static int minRedundancySize = 200 * 1024;
        static int minRedundancyCount = 1;
        static int minSingleRedundancySize = 5 * 1024;

        //冗余量大于500KB并且引用个数大于1,单个资源大于 100KB
        static int minRedundancySize_miniGame = 500 * 1024;
        static int minRedundancyCount_miniGame = 1;
        static int minSingleRedundancySize_miniGame = 100 * 1024;


        static int maxSpriteAtlasTextureSize = 1024;  //图集大小不能超过这个
        static int FILTER_THRESHOLD = 512;          //非图集的资源大小不超过这个数
        static int FILTER_THRESHOLD_miniGame = 1024;          //非图集的资源大小不超过这个数


        static Dictionary<string, List<string>> noPackBundleRes;
        static Dictionary<string, List<string>> referencesDic_Texture;
        static Dictionary<string, List<string>> referencesDic_Mat;
        static Dictionary<string, List<string>> referencesDic_Asset;
        static Dictionary<string, List<string>> referencesDic_Scene;
        static Dictionary<string, List<string>> referencesDic_Prefab;
        static Dictionary<string, List<string>> referencesDic_Font;
        static Dictionary<string, List<string>> referencesDic_EffectTexture;



        static string[] checkStr = new string[] { "检查是否需要开启mipmap(超5KB)" , "检查是否需要开启读写权限(超5KB)" , "图集尺寸超过1024*1024" ,
                                            "大小超过300KB且尺寸超过512*512","大小超过300KB","单独打成bundle的大图资源不是4的倍数",
                                            "超过200KB的冗余资源:单个资源大于5KB","abname超过128个字符", "纹理资源压缩格式为RGBA32",
                                             "英雄贴图512 法线贴图32(maxSize)"};
        static string[] checkStr_miniGame = new string[] { "检查是否需要开启mipmap(超200KB)" , "检查是否需要开启读写权限(超100KB)" , "图集尺寸超过1024*1024" ,
                                            "大小超过1024KB且尺寸超过1024*1024","大小超过1024KB","单独打成bundle的大图资源不是4的倍数(超200KB)",
                                            "超过500KB的冗余资源:单个资源大于100KB","abname超过128个字符", "纹理资源压缩格式为RGBA32"};
        static string[] checkOutPath_read_write = new string[]
        {
            "UI/uiguaguale/ggl_card_mian_01.png","UI/uiguaguale/ggl_card_mian_02.png"
            ,"UI/uiguaguale/ggl_card_mian_03.png","UI/uiguaguale/scrachmask.png",
            "Art/Effects/Textures/f_xulie_mgx_056.png",
          
        };
        static string[] checkOutPath_mipMap  = new string[]
        {
           
        };
        static string[] checkOutPath_x4 = new string[] { "UI/MiniGameIcon"};
        static string[] checkOutPath_weight = new string[] { "yinghuolh/Ex/yinghuolh_0.png", "jitan_daBG.png","Maps/shijieditu/NewBg/America.jpg" ,"Art/Characters/yongzheshilian/Ex/yongzheshilian_0.png"};
        static string[] checkOutPath_Hero = new string[] { "Texture/aerwaleisi04_N.tga" };
        static string[] checkOutPath_redundancy = new string[] { "Art/Effects/", "Art/ActorBackground/shuling.png" ,"Assets/Art/ActorBackground/anying.png"};

        static string[] checkOutPath_miniGame_ReadWrite = new string[] { "LxGetonthesubway/Art/UI/Tips/" };
        static string[] checkOutPath_miniGame_MipMap = new string[] { "Textures/1/BG04.tga", "Textures/1/BG05.tga", "Textures/2/BG01.tga",
                                                                        "Textures/2/BG02.TGA","Textures/2/BG03.tga","Confetti - Original.png" };

        public static void CheckTextureResource(string inputDir) 
        {
            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            noPackBundleRes = new Dictionary<string, List<string>>();
            FindReferencesData();
            //获取到所有Texuure类型的资源
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;
                bool isminiGame = path.Contains("CasualGame");
                if (importer == null)
                    continue;
                //如果发现该资源未被其他资源引用并且未设置bundle则不进行检测
                if (string.IsNullOrEmpty(importer.assetBundleName) && !referencesDic_Texture.ContainsKey(path))
                {
                    continue;
                }
                Texture texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                if (texture == null)
                {
                    texture = AssetDatabase.LoadMainAssetAtPath(path) as Texture;
                }
                if (importer.assetBundleName.Length > 128)
                {
                    AddInCheckDict(isminiGame, checkStr[7], path);
                }
                //先检测图集(空白像素占比,不超过1024)
                bool isSpriteAtlas = !string.IsNullOrEmpty(importer.assetBundleName) && !string.IsNullOrEmpty(importer.spritePackingTag) && importer.assetBundleName.EndsWith(".spritepack");
                //mipmap
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(texture);
                //MipMap权限小游戏和项目内判断标准不一样
                if (importer.mipmapEnabled)
                {
                    if (isminiGame)
                    {
                        if (memorySize > minCheckSize_minigame_sizeX4 && !inStringArray(path, checkOutPath_miniGame_MipMap))
                        {
                            AddInCheckDict(isminiGame, checkStr_miniGame[0], $"{path},Size:{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB");
                        }
                    }
                    else
                    {
                        if (memorySize > minCheckSize && !inStringArray(path,checkOutPath_mipMap))
                        {
                            AddInCheckDict(isminiGame, checkStr[0], $"{path},Size:{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB");
                        }

                    }
                }
                //读写权限小游戏和项目内判断标准不一样
                if (importer.isReadable)
                {
                    if (isminiGame)
                    {
                        if (memorySize > minCheckSize_minigame && !inStringArray(path, checkOutPath_miniGame_ReadWrite))
                        {
                            AddInCheckDict(isminiGame, checkStr_miniGame[1], path);
                        }
                    }
                    else
                    {
                        if (memorySize > minCheckSize&& !inStringArray(path,checkOutPath_read_write))
                        {
                            AddInCheckDict(isminiGame, checkStr[1], path);
                        }

                    }
                }

                if (isSpriteAtlas)
                {
                    //图集大小不超过1024
                    if (texture.width * texture.height > maxSpriteAtlasTextureSize * maxSpriteAtlasTextureSize)
                    {
                        AddInCheckDict(isminiGame, checkStr[2], path);
                    }
                    /*  
                    bool defaultReadable = importer.isReadable;
                     importer.isReadable = true;
                     importer.SaveAndReimport();
                     if (CalculatePixelsRatio(texture as Texture2D) > 0.25f) 
                     {
                         Debug.LogError($"图集中空白像素的占比高达1/4:{path}");
                     }
                     importer.isReadable = defaultReadable;
                     importer.SaveAndReimport();*/
                }
                else
                {
                    //非图集相关的资源未进行压缩(RGBA32)
                    TextureImporterPlatformSettings webGLPlatformSettings = importer.GetPlatformTextureSettings("WebGL");
                    TextureImporterFormat webGLTextureFormat = importer.GetAutomaticFormat("WebGL");
                    if (webGLPlatformSettings.format == TextureImporterFormat.RGBA32)
                    {
                        if (!CalculateTextureIsBundle(path)) { continue; }
                        AddInCheckDict(isminiGame, checkStr[8], $"{path},{webGLPlatformSettings.format}");
                    }
                    //英雄相关的贴图
                    if (path.Contains("Art/Characters") && importer.assetBundleName.EndsWith(".assets"))
                    {
                        if (!inStringArray(path, checkOutPath_Hero)) {
                            if (path.ToLower().EndsWith("_c.tga") && texture.width * texture.height > 512*512 && webGLPlatformSettings.maxTextureSize > 512)
                            {
                                AddInCheckDict(false, checkStr[9], $"{path},maxTextureSize:{webGLPlatformSettings.maxTextureSize}");
                            }
                            else if (path.ToLower().EndsWith("_n.tga") && texture.width * texture.height > 32 * 32 && webGLPlatformSettings.maxTextureSize > 32)
                            {
                                AddInCheckDict(false, checkStr[9], $"{path},maxTextureSize:{webGLPlatformSettings.maxTextureSize}");
                            }
                        }
                    }
                    //图片资源大于300KB(非图集资源)
                    //重点检测
                    bool isweight = memorySize > 300 * 1024 && texture.width * texture.height > FILTER_THRESHOLD * FILTER_THRESHOLD;
                    if (isminiGame)
                    {
                        isweight = memorySize > FILTER_THRESHOLD_miniGame * 1024 && texture.width * texture.height > FILTER_THRESHOLD_miniGame * FILTER_THRESHOLD_miniGame;
                    }
                    if (isweight)
                    {
                        if (CalculateTextureIsBundle(path))
                        {
                            if (!inStringArray(path, checkOutPath_weight))
                            {
                                AddInCheckDict(isminiGame, isminiGame ? checkStr_miniGame[3] : checkStr[3], $"{path},Size:{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB,{texture.width}*{texture.height}");
                            }

                        }
                    }
                    else
                    {
                        if (CalculateTextureIsBundle(path)) {
                            if (isminiGame)
                            {
                                if (memorySize > FILTER_THRESHOLD_miniGame * 1024)
                                {
                                    AddInCheckDict(isminiGame, checkStr_miniGame[4], $"{path},{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB");
                                }
                            }
                            else
                            {
                                if (memorySize > 300 * 1024)
                                {
                                    AddInCheckDict(isminiGame, checkStr[4], $"{path},{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB");
                                }
                            }
                            //非图集检测尺寸
                            if (texture.width * texture.height > FILTER_THRESHOLD * FILTER_THRESHOLD)
                            {
                                Debug.LogWarning($"这张图片尺寸大小大于512*512:{path},{texture.width}*{texture.height}:{importer.assetBundleName}");
                            }
                        }
                    }

                    //大图是否不是4的倍数
                    if (!string.IsNullOrEmpty(importer.assetBundleName) && !importer.assetBundleName.EndsWith(".spritepack"))
                    {
                        //不去检测数组中的地址
                        if (!inStringArray(path, checkOutPath_x4)) 
                        {
                            if (texture.width % 4 != 0 || texture.height % 4 != 0)
                            {
                                var wid = (int)(Mathf.RoundToInt(texture.width / 4) * 4);
                                var hei = (int)(Mathf.RoundToInt(texture.height / 4) * 4);
                                if (wid > 0 && hei > 0)
                                {
                                    if (isminiGame) //小游戏大于100KB不是4的倍数检测
                                    {
                                        if (memorySize > minCheckSize_minigame_sizeX4)
                                        {
                                            AddInCheckDict(isminiGame, checkStr_miniGame[5], $"{path},Size:{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB,{texture.width} * {texture.height}");
                                        }
                                    }
                                    else
                                    {
                                        AddInCheckDict(isminiGame, checkStr[5], $"{path},Size:{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB,{texture.width} * {texture.height}");
                                    }

                                }
                            }
                        }
                    }
                    else
                    {
                        if (!CalculateTextureIsBundle(path)) { continue; }
                        //未单独打成bundle的需要查看他的引用计数和他的大小
                        if (string.IsNullOrEmpty(importer.assetBundleName))
                        {
                            noPackBundleRes.Add(path, new List<string>());
                        }
                    }
                }
            }

            FindUnPackBundResData();
            if (checkInGame.Count > 0 || checkInMiniGame.Count > 0)
            {
                success = false;
            }

         }
        public static void FindUnPackBundResData()
        {
            //冗余分析 如果当前的资源没有打成单独ab的计算当前的这张贴图引用计数 
            foreach (var item in noPackBundleRes)
            {
                string path = item.Key;
                if (referencesDic_Texture.ContainsKey(path)) 
                {
                    foreach (string dependencyItem in referencesDic_Texture[path])
                    {
                        //未打成bundle的资源计算其冗余数据
                        noPackBundleRes[path].Add(dependencyItem);
                    }

                }

            }
            foreach (var texture in noPackBundleRes.OrderByDescending(item => item.Value.Count))
            {
                if (texture.Value.Count > 1)
                {
                    string assetPath = texture.Key;
                    Texture asset = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath);
                    if (asset == null)
                    {
                        asset = AssetDatabase.LoadMainAssetAtPath(assetPath) as Texture;
                    }
                    try
                    {
                        decimal memorySize = EditorTextureUtil.GetStorageMemorySize(asset);
                        decimal totalMemorySize = memorySize * texture.Value.Count;
                        StringBuilder includeFiles = new StringBuilder();
                        foreach (string path in texture.Value)
                        {
                            includeFiles.AppendLine(path);
                        }
                        bool isminiGame = assetPath.Contains("CasualGame");
                        if (isminiGame)
                        {
                            //特效相关的冗余不做处理(策略)
                            //冗余量大于500KB并且引用个数大于1,单个资源大于 100KB
                            if (!inStringArray(assetPath, checkOutPath_redundancy))
                            {
                                if (totalMemorySize > minRedundancySize_miniGame && texture.Value.Count > minRedundancyCount_miniGame && memorySize > minSingleRedundancySize_miniGame)
                                {
                                    AddInCheckDict(isminiGame, checkStr_miniGame[6], $"{assetPath},\"{includeFiles}\",{texture.Value.Count}个引用,{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB,{AssetStatisticsUtility.ByteToKB(totalMemorySize, true)}KB");
                                }
                            }
                        }
                        else
                        {
                            //冗余量大于200KB并且引用个数大于1,单个资源大于 5KB
                            if (!inStringArray(assetPath, checkOutPath_redundancy))
                            {
                                if (totalMemorySize > minRedundancySize && texture.Value.Count > minRedundancyCount && memorySize > minSingleRedundancySize)
                                {
                                    AddInCheckDict(isminiGame, checkStr[6], $"{assetPath},\"{includeFiles}\",{texture.Value.Count}个引用,{AssetStatisticsUtility.ByteToKB(memorySize, true)}KB,{AssetStatisticsUtility.ByteToKB(totalMemorySize, true)}KB");
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"Exception execute:{assetPath}\n{e}");
                        break;
                    }
                }
            }
        }

        public static void FindReferencesData() 
        {
            referencesDic_Texture = new Dictionary<string, List<string>>();
            referencesDic_Mat = new Dictionary<string, List<string>>();
            referencesDic_Asset = new Dictionary<string, List<string>>();
            referencesDic_Scene = new Dictionary<string, List<string>>();
            referencesDic_Prefab = new Dictionary<string, List<string>>();
            referencesDic_Font = new Dictionary<string, List<string>>();
            referencesDic_EffectTexture = new Dictionary<string, List<string>>();
            //整理这几种资源上的图片资源的引用计数
            string[] guids = AssetDatabase.FindAssets("t:prefab t:scene t:material t:scriptableObject", new string[] { "Assets" });
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                string[] dependencies = AssetDatabase.GetDependencies(path, false);
                foreach (string dependency in dependencies)
                {
                    //打非UWA包的时候，会把UWA文件夹干掉
                    if (dependency.Contains("UWA_SDK")) { continue; }
                    TextureImporter textureImporter = AssetImporter.GetAtPath(dependency) as TextureImporter;
                    if (textureImporter != null)
                    {
                        if (dependency.Contains("Assets/Art/Effects_Source") || dependency.Contains("Art/Effects/Effects/Effect_")) 
                        {
                            List<string> files;
                            if (!referencesDic_EffectTexture.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                //Debug.LogError("Add:referencesDic_EffectTexture:" + dependency);
                                referencesDic_EffectTexture.Add(dependency, files);
                            }
                            files.Add(path);
                        } 
                        else
                        {
                            List<string> files;
                            if (!referencesDic_Texture.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                referencesDic_Texture.Add(dependency, files);
                            }
                            files.Add(path);
                        }
                    }
                    else 
                    {
                        if (dependency.EndsWith(".mat"))
                        {
                            List<string> files;
                            if (!referencesDic_Mat.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                referencesDic_Mat.Add(dependency, files);
                            }
                            files.Add(path);
                        }
                        else if (dependency.EndsWith(".unity"))
                        {
                            AssetImporter importer1 = AssetImporter.GetAtPath(path);
                            if (!string.IsNullOrEmpty(importer1.assetBundleName) ||
                               dependency.EndsWith("start1.unity") || dependency.EndsWith("update.unity"))
                            {
                                List<string> files;
                                if (!referencesDic_Scene.TryGetValue(dependency, out files))
                                {
                                    files = new List<string>();
                                    referencesDic_Scene.Add(dependency, files);
                                }
                                files.Add(path);
                            }
                        }
                        else if (dependency.EndsWith(".asset"))
                        {
                            List<string> files;
                            if (!referencesDic_Asset.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                referencesDic_Asset.Add(dependency, files);
                            }
                            files.Add(path);
                        }
                        else if (dependency.EndsWith(".prefab"))
                        {
                            List<string> files;
                            if (!referencesDic_Prefab.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                referencesDic_Prefab.Add(dependency, files);
                            }
                            files.Add(path);
                        }
                        else if (dependency.ToLower().EndsWith(".ttf") || dependency.ToLower().EndsWith(".otf"))
                        {
                            List<string> files;
                            if (!referencesDic_Font.TryGetValue(dependency, out files))
                            {
                                files = new List<string>();
                                referencesDic_Font.Add(dependency, files);
                            }
                            files.Add(path);
                        }
                    }
                }
            }
        }

        //当前资源被其他资源所依赖，并且有abname打进了资源中

        public static bool CalculateTextureIsBundle(string texturePath)
        {
            if (referencesDic_Texture.ContainsKey(texturePath)) 
            {
                foreach (var dependency in referencesDic_Texture[texturePath])
                {
                    AssetImporter importer = AssetImporter.GetAtPath(dependency);
                    if (!string.IsNullOrEmpty(importer.assetBundleName))
                    {
                        //Debug.LogError($"Texture有bundle{dependency}");
                        return true;
                    }
                    else
                    {
                        bool value =  CalculateCurAssetIsBundle(dependency);
                        //Debug.LogError($"依赖{dependency}::{value}");
                        if(value) return true;
                    }
                }
            }
            return false;
        }

        public static bool CalculateEffectTextureIsBundle(string texturePath)
        {
            if (referencesDic_EffectTexture.ContainsKey(texturePath))
            {
                foreach (var dependency in referencesDic_EffectTexture[texturePath])
                {
                    AssetImporter importer = AssetImporter.GetAtPath(dependency);
                    if (!string.IsNullOrEmpty(importer.assetBundleName))
                    {
                        return true;
                    }
                    else
                    {
                        bool value = CalculateCurAssetIsBundle(dependency);
                        if (value) return true;
                    }
                }
            }
            return false;
        }

        public static bool CalculateCurAssetIsBundle(string path)
        {
            if (path.EndsWith(".mat"))
            {
                if (referencesDic_Mat.ContainsKey(path))
                {
                    foreach (var dependency in referencesDic_Mat[path])
                    {
                        if (path == dependency) continue;
                        AssetImporter importer = AssetImporter.GetAtPath(dependency);
                        if (!string.IsNullOrEmpty(importer.assetBundleName))
                        {
                            return true;
                        }
                        else
                        {
                            CalculateCurAssetIsBundle(dependency);
                        }
                    }
                }
                return false;
            }
            else if (path.EndsWith(".unity"))
            {
                if (referencesDic_Scene.ContainsKey(path))
                {
                    foreach (var dependency in referencesDic_Scene[path])
                    {
                        if (path == dependency) continue;
                        AssetImporter importer = AssetImporter.GetAtPath(dependency);
                        if (!string.IsNullOrEmpty(importer.assetBundleName))
                        {
                            return true;
                        }
                        else
                        {
                            CalculateCurAssetIsBundle(dependency);
                        }
                    }
                }
                return false;
            }
            else if (path.EndsWith(".asset"))
            {
                if (referencesDic_Asset.ContainsKey(path))
                {
                    foreach (var dependency in referencesDic_Asset[path])
                    {
                        if (path == dependency) continue;
                        AssetImporter importer = AssetImporter.GetAtPath(dependency);
                        if (!string.IsNullOrEmpty(importer.assetBundleName))
                        {
                            return true;
                        }
                        else
                        {
                            CalculateCurAssetIsBundle(dependency);
                        }
                    }
                }
                return false;
            }
            else if (path.EndsWith(".prefab"))
            {
                if (referencesDic_Prefab.ContainsKey(path))
                {
                    foreach (var dependency in referencesDic_Prefab[path])
                    {
                        if (path == dependency) continue;
                        AssetImporter importer = AssetImporter.GetAtPath(dependency);
                        if (!string.IsNullOrEmpty(importer.assetBundleName))
                        {
                            return true;
                        }
                        else
                        {
                            CalculateCurAssetIsBundle(dependency);
                        }
                    }
                }
                return false;
            }
            else 
            {
                Debug.LogError($"纹理资源被其他资源类型引用{path}");
                return false; 
            }

        }

        private static float CalculatePixelsRatio(Texture2D texture) 
        {
            // 计算空白像素占比
            Color[] pixels = texture.GetPixels(0, 0, texture.width, texture.height);
            int transparentPixelsCount = 0;
            foreach (Color pixel in pixels)
            {
                if (pixel.a == 0)
                {
                    transparentPixelsCount++;
                }
            }

            float transparentPixelsRatio = (float)transparentPixelsCount / (pixels.Length);
            return transparentPixelsRatio;
        }

        #endregion

        #region 网格资源的检测

        static int meshTriangles = 5500;            //三角面数
        static int meshvertex = 15000;            //顶点个数
        static string[] checkOutPath_Mesh = new string[] { "Ultimate Game Tools" };
        static string[] checkOutPath_Mesh_triangles = new string[] { "andelie_LOD1.mesh", "anta_LOD1.mesh", "bahamute_jiao_low_LOD1.mesh"
                                                                      ,"dannisi_LOD1.mesh","falineili_LOD1.mesh","feinikesi_LOD1.mesh","laina_LOD1.mesh","nikou_LOD1.mesh",
                                                                       "tiyamate_LOD1.mesh","tulongzhe_LOD1.mesh","tuolun_LOD1.mesh","xiersha_LOD1.mesh","yinghuo01_LOD1.mesh",
                                                                        "zhanzhengnvshen_LOD1.mesh","Yisuoerde_an_LOD1.mesh"};


        static string[] checkStr_mesh = new string[] { "三角面数(超5500)", "顶点数(超15000)","读写权限" };
        
        static string[] checkOutPath_Mesh_read_write = new string[]
        {
            "Animations/Characters/XG_guaguale/Ex/Mesh_0.asset",
            "Animations/UI/gq_icon_guo/Ex/Mesh_0.asset",
        };

        public static void CheckMeshResource(string inputDir) 
        {
            string[] guids = AssetDatabase.FindAssets("t:Mesh", new string[] { inputDir });
            //检测
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                if (path.ToLower().EndsWith(".fbx") || path.ToLower().EndsWith(".obj")/*|| path.ToLower().EndsWith(".asset")*/) { continue; }
                if (inStringArray(path, checkOutPath_Mesh)) { continue; }
                bool isminiGame = path.Contains("CasualGame");
                Mesh meshAsset = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                int triangleCount = meshAsset.triangles.Length / 3;
                int vertexCount = meshAsset.vertexCount;
                bool isReadable = meshAsset.isReadable;
                if (path.Contains("/Art/Characters")) 
                {
                    //剔除Characters文件夹下非Lod的检测
                    string fileName = Path.GetFileNameWithoutExtension(path);
                    if (!fileName.Contains("LOD")) { continue; }
                }
                //Debug.LogError($"{path}:面片数{triangleCount}：顶点数：{vertexCount}，读写权限{isReadable}");
                if (triangleCount > meshTriangles) 
                {
                    if (inStringArray(path, checkOutPath_Mesh_triangles)) { continue; }
                    AddInCheckDict(isminiGame, checkStr_mesh[0], $"{path},三角面数:{triangleCount}");
                }
                if (vertexCount > meshvertex)
                {
                    AddInCheckDict(isminiGame, checkStr_mesh[1], $"{path},顶点个数:{vertexCount}");
                }
                if (meshAsset.isReadable)
                {
                    string pattern = @"^Assets/.+/Ex/Mesh_.*\.asset$";
                    Match match = Regex.Match(path, pattern);
                    if (!match.Success)
                    {
                        if (inStringArray(path,checkOutPath_Mesh_read_write))
                            continue;
                        AddInCheckDict(isminiGame, checkStr_mesh[2], $"{path},读写权限:{isReadable}");
                    }
                    else 
                    {
                        if (isminiGame)
                        {
                            AddInCheckDict(isminiGame, checkStr_mesh[2], $"{path},小游戏是否需要修复这个读写权限:{isReadable}");
                        }
                        else //修复处理这种数据非小游戏
                        {
                            if (inStringArray(path,checkOutPath_Mesh_read_write))
                                continue;
                            //MarkModifiedMesh(meshAsset);
                            AddInCheckDict(isminiGame, checkStr_mesh[2], $"{path},手动修复这个读写权限:{isReadable}");
                        }
                    }
                }

            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

        }

        //工具修复以.asset结尾的mesh资源的读写
        [MenuItem("Tools/Resources/纹理资源读写权限关闭(非小游戏)", false, 1)]
        public static void FixMeshReadable()
        {
            string[] guids = AssetDatabase.FindAssets("t:Mesh", new string[] { "Assets" });
            //检测
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                if (path.ToLower().EndsWith(".fbx") || path.ToLower().EndsWith(".obj")/*|| path.ToLower().EndsWith(".asset")*/) { continue; }
                if (inStringArray(path, checkOutPath_Mesh)) { continue; }
                bool isminiGame = path.Contains("CasualGame");
                Mesh meshAsset = AssetDatabase.LoadAssetAtPath<Mesh>(path);
                bool isReadable = meshAsset.isReadable;
                if (path.Contains("/Art/Characters"))
                {
                    //剔除Characters文件夹下非Lod的检测
                    string fileName = Path.GetFileNameWithoutExtension(path);
                    if (!fileName.Contains("LOD")) { continue; }
                }
                if (meshAsset.isReadable)
                {
                    string pattern = @"^Assets/.+/Ex/Mesh_.*\.asset$";
                    Match match = Regex.Match(path, pattern);
                    if (!match.Success)
                    {
                    }
                    else
                    {
                        if (isminiGame)
                        {
                        }
                        else //修复处理这种数据非小游戏
                        {
                            Debug.LogWarning($"修复的资源:{path}");
                            MarkModifiedMesh(meshAsset);
                        }
                    }
                }

            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

        }

        #endregion

        #region 字体资源的检测

        static string[] checkStr_font = new string[] { "字体资源会多一份(Resources下或者ab资源下载前的资源引用)" };
        static string[] checkOutPath_Font = new string[] { "/Resources/","UIFeedback.prefab","update.unity" };

        [MenuItem("Tools/Resources/字体资源检测", false, 1)]
        public static void CheckFontRes()
        {
            DoCheckRes("", Application.dataPath, 3);
        }
        public static void CheckFontResource()
        {
            if (referencesDic_Font == null) { FindReferencesData(); }
            foreach (var path in referencesDic_Font)
            {
                foreach (var dependency in path.Value) 
                {
                    if (dependency.Contains("/Resources/"))
                    {
                        bool isminiGame = path.Key.Contains("CasualGame");
                        AddInCheckDict(isminiGame, checkStr_font[0], $"{path.Key}被Resources下的文件{dependency}引用");                    }
                    else
                    {
                        //查找对应的依赖资源是否有Resource下的或者资源下载之前要用的字体资源。
                        if (inStringArray(dependency, checkOutPath_Font))
                        {
                            if (path.Key.Contains("FZY4JW"))
                            {
                                Debug.LogWarning(path.Key + "字体资源被" + dependency + "引用");
                            }
                            else
                            {
                                bool isminiGame = path.Key.Contains("CasualGame");
                                AddInCheckDict(isminiGame, checkStr_font[0], $"{path.Key}被资源下载前的{dependency}引用!!!");
                            }
                        }
                    }
                }
            }
        }

        #endregion

        #region 重复资源的检测
        static string[] checkStr_repeat = new string[] { "项目内Md5一致的重复文件)" };


        [MenuItem("Tools/Resources/重复资源检测", false, 1)]
        public static void CheckRepeatRes() 
        {
            DoCheckRes("", Application.dataPath, 4);
        }


        public static void CheckRepeatResource()
        {
           
            if (referencesDic_Texture == null) { FindReferencesData(); }
            Dictionary<string, List<string>> fileHashesDic = new Dictionary<string, List<string>>();
            // 获取项目内所有文件
            string path = "Assets/";
            string[] files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories);


            foreach (string file in files)
            {
                if (file.EndsWith(".meta") || file.ToLower().EndsWith(".fbx")) { continue; }
                // 使用MD5计算文件的哈希值
                using (var md5 = System.Security.Cryptography.MD5.Create())
                {
                    using (var stream = File.OpenRead(file))
                    {
                        byte[] hash = md5.ComputeHash(stream);
                        string hashStr = BitConverter.ToString(hash).Replace("-", "").ToLower();

                        // 判断是否存在相同哈希值的文件
                        if (fileHashesDic.ContainsKey(hashStr))
                        {
                            fileHashesDic[hashStr].Add(file);
                        }
                        else
                        {
                            List<string> hashFiles = new List<string>();
                            hashFiles.Add(file);
                            fileHashesDic.Add(hashStr, hashFiles);
                        }
                    }
                }
            }

            foreach (var file in fileHashesDic.OrderByDescending(item => item.Value.Count)) 
            {
                if (file.Value.Count > 1)
                {
                    //文件大小
                    bool isOutLimit = false;
                    bool isTexture = false;
                    if (file.Value[0].Replace("\\", "/").Contains("/Effect")) { continue; }
                    string size = FileSizeCalculator(file.Value[0], out isOutLimit,out isTexture);
                    if (!isOutLimit) { continue; }
                    string countStr = "重复资源个数";
                    int count = 0;
                    string sizeStr = $"个-- - 单个资源大小:{ size}";
                    string str = $" :: {Path.GetFileName(file.Value[0])}\n";
                    foreach (var fi in file.Value) 
                    {
                        if (isTexture)
                        {
                            string fiV = ConvertToAssetPath(fi.Replace("\\", "/"));
                            Debug.LogError($"fiv:{fiV}");
                            if (fiV.Replace("\\", "/").Contains("/Effect")) { continue; }
                            AssetImporter importer = AssetImporter.GetAtPath(ConvertToAssetPath(file.Value[0]));

                            bool isBundle = CalculateTextureIsBundle(fiV);
                            if (isBundle || importer.assetBundleName.ToLower().EndsWith(".spritepack"))
                            {
                                count++;
                                str += fiV + "\n";
                            }
                        }
                        else 
                        {
                            Debug.LogError($"fi:{fi}");
                            if (fi.Replace("\\", "/").Contains("/Effect")) { continue; }
                            count++;
                            str += fi + "\n";
                        }
                    }
                    countStr += count;
                    if (count == 0) continue;
                    if (checkInMiniGame == null)
                    {
                        Debug.LogError($"{file.Key}{countStr}{sizeStr}重复文件{str}");
                    }
                    else 
                    {
                        bool isminiGame = str.Contains("CasualGame");
                        if (isTexture)
                        {
                            AddInCheckDict(isminiGame, checkStr_repeat[0], $"{file.Key}{countStr}{sizeStr}--{str}");
                        }
                        else 
                        {
                            AddInCheckDict(isminiGame, checkStr_repeat[0], $"{file.Key}:{countStr}{sizeStr}--{str}");
                        }
                    }
                }
            }
        }

        #endregion

        #region 共用

        // 将本地文件路径转换为Assets文件夹下的Asset路径
        public static string ConvertToAssetPath(string fullPath)
        {
            string assetsFolderPath = Application.dataPath;
            assetsFolderPath = assetsFolderPath.Substring(0, assetsFolderPath.LastIndexOf("Assets"));

            string relativePath = fullPath.Replace(assetsFolderPath, "");
            return relativePath;
        }
        static decimal limitValue = 4 * 1024;
        private static string FileSizeCalculator(string file,out bool isoutLimet, out bool istexture)
        {
            string size = "";
            isoutLimet = false;
            istexture= false;
            Texture texture = AssetDatabase.LoadAssetAtPath<Texture2D>(ConvertToAssetPath(file));
            if (texture == null)
            {
                texture = AssetDatabase.LoadMainAssetAtPath(ConvertToAssetPath(file)) as Texture;
            }
            if (texture == null)
            {
                istexture = false;
                if (File.Exists(file))
                {
                    FileInfo fileInfo = new FileInfo(file);
                    long fileSizeInBytes = fileInfo.Length;
                    isoutLimet = fileSizeInBytes > limitValue;
                    size = "文件大小:"+fileSizeInBytes / 1024f + "KB";
                }
                else
                {
                    Debug.Log("File does not exist.");
                }
            }
            else
            {
                istexture = true;
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(texture);
                isoutLimet = memorySize > limitValue;
                size = "Texture大小:"+AssetStatisticsUtility.ByteToKB(memorySize, true) + "KB";
            }
            return size;
        }

        private static void MarkModifiedMesh(Mesh meshAsset) 
        {
            meshAsset.MarkModified();
            SerializedObject so = new SerializedObject(meshAsset);
            SerializedProperty valuesProperty = so.FindProperty("m_IsReadable");
            valuesProperty.boolValue = false;
            so.ApplyModifiedProperties();
        }

        private static void AddInCheckDict(bool isminiGame, string checkKey, string path)
        {
            if (isminiGame)
            {
                //小游戏资源过滤处理相关
                if (checkInMiniGameCache != null) 
                {
                    if (checkInMiniGameCache.ContainsKey(checkKey))
                    {
                        var list = checkInMiniGameCache[checkKey];
                        //如果发现存在此条记录，则过滤
                        if (list.Contains(path))
                        {
                            return;
                        }
                    }
                }
                if (!checkInMiniGame.ContainsKey(checkKey))
                {
                    checkInMiniGame.Add(checkKey, new List<string>());
                }
                checkInMiniGame[checkKey].Add(path);
            }
            else
            {
                if (!checkInGame.ContainsKey(checkKey))
                {
                    checkInGame.Add(checkKey, new List<string>());
                }
                checkInGame[checkKey].Add(path);
            }
        }


        private static bool inStringArray(string path, string[] array)
        {
            bool isIn = false;
            for (int i = 0; i < array.Length; i++)
            {
                if (path.Contains(array[i]))
                {
                    isIn = true;
                    break;
                }
            }
            return isIn;
        }


        #endregion

        #region 检测项目内sprite的资源被当作Texture2d所引用
        static StringBuilder sb;
        static float checkLimitSize = 10;
        static List<AssetDataStruct> list;
        struct AssetDataStruct 
        {
            public string path;
            public string objName;
            public string spriteName;
            public float size;
        }

        [MenuItem("Tools/Resources/SpriteUseByTexture2D", false, 1)]
        public static void CheckSpriteUseByTexture2D()
        {
            sb = new StringBuilder();
            list = new List<AssetDataStruct>();
            FindReferencesData();
            string[] guids = AssetDatabase.FindAssets("t:prefab t:material", new string[] { "Assets" });
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                if (path.EndsWith(".prefab"))
                {
                    GameObject gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                    RawImage[] rawImages = gameObject.GetComponentsInChildren<RawImage>(true);
                    string[] dependencies = AssetDatabase.GetDependencies(path, false);
                    foreach (string dependency in dependencies)
                    {
                        Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(dependency);
                        if (texture == null)
                        {
                            texture = AssetDatabase.LoadMainAssetAtPath(dependency) as Texture2D;
                        }
                        if (texture != null)
                        {
                            TextureImporter importer = AssetImporter.GetAtPath(dependency) as TextureImporter;
                            foreach (RawImage rawImage in rawImages)
                            {
                                if (rawImage.texture == texture && importer.textureType == TextureImporterType.Sprite)
                                {
                                    AssetDataStruct assetDataStruct = new AssetDataStruct();
                                    assetDataStruct.path = path;
                                    assetDataStruct.objName = rawImage.name;
                                    assetDataStruct.spriteName = texture.name;
                                    decimal memorySize = EditorTextureUtil.GetStorageMemorySize(texture);
                                    assetDataStruct.size = float.Parse(AssetStatisticsUtility.ByteToKB(memorySize, true));
                                    if (assetDataStruct.size > checkLimitSize)
                                    {
                                        list.Add(assetDataStruct);
                                    }
                                }
                            }
                        }
                    }

                }
                else if (path.EndsWith(".mat"))
                {
                    bool isminiGame = path.Contains("CasualGame");
                    if (isminiGame) { continue; }
                    Material material = AssetDatabase.LoadAssetAtPath<Material>(path);

                    int textureCount = ShaderUtil.GetPropertyCount(material.shader);

                    for (int i = 0; i < textureCount; i++)
                    {        
                        if (ShaderUtil.GetPropertyType(material.shader, i) == ShaderUtil.ShaderPropertyType.TexEnv)
                        {
                            string texturePropertyName = ShaderUtil.GetPropertyName(material.shader, i);
                            string propertyDescription = ShaderUtil.GetPropertyDescription(material.shader, i);
                            if (propertyDescription.Contains("Sprite Texture"))
                            {
                                continue;
                            }
                            Texture texture = material.GetTexture(texturePropertyName);
                            string texturePath = AssetDatabase.GetAssetPath(texture);
                            TextureImporter importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
                            if (texture != null && importer !=null && importer.textureType == TextureImporterType.Sprite)
                            {
                                bool isbundle = CalculateCurAssetIsBundle(path);
                                if (isbundle) 
                                {
                                    AssetDataStruct assetDataStruct = new AssetDataStruct();
                                    assetDataStruct.path = path;
                                    assetDataStruct.objName = "";
                                    assetDataStruct.spriteName = texture.name;
                                    decimal memorySize = EditorTextureUtil.GetStorageMemorySize(texture);
                                    assetDataStruct.size = float.Parse(AssetStatisticsUtility.ByteToKB(memorySize, true));
                                    if (assetDataStruct.size > checkLimitSize) 
                                    {
                                        list.Add(assetDataStruct);
                                    }
                                }

                            }
                        }
                    }
                }
            }

            //对大小进行排序
            sb.AppendLine($"资源路径,挂载的节点名字,图片Name,大小(KB)");
            foreach (var item in list.OrderByDescending(item => item.size))
            {
                sb.AppendLine($"{item.path},{item.objName},{item.spriteName},{item.size}");
            }

            File.WriteAllText($"{Application.dataPath}/SpriteUseByTexture.csv", sb.ToString(), Encoding.UTF8);
        }

        #endregion

    }
}