using LitJson;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        private const string CONFIG_PATH = "/../statistics_commands.json";
        private static int CurrentVersion = 0;

        [MenuItem("AssetBundles/模拟Jenkins执行统计命令", false, 1)]
        public static void JenkinsExecuteCommands()
        {
            string configPath = $"{Application.dataPath}{CONFIG_PATH}";
            if (!File.Exists(configPath))
            {
                Debug.LogError($"{configPath} not found");
                return;
            }

            CurrentVersion = AssetStatisticsUtility.GetLastVersion() + 1;
            bool isErrorBreak = false;

            Dictionary<string, string> cmds =
                Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(File.ReadAllText(configPath));

            foreach (var command in cmds)
            {
                try
                {
                    Debug.Log($"Begin execute:{command.Key} {command.Value}");
                    if (!ExecuteCommand(command.Key, command.Value))
                    {
                        isErrorBreak = true;
                        Debug.LogError($"Error execute:{command.Key} {command.Value}");
                        break;
                    }
                    else
                    {
                        Debug.Log($"End execute:{command.Key} {command.Value}");
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Exception execute:{command.Key} {command.Value}\n{e}");
                    isErrorBreak = true;
                    break;
                }
            }

            //拷贝最新统计结果
            string srcPath = AssetStatisticsUtility.GetDefaultOutput(CurrentVersion);
            string destPath = Path.Combine(srcPath, "..", "newest");
            if (Directory.Exists(destPath))
                Directory.Delete(destPath, true);
            Directory.CreateDirectory(destPath);
            string[] files = Directory.GetFiles(srcPath);
            foreach (string filePath in files)
            {
                File.Copy(filePath, $"{Path.Combine(destPath, Path.GetFileName(filePath))}");
            }

            if (isErrorBreak)
            {
                EditorApplication.Exit(1);
            }
        }

        public static bool ExecuteCommand(string order, string param)
        {
            string[] args = param.Split(',');
            Type type = typeof(AssetStatisticsEditor);
            MethodInfo methodInfo =
                type.GetMethod(order, BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static);

            if (methodInfo == null)
            {
                throw new Exception("Command not present!");
            }

            bool result = (bool)methodInfo.Invoke(null, new object[] { args });
            return result;
        }

        #region 统计资源包中资源占比

        private static bool filterModelHigh = true;

        private static string[] assetsDistributionExcludeFilter =
        {
            @".*\.manifest$",
            @".*\.meta",
        };

        private static string[] assetsDistributionSpecialFilter =
        {
//需要请添加其他平台的资源
#if UNITY_WEBGL
            @"(battlescp)_([a-z0-9]*)$",
            @"(exec)[0-9]_([a-z0-9]*)\.asset$",
            @"(shaders_for_warmup)_([a-z0-9]*)$",
            @"(WebGL)_([a-z0-9]*)$",
            @"(webgldownloadpack)_([a-z0-9]*)$",
            @"(wishpoolnew)_([a-z0-9]*)$",
            @"(luascript)_([a-z0-9]*)\.zip$",
#elif UNITY_ANDROID
#elif UNITY_IOS
#else
#endif
        };

        private static Dictionary<string, string> assetsDistributionEntityFilter = new Dictionary<string, string>()
        {
            { @"\.(bmp|tif|jpg|png|tga|psd|rendertexture|spritepack)", "图集图像类" },
            { @"\.(ttf|dfont|otf)", "字体类" },
            { @"\.(anim|playable|controller)", "动画类" },
            { @"\.(wav|ogg|mixer|mp3)", "音频类" },
            { @"\.(mesh|fbx|ab|mat)", "模型类" },
            { @"\.(prefab|textures)", "Prefab类" },
            { @"\.(asset|alone|assetset|bytes|json|txt)", "配置类" },
            { @"\.(zip)", "Lua脚本类" },
            { @"\.(unity|unity3d)", "场景类" },
        };

        private static double GetUTCNowTimestamp()
        {
            DateTime dt0 = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
            TimeSpan ts = DateTime.UtcNow - dt0;

            return Math.Floor(ts.TotalSeconds);
        }

        private static string GetTGMKB(long size)
        {
            long kb = 1024;
            long mb = kb * 1024;
            long gb = mb * 1024;
            if (size >= gb)
            {
                return ((double)size / gb).ToString("f2") + "GB";
            }
            else if (size >= mb)
            {
                return ((double)size / mb).ToString("f2") + "MB";
            }
            else if (size >= kb)
            {
                return ((double)size / kb).ToString("f2") + "KB";
            }
            else
                return size + "B";
        }

        private static int GetProjectAssetsDistribution(out string target)
        {
            string outputDir = $"{Application.dataPath}/../Profiler";
            if (!Directory.Exists(outputDir))
            {
                Directory.CreateDirectory(outputDir);
            }

            string outputFileName = "AssetsDistribution";
            double timestamp = GetUTCNowTimestamp();
            string outputFilePath = $"{outputDir}/{outputFileName}_{timestamp}.json";
            target = outputFilePath;

            if (File.Exists(outputFilePath))
            {
                File.Delete(outputFilePath);
            }

            string assetDir = $"{Application.dataPath}/StreamingAssets/AssetBundles/WebGL";
            if (!Directory.Exists(assetDir))
            {
                return -1;
            }

            long totalSize = 0;
            JsonData jsDataByExtension = new JsonData();
            string[] fs = Directory.GetFiles(assetDir, "*", SearchOption.AllDirectories);
            string groupKey = null;
            for (int i = 0, imax = fs.Length; i < imax; i++)
            {
                EditorUtility.DisplayProgressBar("", $"Dealing with {fs[i]}", (float)i / imax);

                bool isContinue = false;
                for (int j = 0, jmax = assetsDistributionExcludeFilter.Length; j < jmax; j++)
                {
                    if (Regex.IsMatch(fs[i], assetsDistributionExcludeFilter[j]))
                    {
                        isContinue = true;
                        break;
                    }
                }

                //开启高模英雄模型过滤
                if (filterModelHigh && !isContinue)
                {
                    if (fs[i].EndsWith("_mesh.mesh"))
                    {
                        string fLow1 = Regex.Replace(fs[i], @"_mesh\.mesh$", "_lod1.mesh");
                        string fLow2 = Regex.Replace(fs[i], @"models/.*_mesh\.mesh$", "single_group.assets");
                        if (File.Exists(fLow1) || File.Exists(fLow2))
                        {
                            isContinue = true;
                            Debug.LogWarning($"过滤高模ab资源 {fs[i]}");
                        }
                    }
                }

                if (isContinue)
                {
                    continue;
                }

                FileInfo fi = new FileInfo(fs[i]);

                totalSize += fi.Length;

                bool isSpecial = false;
                for (int j = 0, jmax = assetsDistributionSpecialFilter.Length; j < jmax; j++)
                {
                    if (Regex.IsMatch(fs[i], assetsDistributionSpecialFilter[j]))
                    {
                        groupKey = Regex.Match(fs[i], assetsDistributionSpecialFilter[j]).Groups[1].Value;
                        isSpecial = true;
                        break;
                    }
                }

                if (!isSpecial)
                {
                    groupKey = fi.Extension;
                }

                if (string.IsNullOrEmpty(groupKey))
                {
                    groupKey = ".alone";
                }

                if (!jsDataByExtension.Keys.Contains(groupKey))
                {
                    jsDataByExtension[groupKey] = new JsonData();
                    jsDataByExtension[groupKey]["key"] = groupKey;
                    jsDataByExtension[groupKey]["size"] = new JsonData(0L);
                    jsDataByExtension[groupKey]["sizes"] = GetTGMKB(0L);
                    jsDataByExtension[groupKey]["textures"] = new JsonData();
                    jsDataByExtension[groupKey]["textures"].SetJsonType(JsonType.Array);
                }

                JsonData groupData = jsDataByExtension[groupKey];

                long size = (long)(groupData["size"]);

                groupData["size"] = (size + fi.Length);
                groupData["sizes"] = GetTGMKB((long)(groupData["size"]));

                JsonData jsItem = new JsonData();
                jsItem["size"] = fi.Length;
                jsItem["sizes"] = GetTGMKB(fi.Length);
                jsItem["name"] = fi.Name;

                groupData["textures"].Add(jsItem);
            }

            JsonData jsDataByGroup = new JsonData();
            groupKey = null;
            string key = null;
            ICollection<string> keys = jsDataByExtension.Keys;
            IEnumerator<string> ie = keys.GetEnumerator();
            while (ie.MoveNext())
            {
                key = ie.Current;

                groupKey = null;
                foreach (var item in assetsDistributionEntityFilter)
                {
                    if (Regex.IsMatch(key, item.Key))
                    {
                        groupKey = item.Value;
                        break;
                    }
                }

                if (string.IsNullOrEmpty(groupKey))
                {
                    groupKey = "其它";
                }

                if (!jsDataByGroup.Keys.Contains(groupKey))
                {
                    jsDataByGroup[groupKey] = new JsonData();
                    jsDataByGroup[groupKey]["size"] = 0L;
                    jsDataByGroup[groupKey]["sizes"] = GetTGMKB(0L);
                    jsDataByGroup[groupKey]["textures"] = new JsonData();
                    jsDataByGroup[groupKey]["textures"].SetJsonType(JsonType.Array);
                }

                JsonData d = jsDataByExtension[key];
                long size = (long)(d["size"]);

                JsonData groupData = jsDataByGroup[groupKey];
                long sizeGroup = (long)(groupData["size"]);

                groupData["size"] = (sizeGroup + size);
                groupData["sizes"] = GetTGMKB(sizeGroup + size);

                jsDataByGroup[groupKey]["textures"].Add(d);
            }

            List<JsonData> jsList = new List<JsonData>();
            keys = jsDataByGroup.Keys;
            ie = keys.GetEnumerator();
            while (ie.MoveNext())
            {
                key = ie.Current;
                JsonData d = jsDataByGroup[key];

                d["key"] = key;
                long size = (long)(d["size"]);
                float percent = (float)size / totalSize;
                d["percent"] = (percent * 100).ToString("f5") + "%";

                jsList.Add(d);
            }

            jsList.Sort((data1, data2) =>
            {
                long size1 = (long)(data1["size"]);
                long size2 = (long)(data2["size"]);

                if (size1 < size2)
                {
                    return 1;
                }
                else if (size1 == size2)
                {
                    return 0;
                }
                else
                {
                    return -1;
                }
            });

            JsonData re = new JsonData();
            re["totalSize"] = totalSize;
            re["totalSizes"] = GetTGMKB(totalSize);
            re["textures"] = new JsonData();

            for (int i = 0, imax = jsList.Count; i < imax; i++)
            {
                re["textures"][jsList[i]["key"].ToString()] = jsList[i];
            }

            File.WriteAllText(outputFilePath, JsonMapper.ToJson(re));

            EditorUtility.ClearProgressBar();

            return 0;
        }

        [MenuItem("AssetBundles/Get Project Assets Distribution")]
        public static void ToGetProjectAssetsDistribution()
        {
            if (EditorUtility.DisplayDialog("",
                    "Please ensure that the assets below StreamingAssets is latest. \nOr building assetbundle is suggested.",
                    "Continue", "Quit"))
            {
                string output;
                int re = GetProjectAssetsDistribution(out output);

                if (EditorUtility.DisplayDialog("", $"All done!\ncode:{re}", "OK"))
                {
                    string outputDir = $"{Application.dataPath}/../Profiler";
                    EditorUtility.RevealInFinder(output);
                }
            }
        }

#endregion

#region 查找files2.txt hash相同数量个数

        public class File2Data
        {
            public Dictionary<string, string[]> list;
        }

        [MenuItem("AssetBundles/查找files2.txt hash相同数量个数")]
        static void CompareToFile2Hash()
        {
            string srcPath = EditorUtility.OpenFilePanel("选择新的Files2.txt", Application.dataPath, "txt");
            if (!File.Exists(srcPath))
            {
                Debug.LogError("新的Files2不存在！");
                return;
            }

            string comparePath = EditorUtility.OpenFilePanel("选择对比老的Files2.txt", Path.GetDirectoryName(srcPath), "txt");
            if (!File.Exists(comparePath))
            {
                Debug.LogError("老的Files2不存在！");
                return;
            }

            File2Data filesData = Newtonsoft.Json.JsonConvert.DeserializeObject<File2Data>(File.ReadAllText(srcPath));
            File2Data files2Data =
                Newtonsoft.Json.JsonConvert.DeserializeObject<File2Data>(File.ReadAllText(comparePath));

            Debug.Log("新的files2总个数:" + filesData.list.Count + "/旧的files2总个数:" + files2Data.list.Count);
            int fontCount = 0;
            foreach (var item in filesData.list)
            {
                string[] value;
                if (files2Data.list.TryGetValue(item.Key, out value))
                {
                    //Debug.Log(item.Key + "/" + item.Value[1]);
                    if (item.Value[1] == value[1])
                    {
                        //Debug.LogError(item.Key + "/" + item.Value[1]);
                        fontCount++;
                    }
                }
            }

            Debug.Log("相同的个数：" + fontCount + "/" + filesData.list.Count);
        }

#endregion
    }
}