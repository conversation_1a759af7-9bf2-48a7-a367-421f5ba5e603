using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        const ModelImporterAnimationCompression ANIM_COMPRESSION = ModelImporterAnimationCompression.KeyframeReduction;
        const uint OPTIMIZE_FLOAT = 3;
        const float OPTIMIZE_ERROR = 0.1f;

        [MenuItem("Assets/MiniGameTool/英雄动画检查", false, 1)]
        public static void AnimationCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(AnimationCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool AnimationCheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Characters");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            
            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Model", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            bool result = true;
            StringBuilder statistics = new StringBuilder("资源路径,压缩方式,位置容错率,旋转容错率,缩放容错率\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;

                AnimationClip src;
                AnimationClip dst;
                if (CheckAnimImportSetting(importer, out src, out dst))
                {
                    continue;
                }

                result = false;
                statistics.AppendLine($"{path},{importer.animationCompression}," +
                                      $"{importer.animationPositionError}," +
                                      $"{importer.animationRotationError}," +
                                      $"{importer.animationScaleError}");
            }
            
            File.WriteAllText($"{outputDir}/AnimationCheck.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return result;
        }
        
        [MenuItem("Assets/MiniGameTool/英雄动画修复", false, 1)]
        public static void AnimationFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(AnimationFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool AnimationFixCommand(string[] args)
        {        
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Art/Characters");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            
            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Model", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            bool result = true;
            StringBuilder statistics = new StringBuilder("原始资源路径,修复资源路径\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter importer = AssetImporter.GetAtPath(path) as ModelImporter;

                AnimationClip src;
                AnimationClip dst;
                if (CheckAnimImportSetting(importer, out src, out dst))
                {
                    continue;
                }

                importer.animationCompression = ANIM_COMPRESSION;
                importer.animationPositionError = OPTIMIZE_ERROR;
                importer.animationRotationError = OPTIMIZE_ERROR;
                importer.animationScaleError = OPTIMIZE_ERROR;
                importer.SaveAndReimport();

                dst.ClearCurves();
                EditorUtility.CopySerialized(src, dst);
                OptimizeAnimationFloat(dst, OPTIMIZE_FLOAT);
                EditorUtility.SetDirty(dst);
                result = false;
                statistics.AppendLine($"{path},{AssetDatabase.GetAssetPath(dst)}");
            }
            AssetDatabase.SaveAssets();

            File.WriteAllText($"{outputDir}/AnimationFix.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return result;
        }

        private static bool CheckAnimImportSetting(ModelImporter importer, out AnimationClip src, out AnimationClip dst)
        {
            src = null;
            dst = null;
            
            if (!importer.importAnimation)
            {
                return true;
            }

            src = AssetDatabase.LoadAssetAtPath<AnimationClip>(importer.assetPath);
            if (src == null)
            {
                return true;
            }
            
            string path = $"{Path.GetDirectoryName(importer.assetPath)}/{src.name}.anim";
            dst = AssetDatabase.LoadAssetAtPath<AnimationClip>(path);
            if (dst == null)
            {
                return true;
            }

            if (importer.animationCompression >= ANIM_COMPRESSION
                && importer.animationPositionError >= OPTIMIZE_ERROR
                && importer.animationRotationError >= OPTIMIZE_ERROR
                && importer.animationScaleError >= OPTIMIZE_ERROR)
            {
                return true;
            }

            return false;
        }

        private static void OptimizeAnimationFloat(AnimationClip clip, uint digitNumber)
        {
            if (clip == null || digitNumber == 0)
            {
                return;
            }

            //浮点数精度压缩到f3
            uint floatFormat = 1;
            for (int i = 0; i < digitNumber; i++)
            {
                floatFormat *= 10;
            }
            
            AnimationClipCurveData[] curves = AnimationUtility.GetAllCurves(clip);
            Keyframe key;
            Keyframe[] keyFrames;
            if (curves != null && curves.Length > 0)
            {
                for (int ii = 0; ii < curves.Length; ++ii)
                {
                    AnimationClipCurveData curveDate = curves[ii];
                    if (curveDate.curve == null || curveDate.curve.keys == null)
                    {
                        //Debug.LogWarning(string.Format("AnimationClipCurveData {0} don't have curve; Animation name {1} ", curveDate, animationPath));
                        continue;
                    }

                    keyFrames = curveDate.curve.keys;
                    for (int i = 0; i < keyFrames.Length; i++)
                    {
                        key = keyFrames[i];
                        key.value = ClipFloat(key.value, floatFormat);
                        key.inTangent = ClipFloat(key.inTangent, floatFormat);
                        key.outTangent = ClipFloat(key.outTangent, floatFormat);
                        key.inWeight = ClipFloat(key.inWeight, floatFormat);
                        key.outWeight = ClipFloat(key.outWeight, floatFormat);
                        keyFrames[i] = key;
                    }

                    curveDate.curve.keys = keyFrames;
                    clip.SetCurve(curveDate.path, curveDate.type, curveDate.propertyName, curveDate.curve);
                }
            }
        }

        private static float ClipFloat(float value, uint format)
        {
            int _value = (int)(value * format);
            return (float)_value / format;
        }
    }
}