//#define ASSET_STUDIO

#if ASSET_STUDIO
using AssetStudioUtility = AssetStudio.AssetUtility;
#endif

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEditor;
using War.Battle;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        #if ASSET_STUDIO
        private static List<Mesh> GetMeshesByFBX(string assetPath)
        {
            var assets = AssetDatabase.LoadAllAssetsAtPath(assetPath);
            List<Mesh> meshes = new List<Mesh>();

            foreach (var asset in assets)
            {
                Mesh assetMesh = asset as Mesh;
                if (assetMesh)
                {
                    meshes.Add(assetMesh);
                }
            }

            return meshes;
        }

        private static string GetMeshOriginalPath(string meshName, string container)
        {
            string[] dependencies = AssetDatabase.GetDependencies(container);
            foreach (string dependencyPath in dependencies)
            {
                if (dependencyPath.EndsWith(".mesh") && Path.GetFileNameWithoutExtension(dependencyPath) == meshName)
                {
                    return dependencyPath;
                }
                else if (dependencyPath.EndsWith(".fbx") || dependencyPath.EndsWith(".FBX"))
                {
                    Mesh mesh = GetMeshesByFBX(dependencyPath).Find(mesh => mesh.name == meshName);
                    if (mesh)
                    {
                        return AssetDatabase.GetAssetPath(mesh);
                    }
                }
            }

            return null;
        }

        [MenuItem("AssetBundles/特效AB依赖统计", false, 4)]
        public static void EffectABDependenciesStatistics()
        {
            string input = EditorUtility.OpenFilePanel("选择Files.txt", Application.dataPath, "txt");
            string inputDir = Path.GetDirectoryName(input);
            string output = EditorUtility.OpenFolderPanel("选择导出文件夹", inputDir, "");

            try
            {
                ExecuteCommand(nameof(StatisticsEffectABDependenciesCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool StatisticsEffectABDependenciesCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string input = args[0];
            string outputDir = args[1];

            if (input.Contains("$Default"))
            {
                input = input.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!File.Exists(input))
            {
                throw new Exception("ABFiles.txt does not exist!");
            }
            else if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            var filesContent = File.ReadAllText(input);
            var filesDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(filesContent);

            if (!filesDic.ContainsKey("list"))
            {
                throw new Exception("File format exception!");
            }

            AssetStudio.AssetsManager manager = new AssetStudio.AssetsManager();
            List<string> loadFiles = new List<string>();
            string abRoot = Path.GetDirectoryName(input);
            Dictionary<string, decimal[]> meshes = new Dictionary<string, decimal[]>();
            Dictionary<string, string[]> textures = new Dictionary<string, string[]>();

            foreach (var fileInfo in filesDic["list"])
            {
                string assetPath = fileInfo.Key;
                string assetMD5 = fileInfo.Value[1];
                if (assetPath.StartsWith("art/effects/") && assetPath.EndsWith(".prefab"))
                {
                    string abPath = Path.Combine(abRoot, assetPath.Replace("/", "\\\\"));
                    if (!File.Exists(abPath))
                    {
                        abPath =
                            $"{Path.GetDirectoryName(abPath)}\\{Path.GetFileNameWithoutExtension(abPath)}_{assetMD5}{Path.GetExtension(abPath)}";
                    }

                    loadFiles.Add(abPath);
                }
            }

            manager.LoadFiles(loadFiles.ToArray());
            foreach (AssetStudio.SerializedFile asset in manager.assetsFileList)
            {
                List<AssetStudio.AssetItem> assetItems = AssetStudioUtility.GetAssetData(asset);
                foreach (AssetStudio.AssetItem item in assetItems)
                {
                    if (item.Type == AssetStudio.ClassIDType.Mesh)
                    {
                        AssetStudio.Mesh mesh = item.Asset as AssetStudio.Mesh;
                        string name = GetMeshOriginalPath(mesh.m_Name, item.Container);
                        decimal size = mesh.byteSize;
                        int vertexCount = mesh.m_VertexCount;
                        int tangentCount = mesh.m_Indices.Count / 3;

                        if (string.IsNullOrEmpty(name)) continue;

                        name = $"{item.Container}:{name}:{mesh.m_Name}";

                        if (meshes.ContainsKey(name))
                        {
                            name += item.UniqueID;
                        }

                        meshes.Add(name,
                            new decimal[] { vertexCount, tangentCount, size });
                    }
                }
            }

            manager.Clear();

            decimal totalSize = meshes.Sum(mesh => mesh.Value[2]);
            StringBuilder statistics = new StringBuilder();
            statistics.AppendLine($"资源总大小:{AssetStatisticsUtility.ByteToMB(totalSize)}\n");
            statistics.AppendLine($"资源路径,网格资源路径,网格名称,顶点数,面数,网格大小(KB)");
            foreach (var mesh in meshes.OrderByDescending(weight => weight.Value[0]))
            {
                if (mesh.Value[2] > 1024 * 10)
                {
                    string[] names = mesh.Key.Split(':');
                    statistics.AppendLine(
                        $"{names[0]},{names[1]},{names[2]},{mesh.Value[0]},{mesh.Value[1]},{AssetStatisticsUtility.ByteToKB(mesh.Value[2], true)}");
                }
            }

            File.WriteAllText($"{outputDir}/EffectABStatistics.csv", statistics.ToString());
            return true;
        }
        
        [MenuItem("AssetBundles/AB冗余统计", false, 4)]
        public static void AssetBundleRepeatStatistics()
        {
            string input = EditorUtility.OpenFilePanel("选择Files.txt", Application.dataPath, "txt");
            string inputDir = Path.GetDirectoryName(input);
            string output = EditorUtility.OpenFolderPanel("选择导出文件夹", inputDir, "");
            
            try
            {
                ExecuteCommand(nameof(AssetBundleRepeatStatisticsCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool AssetBundleRepeatStatisticsCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string input = args[0];
            string outputDir = args[1];

            if (input.Contains("$Default"))
            {
                input = input.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!File.Exists(input))
            {
                throw new Exception("ABFiles.txt does not exist!");
            }
            else if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            var filesContent = File.ReadAllText(input);
            var filesDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(filesContent);

            if (!filesDic.ContainsKey("list"))
            {
                throw new Exception("File format exception!");
            }

            AssetStudio.AssetsManager manager = new AssetStudio.AssetsManager();
            List<string> loadFiles = new List<string>();
            string abRoot = Path.GetDirectoryName(input);
            Dictionary<long, List<string>> mapper = new Dictionary<long, List<string>>();

            foreach (var fileInfo in filesDic["list"])
            {
                string assetPath = fileInfo.Key;
                string assetMD5 = fileInfo.Value[1];
                string abPath = Path.Combine(abRoot, assetPath.Replace("/", "\\\\"));
                if (!File.Exists(abPath))
                {
                    abPath =
                        $"{Path.GetDirectoryName(abPath)}\\{Path.GetFileNameWithoutExtension(abPath)}_{assetMD5}{Path.GetExtension(abPath)}";
                }

                loadFiles.Add(abPath);
            }

            manager.LoadFiles(loadFiles.ToArray());
            foreach (AssetStudio.SerializedFile asset in manager.assetsFileList)
            {
                List<AssetStudio.AssetItem> assetItems = AssetStudioUtility.GetAssetData(asset);
                foreach (AssetStudio.AssetItem item in assetItems)
                {
                    List<string> dependencies;
                    if (!mapper.TryGetValue(item.m_PathID, out dependencies))
                    {
                        dependencies = new List<string>();
                        mapper.Add(item.m_PathID, dependencies);
                    }

                    if (!dependencies.Contains(item.Container))
                    {
                        dependencies.Add(item.Container);
                    }
                }
            }

            manager.Clear();

            GUID guid;
            // decimal totalSize = meshes.Sum(mesh => mesh.Value[2]);
            // StringBuilder statistics = new StringBuilder();
            // statistics.AppendLine($"资源总大小:{AssetStatisticsUtility.ByteToMB(totalSize)}\n");
            // statistics.AppendLine($"资源路径,网格资源路径,网格名称,顶点数,面数,网格大小(KB)");
            // foreach (var mesh in meshes.OrderByDescending(weight => weight.Value[0]))
            // {
            //     if (mesh.Value[2] > 1024 * 10)
            //     {
            //         string[] names = mesh.Key.Split(':');
            //         statistics.AppendLine(
            //             $"{names[0]},{names[1]},{names[2]},{mesh.Value[0]},{mesh.Value[1]},{AssetStatisticsUtility.ByteToKB(mesh.Value[2], true)}");
            //     }
            // }
            //
            // File.WriteAllText($"{outputDir}/EffectABStatistics.csv", statistics.ToString());
            return true;
        }
#endif
    }
}