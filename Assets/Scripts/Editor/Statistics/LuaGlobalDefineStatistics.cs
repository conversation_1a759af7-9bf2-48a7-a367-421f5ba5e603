#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {

        static string[] formatStrings = {
            "全局变量未定义的文件路径{0},全局变量名,local声明行数,使用全局变量的行数,全局变量名,local声明行数,使用全局变量的行数",
            "Edump使用的文件路径{0},行数",
            "PB使用规范不使用冒号{0},行数",
            "DelayOnceCall 没有返回值{0},行数",
            "DelayOnceCall 第一个参数使用字符串{0},行数",
            "DelayCallOnce 第一个参数使用时间{0},行数",
            "RegisterEvent 使用这种方式定义事件函数(self.funName = function ){0},行数"
        };

        [MenuItem("Assets/Lua/Lua全局变量检测", false, 1)]
        public static void LuaGlobalDefineStatistics()
        {
            //string input = "$Default";
            //string output = "$Default";
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");
            try
            {
                ExecuteCommand(nameof(LuaGlobalDefineCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        // 定义要用作切分的字符串
       static string[] splitStrings = {
            "need global define",
            "use Edump line",
            "pb use colon",
            "DelayOnceCall no return value",
            "DelayOnceCall followed by a string",
            "DelayCallOnce not followed by a string",
            "RegisterEvent not use by self.funName = function define"
        };

        static Dictionary<string,List<string>> outputDic= new Dictionary<string,List<string>>();

        private static bool LuaGlobalDefineCommand(string[] args)
        {
            if (args.Length < 1)
            {
                throw new Exception("Invalid Arguments!");
            }

            string outputDir = args[0];

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }
            var errorData = LuaGlobalDefineCheck.ExecuteLuaSrcDietAll(null);
            outputDic.Clear();
            for (int i = 0; i < errorData.Count; i++)
            {
                SpliteData(errorData[i]);
            }
            bool success = true;
            if (errorData.Count > 0) 
            {
                success = false;
            }
            StringBuilder statistics = new StringBuilder();

            for (int i = 0; i < formatStrings.Length; i++)
            {
                if(outputDic.ContainsKey(splitStrings[i]) && outputDic[splitStrings[i]].Count > 0)
                {
                    statistics.AppendLine(string.Format(formatStrings[i], outputDic[splitStrings[i]].Count));
                    foreach (string log in outputDic[splitStrings[i]])
                    {
                        if (i == 0)
                        {
                            statistics.AppendLine(log);
                        }
                        else
                        {
                            statistics.AppendLine(log.Replace($"{splitStrings[i]} :", ""));
                        }
                    }
                }
            }

            File.WriteAllText($"{outputDir}/LuaGlobalDefineCheck.csv", statistics.ToString(), Encoding.UTF8);
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the LuaGlobalDefineCheck.csv, Please open modify!");
            }
            return success;
        }
        static void SpliteData(string inputString)
        {
            // 将字符串中的关键字合并为正则表达式
            string combinedSplitStrings = String.Join("|", Array.ConvertAll(splitStrings, Regex.Escape));

            // 使用 Regex.Split 进行切分
            string[] parts = Regex.Split(inputString, combinedSplitStrings);
            // 提取文件路径
            string pattern = @"[A-Z]:\/?([^,]+\.txt)"; 
            // 使用 Regex.Match 提取文件路径
            Match match = Regex.Match(inputString, pattern);
            string filePath = "";
            if (match.Success)
            {
                // 输出提取的文件路径
                filePath = match.Groups[1].Value;
            }
            // 遍历每个切分部分
            for (int j = 0; j < parts.Length; j++)
            {
                var trimmedPart = parts[j].Trim(); // 去除两边的空白
                if (!string.IsNullOrEmpty(trimmedPart)) // 只处理非空部分
                {
                    // 查找对应的键
                    string correspondingKey = null;
                    for (int k = 0; k < splitStrings.Length; k++)
                    {
                        if (inputString.Contains(splitStrings[k]) && inputString.IndexOf(splitStrings[k]) < inputString.IndexOf(trimmedPart))
                        {
                            correspondingKey = splitStrings[k];
                        }
                    }

                    // 添加到字典
                    if (correspondingKey == null)
                    {
                        correspondingKey = "need global define";
                    }

                    if (!(trimmedPart.Contains(":/") && trimmedPart.Contains(".txt")))
                    {
                        trimmedPart = $"{filePath},{trimmedPart}";
                    }
                    if (outputDic.ContainsKey(correspondingKey))
                    {
                        outputDic[correspondingKey].Add(trimmedPart);
                    }
                    else
                    {
                        outputDic.Add(correspondingKey, new List<string>() { trimmedPart });
                    }

                }
            }
        }

    }


}