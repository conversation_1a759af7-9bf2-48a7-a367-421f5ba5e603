using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEngine.U2D;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/图片AB检查", false, 1)]
        public static void TextureAbCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(TextureAbCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        [MenuItem("Assets/MiniGameTool/特效Common检查", false, 1)]
        public static void EffectCommonTextureCheck()
        {
            try
            {
                ExecuteCommand(nameof(TextureAbCheckCommand), $"Assets/Art/Effects/Common/Textures,$Default");
                ExecuteCommand(nameof(TextureAbCheckCommand), $"Assets/Art/Effects/Textures,$Default");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static string GetGroupABName(List<string> textures)
        {
            if (textures == null || textures.Count == 0)
            {
                return string.Empty;
            }

            textures.Sort();
            return AssetStatisticsUtility.GetABName(textures[0], ".spritepack");
        }

        private static bool TextureAbCheckCommand(string[] args)
        {
            //过滤小于100KB的冗余
            const decimal FILTER_THRESHOLD = 1024 * 100;

            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Effects/Textures");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            Dictionary<string, List<string>> textures = new Dictionary<string, List<string>>();
            Dictionary<string, long> textureMemorys = new Dictionary<string, long>();
            Dictionary<string, string> textureAbNames = new Dictionary<string, string>();
            Dictionary<string, string> pngs = new Dictionary<string, string>();
            List<string> smalls = new List<string>();
            List<string> alones = new List<string>();
            List<string> nones = new List<string>();
            Dictionary<string, List<string>> sameGroups = new Dictionary<string, List<string>>();
            Dictionary<string, List<string>> diffGroups = new Dictionary<string, List<string>>();

            //扫描图片
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(path);

                textures.Add(path, new List<string>());
                textureMemorys.Add(path, EditorTextureUtil.GetStorageMemorySize(texture));
            }

            guids = AssetDatabase.FindAssets("t:Prefab");
            if (guids == null)
            {
                return false;
            }

            //搜索依赖
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                string[] dependencies = AssetDatabase.GetDependencies(path);
                foreach (string dependency in dependencies)
                {
                    if (textures.ContainsKey(dependency))
                    {
                        textures[dependency].Add(path);
                    }
                }
            }

            //优化特效图片大小
            HeroResPreview window = EditorWindow.GetWindow(typeof(HeroResPreview)) as HeroResPreview;
            AssetDatabase.StartAssetEditing();
            try
            {
                HeroResOptimal.DoAssetOptimal(new List<string>(textures.Keys), window.GetOptimalOption());
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            //筛选AB组
            SortedDictionary<string, List<string>> groups = new SortedDictionary<string, List<string>>();
            foreach (var texture in textures)
            {
                long texMemory = textureMemorys[texture.Key];
                //小于2个引用或者冗余内存小于100KB的忽略
                if (texture.Value.Count == 0)
                {
                    nones.Add(texture.Key);
                    continue;
                }
                else if (texture.Value.Count == 1)
                {
                    alones.Add(texture.Key);
                    continue;
                }
                else if (texture.Value.Count * texMemory < FILTER_THRESHOLD)
                {
                    smalls.Add(texture.Key);
                    continue;
                }

                texture.Value.Sort();
                string groupName = string.Empty;
                for (int i = 0; i < texture.Value.Count; i++)
                {
                    if (i > 0)
                    {
                        groupName += "_";
                    }

                    groupName += Path.GetFileNameWithoutExtension(texture.Value[i]);
                }

                List<string> group;
                if (!groups.TryGetValue(groupName, out group))
                {
                    group = new List<string>();
                    groups.Add(groupName, group);
                }

                group.Add(texture.Key);
            }

            //合并AB组
            List<string> mergeTextures = null;
            long mergeMemory = 0;
            foreach (var group in groups)
            {
                if (group.Value.Count > 1)
                {
                    long totalMemory = 0;
                    foreach (string texture in group.Value)
                    {
                        totalMemory += textureMemorys[texture] * textures[texture].Count;
                    }

                    if (totalMemory >= FILTER_THRESHOLD)
                    {
                        string abName = GetGroupABName(group.Value);
                        sameGroups.Add(abName, new List<string>(group.Value));
                        continue;
                    }
                }

                string texPath = group.Value[0];
                // long texMemory = textureMemorys[texPath];

                //将小于100KB的图片合并到一起
                // if (texMemory < FILTER_THRESHOLD)
                // {
                //     if (mergeTextures == null)
                //     {
                //         mergeTextures = group.Value;
                //     }
                //     else
                //     {
                //         mergeTextures.Add(texPath);
                //     }
                //
                //     mergeMemory += texMemory;
                // }
                // else
                {
                    //大于100KB的图片单独设置AB
                    string abName = AssetStatisticsUtility.GetABName(texPath, Path.GetExtension(texPath));
                    textureAbNames.Add(texPath, abName);
                    pngs.Add(texPath, abName);
                }

                // if (mergeTextures != null && mergeMemory >= FILTER_THRESHOLD)
                // {
                //     string abName = GetGroupABName(mergeTextures);
                //     diffGroups.Add(abName, new List<string>(mergeTextures));
                //     mergeTextures = null;
                //     mergeMemory = 0;
                // }
            }

            // if (mergeTextures != null)
            // {
            //     string abName = GetGroupABName(mergeTextures);
            //     diffGroups.Add(abName, new List<string>(mergeTextures));
            // }

            //设置组AB
            foreach (var group in groups)
            {
                if (group.Value.Count < 2 && group.Value != mergeTextures)
                {
                    continue;
                }

                string abName = GetGroupABName(group.Value);
                foreach (string texture in group.Value)
                {
                    textureAbNames.Add(texture, abName);
                }
            }

            //修改ABName
            bool isDirty = false;
            foreach (var texture in textures)
            {
                string path = texture.Key;
                AssetImporter importer = AssetImporter.GetAtPath(path);
                string oldAbName = importer.assetBundleName;
                string newAbName = string.Empty;
                textureAbNames.TryGetValue(path, out newAbName);

                if (newAbName != oldAbName && !(string.IsNullOrEmpty(newAbName) && string.IsNullOrEmpty(oldAbName)))
                {
                    importer.assetBundleName = newAbName;
                    isDirty = true;
                    Debug.Log($"{path} Change ABName:\n{oldAbName} -> {newAbName}");
                }
            }

            //统计AB信息
            StringBuilder statistics = new StringBuilder("ABName,包含资源,资源依赖,总内存(KB),资源状态\n");
            foreach (var ab in sameGroups)
            {
                long totalMemory = 0;
                StringBuilder dependencies = new StringBuilder();
                StringBuilder include = new StringBuilder();

                foreach (var texture in ab.Value)
                {
                    totalMemory += textureMemorys[texture];
                    include.AppendLine(texture);
                    dependencies.AppendLine($"{texture}:");
                    foreach (string dependency in textures[texture])
                    {
                        dependencies.AppendLine(dependency);
                    }
                }

                statistics.AppendLine(
                    $"{ab.Key},\"{include}\",\"{dependencies}\",{AssetStatisticsUtility.ByteToKB(totalMemory, true)},捆绑图集");
            }

            // foreach (var ab in diffGroups)
            // {
            //     long totalMemory = 0;
            //     StringBuilder dependencies = new StringBuilder();
            //     StringBuilder include = new StringBuilder();
            //
            //     foreach (var texture in ab.Value)
            //     {
            //         totalMemory += textureMemorys[texture];
            //         include.AppendLine(texture);
            //         dependencies.AppendLine($"{texture}:");
            //         foreach (string dependency in textures[texture])
            //         {
            //             dependencies.AppendLine(dependency);
            //         }
            //     }
            //
            //     statistics.AppendLine($"{ab.Key},\"{include}\",\"{dependencies}\",{AssetStatisticsUtility.ByteToKB(totalMemory, true)},合并小图集");
            // }

            foreach (var texture in pngs)
            {
                long memory = textureMemorys[texture.Key];
                StringBuilder dependencies = new StringBuilder();

                foreach (string dependency in textures[texture.Key])
                {
                    dependencies.AppendLine(dependency);
                }

                statistics.AppendLine(
                    $"{texture.Value},\"{texture.Key}\",\"{dependencies}\",{AssetStatisticsUtility.ByteToKB(memory, true)},单图");
            }

            foreach (string texture in smalls)
            {
                long totalMemory = textureMemorys[texture] * textures[texture].Count;
                StringBuilder dependencies = new StringBuilder();

                foreach (string dependency in textures[texture])
                {
                    dependencies.AppendLine(dependency);
                }

                statistics.AppendLine(
                    $",\"{texture}\",\"{dependencies}\",{AssetStatisticsUtility.ByteToKB(totalMemory, true)},冗余小于{AssetStatisticsUtility.ByteToKB(FILTER_THRESHOLD)}");
            }

            foreach (string texture in alones)
            {
                long totalMemory = textureMemorys[texture];
                StringBuilder dependencies = new StringBuilder();

                foreach (string dependency in textures[texture])
                {
                    dependencies.AppendLine(dependency);
                }

                statistics.AppendLine(
                    $",\"{texture}\",\"{dependencies}\",{AssetStatisticsUtility.ByteToKB(totalMemory, true)},只有一个引用");
            }

            foreach (string texture in nones)
            {
                statistics.AppendLine($",\"{texture}\",,0,没有引用");
            }

            File.WriteAllText($"{outputDir}/TextureABCheck.csv", statistics.ToString(), Encoding.UTF8);

            if (isDirty)
            {
                AssetDatabase.SaveAssets();
            }

            Resources.UnloadUnusedAssets();
            AssetDatabase.RemoveUnusedAssetBundleNames();
            return true;
        }

        private static bool FixTextureSizeX4(TextureImporter textureImporter,
            TextureImporterPlatformSettings platformSettings,
            out int fixWidth, out int fixHeight)
        {
            int originalWidth;
            int originalHeight;
            AssetStatisticsUtility.GetSourceTextureWidthAndHeight(textureImporter, out originalWidth,
                out originalHeight);
            fixWidth = Mathf.RoundToInt(originalWidth / 4.0f) * 4;
            fixHeight = Mathf.RoundToInt(originalHeight / 4.0f) * 4;

            if (originalWidth != fixWidth || originalHeight != fixHeight)
            {
                //需要暂时关闭后处理脚本，否则加载原始图片会被压缩变糊
                TextureAssetPostprocessor.Disabled = true;

                TextureImporterSettings importerSettings = new TextureImporterSettings();
                TextureImporterSettings originalSettings = new TextureImporterSettings();
                textureImporter.ReadTextureSettings(importerSettings);
                importerSettings.CopyTo(originalSettings);
                importerSettings.npotScale = TextureImporterNPOTScale.None;
                importerSettings.filterMode = FilterMode.Point;
                importerSettings.readable = true;
                textureImporter.SetTextureSettings(importerSettings);

                TextureImporterPlatformSettings originalPlatformSettings = new TextureImporterPlatformSettings();
                platformSettings.CopyTo(originalPlatformSettings);
                platformSettings.format = TextureImporterFormat.RGBA32;
                platformSettings.maxTextureSize = 8192;
                platformSettings.overridden = true;
                platformSettings.textureCompression = TextureImporterCompression.Uncompressed;
                textureImporter.SetPlatformTextureSettings(platformSettings);

                textureImporter.SaveAndReimport();
                Texture2D originalTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(textureImporter.assetPath);
                Texture2D fixTexture = TexCache.Instance.CacheTex(fixWidth, fixHeight);
                EditorHelp.ApplyUnReadTex____(originalTexture, fixTexture);

                byte[] bytes = textureImporter.assetPath.ToLower().Contains(".jpg")
                    ? fixTexture.EncodeToJPG()
                    : fixTexture.EncodeToPNG();
                File.WriteAllBytes(textureImporter.assetPath, bytes);

                textureImporter.SetTextureSettings(originalSettings);
                textureImporter.SetPlatformTextureSettings(originalPlatformSettings);
                textureImporter.SaveAndReimport();

                TextureAssetPostprocessor.Disabled = false;
                return true;
            }

            return false;
        }

        [MenuItem("Assets/MiniGameTool/图片尺寸修复", false, 1)]
        public static void TextureSizeFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(TextureSizeFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool TextureSizeFixCommand(string[] args)
        {
            //过滤尺寸小于256的图片
            const int FILTER_THRESHOLD = 256;

            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/UI");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics =
                new StringBuilder("资源路径,修改前原始尺寸,修改后原始尺寸,修改前实际尺寸,修改后实际尺寸,修改前实际内存(KB),修改后实际内存(KB)\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

                if (importer == null)
                    continue;

                if (!string.IsNullOrEmpty(importer.assetBundleName)
                    && !string.IsNullOrEmpty(importer.spritePackingTag)
                    && importer.assetBundleName.EndsWith(".spritepack"))
                {
                    string atlasPath = $"Assets/UI/0SpriteAtlas/{importer.spritePackingTag}.spriteatlas";
                    SpriteAtlas atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
                    string name = Path.GetFileNameWithoutExtension(importer.assetPath);
                    Sprite sprite = atlas?.GetSprite(name);
                    if (sprite != null)
                    {
                        continue;
                    }
                }

                string platformString =
                    War.Base.BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
                TextureImporterPlatformSettings platformSettings = importer.GetPlatformTextureSettings(platformString);
                if (platformSettings.maxTextureSize < FILTER_THRESHOLD)
                {
                    continue;
                }

                Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
                long runtimeMemory = EditorTextureUtil.GetStorageMemorySize(texture);
                int runtimeWidth = texture.width;
                int runtimeHeight = texture.height;
                int width = Mathf.RoundToInt(runtimeWidth / 4.0f) * 4;
                int height = Mathf.RoundToInt(runtimeHeight / 4.0f) * 4;
                if (width == runtimeWidth && height == runtimeHeight)
                {
                    continue;
                }
                else if (width < FILTER_THRESHOLD && height < FILTER_THRESHOLD)
                {
                    continue;
                }
                else if (width < 32 || height < 32)
                {
                    continue;
                }

                int originalWidth;
                int originalHeight;
                int fixOriginalWidth;
                int fixOriginalHeight;
                AssetStatisticsUtility.GetSourceTextureWidthAndHeight(importer, out originalWidth, out originalHeight);
                if (FixTextureSizeX4(importer, platformSettings, out fixOriginalWidth, out fixOriginalHeight))
                {
                    texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
                    long fixRuntimeMemory = EditorTextureUtil.GetStorageMemorySize(texture);
                    int fixRuntimeWidth = texture.width;
                    int fixRuntimeHeight = texture.height;
                    statistics.AppendLine(
                        $"{importer.assetPath},{originalWidth}*{originalHeight},{fixOriginalWidth}*{fixOriginalHeight},{runtimeWidth}*{runtimeHeight},{fixRuntimeWidth}*{fixRuntimeHeight},{AssetStatisticsUtility.ByteToKB(runtimeMemory, true)},{AssetStatisticsUtility.ByteToKB(fixRuntimeMemory, true)}");
                }
            }

            File.WriteAllText($"{outputDir}/TextureSizeFix.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return true;
        }
        
        [MenuItem("Assets/MiniGameTool/图片Mipmap修复", false, 1)]
        public static void TextureMipmapFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(TextureMipmapFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool TextureMipmapFixCommand(string[] args)
        {
            //过滤尺寸小于100kb的图片
            const decimal FILTER_THRESHOLD = 1024 * 100;
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/UI");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,修改前实际内存(KB),修改后实际内存(KB)\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

                if (importer == null || !importer.mipmapEnabled)
                    continue;

                if (string.IsNullOrEmpty(importer.assetBundleName)
                    || !string.IsNullOrEmpty(importer.spritePackingTag)
                    || importer.assetBundleName.EndsWith(".spritepack"))
                {
                    continue;
                }

                Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
                long runtimeMemory = EditorTextureUtil.GetStorageMemorySize(texture);

                if (runtimeMemory < FILTER_THRESHOLD)
                {
                    continue;
                }
                
                importer.mipmapEnabled = false;
                importer.SaveAndReimport();
                
                texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
                long fixRuntimeMemory = EditorTextureUtil.GetStorageMemorySize(texture);

                statistics.AppendLine(
                    $"{importer.assetPath},{AssetStatisticsUtility.ByteToKB(runtimeMemory, true)},{AssetStatisticsUtility.ByteToKB(fixRuntimeMemory, true)}");
            }

            File.WriteAllText($"{outputDir}/TextureMipmapFix.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return true;
        }
    }
}