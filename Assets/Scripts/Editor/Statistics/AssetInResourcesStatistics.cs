#if UNITY_WEBGL
#define WX_LOD_OPTIMIZE
#endif

using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Tools/Resources/Resources下资源检测", false, 1)]
        public static void AssetInResourcesStatistics()
        {
            string output = EditorUtility.OpenFolderPanel("导出统计表格",Application.dataPath,"");

            try
            {
                ExecuteCommand(nameof(AssetInResourcesCommand), $"{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }
        static StringBuilder assetData;
        static List<string> orgainData;

        static string configCachePath = Path.Combine(Application.dataPath, "../assetInResourcesCache.txt");
        //获取当前在Resources下的资源
        private static void InitOrgainAssets() 
        {
            assetData = new StringBuilder();
            if (File.Exists(configCachePath))
            {
                orgainData = File.ReadAllLines(configCachePath).ToList();
            }
            else 
            {
                orgainData = new List<string>();
                Debug.LogError($"Confirm if the whitelist has been added in this directory:{configCachePath}");
            }
        }



        private static bool AssetInResourcesCommand(string[] args)
        {
            if (args.Length < 1)
            {
                throw new Exception("Invalid Arguments!");
            }

            string outputDir = args[0];

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }
            string[] resourcesPaths = Resources.LoadAll("").Select(obj => AssetDatabase.GetAssetPath(obj)).ToArray().ToArray();
            bool success = true;
            if (resourcesPaths.Length > 0) 
            {
                success = false;
            }
            InitOrgainAssets();
            Dictionary<string, string> projectAsset = new Dictionary<string, string>();
            Dictionary<string, string> casualgameAsset = new Dictionary<string, string>();

            foreach (string log in resourcesPaths)
            {
                if (!orgainData.Contains(log))
                {
                    bool isminiGame = log.Contains("Assets/CasualGame");
                    bool isEditor = log.Contains("Editor");

                    if (!isminiGame)
                    {
                        //截取到resource这一层
                        var result = log.Substring(0, log.IndexOf("Resources") + "Resources".Length);
                        if (!projectAsset.ContainsKey(result)) 
                        {
                            if (!isEditor) 
                            {
                                projectAsset.Add(result,log);
                            }
                        }
                    }
                    else 
                    {
                        //截取到resource这一层
                        var result = log.Substring(0, log.IndexOf("Resources") + "Resources".Length);
                        if (!casualgameAsset.ContainsKey(result))
                        {
                            if (!isEditor)
                            {
                                casualgameAsset.Add(result, log);
                            }
                        }
                    }
                }
            }
            if (projectAsset.Count > 0) 
            {
                assetData.AppendLine("项目内新增的Resources资源:");
            }
            foreach (var log in projectAsset)
            {
                assetData.AppendLine(log.Value);
            }
            if (casualgameAsset.Count > 0)
            {
                assetData.AppendLine("小游戏内新增的Resources资源:");
            }
            foreach (var log in casualgameAsset) 
            {
                assetData.AppendLine(log.Value);
            }
            if (assetData.Length > 0)
            {
                success = false;
                File.WriteAllText($"{outputDir}/assetInResources.csv", assetData.ToString(), Encoding.UTF8);

            }
            else 
            {
                success = true;
            }
            if (!success)
            {
                Debug.LogError("The dependency file has been output to the assetInResources.csv, Please open modify!");
            }
            return success;
        }
    }
}