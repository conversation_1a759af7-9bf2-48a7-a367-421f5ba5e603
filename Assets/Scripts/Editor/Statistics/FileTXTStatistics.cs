using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Newtonsoft.Json.Linq;
using UnityEngine;
using UnityEditor;
using Object = UnityEngine.Object;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : UnityEditor.Editor
    {
        [MenuItem("AssetBundles/统计分析files.txt", false, 1)]
        public static void StatisticsABFiles()
        {
            string input = EditorUtility.OpenFilePanel("选择Files.txt", Application.dataPath, "txt");
            string inputDir = Path.GetDirectoryName(input);
            string manifest = EditorUtility.OpenFilePanel("选择Manifest(可选)", inputDir, "");
            string output = EditorUtility.OpenFolderPanel("选择导出文件夹", inputDir, "");

            try
            {
                ExecuteCommand(nameof(StatisticsABFilesCommand), $"{input},{output},false,{manifest}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool StatisticsABFilesCommand(string[] args)
        {
            if (args.Length < 3)
            {
                throw new Exception("Invalid Arguments!");
            }

            //统计目录深度
            const int DIR_DEPTH = 3;
            //统计过滤目录
            const string FILTER_DIR = "casualgame";

            string input = args[0];
            string outputDir = args[1];
            bool isArchive = bool.Parse(args[2]);
            string manifest = null;
            if (args.Length > 3)
            {
                manifest = args[3];
            }

            if (input.Contains("$Default"))
            {
                input = input.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (manifest.Contains("$Default"))
            {
                manifest = manifest.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (!File.Exists(input))
            {
                throw new Exception("ABFiles.txt does not exist!");
            }
            else if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] assetbundles = null;
            var filesContent = File.ReadAllText(input);
            var filesDic = UIHelper.ToObj<Dictionary<string, object>>(filesContent);

            decimal totalSize = 0;
            Dictionary<string, List<decimal>> fileTypeTotalSize = new Dictionary<string, List<decimal>>();

            Dictionary<string, List<decimal>> directoryTotalSize = new Dictionary<string, List<decimal>>();
            Dictionary<string, decimal> fileInfoSize = new Dictionary<string, decimal>();

            if (!string.IsNullOrEmpty(manifest) && File.Exists(manifest))
            {
                AssetBundle ab = AssetBundle.LoadFromFile(manifest);
                AssetBundleManifest abManifest = ab?.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
                assetbundles = abManifest?.GetAllAssetBundles();
                ab?.Unload(true);
            }

            if (!filesDic.ContainsKey("list"))
            {
                throw new Exception("File format exception!");
            }
            
            foreach (var fileInfo in (JObject)filesDic["list"])
            {
                string assetPath = fileInfo.Key;
                decimal assetSize = decimal.Parse((string)fileInfo.Value[2]);

                if (assetbundles != null && !assetbundles.Contains(assetPath))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(FILTER_DIR) && assetPath.Contains(FILTER_DIR))
                {
                    continue;
                }

                List<decimal> fileTypeInfo;
                string fileType = Path.GetExtension(assetPath);
                if (!fileTypeTotalSize.TryGetValue(fileType, out fileTypeInfo))
                {
                    fileTypeInfo = new List<decimal>() { 0, 0 };
                    fileTypeTotalSize.Add(fileType, fileTypeInfo);
                }

                string directory = "";
                string[] subDirectorys = Path.GetDirectoryName(assetPath).Split('\\');
                for (int i = 0; i < Mathf.Min(DIR_DEPTH, subDirectorys.Length); i++)
                {
                    if (i > 0) directory += '/';
                    directory += subDirectorys[i];
                    List<decimal> directoryInfo;
                    if (!directoryTotalSize.TryGetValue(directory, out directoryInfo))
                    {
                        directoryInfo = new List<decimal>() { 0, 0 };
                        directoryTotalSize.Add(directory, directoryInfo);
                    }

                    directoryInfo[0] += assetSize;
                }

                fileInfoSize[assetPath] = assetSize;

                fileTypeInfo[0] += assetSize;
                totalSize += assetSize;
            }

            List<decimal> otherFileTypeSize = new List<decimal>() { 0, 0 };
            foreach (var fileTypeInfo in fileTypeTotalSize)
            {
                fileTypeInfo.Value[1] = (fileTypeInfo.Value[0] / totalSize) * 100;

                if (string.IsNullOrEmpty(fileTypeInfo.Key) || fileTypeInfo.Value[1] < 1)
                {
                    otherFileTypeSize[0] += fileTypeInfo.Value[0];
                    otherFileTypeSize[1] = (otherFileTypeSize[0] / totalSize) * 100;
                }
            }

            List<decimal> otherDirectoryTotalSize = new List<decimal>() { 0, 0 };
            foreach (var directoryInfo in directoryTotalSize)
            {
                directoryInfo.Value[1] = directoryInfo.Value[0] / totalSize * 100;

                if (string.IsNullOrEmpty(directoryInfo.Key) || directoryInfo.Value[1] < 1)
                {
                    otherDirectoryTotalSize[0] += directoryInfo.Value[0];
                    otherDirectoryTotalSize[1] = (otherFileTypeSize[0] / totalSize) * 100;
                }
            }

            StringBuilder statistics = new StringBuilder();
            statistics.AppendLine($"资源总大小:{AssetStatisticsUtility.ByteToMB(totalSize)}");
            statistics.AppendLine("\n\n");
            statistics.AppendLine($"资源类型分布：");
            statistics.AppendLine($"资源类型,总大小,总占比");
            foreach (var fileTypeInfo in fileTypeTotalSize.OrderByDescending(weight => weight.Value[0]))
            {
                if (string.IsNullOrEmpty(fileTypeInfo.Key) || fileTypeInfo.Value[1] < 1)
                {
                    continue;
                }

                string fileSize = $"{AssetStatisticsUtility.ByteToMB(fileTypeInfo.Value[0])}";
                string fileRatio = $"{string.Format("{0:F2}", fileTypeInfo.Value[1])}%";
                statistics.AppendLine($"{fileTypeInfo.Key},{fileSize},{fileRatio}");
            }

            string otherFileSize = $"{AssetStatisticsUtility.ByteToMB(otherFileTypeSize[0])}";
            string otherFileRatio = $"{string.Format("{0:F2}", otherFileTypeSize[1])}%";
            statistics.AppendLine($"其他,{otherFileSize},{otherFileRatio}");
            statistics.AppendLine("\n\n");
            statistics.AppendLine($"资源目录分布：");
            statistics.AppendLine($"资源目录,总大小,总占比");
            foreach (var directoryInfo in directoryTotalSize.OrderBy(weight => weight.Key))
            {
                if (string.IsNullOrEmpty(directoryInfo.Key) || directoryInfo.Value[1] < 1)
                {
                    continue;
                }

                string directorySize = $"{AssetStatisticsUtility.ByteToMB(directoryInfo.Value[0])}";
                string directoryRatio = $"{string.Format("{0:F2}", directoryInfo.Value[1])}%";
                statistics.AppendLine($"{directoryInfo.Key},{directorySize},{directoryRatio}");
            }

            string otherDirectorySize = $"{AssetStatisticsUtility.ByteToMB(otherFileTypeSize[0])}";
            string otherDirectoryRatio = $"{string.Format("{0:F2}", otherFileTypeSize[1])}%";
            statistics.AppendLine($"其他,{otherDirectorySize},{otherDirectoryRatio}");
            statistics.AppendLine("\n\n");
            int beforeRank = 50;
            statistics.AppendLine($"资源大小排名前{beforeRank}：");
            statistics.AppendLine($"资源路径,文件大小");
            int rank = 0;
            foreach (var fileInfo in fileInfoSize.OrderByDescending(weight => weight.Value))
            {
                rank++;
                string fileSize = $"{AssetStatisticsUtility.ByteToMB(fileInfo.Value)}";
                statistics.AppendLine($"{fileInfo.Key},{fileSize}");

                if (rank >= beforeRank)
                {
                    break;
                }
            }

            File.WriteAllText($"{outputDir}/ABFilesStatistics.csv", statistics.ToString());

            if (isArchive)
            {
                string copyPath =
                    Path.Combine(AssetStatisticsUtility.GetDefaultOutput(CurrentVersion), Path.GetFileNameWithoutExtension(input) + ".profile");
                File.Copy(input, copyPath);
                if (!string.IsNullOrEmpty(manifest) && File.Exists(manifest))
                {
                    copyPath = Path.Combine(AssetStatisticsUtility.GetDefaultOutput(CurrentVersion), Path.GetFileName(manifest));
                    File.Copy(manifest, copyPath);
                }
            }

            return true;
        }

        [MenuItem("AssetBundles/对比分析files.txt差异", false, 1)]
        public static void CompareABFiles()
        {
            string srcPath = EditorUtility.OpenFilePanel("选择源Files.txt", Application.dataPath, "txt");
            string srcManifest = EditorUtility.OpenFilePanel("选择源Manifest(可选)", Path.GetDirectoryName(srcPath), "");
            string comparePath = EditorUtility.OpenFilePanel("选择对比Files.txt", Path.GetDirectoryName(srcPath), "txt");
            string compareManifest =
                EditorUtility.OpenFilePanel("选择对比Manifest(可选)", Path.GetDirectoryName(comparePath), "");
            string inputDir = Path.GetDirectoryName(comparePath);
            string output = EditorUtility.OpenFolderPanel("选择导出文件夹", inputDir, "");

            ExecuteCommand(nameof(CompareABFilesCommand),
                $"{srcPath},{comparePath},{output},{srcManifest},{compareManifest}");
        }

        private static bool CompareABFilesCommand(string[] args)
        {
            if (args.Length < 3)
            {
                throw new Exception("Invalid Arguments!");
            }

            //过滤小于10KB的统计
            const decimal FILTER_THRESHOLD = 1024 * 10;
            //过滤文件类型
            const string FILTER_TYPE = "";
            //统计过滤目录
            const string FILTER_DIR = "casualgame";

            string srcPath = args[0];
            string comparePath = args[1];
            string outputDir = args[2];
            string srcManifest = null;
            string compareManifest = null;

            if (args.Length > 3)
            {
                srcManifest = args[3];
            }

            if (args.Length > 4)
            {
                compareManifest = args[4];
            }

            if (srcPath.Contains("$Default"))
            {
                srcPath = srcPath.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (comparePath.Contains("$Default"))
            {
                int prevVersion = AssetStatisticsUtility.GetLastVersion(CurrentVersion);
                comparePath = comparePath.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(prevVersion));

                //如果没有上个版本资源清单直接跳过
                if (File.Exists(srcPath) && !File.Exists(comparePath))
                {
                    return true;
                }
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (srcManifest.Contains("$Default"))
            {
                srcManifest = srcManifest.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (compareManifest.Contains("$Default"))
            {
                compareManifest = compareManifest.Replace("$Default", AssetStatisticsUtility.GetAssetBundlesRoot());
            }

            if (!File.Exists(srcPath))
            {
                throw new Exception("Source ABFiles.txt does not exist!");
            }
            else if (!File.Exists(comparePath))
            {
                throw new Exception("Compare ABFiles.txt does not exist!");
            }
            else if (!Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            var srcFilesContent = File.ReadAllText(srcPath);
            var srcFilesDic = UIHelper.ToObj<Dictionary<string, object>>(srcFilesContent);
            var compareFilesContent = File.ReadAllText(comparePath);
            var compareFilesDic = UIHelper.ToObj<Dictionary<string, object>>(compareFilesContent);
            string[] srcAssetBundles = null;
            string[] compareAssetBundles = null;
            Dictionary<string, decimal> srcFileInfoSize = new Dictionary<string, decimal>();
            Dictionary<string, decimal> compareFileInfoSize = new Dictionary<string, decimal>();

            if (!srcFilesDic.ContainsKey("list") || !compareFilesDic.ContainsKey("list"))
            {
                throw new Exception("File format exception!");
            }

            if (!string.IsNullOrEmpty(srcManifest) && File.Exists(srcManifest))
            {
                AssetBundle ab = AssetBundle.LoadFromFile(srcManifest);
                AssetBundleManifest abManifest = ab?.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
                srcAssetBundles = abManifest?.GetAllAssetBundles();
                ab?.Unload(true);
            }

            if (!string.IsNullOrEmpty(compareManifest) && File.Exists(compareManifest))
            {
                AssetBundle ab = AssetBundle.LoadFromFile(compareManifest);
                AssetBundleManifest abManifest = ab?.LoadAsset<AssetBundleManifest>("AssetBundleManifest");
                compareAssetBundles = abManifest?.GetAllAssetBundles();
                ab?.Unload(true);
            }

            StringBuilder statistics = new StringBuilder();
            Dictionary<string, decimal> greater = new Dictionary<string, decimal>();
            Dictionary<string, decimal> lesser = new Dictionary<string, decimal>();
            Dictionary<string, decimal> add = new Dictionary<string, decimal>();
            Dictionary<string, decimal> remove = new Dictionary<string, decimal>();
            decimal srcTotalSize = 0;
            decimal compareTotalSize = 0;
            foreach (var fileInfo in (JObject)srcFilesDic["list"])
            {
                string assetPath = fileInfo.Key;
                decimal assetSize = decimal.Parse((string)fileInfo.Value[2]);

                if (srcAssetBundles != null && !srcAssetBundles.Contains(assetPath))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(FILTER_DIR) && assetPath.Contains(FILTER_DIR))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(FILTER_TYPE) && !assetPath.EndsWith(FILTER_TYPE))
                {
                    continue;
                }

                srcTotalSize += assetSize;
                srcFileInfoSize[assetPath] = assetSize;
            }

            foreach (var fileInfo in (JObject)compareFilesDic["list"])
            {
                string assetPath = fileInfo.Key;
                decimal assetSize = decimal.Parse((string)fileInfo.Value[2]);

                if (compareAssetBundles != null && !compareAssetBundles.Contains(assetPath))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(FILTER_DIR) && assetPath.Contains(FILTER_DIR))
                {
                    continue;
                }

                if (!string.IsNullOrEmpty(FILTER_TYPE) && !assetPath.EndsWith(FILTER_TYPE))
                {
                    continue;
                }

                compareTotalSize += assetSize;
                compareFileInfoSize[assetPath] = assetSize;
            }

            foreach (var srcFileInfo in srcFileInfoSize)
            {
                decimal compareAssetSize;
                if (compareFileInfoSize.TryGetValue(srcFileInfo.Key, out compareAssetSize))
                {
                    if (compareAssetSize > srcFileInfo.Value)
                    {
                        greater.Add(srcFileInfo.Key, compareAssetSize - srcFileInfo.Value);
                    }
                    else
                    {
                        lesser.Add(srcFileInfo.Key, compareAssetSize - srcFileInfo.Value);
                    }
                }
                else
                {
                    remove.Add(srcFileInfo.Key, srcFileInfo.Value);
                }
            }

            foreach (var compareFileInfo in compareFileInfoSize)
            {
                if (!srcFileInfoSize.ContainsKey(compareFileInfo.Key))
                {
                    add.Add(compareFileInfo.Key, compareFileInfo.Value);
                }
            }

            statistics.AppendLine($"源文件总大小：{AssetStatisticsUtility.ByteToMB(srcTotalSize)}");
            statistics.AppendLine($"比较文件总大小：{AssetStatisticsUtility.ByteToMB(compareTotalSize)}");
            statistics.AppendLine($"对比大小差异：{AssetStatisticsUtility.ByteToMB(compareTotalSize - srcTotalSize)}");
            statistics.AppendLine($"对比数量差异：{compareFileInfoSize.Count - srcFileInfoSize.Count}");

            #region 统计新增文件总大小及数量
            decimal newFileSize = 0;
            int newFileCount = 0;
            foreach (var fileInfo in add.OrderByDescending(weight => weight.Value))
            {
                if (fileInfo.Value > FILTER_THRESHOLD)
                {
                    newFileSize += fileInfo.Value;
                    newFileCount += 1;
                }
            }

            if (newFileCount > 0) 
            {
                statistics.AppendLine($"新增文件总大小：{AssetStatisticsUtility.ByteToMB(newFileSize)}");
                statistics.AppendLine($"新增文件总数量：{newFileCount}");
            }
            #endregion

            #region 统计增大文件总大小及数量
            decimal newFileSize_to_big = 0;
            int newFileCount_to_big = 0;
            foreach (var fileInfo in greater.OrderByDescending(weight => weight.Value))
            {
                if (fileInfo.Value > FILTER_THRESHOLD)
                {
                    newFileSize_to_big += fileInfo.Value;
                    newFileCount_to_big += 1;
                }
            }

            if (newFileCount_to_big > 0)
            {
                statistics.AppendLine($"变大文件总大小：{AssetStatisticsUtility.ByteToMB(newFileSize_to_big)}");
                statistics.AppendLine($"变大文件总数量：{newFileCount_to_big}");
            }
            #endregion

            statistics.AppendLine($"文件变化：");
            statistics.AppendLine($",文件路径,变化类型,变化大小(KB)");
            foreach (var fileInfo in greater.OrderByDescending(weight => weight.Value))
            {
                if (fileInfo.Value > FILTER_THRESHOLD)
                {
                    statistics.AppendLine($",{fileInfo.Key},更大,{AssetStatisticsUtility.ByteToKB(fileInfo.Value, true)}");
                }
            }

            foreach (var fileInfo in lesser.OrderBy(weight => weight.Value))
            {
                if (fileInfo.Value < -FILTER_THRESHOLD)
                {
                    statistics.AppendLine($",{fileInfo.Key},更小,{AssetStatisticsUtility.ByteToKB(fileInfo.Value, true)}");
                }
            }

            foreach (var fileInfo in add.OrderByDescending(weight => weight.Value))
            {
                if (fileInfo.Value > FILTER_THRESHOLD)
                {
                    statistics.AppendLine($",{fileInfo.Key},新增,{AssetStatisticsUtility.ByteToKB(fileInfo.Value, true)}");
                }
            }

            foreach (var fileInfo in remove.OrderByDescending(weight => weight.Value))
            {
                if (fileInfo.Value > FILTER_THRESHOLD)
                {
                    statistics.AppendLine($",{fileInfo.Key},移除,{AssetStatisticsUtility.ByteToKB(-fileInfo.Value, true)}");
                }
            }

            File.WriteAllText($"{outputDir}/ABFilesCompare.csv", statistics.ToString());
            return true;
        }
    }
}