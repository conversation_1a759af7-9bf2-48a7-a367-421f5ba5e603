using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine.U2D;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/图集检查", false, 1)]
        public static void SpriteAtlasCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(SpriteAtlasCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool SpriteAtlasCheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/UI");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            
            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            Dictionary<string,string> bigTextures = new Dictionary<string,string>();
            Dictionary<string,string> mergeTextures = new Dictionary<string,string>();
            Dictionary<string,List<string>> missAtlases = new Dictionary<string,List<string>>();
            GetNoPackSprites(inputDir, bigTextures, mergeTextures, missAtlases);

            StringBuilder statistics = new StringBuilder("资源路径,图片尺寸,内存大小(KB),图集名称,处理方式\n");
            foreach (var texture in bigTextures)
            {
                string path = texture.Key;
                string spritePackingTag = texture.Value;
                Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                int width = sprite.texture.width;
                int height = sprite.texture.height;
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(sprite.texture);
                
                statistics.AppendLine(
                    $"{path},{width}*{height},{AssetStatisticsUtility.ByteToKB(memorySize, true)},{spritePackingTag},独立大图");
            }
            foreach (var texture in mergeTextures)
            {
                string path = texture.Key;
                string spritePackingTag = texture.Value;
                Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                int width = sprite.texture.width;
                int height = sprite.texture.height;
                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(sprite.texture);
                
                statistics.AppendLine(
                    $"{path},{width}*{height},{AssetStatisticsUtility.ByteToKB(memorySize, true)},{spritePackingTag},合入图集");
            }
            foreach (var atlas in missAtlases)
            {
                string spritePackingTag = atlas.Key;
                foreach (string path in atlas.Value)
                {
                    Sprite sprite = AssetDatabase.LoadAssetAtPath<Sprite>(path);
                    int width = sprite.texture.width;
                    int height = sprite.texture.height;
                    decimal memorySize = EditorTextureUtil.GetStorageMemorySize(sprite.texture);
                
                    statistics.AppendLine(
                        $"{path},{width}*{height},{AssetStatisticsUtility.ByteToKB(memorySize, true)},{spritePackingTag},缺少图集");
                }
            }

            File.WriteAllText($"{outputDir}/SpriteAtlasCheck.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return bigTextures.Count == 0 && mergeTextures.Count == 0 && missAtlases.Count == 0;
        }
        
        [MenuItem("Assets/MiniGameTool/图集修复", false, 1)]
        public static void SpriteAtlasFix()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(SpriteAtlasFixCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool SpriteAtlasFixCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/UI");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }
            
            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            Dictionary<string,string> bigTextures = new Dictionary<string,string>();
            Dictionary<string,string> mergeTextures = new Dictionary<string,string>();
            Dictionary<string,List<string>> missAtlases = new Dictionary<string,List<string>>();
            GetNoPackSprites(inputDir, bigTextures, mergeTextures, missAtlases);

            if (bigTextures.Count == 0 && mergeTextures.Count == 0)
            {
                return true;
            }

            StringBuilder statistics = new StringBuilder("资源路径,图集名称,处理方式\n");
            foreach (var texture in bigTextures)
            {
                string path = texture.Key;
                string spritePackingTag = texture.Value;
                string assetBundleName = AssetStatisticsUtility.GetABName(path, Path.GetExtension(path));
                TextureImporter assetImporter = AssetImporter.GetAtPath(path) as TextureImporter;
                // 添加NonPacktag 标签
                List<string> lables = new List<string>();
                lables.AddRange(AssetDatabase.GetLabels(assetImporter));
                if (lables.IndexOf("NonPacktag") == -1)
                {
                    lables.Add("NonPacktag");
                }

                AssetDatabase.SetLabels(assetImporter, lables.ToArray());
                //清空spritePackingTag
                assetImporter.spritePackingTag = "";
                //设置assetBundleName为path
                assetImporter.assetBundleName = assetBundleName;
                EditorUtility.SetDirty(assetImporter);
                assetImporter.SaveAndReimport();

                statistics.AppendLine($"{path},{spritePackingTag},独立大图");
            }

            foreach (var texture in mergeTextures)
            {
                string path = texture.Key;
                string spritePackingTag = texture.Value;
                string atlasPath = $"Assets/UI/0SpriteAtlas/{spritePackingTag}.spriteatlas";
                UnityEngine.Object[] sprites = AssetDatabase.LoadAllAssetRepresentationsAtPath(path);
                SpriteAtlas atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
                atlas.Add(sprites);
                EditorUtility.SetDirty(atlas);
                statistics.AppendLine($"{path},{spritePackingTag},合入图集");
            }
            
            foreach (var atlas in missAtlases)
            {
                CreateSpriteAtlas.CreateAtlasBySprites(atlas.Key, atlas.Value);
                foreach (var path in atlas.Value)
                {
                    statistics.AppendLine($"{path},{atlas.Key},生成图集");
                }
            }

            File.WriteAllText($"{outputDir}/SpriteAtlasFix.csv", statistics.ToString(), Encoding.UTF8);
            AssetDatabase.SaveAssets();
            Resources.UnloadUnusedAssets();
            
            //修改完后生成一遍图集
            SpriteAtlasUtility.PackAllAtlases(EditorUserBuildSettings.activeBuildTarget);
            return false;
        }

        private static void GetNoPackSprites(string directory, Dictionary<string,string> bigTextures,
            Dictionary<string,string> margeTextures, Dictionary<string, List<string>> missAtlases)
        {
            //超过256*256的图片算大图
            const int BIG_THRESHOLD = 256;
            //过滤32*32的图片
            const int FILTER_THRESHOLD = 32;
            
            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { directory });
            if (guids == null)
            {
                return;
            }
            
            //先生成一遍图集，避免后面获取不到
            SpriteAtlasUtility.PackAllAtlases(EditorUserBuildSettings.activeBuildTarget);
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter importer = AssetImporter.GetAtPath(path) as TextureImporter;

                if (importer == null || importer.textureType != TextureImporterType.Sprite)
                    continue;

                if (string.IsNullOrEmpty(importer.assetBundleName)
                    || string.IsNullOrEmpty(importer.spritePackingTag)
                    || !importer.assetBundleName.EndsWith(".spritepack"))
                    continue;

                string atlasPath = $"Assets/UI/0SpriteAtlas/{importer.spritePackingTag}.spriteatlas";
                SpriteAtlas atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasPath);
                UnityEngine.Object[] subSprites = AssetDatabase.LoadAllAssetRepresentationsAtPath(path);
                if (subSprites.Length == 0 || atlas?.GetSprite(subSprites[0].name) != null)
                {
                    continue;
                }

                Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
                int width = texture.width;
                int height = texture.height;
                if (width * height < FILTER_THRESHOLD * FILTER_THRESHOLD)
                {
                    continue;
                }
                else if (width * height > BIG_THRESHOLD * BIG_THRESHOLD)
                {
                    bigTextures.Add(importer.assetPath, importer.spritePackingTag);
                }
                else if (atlas == null)
                {
                    List<string> sprites;
                    if (!missAtlases.TryGetValue(importer.spritePackingTag, out sprites))
                    {
                        sprites = new List<string>();
                        missAtlases[importer.spritePackingTag] = sprites;
                    }
                    sprites.Add(importer.assetPath);
                }
                else
                {
                    margeTextures.Add(importer.assetPath, importer.spritePackingTag);
                }
            }

            //图集只有一个Sprite需要移除atlas
            List<string> removeAtlases = new List<string>();
            foreach (var atlase in missAtlases)
            {
                if (atlase.Value.Count == 1)
                {
                    removeAtlases.Add(atlase.Key);
                }
            }
            foreach (string name in removeAtlases)
            {
                missAtlases.Remove(name);
            }
        }
    }
}