using System;
using System.IO;
using System.Text;
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        private static string GetGameObjectPath(GameObject obj)
        {
            string path = "/" + obj.name;
            while (obj.transform.parent != null)
            {
                obj = obj.transform.parent.gameObject;
                path = "/" + obj.name + path;
            }

            return path;
        }

        [MenuItem("Assets/MiniGameTool/UI冗余检查", false, 1)]
        public static void UICheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(UICheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool UICheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];

            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/UI");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Prefab", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,节点路径,引用路径,图片内存(KB)\n");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                GameObject asset = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                RawImage[] images = asset.GetComponentsInChildren<RawImage>();
                foreach (RawImage image in images)
                {
                    if (image.texture != null)
                    {
                        string imagePath = AssetDatabase.GetAssetPath(image.texture);
                        if (!string.IsNullOrEmpty(imagePath))
                        {
                            TextureImporter imageImporter = AssetImporter.GetAtPath(imagePath) as TextureImporter;
                            if (imageImporter && imageImporter.textureType == TextureImporterType.Sprite
                                              && !imageImporter.assetBundleName.EndsWith(".spritepack"))
                            {
                                string childPath = GetGameObjectPath(image.gameObject);
                                decimal memorySize = EditorTextureUtil.GetStorageMemorySize(image.texture);
                                statistics.AppendLine($"{path},{childPath}" +
                                                      $",{imagePath},{AssetStatisticsUtility.ByteToKB(memorySize, true)}");
                            }
                        }
                    }
                }
            }

            File.WriteAllText($"{outputDir}/UICheck.csv", statistics.ToString(), Encoding.UTF8);
            return true;
        }
    }
}