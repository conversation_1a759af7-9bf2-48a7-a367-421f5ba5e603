using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEditor;

namespace StatisticsTools
{
    public partial class AssetStatisticsEditor : Editor
    {
        [MenuItem("Assets/MiniGameTool/角色冗余检查", false, 1)]
        public static void CharactersCheck()
        {
            string input = AssetStatisticsUtility.GetSelectionFolder();
            string output = EditorUtility.OpenFolderPanel("导出统计表格", input, "");

            try
            {
                ExecuteCommand(nameof(CharactersCheckCommand), $"{input},{output}");
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }
        }

        private static bool CharactersCheckCommand(string[] args)
        {
            if (args.Length < 2)
            {
                throw new Exception("Invalid Arguments!");
            }

            string inputDir = args[0];
            string outputDir = args[1];


            if (inputDir.Contains("$Default"))
            {
                inputDir = inputDir.Replace("$Default", "Assets/Animations");
            }

            if (outputDir.Contains("$Default"))
            {
                outputDir = outputDir.Replace("$Default", AssetStatisticsUtility.GetDefaultOutput(CurrentVersion));
            }

            if (!AssetDatabase.IsValidFolder(inputDir) || !Directory.Exists(outputDir))
            {
                throw new Exception("Invalid Directory!");
            }

            string[] guids = AssetDatabase.FindAssets("t:Texture", new string[] { inputDir });
            if (guids == null)
            {
                return false;
            }

            Dictionary<string, Texture> textures = new Dictionary<string, Texture>();
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (!string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                Texture texture;
                if (!textures.TryGetValue(path, out texture))
                {
                    texture = AssetDatabase.LoadAssetAtPath<Texture>(path);
                    textures.Add(path, texture);
                }
            }

            guids = AssetDatabase.FindAssets("t:Prefab");
            if (guids == null)
            {
                return false;
            }

            StringBuilder statistics = new StringBuilder("资源路径,冗余引用资源路径,冗余数量,冗余图片内存大小(KB)\n");
            Dictionary<string, List<string>> prefabs = new Dictionary<string, List<string>>();

            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AssetImporter importer = AssetImporter.GetAtPath(path);

                if (string.IsNullOrEmpty(importer.assetBundleName))
                    continue;

                string[] dependencies = AssetDatabase.GetDependencies(path);
                foreach (string dependency in dependencies)
                {
                    if (textures.ContainsKey(dependency))
                    {
                        List<string> files;
                        if (!prefabs.TryGetValue(path, out files))
                        {
                            files = new List<string>();
                            prefabs.Add(path, files);
                        }

                        files.Add(dependency);
                    }
                }
            }

            foreach (var prefab in prefabs.OrderByDescending(item => item.Value.Count))
            {
                string assetPath = prefab.Key;
                decimal totalMemorySize = 0;
                foreach (string texturePath in prefab.Value)
                {
                    Texture asset = textures[texturePath];
                    decimal memorySize = EditorTextureUtil.GetStorageMemorySize(asset);
                    totalMemorySize += memorySize;
                }

                StringBuilder includeFiles = new StringBuilder();
                foreach (string path in prefab.Value)
                {
                    includeFiles.AppendLine(path);
                }

                statistics.AppendLine(
                    $"{assetPath},\"{includeFiles}\",{prefab.Value.Count},{AssetStatisticsUtility.ByteToKB(totalMemorySize, true)}");
            }

            File.WriteAllText($"{outputDir}/CharactersCheck.csv", statistics.ToString(), Encoding.UTF8);
            Resources.UnloadUnusedAssets();
            return true;
        }
    }
}