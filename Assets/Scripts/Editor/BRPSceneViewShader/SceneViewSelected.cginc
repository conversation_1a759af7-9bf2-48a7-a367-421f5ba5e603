#ifndef DRAW_COLOR
#define DRAW_COLOR 1
#endif

#include "UnityCG.cginc"

sampler2D _MainTex;
float4 _MainTex_ST;
float _DoClip;
fixed _Cutoff;

struct appdata_t
{
    float4 vertex   : POSITION;
    float2 texcoord : TEXCOORD0;
};

struct v2f
{
    float4 vertex        : SV_POSITION;
    float2 texcoord      : TEXCOORD0;
};

v2f vert (appdata_t IN)
{
    v2f OUT;
    float4x4 localToWorld = unity_ObjectToWorld;
    localToWorld[3] = float4(0, 0, 0, 1);
    float4 worldPos = mul(localToWorld, float4(IN.vertex.xyz, 1.0));
    OUT.vertex = mul(UNITY_MATRIX_VP, worldPos);
    //OUT.vertex = UnityObjectToClipPos(IN.vertex);
    OUT.texcoord = TRANSFORM_TEX(IN.texcoord, _MainTex);
    return OUT;
}

fixed4 frag (v2f IN) : SV_Target
{
    if (_DoClip)
    {
        fixed4 col = tex2D( _MainTex, IN.texcoord);
        clip(col.a - _Cutoff);
    }
    return DRAW_COLOR;
}
