using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class ParticleDelayEditor : EditorWindow
{
    List<ParticleSystem> particlesInChildren = new List<ParticleSystem>();
    List<ParticleSystem> particlesInParent = new List<ParticleSystem>();

    bool withChildren = true;
    float value = 0.05f;

    // Add menu named "My Window" to the Window menu
    [MenuItem("Artist/Particle Delay Editor")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        ParticleDelayEditor window = (ParticleDelayEditor)EditorWindow.GetWindow(typeof(ParticleDelayEditor));
        window.Show();
    }

    private void OnEnable()
    {
        Selection.selectionChanged += OnSelectionChange;
        RefreshContent();
    }

    private void OnDisable()
    {
        Selection.selectionChanged -= OnSelectionChange;
    }

    private void OnGUI()
    {
        EditorGUILayout.BeginHorizontal();
        withChildren = EditorGUILayout.Toggle("With Children:", withChildren);
        EditorGUILayout.EndHorizontal();


        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("+0.1"))
        {
            SetDelayOffset(0.1f, withChildren);
        }
        if (GUILayout.Button("+0.01"))
        {
            SetDelayOffset(0.01f, withChildren);
        }

        if (GUILayout.Button("-0.1"))
        {
            SetDelayOffset(-0.1f, withChildren);
        }
        if (GUILayout.Button("-0.01"))
        {
            SetDelayOffset(-0.01f, withChildren);
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        value = EditorGUILayout.FloatField("Value:", value);
        GUILayout.FlexibleSpace();
        if (GUILayout.Button("set" , GUILayout.Width(70)))
        {
            SetDelay(value, withChildren);
        }

        if (GUILayout.Button("+", GUILayout.Width(70)))
        {
            SetDelayOffset(value, withChildren);
        }
        if (GUILayout.Button("-", GUILayout.Width(70)))
        {
            SetDelayOffset(-value, withChildren);
        }

        EditorGUILayout.EndHorizontal();
        EditorGUILayout.BeginVertical();

        GUILayout.Space(20);
        //DrawParticles(particlesInParent, Color.red);
        DrawParticles(particlesInChildren, Color.green);

        EditorGUILayout.EndVertical();
    }

    private void DrawParticles(List<ParticleSystem> particles, Color c)
    {
        foreach (ParticleSystem particle in particles)
        {
            ParticleSystem.MainModule main = particle.main;
            ParticleSystem.MinMaxCurve curve = main.startDelay;

            if (curve.mode == ParticleSystemCurveMode.Constant)
            {
                GUI.color = withChildren || Selection.Contains(particle.gameObject.GetInstanceID()) ? c : Color.white;
                EditorGUILayout.BeginHorizontal();
                GUILayout.Label(particle.name);
                GUILayout.FlexibleSpace();
                GUILayout.Label(curve.constant.ToString());
                EditorGUILayout.EndHorizontal();
                GUI.color = Color.white;
            }

            else
            {
                GUI.color = Color.red;
                GUILayout.Label(particle.name);
                GUILayout.FlexibleSpace();
                GUILayout.Label("Not Support");
                GUI.color = Color.white;
            }
        }
    }

    private void OnSelectionChange()
    {
        RefreshContent();
        Repaint();
    }

    private void RefreshContent()
    {
        Object[] objectList = Selection.objects;
        particlesInChildren.Clear();
        particlesInParent.Clear();
        foreach (Object obj in Selection.objects)
        {
            GameObject go = obj as GameObject;
            if (go)
            {
                particlesInChildren.AddRange(go.GetComponentsInChildren<ParticleSystem>());
                particlesInParent.AddRange(go.GetComponentsInParent<ParticleSystem>());
            }
        }
    }

    private void SetDelayOffset(float offset, bool withChildren)
    {
        Undo.RecordObjects(particlesInChildren.ToArray(), "SetDelayOffset");
        foreach (ParticleSystem particle in particlesInChildren)
        {
            if (withChildren == false)
            {
                if (Selection.Contains(particle.gameObject.GetInstanceID()) == false)
                    continue;
            }

            ParticleSystem.MainModule main = particle.main;
            ParticleSystem.MinMaxCurve curve = main.startDelay;

            if (curve.mode == ParticleSystemCurveMode.Constant)
            {
                curve.constant += offset;
            }

            main.startDelay = curve;
        }
    }

    private void SetDelay(float value, bool withChildren)
    {
        Undo.RecordObjects(particlesInChildren.ToArray(), "SetDelay");
        foreach (ParticleSystem particle in particlesInChildren)
        {
            if (withChildren == false)
            {
                if (Selection.Contains(particle.gameObject.GetInstanceID()) == false)
                    continue;
            }

            ParticleSystem.MainModule main = particle.main;
            ParticleSystem.MinMaxCurve curve = main.startDelay;

            if (curve.mode == ParticleSystemCurveMode.Constant)
            {
                curve.constant = value;
            }
            main.startDelay = curve;
        }
    }
}
