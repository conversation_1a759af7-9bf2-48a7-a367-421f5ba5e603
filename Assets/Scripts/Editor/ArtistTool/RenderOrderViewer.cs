using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.Battle;

class RenderOrderViewer : EditorWindow
{
    // Add menu named "My Window" to the Window menu
    [MenuItem("Artist/Render Viewer")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        RenderOrderViewer window = (RenderOrderViewer)EditorWindow.GetWindow(typeof(RenderOrderViewer));
        window.Show();
    }

    class RendererItem
    {
        public Component component;
        public int sortingLayer;
        public int sortingOrder;
        public Color color;

        public RendererItem (Component comp, int layer, int order, Color c)
        {
            component = comp;
            sortingLayer = layer;
            sortingOrder = order;
            color = c;
        }
    }

    List<RendererItem> infos = new List<RendererItem>();

    private void OnGUI()
    {
        if (GUILayout.Button("Refresh"))
        {
            RefreshContent();
            Repaint();
        }
        GUILayout.Space(20);
        DrawParticles();
    }

    private void DrawParticles()
    {
        float name_width = 0;
        float sortingLayer_width = 0;
        float sortingOrder_width = 0;
        float layer_width = 0;
        float component_width = 0;

        foreach (RendererItem info in infos)
        {
            name_width = Mathf.Max(name_width, GUI.skin.label.CalcSize(new GUIContent(info.component.name)).x);
            sortingLayer_width = Mathf.Max(sortingLayer_width, GUI.skin.label.CalcSize(new GUIContent(SortingLayer.IDToName(info.sortingLayer))).x);
            sortingOrder_width = Mathf.Max(sortingOrder_width, GUI.skin.label.CalcSize(new GUIContent(info.sortingOrder.ToString())).x);
            layer_width = Mathf.Max(layer_width, GUI.skin.label.CalcSize(new GUIContent(LayerMask.LayerToName(info.component.gameObject.layer))).x);
            component_width = Mathf.Max(component_width, GUI.skin.label.CalcSize(new GUIContent(info.component.GetType().Name)).x);
        }

        Color c = GUI.color;
        foreach (RendererItem info in infos)
        {
            GUILayout.BeginHorizontal();
            GUI.color = info.color;
            GUILayout.Label(info.component.name, GUILayout.Width(name_width + 10));
            GUILayout.Label(SortingLayer.IDToName(info.sortingLayer), GUILayout.Width(sortingLayer_width + 10));
            GUILayout.Label(info.sortingOrder.ToString(), GUILayout.Width(sortingOrder_width + 10));
            GUILayout.Label(LayerMask.LayerToName(info.component.gameObject.layer), GUILayout.Width(layer_width + 10));
            GUILayout.Label(info.component.GetType().Name, GUILayout.Width(component_width + 10));
            GUILayout.Label(info.component.GetComponent<Renderer>().GetType().Name);
            GUILayout.EndHorizontal();
        }
        GUI.color = c;
    }

    private void RefreshContent()
    {
        if (Selection.objects.Length != 0)
        {
            infos.Clear();
            GameObject go = Selection.objects[0] as GameObject;
            if (go)
            {
                Card.CollectContext context = new Card.CollectContext();
                context.Traverse(go);

                //foreach (Component smInstance in context.spriteMeshes)
                //{
                //    SpriteMeshInstance mesh = smInstance as SpriteMeshInstance;
                //    infos.Add(new RendererItem(mesh, mesh.sortingLayerID, mesh.sortingOrder, Color.green));
                //}

                foreach (Renderer particle in context.particles)
                {
                    infos.Add(new RendererItem(particle, particle.sortingLayerID, particle.sortingOrder, Color.red));
                }

                foreach (SpriteRenderer sprite in context.sprites)
                {
                    infos.Add(new RendererItem(sprite, sprite.sortingLayerID, sprite.sortingOrder, Color.cyan));
                }

                foreach (MeshRenderer mesh in context.meshes)
                {
                    infos.Add(new RendererItem(mesh, mesh.sortingLayerID, mesh.sortingOrder, Color.magenta));
                }
            }

            infos.Sort((left, right) => left.sortingLayer - right.sortingLayer == 0 ? left.sortingOrder - right.sortingOrder : left.sortingLayer - right.sortingLayer);
        
        }
    }

   
}