using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
/// <summary>
/// 截取部分图片用于RawImage显示
/// </summary>
public class ClipTextureEditor : EditorWindow
{
    static ClipTextureEditor _windowInstance;

    //用于裁剪的图片资源
    RawImage rawImage;
    Rect uvRect = new Rect(0, 0, 1, 1);
    Vector2 showSize = Vector2.one;

    //图片路径
    Rect imgPathRect;
    string imgPath;

    //图片信息保存路径
    Rect savePathRect;
    string saveRootPath = "Assets/Lua/res/model_img_region_res.txt";

    //ab资源节点映射到ab图片信息文件
    Rect abPathRect;
    string abResMapPath = "Assets/Lua/res/model_img_res.txt";

    readonly Dictionary<string, string> abImgMap = new Dictionary<string, string>();
    readonly Dictionary<string, Rect> clipRegionInfo = new Dictionary<string, Rect>();

    [MenuItem("Tools/Cip Texture")]
    static void Init()
    {
        if (_windowInstance == null)
        {
            _windowInstance = GetWindow(typeof(ClipTextureEditor), true, "裁剪图片") as ClipTextureEditor;
        }
    }

    void OnGUI()
    {
        OnGUIClipRawImage();
    }

    public void OnDestroy()
    {
        _windowInstance = null;
    }

    void OnGUIClipRawImage()
    {
        rawImage = EditorGUILayout.ObjectField("裁剪图片", rawImage, typeof(RawImage), true) as RawImage;
        uvRect = EditorGUILayout.RectField("UV Rect", uvRect);
        showSize = EditorGUILayout.Vector2Field("显示区域", showSize);

        if (rawImage == null)
        {
            return;
        }
        if(rawImage.uvRect.x != uvRect.x || rawImage.uvRect.y != uvRect.y || rawImage.uvRect.width != uvRect.width
            || rawImage.uvRect.height != uvRect.height)
        {
            Undo.RecordObject(rawImage, "Modify RawImage uvRect");
            rawImage.uvRect = uvRect;
        }

        //信息保存路径
        savePathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        saveRootPath = EditorGUI.TextField(savePathRect, "图片区域信息文件: ", saveRootPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && savePathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                saveRootPath = DragAndDrop.paths[0];
            }
        }

        //资源映射路径设置
        abPathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        abResMapPath = EditorGUI.TextField(abPathRect, "AB映射信息保存路径:", abResMapPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && abPathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                abResMapPath = DragAndDrop.paths[0];
            }
        }

        EditorGUILayout.BeginHorizontal();
        //渲染单个指定物体
        if (GUILayout.Button("计算区域", GUILayout.Width(100), GUILayout.Height(30)))
        {
            string assetPath = AssetDatabase.GetAssetPath(rawImage.texture);
            if(string.IsNullOrEmpty(assetPath))
            {
                return;
            }
            abImgMap.Clear();
            ParseABConfig(abImgMap);

            assetPath = assetPath.Replace("Assets/", "");
            assetPath = assetPath.ToLower();
            string prefabPath;
            if(!abImgMap.TryGetValue(assetPath, out prefabPath))
            {
                Debug.LogError("未能找到文件映射的prefab资源：" + assetPath);
                return;
            }
            var obj = AssetDatabase.LoadAssetAtPath("Assets/" + prefabPath, typeof(GameObject)) as GameObject;
            if (obj == null)
            {
                Debug.LogError("prefab资源不存在：" + prefabPath);
                return;
            }

            if(CalculateUVRect(rawImage, obj))
            {
                ReplaceRegionInfo(assetPath, uvRect);
            }

            RefreshScene();
            Resources.UnloadUnusedAssets();
            Debug.LogWarning("计算" + assetPath + "显示区域完成");
        }

        if(GUILayout.Button("保存区域", GUILayout.Width(100), GUILayout.Height(30)))
        {
            string assetPath = AssetDatabase.GetAssetPath(rawImage.texture);
            if (string.IsNullOrEmpty(assetPath))
            {
                return;
            }
            assetPath = assetPath.Replace("Assets/", "");
            assetPath = assetPath.ToLower();
            ReplaceRegionInfo(assetPath, uvRect);
            RefreshScene();
            Debug.LogWarning("保存" + assetPath + "显示区域完成");
        }
        EditorGUILayout.EndHorizontal();

        OnGUIBatchClipRawImage();
    }

    void OnGUIBatchClipRawImage()
    {
        //图片路径
        imgPathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        imgPath = EditorGUI.TextField(imgPathRect, "批处理图片根文件路径: ", imgPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && imgPathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                imgPath = DragAndDrop.paths[0];
            }
        }

        if (GUILayout.Button("批量计算区域", GUILayout.Width(100), GUILayout.Height(30)))
        {
            int calculateCount = 0;
            string prefabFolder = imgPath;
            if(!Directory.Exists(imgPath))
            {
                prefabFolder = Path.GetDirectoryName(imgPath);
            }
            string[] allPath = AssetDatabase.FindAssets("t:Texture", new string[] { prefabFolder });
            int textureCount = allPath.Length;
            abImgMap.Clear();
            ParseABConfig(abImgMap);
            clipRegionInfo.Clear();

            for (int i = 0; i < textureCount; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(Texture)) as Texture;
                if (obj == null)
                {
                    continue;
                }
                rawImage.texture = obj;

                string prefabPath;
                string assetPath = path.Replace("Assets/", "");
                assetPath = assetPath.ToLower();
                if (!abImgMap.TryGetValue(assetPath, out prefabPath))
                {
                    Debug.LogError("未能找到文件映射的prefab资源：" + assetPath);
                    continue;
                }
                var prefabObj = AssetDatabase.LoadAssetAtPath("Assets/" + prefabPath, typeof(GameObject)) as GameObject;
                if (obj == null)
                {
                    Debug.LogError("prefab资源不存在：" + prefabPath);
                    continue;
                }

                if (!CalculateUVRect(rawImage, prefabObj))
                {
                    continue;
                }

                clipRegionInfo[assetPath] = new Rect(uvRect.x, uvRect.y, uvRect.width, uvRect.height);

                EditorUtility.DisplayProgressBar("计算图片显示区域", path, (float)i / textureCount);
                calculateCount++;
            }
            EditorUtility.ClearProgressBar();
            BuildClipRegionConfig(clipRegionInfo);
            RefreshScene();
            Debug.LogWarning("计算完成，共处理" + calculateCount + "张图片");
        }
    }

    bool CalculateUVRect(RawImage rawImage, GameObject cardPrefab)
    {
        uvRect.width = showSize.x;
        uvRect.height = showSize.y;
        //计算图片与显示区域分辨率比值
        var texelSize = new Vector2(rawImage.texture.width, rawImage.texture.height);
        RectTransform rawSize = rawImage.GetComponent<RectTransform>();
        if (rawSize == null)
        {
            return false;
        }
        float rawRatio = rawSize.rect.height / rawSize.rect.width;
        float texelRatio = texelSize.y / texelSize.x;
        //修正显示区域
        if (rawRatio < texelRatio)
        {
            uvRect.width = showSize.x * texelRatio / rawRatio;
        }
        Vector2 realShowSize = new Vector2(uvRect.width, uvRect.height);

        War.Battle.Card card = cardPrefab.GetComponentInChildren<War.Battle.Card>();

        var cardConfig = card.GetComponent<War.Battle.CardConfig>();
        if (cardConfig == null)
        {
            //此卡牌未包含对齐框信息
            return false;
        }
        card.Initialize();
        //计算头位置的uv
        Vector2 anchorPos = cardConfig.anchor;
        uvRect.x = ((anchorPos.x - cardConfig.center.x) * 2 / cardConfig.size + 1) * 0.5f;
        uvRect.y = ((anchorPos.y - cardConfig.center.y) * 2 / cardConfig.size + 1) * 0.5f;

        //将头位置居中
        uvRect.x -= 0.5f * realShowSize.x;
        uvRect.y -= 0.5f * realShowSize.y;

        //计算右上角坐标
        Vector2 rightTopPos = new Vector2()
        {
            x = uvRect.x + realShowSize.x,
            y = uvRect.y + realShowSize.y,
        };
        //防止uv越界修正坐标
        if (uvRect.x < 0)
        {
            uvRect.x = 0;
        }
        if (rightTopPos.x > 1)
        {
            uvRect.x += 1 - realShowSize.x - uvRect.x;
        }
        if (rightTopPos.y > 1)
        {
            uvRect.y += 1 - realShowSize.y - uvRect.y;
        }
        return true;
    }

    public void ParseABConfig(Dictionary<string, string> abImgMap)
    {
        FileStream fs = File.Open(abResMapPath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
        StreamReader sr = new StreamReader(fs);
        string strContent = sr.ReadToEnd();
        sr.Close();
        fs.Close();

        var match = new Regex(@"config=(\n\{\n.*\})", RegexOptions.Singleline);
        Match matchInfo = match.Match(strContent);
        var strMap = matchInfo.Groups[1].Value;
        string [] strRecord = strMap.Split(',');
        string recordTmp;
        foreach(var strFileMap in strRecord)
        {
            recordTmp = strFileMap.Replace("{", "");
            recordTmp = recordTmp.Replace("}", "");
            recordTmp = recordTmp.Replace("[", "");
            recordTmp = recordTmp.Replace("]", "");
            recordTmp = recordTmp.Replace("\n", "");
            recordTmp = recordTmp.Replace("\"", "");
            recordTmp = recordTmp.Replace(" ", "");
            if (string.IsNullOrEmpty(recordTmp))
            {
                continue;
            }

            string []realMap = recordTmp.Split('=');
            if(realMap.Length != 2)
            {
                Debug.LogError("文件映射解析错误:" + recordTmp);
                continue;
            }
            abImgMap[realMap[1]] = realMap[0];
        }
    }

    public void BuildClipRegionConfig(Dictionary<string, Rect> clipRegionInfo)
    {
        FileStream fs = File.Open(saveRootPath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
        StreamReader sr = new StreamReader(fs);
        string strContent = sr.ReadToEnd();
        sr.Close();
        fs.Close();

        StringBuilder stringBuilder = new StringBuilder();
        foreach (var regionInfo in clipRegionInfo)
        {
            Rect rect = regionInfo.Value;
            stringBuilder.Append("[\"" + regionInfo.Key + "\"] = {x=" + rect.x);
            stringBuilder.Append(",y=" + rect.y);
            stringBuilder.Append(",width=" + rect.width);
            stringBuilder.Append(",height=" + rect.height + "},\n");
        }

        var match = new Regex(@"config=(\n\{\n.*\})", RegexOptions.Singleline);
        var fContent = match.Replace(strContent, "config=\n{\n" + stringBuilder + "\n}");
        File.WriteAllText(saveRootPath, fContent);
    }

    public void ReplaceRegionInfo(string key, Rect regionInfo)
    {
        clipRegionInfo.Clear();

        FileStream fs = File.Open(saveRootPath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
        StreamReader sr = new StreamReader(fs);
        string strContent = sr.ReadToEnd();
        sr.Close();
        fs.Close();

        var match = new Regex(@"config=(\n\{\n.*\})", RegexOptions.Singleline);
        Match matchInfo = match.Match(strContent);
        var strMap = matchInfo.Groups[1].Value;
        string[] separator = new string[] { "}," };
        string[] strRecord = strMap.Split(separator, System.StringSplitOptions.RemoveEmptyEntries);
        string recordTmp;
        foreach (var strFileMap in strRecord)
        {
            recordTmp = strFileMap.Replace("{", "");
            recordTmp = recordTmp.Replace("}", "");
            recordTmp = recordTmp.Replace("[", "");
            recordTmp = recordTmp.Replace("]", "");
            recordTmp = recordTmp.Replace("\n", "");
            recordTmp = recordTmp.Replace("\"", "");
            recordTmp = recordTmp.Replace(" ", "");
            if (string.IsNullOrEmpty(recordTmp))
            {
                continue;
            }

            var regionMatch = new Regex("(.*)=x=([0-9]{1,}[.]?[0-9]*),y=([0-9]{1,}[.]?[0-9]*),width=([0-9]{1,}[.]?[0-9]*),height=([0-9]{1,}[.]?[0-9]*)"
                , RegexOptions.Singleline);
            var matchRegion = regionMatch.Match(recordTmp);
            var groups = matchRegion.Groups;
            try
            {
                clipRegionInfo[groups[1].Value] = new Rect(ParseFloat(groups[2].Value), ParseFloat(groups[3].Value), ParseFloat(groups[4].Value), ParseFloat(groups[5].Value));
            }
            catch(System.Exception e)
            {
                Debug.LogError("record parse error:" + strFileMap + ",exception:" + e);
            }
        }

        clipRegionInfo[key] = regionInfo;

        BuildClipRegionConfig(clipRegionInfo);
    }

    void RefreshScene()
    {
        //EditorUtility.SetDirty(rawImage);
        //EditorUtility.SetDirty(rawImage.gameObject);
        //AssetDatabase.Refresh();
        UnityEngine.SceneManagement.Scene activeScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
        UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(activeScene);
        UnityEditor.SceneManagement.EditorSceneManager.SaveScene(activeScene);
    }

    float ParseFloat(string value)
    {
        float oValue;
        if (!float.TryParse(value, out oValue))
        {
            Debug.LogError("Parse float failed :" + value);
            throw new System.Exception();
        }
        return oValue;
    }
}
