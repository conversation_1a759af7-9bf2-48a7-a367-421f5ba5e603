using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.SceneManagement;
using System.Text.RegularExpressions;
using System.IO;
using System;
using System.Text;

public class RenderObjectEditor : EditorWindow
{
    static RenderObjectEditor _windowInstance;
    [Header("需要拍照的物体")]
    GameObject renderObject;
    [Header("摄像机")]
    Camera camera;
    [Header("图片大小")]
    Vector2 imgSize;
    [Header("裁剪区域")]
    Vector4 imgOffset;

    //节点对对齐区域
    MeshRenderer alignRenderer;
    //是否对齐Card
    bool bAlignCard = true;

    Rect pathRect;
    [Header("批量处理预制体路径")]
    string prefabPath;
    int containFilterCount = 0;
    readonly List<string> containFilter = new List<string>();

    [Header("动画进度")]
    float animationPercent = -1;

    Rect savePathRect;
    [Header("渲染图片保存路径")]
    string saveRootPath = "";
    //是否设置asset bundle name
    bool bSetABName = true;
    //是否剔除透明度
    bool bCullAlpha = false;
    //是否使用mipmap
    bool bGenerateMipmap = false;
    //non power of 2
    TextureImporterNPOTScale npotScale = TextureImporterNPOTScale.None;

    //ab资源节点映射到ab图片信息文件
    Rect abPathRect;
    [Header("Prefab映射信息保存路径")]
    string abResMapPath = "Assets/Lua/res/model_img_res.txt";

    string savePath = "";

    readonly Dictionary<string, string> abImgMap = new Dictionary<string, string>();

    [MenuItem("Tools/GameObjectRenderTool")]
    static void Init()
    {
        if (_windowInstance == null)
        {
            _windowInstance = GetWindow(typeof(RenderObjectEditor), true, "渲染物体到图片") as RenderObjectEditor;
        }
    }
    public void OnDestroy()
    {
        _windowInstance = null;
    }

    ///将RenderTexture保存为 png 图片
    bool SaveRenderTextureToPNG(RenderTexture rt, string filePath, string pngName)
    {
        RenderTexture prev = RenderTexture.active;
        RenderTexture.active = rt;
        Rect view = new Rect
        {
            x = rt.width * imgOffset.x,
            y = rt.height * imgOffset.y,
            width = rt.width * (1 - imgOffset.x - imgOffset.z),
            height = rt.height * (1 - imgOffset.y - imgOffset.w)
        };

        Texture2D png = new Texture2D((int)view.width, (int)view.height, TextureFormat.ARGB32, bGenerateMipmap);
        png.ReadPixels(new Rect((int)view.x, (int)view.y, (int)view.width, (int)view.height), 0, 0);
        if (bCullAlpha)
        {
            Color[] colors = png.GetPixels();
            for(int i = 0; i < colors.Length; i++)
            {
                colors[i].a = 1;
            }
            png.SetPixels(colors);
        }
        byte[] bytes = png.EncodeToPNG();

        if (!Directory.Exists(filePath))
        {
            Directory.CreateDirectory(filePath);
        }
        string savePath = filePath + "/" + pngName + ".png";
        FileStream file = File.Open(savePath, FileMode.Create);
        BinaryWriter writer = new BinaryWriter(file);
        writer.Write(bytes);
        writer.Close();
        file.Close();
        RenderTexture.active = prev;
        DestroyImmediate(png);

        return true;
    }

    void DestroyChildren(Transform rootNode)
    {
        int childCount = rootNode.childCount;
        for(int i = 0; i < childCount; i++)
        {
            DestroyImmediate(rootNode.GetChild(0).gameObject);
        }
    }

    string GetSaveRootPath()
    {
        if (string.IsNullOrEmpty(saveRootPath))
        {
            return Application.persistentDataPath + "/RenderImg/";
        }
        else
        {
            if(!saveRootPath.EndsWith("/"))
            {
                saveRootPath += "/";
            }
            return saveRootPath;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="fileName"></param>
    /// <returns>保存的文件绝对路径</returns>
    string RenderObject(string fileName)
    {
        PlayAnimation(renderObject);

        string rttName = "RenderObjectEditor_IMG";
        RenderTexture renderTexture = RenderObjectTool.Instance.RenderGameObject(renderObject, rttName, (int)imgSize.x, (int)imgSize.y, camera);
        if (string.IsNullOrEmpty(fileName))
        {
            fileName = DateTime.Now.Month + "_" + DateTime.Now.Day + "_" + DateTime.Now.Hour + "_" + DateTime.Now.Minute + "_" + DateTime.Now.Second;
        }
        string filePath = GetSaveRootPath();
        SaveRenderTextureToPNG(renderTexture, filePath, fileName);
        savePath = filePath + fileName + ".png";
        RenderObjectTool.Instance.ReleaseRenderTexture(rttName);
        return savePath;
    }

    public void BuildABConfig(Dictionary<string,string> abImgMap)
    {
        FileStream fs = File.Open(abResMapPath, FileMode.OpenOrCreate, FileAccess.ReadWrite);
        StreamReader sr = new StreamReader(fs);
        string strContent = sr.ReadToEnd();
        sr.Close();
        fs.Close();

        StringBuilder stringBuilder = new StringBuilder();
        foreach(var imgMap in abImgMap)
        {
            stringBuilder.Append("[\"" + imgMap.Key + "\"] = \"" + imgMap.Value + "\",\n");
        }

        var match = new Regex(@"config=(\n\{\n.*\})", RegexOptions.Singleline);
        var fContent = match.Replace(strContent, "config=\n{\n" + stringBuilder + "\n}");
        File.WriteAllText(abResMapPath, fContent);
    }

    void RenderObjectSetting()
    {
        renderObject = EditorGUILayout.ObjectField("渲染物体", renderObject, typeof(GameObject), true) as GameObject;
        camera = EditorGUILayout.ObjectField("摄像机", camera, typeof(Camera), true) as Camera;
        imgSize = EditorGUILayout.Vector2Field("图片大小", imgSize);
        imgOffset = EditorGUILayout.Vector4Field("图片裁剪区域[0~1]:", imgOffset);
        if (renderObject == null || camera == null || (int)imgSize.x <= 0 || (int)imgSize.y <= 0)
        {
            return;
        }
        if (imgOffset.x < 0 || imgOffset.x > 1 || imgOffset.y < 0 || imgOffset.y > 1 || imgOffset.y + imgOffset.w >= 1 || imgOffset.x + imgOffset.z >= 1)
        {
            Debug.LogError("裁剪区域无效");
            return;
        }
        alignRenderer = EditorGUILayout.ObjectField("对齐区域:", alignRenderer, typeof(MeshRenderer), true) as MeshRenderer;
        bAlignCard = EditorGUILayout.Toggle("是否对齐Card:", bAlignCard);
        animationPercent = EditorGUILayout.FloatField("动画进度:", animationPercent);

        //保存路径设置
        savePathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        saveRootPath = EditorGUI.TextField(savePathRect, "保存路径:", saveRootPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && savePathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                saveRootPath = DragAndDrop.paths[0];
            }
        }

        bSetABName = EditorGUILayout.Toggle("是否设置AB Name:", bSetABName);
        bCullAlpha = EditorGUILayout.Toggle("是否剔除图片透明度:", bCullAlpha);
        bGenerateMipmap = EditorGUILayout.Toggle("是否生成Mipmap:", bGenerateMipmap);
        npotScale = (TextureImporterNPOTScale)EditorGUILayout.EnumPopup("Non Power of 2", npotScale);

        //保存路径设置
        abPathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        abResMapPath = EditorGUI.TextField(abPathRect, "AB映射信息保存路径:", abResMapPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && abPathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                abResMapPath = DragAndDrop.paths[0];
            }
        }

        //渲染单个指定物体
        if (GUILayout.Button("渲染", GUILayout.Width(100), GUILayout.Height(30)))
        {
            abImgMap.Clear();
            if(!CheckValidRootNode())
            {
                return;
            }
            AlignNodeBound();
            string fileSavePath = RenderObject(null);
            if (bSetABName)
            {
                string abName = SetABNameByPath(fileSavePath);
                if(!string.IsNullOrEmpty(abName))
                {
                    UnityEngine.Object prefabParent = PrefabUtility.GetPrefabParent(renderObject);
                    if(prefabParent == null && renderObject.transform.childCount > 0)
                    {
                        prefabParent = PrefabUtility.GetPrefabParent(renderObject.transform.GetChild(0).gameObject);
                    }
                    if (prefabParent)
                    {
                        string assetPath = AssetDatabase.GetAssetPath(prefabParent);
                        abImgMap[assetPath] = abName.ToLower();
                        BuildABConfig(abImgMap);
                    }
                }
            }
            DealMipmapSetting(fileSavePath);
            DealNoPowerOf2(fileSavePath);
            AssetDatabase.SaveAssets();
        }
        BatchRenderGUI();

        EditorGUILayout.LabelField(savePath);
    }

    void AlignNodeBound()
    {
        if(alignRenderer == null || renderObject == null)
        {
            return;
        }

        AlignCard();
    }

    void AlignCard()
    {
        if(!bAlignCard)
        {
            return;
        }
        if(alignRenderer == null)
        {
            Debug.LogError("未设置卡牌对齐区域");
            return;
        }
        Bounds bounds = alignRenderer.bounds;

        War.Battle.Card[] cards = renderObject.GetComponentsInChildren<War.Battle.Card>();
        foreach(var card in cards)
        {
            var cardConfig = card.GetComponent<War.Battle.CardConfig>();
            if(cardConfig == null)
            {
                //此卡牌未包含对齐框信息
                continue;
            }
            card.Initialize();
            card.SetupTransform(bounds, renderObject.transform);
        }
    }

    string FullPathToAssetPath(string filePath)
    {
        string fullPath = Path.GetFullPath(filePath);
        fullPath = fullPath.Replace("\\", "/");

        if (fullPath.Contains(Application.dataPath))
        {
            return "Assets/" + fullPath.Replace(Application.dataPath + "/", "");
        }
        return "";
    }

    void DealMipmapSetting(string filePath)
    {
        string assetPath = FullPathToAssetPath(filePath);
        var assetImporter = GetAssetImport(assetPath) as TextureImporter;
        if (!assetImporter)
        {
            return;
        }
        assetImporter.mipmapEnabled = bGenerateMipmap;
    }

    void DealNoPowerOf2(string filePath)
    {
        string assetPath = FullPathToAssetPath(filePath);
        var assetImporter = GetAssetImport(assetPath) as TextureImporter;
        if (!assetImporter)
        {
            return;
        }
        assetImporter.npotScale = npotScale;
    }

    void BatchRenderGUI()
    {
        //批量处理设置
        //获得一个长300的框
        pathRect = EditorGUILayout.GetControlRect();
        //将上面的框作为文本输入框
        prefabPath = EditorGUI.TextField(pathRect, "批处理根文件路径: ", prefabPath);

        //如果鼠标正在拖拽中或拖拽结束时，并且鼠标所在位置在文本输入框内
        if ((Event.current.type == EventType.DragUpdated
          || Event.current.type == EventType.DragExited)
          && pathRect.Contains(Event.current.mousePosition))
        {
            //改变鼠标的外表
            DragAndDrop.visualMode = DragAndDropVisualMode.Generic;
            if (DragAndDrop.paths != null && DragAndDrop.paths.Length > 0)
            {
                prefabPath = DragAndDrop.paths[0];
            }
        }

        //文件过滤设置
        containFilterCount = EditorGUILayout.IntField("包含过滤字符串数量:", containFilterCount);
        if(containFilterCount != containFilter.Count)
        {
            int offsetCount = containFilterCount - containFilter.Count;
            if (offsetCount < 0)
            {
                containFilter.RemoveRange(containFilterCount, -offsetCount);
            }
            else
            {
                for(int i = 0; i < offsetCount; i++)
                {
                    containFilter.Add(string.Empty);
                }
            }
        }
        for (int i = 0; i < containFilterCount; i++)
        {
            pathRect = EditorGUILayout.GetControlRect();
            containFilter[i] = EditorGUI.TextField(pathRect, "过滤字符 " + i + ":", containFilter[i]);
        }

        if (GUILayout.Button("批量渲染", GUILayout.Width(100), GUILayout.Height(30)))
        {
            if (!CheckValidRootNode())
            {
                return;
            }

            int renderCount = 0;
            string prefabFolder = Path.GetDirectoryName(prefabPath);
            string[] allPath = AssetDatabase.FindAssets("t:Prefab", new string[] { prefabFolder });
            int prefabCount = allPath.Length;
            abImgMap.Clear();
            for (int i = 0; i < prefabCount; i++)
            {
                string path = AssetDatabase.GUIDToAssetPath(allPath[i]);
                if(!IsRefabValid(path))
                {
                    //不满足过滤文件
                    continue;
                }
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(GameObject)) as GameObject;
                if (obj == null)
                {
                    continue;
                }
                DestroyChildren(renderObject.transform);
                Instantiate(obj, renderObject.transform, false);
                AlignNodeBound();
                string fileName = Path.GetFileNameWithoutExtension(path);
                string fileSavePath = RenderObject(fileName);
                if (bSetABName)
                {
                    string abName = SetABNameByPath(fileSavePath);
                    if (!string.IsNullOrEmpty(abName))
                    {
                        var assetImporter = GetAssetImport(path);
                        if (assetImporter != null)
                        {
                            abImgMap[assetImporter.assetBundleName] = abName.ToLower();
                        }
                    }
                }
                DealMipmapSetting(fileSavePath);
                DealNoPowerOf2(fileSavePath);
                EditorUtility.DisplayProgressBar("渲染图片到文件", path, (float)i / prefabCount);
                renderCount++;
            }
            if(bSetABName)
            {
                BuildABConfig(abImgMap);
            }
            DestroyChildren(renderObject.transform);
            EditorUtility.ClearProgressBar();
            Debug.LogWarning("渲染完成，共渲染" + renderCount + "张图片");
            savePath = GetSaveRootPath();
            AssetDatabase.SaveAssets();
        }
    }

    void PlayAnimation(GameObject gameObject)
    {
        if(animationPercent <= 0)
        {
            return;
        }
        if(animationPercent > 1)
        {
            Debug.LogError("动画进度范围为[0 ~ 1]");
            return;
        }
        ParticleSystem[] particleSystems = gameObject.GetComponentsInChildren<ParticleSystem>();
        foreach(var particleSystem in particleSystems)
        {
            float duration = particleSystem.main.duration;
            particleSystem.Simulate(duration * animationPercent, false);
        }
        Animation[] animations = gameObject.GetComponentsInChildren<Animation>();
        foreach(var animation in animations)
        {
            var clip = animation.clip;
            if(clip == null)
            {
                continue;
            }
            var len = clip.length;
            clip.SampleAnimation(gameObject, len * animationPercent);
        }
        Animator[] animators = gameObject.GetComponentsInChildren<Animator>();
        foreach (var animator in animators)
        {
            if(!animator.gameObject.activeInHierarchy || !animator.isActiveAndEnabled)
            {
                continue;
            }
            var animatorCtr = animator.runtimeAnimatorController;
            if(animatorCtr == null)
            {
                continue;
            }
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            animator.Play(stateInfo.fullPathHash, -1, animationPercent);
        }
    }

    AssetImporter GetAssetImport(string assetPath)
    {
        var assetImporter = AssetImporter.GetAtPath(assetPath);
        if (assetImporter == null && File.Exists(assetPath))
        {
            string fullPath = Path.GetFullPath(assetPath);
            fullPath = fullPath.Replace("\\", "/");
            if (!fullPath.Contains(Application.dataPath))
            {
                //不在Unity 资源路径下
                return null;
            }

            //资源刚生成尚未导入
            AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
            assetImporter = AssetImporter.GetAtPath(assetPath);
        }
        return assetImporter;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns>AB Name</returns>
    string SetABNameByPath(string filePath)
    {
        string fullPath = Path.GetFullPath(filePath);
        fullPath = fullPath.Replace("\\", "/");
        if (!fullPath.Contains(Application.dataPath))
        {
            //不在Unity 资源路径下，不能设置 asset bundle
            return null;
        }
        if (File.Exists(fullPath) == false)
        {
            return null;
        }

        var assetPath = "Assets/" + fullPath.Replace(Application.dataPath + "/", "");
        var assetImporter = GetAssetImport(assetPath);
        string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
        assetImporter.assetBundleName = assetBundleName;
        return assetBundleName;
    }

    bool IsRefabValid(string filePath)
    {
        for(int i = 0; i < containFilterCount; i++)
        {
            string strContain = containFilter[i];
            if(string.IsNullOrEmpty(strContain) || !filePath.Contains(strContain))
            {
                return false;
            }
        }
        return true;
    }

    bool CheckValidRootNode()
    {
        if(renderObject == null)
        {
            Debug.LogError("渲染物体未设置");
            return false;
        }
        return true;
    }

    void OnGUI()
    {
        RenderObjectSetting();
    }
}
