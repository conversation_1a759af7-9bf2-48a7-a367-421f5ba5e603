using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class ModifyAB : Singleton<ModifyAB>
    {
        /// <summary>
        /// 根据其它项目已经打包的ab资源，拷贝到当前项目并出包
        /// </summary>
        /// <param name="abRoot"></param>
        public void Build(string abRoot)
        {

            /// 1,外网res版本
            /// 2,外网res版本对应svn版本打出的res版本
            /// 3,更到最新打出的res版本
            var choose_increase_ab_param = JenkinsEnv.Instance.Get("choose_increase_ab_param", "");//226,234,236
            string targetPatchMark=JenkinsEnv.Instance.Get("choose_increase_ab_target_patchmark", "");
            LogHelp.Instance.Log("ModifyAB build+");
            "".Print("!!!ModifyAB Build abRoot", abRoot);
            if(Directory.Exists(abRoot))
            {
                "".Print("!!!ModifyAB delete abRoot",abRoot);
                Directory.Delete(abRoot,true);
            }

            List<int> list = null;
            try
            {
                list = ToolUti.ToObj<List<int>>($"[{choose_increase_ab_param}]");
            }
            catch (System.Exception e)
            {
                "".Print("Error ModifyAB", e.ToString());
                return;
            }
            var oldRVer = list[0];
            var newRVer = list[1];
            int newFRVer = 0;
            if(list.Count>2)
             newFRVer = list[2];
            else
            {
                //如果只传两个参数,第三个参数默认使用当前最新的版本
                string patchmark = targetPatchMark;

                if (string.IsNullOrEmpty(patchmark))
                {
                    patchmark= JenkinsEnv.Instance.Get("patch_mark", "");

                }
                newFRVer = GetNewestVersionByPatchmark(patchmark);
            }
            
            var hcV1 = GetHashCheck(oldRVer);
            var hcV2 = GetHashCheckByPatchMark(newRVer,targetPatchMark);
            var hcV3 = GetHashCheckByPatchMark(newFRVer,targetPatchMark);

            if(
                hcV1==null
                ||hcV2==null
                ||hcV3== null
                )
            {
                "".Print("error GetHashCheck null:",hcV1,hcV2,hcV3);
                return;
            }
            
            
            
            //计算hc3跟hc2的差异部分,复制到本地
            var dic = new Dictionary<string,string[]>();
            foreach (var k in hcV3.list.Keys)
            {
                var v = hcV3.GetVer(k);
                if(int.TryParse(v,out var vv))
                {
                    if (vv > newRVer)
                    {
                        dic[k] = hcV3.list[k];
                    }
                }
                else
                {
                    "".Print("Error hcV3 parse int", k, v);
                }
            }
            "".Print("listNew", ToolUti.ToJson(dic));

            //除了差异的资源,其他资源如果是hcv1有的资源都使用hcv1的版本
            List<string> useV1List=new List<string>();
            var klist = new List<string>(hcV3.list.Keys);
            foreach (var k in klist)
            {
                //差异的资源还是使用hcv3的版本
                if(dic.ContainsKey(k))
                {
                    continue;
                }
                if(hcV1.list.TryGetValue(k, out var vv))
                {
                    hcV3.list[k] = vv;
                    useV1List.Add(k);
                }
                else
                {
                    //这里是hcv3里hcv1没有的资源,直接拷贝过来
                    "".Print("Warning hcV1 missing", k);
                }
            }

            "".Print("hcV3", ToolUti.ToJson(hcV3));
            buildAssetBundleRes(hcV3,abRoot,useV1List);

            LogHelp.Instance.Log("ModifyAB build-");
        }

        /// <summary>
        /// 根据PatchMark获取最新的reource版本
        /// </summary>
        /// <param name="patchMark"></param>
        /// <returns></returns>
        public  int GetNewestVersionByPatchmark(string patchMark)
        {
            var prefix = GetResUrlPrefix(patchMark);
            var updateJsonPath = prefix.Replace("/resource/", "/update.json");
            string targetFileContent = "";
            int resVersion = 0;
            "".Print("!!!ModifyAB GetNewestVersionByPatchmark", updateJsonPath, prefix);
            if (File.Exists(updateJsonPath))
            {
                targetFileContent = File.ReadAllText(updateJsonPath);
            }
            Dictionary<string, object> oldUpdateJsion = UIHelper.ToObj<Dictionary<string, object>>(targetFileContent);
            if (oldUpdateJsion.ContainsKey("files_url"))
            {
                var files_url = oldUpdateJsion["files_url"];

                Regex driverRegex = new Regex(@".+/resource/(\d+)/files.txt");
                var matches = driverRegex.Matches(files_url.ToString());
                if (matches.Count > 0)
                {
                    var match = matches[0];
                    resVersion = int.Parse(match.Groups[1].Value);

                    Debug.Log("update.json 读取resversion:" + resVersion + " match:" + match);
                }
            }
            return resVersion;
        }

        /// <summary>
        /// 根据最新的hashCheck生成ab资源列表
        /// </summary>
        /// <param name="hc"></param>
        /// <param name="abRoot"></param>
        public void buildAssetBundleRes(hashCheck hc,string abRoot,List<string>useV1List)
        {
            string v1patchmark=JenkinsEnv.Instance.Get("patch_mark", "");
            //v3-v2的差异资源从choose_increase_ab_target_patchmark复制
            string targetPatchMark=JenkinsEnv.Instance.Get("choose_increase_ab_target_patchmark", "");
            foreach (var k in hc.list.Keys)
            {
                
                var prefix = GetResUrlPrefix(targetPatchMark);
                //使用v1版本的资源从v1patchmark复制
                if (useV1List.Contains(k))
                {
                    prefix = GetResUrlPrefix(v1patchmark);
                }
                var ver = hc.GetVer(k);

                var p = prefix + ver + "/" + k;
                var t = abRoot + "/" + k;
                ToolUti.CheckDir(t);
                //"".Print("copy",p,t);
                try
                {
                    File.Copy(p, t, true);
                }
                catch (System.Exception e)
                {
                    "".Print("buildAssetBundleRes copy error", e.ToString());
                }
            }

            string file2Json = hc.ToJson();
            File.WriteAllText(abRoot + "/files2.txt", file2Json);
            var hcFile = hashCheck.Parse(file2Json,"none");// 去除所有res_key资源
            hcFile.key2mark = null;
            hcFile.mark2list = null;
            hcFile.langkey2mark = null;
            hcFile.langmark2list = null;

            File.WriteAllText(abRoot + "/files.txt", hcFile.ToJson());

            var hashDic = new Dictionary<string, string>();

            foreach (var k in hc.list.Keys)
            {
                var md5 = hc[k];
                var crc = hc.GetUnityCrc(k);
                hashDic[md5] = crc.ToString();
            }

            string md52crcPath = Application.dataPath + "/../md52crc.txt";
            File.WriteAllText(md52crcPath, ToolUti.ToJson(hashDic));
        }

        public hashCheck GetHashCheckByPatchMark(int ver,string patchMark="")
        {
            var resUrlPrefix = GetResUrlPrefix(patchMark);

            string folder = resUrlPrefix + ver;

            return GetHashCheckFromFolder(folder);
        }
        

        public hashCheck GetHashCheck(int ver)
        {
            var resUrlPrefix = GetResUrlPrefix();

            string folder = resUrlPrefix + ver;

            return GetHashCheckFromFolder(folder);
        }

        public hashCheck GetHashCheckFromFolder(string folder,bool isFiles1=false)
        {
            var filesUrl = folder + "/files2.txt";
            if (File.Exists(filesUrl) == false || isFiles1)
            {
                filesUrl = folder + "/files.txt";
            }
            if (File.Exists(filesUrl) == false)
            {
                "".Print("GetHashCheck files no exist", filesUrl);
                return null;
            }
            "".Print("hashcheck: "+folder);
            var content = File.ReadAllText(filesUrl);

            var hc = hashCheck.Parse(content);
            hc.name = filesUrl;
            return hc;
        }

        /// <summary>
        /// 该接口目前只针对获取StreamingAssets中ab文件家中files的读取解析
        /// </summary>
        /// <param name="folder"></param>
        /// <returns></returns>
        public hashCheck GetHashCheckForFilesTxtInPath(string folder)
        {
            var filesUrl = folder + "/files.txt";

            if (File.Exists(filesUrl) == false)
            {
                "".Print("GetHashCheck files no exist", filesUrl);
                return null;
            }
            var content = File.ReadAllText(filesUrl);

            var hc = hashCheck.Parse(content);
            hc.name = filesUrl;
            return hc;
        }

        public string GetResUrlPrefix(string targetPatchmark="")
        {
            var patchmark = JenkinsEnv.Instance.Get("patch_mark","Test");
            
            //如果有 choose_increase_ab_target_patchmark,则使用该值
            if(!string.IsNullOrEmpty(targetPatchmark))
            {
                patchmark=targetPatchmark;
                //"".Print("use choose_increase_ab_target_patchmark:",patchmark);
            }
            
            if(string.IsNullOrEmpty(patchmark))
            {
                "".Print("Error patch_mark",patchmark);
            }

            string patchDir = "../../Tools/Patch";
            string configNewDir = patchDir + "/update_all/config_new.json";


            string configNewContent = File.ReadAllText(configNewDir);
            var configNewDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string>>>(configNewContent);
            //if (configNewDic != null && configNewDic.ContainsKey(patchmark))

            var configJson = configNewDic[patchmark];
            var publicPatch = configJson["PublicPath"];
            publicPatch = win2linuxDir(publicPatch);
            var platform =BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            var resourceDir = publicPatch + "/" + platform + "/resource/";

            return resourceDir;
        }

        public string win2linuxDir(string p)
        {
            if (p.Contains("mnt")){
                p = p.Replace("/mnt/", "");
                p = p.Insert(1, ":");
            }

            return p;
        }

        public string GetResVerPath(int ver)
        {
            var resUrlPrefix = GetResUrlPrefix();

            var filesUrl = resUrlPrefix + ver + "/files.txt";

            return filesUrl;
        }
        /// <summary>
        /// 以genVer版本的files.txt作为当前资源列表，添加对应res文件夹，jenkins通过relative_res_ver，大于0时生成
        /// </summary>
        /// <param name="genVer"></param>
        public void GenSvnOldResFolder(int genVer)
        {
            var genPath = GetResVerPath(genVer);
            "".Print(genPath);

            if(File.Exists(genPath)==false)
            {
                "".PrintError("error GenSvnOldResFolder:", genPath);
                return;
            }
            var res_root = Path.GetDirectoryName(Path.GetDirectoryName(genPath));
            var files = Directory.GetDirectories(Path.GetFullPath(res_root), "*", SearchOption.TopDirectoryOnly);
            "".print(res_root, Path.GetFullPath(res_root), files.Length);

            var m = 0;
            foreach (var f in files)
            {
                var fn = Path.GetFileName(f);
                //"".print(fn);

                if(int.TryParse(fn,out var v))
                {
                    m = Mathf.Max(m, v);
                }
            }
            if(m==0)
            {
                "".PrintError("GenSvnOldResFolder error:",m);
                return;
            }
            m++;
            var nFolder = res_root + "/" + m;
            if (!Directory.Exists(nFolder))
            {
                Directory.CreateDirectory(nFolder);
            }
            File.Copy(genPath, nFolder + "/files.txt", true);

            var genPath2 = genPath.Replace("files.txt", "files2.txt");
            if(File.Exists(genPath2))
            {
                File.Copy(genPath2, nFolder + "/files2.txt", true);
            }
        } 
    }
}
