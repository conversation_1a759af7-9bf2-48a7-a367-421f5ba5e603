// #define UnityLocalTest

using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using UnityEditor.Sprites;
using War.Base;
using System.Linq;
using FtpTest1;
using ICSharpCode.SharpZipLib.Zip;
using CLeopardZip;
using System.Text;
using System.Text.RegularExpressions;

using War.Common;
using XLua.LuaDLL;
using Sirenix.OdinInspector.Demos;
using Random = System.Random;

namespace War.Base
{
    public class GarbleAssetBundlesPath
    {
        static List<string> needCheckEmptyFloderList = new List<string>();
        static List<string> checkedAbNameList = new List<string>();
        static int garbledSameAbNameNum = 0;//混淆之后可能出现同名文件（只有大小写区分算一同一文件，比如：Abc 与 AbC)

        [MenuItem("Assets/UI/GarbleAbPath")]
        /// 混淆包内 ab 资源路径
        public static void GarbleAbPath()
        {
            // try
            // {
                int abTotalNumber = 0;
                BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
                string outputFolder = BuildScript.GetPlatformFolderForAssetBundles(buildTarget);
                var androiddatapath = Path.Combine(Application.streamingAssetsPath, BuildScript.AssetBundlesOutputPath);
                var destination = System.IO.Path.Combine(androiddatapath, outputFolder);
                string outputPath = Path.Combine(BuildScript.AssetBundlesOutputPath,BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
                string filePath = Path.Combine(destination, "files.txt");
                Debug.Log("files.txt path = " + filePath);

                string filesTxtStr = File.ReadAllText(filePath);
                // var tempNewestPatchFileDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(filesTxtStr);
                    
                Dictionary<string, object> fileJson =  UIHelper.ToObj<Dictionary<string, object>>(filesTxtStr);
                Dictionary<string, string[]> needGarbleAbPathDic = UIHelper.ToObj<Dictionary<string,string[]>>(fileJson["list"].ToString());
                Dictionary<string, string> garbleABPathDic = new Dictionary<string, string>();

                var garbledABPathLogPath = Application.streamingAssetsPath + "/garbledABPathLog.txt";
                string[] filesArray = Directory.GetFiles(Application.streamingAssetsPath);
                string garbledABPathLogFileName = "";
                foreach (var item in filesArray)
                {
                    if(item.Contains("garbledABPathLog_") && item.Split('.').Length == 2)
                    {
                        garbledABPathLogFileName = item;
                        Debug.Log("zzd_________found garbledABPathLog. file " + garbledABPathLogFileName);
                        break;
                    }
                }
                if(File.Exists(garbledABPathLogFileName))
                {
                    File.Delete(garbledABPathLogFileName);
                    if(File.Exists(garbledABPathLogFileName))
                    {
                        Debug.Log("zzd______garbledABPathLog. file delete fail!!!");
                    }
                }
                // string[] checkEmptyFloderArray = Directory.GetDirectories(destination);
                // foreach(var item in checkEmptyFloderArray)
                // {
                //     Debug.Log("zzd_____________需要检测空文件夹的文件夹 ：" + item);
                // }
                // CheckEmptyFloder(destination,checkEmptyFloderArray);
                // return;

            // #if !UnityLocalTest
                #region IOS马甲包混淆
                string updateKey = "update.json";
                if (needGarbleAbPathDic.ContainsKey(updateKey) == false) 
                    needGarbleAbPathDic.Add(updateKey,null);
                
                string packageKey = "package.json";
                if (needGarbleAbPathDic.ContainsKey(packageKey) == false) 
                    needGarbleAbPathDic.Add(packageKey,null);
                #endregion
            // #endif
                
                GetGarbleWordsList();
                
                string garbleABPathStr = "";//混淆之后的AB name
                int needGarbleAbPathNum = needGarbleAbPathDic.Count *2;
                Debug.Log("needGarbleAbPathNum *2 = " + needGarbleAbPathNum);
                // List<string> needCheckEmptyFloderList = new List<string>();
                needCheckEmptyFloderList.Clear();
                checkedAbNameList.Clear();
                garbledSameAbNameNum = 0;
                if(File.Exists(garbledABPathLogPath))
                {
                    File.Delete(garbledABPathLogPath);
                }
                File.Create(garbledABPathLogPath).Dispose();
                foreach (var item in needGarbleAbPathDic)
                {
                    // if(item.Key.Contains('/'))
                    // {
                    //     Debug.Log("有文件夹");
                    //     string floderName = Path.Combine(destination,item.Key.Split('/')[0]);
                    //     if(!needCheckEmptyFloderList.Contains(floderName))
                    //     {
                    //         Debug.Log("添加需要检测空文件夹的文件夹 " + floderName);
                    //         needCheckEmptyFloderList.Add(floderName); 
                    //     }
                    // }

                    GetGarbleABPath2(destination,item.Key,out garbleABPathStr);
                    if(garbleABPathStr == "")
                    {
                        Debug.Log("asset bundle file not exist,check if .manifes file exist");
                    }
                    else
                    {
                        checkedAbNameList.Add(garbleABPathStr);
                        garbleABPathDic.Add(item.Key, garbleABPathStr);
                        abTotalNumber++;

                        StreamWriter sw;
                        FileInfo garbledABPathTxt = new FileInfo(garbledABPathLogPath);
                        sw = garbledABPathTxt.AppendText();
                        sw.WriteLine(item.Key + "|" + garbleABPathStr);
                        sw.Flush();
                        sw.Close();
                        sw.Dispose();
                    }

                    GetGarbleABPath2(destination,item.Key + ".manifest",out garbleABPathStr);
                    if(garbleABPathStr == "")
                    {
                        // Debug.Log("ab.manifest file garble fail, continue");
                        continue;
                    }
                    else
                    {
                        abTotalNumber++;
                    }
                    
                    bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildGarbleAbPath..", item.Key, (float)abTotalNumber / (float)needGarbleAbPathNum);
                    if (isCancel)
                    {
                        break;
                    }
                    // Debug.Log(" garble files number:" + abTotalNumber);
                }
                Debug.Log(" garbled same ab name number:" + garbledSameAbNameNum);
                if(abTotalNumber < needGarbleAbPathNum)
                {
                    Debug.LogError("Garble assetbundle number not correct");
                }
                // EditorUtility.ClearProgressBar();

                if(File.Exists(garbledABPathLogPath))
                {
                    string temp_goalPath = Path.Combine(Application.streamingAssetsPath,"garbledABPathLog_" + GetRandomString(3) + "." + GetRandomString(UnityEngine.Random.Range(3,10)));
                    FileUtil.MoveFileOrDirectory(garbledABPathLogPath, temp_goalPath);
                }

                var path = "Assets/Resources/garbleABPath.asset";
                FileUtil.DeleteFileOrDirectory(path);
                if (!System.IO.File.Exists(path))
                {
                    Debug.Log("zzd___________garbleABPath.asset not exist.  path = " + path);
                    EditorHelp.CheckDir(path);
                    var rk = ScriptableObject.CreateInstance<ABPathKey>();
                    rk.keys = new System.Collections.Generic.List<ABPathKey.KeyValuePair>();
                    UnityEditor.AssetDatabase.CreateAsset(rk, path);
                }
                if(File.Exists(Application.dataPath + "/Resources/garbleABPath.asset"))
                {
                    Debug.Log("zzd_____garbleABPath.asset__Exists____ " + Application.dataPath + "/Resources/garbleABPath.asset");
                }
                else
                {
                     Debug.Log("zzd_____garbleABPath.asset__not Exists____ " + Application.dataPath + "/Resources/garbleABPath.asset");
                }
                var rk1 = AssetDatabase.LoadAssetAtPath<ABPathKey>(path);
                //Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();
                rk1.keys.Clear();
                foreach (var item in garbleABPathDic)
                {
                    rk1.keys.Add(new ABPathKey.KeyValuePair()
                    {
                        key = item.Key,
                        value = item.Value
                    });

                    rk1.configs.Add(item.Key,item.Value);
                }
                int creatRubbishFileNum = 0;
                int creatRubbishFileMaxLimit = JenkinsEnv.Instance.GetInt("garble_ab_rubbish_num",0);
                if(File.Exists(Application.streamingAssetsPath + "/111.txt"))
                {
                    FileUtil.DeleteFileOrDirectory(Application.streamingAssetsPath + "/111.txt");
                }
                Debug.Log("zzd_______Start add rubbish files");
                while(creatRubbishFileNum < creatRubbishFileMaxLimit && garbleABPathDic.Count > 0)
                {
                    foreach (var item in garbleABPathDic)
                    {
                       bool isSuccess = CreateRubbishFiles(destination,item.Value,Application.streamingAssetsPath);
                       if(isSuccess)
                       {
                           creatRubbishFileNum ++;

                           if(creatRubbishFileNum == creatRubbishFileMaxLimit)
                           {
                               break;
                           }
                       }
                    }
                    if(creatRubbishFileNum == 0)
                    {
                        Debug.Log("zzd________加入垃圾文件 防止死循环 garbleABPathDic.Count = " + garbleABPathDic.Count);
                        break;
                    }
                }
                if(creatRubbishFileNum == creatRubbishFileMaxLimit)
                {
                    Debug.Log("zzd_______  Add rubbish files success !!");
                }
                Debug.Log("zzd_______  Add rubbish files end!!!");

            // #if !UnityLocalTest
                #region IOS马甲包混淆 local_server_list
                string lslJson = "local_server_list.json";
                // GetGarbleABPath(Application.streamingAssetsPath, randomNum,lslJson ,out string gPath);
                GetGarbleABPath2(Application.streamingAssetsPath, lslJson ,out string gPath);
                if (string.IsNullOrEmpty(gPath) == false)
                {
                    rk1.keys.Add(new ABPathKey.KeyValuePair()
                    {
                        key = lslJson,
                        value = gPath
                    });

                    rk1.configs.Add(lslJson,gPath);
                }
                #endregion
            // #endif

                Debug.Log("garbleABPath.asset path:" + path);
                EditorUtility.SetDirty(rk1);
                UIHelper.ImportAssets(path);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                // Debug.LogError("《《《《《《《《《《《开始检测空文件夹《《《《《《《《《《《《《《《《《《《《《《《《《《《《");
                // string[] checkEmptyFloderArray = Directory.GetDirectories(destination);
                // foreach(var item in checkEmptyFloderArray)
                // {
                //     Debug.Log("zzd_____________需要检测空文件夹的文件夹 ：" + item);
                // }
                // for (int i = 0; i < needCheckEmptyFloderList.Count; i++)
                // {
                //     Debug.Log("混淆的根文件夹，第 " + i + " 个为：" + needCheckEmptyFloderList[i]);
                // }
                // CheckEmptyFloder(destination,needCheckEmptyFloderList.ToArray());

                EditorUtility.ClearProgressBar();

            // }
            // catch (Exception e)
            // {
            //     EditorUtility.ClearProgressBar();
            //     Debug.LogError(" throw exception _______GarbleAbPath:" + e.ToString());
            // }
        }

        /// <summary>
        /// 生成垃圾文件数量
        /// </summary>
        /// <param name="streamingAssetsPath"></param>
        /// <param name="destination"></param>
        /// <param name="garbleABPathDic"></param>
        /// <param name="rubbishNum"></param>
        static void GenGarbleAbRubbishNum(string streamingAssetsPath, string destination, Dictionary<string, string> garbleABPathDic, int rubbishNum = 600)
        {
            int creatRubbishFileNum = 0;
            int creatRubbishFileMaxLimit = JenkinsEnv.Instance.GetInt("garble_ab_rubbish_num", rubbishNum);
            if (File.Exists(streamingAssetsPath + "/111.txt"))
            {
                //var lines = File.ReadAllLines(streamingAssetsPath + "/111.txt");
                //foreach(var line in lines)
                //{
                //    var rubbishPath = line + ".txt";
                //    var exist = File.Exists(rubbishPath);
                //    Debug.Log($"{rubbishPath} exist:{exist}");
                //    if (exist)
                //    {
                //        File.Delete(rubbishPath);
                //    }
                //}

                FileUtil.DeleteFileOrDirectory(streamingAssetsPath + "/111.txt");
            }
            Debug.Log("zzd_______Start add rubbish files");
            while (creatRubbishFileNum < creatRubbishFileMaxLimit && garbleABPathDic.Count > 0)
            {
                foreach (var item in garbleABPathDic)
                {
                    bool isSuccess = CreateRubbishFiles(destination, item.Value, streamingAssetsPath);
                    if (isSuccess)
                    {
                        creatRubbishFileNum++;

                        if (creatRubbishFileNum == creatRubbishFileMaxLimit)
                        {
                            break;
                        }
                    }
                }
                if (creatRubbishFileNum == 0)
                {
                    Debug.Log("zzd________加入垃圾文件 防止死循环 garbleABPathDic.Count = " + garbleABPathDic.Count);
                    break;
                }
            }
            if (creatRubbishFileNum == creatRubbishFileMaxLimit)
            {
                Debug.Log("zzd_______  Add rubbish files success !!");
            }
            Debug.Log("zzd_______  Add rubbish files end!!!");
        }

        /// <summary>
        /// 只生成AbPathLog和.asset
        /// </summary>
        public static void GarbleAbPathLogAndAssets()
        {
            GarbleAbPathWitParam(true, true, Application.streamingAssetsPath, Application.dataPath);
        }

        static void GarbleAbPathWitParam(bool notMoveFileOrDir = false, bool genABPathAssets = true, string streamingAssetsPath = "", string dataPath = "")
        {
            int abTotalNumber = 0;
            BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
            string outputFolder = BuildScript.GetPlatformFolderForAssetBundles(buildTarget);
            var androiddatapath = Path.Combine(streamingAssetsPath, BuildScript.AssetBundlesOutputPath);
            var destination = System.IO.Path.Combine(androiddatapath, outputFolder);
            string outputPath = Path.Combine(BuildScript.AssetBundlesOutputPath, BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
            string filePath = Path.Combine(destination, "files.txt");
            Debug.Log("files.txt path = " + filePath);

            string filesTxtStr = File.ReadAllText(filePath);
            // var tempNewestPatchFileDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(filesTxtStr);

            Dictionary<string, object> fileJson = UIHelper.ToObj<Dictionary<string, object>>(filesTxtStr);
            Dictionary<string, string[]> needGarbleAbPathDic = UIHelper.ToObj<Dictionary<string, string[]>>(fileJson["list"].ToString());
            Dictionary<string, string> garbleABPathDic = new Dictionary<string, string>();

            var garbledABPathLogPath = streamingAssetsPath + "/garbledABPathLog.txt";
            string[] filesArray = Directory.GetFiles(streamingAssetsPath);
            string garbledABPathLogFileName = "";
            foreach (var item in filesArray)
            {
                if (item.Contains("garbledABPathLog_") && item.Split('.').Length == 2)
                {
                    garbledABPathLogFileName = item;
                    Debug.Log("zzd_________found garbledABPathLog. file " + garbledABPathLogFileName);
                    break;
                }
            }
            if (File.Exists(garbledABPathLogFileName))
            {
                File.Delete(garbledABPathLogFileName);
                if (File.Exists(garbledABPathLogFileName))
                {
                    Debug.Log("zzd______garbledABPathLog. file delete fail!!!");
                }
            }
            // string[] checkEmptyFloderArray = Directory.GetDirectories(destination);
            // foreach(var item in checkEmptyFloderArray)
            // {
            //     Debug.Log("zzd_____________需要检测空文件夹的文件夹 ：" + item);
            // }
            // CheckEmptyFloder(destination,checkEmptyFloderArray);
            // return;

            // #if !UnityLocalTest
            #region IOS马甲包混淆
            string updateKey = "update.json";
            if (needGarbleAbPathDic.ContainsKey(updateKey) == false)
                needGarbleAbPathDic.Add(updateKey, null);

            string packageKey = "package.json";
            if (needGarbleAbPathDic.ContainsKey(packageKey) == false)
                needGarbleAbPathDic.Add(packageKey, null);
            #endregion
            // #endif

            GetGarbleWordsList();

            string garbleABPathStr = "";//混淆之后的AB name
            int needGarbleAbPathNum = needGarbleAbPathDic.Count * 2;
            Debug.Log("needGarbleAbPathNum *2 = " + needGarbleAbPathNum);
            // List<string> needCheckEmptyFloderList = new List<string>();
            needCheckEmptyFloderList.Clear();
            checkedAbNameList.Clear();
            garbledSameAbNameNum = 0;
            if (File.Exists(garbledABPathLogPath))
            {
                File.Delete(garbledABPathLogPath);
            }
            File.Create(garbledABPathLogPath).Dispose();
            foreach (var item in needGarbleAbPathDic)
            {
                // if(item.Key.Contains('/'))
                // {
                //     Debug.Log("有文件夹");
                //     string floderName = Path.Combine(destination,item.Key.Split('/')[0]);
                //     if(!needCheckEmptyFloderList.Contains(floderName))
                //     {
                //         Debug.Log("添加需要检测空文件夹的文件夹 " + floderName);
                //         needCheckEmptyFloderList.Add(floderName); 
                //     }
                // }

                GetGarbleABPath2(destination, item.Key, out garbleABPathStr, notMoveFileOrDir);
                if (garbleABPathStr == "")
                {
                    Debug.Log("asset bundle file not exist,check if .manifes file exist");
                }
                else
                {
                    checkedAbNameList.Add(garbleABPathStr);
                    garbleABPathDic.Add(item.Key, garbleABPathStr);
                    abTotalNumber++;

                    StreamWriter sw;
                    FileInfo garbledABPathTxt = new FileInfo(garbledABPathLogPath);
                    sw = garbledABPathTxt.AppendText();
                    sw.WriteLine(item.Key + "|" + garbleABPathStr);
                    sw.Flush();
                    sw.Close();
                    sw.Dispose();
                }

                GetGarbleABPath2(destination, item.Key + ".manifest", out garbleABPathStr, notMoveFileOrDir);
                if (garbleABPathStr == "")
                {
                    // Debug.Log("ab.manifest file garble fail, continue");
                    continue;
                }
                else
                {
                    abTotalNumber++;
                }

                bool isCancel = EditorUtility.DisplayCancelableProgressBar("BuildGarbleAbPath..", item.Key, (float)abTotalNumber / (float)needGarbleAbPathNum);
                if (isCancel)
                {
                    break;
                }
                Debug.Log(" garble files number:" + abTotalNumber);
            }
            Debug.Log(" garbled same ab name number:" + garbledSameAbNameNum);
            if (abTotalNumber < needGarbleAbPathNum)
            {
                Debug.LogError($"Garble assetbundle number not correct {abTotalNumber} < {needGarbleAbPathNum}");
            }
            // EditorUtility.ClearProgressBar();

            if (File.Exists(garbledABPathLogPath))
            {
                string temp_goalPath = Path.Combine(streamingAssetsPath, "garbledABPathLog_" + GetRandomString(3) + "." + GetRandomString(UnityEngine.Random.Range(3, 10)));
                FileUtil.MoveFileOrDirectory(garbledABPathLogPath, temp_goalPath);
            }

            var path = "";
            if (genABPathAssets)
            {
                path = "Assets/Resources/garbleABPath.asset";
                FileUtil.DeleteFileOrDirectory(path);
                if (!System.IO.File.Exists(path))
                {
                    Debug.Log("zzd___________garbleABPath.asset not exist.  path = " + path);
                    EditorHelp.CheckDir(path);
                    var rk = ScriptableObject.CreateInstance<ABPathKey>();
                    rk.keys = new System.Collections.Generic.List<ABPathKey.KeyValuePair>();
                    UnityEditor.AssetDatabase.CreateAsset(rk, path);
                }
                if (File.Exists(dataPath + "/Resources/garbleABPath.asset"))
                {
                    Debug.Log("zzd_____garbleABPath.asset__Exists____ " + dataPath + "/Resources/garbleABPath.asset");
                }
                else
                {
                    Debug.Log("zzd_____garbleABPath.asset__not Exists____ " + dataPath + "/Resources/garbleABPath.asset");
                }
            }

            ABPathKey rk1 = null;
            if (genABPathAssets)
            {
                rk1 = AssetDatabase.LoadAssetAtPath<ABPathKey>(path);
                //Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();
                rk1.keys.Clear();
                foreach (var item in garbleABPathDic)
                {
                    rk1.keys.Add(new ABPathKey.KeyValuePair()
                    {
                        key = item.Key,
                        value = item.Value
                    });

                    rk1.configs.Add(item.Key, item.Value);
                }
            }

            // #if !UnityLocalTest
            #region IOS马甲包混淆 local_server_list
            string lslJson = "local_server_list.json";
            // GetGarbleABPath(streamingAssetsPath, randomNum,lslJson ,out string gPath);
            GetGarbleABPath2(streamingAssetsPath, lslJson, out string gPath, notMoveFileOrDir);
            if (string.IsNullOrEmpty(gPath) == false && genABPathAssets)
            {
                rk1.keys.Add(new ABPathKey.KeyValuePair()
                {
                    key = lslJson,
                    value = gPath
                });

                rk1.configs.Add(lslJson, gPath);
            }
            #endregion
            // #endif

            GenGarbleAbRubbishNum(streamingAssetsPath, destination, garbleABPathDic);

            Debug.Log("garbleABPath.asset path:" + path);
            if (!string.IsNullOrEmpty(path))
            {
                EditorUtility.SetDirty(rk1);
                UIHelper.ImportAssets(path);
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            // Debug.LogError("《《《《《《《《《《《开始检测空文件夹《《《《《《《《《《《《《《《《《《《《《《《《《《《《");
            // string[] checkEmptyFloderArray = Directory.GetDirectories(destination);
            // foreach(var item in checkEmptyFloderArray)
            // {
            //     Debug.Log("zzd_____________需要检测空文件夹的文件夹 ：" + item);
            // }
            // for (int i = 0; i < needCheckEmptyFloderList.Count; i++)
            // {
            //     Debug.Log("混淆的根文件夹，第 " + i + " 个为：" + needCheckEmptyFloderList[i]);
            // }
            // CheckEmptyFloder(destination,needCheckEmptyFloderList.ToArray());

            EditorUtility.ClearProgressBar();

            // }
            // catch (Exception e)
            // {
            //     EditorUtility.ClearProgressBar();
            //     Debug.LogError(" throw exception _______GarbleAbPath:" + e.ToString());
            // }
        }

        [NUnit.Framework.Test]
        public static void TestGarbleAbPath2()
        {
            GarbleAbPathWitParam(true, true, Application.streamingAssetsPath, Application.dataPath);
        }

        /// <summary>
        /// 在gradleProject混淆路径及生成垃圾文件
        /// </summary>
        public static void GarbleAbPathInGradleProject()
        {
            GarbleAbPathInGradleProjectWithParam("", true, 600);
        }

        static void GarbleAbPathInGradleProjectWithParam(string defaultPath = "", bool moveFileOrDir = false, int rubbishNum = 600)
        {
            string gradleProjectPath = "";
            var gradle_project_path = JenkinsEnv.Instance.Get("platform_gradle_project_path", defaultPath);
            if(!string.IsNullOrEmpty(gradle_project_path))
            {
                gradleProjectPath = gradle_project_path;
            }
            else
            {
                Debug.LogError("gradle_project_path is null");
                EditorApplication.Exit(1);
                return;
            }

            Debug.Log($"GarbleAbPathInGradleProject:[{gradleProjectPath}]");
            var assetsPath = $"{gradleProjectPath}/gradleProject/unityLibrary/src/main/assets";
            if(!Directory.Exists(assetsPath))
            {
                Debug.LogError($"GarbleAbPathInGradleProject assets path not exists:[{assetsPath}]");
                EditorApplication.Exit(1);
                return;
            }

            var files = Directory.GetFiles(assetsPath, "garbledABPathLog_*.*");
            if(files.Length == 0)
            {
                Debug.LogError($"GarbleAbPathInGradleProject cannot garbledABPathLog_*.* in path [{assetsPath}]");
                EditorApplication.Exit(1);
                return;
            }

            string platform = JenkinsEnv.Instance.Get("target_platform", "Android");
            var destination = Path.Combine(assetsPath, BuildScript.AssetBundlesOutputPath, platform).Replace('\\', '/');
            Debug.Log($"destination:{destination}");

            var garbledABPathLogPath = files[0];
            Debug.Log($"GarbleAbPathInGradleProject logPath:{garbledABPathLogPath}");

            string[] lines = File.ReadAllLines(garbledABPathLogPath);
            Dictionary<string, string> garbleABPathDic = new Dictionary<string, string>();
            Dictionary<string, int> folderDic = new Dictionary<string, int>();
            foreach (var line in lines)
            {
                var ss = line.Split('|');
                if (ss.Length < 2)
                {
                    continue;
                }

                string raw = Path.Combine(destination, ss[0].Trim()).Replace('\\', '/');
                string garbled = Path.Combine(destination, ss[1].Trim()).Replace('\\', '/');
                string target = null;
                garbleABPathDic[raw] = garbled;

                if(File.Exists(raw))
                {
                    target = ss[0].Trim();
                    Debug.LogWarning($"GarbleAbPathInGradleProject move:[{raw}]->{garbled}");
                    MoveFileOrDir(raw, garbled, moveFileOrDir);
                }
                //else if(File.Exists(garbled)) // 用于测试还原
                //{
                //    target = ss[1].Trim();
                //    //Debug.LogWarning($"GarbleAbPathInGradleProject move:[{garbled}]->{raw}");
                //    MoveFileOrDir(garbled, raw, moveFileOrDir);
                //}

                if(target != null)
                {
                    int idx = target.IndexOf('/');
                    if (idx != -1)
                    {
                        var prefix = target.Substring(0, idx); // 获取文件夹前缀，用于后面的删除
                        if(!string.IsNullOrEmpty(prefix))
                        {
                            if (folderDic.ContainsKey(prefix))
                                folderDic[prefix]++;
                            else
                                folderDic[prefix] = 1;
                        }
                    }
                }
            }

            // 删除移动完文件后残留的空文件夹
            foreach(var folder in folderDic)
            {
                var fullPath = Path.Combine(destination, folder.Key).Replace('\\', '/').Trim();
                if(Directory.Exists(fullPath))
                {
                    var fs = Directory.GetFiles(fullPath, "*.*", SearchOption.AllDirectories);
                    if(fs != null && fs.Length == 0)
                    {
                        Debug.Log($"delete folder:[{fullPath}]");
                        Directory.Delete(fullPath, true);
                    }
                }
            }

            GenGarbleAbRubbishNum(assetsPath, destination, garbleABPathDic, rubbishNum);
        }

        [NUnit.Framework.Test]
        [NUnit.Framework.TestCase("G:/zHero_android_MobHeroes", false, 0)]
        [NUnit.Framework.TestCase("G:/zHero_android_MobHeroes", false, 6)]
        [NUnit.Framework.TestCase("G:/zHero_android_MobHeroes", true, 0)]
        [NUnit.Framework.TestCase("G:/zHero_android_MobHeroes", true, 6)]
        public static void TestGarbleAbPathInGradleProjectWithParam(string defaultPath = "", bool moveFileOrDir = false, int rubbishNum = 600)
        {
            GarbleAbPathInGradleProjectWithParam(defaultPath, moveFileOrDir, rubbishNum);
        }

        /// <summary>
        /// 移动文件或目录，如果没有路径文件夹不存在则创建
        /// </summary>
        /// <param name="source"></param>
        /// <param name="dest"></param>
        /// <param name="moveFileOrDir"></param>
        static void MoveFileOrDir(string source, string dest, bool moveFileOrDir = true)
        {
            string path = Path.GetDirectoryName(dest);
            if (!string.IsNullOrEmpty(path))
            {
                string fullPath = Path.GetFullPath(path);
                if (!Directory.Exists(fullPath))
                {
                    //Debug.LogWarning($"create dir:[{source}]->[{fullPath}]");
                    if(moveFileOrDir)
                    {
                        Directory.CreateDirectory(fullPath);
                    }
                }
            }

            if(moveFileOrDir)
            {
                if (File.Exists(dest))
                {
                    File.Delete(dest);
                }

                FileUtil.MoveFileOrDirectory(source, dest);
            }
        }

        static void CheckEmptyFloder(string outputPath,string[] checkFloders)
        {
            bool hasEmptyFloderInPath = false;
            for (int i = 0; i < checkFloders.Length; i++)
            {
                bool isCancel = EditorUtility.DisplayCancelableProgressBar("CheckAndDeleteEmptyFloder..", checkFloders[i], (float)i / (float)checkFloders.Length);
                if (isCancel)
                {
                    break;
                }
                if(Directory.Exists(checkFloders[i]))
                {
                    if(Directory.GetDirectories(checkFloders[i]).Length > 0)
                    {
                        // Debug.Log("-------------------不是空文件夹" + checkFloders[i]);
                        hasEmptyFloderInPath = false;
                        // Debug.LogError("查看是否有下层文件夹");
                       string[] subFloders = Directory.GetDirectories(checkFloders[i]);
                       if(subFloders.Length > 0)
                       {
                        //    Debug.LogError("有下层文件夹");
                           CheckEmptyFloder(outputPath, subFloders);
                       }
                       else
                       {
                        //    CheckEmptyFloder(checkFloders);
                            Debug.LogError(" 没有下层文件夹----");
                       }
                    }
                    else
                    {
                        Debug.LogError("已经是最底层文件夹，判断全部是.meta文件则删除该文件夹" + checkFloders[i]);
                        if(Directory.GetFiles(checkFloders[i]).Length > 0)
                        {
                            bool isAllMetaFile = false;
                            foreach (var item in Directory.GetFiles(checkFloders[i]))
                            {
                                Debug.Log("判断文件夹中是否都是 .meta 文件------------" + item);
                                if (item.EndsWith(".meta"))
                                {
                                    Debug.Log("发现.meta文件 " + item);
                                    File.Delete(item);
                                    if (File.Exists(item))
                                    {
                                        Debug.Log(item + "删除失败");
                                    }
                                }
                                else
                                {
                                    isAllMetaFile = false;
                                    Debug.Log("有不是.meta后缀文件： "+ item);
                                    break;
                                }
                            }
                            if (isAllMetaFile == true)
                            {
                                Debug.LogError("--------全是.meta文件，删除文件夹");
                                Directory.Delete(checkFloders[i],true);
                                // if(File.Exists(checkFloders[i] + ".meta"))
                                // {
                                //     File.Delete(checkFloders[i] + ".meta");
                                // }
                                // AssetDatabase.Refresh();
                                if(Directory.Exists(checkFloders[i]))
                                {
                                    Debug.LogError(checkFloders[i] + "   ！！！！！！！！！！！删除不成功");
                                }
                                else
                                {
                                    Debug.LogError(checkFloders[i] + "  删除成功 继续开始查找");
                                    checkFloders = Directory.GetDirectories(outputPath);
                                    CheckEmptyFloder(outputPath, checkFloders);
                                }
                            }
                            else
                            {
                                Debug.LogError("有不是.meta的文件 跳过这个文件夹 继续寻找");
                            }
                        }
                        else
                        {
                            Debug.LogError("--------空文件夹，删除文件夹---" + checkFloders[i]);
                            Directory.Delete(checkFloders[i],true);
                            // AssetDatabase.Refresh();
                            if(Directory.Exists(checkFloders[i]))
                            {
                                Debug.LogError(checkFloders[i] + "   ！！！！！！！！！！！空文件夹删除不成功");
                            }
                            else
                            {
                                Debug.LogError( "  空文件夹删除成功 继续开始查找 " + checkFloders[i]);
                                 if(File.Exists(checkFloders[i] + ".meta"))
                                {
                                    File.Delete(checkFloders[i] + ".meta");
                                }
                                if(checkFloders[i] == outputPath)
                                {
                                    Debug.LogError( "  zzd__________ 到达最外层 " + checkFloders[i]);
                                    break;
                                }
                                checkFloders = Directory.GetDirectories(outputPath);
                                CheckEmptyFloder(outputPath, checkFloders);
                            }
                        }
                        
                    }
                }
            }
        }

        static List<char> garbleBaseList = new List<char> {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z','0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '_'};
        static List<char> garbledList = new List<char> {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z','0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '_'};
        //混淆garbleBaseList之后的数组，与garbleBaseList对应
        //混淆规则：数组中随机取一半元素互相交换，然后再整体移位一个随机位数
        static List<char> newGarbledList = new List<char>(garbleBaseList.Count);
        static char replaceDotChar = '@';//替换后缀中的'.'
        [MenuItem("Assets/UI/GarbleAbPathList")]
        private static void GetGarbleWordsList()
        {
            //随机几组交换位置
            // int randomIndexGroupCount = UnityEngine.Random.Range(1, Mathf.CeilToInt(garbledList.Count/2));
            int randonIndexListCount = Mathf.CeilToInt(garbledList.Count/2) * 2;
            Debug.Log("  randonIndexListCount = " + randonIndexListCount);
            List<int> randomIndexList = new List<int>(randonIndexListCount);
            Random rd = new Random();
            for(int i = 0;i < randonIndexListCount;i++)
            {
                int randomIndex = rd.Next(0,garbledList.Count);
                while(randomIndexList.Contains(randomIndex))
                {
                    randomIndex = rd.Next(1,garbledList.Count);
                }
                randomIndexList.Add(randomIndex);
            }
            for(int i = 0;i < randomIndexList.Count;i++)
            {
                char tempIndex = new char();
                if(i%2 == 0)//双数
                {
                    tempIndex = garbledList[randomIndexList[i]];
                    garbledList[randomIndexList[i]] = garbledList[randomIndexList[i + 1]];
                    garbledList[randomIndexList[i + 1]] = tempIndex;
                }
            }
            Random moveSeed = new Random();
            int randomSeed = moveSeed.Next(1,garbledList.Count);
            newGarbledList.Clear();
            StringBuilder newGarbledListStr = new StringBuilder();
            for (int i = 0; i < garbledList.Count; i++)
            {
                int newIndex = i + randomSeed >= garbledList.Count
                            ? (i + randomSeed) % garbledList.Count
                            : i + randomSeed;
                newGarbledList.Add(garbledList[newIndex]);
                newGarbledListStr.Append("'");
                newGarbledListStr.Append(garbledList[newIndex]);
                newGarbledListStr.Append("',");
            }
            newGarbledListStr.Remove(newGarbledListStr.Length - 1,1);
            Debug.Log("garbleBaseList.count = " + garbleBaseList.Count);
            Debug.Log("garbled list str = " + newGarbledListStr.ToString());

            replaceDotChar = newGarbledList[rd.Next(0,newGarbledList.Count)];
            Debug.Log("replaceDotChar = " + replaceDotChar);
        }
        /// <summary>
        /// Asset Bundle Path Garble
        /// </summary>
        /// <param name="abOutputPath">输出路径</param>
        /// <param name="abName"></param>
        /// <param name="garblePath"></param>
        private static void GetGarbleABPath2(string abOutputPath,string abName, out string garblePath, bool notMoveFileOrDir = false)
        {
             var oldPath = System.IO.Path.Combine(abOutputPath, abName).Replace("\\", "/");
             if (!File.Exists(oldPath))
            {
                if(!abName.EndsWith(".manifest"))
                    Debug.LogError("need Garble Ab path = " + oldPath + " ----not exist,please check ab files");
                garblePath = "";
                return;
            }

            // string abNameSuffixStr = "";
            // if(abName.EndsWith("lobby.unity"))
            // {
            //     Debug.Log("zzd__________   .unity 后缀");
            //     abNameSuffixStr = "lobby.unity";
            // }
            // abName = abName.Replace(".","");
            // char[] abNameArray = abNameSuffixStr == "" ?abName.ToArray() :abName.Replace(abNameSuffixStr, "").ToArray();
            char[] abNameArray = abName.ToArray();
            string abName_replace = "";
            for (int i = 0; i < abNameArray.Length; i++)
            {
                // Debug.Log(i + "    当前字符为：" + abNameArray[i]);
                int index = garbleBaseList.IndexOf(abNameArray[i]);
                // Debug.Log(i + "   当前字符在 garbleBaseList 中的index = " + index + "    abNameArray[i] = " + abNameArray[i]);
                if(index < 0) 
                {
                    if(abNameArray[i] == '.')
                    {
                        abNameArray[i] = replaceDotChar;
                    }
                    else
                    {
                        continue;
                    }
                }
                else
                {
                    abNameArray[i] = newGarbledList[index];
                }
                // Debug.Log( i + " 替换之后的   abNameArray[i] = " + abNameArray[i]);
            }
            abName_replace = new string(abNameArray);
            //防止相同名字导致资源无法导入
            if(checkedAbNameList.Contains(abName_replace))
            {
                //unity 大小写不敏感
                Debug.LogError("!!!!!!!!!!!!!!!!!!!!!!!!!!!!有相同路径—————————————— garbleABPathStr = " + abName_replace);
                
                char[] temp_abNameArray = abName_replace.ToArray();
                int temp_len = temp_abNameArray.Length;
                Random rd = new Random();
                while(checkedAbNameList.Contains(abName_replace))
                {
                   for (int i = 0; i < temp_len; i++)
                    {
                        temp_abNameArray[i] = newGarbledList[rd.Next(newGarbledList.Count)];
                    }
                    abName_replace = new String(temp_abNameArray);
                }
                Debug.LogError("!!!!!!!!!!!!!!!!!!!!!!!!!!!!有相同路径———————修改后——————— garbleABPathStr = " + abName_replace);
                garbledSameAbNameNum ++;
            }

            Debug.Log("Garble Before AB Path = " + abName);
            Debug.Log("Garble Ab path = " + abName_replace);

            string goalPath = Path.Combine(abOutputPath, abName_replace).Replace("\\", "/");
            if(!notMoveFileOrDir) // 如果还需要移动文件或文件夹
            {
                if (File.Exists(goalPath))
                {
                    // Debug.Log("goalPath = " + goalPath + "  exist，need delete");
                    FileUtil.DeleteFileOrDirectory(goalPath);
                }
                else
                {
                    // Debug.Log("goalPath = " + goalPath + "----Not exist");
                    string goalFloderPath = goalPath.Replace(goalPath.Split('/')[goalPath.Split('/').Length - 1], "");
                    if (!Directory.Exists(goalFloderPath))
                    {
                        // Debug.Log("create Floder path：" + goalFloderPath);
                        Directory.CreateDirectory(goalFloderPath);
                    }
                }

                FileUtil.MoveFileOrDirectory(oldPath, goalPath);
            
                 if (File.Exists(oldPath))
                {
                    Debug.LogError("oldPath = " + oldPath + "----exit, file moved fail");
                }
            }

             garblePath = abName_replace;
        }

         private static bool CreateRubbishFiles(string outPath,string garbledAbName, string streamingAssetsPath)
        {
            int fileNameLength = UnityEngine.Random.Range(4, 20);
            var rand = System.Security.Cryptography.RandomNumberGenerator.Create();
            byte[] bytes = new byte[1024*2]; //4k大小
            rand.GetBytes(bytes);
            string rubbishStr = Encoding.UTF8.GetString(bytes);
            string abFloderPath = "";
            string[] garbledAbNameArray = garbledAbName.Split('/');
            int len = garbledAbNameArray.Length;
            //保证有文件夹
            if(len > 1)
            {
                abFloderPath = garbledAbName.Substring(0,garbledAbName.Length - garbledAbNameArray[len - 1].Length);
                if(!Directory.Exists(abFloderPath))
                {
                    // Debug.LogError("CreateRubbishFiles path not exists  path = " + abFloderPath);
                    return false;
                }
                string goalPath = Path.Combine(outPath, abFloderPath + GetRandomString(fileNameLength)).Replace("\\", "/");
                // Debug.Log("goalPath = " + goalPath);
                File.WriteAllText(goalPath + ".txt",rubbishStr);

                if(!File.Exists(streamingAssetsPath + "/111.txt"))
                {
                    File.Create(streamingAssetsPath + "/111.txt").Dispose();
                }
                StreamWriter sw;
                FileInfo creatRubbishFilesTxt = new FileInfo(streamingAssetsPath + "/111.txt");
                sw = creatRubbishFilesTxt.AppendText();
                sw.WriteLine(goalPath);
                sw.Flush();
                sw.Close();
                sw.Dispose();

                return true;
            }
            return false;
        }

        [MenuItem("Assets/UI/CreatJunkFiles")]
        ///生成垃圾文件
        private static void CreatJunkFiles()
        {
            BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
            string outputFolder = BuildScript.GetPlatformFolderForAssetBundles(buildTarget);
            var androiddatapath = Path.Combine(Application.streamingAssetsPath, BuildScript.AssetBundlesOutputPath);
            var destination = System.IO.Path.Combine(androiddatapath, outputFolder);
            Dictionary<string, string> garbleABPathDic = new Dictionary<string, string>();
            int creatRubbishFileNum = 0;
            int creatRubbishFileMaxLimit = 10;
            garbleABPathDic.Add("637726687737225799/Fightlog.txt","fVZZiff9ZZVZiiCZSS/JXleh_6l.hqh");
            garbleABPathDic.Add("637726687738812552/Fightlog.txt","fVZZiff9ZZV99uiCCi/JXleh_6l.hqh");
            Debug.Log("zzd_______开始加入垃圾文件");
            if(File.Exists(Application.streamingAssetsPath + "/111.txt"))
            {
                FileUtil.DeleteFileOrDirectory(Application.streamingAssetsPath + "/111.txt");
            }
            while(creatRubbishFileNum < creatRubbishFileMaxLimit && garbleABPathDic.Count > 0)
            {
                foreach (var item in garbleABPathDic)
                {
                    bool isSuccess = false;
                    isSuccess = CreateRubbishFiles(destination,item.Value, Application.streamingAssetsPath);
                    if(isSuccess)
                    {
                        creatRubbishFileNum ++;
                        Debug.Log("zzd_______  加入垃圾文件  creatRubbishFileNum = " + creatRubbishFileNum);
                        if(creatRubbishFileNum == creatRubbishFileMaxLimit)
                        {
                            break;
                        }
                    }
                }
                if(creatRubbishFileNum == 0)
                {
                    Debug.Log("zzd________防止死循环 garbleABPathDic.Count = " + garbleABPathDic.Count);
                    break;
                }
            }
            Debug.Log("zzd_______  加入垃圾文件  结束");
        }

       static string GetRandomString(int length)
        {
            string result = "";
            char c = ' ';
            List<char> fileNameNotWords = new List<char>{':',';','<','=','>','?','@','[','\\',']','^','`'};
            for (int i = 0; i < length; i++)
            {
                // char c = (char)new Random(Guid.NewGuid().GetHashCode()).Next(97, 123);
                c = (char)new Random(Guid.NewGuid().GetHashCode()).Next(48, 123);
                while(fileNameNotWords.Contains(c))
                {
                    c = (char)new Random(Guid.NewGuid().GetHashCode()).Next(48, 123);
                } 
                result += c;
            }
            return result;
        }

    }
}
