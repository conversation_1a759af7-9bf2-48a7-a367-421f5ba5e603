using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using War.Script;
using XLua;

[InitializeOnLoad]
public static class LuaTableExportOptimizer
{
    private const string RequestTestKey = "LuaTableExportOptimizer";
    private static bool _hasPlayed;
    static bool isRestart = false;
    static float count = 3f;

    [MenuItem("Tool/策划配置表导出为Lua表", false)]
    private static void ExportLuaTable()
    {
        UnityEditor.SceneManagement.EditorSceneManager.OpenScene("Assets/Scene/Start1.unity");
        EditorPrefs.SetBool(RequestTestKey, true);
        //已经在播放状态，使其重新开始
        if (EditorApplication.isPlaying)
        {
            EditorApplication.isPlaying = false;
            isRestart = true;
        }
        else
        {
            EditorApplication.isPlaying = true;
        }
    }

    static LuaTableExportOptimizer()
    {
        EditorApplication.update += Update;
        EditorApplication.playModeStateChanged += PlaymodeStateChanged;
    }

    private static void Update()
    {
        if (count > 0)
        {
            count -= Time.deltaTime;
            return;
        }
        if (!_hasPlayed && 
            EditorApplication.isPlaying &&
            EditorApplication.isPlayingOrWillChangePlaymode && EditorPrefs.GetBool(RequestTestKey))
        {
            EditorPrefs.DeleteKey(RequestTestKey);
            _hasPlayed = true;
            Delete(Application.dataPath + "/Lua/Tables/Database/");
            LuaManager luaManager = GameObject.Find("Engine").GetComponent<LuaManager>();
            luaManager.luaEnv.DoString("require \"TableSave\"");
            LuaFunction fun = luaManager.luaEnv.Global.Get<LuaFunction>("SaveAllTableToFile");
            fun.Call();
            fun.Dispose();
            EditorApplication.isPlaying = false;
            AssetDatabase.Refresh();
        }
    }

    private static void Delete(string targetDirectoryPath)
    {
        if (!Directory.Exists(targetDirectoryPath))
            return;
        string[] directoryPaths = Directory.GetDirectories(targetDirectoryPath);
        foreach (string directoryPath in directoryPaths)
        {
            Delete(directoryPath);
        }
    }

    private static void PlaymodeStateChanged(PlayModeStateChange state)
    {
        if (!EditorApplication.isPlaying)
        {
            _hasPlayed = false;
        }
        count = 2;
        if (isRestart)
        {
            EditorApplication.isPlaying = true;
            isRestart = false;
        }
    }
}
