#if UNITY_EDITOR


using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

public static class MGameTextureDebuger
{
    /// <summary>
    /// 计算当前路径下，所有ab包的大小
    /// </summary>
    /// <param name="path"></param>
    [MenuItem("Assets/AB包体检查工具/记录当前路径包体详情", false, 2)]
    public static void RecordSelectDirAbs()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        List<CasualGameABInfos> infos = new List<CasualGameABInfos>();
        StringBuilder sb = new StringBuilder(2 << 10);

        foreach (var s in selPathArr)
        {
            var tmpInfos = RecordDirAbs(s);
            infos.Add(tmpInfos);
            sb.Append($"{tmpInfos.DirName},{tmpInfos.sizeInfo}\n");
        }

        File.WriteAllText("Assets/CasualGame/lastDirsAbsInfos.txt", sb.ToString());


        AssetDatabase.Refresh();
    }


    public static CasualGameABInfos RecordDirAbs(string path)
    {
        var inf = BuildDirABAndGetInfos(path);
        string jsonInfoFileName = Path.Combine(path, "lastRecordInfos.json");
        File.WriteAllText(jsonInfoFileName, JsonUtility.ToJson(inf));
        Debug.Log($"path={{path}},sum size={inf.sizeInfo}");
        return inf;
    }


    public static CasualGameABInfos BuildDirABAndGetInfos(string path)
    {
        try
        {
            // 获取当前路径的所有ab包
            var abFiles = BCABUtils.GetDirAssetBundleBuild(path);
            foreach (var fileInfo in abFiles)
            {
                Debug.LogWarning(fileInfo.assetBundleName);
            }

            //对点前进行选择的路径下的所有ab包进行打包
            string outputPath = Path.Combine(GetOutPutDir(), Path.GetFileNameWithoutExtension(path));
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }

            var maniFest = BuildPipeline.BuildAssetBundles(outputPath, abFiles,
                BuildAssetBundleOptions.ChunkBasedCompression,
                EditorUserBuildSettings.activeBuildTarget);
            var abNames = maniFest.GetAllAssetBundles();
            // Debug.LogWarning($"sum={abNames.Length}\n{string.Join("\n", abNames)}");

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            //统计当前打包结果，文件名跟大小
            DirectoryInfo directoryInfo = new DirectoryInfo(outputPath);
            FileInfo[] fileInfos = directoryInfo.GetFiles("*", SearchOption.AllDirectories);
            CasualGameABInfos inf = new CasualGameABInfos();

            long sumSize = 0;

            var rootName = Path.GetFileNameWithoutExtension(path);
            foreach (var fileInfo in fileInfos)
            {
                if (!fileInfo.FullName.EndsWith(".meta") && !fileInfo.FullName.EndsWith(".manifest") &&
                    !fileInfo.FullName.EndsWith(rootName))
                {
                    sumSize += fileInfo.Length;
                    var tmpFileInfo = new ABFileInfo(fileInfo,
                        maniFest.GetAssetBundleHash(Path.GetFileNameWithoutExtension(fileInfo.FullName)).ToString());
                    inf.items.Add(tmpFileInfo);
                }
            }

            Debug.LogWarning($"有效文件数为{inf.items.Count}");
            inf.DirName = Path.GetFileNameWithoutExtension(path);
            inf.items = inf.items.OrderByDescending(_ => _.size).ToList();
            inf.size = sumSize;
            inf.sizeInfo = EditorUtility.FormatBytes(inf.size);
            return inf;
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
            return new CasualGameABInfos()
            {
                DirName = Path.GetFileNameWithoutExtension(path),
                sizeInfo = "unknown"
            };
        }
    }

    public static string GetOutPutDir()
    {
        string dir = Application.streamingAssetsPath + "/abdebuger/";
        switch (EditorUserBuildSettings.activeBuildTarget)
        {
            case BuildTarget.Android:
                dir = Path.Combine(dir, BuildTarget.Android.ToString());
                break;
            case BuildTarget.iOS:
                dir = Path.Combine(dir, BuildTarget.iOS.ToString());
                break;
            case BuildTarget.StandaloneWindows64:
            case BuildTarget.StandaloneWindows:
                dir = Path.Combine(dir, "StandaloneWindows");
                break;
            default:
                Debug.LogError("出现打未定义平台的包，将会有异常");
                break;
        }

        if (Directory.Exists(dir))
        {
            Directory.Delete(dir, true);
        }

        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }


        return dir;
    }

    [MenuItem("Assets/AB包体检查工具/与所记录的包体详情进行对比", false, 2)]
    public static void DiffWithLastRecord()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            DiffDirWithPath(s);
        }
    }

    public static void DiffDirWithPath(string path)
    {
        var lastJsonFile = Path.Combine(path, "lastRecordInfos.json");
        if (File.Exists(lastJsonFile))
        {
            var thisJsonfile = Path.Combine(path, "diffRecordInfos.json");
            var lastInfo = JsonUtility.FromJson<CasualGameABInfos>(File.ReadAllText(lastJsonFile));
            var inf = BuildDirABAndGetInfos(path);
            var diff = lastInfo.size - inf.size;
            Debug.LogWarning($"两次ab总包体，优化后，空间变化为(上次总包体减本次总包体){diff}bit={EditorUtility.FormatBytes(diff)}");
            File.WriteAllText(thisJsonfile, JsonUtility.ToJson(inf));
            var oldDic = lastInfo.GetDic();
            var newDic = inf.GetDic();
            List<string> diffDetail = new List<string>(newDic.Count);
            long hotPackeSize = 0;
            foreach (var neinfo in newDic)
            {
                if (oldDic.ContainsKey(neinfo.Key))
                {
                    if (oldDic[neinfo.Key].hash != neinfo.Value.hash)
                    {
                        diffDetail.Add($"{neinfo.Value.name},{neinfo.Value.sizeInfo}\n");
                        hotPackeSize += neinfo.Value.size;
                    }
                }
                else
                {
                    diffDetail.Add($"{neinfo.Value.name},{neinfo.Value.sizeInfo}\n");
                    hotPackeSize += neinfo.Value.size;
                }
            }

            string hotfixPackageInfo = $"此次如果热更，包体大小约为：{hotPackeSize}bit={EditorUtility.FormatBytes(hotPackeSize)}\n";
            foreach (var s in diffDetail)
            {
                hotfixPackageInfo += (s);
            }

            Debug.LogWarning(hotfixPackageInfo);
        }
        else
        {
            Debug.LogWarning("还未生成对比文件，无法进行对比");
        }
    }


    /// <summary>
    /// 计算所有贴图的的占用空间,并按空间大小进行降序打印到控制台上
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    [MenuItem("Assets/图片工具/列出所有贴图（按存储大小降序排序）", false, 2)]
    public static void LogSelectedDirTextureInfos()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            LogDirTexturesInfo(s);
        }
    }


    private static Regex allPicsPatten =
        new Regex(".(png|gif|jpg|bmp|jpeg|PSD|TIFF|TGA|GIF|PICT|BMP)", RegexOptions.IgnoreCase);

    private static Regex AssetsPatten = new Regex("Assets*.*");

    public static void LogDirTexturesInfo(string path)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(path);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*", SearchOption.AllDirectories);
        List<PicInfo> picInfos = new List<PicInfo>(fileInfos.Length);
        foreach (var fileInfo in fileInfos)
        {
            if (!string.IsNullOrEmpty(allPicsPatten.Match(fileInfo.Name).Value) && fileInfo.Extension != ".meta")
            {
                string assetPath = AssetsPatten.Match(fileInfo.FullName).Value;
                Texture t = AssetDatabase.LoadAssetAtPath<Texture>(assetPath);
                TryGetTextureSize(t, assetPath, out long size, out int width, out int height);
                if (size > 0)
                {
                    picInfos.Add(new PicInfo(assetPath, size, t, width, height));
                }
            }
        }

        var s = picInfos.OrderByDescending(x => x.Size);
        Debug.LogWarning($"path={path}，find files count=={s.Count()}");
        foreach (var picInfo in s)
        {
            Debug.Log(
                $"{picInfo.FullPath} {EditorUtility.FormatBytes(picInfo.Size)}  width={picInfo.width}  height={picInfo.height}",
                picInfo.t);
        }
    }

    static bool TryGetTextureSize(Texture t, string path, out long size, out int width, out int height)
    {
        size = -1;
        width = -1;
        height = -1;
        try
        {
            size = EditorTextureUtil.GetStorageMemorySize(t);
            width = t.width;
            height = t.height;
            return true;
        }
        catch (Exception e)
        {
            Debug.LogError($"GetStorageMemorySize failed ,path=={path}", t);
            return false;
        }
    }

    // static Vector2 TryGetTextureRectSize(Texture t, string path, out long size)
    // {
    //     
    // }


    [System.Serializable]
    public class PicInfo
    {
        public string FullPath;
        public Texture t;
        public long Size;
        public int width;
        public int height;

        public PicInfo(string fullPath, long size, Texture t, int width, int height)
        {
            this.FullPath = fullPath;
            this.Size = size;
            this.t = t;
            this.width = width;
            this.height = height;
        }
    }

    [System.Serializable]
    public class CasualGameABInfos
    {
        public string DirName;
        public long size;
        public string sizeInfo;

        public List<ABFileInfo> items;
        Dictionary<string, ABFileInfo> itemsDic;

        public CasualGameABInfos()
        {
            items = new List<ABFileInfo>(200);
        }

        public Dictionary<string, ABFileInfo> GetDic()
        {
            itemsDic = new Dictionary<string, ABFileInfo>(items.Count);
            foreach (var abFileInfo in items)
            {
                itemsDic.Add(abFileInfo.name, abFileInfo);
            }

            return itemsDic;
        }
    }


    [System.Serializable]
    public class ABFileInfo
    {
        public string name;
        public long size;
        public string sizeInfo;
        public string hash;

        public ABFileInfo(FileInfo info, string hash)
        {
            name = info.Name;
            size = info.Length;
            sizeInfo = EditorUtility.FormatBytes(size);
            this.hash = hash;
        }
    }


    [MenuItem("Assets/图片工具/将选择的贴图近似修改为4的倍数（jpg|png|tag）", false, 2)]
    public static void ResizeSelectTextureNear2four()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            Texture2D t = AssetDatabase.LoadAssetAtPath<Texture2D>(s);

            var tmpNewT2d = ReSetTextureSize(t, GetNear4Number(t.width), GetNear4Number(t.height));
            // t.Resize(GetNear4Number(t.width), GetNear4Number(t.height), t.format, false);

            if (Path.GetExtension(s).ToLower() == ".png")
            {
                byte[] _bytes = tmpNewT2d.EncodeToPNG();
                File.WriteAllBytes(s, _bytes);
            }
            else if (Path.GetExtension(s).ToLower() == ".jpg")
            {
                byte[] _bytes = tmpNewT2d.EncodeToJPG();
                File.WriteAllBytes(s, _bytes);
            }
            else if (Path.GetExtension(s).ToLower() == ".tag")
            {
                byte[] _bytes = tmpNewT2d.EncodeToTGA();
                File.WriteAllBytes(s, _bytes);
            }
        }

        AssetDatabase.Refresh();
    }

    public static Texture2D ReSetTextureSize(Texture2D tex, int width, int height)
    {
        var rendTex = new RenderTexture(width, height, 24, RenderTextureFormat.ARGB32);
        rendTex.Create();
        Graphics.SetRenderTarget(rendTex);
        GL.PushMatrix();
        GL.Clear(true, true, Color.clear);
        GL.PopMatrix();

        var mat = new Material(Shader.Find("Unlit/Transparent"));
        mat.mainTexture = tex;
        Graphics.SetRenderTarget(rendTex);
        GL.PushMatrix();
        GL.LoadOrtho();
        mat.SetPass(0);
        GL.Begin(GL.QUADS);
        GL.TexCoord2(0, 0);
        GL.Vertex3(0, 0, 0);
        GL.TexCoord2(0, 1);
        GL.Vertex3(0, 1, 0);
        GL.TexCoord2(1, 1);
        GL.Vertex3(1, 1, 0);
        GL.TexCoord2(1, 0);
        GL.Vertex3(1, 0, 0);
        GL.End();
        GL.PopMatrix();

        var finalTex = new Texture2D(rendTex.width, rendTex.height, TextureFormat.ARGB32, false);
        RenderTexture.active = rendTex;
        finalTex.ReadPixels(new Rect(0, 0, finalTex.width, finalTex.height), 0, 0);
        finalTex.Apply();
        return finalTex;
    }


    private static int curNumber;

    public static int GetNear4Number(int cur)
    {
        if (cur >= 4)
        {
            curNumber = cur % 4;
            if (curNumber >= 2)
            {
                return cur + (4 - curNumber);
            }
            else
            {
                return cur - curNumber;
            }
        }
        else
        {
            return 4;
        }
    }

    private static void LoadTexture(string fullPath)
    {
        Texture2D t = AssetDatabase.LoadAssetAtPath<Texture2D>(fullPath);
    }


    [MenuItem("Assets/模型替换工具（OBJ）/替换", false, 2)]
    public static void ReplaceResultsObj()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            // Debug.Log(s);
            ReplaceObjs(s);
        }

        // AssetDatabase.Refresh();
    }

    public static void ReplaceObjs(string resultPath)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(resultPath);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*", SearchOption.AllDirectories);
        string parentDor = resultPath.Replace("/Results", "");
        foreach (var fileInfo in fileInfos)
        {
            if (fileInfo.Extension != ".meta")
            {
                string ext = Path.GetExtension(fileInfo.Name);
                if (!string.IsNullOrEmpty(ext))
                {
                    string parentFileName = Path.GetFileNameWithoutExtension(fileInfo.Name);
                    parentFileName = parentFileName.Substring(0, parentFileName.Length - 1);
                    // Debug.Log($"ext={ext},name={parentFileName}");
                    string parentFile = Path.Combine(parentDor, $"{parentFileName}{ext}");
                    if (File.Exists(parentFile))
                    {
                        File.Copy(fileInfo.FullName, parentFile, true);
                        Debug.Log("替换完成" + parentFile);
                    }
                    else
                    {
                        Debug.LogError("不存在文件" + parentFile);
                    }
                }
            }
        }

        // string fileName = Path.GetFileNameWithoutExtension(resultPath);
        // Debug.Log(fileName);
        // Debug.Log(resultPath.Replace("/Results", ""));
    }


    [MenuItem("Assets/图片工具/FileTextureTo2048", false, 2)]
    public static void FileTextureTo2048()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            SetTextureMaxSize(s, 2048, BuildTarget.NoTarget, BuildTarget.iOS, BuildTarget.Android,
                BuildTarget.StandaloneWindows,
                BuildTarget.StandaloneWindows64);
        }

        // AssetDatabase.Refresh();
    }

    
    [MenuItem("Assets/图片工具/FileTextureTo32", false, 2)]
    public static void FileTextureTo32()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            SetTextureMaxSize(s, 32, BuildTarget.NoTarget, BuildTarget.iOS, BuildTarget.Android,
                BuildTarget.StandaloneWindows,
                BuildTarget.StandaloneWindows64);
        }

        // AssetDatabase.Refresh();
    }
    
    public static void SetTextureMaxSize(string t2dPath, int maxSize, params BuildTarget[] targets)
    {
        var textureImporter = TextureImporter.GetAtPath(t2dPath) as TextureImporter;

        foreach (var buildTarget in targets)
        {
            TextureImporterPlatformSettings textureImporterPlatformSettings =
                textureImporter.GetPlatformTextureSettings(buildTarget.ToString());
            textureImporterPlatformSettings.overridden = true;
            textureImporterPlatformSettings.maxTextureSize = maxSize;
            textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
            EditorUtility.SetDirty(textureImporter);
        }

        textureImporter.SaveAndReimport();
        AssetDatabase.Refresh();
    }


    [MenuItem("Assets/图片工具/DirTextureTo32", false, 2)]
    public static void DirTextureTo32()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        foreach (var s in selPathArr)
        {
            Debug.Log($"path={s}");
        }

        var textureList = AssetDatabase.FindAssets("t:texture2D", selPathArr.ToArray());

        foreach (var s in textureList)
        {
            var path = AssetDatabase.GUIDToAssetPath(s);
            SetTextureMaxSize(path, 32, BuildTarget.NoTarget, BuildTarget.iOS, BuildTarget.Android,
                BuildTarget.StandaloneWindows,
                BuildTarget.StandaloneWindows64);
        }
        // AssetDatabase.Refresh();
    }
}


#endif