#if UNITY_EDITOR

using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using Debug = UnityEngine.Debug;

public class MiniGameTool
{
    private static Regex illegalPattern =
        new Regex("[`~!@#-$%^&*()+=|{}':;',\\[\\]<>/?~！@#￥%……&*（）——+|{}【】《》 ‘；：”“’。，、？]");


    [MenuItem("Assets/MiniGameTool/AB标签规范检查", false, 1)]
    public static void ABNameVerify()
    {
        Stopwatch st = new Stopwatch();
        st.Start();
        Debug.Log(@"start verify ab name ");

        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        AssetDatabase.Refresh();
        foreach (var s in selPathArr)
        {
            ABNameVerifyPath(s);
        }

        st.Stop();
        Debug.Log($"verify cost {st.ElapsedMilliseconds.ToString()}mm");
    }
    [MenuItem("Assets/MiniGameTool/一键检测所有Ab包", false, 1)]
    public static void CheckAllAB()
    {
        ABNameVerifyPath("Assets/CasualGame");
    }

    public static void ABNameVerifyPath(string path)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(path);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*.meta", SearchOption.AllDirectories);
        Dictionary<string, int> miniGameMissCount = new Dictionary<string, int>();
        int missCount = 0;
        string miniGameName = "";
        foreach (var fileInfo in fileInfos)
        {

            bool isOk = false;
            if (IsContainABName(fileInfo.FullName, out isOk))
            {
                string filePath = fileInfo.FullName.Replace(".meta", "");
                string fileName = Path.GetFileNameWithoutExtension(filePath);

                if (miniGameName != fileInfo.FullName.Split('\\')[6])
                {
                    missCount = 0;
                }
                if (isOk == false || illegalPattern.IsMatch(fileName))
                {
                    missCount++;
                }
                miniGameName = fileInfo.FullName.Split('\\')[6];
                if (!miniGameMissCount.ContainsKey(miniGameName))
                {
                    miniGameMissCount.Add(miniGameName, missCount);
                }
                else
                {
                    miniGameMissCount[miniGameName] = missCount;
                }

                if (illegalPattern.IsMatch(fileName))
                {
                    Debug.Log($"验证失败,路径=={filePath}");
                }
            }
        }
        foreach (var v in miniGameMissCount)
        {
            if (v.Value != 0)
            {
                var tmpInfos = MGameTextureDebuger.RecordDirAbs("Assets/CasualGame/" + v.Key);
                Debug.Log(v.Key + "小游戏不合规Prefab数为 " + v.Value + " 热更包大小为 " + tmpInfos.sizeInfo);
            }
        }
    }


    private static bool IsContainABName(string FullName, out bool isOk)
    {

        var info = File.ReadAllText(FullName);
        var mat = Regex.Match(info, @"assetBundleName: *.*");
        var matFolder = Regex.Match(info, @"folderAsset: *.*"); //判断是否文件夹
        if (mat.Length > 18)
        {


            string newFullName = FullName.ToLower().Replace(@"\", "/");

            string newMat = mat.ToString().Split(':')[1];


            //Debug.LogError("newFullName：" + newFullName.ToString());
            //Debug.LogError("mat        :" + newMat.ToString());
            if (newFullName.Trim().Contains(newMat.Trim()))
            {

                if (matFolder.Length > 0)
                {
                    Debug.LogError("不合规文件夹：" + FullName);
                    isOk = false;
                }
                else
                {
                    isOk = true;
                }
            }
            else
            {
                Debug.LogError("预制体AB标签不合规：" + FullName);
                isOk = false;
            }
        }
        else
        {
            isOk = false;
        }
        return mat.Length > 18;
    }

    [MenuItem("Assets/MiniGameTool/NameSpaceVerify", false, 1)]
    private static void NameSpaceVerify()
    {
        Stopwatch st = new Stopwatch();
        st.Start();
        Debug.Log(@"start verify name space ");
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        AssetDatabase.Refresh();
        foreach (var s in selPathArr)
        {
            NameSpaceVerifyPath(s);
        }

        st.Stop();
        Debug.Log($"verify cost {st.ElapsedMilliseconds.ToString()}mm");
    }

    static Regex rg = new Regex("(?<=(" + "namespace" + "))[.\\s\\S]*?(?=(" + "{" + "))",
        RegexOptions.Multiline | RegexOptions.Singleline);

    private static void NameSpaceVerifyPath(string path)
    {
        DirectoryInfo directoryInfo = new DirectoryInfo(path);
        FileInfo[] fileInfos = directoryInfo.GetFiles("*", SearchOption.AllDirectories);

        Dictionary<string, List<string>> infos = new Dictionary<string, List<string>>(3);
        foreach (var fileInfo in fileInfos)
        {
            if (fileInfo.Extension == ".cs")
            {
                var csfile = File.ReadAllText(fileInfo.FullName);
                var result = rg.Match(csfile);
                var tmpNameSpace = result.Value.Trim();

                if (string.IsNullOrEmpty(tmpNameSpace))
                {
                    Debug.LogWarning($"没有命名空间，需要增加，路径为{fileInfo.FullName}");
                }
                else
                {
                    if (!infos.ContainsKey(tmpNameSpace))
                    {
                        infos[tmpNameSpace] = new List<string>(999);
                    }

                    infos[tmpNameSpace].Add($"FullPath={fileInfo.FullName}");
                }
            }
        }

        if (infos.Count > 1)
        {
            Debug.LogError($"path={path}存在多个命名空间，一个小游戏只能存在一个命名空间，且以小游戏自己的名字为命名空间");
            foreach (var item in infos)
            {
                Debug.LogWarning($"namespace={item.Key}存在以下脚本");
                foreach (var csf in item.Value)
                {
                    Debug.Log(csf);
                }
            }
        }
    }

    [MenuItem("Assets/MiniGameTool/LogPath", false, 1)]
    private static void LogPath()
    {
        Debug.Log(Application.dataPath);
    }
    [MenuItem("Assets/MiniGameTool/删除合成恐龙本地数据", false, 1)]
    private static void DeleteDragonData()
    {
        PlayerPrefs.DeleteKey("MergeDinosaur_Coin");
        PlayerPrefs.DeleteKey("MergeDinosaur_PlayerConfig");
        PlayerPrefs.DeleteKey("MergeDinosaur_BuyDragonTimes");
        PlayerPrefs.DeleteKey("MergeDinosaur_BuyPlayerTimes");
    }
    [MenuItem("Assets/MiniGameTool/检测prefab脚本丢失", false, 1)]
    static void CheckMissingScripts()
    {
        var selGUIDArr = Selection.assetGUIDs;
        var selPathArr = selGUIDArr.Select(guid => AssetDatabase.GUIDToAssetPath(guid));
        List<string> files = new List<string>();
        Dictionary<string, int> miniGameMissCount = new Dictionary<string, int>();
        foreach (var s in selPathArr)
        {
            Debug.Log(s);
            files.Add(s);
        }
        if (files.Count == 0)
        {
            Debug.Log("未选中文件夹");
            return;
        }
        string[] fileArray = new string[files.Count];
        for (int i = 0; i < files.Count; i++)
        {
            fileArray[i] = files[i];
        }
        string[] listString = AssetDatabase.FindAssets("t:prefab", fileArray);// new string[] { "Assets/Resources" }
        int missCount = 0;
        string miniGameName = "";
        for (int i = 0; i < listString.Length; i++)
        {

            string Path = listString[i];

            float progressBar = (float)i / listString.Length;

            EditorUtility.DisplayProgressBar("Check Missing Scripts", "The progress of ： " + ((int)(progressBar * 100)).ToString() + "%", progressBar);


            Path = AssetDatabase.GUIDToAssetPath(Path);//ChangeFilePath(Path);
            //Debug.Log(Path);
            if (miniGameName != Path.Split('/')[2])
            {
                missCount = 0;
            }
            miniGameName = Path.Split('/')[2];

            GameObject prefab = AssetDatabase.LoadAssetAtPath(Path, typeof(GameObject)) as GameObject;

            if (prefab == null)
            {
                Debug.LogError("空的预设 ： " + Path);

                continue;
            }

            Transform[] transforms = prefab.GetComponentsInChildren<Transform>();

            for (int j = 0; j < transforms.Length; j++)
            {
                GameObject obj = transforms[j].gameObject;

                var components = obj.GetComponents<Component>();


                for (int k = 0; k < components.Length; k++)
                {
                    if (components[k] == null)
                    {
                        Debug.LogError("这个预制中有空的脚本 ：" + Path + " 挂在对象 : " + obj.name + " 上");
                        missCount++;
                        if (!miniGameMissCount.ContainsKey(miniGameName))
                        {
                            miniGameMissCount.Add(miniGameName, missCount);
                        }
                        else
                        {
                            miniGameMissCount[miniGameName] = missCount;
                        }
                        break;
                    }
                }
            }
        }
        foreach (var v in miniGameMissCount)
        {
            Debug.Log(v.Key + "小游戏丢失的脚本数为" + v.Value);
        }
        EditorUtility.ClearProgressBar();
    }
}
#endif