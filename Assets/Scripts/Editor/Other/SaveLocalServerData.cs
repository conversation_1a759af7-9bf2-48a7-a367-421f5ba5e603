using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using War.Base;
using System.Linq;
using LunarConsolePluginInternal;
using War.Common;

namespace War.Base
{
    public class SaveLocalServerData
    {   
        [MenuItem("Assets/UpdateLocalServerData")]
        public static void UpdateLocalServerData()
        {   
            var updateJsonPath = GetHServerUpdateJson();
            var channelTag = JenkinsEnv.Instance.Get("patch_mark");
            if (string.IsNullOrEmpty(channelTag) || string.IsNullOrEmpty(updateJsonPath))
            {
                Debug.LogError($"UpdateLocalServerData error updateJsonPath:{updateJsonPath},channelTag:{channelTag}");
                return;
            }
            var basePersistentPath = BaseLoader.GetPersistentRelativeUrl().Replace("file://", "");
            var outputPath = BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            var localServerListPath = basePersistentPath + "/LocalServerList/" + channelTag + "/" + outputPath + "/";
            var localServerListFilePath = localServerListPath + "local_server_list.json";
            var streaming_localServerList_path = Path.Combine(Application.streamingAssetsPath,"local_server_list.json");
            Debug.Log($"localServerListPath:{localServerListFilePath},streaming_localServerList_path:{streaming_localServerList_path}");

            if(!string.IsNullOrEmpty(updateJsonPath))
            {
                string file_content_str = WebRequestToStream(updateJsonPath);
                if(!string.IsNullOrEmpty(file_content_str))
                {
                    Dictionary<string, object> localUpdateJsion = UIHelper.ToObj<Dictionary<string, object>>(file_content_str);
                    string server_info_url = localUpdateJsion.TryGetValue("server_info_url",out var ob)? ob.ToString():"";
                    string server_info_url1 = localUpdateJsion.TryGetValue("server_info_url1", out var ob1) ? ob1.ToString() : "";

                    ParseServerInfo(server_info_url,out var serverListList);
                    ParseServerInfo(server_info_url1,out var serverListList1);
                    var localServerList = serverListList.Concat(serverListList1).ToList();
                    if (localServerList.Count > 0)
                    {
                        Dictionary<string,object> localServerListDic = new Dictionary<string, object>();
                        localServerListDic["server_list"] = localServerList;
                        string localServerListStr = UIHelper.ToJson(localServerListDic);

                        if(!Directory.Exists(localServerListPath)) Directory.CreateDirectory(localServerListPath);
                        if (File.Exists(localServerListFilePath)) File.Delete(localServerListFilePath);
                        if (File.Exists(streaming_localServerList_path)) File.Delete(streaming_localServerList_path);
                        //LocalServerList.json写到LocalServerList和StreamingAssets目录  
                        File.WriteAllText(localServerListFilePath,localServerListStr);
                        File.WriteAllText(streaming_localServerList_path,localServerListStr);

                        BuildScript.bExistLocalServerList = true;
                        Debug.Log("zzd______update local server list file success");
                    }
                    else Debug.LogError("please check server_info_url");
                }
                else Debug.LogError("load update.json fail,Please check channel updateJsonPath");
            }
            else Debug.LogError("Please Set Channel updateJsonPath");
        }

        private static void ParseServerInfo(string serverInfoUrl,out List<object> serverList)
        {
            serverList = new List<object>();
            if (string.IsNullOrEmpty(serverInfoUrl))
            {
                Debug.Log($"ParseServerInfo is null, serverInfoUrl:{serverInfoUrl}");
                return;
            }
            string file_content_str = WebRequestToStream(serverInfoUrl);
            Debug.Log("zzd_____webRequest:" + file_content_str);
            if(string.IsNullOrEmpty(file_content_str)) return;

            Dictionary<string,object> serverInfoDic = UIHelper.ToObj<Dictionary<string, object>>(file_content_str);
            // foreach (var item in serverInfoDic)
            // {
            //     Debug.Log("zzd______serverInfoDic: item.Key = " + item.Key + "   Value:" + item.Value);
            // }

            serverList = UIHelper.ToObj<List<object>>(serverInfoDic["server_list"].ToString());
            
            
            
            
            // foreach (var item in serverList)
            // {
            //     Debug.Log("zzd______serverListDic: item.Key = " + item);
            // }
        }
        public static string WebRequestToStream(string serverUrl)
        {
            string file_content_str = "";
            try
            {
                using (Stream stream = System.Net.WebRequest.Create(new Uri(serverUrl)).GetResponse().GetResponseStream())
                {
                    using (StreamReader file_content = new StreamReader(stream))
                    {
                        file_content_str = file_content.ReadToEnd();
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"serverUrl:{serverUrl} load error : {e.Message}");
            }
            return file_content_str;
        }
        public static string GetHServerUpdateJson(string targetPatchmark = "")
        {
            var patchmark = !string.IsNullOrEmpty(targetPatchmark)? targetPatchmark : JenkinsEnv.Instance.Get("patch_mark", "Test");
            if (string.IsNullOrEmpty(patchmark))  "".Print("Error patch_mark", patchmark);

            string configNewDir = "../../Tools/Patch/update_all/config_new.json";
            string configNewContent = File.ReadAllText(configNewDir);
            var configNewDic = UIHelper.ToObj<Dictionary<string, Dictionary<string, string>>>(configNewContent);

            var configJson = configNewDic[patchmark];
            var publicPatch = configJson["PublicPath"];
            publicPatch = ModifyAB.Instance.win2linuxDir(publicPatch);
            var plat = BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            var hServerUpdateJson = Path.Combine(publicPatch, plat, "update.json");
            return hServerUpdateJson;
        }
    }

}