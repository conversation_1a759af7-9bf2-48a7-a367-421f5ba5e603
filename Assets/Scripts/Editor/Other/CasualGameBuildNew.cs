using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.Networking;
using War.Common;

namespace War.Base
{
    [System.Serializable]
    public class CaualFileInfo
    {
        public string Name;
        public int Version;
        public string MD5;
        public long CRC32;
        public long Size;
        public string[] Dependencies;
    }

    [System.Serializable]
    public class CaualFile
    {
        public CaualFileInfo[] Entries;
    }

    [System.Serializable]
    public class MarkInfo
    {
        public string GameName;
        public int Version;
    }

    [System.Serializable]
    public class CaualGameUpdate
    {
        public string update_json_url;
        public List<MarkInfo> key2_mark_files_url_map;
        public string resource_url;
    }

    /// <summary>
    /// 小游戏打整包
    /// </summary>
    public class CasualGameBuildNew
    {
        [MenuItem("AssetBundles/CasualGameBuild/CopyToStreamAsset")]
        public static void Build()
        {
            //             var game = EditorWindow.GetWindow(typeof(EditorWindow).Assembly.GetType("UnityEditor.GameView"));
            //             game?.ShowNotification(new GUIContent($"{"提示"}"));

            var serverUrlRoot = @"http://172.18.2.105:8001/CasualGame_Pure_Res_105_002/TinyRes/";
            //UpdateRes(serverUrlRoot);
        }
        public static void DelteRes()
        {
            string updatePath = GetPathUpdate_Assetbundle();
            if (Directory.Exists(updatePath))
                Directory.Delete(updatePath, true);

            string casualPath = GetCasualGamePath();
            if (Directory.Exists(casualPath))
                Directory.Delete(casualPath, true);
        }

        /// <summary>
        /// 更新小游戏资源
        /// </summary>
        public static void UpdateRes(string serverUrlRoot, string[] reskeys = null, bool isdelete = true)
        {
            string updatePath = GetPathUpdate_Assetbundle();
            if (isdelete)
            {
                DelteRes();
            }
            // 独立热更小游戏
            UpdateResByUpdateName(serverUrlRoot, updatePath, "update.txt", reskeys);
            //// 华佗热更小游戏
            //UpdateResByUpdateName(serverUrlRoot, updatePath, "update_tiny.txt", reskeys);
        }

        /// <summary>
        /// 更新小游戏资源
        /// </summary>
        public static void UpdateResByUpdateName(string serverUrlRoot, string updatePath, string updateName, string[] stand_alone_res_keys)
        {
            if (stand_alone_res_keys != null && stand_alone_res_keys.Length > 0)
            {
                foreach (var item in stand_alone_res_keys)
                {
                    // 获取update.txt
                    var updateTarFileUrl = Path.Combine(serverUrlRoot, GetManifestName(), item, updateName);
                    var updateSrcFilePath = Path.Combine(updatePath, item, updateName);
                    if (ToolUti.DownloadFileW(updateTarFileUrl, updateSrcFilePath))
                    {
                        string txtData = File.ReadAllText(updateSrcFilePath);
                        var gameUpdate = JsonUtility.FromJson<CaualGameUpdate>(txtData);
                        if (gameUpdate != null)
                        {
                            foreach (var data in gameUpdate.key2_mark_files_url_map)
                            {
                                if (data.GameName == item)
                                {
                                    UpdataPatch(updatePath, data.GameName, gameUpdate.resource_url, data.Version);
                                }
                            }
                        }
                        else
                        {
                            Debug.LogError(" GameUpdate is Null ");
                            EditorApplication.Exit(1);
                            return;
                        }
                        UpdateJson(gameUpdate);
                    }
                }
            }
        }

        public static void UpdataPatch(string updatePath, string gamekey, string resourceUrl, int version)
        {
            var filePath = Path.Combine(updatePath, gamekey);
            DirectoryInfo rDirInfo = new DirectoryInfo(filePath);
            if (!rDirInfo.Exists) rDirInfo.Create();

            // 更新文件列表
            var fileTarUrl = Path.Combine(resourceUrl, gamekey, version.ToString(), "file.txt");
            var fileDstPath = Path.Combine(updatePath, gamekey, "file.txt");

            if (ToolUti.DownloadFileW(fileTarUrl, fileDstPath))
            {
                var txtData = File.ReadAllText(fileDstPath);
                var fileTxt = JsonUtility.FromJson<CaualFile>(txtData);

                // 添加开关控制是否使用异步处理
                bool useAsyncProcessing = EditorPrefs.GetBool("CasualGameBuild_UseAsync", true);
                
                if (useAsyncProcessing)
                {
                    // 使用异步方式处理文件下载
                    ProcessFilesAsync(fileTxt.Entries, updatePath, gamekey, resourceUrl, 3, (success, errorMessage) =>
                    {
                        if (!success)
                        {
                            Debug.LogError("========== Files download failed: " + errorMessage);
                            EditorApplication.Exit(1);
                        }
                        else
                        {
                            Debug.Log("All files downloaded and verified successfully!");
                        }
                    });
                }
                else
                {
                    // 使用同步方式处理文件下载
                    foreach (var data in fileTxt.Entries)
                    {
                        var url = Path.Combine(resourceUrl, gamekey, data.Version.ToString(), data.Name);
                        var file = Path.Combine(updatePath, gamekey, data.Name);
                    
                        int i = 0;
                        for (i = 0; i < 10; i++)
                        {
                            if (ToolUti.DownloadFileW(url, file))
                            {
                                var crc = BuildScript.File2CRC32(file);
                                if (crc == data.CRC32)
                                    break;
                            }
                        }
                        if (i == 10)
                        {
                            Debug.LogError("========== the file down error " + url);
                            EditorApplication.Exit(1);
                        }
                    }
                }
            }
        }

        public static void UpdateJson(CaualGameUpdate gameUpdate)
        {
            var key2markpath = Application.streamingAssetsPath + "/key2exclude.json";
            if (File.Exists(key2markpath) == false)
                return;
            var key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2markpath));

            List<string> includeKeyList = new List<string>();
            foreach (var gameKey in key2exclude.Keys)
                includeKeyList.Add(gameKey);

            List<string> gameList = new List<string>();
            foreach (var data in gameUpdate.key2_mark_files_url_map)
                gameList.Add(data.GameName);

            foreach (var gameKey in includeKeyList)     // 旧配置下直接删除所有小程序的配置
            {
                var gameValue = new List<string>(key2exclude[gameKey]);
                foreach (var key in gameList)
                {
                    var path = Path.Combine("assets", "AssetBundles", GetManifestName(), key).Replace("\\", "/");
                    if (!gameValue.Contains(path))
                        gameValue.Add(path);
                }
                key2exclude[gameKey] = gameValue.ToArray();
            }
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2exclude));

            // 生成新配置
            var casualgameclude = Application.streamingAssetsPath + "/casualgameclude.json";
            Dictionary<string, string[]> casualgameDict = new Dictionary<string, string[]>();
            foreach (var gameKey in includeKeyList)
            {
                var allclude = new List<string>(key2exclude["All"]);
                foreach (var key in gameList)
                {
                    var path = Path.Combine("assets", "AssetBundles", GetManifestName(), key).Replace("\\", "/");
                    if (key == gameKey)
                    {
                        allclude.Remove(path);
                    }
                }
                casualgameDict.Add(gameKey, allclude.ToArray());
            }
            File.WriteAllText(casualgameclude, ToolUti.ToJson(casualgameDict));
        }

        public static string GetPathUpdate_Assetbundle()
        {
            string AssetbundlePath = "AssetBundles";
            return Path.Combine(Application.streamingAssetsPath, AssetbundlePath, GetManifestName()).Replace("\\", "/");
        }

        public static string GetManifestName()
        {
            var platform = War.Base.BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            return "T" + platform;
        }

        public static string GetCasualGamePath()
        {
            return Path.Combine(Application.streamingAssetsPath, "CasualGame").Replace("\\", "/");
        }

        /// <summary>
        /// 处理文件列表下载（优化的同步方式）
        /// </summary>
        private static void ProcessFilesAsync(CaualFileInfo[] files, string updatePath, string gamekey, string resourceUrl, int maxRetries, System.Action<bool, string> onComplete)
        {
            if (files == null || files.Length == 0)
            {
                onComplete?.Invoke(true, "");
                return;
            }
            try
            {
                for (int i = 0; i < files.Length; i++)
                {
                    var data = files[i];
                    var url = Path.Combine(resourceUrl, gamekey, data.Version.ToString(), data.Name);
                    var file = Path.Combine(updatePath, gamekey, data.Name);
                    Debug.Log($"Processing file {i + 1}/{files.Length}: {data.Name}");
                    bool success = DownloadFileWithCRC32Sync(url, file, data.CRC32, maxRetries);
                    if (!success)
                    {
                        onComplete?.Invoke(false, $"Failed to download {data.Name} after {maxRetries} retries");
                        return;
                    }
                    Debug.Log($"Successfully downloaded and verified: {data.Name}");
                }
                // 所有文件处理完成
                onComplete?.Invoke(true, "");
            }
            catch (System.Exception ex)
            {
                onComplete?.Invoke(false, $"Exception during file processing: {ex.Message}");
            }
        }

        /// <summary>
        /// 同步下载文件并进行CRC32校验，支持重试
        /// </summary>
        private static bool DownloadFileWithCRC32Sync(string url, string filePath, long expectedCRC32, int maxRetries)
        {
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    Debug.Log($"Download attempt {attempt}/{maxRetries} for: {Path.GetFileName(filePath)}");
                    // 确保目录存在
                    var directory = Path.GetDirectoryName(filePath);
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }
                    // 如果文件已存在，先删除
                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                    }
                    // 使用 UnityWebRequest 进行同步下载
                    using (var request = UnityWebRequest.Get(url))
                    {
                        request.downloadHandler = new DownloadHandlerFile(filePath);
                        request.timeout = 30; // 30秒超时
                        var operation = request.SendWebRequest();
                        // 等待下载完成
                        while (!operation.isDone)
                        {
                            System.Threading.Thread.Sleep(50); // 等待50ms
                        }

                        if (request.result == UnityWebRequest.Result.Success)
                        {
                            // 下载成功，验证文件是否存在
                            if (File.Exists(filePath))
                            {
                                // 进行CRC32校验
                                var actualCRC32 = BuildScript.File2CRC32(filePath);
                                if (actualCRC32 == expectedCRC32)
                                {
                                    Debug.Log($"✓ File verified successfully: {Path.GetFileName(filePath)}");
                                    return true; // 成功
                                }
                                else
                                {
                                    Debug.LogWarning($"✗ CRC32 mismatch for {Path.GetFileName(filePath)}. Expected: {expectedCRC32}, Actual: {actualCRC32}");

                                    // 删除损坏的文件
                                    if (File.Exists(filePath))
                                    {
                                        File.Delete(filePath);
                                    }
                                }
                            }
                            else
                            {
                                Debug.LogWarning($"✗ Downloaded file does not exist: {filePath}");
                            }
                        }
                        else
                        {
                            Debug.LogWarning($"✗ Download failed: {request.error}");

                            // 删除可能存在的不完整文件
                            if (File.Exists(filePath))
                            {
                                File.Delete(filePath);
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"✗ Exception during download attempt {attempt}: {ex.Message}");
                    // 删除可能存在的损坏文件
                    if (File.Exists(filePath))
                    {
                        try
                        {
                            File.Delete(filePath);
                        }
                        catch (System.Exception deleteEx)
                        {
                            Debug.LogWarning($"Failed to delete corrupted file: {deleteEx.Message}");
                        }
                    }
                }
                if (attempt < maxRetries)
                {
                    Debug.Log($"Retrying in 1 second... ({maxRetries - attempt} attempts remaining)");
                    System.Threading.Thread.Sleep(1000); // 等待1秒后重试
                }
            }
            Debug.LogError($"✗ Failed to download {Path.GetFileName(filePath)} after {maxRetries} attempts");
            return false;
        }

        // 添加一个菜单项来切换异步/同步模式
        [MenuItem("AssetBundles/CasualGameBuild/Toggle Async Processing")]
        public static void ToggleAsyncProcessing()
        {
            bool currentValue = EditorPrefs.GetBool("CasualGameBuild_UseAsync", false);
            bool newValue = !currentValue;
            EditorPrefs.SetBool("CasualGameBuild_UseAsync", newValue);
            Debug.Log($"CasualGameBuild: {(newValue ? "启用" : "禁用")}异步处理模式");
        }

        // 添加一个菜单项来显示当前模式
        [MenuItem("AssetBundles/CasualGameBuild/Check Current Processing Mode")]
        public static void CheckCurrentProcessingMode()
        {
            bool isAsync = EditorPrefs.GetBool("CasualGameBuild_UseAsync", false);
            Debug.Log($"CasualGameBuild: 当前使用{(isAsync ? "异步" : "同步")}处理模式");
        }
    }
}
