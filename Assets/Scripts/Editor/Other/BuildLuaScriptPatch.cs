
using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using UnityEditor.Sprites;
using War.Base;
using System.Linq;
using FtpTest1;
using ICSharpCode.SharpZipLib.Zip;
using CLeopardZip;
using System.Text;
using System.Text.RegularExpressions;

using War.Common;
using XLua.LuaDLL;
using Sirenix.OdinInspector.Demos;
using Random = System.Random;
using System.Net;
using Newtonsoft.Json;
using System.Runtime.Serialization.Formatters.Binary;
using Process = System.Diagnostics.Process;
using XLua;

public class BuildLuaScriptPatch
{

    public static int luaPatchCurVersion = 0;
    private static string tableExecMark = "exec_t_";
    private static string otherExecMark = "exec_o_";
    private static string tablePatchExecMark = "exec_tp_";
    private static string pbExecMark = "exec_pb_";
    public const string patch_misc_ver_key = "patch_misc_ver";
    //旧版exec开关
    private static bool isUseOldExec = false;

    static int version_of_check_lua_patch
    {
        get { return JenkinsEnv.Instance.GetInt("version_of_check_lua_patch", 0); }
    }

    private static string lua_c_path = Application.dataPath + "/../Tools/luac5.1.exe";
#if UNITY_EDITOR_OSX
    private static string luajit_c_path = Application.dataPath + "/../Tools/luajit/bin/osx64/luajit";
    private static string luajit_c_32_path = Application.dataPath + "/../Tools/luajit/bin/osx32/luajit-32";
    private static string protoc_path = Application.dataPath + "/../../../Tools/protobuf/protoc-mac";
#elif UNITY_EDITOR_WIN
    private static string luajit_c_path = Application.dataPath + "/../Tools/luajit/bin/mingw64/luajit.exe";
    private static string luajit_c_32_path = Application.dataPath + "/../Tools/luajit/bin/mingw32/luajit-32.exe";
    private static string protoc_path = Application.dataPath + "/../../../Tools/protobuf/protoc.exe";
#endif
    private static string LuaBytesFullPostfix = "_luaBytes.bytes";
    private static string LuaPBFileFullPostfix = "_pb_bin.bytes";

    /// <summary>
    /// 是否移除Recharge表
    /// 判断gameconfig中ENABLE_RECHARGE_CHANNEL_TABLE是否为true,是否使用新支付方式
    /// </summary>
    /// <returns>是否开启了Recharge表的移除</returns>
    private static Boolean IsEnableRemoveRechargeTables()
    {
        // return IsGetParamsValueByKey("ENABLE_RECHARGE_CHANNEL_TABLE");
        return true;
    }

    /// <summary>
    /// 根据传过来的key拿到打包参数中的value值
    /// </summary>
    /// <returns>布尔类型的值</returns>
    private static Boolean IsGetParamsValueByKey (string key)
    {
// #if !UNITY_WEBGL
//         return false;
// #endif

        String pJenkinsAppendParams = "../../Tools/GameConfig/custom/custom_jenkins.json";
        if (!File.Exists(pJenkinsAppendParams))
        {
            return false;
        }

        Dictionary<String, System.Object> jenkinsAppendParamsDict = (Dictionary<String, System.Object>)MiniJSON.Json.Deserialize(File.ReadAllText(pJenkinsAppendParams));
        String enabledStr = BuildScript.GetBuildConfig(jenkinsAppendParamsDict, key);
        Boolean enabled = ("True" == enabledStr);

        return enabled;
    }


    /// <summary>
    /// 指定路径的Lua脚本是否满足Recharge表的移除条件
    /// 移除Assets/Lua/Tables下的 Recharge、Recharge_n、Recharge_M_n 表
    /// </summary>
    /// <param name="pLua"></param>
    /// <returns>是否移除Lua脚本</returns>
    private static Boolean IsRechargeTableRemoveable(String pLua)
    {
        Boolean remove = Regex.IsMatch(pLua, ".+(/|\\\\?)Tables(/|\\\\?).+");
        if (!remove)
        {
            return remove;
        }

        remove = remove && Regex.IsMatch(pLua, ".+(/|\\\\?)Recharge(_[a-zA-Z]+)?(_[0-9a-zA-Z]+)?\\.txt$");

        if (remove)
        {
            LogHelp.Instance.Log($"[BUILD][LUA][TABLES]BuildLuaScriptPatch.() : remove lua table {pLua}.");
        }

        return remove;
    }

    public static bool LuaToBinary(bool isLuaJIT, string originalFile, out byte[] binaryFile, out byte[] binary32File,out string binary32FilePath, Dictionary<string, byte[]> bomFiles)
    {
        
        string folder = Path.GetDirectoryName(originalFile);
        folder = folder.Replace("\\", "/");
        folder = Path.Combine(Application.dataPath, folder.Substring(7));//Remove "Assets/"

        string fileName = Path.GetFileNameWithoutExtension(originalFile);
        string originalFilePath = Path.Combine(folder, fileName + ".txt");
        
        binary32FilePath = Path.Combine(folder, fileName + LuaBytesFullPostfix);

        //remove bom
        byte[] oldBytes = File.ReadAllBytes(originalFile);
        if (oldBytes.Length == 0)
        {
            Debug.LogWarningFormat("LD: LuaToBinary empty file {0}", originalFile);
        }
        bomFiles[originalFile] = oldBytes;
        if (oldBytes.Length > 2 && oldBytes[0] == 0xEF && oldBytes[1] == 0xBB && oldBytes[2] == 0xBF)
        {
            FileStream fs = new FileStream(originalFile, FileMode.Truncate, FileAccess.Write);
            fs.Write(oldBytes, 3, (int)oldBytes.Length - 3);
            fs.Close();
        }

        bool success = false;
        try
        {
            Process process = new Process();
#if UNITY_EDITOR_OSX
            process.StartInfo.FileName = "/bin/bash";
#else
            process.StartInfo.FileName = "cmd.exe";
#endif
            process.StartInfo.CreateNoWindow = true;
            process.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.RedirectStandardError = true;
            process.StartInfo.StandardErrorEncoding = Encoding.UTF8;
            process.StartInfo.RedirectStandardInput = true;
            process.StartInfo.StandardOutputEncoding = Encoding.UTF8;
            process.StartInfo.UseShellExecute = false;
            //设置工作目录
            process.StartInfo.WorkingDirectory = folder;

            process.Start();

            if (isLuaJIT)
            {
                //直接替换原始文件, 打包结束后通过bomFile还原
                //设置工作目录后, 仅使用文件名, 避免文件完整路径被写入调试信息
                //编译32位字节码使用的工具依赖jit的一些Lua模块, 设置工作目录后会导致这些模块无法找到, 只能通过package.path的方法添加上去
                string jitFolder = Path.GetDirectoryName(luajit_c_32_path).Replace('\\', '/');
                string cmdLine = luajit_c_32_path;
                cmdLine += " -e ";
                cmdLine += "\"package.path = package.path .. ';' .. '" + jitFolder + "/lua/jit/?.lua' ";
                cmdLine += " local bcsave = require 'bcsave' bcsave.start('-gW','" + fileName + ".txt" + "','" + fileName + LuaBytesFullPostfix + "') \"";
                process.StandardInput.WriteLine(cmdLine);

                //编译64位字节码
                process.StandardInput.WriteLine(luajit_c_path + " -bg " + fileName + ".txt " + fileName + ".txt");
            }
            else
            {
                process.StandardInput.WriteLine(luajit_c_32_path + " -bg -W " + fileName + LuaBytesFullPostfix + " " + fileName + ".txt");
                process.StandardInput.WriteLine(lua_c_path + " -o " + fileName + ".txt " + fileName + ".txt");
            }

            process.StandardInput.WriteLine("exit");
            process.StandardInput.AutoFlush = true;

            string errorOutput = process.StandardError.ReadToEnd();
            if (!string.IsNullOrEmpty(errorOutput)) 
            {
                Debug.LogErrorFormat("LD: cmdLine failed: " + errorOutput);
            }
            process.WaitForExit();
            process.Close();

            if (File.Exists(originalFilePath) && File.Exists(binary32FilePath))
            {
                //Debug.LogFormat("LD: check compile file success: {0}", originalFilePath);
                binaryFile = File.ReadAllBytes(originalFilePath);
                binary32File = File.ReadAllBytes(binary32FilePath);
                success = true;
            }
            else
            {
                Debug.LogFormat("LD: check compile file not exsit: {0}", originalFilePath);
                binaryFile = new byte[0];
                binary32File = new byte[0];
            }
        }
        catch (Exception e)
        {
            Debug.LogError("LD: LuaToBinary compile failed: " + e.Message);
            binaryFile = new byte[0];
            binary32File = new byte[0];
        }
        return success;
    }

    private static string pbParse = @"
            local File = CS.System.IO.File
            local Path = CS.System.IO.Path
            local protoc = require 'protoc' 
            local p = protoc.new()
            p.unknown_import = function(self, module_name) print('unknown import',module_name) end
            p.unknown_type = function(self, type_name) print('unknown type',type_name) end
            p.include_imports = false
            
            for _,searchPath in ipairs(protoSearchPaths) do
                p:addpath(searchPath)
            end

            for _,protoFile in ipairs(protoList) do
                print('start compile',protoFile)
                local protoContent = File.ReadAllText(protoFile)
                local fileName = Path.GetFileNameWithoutExtension(protoFile)
                local res = p:compile(protoContent,fileName)
                local dest = Path.Combine(destFolder, fileName .. pbFilePostfix);
                File.WriteAllBytes(dest,res)
                p:reset()
            end
            print('Generate Success')
        ";

    [MenuItem("AssetBundles/GeneratePB_Lua")]
    public static void GeneratePB_Lua()
    {
        string[] protoFolders = { "Assets/../../../Proto/Message", "Assets/../../../Proto/Tbs" };
        string destFolder = "Assets/Lua/protomsg_new";
        string protocPath = "Assets/../../../Proto/protoc.lua";
        LuaEnv tempEnv = new LuaEnv();

        //force open pb_new lib when generating pb
        Lua.luaopen_pb_new(tempEnv.L);
        try
        {
            if (!Directory.Exists(destFolder))
            {
                Directory.CreateDirectory(destFolder);
            }
            string[] oldFiles = Directory.GetFiles(destFolder, "*_pb_bin.bytes");
            foreach (string file in oldFiles)
            {
                File.Delete(file);
            }

            tempEnv.AddLoader((ref string filepath) =>
            {
                if (filepath == "protoc")
                {
                    if (File.Exists(protocPath))
                    {
                        return File.ReadAllBytes(protocPath);
                    }
                    else
                    {
                        return null;
                    }
                }
                else
                {
                    return null;
                }
            });

            StringBuilder sb = new StringBuilder();
            StringEx.GetluaStack = () =>
            {
                if (tempEnv == null)
                {
                    return "";
                }
                var objs = tempEnv.DoString("return debug.traceback()");
                sb.Clear();
                if (objs != null)
                    for (int i = 0; i < objs.Length; i++)
                    {
                        sb.Append(objs[i]);
                    }
                return sb.ToString();
            };
            LuaTable protoList = tempEnv.NewTable();
            LuaTable protoSearchPaths = tempEnv.NewTable();

            int fileCounter = 1;
            for (int folderIndex = 0; folderIndex < protoFolders.Length; ++folderIndex)
            {
                string[] allProto = Directory.GetFiles(protoFolders[folderIndex], "*.proto");

                protoSearchPaths.Set(folderIndex + 1, Path.GetFullPath(protoFolders[folderIndex]));

                for (int i = 0; i < allProto.Length; ++i)
                {
                    protoList.Set(fileCounter, allProto[i]);
                    fileCounter++;
                }
            }
            tempEnv.Global.Set("protoList", protoList);
            tempEnv.Global.Set("destFolder", destFolder);
            tempEnv.Global.Set("protoSearchPaths", protoSearchPaths);
            tempEnv.Global.Set("pbFilePostfix", LuaPBFileFullPostfix);
            tempEnv.DoString(pbParse);
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
        }

        tempEnv.Dispose();
    }

    [MenuItem("AssetBundles/TestExportEffect")]
    public static void TestExportEffect()
    {
        Sirenix.OdinInspector.Demos.AutoExportEffectAssets.ExportRes();
    }
    [MenuItem("AssetBundles/GeneratePB")]
    public static void GeneratePB()
    {
        string[] protoFolders = { Application.dataPath + "/../Assets/../../../Proto/Message", Application.dataPath + "/../Assets/../../../Proto/Tbs" };
        string destFolder = Application.dataPath + "/../Assets/Lua/protomsg_new";

        string[] oldPbFile = Directory.GetFiles(destFolder, "*.bytes");
        foreach(string pbFile in oldPbFile)
        {
            File.Delete(pbFile);
        }

#if UNITY_EDITOR_OSX
        try
        {
            Process process = new Process();
            System.Diagnostics.ProcessStartInfo startInfo;
            startInfo = new System.Diagnostics.ProcessStartInfo("/bin/bash", "-c \"chmod +x " + protoc_path + "\"");

            startInfo.CreateNoWindow = true;
            startInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
            startInfo.RedirectStandardOutput = true;
            startInfo.RedirectStandardError = true;
            startInfo.UseShellExecute = false;
            process.StartInfo = startInfo;

            process.Start();
            process.WaitForExit();
        }
        catch (Exception e)
        {
            Debug.LogError("LD: pb privilege failed: " + e.Message);
        }
#endif

        foreach (string protoFolder in protoFolders)
        {
            string[] protoFiles = Directory.GetFiles(protoFolder, "*.proto");
            foreach(string protoFile in protoFiles)
            {
                try
                {
                    Process process = new Process();
#if UNITY_EDITOR_OSX
                    process.StartInfo.FileName = "/bin/bash";
#elif UNITY_EDITOR_WIN
                    process.StartInfo.FileName = "cmd.exe";
#endif
                    process.StartInfo.CreateNoWindow = true;
                    process.StartInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.RedirectStandardError = true;
                    //process.StartInfo.StandardErrorEncoding = Encoding.UTF8;
                    process.StartInfo.RedirectStandardInput = true;
                    //process.StartInfo.StandardOutputEncoding = Encoding.UTF8;
                    process.StartInfo.UseShellExecute = false;

                    process.Start();
                    string outFileName = destFolder + "/" + Path.GetFileNameWithoutExtension(protoFile) + LuaPBFileFullPostfix;

#if UNITY_EDITOR_OSX
                    string cmdLine = string.Format("{0} {1} --proto_path={2} --proto_path={3} --descriptor_set_out={4}", protoc_path, protoFile, protoFolders[0], protoFolders[1], outFileName);
#elif UNITY_EDITOR_WIN
                    string cmdLine = string.Format("{0} {1} --proto_path={2} --proto_path={3} --descriptor_set_out={4}", protoc_path, protoFile, protoFolders[0], protoFolders[1], outFileName);
#endif
                    process.StandardInput.WriteLine(cmdLine);
                    process.StandardInput.WriteLine("exit");
                    process.StandardInput.AutoFlush = true;


                    string errorOutput = process.StandardError.ReadToEnd();
                    if (!string.IsNullOrEmpty(errorOutput))
                    {
                        Debug.Log("LD: pb cmdLine info: " + errorOutput);
                    }

                    process.WaitForExit();
                    process.Close();
                }
                catch (Exception e)
                {
                    Debug.LogError(e.ToString());
                }
            }
        }
    }
#if UNITY_EDITOR_OSX
    private static void MakeJITExecutable()
    {
        try
        {
            System.Diagnostics.Process process = new System.Diagnostics.Process();
            System.Diagnostics.ProcessStartInfo startInfo;
            startInfo = new System.Diagnostics.ProcessStartInfo("/bin/bash", "-c \"chmod +x " + luajit_c_path + " " + luajit_c_32_path + "\"");

            startInfo.CreateNoWindow = true;
            startInfo.WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden;
            startInfo.RedirectStandardOutput = true;
            startInfo.RedirectStandardError = true;
            startInfo.UseShellExecute = false;
            process.StartInfo = startInfo;

            process.Start();
            process.WaitForExit();
        }
        catch (Exception e)
        {
            Debug.LogError("LD: LuaToBinary privilege failed: " + e.Message);
        }
    }
#endif

    /// <summary>
    /// 生成luascript,exec,manifest
    /// </summary>
    /// <param name="DictLuaBs">指定文件夹下 -> <文件名,文件数据块> 数据对 </文件名></param>
    /// <param name="stripLog">是否剔除日志相关代码</param>
    public static void BuildLuascriptVice(Dictionary<string, byte[]> DictLuaBs, bool stripLog = true)
    {
        var path = Application.dataPath + "/Scripts/GameUpdate/Plugins/_luascript.json";
        if (!File.Exists(path))
        {
            return;
        }

        if (JenkinsEnv.Instance.GetBool("test_delete_exec", false))
        {
            var exec_path = "Assets/Lua/exec";
            if (Directory.Exists(exec_path))
            {
                "".Print("test_delete_exec", exec_path);

                Directory.Delete(exec_path, true);
            }
        }

        LogHelp.Instance.Log("BuildLuascriptVice-start");

        if (JenkinsEnv.Instance.GetBool("USE_PACKAGE_COPY", false))
        {
            // 获取包里配置的模块
            LuaPackageLoader.InitLuaPackage();
        }

        var LuaPath = "Assets/Lua";
        string[] luaFiles = Directory.GetFiles(LuaPath + "/", "*.txt", SearchOption.AllDirectories);

        if (!JenkinsEnv.Instance.GetBool("USE_PACKAGE_COPY", false))
        {
            luaFiles = LuaPackageLoader.LuaFileReplace(luaFiles);
        }

        Boolean removeTableRecharge = IsEnableRemoveRechargeTables();
        LogHelp.Instance.Log($"[BUILD][LUA][TABLES]BuildLuaScriptPatch.BuildLuascriptVice() : ENABLE_REMOVE_RECHARGE_TABLE={removeTableRecharge}.");

        bool isLuaToBinary = JenkinsEnv.Instance.GetBool("lua_to_binary", false);
        bool isLuaJITCompiler = JenkinsEnv.Instance.GetBool("lua_jit_compiler", true);

        //记录生成的字节码文件, 打包完毕后删除
        List<string> luaBytesFiles = new List<string>();

        //记录原始lua文件名到编译后的字节码文件路径的映射
        Dictionary<string, string> fileNameToBytesPath = new Dictionary<string, string>();

        //编译字节码需要先清除bom标记, 此处记录下来用于还原bom标记
        Dictionary<string, byte[]> bomFiles = new Dictionary<string, byte[]>();

        bool isPBNew = true;//JenkinsEnv.Instance.GetBool("lua_protobuf_new", false);

#if UNITY_EDITOR_OSX
        if (isLuaToBinary && isLuaJITCompiler)
        {
            MakeJITExecutable();
        }
#endif

        if (DictLuaBs == null)
        {
            DictLuaBs = new Dictionary<string, byte[]>();
            foreach (var luaFile in luaFiles)
            {
                if (IsLangLuaRemoveable(luaFile))
                {
                    continue;
                }
                if (removeTableRecharge)
                {
                    if (IsRechargeTableRemoveable(luaFile))
                    {
                        
                        continue;
                    }
                }

                byte[] bytes;
                if (isLuaToBinary)
                {
                    //build original lua bytes file
                    string binary32FilePath;
                    byte[] bytes32;
                    if (LuaToBinary(isLuaJITCompiler, luaFile, out bytes, out bytes32, out binary32FilePath, bomFiles))
                    {
                        binary32FilePath = binary32FilePath.Substring(binary32FilePath.IndexOf("Assets"));
                        luaBytesFiles.Add(binary32FilePath);
                        DictLuaBs.Add(binary32FilePath, bytes32); 
                        fileNameToBytesPath[Path.GetFileNameWithoutExtension(luaFile)] = binary32FilePath;
                    }
                    else
                    {
                        Debug.LogError("LD: LuaToBinary failed: " + luaFile);
                    }
                }
                else
                {
                    var fstr = System.IO.File.ReadAllText(luaFile, System.Text.Encoding.UTF8);
                    bytes = System.Text.Encoding.UTF8.GetBytes(fstr);
                }
                DictLuaBs.Add(luaFile, bytes);
            }
        }
        else
        {
            if (removeTableRecharge)
            {
                Dictionary<String, Byte[]> dictWithoutTableRecharge = new Dictionary<String, Byte[]>(DictLuaBs.Count);
                foreach (var item in DictLuaBs)
                {
                    if (IsRechargeTableRemoveable(item.Key))
                    {
                        continue;
                    }

                    dictWithoutTableRecharge.Add(item.Key, item.Value);
                }

                DictLuaBs = dictWithoutTableRecharge;
            }
        }
        
        //剔除不需要的文件，剔除压缩等处理
        //DictLuaBs = BuildSimplifyLua.ProcessLuaDictWithLuallex(DictLuaBs);
        var bCompressLua = JenkinsEnv.Instance.GetBool("enablue_compress_lua", true);
        DictLuaBs = BuildSimplifyLua.ProcessLuaDictWithLuaSrcDiet(DictLuaBs, bCompressLua, stripLog);
        LogHelp.Instance.Log($"BuildLuascriptVice-bCompressLua { bCompressLua }");
        LogHelp.Instance.Log("BuildLuascriptVice-start1");

        var json = File.ReadAllText(path);
        var lusConfig = UIHelper.ToObj<LuaScriptConfig>(json);
        var dicLua = lusConfig.GetLuaDic();

        var listLuaInZip = new Dictionary<string, bool>();
        var listLuaOutOfZip = new Dictionary<string, bool>();
        foreach (var item in DictLuaBs)
        {
            if (item.Key.EndsWith(LuaBytesFullPostfix))
            {
                continue;
            }
            //Debug.Log(item.Key);
            var filename = Path.GetFileNameWithoutExtension(item.Key);
            
            if (dicLua.ContainsKey(filename))
            {
                string itemPath = item.Key.Replace("\\", "/");
                string luacompressPath = itemPath.Replace("/Lua/", "/LuaCompressed/");
                if (bCompressLua && File.Exists(luacompressPath))
                {
                    listLuaInZip[luacompressPath] = true;
                }
                else 
                {
                    listLuaInZip[item.Key] = true;
                }

                if (isLuaToBinary)
                {
                    listLuaInZip[fileNameToBytesPath[filename]] = true;
                }
            }
            else
            {
                listLuaOutOfZip[item.Key] = true;

                if (isLuaToBinary)
                {
                    listLuaOutOfZip[fileNameToBytesPath[filename]] = true;
                }
            }
        }

        if (isPBNew)
        {
            var binaryPBPath = "Assets/Lua/protomsg_new/";
            if (!Directory.Exists(binaryPBPath))
            {
                Directory.CreateDirectory(binaryPBPath);
                GeneratePB();
            }
            
            string[] binaryPBFiles = Directory.GetFiles(binaryPBPath, "*.bytes", SearchOption.AllDirectories);
            Debug.LogFormat("pb files count: {0}", binaryPBFiles.Length);
            foreach (string file in binaryPBFiles) 
            {
                var pbName = Path.GetFileNameWithoutExtension(file);
                pbName = pbName.Substring(0, pbName.Length - 4);
                if (dicLua.ContainsKey(pbName))
                {
                    listLuaInZip[file] = true;
                }
                else
                {
                    listLuaOutOfZip[file] = true;
                }
                DictLuaBs[file] = File.ReadAllBytes(file);
            }
        }


        Debug.Log(UIHelper.ToJson(listLuaInZip));
        Debug.Log(UIHelper.ToJson(dicLua));
        LogHelp.Instance.Log("BuildLuascriptVice-start2");

        System.Func<string, int> getPathNameV = delegate (string s)
        {
            int length = s.Length;
            var num = 0;
            for (int i = 0; i < length; i++)
            {
                num += s[i];
            }

            return num;
        };

        var listAssets = new Dictionary<string, List<string>>();
        var listAssetsOld = new List<List<string>>();
        var listMoveFileName = new List<string>();
        var listToImport = new List<string>();
        var splitCount = GetExecNameConfig().Length;
        int tableListCount = GetExecCountByType(tableExecMark);
        int otherListCount = GetExecCountByType(otherExecMark);
        int tablePatchCount = GetExecCountByType(tablePatchExecMark);
        int pbListCount = GetExecCountByType(pbExecMark);
        //var rktest = ScriptableObject.CreateInstance<ResKey>();
        if (isUseOldExec)
        {
            splitCount = 5;
            for (int i = 0; i < splitCount; i++)
            {
                listAssetsOld.Add(new List<string>());
            }
        }
        else
        {
            for (int i = 0; i < tableListCount; i++)
            {
                listAssets.Add(tableExecMark + i, new List<string>());
            }

            for (int i = 0; i < otherListCount; i++)
            {
                listAssets.Add(otherExecMark + i, new List<string>());
            }

            for (int i = 0; i < tablePatchCount; i++)
            {
                listAssets.Add(tablePatchExecMark + i, new List<string>());
            }

            if (isPBNew)
            {
                for (int i = 0; i < pbListCount; i++)
                {
                    listAssets.Add(pbExecMark + i, new List<string>());
                }
            }
        }
        

        int t = 0;
        int n = 0;
        int m = 0;
        long tableListBytes = 0;
        long tablePatchListBytes = 0;
        long pbListBytes = 0;
        long otherListBytes = 0;
        foreach (var l in listLuaOutOfZip.Keys)
        {
            string luaTablePath = l.Replace("\\","/");
            if (isUseOldExec)
            {
                if (luaTablePath.Contains(ConstPaths.luaTableAbMark))
                {
                    var a = getPathNameV(l);
                    listAssetsOld[a % 3 + 2].Add(l);

                }
                else
                {
                    var a = getPathNameV(l);
                    listAssetsOld[a % 2].Add(l);
                    //LogHelp.Instance.Log($"listLuaOutOfZip:{l}:{n}");
                }
            }
            else
            {
                if (!File.Exists(luaTablePath)) continue;

                string fstr = "";
                byte[] fbytes = null;
                if (luaTablePath.EndsWith(LuaBytesFullPostfix) || luaTablePath.EndsWith(LuaPBFileFullPostfix))
                {
                    fbytes = File.ReadAllBytes(luaTablePath);
                }
                else
                {
                    fstr = System.IO.File.ReadAllText(luaTablePath, System.Text.Encoding.UTF8);
                }

                if (luaTablePath.Contains(ConstPaths.luaTableAbMark) && !luaTablePath.Contains(ConstPaths.luaTablePatchAbMark))
                {     
                    if (luaTablePath.EndsWith(LuaBytesFullPostfix))
                    {
                        tableListBytes += fbytes.Length;
                    }
                    else
                    {
                        tableListBytes += System.Text.Encoding.UTF8.GetBytes(fstr).Length;
                    }
                    bool tableisOver2M = GetLuaListOfOutSize(t,tableListBytes);
                    if (tableisOver2M && t < tableListCount - 1)
                    {
                        t += 1;
                    }
                    listAssets[tableExecMark + t].Add(l);
                    //Debug.LogWarning("tableisOver2M:" + tableisOver2M + "  t:" + t + "   tableListBytes:" + tableListBytes / (1024 * 1024));
                }
                else if (luaTablePath.Contains(ConstPaths.luaTablePatchAbMark))
                {
                    if (luaTablePath.EndsWith(LuaBytesFullPostfix))
                    {
                        tablePatchListBytes += fbytes.Length;
                    }
                    else
                    {
                        tablePatchListBytes += System.Text.Encoding.UTF8.GetBytes(fstr).Length;
                    }
                    bool tablePatchisOver2M = GetLuaListOfOutSize(n,tablePatchListBytes);
                    if (tablePatchisOver2M && n < tablePatchCount - 1)
                    {
                        n += 1;
                    }
                    listAssets[tablePatchExecMark + n].Add(l);
                    //Debug.LogWarning("tablePatchisOver2M:" + tablePatchisOver2M + "    n:" + n + "    tablePatchListBytes:" + tablePatchListBytes / (1024 * 1024));
                }
                else if (luaTablePath.Contains(ConstPaths.luaBinaryPBABMark))
                {
                    if (isPBNew)
                    {
                        pbListBytes += fbytes.Length;
                    }
                    listAssets[pbExecMark + 0].Add(l);
                }
                else
                {
                    if (luaTablePath.EndsWith(LuaBytesFullPostfix))
                    {
                        otherListBytes += fbytes.Length;
                    }
                    else
                    {
                        otherListBytes += System.Text.Encoding.UTF8.GetBytes(fstr).Length;
                    }
                    bool otherisOver2M = GetLuaListOfOutSize(m, otherListBytes);
                    if (otherisOver2M && m < otherListCount - 1)
                    {
                        m += 1;
                    }
                    listAssets[otherExecMark + m].Add(l);
                    //Debug.LogWarning("otherisOver2M:" + otherisOver2M + "    m:" + m + "    otherListBytes:" + otherListBytes / (1024 * 1024));
                }
            }
            
        }

        LogHelp.Instance.Log("BuildLuascriptVice-start3");

        //删除多余的lua/exec目录下文件和assetbundles目录下exec文件及相关manifest文件，避免新旧重复 
        string folderPath  = LuaPath + "/exec";
        string delExecPath = "";
        string delABExecPath = "";
        string sourceFilePath = "";
        string delExecPathManifest = "";
        string sourceABManifestFilePath = "";
        string delABFileExecPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        if(isUseOldExec)
        {
            //删除新版exec生成的文件
            var execNameConfig = GetExecNameConfig();
            for (int i = 0; i < execNameConfig.Length; i++)
            {
                delExecPath = execNameConfig[i] + ".asset";
                DeleteExecByFileName(folderPath,delExecPath);
                delABExecPath = execNameConfig[i] + ".asset";
                delExecPathManifest = execNameConfig[i] + ".asset.manifest";
                DeleteExecByFileName(delABFileExecPath,delABExecPath);
                DeleteExecByFileName(delABFileExecPath,delExecPathManifest);    
            }
        }
        else
        {
            for (int i = 0; i < 5; i++)
            {
                delExecPath = "exec" + i + ".asset";
                DeleteExecByFileName(folderPath,delExecPath);
                delABExecPath = "exec" + i + ".asset";
                delExecPathManifest = "exec" + i + ".asset.manifest";
                DeleteExecByFileName(delABFileExecPath,delABExecPath);
                DeleteExecByFileName(delABFileExecPath,delExecPathManifest);
            }
        }

        // 已经处理过此模块名,如果冲突,则可能是因为存在同名文件，需要手动处理
        Dictionary<string, string> existModuleSet = new Dictionary<string, string>();
        Dictionary<string, string> outZipSet = new Dictionary<string, string>();

        if(isUseOldExec)
        {
            for (int i = 0; i < listAssetsOld.Count; i++)
            {
                var lll = listAssetsOld[i];

                // exec asset 
                var exec_file_name = "exec" + i + ".asset";
                var exc_asset_path = LuaPath + "/exec/" + exec_file_name;
                if (File.Exists(exc_asset_path))
                {
                    File.Delete(exc_asset_path);
                }

                if (!System.IO.File.Exists(exc_asset_path))
                {
                    EditorHelp.CheckDir(exc_asset_path);
                    var rk = ScriptableObject.CreateInstance<ResKey>();
                    rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
                    UnityEditor.AssetDatabase.CreateAsset(rk, exc_asset_path);
                }

                string modlePath;

                var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(exc_asset_path);
                Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();

                foreach (ResKey.KeyValuePair p in rk1.keys)
                {
                    dicKeys[p.key] = p;
                }

                //rk1.keys.Clear();
                var bDirty = false;
                foreach (var item in lll)
                {
                    var filename = Path.GetFileNameWithoutExtension(item);
                    if (!outZipSet.ContainsKey(filename))
                    {
                        outZipSet[filename] = exec_file_name;
                    }

                    if (existModuleSet.TryGetValue(filename, out modlePath))
                    {
                        Debug.LogError("Exist the same file:" + modlePath + "\n" + item);
                    }
                    else
                    {
                        existModuleSet[filename] = item;
                    }

                    ResKey.KeyValuePair p = null;
                    if (dicKeys.TryGetValue(filename, out p))
                    {
                        if (!CompareBytes(p.value, DictLuaBs[item]))
                        {
                            p.value = DictLuaBs[item];
                            bDirty = true;
                        }
                    }
                    else
                    {
                        rk1.keys.Add(new ResKey.KeyValuePair()
                        {
                            key = filename,
                            value = DictLuaBs[item]
                        });
                        bDirty = true;
                    }
                }

                var listRm = new List<ResKey.KeyValuePair>();
                foreach (var ks in rk1.keys)
                {
                    var k = ks.key;
                    //仅判断不包含情况，包含情况会自动刷新，不同exec重复不需处理
                    if (!outZipSet.ContainsKey(k) || outZipSet[k] != exec_file_name)
                    {
                        listRm.Add(ks);
                    }
                }

                foreach (var k in listRm)
                {
                    bDirty = true;
                    rk1.keys.Remove(k);
                }

                "".Print("RemoveUnused from ", exec_file_name,
                    UIHelper.ToJson(listRm.ConvertAll((o) => o.key).ToArray()));


                var ai = AssetImporter.GetAtPath(exc_asset_path);
                if (ai)
                {
                    ai.assetBundleName = exec_file_name;
                    listToImport.Add(exc_asset_path);
                }

                if (bDirty)
                {
                    EditorUtility.SetDirty(rk1);
                }
            }
        }
        else
        {
            foreach (var execName in listAssets)
            {
                var lll = execName;
                var exec_file_name = lll.Key + ".asset";

                // exec asset 
                
                var exc_asset_path = LuaPath + "/exec/" + exec_file_name;
                
                
                if (File.Exists(exc_asset_path))
                {
                    File.Delete(exc_asset_path);
                }
                
                if (!System.IO.File.Exists(exc_asset_path))
                {
                    EditorHelp.CheckDir(exc_asset_path);
                    var rk = ScriptableObject.CreateInstance<ResKey>();
                    rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
                    UnityEditor.AssetDatabase.CreateAsset(rk, exc_asset_path);
                }

                string modlePath;

                var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(exc_asset_path);
                Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();

                foreach (ResKey.KeyValuePair p in rk1.keys)
                {
                    dicKeys[p.key] = p;
                }

                //rk1.keys.Clear();
                var bDirty = false;
                foreach (var item in lll.Value)
                {
                    var filename = Path.GetFileNameWithoutExtension(item);
                    if (!outZipSet.ContainsKey(filename))
                    {
                        outZipSet[filename] = exec_file_name;
                    }

                    if (existModuleSet.TryGetValue(filename, out modlePath))
                    {
                        Debug.LogError("Exist the same file:" + modlePath + "\n" + item);
                    }
                    else
                    {
                        existModuleSet[filename] = item;
                    }

                    ResKey.KeyValuePair p = null;
                    if (dicKeys.TryGetValue(filename, out p))
                    {
                        if (!CompareBytes(p.value, DictLuaBs[item]))
                        {
                            p.value = DictLuaBs[item];
                            bDirty = true;
                        }
                    }
                    else
                    {
                        rk1.keys.Add(new ResKey.KeyValuePair()
                        {
                            key = filename,
                            value = DictLuaBs[item]
                        });
                        bDirty = true;
                    }
                }

                var listRm = new List<ResKey.KeyValuePair>();
                foreach (var ks in rk1.keys)
                {
                    var k = ks.key;
                    //仅判断不包含情况，包含情况会自动刷新，不同exec重复不需处理
                    if (!outZipSet.ContainsKey(k) || outZipSet[k] != exec_file_name)
                    {
                        listRm.Add(ks);
                    }
                }

                foreach (var k in listRm)
                {
                    bDirty = true;
                    rk1.keys.Remove(k);
                }

                "".Print("RemoveUnused from ", exec_file_name,
                    UIHelper.ToJson(listRm.ConvertAll((o) => o.key).ToArray()));


                var ai = AssetImporter.GetAtPath(exc_asset_path);
                if (ai)
                {
                    ai.assetBundleName = exec_file_name;
                    listToImport.Add(exc_asset_path);
                }

                if (bDirty)
                {
                    EditorUtility.SetDirty(rk1);
                }
            }
        }
        

        var ouzipPath = "Assets/Log/OutZip.json";
        EditorHelp.CheckDir(ouzipPath);
        File.WriteAllText(ouzipPath, UIHelper.ToJson(outZipSet));

        LogHelp.Instance.Log("BuildLuascriptVice-start4");

        AssetDatabase.SaveAssets();
        LogHelp.Instance.Log("BuildLuascriptVice-start5");


        string[] luaFiles2 =
            AssetDatabase.GetAssetPathsFromAssetBundle(War.Script.LuaManager.LuaScriptAssetBundleName);
        foreach (var f in luaFiles2)
        {
            var ai = AssetImporter.GetAtPath(f);
            if (ai)
            {
                var fn = Path.GetFileNameWithoutExtension(f);

                if (!string.IsNullOrEmpty(ai.assetBundleName))
                {
                    ai.assetBundleName = "";
                    listToImport.Add(f);
                }
            }
        }

        LogHelp.Instance.Log("BuildLuascriptVice-start6");
        Debug.Log("listToImport");
        Debug.Log(UIHelper.ToJson(listToImport));
        UIHelper.ImportAssets(listToImport.ToArray());
        AssetDatabase.Refresh();


        //return;
        /// 打包生成的_luascript
        var buildmap = new AssetBundleBuild[1 + splitCount];
        var abd = new AssetBundleBuild();
        string out_abname = War.Script.LuaManager.LuaScriptAssetBundleName;
        abd.assetBundleName = out_abname;
        abd.assetNames = listLuaInZip.Keys.ToArray();
        buildmap[0] = abd;
        var execNameList = GetExecNameConfig();
        for (int i = 0; i < splitCount; i++)
        {
            var abd2 = new AssetBundleBuild();
            string out_exe_abname = execNameList[i] + ".asset";
            var exc_asset = LuaPath + "/exec/" + execNameList[i] + ".asset";
            if(isUseOldExec)
            {
                out_exe_abname = "exec" + i + ".asset";
                exc_asset = LuaPath + "/exec/exec" + i + ".asset";
            }
            abd2.assetBundleName = out_exe_abname;
            abd2.assetNames = new string[]
            {
                    exc_asset
            };
            buildmap[i + 1] = abd2;

            listMoveFileName.Add(out_exe_abname);
            listMoveFileName.Add(out_exe_abname + ".manifest");
        }

        for (int i = 0; i < buildmap.Length; i++)
        {
            var _abd = buildmap[i];
            Debug.Log("build ab:" + UIHelper.ToJson(_abd.assetNames));
        }


        string tmp_outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget) + "2");
        string outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        if (abd.assetNames.Length > 0)
        {

            EditorHelp.CheckDir(tmp_outputPath + "/");
            BuildPipeline.BuildAssetBundles(tmp_outputPath, buildmap
                , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
                , EditorUserBuildSettings.activeBuildTarget);
        }

        Gen_Lang_Exec(isLuaToBinary, isLuaJITCompiler, luaBytesFiles, bomFiles);
        ///// encry lua file
        MoveLuaScriptFromSecPlace();

        ComFuncs.PrintDirHash(tmp_outputPath);
        ComFuncs.PrintDirHash(tmp_outputPath.Substring(0, tmp_outputPath.Length - 1));
        foreach (var f in listMoveFileName)
        {
            try
            {
                var exec_path = tmp_outputPath + "/" + f;
                string destFileName2 = exec_path.Replace(tmp_outputPath, outputPath);
                if (File.Exists(exec_path))
                {
                    File.Copy(exec_path, destFileName2, true);
                }

            }
            catch (Exception e)
            {
                Debug.Log("Error:" + e.ToString());
            }
        }

        //删除生成的字节码文件, 还原bom标记
        if (isLuaToBinary)
        {
            foreach (var f in luaBytesFiles)
            {
                if (File.Exists(f))
                {
                    File.Delete(f);
                }
            }

            foreach (KeyValuePair<string, byte[]> bomFile in bomFiles)
            {
                File.WriteAllBytes(bomFile.Key, bomFile.Value);
            }
        }

        LogHelp.Instance.Log("BuildLuascriptVice-end");
    }


    #region Lang表lua版生成Exec

    private static string LangExecDatabase = "Assets/Lua/Tables/Lang/Database";
    private static string LangExecPath = "Assets/Lua/Tables/Lang/lang_exec";

    static string FormatPath(string path)
    {
        return path.Replace("\\", "/");
    }

    /// <summary>
    /// Lang表单独生成Exec
    /// </summary>
    /// <param name="luaPath"></param>
    /// <returns></returns>
    public static bool IsLangLuaRemoveable(string luaPath)
    {
        luaPath = FormatPath(luaPath);
        bool needRemove = luaPath.Contains(LangExecDatabase);
        return needRemove;
    }

   [MenuItem("AssetBundles/CreateLangExec")]
    private static void Gen_Lang_Exec(bool isLuaToBinary, bool isLuaJIT, List<string> binaryFiles, Dictionary<string, byte[]> bomFiles)
    {
        string tmp_outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget) );
        //先删除Exec
        if (Directory.Exists(LangExecPath))
        {
            Directory.Delete(LangExecPath,true);
        }
           
        binaryFiles = new List<string>();

        Directory.CreateDirectory(LangExecPath);
        int exec_lang_count = 0;
        List<ResKey> rkList=new List<ResKey>();
        List<AssetBundleBuild> buildmap =new List<AssetBundleBuild>();
        if (!Directory.Exists(LangExecDatabase))
        {
            Debug.LogError("exec_lang:NO Lang Lua ");
            return;
        }

        List<string> exitList = new List<string>();
        //遍历Database下每个目录,如果文件夹以lang_开头,创建exec
        foreach (var dir in Directory.GetDirectories(LangExecDatabase))
        {
            if (Path.GetFileName(dir).StartsWith("lang_"))
            {
                //判断文件夹内有没有文件,如果没有,则不生成ab
                var tmpFiles = Directory.GetFiles(dir);
                if (tmpFiles.Length == 0)
                {
                    continue;
                }


                var execName = "exec_" + Path.GetFileName(dir) + ".asset";
                exitList.Add(execName);
                var execPath = Path.Combine(LangExecPath, execName);
                var rk = ScriptableObject.CreateInstance<ResKey>();
                rk.keys = new List<ResKey.KeyValuePair>();
                rkList.Add(rk);

                //AssetBundleBuild
                var abd2 = new AssetBundleBuild();
                abd2.assetBundleName = "lang_exec/" + execName;
                abd2.assetNames = new string[]
                {
                    execPath
                };
                buildmap.Add(abd2);


                //遍历每个文件夹的文件,设置keys
                foreach (var file in Directory.GetFiles(dir, "*.txt"))
                {
                    var content = File.ReadAllText(file);
                    byte[] fbytes;
                    if (isLuaToBinary)
                    {
                        string binary32FilePath;
                        byte[] fbytes32;
                        if (LuaToBinary(isLuaJIT, file, out fbytes, out fbytes32, out binary32FilePath, bomFiles))
                        {
                            binaryFiles.Add(binary32FilePath);

                            rk.keys.Add(new ResKey.KeyValuePair
                            {
                                key = Path.GetFileNameWithoutExtension(binary32FilePath),
                                value = fbytes32
                            });
                        }
                        else
                        {
                            Debug.LogError("LuaToBinary failed: " + file);
                        }
                    }
                    else
                    {
                        fbytes = System.Text.Encoding.UTF8.GetBytes(content);
                    }
                    rk.keys.Add(new ResKey.KeyValuePair
                    {
                        key = Path.GetFileNameWithoutExtension(file),
                        value = fbytes
                    });

                    
                }
                AssetDatabase.CreateAsset(rk, execPath);
                AssetDatabase.SaveAssets();
            }
        }
        AssetDatabase.Refresh();


        var langExecAbPath = Path.Combine(tmp_outputPath, "lang_exec");

        if (Directory.Exists(langExecAbPath))
        {
            foreach (var item in Directory.GetFiles(langExecAbPath))
            {

                var fileName = Path.GetFileName(item);

                if (exitList.Contains(fileName)|| fileName.Contains(".manifest") )
                {
                    continue;
                }
                else
                {
                    Debug.Log("Delete Not Exit Lang ab: "+fileName);
                }
                File.Delete(Path.Combine(langExecAbPath,fileName));
                var manifestFile = Path.Combine(langExecAbPath, fileName) + ".manifest";
                if (File.Exists(manifestFile))
                {
                    File.Delete(manifestFile);
                }
            }
        }



        EditorHelp.CheckDir(tmp_outputPath + "/");
        BuildPipeline.BuildAssetBundles(tmp_outputPath, buildmap.ToArray()
            , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
            , EditorUserBuildSettings.activeBuildTarget);
        Debug.Log("exec_lang:Gen Lang ab finish");
    }

       #endregion

    /// <summary>
    /// 打包lua补丁 
    /// isBuildLuaPatch = false 只生成luamd5，不生成lua补丁文件(在打包exec或者luascript时执行该逻辑)
    /// isBuildLuaPatch = true  生成lua补丁
    /// </summary>
    public static bool GetBuildLuaPath()
    {
        bool isBuildLuaPatch = version_of_check_lua_patch > 0;
        var choose_increase_abParam = JenkinsEnv.Instance.Get("choose_increase_ab_param", "");
        if (choose_increase_abParam != "")
        {
            isBuildLuaPatch = false;
        }
        if (version_of_check_lua_patch >= ComFuncs.GetResouceVersion())
        {
            isBuildLuaPatch = false;
        }

        bool gen_lua = JenkinsEnv.Instance.GetBool("gen_lua", true);
        bool gen_other_ab = JenkinsEnv.Instance.GetBool("gen_other_ab", true);
        bool copylua = JenkinsEnv.Instance.GetBool("copy_last_lua",false);
        if(!gen_lua || !gen_other_ab || copylua)
        {
            isBuildLuaPatch = false;
        }

        LogHelp.Instance.Log($"gen_lua:{gen_lua} gen_other_ab:{gen_other_ab} copylua:{copylua}");
        LogHelp.Instance.Log("BuildLuaPatch-end   isBuildLuaPatch:" + isBuildLuaPatch +
                             " version_of_check_lua_patch:" + version_of_check_lua_patch + " current_version" +
                             BuildScript.patch_version);

        return isBuildLuaPatch;
    }


    /// <summary>
    /// copy_last_lua,则从服务器替换lua ab资源到assetbundle文件夹,否则把BuildLuascriptVice生成的luascript 加密后复制到 目录:Assetbundle
    /// </summary>
    /// <param name="outputPath"></param>
    public static void SetGenLua(string outputPath)
    {

        //不生成lua 的ab，直接从服务器复制过来本地
        if (JenkinsEnv.Instance.GetBool("copy_last_lua"))
        {
            LogHelp.Instance.Log("copy_last_lua --> copy lua ab from server");
            CopyLuaAbsFromServer(outputPath);
            // isBuildLuaPatch = false;
        }
        LuaPatchCopyLuaAbAfterBuildCompleted(GetBuildLuaPath());
        LogHelp.Instance.Log("BuildLuaPatch LuaPatchCopyLuaAbAfterBuildCompleted-end");
    }

    /// <summary>
    /// 打包lua补丁 
    /// isBuildLuaPatch = false 只生成luamd5，不生成lua补丁文件(在打包exec或者luascript时执行该逻辑)
    /// isBuildLuaPatch = true  生成lua补丁
    /// </summary>
    public static void BuildLuaPatch(bool buildPatch = false)
    {
        //  注意 和 lua_patch 的 version_of_check_lua_patch 参数互斥
        int patch_misc_ver = JenkinsEnv.Instance.GetInt(BuildLuaScriptPatch.patch_misc_ver_key, -1);
        if (patch_misc_ver >= 0)
        {
            BuildMiscPatch(buildPatch, patch_misc_ver);
            return;
        }
        bool isBuildLuaPatch = buildPatch || GetBuildLuaPath();
        var buildTarget = EditorUserBuildSettings.activeBuildTarget;
        string luaMd5RootPath =
            Path.Combine(ConstPaths.LuaPatchFilesOutputPath, ComFuncs.GetPlatformFolderForAssetBundles(buildTarget));
        int oldVersion = version_of_check_lua_patch;
        int curVersion = ComFuncs.GetResouceVersion();
        luaPatchCurVersion = curVersion;
        Debug.Log("BuildLuaPatch curVersion:" + curVersion + " oldVersion:" + oldVersion);
        string checkTargetPath = Path.Combine(luaMd5RootPath, oldVersion.ToString());
        string currntOutputPath = Path.Combine(luaMd5RootPath, curVersion.ToString());

        var luaPatchAssetPath = "Assets/Lua/exec/" + ConstPaths.LuaPatchAssetName;
        if (File.Exists(luaPatchAssetPath))
        {
            Debug.Log("BuildLuaPatch 检测到lua补丁脚本！！！！ 删除！！" + luaPatchAssetPath);
            File.Delete(luaPatchAssetPath);
        }

        if (Directory.Exists(currntOutputPath))
        {
            Debug.Log("BuildLuaPatch 检测到已有lua缓存文件夹！！！！删除 " + currntOutputPath);
            Directory.Delete(currntOutputPath, true);
        }

        //对比文件夹目录无效，则只生成lua md5
        if (isBuildLuaPatch && !Directory.Exists(checkTargetPath))
        {
            Debug.Log("BuildLuaPatch 对比文件夹目录无效，则只生成lua md5!!!!!!!" + checkTargetPath);
            isBuildLuaPatch = false;
        }

        //只生成luamd5，不生成lua补丁文件(在打包exec或者luascript时执行该逻辑)
        if (!isBuildLuaPatch)
        {
            CreateLuaMd5File(currntOutputPath, ConstPaths.initialLuaMd5Name);
            return;
        }

        Dictionary<string, List<string>> currentLuaDic = CreateLuaMd5File(currntOutputPath, ConstPaths.LuaPatchMd5Name);
        Dictionary<string, List<string>> changedLuaFileDic =
            CheckLuaMd5(checkTargetPath, currntOutputPath, currentLuaDic);

        CopySourceLuaAbToTargetPath(checkTargetPath, currntOutputPath);

        string changedLuaJson = UIHelper.ToJson(changedLuaFileDic);
        Debug.Log("BuildLuaPatch 变化的lua文件列表:" + changedLuaJson);

        string curVersionLuaPath = Path.Combine(luaMd5RootPath, curVersion.ToString());

        File.WriteAllText(string.Format("{0}/{1}", curVersionLuaPath, ConstPaths.LuaPatchChangedMd5Name), changedLuaJson);

        //对比本次变化文件md5和上次的变化
        string sameMd5TargtVersion;
        bool isSameOfLastMd5 = IsSameOfChangedLuasToOldLuaPatch(luaMd5RootPath, currntOutputPath,
            curVersion.ToString(), changedLuaFileDic, out sameMd5TargtVersion);
        if (isSameOfLastMd5)
        {
            //和上次生成补丁没有变化，直接拷贝上次生成的lua补丁,本次不生成补丁
            string sameMd5TargtPath =
                string.Format("{0}/{1}/{2}", luaMd5RootPath, sameMd5TargtVersion, ConstPaths.LuaPatchAssetName);
            //string sourceFilePath = string.Format("{0}/{1}", checkTargetPath, ConstPaths.LuaPatchAssetName);
            if (System.IO.File.Exists(sameMd5TargtPath))
            {
                //拷贝到目标补丁版本目录下
                string targetFilePath = string.Format("{0}/{1}", currntOutputPath, ConstPaths.LuaPatchAssetName);
                Debug.Log("BuildLuaPatch copy1 拷贝到目标补丁版本目录下:" + targetFilePath);
                File.Copy(sameMd5TargtPath, targetFilePath, true);

                //拷贝到资源打包目录下
                string abOutputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
                    ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
                string abTargetFilePath = string.Format("{0}/{1}", abOutputPath, ConstPaths.LuaPatchAssetName);
                Debug.Log("BuildLuaPatch copy2 拷贝到资源打包目录下" + abTargetFilePath);
                File.Copy(sameMd5TargtPath, abTargetFilePath, true);
                return;
            }
        }

        //生成lua补丁
        BuildLuaPatchZip(changedLuaFileDic, luaPatchAssetPath);
    }
    /// <summary>
    /// 创建lua补丁文件
    /// </summary>
    /// <param name="changedLuaFileDic"></param>
    /// <param name="buildPath"></param>
    public static void BuildLuaPatchZip(Dictionary<string, List<string>> changedLuaFileDic, string buildPath)
    {
        if (changedLuaFileDic.Keys.Count <= 0)
        {
            Debug.Log("没有需要打包的lua文件");
            return;
        }
        //-------------------exec的方式打包lua补丁-------------------------
        //默认false 生成table补丁文件
        var luaPatchAssetPath = buildPath;
        FileUtil.DeleteFileOrDirectory(luaPatchAssetPath);
        if (!System.IO.File.Exists(luaPatchAssetPath))
        {
            EditorHelp.CheckDir(luaPatchAssetPath);
            var rk = ScriptableObject.CreateInstance<ResKey>();
            rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
            UnityEditor.AssetDatabase.CreateAsset(rk, luaPatchAssetPath);
        }

        var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(luaPatchAssetPath);
        //Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();
        rk1.keys.Clear();
        Dictionary<string, byte[]> DictLuaBs = new Dictionary<string, byte[]>();
        foreach (string luaFile in changedLuaFileDic.Keys)
        {
            var fstr = System.IO.File.ReadAllText(luaFile, System.Text.Encoding.UTF8);
            var bytes = System.Text.Encoding.UTF8.GetBytes(fstr);

            //var bytes = File.ReadAllBytes(luaFile);
            DictLuaBs.Add(luaFile, bytes);
            var filename = Path.GetFileNameWithoutExtension(luaFile);
            rk1.keys.Add(new ResKey.KeyValuePair()
            {
                key = filename,
                value = bytes
            });
        }



        var ai = AssetImporter.GetAtPath(luaPatchAssetPath);
        if (ai)
        {
            ai.assetBundleName = ConstPaths.LuaPatchAssetName;
        }

        Debug.Log("生成lua补丁：" + luaPatchAssetPath);
        EditorUtility.SetDirty(rk1);
        UIHelper.ImportAssets(luaPatchAssetPath);
        AssetDatabase.Refresh();
        
        // //补丁文件 luapatch.asset 单独生成
        // var buildmap = new AssetBundleBuild[1];
        // var abd = new AssetBundleBuild();
        //
        // abd.assetBundleName = ai.assetBundleName;
        // abd.assetNames = new string[]
        // {
        //     luaPatchAssetPath
        // };
        // string outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
        //     ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        // buildmap[0] = abd;
        // BuildPipeline.BuildAssetBundles(outputPath, buildmap
        //     , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
        //     , EditorUserBuildSettings.activeBuildTarget);
        // Debug.Log("生成 luaPatch ab：" + ai.assetBundleName);
    }

    #region 外网修复 lua 补丁包
    /// <summary>
    /// 打包lua 调试问题玩家的补丁
    /// buildPatch 强制打包
    /// </summary>
    public static void BuildMiscPatch(bool buildPatch = false, int ver = 0)
    {
        bool isBuildLuaPatch = buildPatch || GetBuildMiscPatch();
        var buildTarget = EditorUserBuildSettings.activeBuildTarget;
        string luaMd5RootPath =
            Path.Combine(ConstPaths.LuaPatchFilesOutputPath, ComFuncs.GetPlatformFolderForAssetBundles(buildTarget));
        int curVersion = ComFuncs.GetResouceVersion();
        int oldVersion = ver == 0 ? curVersion - 1 : ver;
        luaPatchCurVersion = curVersion;
        Debug.Log("BuildMiscPatch curVersion:" + curVersion + " oldVersion:" + oldVersion);
        string checkTargetPath = Path.Combine(luaMd5RootPath, oldVersion.ToString());
        string currntOutputPath = Path.Combine(luaMd5RootPath, curVersion.ToString());
        string assetName = ConstPaths.LuaMiscAssetName;

        var luaPatchAssetPath = "Assets/Lua/exec/" + assetName;
        if (File.Exists(luaPatchAssetPath))
        {
            Debug.Log("BuildMiscPatch 检测到lua调试补丁！！！！ 删除！！" + luaPatchAssetPath);
            File.Delete(luaPatchAssetPath);
        }

        if (Directory.Exists(currntOutputPath))
        {
            Debug.Log("BuildMiscPatch 检测到已有lua调试缓存文件夹！！！！删除 " + currntOutputPath);
            Directory.Delete(currntOutputPath, true);
        }

        //需要先删除AssetBundle下的 .asset文件,要不然在开启LuaPatch的情况下,如果没有生成luapatch的话(即比较的版本没有发生变化),会有之前版本的luapatch
        string lua_patchPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        string sourceFilePath = string.Format("{0}/{1}", lua_patchPath, assetName);
        if (System.IO.File.Exists(sourceFilePath))
        {
            "".PrintError("BuildMiscPatch needdelet", sourceFilePath);
            File.Delete(sourceFilePath);
        }

        //对比文件夹目录无效，则只生成lua md5
        if (isBuildLuaPatch && !Directory.Exists(checkTargetPath))
        {
            Debug.Log("BuildMiscPatch 对比文件夹目录无效，则只生成lua md5!!!!!!!" + checkTargetPath);
            isBuildLuaPatch = false;
        }

        //只生成luamd5，不生成lua补丁文件(在打包exec或者luascript时执行该逻辑)
        if (!isBuildLuaPatch)
        {
            CreateLuaMd5File(currntOutputPath, ConstPaths.initialLuaMd5Name);
            return;
        }

        Dictionary<string, List<string>> currentLuaDic = CreateLuaMd5File(currntOutputPath, ConstPaths.LuaMiscMd5Name);
        Dictionary<string, List<string>> changedLuaFileDic =
            CheckLuaMd5(checkTargetPath, currntOutputPath, currentLuaDic);

        CopySourceLuaAbToTargetPath(checkTargetPath, currntOutputPath);

        string changedLuaJson = UIHelper.ToJson(changedLuaFileDic);
        Debug.Log("BuildMiscPatch 变化的lua文件列表:" + changedLuaJson);

        string curVersionLuaPath = Path.Combine(luaMd5RootPath, curVersion.ToString());

        File.WriteAllText(string.Format("{0}/{1}", curVersionLuaPath, ConstPaths.LuaMiscChangedMd5Name), changedLuaJson);

        //对比本次变化文件md5和上次的变化
        string sameMd5TargtVersion;
        bool isSameOfLastMd5 = IsSameOfChangedLuasToOldLuaPatch(luaMd5RootPath, currntOutputPath,
            curVersion.ToString(), changedLuaFileDic, out sameMd5TargtVersion);
        if (isSameOfLastMd5)
        {
            //和上次生成补丁没有变化，直接拷贝上次生成的lua补丁,本次不生成补丁
            string sameMd5TargtPath =
                string.Format("{0}/{1}/{2}", luaMd5RootPath, sameMd5TargtVersion, assetName);
            //string sourceFilePath = string.Format("{0}/{1}", checkTargetPath, assetName);
            if (System.IO.File.Exists(sameMd5TargtPath))
            {
                //拷贝到目标补丁版本目录下
                string targetFilePath = string.Format("{0}/{1}", currntOutputPath, assetName);
                Debug.Log("BuildMiscPatch copy1 拷贝到目标补丁版本目录下:" + targetFilePath);
                File.Copy(sameMd5TargtPath, targetFilePath, true);

                //拷贝到资源打包目录下
                string abOutputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
                    ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
                string abTargetFilePath = string.Format("{0}/{1}", abOutputPath, assetName);
                Debug.Log("BuildMiscPatch copy2 拷贝到资源打包目录下" + abTargetFilePath);
                File.Copy(sameMd5TargtPath, abTargetFilePath, true);
                return;
            }
        }

        //生成lua补丁
        BuildMiscPatchZip(changedLuaFileDic, luaPatchAssetPath);
    }
    public static bool GetBuildMiscPatch()
    {
        bool isBuildLuaPatch = true;
        var choose_increase_abParam = JenkinsEnv.Instance.Get("choose_increase_ab_param", "");
        if (choose_increase_abParam != "")
        {
            isBuildLuaPatch = false;
        }
        bool gen_lua = JenkinsEnv.Instance.GetBool("gen_lua", true);
        bool gen_other_ab = JenkinsEnv.Instance.GetBool("gen_other_ab", true);
        bool copylua = JenkinsEnv.Instance.GetBool("copy_last_lua", false);
        if (!gen_lua || !gen_other_ab || copylua)
        {
            isBuildLuaPatch = false;
        }

        LogHelp.Instance.Log($"gen_lua:{gen_lua} gen_other_ab:{gen_other_ab} copylua:{copylua} isBuildLuaPatch: {isBuildLuaPatch} current_version{BuildScript.patch_version}");

        return isBuildLuaPatch;
    }

    /// <summary>
    /// 创建lua补丁文件
    /// </summary>
    /// <param name="changedLuaFileDic"></param>
    /// <param name="buildPath"></param>
    public static void BuildMiscPatchZip(Dictionary<string, List<string>> changedLuaFileDic, string buildPath)
    {
        if (changedLuaFileDic.Keys.Count <= 0)
        {
            Debug.Log("没有需要打包的调试lua文件");
            return;
        }
        //-------------------exec的方式打包lua补丁-------------------------
        //默认false 生成table补丁文件
        var luaPatchAssetPath = buildPath;
        FileUtil.DeleteFileOrDirectory(luaPatchAssetPath);
        if (!System.IO.File.Exists(luaPatchAssetPath))
        {
            EditorHelp.CheckDir(luaPatchAssetPath);
            var rk = ScriptableObject.CreateInstance<ResKey>();
            rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
            UnityEditor.AssetDatabase.CreateAsset(rk, luaPatchAssetPath);
        }

        var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(luaPatchAssetPath);
        //Dictionary<string, ResKey.KeyValuePair> dicKeys = new Dictionary<string, ResKey.KeyValuePair>();
        rk1.keys.Clear();
        Dictionary<string, byte[]> DictLuaBs = new Dictionary<string, byte[]>();
        foreach (string luaFile in changedLuaFileDic.Keys)
        {
            var fstr = System.IO.File.ReadAllText(luaFile, System.Text.Encoding.UTF8);
            var bytes = System.Text.Encoding.UTF8.GetBytes(fstr);

            //var bytes = File.ReadAllBytes(luaFile);
            DictLuaBs.Add(luaFile, bytes);
            var filename = Path.GetFileNameWithoutExtension(luaFile);
            rk1.keys.Add(new ResKey.KeyValuePair()
            {
                key = filename,
                value = bytes
            });
        }



        var ai = AssetImporter.GetAtPath(luaPatchAssetPath);
        if (ai)
        {
            ai.assetBundleName = ConstPaths.LuaMiscAssetName;
        }

        Debug.Log("生成lua补丁：" + luaPatchAssetPath);
        EditorUtility.SetDirty(rk1);
        UIHelper.ImportAssets(luaPatchAssetPath);
        AssetDatabase.Refresh();

        // //补丁文件 luapatch.asset 单独生成
        // var buildmap = new AssetBundleBuild[1];
        // var abd = new AssetBundleBuild();
        //
        // abd.assetBundleName = ai.assetBundleName;
        // abd.assetNames = new string[]
        // {
        //     luaPatchAssetPath
        // };
        // string outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
        //     ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        // buildmap[0] = abd;
        // BuildPipeline.BuildAssetBundles(outputPath, buildmap
        //     , BuildAssetBundleOptions.ChunkBasedCompression | BuildAssetBundleOptions.DeterministicAssetBundle
        //     , EditorUserBuildSettings.activeBuildTarget);
        // Debug.Log("生成 luaPatch ab：" + ai.assetBundleName);
    }
    #endregion

    /// <summary>
    /// 资源打包结束后lua补丁相关拷贝：拷贝lua ab相关文件
    /// </summary>
    /// <param name="isOnlyLuaMd5"></param>
    public static void LuaPatchCopyLuaAbAfterBuildCompleted(bool isBuildLuaPatch)
    {
        var buildTarget = EditorUserBuildSettings.activeBuildTarget;
        string luaMd5RootPath =
            Path.Combine(ConstPaths.LuaPatchFilesOutputPath, ComFuncs.GetPlatformFolderForAssetBundles(buildTarget));
        string currntOutputPath = Path.Combine(luaMd5RootPath, luaPatchCurVersion.ToString());
        string abOutputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));
        //不创建lua补丁时，拷贝当前生成的最新exec和luascript文件 到lua补丁文件夹
        if (!isBuildLuaPatch)
        {
            CopySourceLuaAbToTargetPath(abOutputPath, currntOutputPath);

            string sourceFilePath = string.Format("{0}/{1}", abOutputPath, ConstPaths.LuaPatchAssetName);
            "".PrintError("needdelet", sourceFilePath, System.IO.File.Exists(sourceFilePath));

            if (System.IO.File.Exists(sourceFilePath))
            {
                File.Delete(sourceFilePath);
                return;
            }
        }
        else
        {
            //创建lua补丁时，将生成的补丁ab拷贝到lua补丁文件夹
            string sourceFilePath = string.Format("{0}/{1}", abOutputPath, ConstPaths.LuaPatchAssetName);
            if (!System.IO.File.Exists(sourceFilePath))
            {
                return;
            }

            string targetFilePath = string.Format("{0}/{1}", currntOutputPath, ConstPaths.LuaPatchAssetName);
            "".PrintError("LuaPatchCopyLuaAbAfterBuildCompleted ", sourceFilePath,
                System.IO.File.Exists(sourceFilePath), " targetFilePath:", targetFilePath);
            File.Copy(sourceFilePath, targetFilePath, true);

            //删除打包目录exec相关的mainfest
            string[] files = Directory.GetFiles(abOutputPath, "*.asset.manifest");

            foreach (var file in files)
            {
                File.Delete(file);
            }

            //将原始lua ab拷贝到ab打包文件夹,替换被修改的lua ab文件
            CopySourceLuaAbToTargetPath(currntOutputPath, abOutputPath);
        }
    }


    public static Dictionary<string, List<string>> CreateLuaMd5File(string outputPath, string fileName)
    {
        /// create patch file
        if (!Directory.Exists(outputPath))
        {
            Directory.CreateDirectory(outputPath);
        }

        string luaSouceFilesRootPath = "Assets/Lua";
        try
        {
            var fileDic = new Dictionary<string, List<string>>();

            var files = Directory.GetFiles(luaSouceFilesRootPath, "*.txt", SearchOption.AllDirectories);
            foreach (var f in files)
            {
                //lang表的lua不计算补丁
                if(IsLangLuaRemoveable(f))
                    continue;
                var md5 = ComFuncs.File2MD5(f);
                var shortf = f.Replace(outputPath, "").Trim('\\', '/').Replace("\\", "/");
                var list = new List<string>() { md5, new FileInfo(f).Length + "" };
                fileDic.Add(shortf, list);
            }

            //var content = string.Join("\n", fileList.ToArray());
            var dic = new Dictionary<string, object>();
            dic["list"] = fileDic;

            if (fileName == "")
            {
                fileName = "luaPatchMd5files.txt";
            }

            File.WriteAllText(string.Format("{0}/{1}", outputPath, fileName), UIHelper.ToJson(dic));
            return fileDic;
        }
        catch (Exception e)
        {
            Debug.LogError(e.ToString());
        }

        return null;
    }

    /// <summary>
    /// 从服务器复制Lua对应ab文件到打包机
    /// </summary>
    private static void CopyLuaAbsFromServer(string outputPath)
    {
        List<string> copyList = new List<string>(){"luascript.zip"};
        int lastVer = ComFuncs.GetResouceVersion() - 1;
        AddExecNameList(copyList,true);
        //先删掉打包机的manifest
        //删除打包目录exec相关的mainfest
        string[] files = Directory.GetFiles(outputPath, "*.asset.manifest");

        foreach (var file in files)
        {
            File.Delete(file);
        }

        hashCheck hc = ModifyAB.Instance.GetHashCheck(lastVer);
        var prefix = ModifyAB.Instance.GetResUrlPrefix();
        foreach (var k in hc.list.Keys)
        {
            var ver = hc.GetVer(k);
            if (!copyList.Contains(k))
                continue;
            var p = prefix + ver + "/" + k;
            var t = outputPath + "/" + k;
            ToolUti.CheckDir(t);
            "".Print("copy", p, t);
            try
            {
                File.Copy(p, t, true);
            }
            catch (System.Exception e)
            {
                "".Print("buildAssetBundleRes copy error", e.ToString());
            }
        }
    }

    public static void CopySourceLuaAbToTargetPath(string sourcePath, string targetPath)
    {
        List<string> copyList = new List<string>()
                {"luascript.zip"};
        AddExecNameList(copyList,true);
        string sourceFilePath;
        string targetFilePath;
        EditorHelp.CheckDir(targetPath);
        foreach (string name in copyList)
        {
            sourceFilePath = string.Format("{0}/{1}", sourcePath, name);
            if (!System.IO.File.Exists(sourceFilePath))
            {
                Debug.LogError("not exists by path:" + sourceFilePath);
                continue;
            }
            targetFilePath = string.Format("{0}/{1}", targetPath, name);
            File.Copy(sourceFilePath, targetFilePath, true);
        }
    }

    public static Dictionary<string, List<string>> CheckLuaMd5(string checkTargetPath, string curBuildPath,
        Dictionary<string, List<string>> currentLuaDic)
    {
        string targetLuaMd5Path = string.Format("{0}/{1}", checkTargetPath, ConstPaths.initialLuaMd5Name);
        string targetFileContent = null;
        if (File.Exists(targetLuaMd5Path))
        {
            targetFileContent = File.ReadAllText(targetLuaMd5Path);
        }

        if (string.IsNullOrEmpty(targetFileContent))
        {
            Debug.Log("检查md5值失败：无法找到需要对比的lua md5信息文件，path:" + targetLuaMd5Path);
            return currentLuaDic;
        }

        //将初版lua md5文件拷贝到当前打包版本目录下，方便后续打包时对比
        string curLuaMd5Path = string.Format("{0}/{1}", curBuildPath, ConstPaths.initialLuaMd5Name);
        File.Copy(targetLuaMd5Path, curLuaMd5Path, true);

        Dictionary<string, string[]> oldLuaFileDic = new Dictionary<string, string[]>();
        var tempNewestPatchFileDic =
            UIHelper.ToObj<Dictionary<string, Dictionary<string, string[]>>>(targetFileContent);
        if (tempNewestPatchFileDic.ContainsKey("list"))
        {
            oldLuaFileDic = tempNewestPatchFileDic["list"];
        }

        if (tempNewestPatchFileDic.ContainsKey("list"))
        {
            oldLuaFileDic = tempNewestPatchFileDic["list"];
        }

        Dictionary<string, List<string>> changedLuaFileDic = new Dictionary<string, List<string>>();
        List<string> currentLuaInfo;
        List<string> oldLuaInfo;
        foreach (string key in currentLuaDic.Keys)
        {
            currentLuaInfo = currentLuaDic[key];
            if (!oldLuaFileDic.ContainsKey(key))
            {
                //新增lua文件
                changedLuaFileDic.Add(key, currentLuaInfo);
                continue;
            }

            oldLuaInfo = new List<string>(oldLuaFileDic[key]);

            //检测md5值和长度值是否有变化
            if (currentLuaInfo[0].Equals(oldLuaInfo[0]) && currentLuaInfo[1].Equals(oldLuaInfo[1]))
            {
                continue;
            }
            Debug.Log("检测md5值和长度值是否有变化md5:" + currentLuaInfo[0] + " old:" + oldLuaInfo[0] + " length:" +
                      currentLuaInfo[1] + " old:" + oldLuaInfo[1]);
            changedLuaFileDic.Add(key, currentLuaInfo);
        }

        return changedLuaFileDic;
    }

    public static bool IsSameOfChangedLuasToOldLuaPatch(string rootPath, string currentOutputPath,
        string currentVersion, Dictionary<string, List<string>> changedLuaFileDic, out string sameMd5TargtVersion)
    {
        //生成修改文件的md5值
        var fileNames = changedLuaFileDic.Keys.ToList<string>();
        //Debug.LogError("排序前：" + UIHelper.ToJson(fileNames));
        fileNames.Sort();
        Debug.Log("排序后：" + UIHelper.ToJson(fileNames));
        string fileName = "";
        string md5;
        string allMd5 = "";
        //将变化的md5累加，生成新的md5
        for (int i = 0; i < fileNames.Count; i++)
        {
            fileName = fileNames[i];

            md5 = changedLuaFileDic[fileName][0];
            allMd5 += md5;
        }


        byte[] allMd5Bytes = System.Text.Encoding.Default.GetBytes(allMd5);
        //当前打包补丁文件的md5,与历史版本对比是否有变化，如果没有则使用原始lua补丁
        string changedMd5 = ComFuncs.GetMD5HashFromFile(allMd5Bytes);

        string curLuaMd5Path = string.Format("{0}/{1}", currentOutputPath, ConstPaths.LastBuilLuaPatchChangedMd5Name);
        File.WriteAllText(curLuaMd5Path, changedMd5);

        string luaPatchMd5ListPath = string.Format("{0}/{1}", rootPath, ConstPaths.luaPatchMd5List);
        string allLuaPatchMd5Str = "";
        if (File.Exists(luaPatchMd5ListPath))
        {
            allLuaPatchMd5Str = File.ReadAllText(luaPatchMd5ListPath);
        }

        sameMd5TargtVersion = "";
        //检测补丁的历史版本，是否有md5值相同的补丁，如果有，则使用历史补丁，否则生成新的
        Dictionary<string, string> allLuaPatchMd5 = UIHelper.ToObj<Dictionary<string, string>>(allLuaPatchMd5Str);
        if (allLuaPatchMd5 == null)
        {
            allLuaPatchMd5 = new Dictionary<string, string>();
        }

        if (allLuaPatchMd5.ContainsKey(changedMd5))
        {
            //获取相同md5文件对应的补丁版本号
            sameMd5TargtVersion = allLuaPatchMd5[changedMd5];
        }

        //将当前补丁md5存储进md5列表中
        allLuaPatchMd5[changedMd5] = currentVersion;
        string allLuaPatchMd5Json = UIHelper.ToJson(allLuaPatchMd5);
        File.WriteAllText(luaPatchMd5ListPath, allLuaPatchMd5Json);

        if (sameMd5TargtVersion == "")
        {
            Debug.Log("lua补丁相比上次md5有变化，生成新的lua补丁  currentMd5:" + changedMd5 + " lastMd5" + allLuaPatchMd5);
            return false;
        }

        Debug.LogError("本次生成lua补丁和上次没有变化，不生成新的lua补丁  currentMd5:" + changedMd5 + " sameMd5TargtVersion" +
                       sameMd5TargtVersion);
        return true;
    }


    /// <summary>
    /// 从Android2对luascript文件加密复制到Android文件夹
    /// </summary>
    private static void MoveLuaScriptFromSecPlace()
    {
        string out_abname = War.Script.LuaManager.LuaScriptAssetBundleName;
        string tmp_outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
           ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget) + "2");
        string outputPath = Path.Combine(ConstPaths.AssetBundlesOutputPath,
            ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget));

        var luaOutPath = tmp_outputPath + "/" + out_abname;
        string newFilePath = luaOutPath + ".zip";
        if (File.Exists(newFilePath))
            File.Delete(newFilePath);
        ComFuncs.EncryptFile(luaOutPath, out_abname, newFilePath);
        //File.Delete(luaOutPath);
        string destFileName = newFilePath.Replace(tmp_outputPath, outputPath);
        ToolUti.CheckDir(destFileName);

        File.Copy(newFilePath, destFileName, true);
    }


    public static bool CompareBytes(byte[] b1, byte[] b2)
    {
        if (b1 == null || b2 == null)
        {
            return false;
        }

        if (b1.Length != b2.Length) return false;

        for (int i = 0; i < b1.Length; i++)
        {
            if (b1[i] != b2[i]) return false;
        }

        return true;
    }

    public class LuaScriptConfig
    {
        public List<string> luascr;
        public List<List<string>> list;

        Dictionary<string, bool> luaDic;

        public Dictionary<string, bool> GetLuaDic()
        {
            if (luaDic == null)
            {
                luaDic = new Dictionary<string, bool>();
                foreach (var item in luascr)
                {
                    luaDic[item] = true;
                }
            }

            return luaDic;
        }
    }

    public static bool GetLuaListOfOutSize(int size,long luaListBytes)
    {
        bool isOver2 = false;
        // 大概的值，单位为M,每个exec存文件大概6M，最后生成的ab大概2M左右
        double fixedValue = 6 * (size + 1);


        double tableListSize = (double)luaListBytes / (1024 * 1024);
        

        if (tableListSize > fixedValue)
        {
            Debug.LogWarning($"size:{size},   luaListBytes_KB:{luaListBytes / 1024}KB,  luaListBytes_MB:{tableListSize}:MB");
            isOver2 = true;
        }
              
        return isOver2;
    }

    public static string[] GetExecNameConfig()
    {
        var filePath = Application.dataPath + "/Scripts/Editor/Other/exec_split.json";
        if (!File.Exists(filePath))
        {
            Debug.LogError($"exec_split.json is not exist /Scripts/Editor/Other/ !");
            return null;
        }
        var dataList = GetJsonData(filePath);
        
        return dataList["execName"];
    }

    public static int GetExecCountByType(string typeName)
    {
        var dataList = GetExecNameConfig();
        int count = 0;
        for (int i = 0; i < dataList.Length; i++)
        {
            if (dataList[i].Contains(typeName))
            {
                count++;
            }
        }
        Debug.LogWarning("GetExecCountByType:" + typeName + "count:" + count);
        return count;
    }

    public static void AddExecNameList(List<string> copyList,bool isAddManifest)
    {
        if(isUseOldExec)
        {
            for (int i = 0; i < 5; i++)
            {
                copyList.Add("exec" + i + ".asset");
                if (isAddManifest)
                {
                    copyList.Add("exec" + i + ".asset.manifest");
                }
            }
        }
        else
        {
            var execNameList = GetExecNameConfig();
            for (int i = 0; i < execNameList.Length; i++)
            {
                copyList.Add(execNameList[i] + ".asset");
                if (isAddManifest)
                {
                    copyList.Add(execNameList[i] + ".asset.manifest");
                }
            }
        }

    }

    public static Dictionary<string, string[]> GetJsonData(string path)
    {
        if (!string.IsNullOrEmpty(path))
        {
            try
            {
                var jsonData = File.ReadAllText(path);
                if (!string.IsNullOrEmpty(path))
                {
                    var data = ToolUti.ToObj<Dictionary<string, string[]>>(jsonData);
                    return data;
                }
            }
            catch (System.Exception e)
            {
                "".Print("error:", e.ToString());
            }
        }
        return new Dictionary<string, string[]>();
    }

    public static void DeleteExecByFileName(string path,string fileName)
    {
        string sourceFilePath = string.Format("{0}/{1}", path, fileName);
        if (File.Exists(sourceFilePath))
        {
            File.Delete(sourceFilePath);
            Debug.LogWarning("DeleteExecByFileName:" + sourceFilePath);
        }        
    }
}
