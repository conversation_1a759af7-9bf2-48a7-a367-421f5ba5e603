
using UnityEditor;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System;

public class EditorHelp
{
	static public BuildTarget GetPlatform ()
	{
#if UNITY_ANDROID
		return BuildTarget.Android;
#elif UNITY_IPHONE
		return BuildTarget.iOS;
#else
		return BuildTarget.StandaloneWindows;
#endif
	}

	static public string GetPlatformName (BuildTarget platform, bool isLower = false)
	{
		string path = "";

		switch (platform) {
		case BuildTarget.StandaloneWindows:
			{
				path = "Windows";
			}
			break;
		case BuildTarget.Android:
			{
				path = "Android";
			}
			break;
		case BuildTarget.iOS:
			{
				path = "IPhone";
			}
			break;
		}

		return isLower ? path.ToLower () : path;
	}

	static public string GetPlatformName ()
	{
		return GetPlatformName (GetPlatform ());
	}

	static public string GetAssetPath (string cat, string assetName)
	{
		var platformT = GetPlatform ();
		var platform = GetPlatformName (platformT);
		string path = string.Format ("Assets/StreamingAssets/{0}/{2}/{1}.unity3d_{3}", platform, assetName, cat, GetPlatformName (platformT, true));
		return path;
	}

	public static void ExplorePath (string path,bool isFile = false)
	{
		if (string.IsNullOrEmpty(path)) return;
		//Debug.Log (path);
		path = path.Replace ("/", "\\");

		if (isFile)
		{
			if (!File.Exists(path))
			{
				Debug.LogError("No File: " + path);
				return;
			}
			path = string.Format("/Select, {0}", path);
		}
		else
		{
			if (!Directory.Exists(path))
			{
				Debug.LogError("No Directory: " + path);
				return;
			}
		}

		System.Diagnostics.Process.Start ("explorer.exe", string.Format (@"{0}", path));
	}

	public static void ClearPath(string path)
	{
		if (string.IsNullOrEmpty(path)) return;

		if(Directory.Exists(path))
        {
			Directory.Delete(path, true);
        }

		Directory.CreateDirectory(path);
	}

	public static void CopyFile (string source, string target, bool overwrite = true, bool log = true)
	{
		try {
			File.Copy (source, target, overwrite);
			if (log) {
				Debug.LogFormat ("Moved:\n{0},\n{1}", source, target);
			}
		} catch (System.Exception e) {
			Debug.LogError (e.ToString ());
		}
	}

    public static void CheckThisDir(string path)
    {
        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }
    }
    public static void CheckDir (string path)
	{ 
        var dir = Path.GetDirectoryName (path);
		if (!Directory.Exists (dir)) {
			Directory.CreateDirectory (dir);
		}
	}

	public static void IterPrefab (System.Func<GameObject, bool> One_GO_Modify, UnityEngine.Object[] objs, string title = "title")
	{
		float proccess = 0;
		EditorUtility.DisplayProgressBar (title, "begin..", proccess);
		GameObject selgoInScene = null;
		for (int i = 0; i < objs.Length; i++) {
			try {
				GameObject selgo = objs [i] as GameObject;
				proccess = i / (float)objs.Length;
				EditorUtility.DisplayProgressBar (title, string.Format ("{1}%...{0}", selgo.name, (proccess * 100).ToString ("F2")), proccess);
				selgoInScene = PrefabUtility.InstantiatePrefab (objs [i]) as GameObject;
				if (One_GO_Modify != null) {
					var bdirty = One_GO_Modify (selgoInScene);
					if (bdirty) {
						if (PrefabUtility.GetPrefabType (selgoInScene) == PrefabType.PrefabInstance) {
							UnityEngine.Object parentObject = PrefabUtility.GetPrefabParent (selgoInScene);
							//替换预设  
							PrefabUtility.ReplacePrefab (selgoInScene, parentObject, ReplacePrefabOptions.ConnectToPrefab);
							GameObject.DestroyImmediate (selgoInScene);
							//刷新  
							AssetDatabase.Refresh ();
							Debug.Log ("Modify:" + selgo.name);
						}
					}
				}
				GameObject.DestroyImmediate (selgoInScene);
			} catch (System.Exception ex) {
				Debug.LogError (ex.ToString ());
				if (selgoInScene) {
					GameObject.DestroyImmediate (selgoInScene);
				}
			}
		}
		EditorUtility.ClearProgressBar ();
    }


    /// <summary>
    /// Applies the un read tex. 贴图像素拷贝
    /// </summary>
    /// <param name="tex">Tex.</param>
    /// <param name="target">Target.</param>
    public static void ApplyUnReadTex(Texture2D tex, Texture2D target)
    {
        RenderTexture tmp = RenderTexture.GetTemporary(
                            tex.width,
                            tex.height,
                            0,
                            RenderTextureFormat.Default,
                            RenderTextureReadWrite.Linear);

        // 将texture的像素复制到RenderTexture
        Graphics.Blit(tex, tmp);

        // 备份当前设置的RenderTexture
        RenderTexture previous = RenderTexture.active;

        // 将创建的临时纹理tmp设置为当前RenderTexture
        RenderTexture.active = tmp;


        // 将RenderTexture的像素值拷贝到新的纹理中
        target.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);
        target.Apply();

        // 重置激活的RenderTexture
        RenderTexture.active = previous;

        // 释放临时RenderTexture
        RenderTexture.ReleaseTemporary(tmp);
	}

	 
	public static Texture2D CloneTex(Texture2D tex)
	{
		Texture2D target = new Texture2D(tex.width, tex.height, TextureFormat.RGBA32, false);
		target.alphaIsTransparency = true;
		target.filterMode = tex.filterMode;
		ApplyUnReadTex____(tex, target);
		return target;
	}


	/// <summary>
	/// Applies the un read tex. 贴图像素拷贝
	/// </summary>
	/// <param name="tex">Tex.</param>
	/// <param name="target">Target.</param>
	public static void ApplyUnReadTex____(Texture2D tex, Texture2D target)
	{
		Material mat = new Material(Shader.Find("Sprites/MaskHero"));
        //Material mat = new Material(Shader.Find("Unlit/Transparent"));

		

		RenderTexture rt = RenderTexture.GetTemporary (target.width, target.height, 0, RenderTextureFormat.ARGB32,  RenderTextureReadWrite.sRGB);
		Graphics.SetRenderTarget (rt);
		GL.Clear (true, true, Color.clear);
		GL.PushMatrix ();
		GL.LoadOrtho (); 
		mat.mainTexture = tex;
		mat.SetPass (0);
		GL.Begin (GL.TRIANGLES);
		var tris = new int[] {
			0, 1, 2, 0, 2, 3
		};
		var uvs = new Vector3[] {
			new Vector3 (0, 0),
			new Vector3 (0, 1),
			new Vector3 (1, 1),
			new Vector3 (1, 0), 
		};
		var atlasUvs = new Vector3[] {
			new Vector3 (0, 0),
			new Vector3 (0, 1),
			new Vector3 (1, 1),
			new Vector3 (1, 0),
		};
		foreach (int index in tris) {
			GL.TexCoord (uvs [index]);
			GL.Vertex (atlasUvs [index]);
		}
		GL.End (); 
		GL.PopMatrix ();

		target.ReadPixels (new Rect (0, 0, target.width, target.height), 0, 0);
		target.Apply ();
		RenderTexture.ReleaseTemporary (rt);
	}

	public static string ToJson (object o)
	{
		return Newtonsoft.Json.JsonConvert.SerializeObject (o, Newtonsoft.Json.Formatting.Indented);
	}

	public static T ToObj<T> (string o)
	{
		return Newtonsoft.Json.JsonConvert.DeserializeObject<T> (o);
	}

    public static void WriteFile(string path, string str = "")
    {
        var file = new StreamWriter(path, true);
        file.WriteLine(str);
        file.Close();
    }
}