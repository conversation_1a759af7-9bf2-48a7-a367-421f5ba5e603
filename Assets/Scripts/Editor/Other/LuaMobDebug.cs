// IDEA + EmmyLua���Կ���
// ������ 2022.02.18
// lua���������init_enter.txt��mobdebug
using UnityEngine;
using UnityEditor;

public static class LuaMobDebug
{
    const string MENU_ITEM_KEY = "Test/LuaMobDebug";
    const string EDITOR_PREFS_KEY = "_LuaMobDebug_";
    //Set CheckMark
    [MenuItem(MENU_ITEM_KEY, true)]
    public static bool CheckLuaDebug()
    {
        int mobDebug = PlayerPrefs.GetInt(EDITOR_PREFS_KEY, 0);
        Menu.SetChecked(MENU_ITEM_KEY, mobDebug == 1);
        return true;
    }

    //Switch Platform
    [MenuItem(MENU_ITEM_KEY)]
    public static void SwitchLuaDebug()
    {
        int mobDebug = PlayerPrefs.GetInt(EDITOR_PREFS_KEY, 0);
        PlayerPrefs.SetInt(EDITOR_PREFS_KEY, mobDebug == 0 ? 1 : 0);
    }
}
