using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class FileCheck:Singleton<FileCheck>
    {
        public string pathRoot = "ui/";
        public object[] InitCheck(string path)
        {
            pathRoot = path;
            return null;
        }

        static string[] _abNames = null;
        public static string[] abnames { get { if (_abNames == null) _abNames = AssetDatabase.GetAllAssetBundleNames(); return _abNames; } }
        /// <summary>
        /// 计算出列表项的唯一码，返回唯一码字典
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        public static Dictionary<string,string> CalFile(IEnumerable<string> list)
        {
            var dic = new Dictionary<string, string>();
            foreach (var path in  list)
            {
                var ai = AssetImporter.GetAtPath(path);
                if(ai)
                {
                    var origin = new StringBuilder();

                    if(ai is TextureImporter)
                    {
                        var setting = (ai as TextureImporter).GetPlatformTextureSettings(EditorUserBuildSettings.activeBuildTarget.ToString());

                        var json = JsonUtility.ToJson(setting);
                        origin.Append(json);
                        //"".Print("TextureImporter", json);

                    }
                    else{
                        if(File.Exists(path + ".meta"))
                        {
                            var json = File.ReadAllText(path+".meta");
                            origin.Append(json);
                            //"".Print("AssetImporter", json);
                        }

                    }
                    var filemd5 = ToolUti.File2MD5(path);
                    var md5 = ToolUti.String2MD5(origin.ToString());
                    dic[path] = string.Format("{0}|{1}", filemd5 , md5);


                }

            }
            return dic;
        }
        public static string GetABMD5(Dictionary<string,string> ctx,string ab)
        {
            var origin = new StringBuilder();
            //var abs = AssetDatabase.GetAssetBundleDependencies(ab,false);
            //foreach (var tab in abs)
            //{
            //    IterAB(ctx, origin, tab);
            //}
                IterAB(ctx, origin, ab);

            ctx[ab] = ToolUti.String2MD5(origin.ToString());
            return ctx[ab];

        }

        private static void IterAB(Dictionary<string, string> ctx, StringBuilder origin, string tab)
        {
            var tass = AssetDatabase.GetAssetPathsFromAssetBundle(tab);

            var l = new List<string>(tass);
            l.Sort();
            var tmd = "";

            foreach (var item in l)
            {
                if (ctx.TryGetValue(item, out tmd))
                {
                    origin.Append(tmd);
                    origin.Append("\n");
                }
            }
        }

        public List<string> CompareCtxWithPath(string old_path, string new_path, string pathRoot)
        {
            if(!File.Exists(old_path) || !File.Exists(new_path))
            {
                "".Print("CompareCtx path does not exist",old_path,new_path);
                List<string> lists = new List<string>();
                return lists;
            }
            var abnames = AssetDatabase.GetAllAssetBundleNames();
            var tabs = ArrayUtility.FindAll(abnames, (ab) => ab.StartsWith(pathRoot));
            var old_str = File.ReadAllText(old_path);
            var old_ctx = UIHelper.ToObj<Dictionary<string, string>>(old_str);
            var new_str = File.ReadAllText(new_path);
            var new_ctx = UIHelper.ToObj<Dictionary<string, string>>(new_str);


            var list = CompareCtx(old_ctx, new_ctx, tabs);

            return list;

        }
        public List<string> CompareCtx(Dictionary<string, string> old_ctx, Dictionary<string, string> new_ctx, IEnumerable<string> abs)
        {

            List<string> lists = new List<string>();
            var old_md5 = "";
            var new_md5 = "";

            foreach (var ab in abs)
            {
                old_md5 = GetABMD5(old_ctx, ab);
                new_md5 = GetABMD5(new_ctx, ab);
                if (old_md5 == new_md5) continue;
                lists.Add(ab);
                "".Print("CompareCtx diff", ab, old_md5, new_md5);
            }
            return lists;
        }
        public static Dictionary<string, string> CheckAndSaveCtx(string pathRoot,string savePath)
        {
            LogHelp.Instance.Log("FileCheck start");
            var abnames = AssetDatabase.GetAllAssetBundleNames();

            var tabs = ArrayUtility.FindAll(abnames, (ab) => ab.StartsWith(pathRoot));
            var dic = CalMd5Ctx(tabs);
            var dic_str = UIHelper.ToJson(dic);
            EditorHelp.CheckDir(savePath);
            File.WriteAllText(savePath, dic_str);
            LogHelp.Instance.Log("FileCheck end");
            return dic;
        }

        public void Exec(string outputPath = "AssetBundles")
        {
            var list2remove = new List<string>();
            var remove_ab_str = JenkinsEnv.Instance.Get("remove_ab_list");
            if(!string.IsNullOrEmpty(remove_ab_str))
            {
                var arr = remove_ab_str.Split(';');
                list2remove.AddRange(arr);
            }

            var oldpath = "AssetBundles/old.json";
            var newpath = "AssetBundles/new.json";

            CheckAndSaveCtx(pathRoot, newpath);
            if (File.Exists(oldpath))
            {
                var l = CompareCtxWithPath(oldpath, newpath, pathRoot);
                "".Print("FileCheck", UIHelper.ToJson(l));

                l.AddRange(list2remove);

                foreach (var ab in l)
                {
                    var manifestpath = string.Format("{0}/{1}.manifest", outputPath, ab);
                    if (File.Exists(manifestpath))
                    {
                        File.Delete(manifestpath);
                    }
                }
                 
            }
            File.Delete(oldpath);
            File.Move(newpath, oldpath);
        }

        private static Dictionary<string, string> CalMd5Ctx(List<string> tabs)
        {
            HashSet<string> set; Dictionary<string, string> dic;
            set = new HashSet<string>();
            foreach (var ab in tabs)
            {
                var abs = AssetDatabase.GetAssetBundleDependencies(ab, true);
                foreach (var tab in abs)
                {
                    var tass = AssetDatabase.GetAssetPathsFromAssetBundle(tab);

                    set.UnionWith(tass);
                }
            }
            dic = CalFile(set);
            return dic;

        }


        #region 检测Ab的有效性
        static string[] GetFiles(string dir)
        {
            IEnumerable<string> fs = Directory.GetFiles(dir, "*", SearchOption.AllDirectories).Where(file => {
                var extension = Path.GetExtension(file);
                if (extension == ".manifest" || extension == ".DS_Store") return false;
                return true;
            });
            return fs.ToArray();
        }
        //检测Ab的有效性
        static List<string> CheckAbVaild(string[] abNames, string outPath, string[] fs)
        {
            List<string> ls = new List<string>();
            int len = fs.Length;
            if (len == 0) return ls;
            string dn = Path.GetFileName(outPath);
            string dn2 = $"{dn}/";
            int subStart = fs[0].Replace(@"\", @"/").IndexOf(dn2) + dn2.Length;
            Action<string> action = file => {
                string nn = file.Substring(subStart);
                if (nn == dn) return; //ab包所有资源的依赖关系信息
                //忽略检查文件列表
                if (nn == "luascript.zip" //进入游戏前需要用的lua脚本
                || nn == "files.txt" || nn == "files2.txt" || nn == "update.json" || nn == "1update.json" //版本更新文件
                || nn == "package.json"//包内参数信息
                || nn == "patch_misc.asset"//外网出问题时用的补丁脚本
                || nn == "lua_patch.asset"//lua补丁更新
                || nn == "files2.bytes"//版本更新压缩文件
                ) return;
                if (nn.StartsWith("exec_")) return; //lua脚本,包括配置表及补丁
                if (nn.StartsWith("lang_exec")) return; //所有语言表
                if (nn.StartsWith("diffres_exec")) return; //地区差异化脚本
                nn = nn.Replace(@"\", @"/");
                bool flg = abNames.Contains(nn);
                //Debug.LogError(flg + "____" + nn + "____" + file);
                if (!flg)
                {
                    ls.Add(nn);
                }
            };
            fs.AsParallel().ForAll<string>(action);
            return ls;
        }
        //检查ab资源有效性,并删除冗余,outPath 输出目录, mode 模式,0不处理,1仅打印,2移出冗余,3删除冗余,4复制冗余
        public static void CheckAndHandleAbVaild(string outPath, int mode = 0)
        {
            Debug.Log($"check res:mode={mode},op={outPath}");
            if (mode == 0) return;
            string[] fs = GetFiles(outPath);// Directory.GetFiles(outPath, "*", SearchOption.AllDirectories);
            if (fs.Length == 0) return;
            List<string> rels = CheckAbVaild(abnames, outPath, fs);
            var len = rels.Count;
            StringBuilder reSb = new StringBuilder("res total=" + fs.Length + ",ab total=" + abnames.Length + ",rm total=" + len);
            if (len == 0)
            {
                Debug.Log(reSb);
                return;
            }
            rels.Sort();
            string fstr = string.Join("\n", rels);
            reSb.AppendLine().Append(fstr);
            string str = reSb.ToString();
            Debug.Log(str);
            string moveDir = "del_ab/" + DateTime.Now.ToString("yyyy_MM_dd_HH_mm_ss");
            Directory.CreateDirectory(moveDir);
            File.WriteAllText($"{moveDir}.txt", str);
            if (mode != 2 && mode != 3 && mode != 4) return;
            moveDir = moveDir + "/";
            if (!outPath.EndsWith("/")) outPath = outPath + "/";
            Action<string> action = fn => {
                if (string.IsNullOrEmpty(fn)) return;//jenkins打包偶现会出现空文件路径,原因未知
                string f = outPath + fn;
                string mf = f + ".manifest";
                if (mode == 2)
                {
                    string tf = moveDir + fn;
                    string td = Path.GetDirectoryName(tf);
                    if (!Directory.Exists(td)) Directory.CreateDirectory(td);
                    File.Move(f, tf);
                    string tmf = tf + ".manifest";
                    if (File.Exists(mf)) File.Move(mf, tmf);
                }
                else if (mode == 3)
                {
                    File.Delete(f);
                    if (File.Exists(mf)) File.Delete(mf);
                }
                else if (mode == 4)
                {
                    string tf = moveDir + fn;
                    string td = Path.GetDirectoryName(tf);
                    if (!Directory.Exists(td)) Directory.CreateDirectory(td);
                    File.Copy(f, tf);
                    string tmf = tf + ".manifest";
                    if (File.Exists(mf)) File.Copy(mf, tmf);
                }
            };
            rels.AsParallel().ForAll<string>(action);
        }
        #endregion
    }
}
