using UnityEngine;
using System.Collections.Generic;
using UnityEditor;
using System.IO;
using System.Text;
using System;

public class ResSizeManager : MonoBehaviour {

	public static int maxSize = 10; //大小上限（单位m）
    public static string firstFileLocalPath = "/editorconfig/restag/firstres.bytes";
    public static string rootPath = ""; //Application.dataPath + "/Anima2D";

    [MenuItem("Tools/包内资源管理器")]
    public static void ResSizeManagerTool()
    {
        maxSize = maxSize * 1048576; //单位byte
        double totalFileSize = 0;
        DirectoryInfo dictoryInfo = new DirectoryInfo(rootPath);
        List<string> allFilePath = new List<string>();
        FileInfo[] fileInfos = dictoryInfo.GetFiles("*", SearchOption.AllDirectories);
        
        string firstFilePath = Application.dataPath + firstFileLocalPath;
        List<string> firstFiles = new List<string>(); //GetFileListBy(firstFilePath);
        //firstFiles.Add("scripts/editor/triangle/io/datareader.cs");
        //firstFiles.Add("scripts/editor/triangle/tools/quadtree.cs");
        //firstFiles.Add("scripts/editor/exporter/exporter.cs");
        //firstFiles.Add("Scripts/Editor/Triangle/Algorithm/Dwyer.cs");
        rootPath = dictoryInfo.FullName;
		rootPath =  rootPath.Replace("\\", "/");
        fileInfos = SortByPriorityFileList(fileInfos, firstFiles, rootPath);
        //string extension = "";
        //排序
        //System.Array.Sort(fileInfos, (x, y) => { return x.Length.CompareTo(y.Length); });
        //文件原始相对路径
        string fileLocalPath = "";
        //目标根目录路径
        string targetRootPath = Application.streamingAssetsPath + "/AssetBundles/Android";

        string targetPath = "";
        foreach (FileInfo file in fileInfos)
        {
            //Debug.Log("文件名: " + file.Name + "文件大小: " + file.Length/1048576);
            if (file != null)
            {
                totalFileSize += file.Length;
                //Debug.Log("累计文件大小: " + totalFileSize/1048576);
            }
            string fileFullPath = Path.Combine(file.DirectoryName, file.Name);

			fileFullPath = fileFullPath.Replace("\\", "/");
            fileLocalPath = fileFullPath.Replace(rootPath, "");
            targetRootPath = targetRootPath.Replace("\\", "/");
            targetPath = targetRootPath + fileLocalPath;
            //Debug.Log("相对路径:" + fileLocalPath + "目标路径:" + targetPath);
            //Debug.Log("根路径:" + rootPath + "目标根路径:" + targetRootPath);
            int last = targetPath.LastIndexOf('/');
            //Debug.Log("last index:" + last);
            string directory = targetPath.Substring(0, last);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            //删除要替换的文件
            if (File.Exists(targetPath))
            {
                File.Delete(targetPath);
            }

            file.CopyTo(targetPath);
            //File.Move(sourceFilePath, targetPath);
            if (totalFileSize >= maxSize)
            {
                Debug.Log("达到上限: " + totalFileSize/1048576);
                break;
            }
        }
        totalFileSize /= 1048576;//byte换算为mb
        Debug.Log(string.Format("输出目录：{0}；总大小：{1}mb", rootPath, totalFileSize));
    }

    //加载文件列表
    private static List<string> GetFileListBy(string filePath)
    {
        string[] lines = System.IO.File.ReadAllLines(filePath);
        List<string> files = new List<string>();
        foreach(string line in lines)
        {
            string newLine = line.Replace("\\", "/");
            files.Add(newLine);
            //Debug.Log("文件名字！！！！！！！！！！！: " + newLine);
        }
        return files;
    }

    //根据文件列表排序
    private static FileInfo[] SortByPriorityFileList(FileInfo[] fileInfos, List<string> firstFiles, string rootPath)
    {
        System.Array.Sort(fileInfos, (x, y) => {
            string xfileFullPath = Path.Combine(x.DirectoryName, x.Name);
			xfileFullPath = xfileFullPath.Replace("\\", "/");
            string xfileLocalPath = xfileFullPath.Replace(rootPath + "/", "").ToLower();
            string yfileFullPath = Path.Combine(y.DirectoryName, y.Name);
			yfileFullPath = yfileFullPath.Replace("\\", "/");
            string yfileLocalPath = yfileFullPath.Replace(rootPath + "/", "").ToLower();
			
            if (firstFiles.IndexOf(xfileLocalPath) >= 0)
            {   
                return 1;
            }
            else if (firstFiles.IndexOf(yfileLocalPath) >= 0)
            {   
                return -1;
            }
             return 0;
            });
		Array.Reverse(fileInfos); //数组反转，firstFiles内文件优先
        return fileInfos;
    }

}
