using UnityEngine;
using UnityEditor;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using System.Text;
using System;
using System.Linq;
public static class BaseExt
{
    public static void ForEach<T>(this T[] array, Action<T> action)
    {
        foreach (var e in array)
        {
            action(e);
        }
    }

    [MenuItem("Test/TestModifyGoogleMobileAdsReference &w")]
    public static void TestModifyGoogleMobileAdsReference()
    {

        AutoBuildDLL.ModifyDlls();
        //"".Print("xml:", csproject_document.InnerXml);

        ////csproject_document.Save(script_cs_path);
        //csproject_document = null;
    }
}

public class AutoBuildDLL : EditorWindow
{
    public enum DLL_BUILD_OPTIONS
    {
        Debug = 0,
        Release = 1,
        ReleaseAndroid = 2,
        ReleaseAndroidProfiler = 3,
        ReleaseIos = 4,
        ReleasePc = 5,
        ReleaseIos_2022 = 6,
        ReleaseAndroid_2022 = 7
    }
#if UNITY_2022_1_OR_NEWER
    static DLL_BUILD_OPTIONS op = DLL_BUILD_OPTIONS.ReleaseAndroid_2022;
#else
    static DLL_BUILD_OPTIONS op = DLL_BUILD_OPTIONS.ReleaseAndroid;
#endif

    static bool firstPass = true;
#if UNITY_EDITOR_OSX
    static string ms_DevenvPath = "/Library/Frameworks/Mono.framework/Versions/Current/bin/msbuild";
#else
    static string ms_DevenvPath = "C:\\Program Files (x86)\\Microsoft Visual Studio\\2017\\Enterprise\\Common7\\IDE\\devenv.com";
#endif
    static List<string> ms_DevenvPathList =new List<string>{
        @"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\devenv.exe",
        "C:/ProgramFiles/VS2019/Common7/IDE/devenv.exe",
        "C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\Common7\\IDE\\devenv.com"
    };
    static string ms_ProjectPath = "";
    static readonly string build_config = "../../Tools/GameConfig/custom/custom_jenkins.json";

    /// <summary>
    /// The ms CS project path buffer. /Client/CSharp
    /// </summary>
    static string ms_CSProjectPathBuffer = "";
    static string ms_CSharpPathWarning = "";
    static string ms_DevenvPathWarning = "";
    static string denenvPath_Buffer = "";
    static string projectPath_Buffer = "";
    static string cachepath = "Assets/dllBuildEnvCache.xml";
    static bool inited = false;
    static XmlDocument cachefile = null;
    private static string msLogF = "";
    private static string msLogS = "";
    private static string ToolVersion = "1.0";

    private bool hotfixInjext = false;

    AutoBuildDLL()
    {
        titleContent = new GUIContent("动态库编译工具");
    }

    private void OnEnable()
    {
        cachepath = GetCachepath();
        //msLogS = "";
        //msLogF = "";
    }

    public static string GetCachepath()
    {
        return Application.dataPath + "/dllBuildEnvCache.xml";
    }

    private static void Init()
    {
        if (!inited)
        {
            cachepath = GetCachepath();
            LoadCache();
            CheckDefaultCSharpPath();
            ms_CSProjectPathBuffer = ms_ProjectPath;

            FileInfo myfir = new FileInfo(ms_DevenvPath);
            if (myfir.Exists)
            {
                ms_DevenvPathWarning = "已找到devenv.com!";
                SaveDenenvCache(ms_DevenvPath);
            }
            else
            {
                //Debug.Log("未找到devenv.com!");
                ms_DevenvPathWarning = "未找到devenv.com!";
            }
            op = LoadSetting();
            msLogF = LoadLog(true);
            msLogS = LoadLog(false);
            inited = true;
        }
    }

    [MenuItem("Tool/DLL Builder")]
    static void ShowWindow()
    {
        EditorWindow.GetWindow(typeof(AutoBuildDLL));
        msLogF = "";
        msLogS = "";
        RefreshCacheFile();
        SaveLog(" ", true);
        SaveLog(" ", false);
        Init();
    }

    [MenuItem("Tool/DLL Commit")]
    static void CommitDll()
    {
        var text = @"print (aa, ss(93)))";
        Regex ms_CommentLogRegex = new Regex(@"([\n\t ]*?)(print|log.Log|log.LogFormat|log.Warning|log.WarningFormat) *([^()]|(?<open>\()|(?<-open>\)))*(?(open)(?!))");//""
        var match = ms_CommentLogRegex.Match(text);
        if (match.Success)
            foreach (Group _ in match.Groups)
            {
                Debug.Log(_.Value);
            }

        /*ShowWindow();

        System.Diagnostics.Process p = new System.Diagnostics.Process();
        p.StartInfo.FileName = Application.dataPath + "/Scripts/Editor/Other/copy_commit.bat";
        p.StartInfo.Arguments = (op == DLL_BUILD_OPTIONS.ReleaseAndroid ? "android" : "ios");
        p.StartInfo.UseShellExecute = true;
        p.StartInfo.RedirectStandardInput = false;
        p.StartInfo.RedirectStandardOutput = false;
        p.StartInfo.RedirectStandardError = false;
        p.StartInfo.CreateNoWindow = false;

        // 没有重定向输出。下面的无效
        //p.EnableRaisingEvents = true;
        //p.BeginOutputReadLine();
        //p.OutputDataReceived += (object sender, System.Diagnostics.DataReceivedEventArgs e) =>
        //{
        //    Debug.Log(e.Data);
        //};

        System.Console.InputEncoding = System.Text.Encoding.UTF8;
        p.Start();
        p.WaitForExit();*/
    }

    void OnGUI()
    {
        Init();
        GUILayout.BeginVertical();
        GUILayout.Space(10);
        GUI.skin.label.fontSize = 24;
        GUI.skin.label.alignment = TextAnchor.MiddleCenter;
        GUILayout.Label("动态库编译工具");
        GUI.skin.label.fontSize = 12;
        GUI.skin.label.alignment = TextAnchor.UpperLeft;
        GUILayoutOption[] opt = { GUILayout.ExpandWidth(true), GUILayout.Width(600.0f) };

        ms_CSProjectPathBuffer = EditorGUILayout.TextField("CSharp Path", ms_CSProjectPathBuffer, opt);
        if (IsCSharpPath(ms_CSProjectPathBuffer))
        {
            ms_CSharpPathWarning = "已找到CSharp项目!";
            SaveCSharpPathCache(ms_CSProjectPathBuffer);
        }
        else
        {
            ms_CSharpPathWarning = "未找到CSharp项目!";
        }
        GUILayout.Space(10);
        GUILayout.Label(ms_CSharpPathWarning);

        if (!ms_DevenvPath.Equals(""))
        {
            FileInfo myfir = new FileInfo(ms_DevenvPath);
            if (myfir.Exists)
            {
                ms_DevenvPathWarning = "已找到devenv.com!";
                SaveDenenvCache(ms_DevenvPath);
            }
            else
            {
                ms_DevenvPathWarning = "未找到devenv.com!";
            }
        }
        else
        {
            ms_DevenvPathWarning = "未找到devenv.com!";
        }
        GUILayout.Space(10);

        ms_DevenvPath = EditorGUILayout.TextField("denenv.com", ms_DevenvPath, opt);
        GUILayout.Label(ms_DevenvPathWarning);

        op = (DLL_BUILD_OPTIONS)EditorGUILayout.EnumPopup("Build Option:", op);

        GUILayout.Space(10);

        if (GUILayout.Button("Build"))
        {
            SaveScriptCompilingState(0);
            msLogF = "";
            msLogS = "";
            // 编译 dll
            if (BuildDLL())
            {
                // 若不通过消息监听 + 状态机机制编译 xlua gen 和 hotfix ，则放开下面的代码
                // generate xlua
                firstPass = false;
                if (BuildDLL())
                {
                    // hotfix
                    SaveScriptCompilingState(0);
                    hotfixInjext = true;
                }
            }
        }
        if(hotfixInjext && !EditorApplication.isCompiling)
        {
            HotfixInject();
            EditorUtility.ClearProgressBar();
            hotfixInjext = false;
        }
        GUILayout.Space(10);
        // string outputtest = msLogF;
        GUILayout.Label(msLogF);
        GUILayout.Space(5);
        //outputtest = msLogS;
        GUILayout.Label(msLogS);
        GUILayout.EndVertical();
    }

    public static void BatBuildDll()
    {
        msLogF = "";
        msLogS = "";
        // 通过代码调用时，若未进行 Init 初始化，cachepath，ms_DevenvPath 等均未初始化，直接使用代码设置的默认值，可能找不到 ms_DevenvPath 编译器并丢失缓存配置
        Init();
        SaveScriptCompilingState(0);
        firstPass = true;
        StringBuilder builder = new StringBuilder();
        System.Environment.GetCommandLineArgs().ForEach((str) => { builder.Append(str + " "); });
        var command = builder.ToString();
        Match match = Regex.Match(command, @"buildoption (\d+)");
        if (match.Success)
        {
            op = (DLL_BUILD_OPTIONS)Convert.ToInt32(match.Groups[1].Value);
#if UNITY_2022_1_OR_NEWER
            if (op == DLL_BUILD_OPTIONS.ReleaseIos)
            {
                op = DLL_BUILD_OPTIONS.ReleaseIos_2022;
				Debug.Log("Force ReleaseIos_2022");
            }
#endif

        }
        match = Regex.Match(command, @"csharp (\S+)");
        if (match.Success)
        {
            ms_ProjectPath = match.Groups[1].Value;
        }

        if (Regex.IsMatch(command, "-second_pass"))
        {
            firstPass = false;
        }
        /*var args = System.Environment.GetCommandLineArgs();
        List<string> argList = new List<string>(args);
        argList.ForEach((str) => { builder.Append(str + " "); });
        args = command.Split(new string[] { " - " }, System.StringSplitOptions.RemoveEmptyEntries);
        Regex regex = new Regex(@"buildoption (\d+)", RegexOptions.Compiled);

        argList = new List<string>(args);
        var option = argList.Find((str) => {  return regex.IsMatch(str); });//.ForEach((str)=> { Debug.Log(str); });
        op = (DLL_BUILD_OPTIONS)Convert.ToInt32(regex.Match(option).Groups[1].Value);

        regex = new Regex(@"csharp (\S+)", RegexOptions.Compiled);
        option = argList.Find((str) => {  return regex.IsMatch(str); });
        ms_ProjectPath = regex.Match(option).Groups[1].Value;
        */

        // 获取编译器路径
        var building_dll_result = BuildDLL();
        // true: 仅表示正在编译，并不表示编译成功。 false: 不能编译
        if (!building_dll_result)
        {
            EditorApplication.Exit(1);
        }
        //CommitDll();
    }

    public static bool BuildDLL()
    {
        var ms_command = GetFullPath(ms_DevenvPath);
        var prj_path = GetFullPath(ms_ProjectPath);
        var build_opt_e = op;
#if UNITY_2022_3_OR_NEWER
        //这里强制转一下
        if (build_opt_e == DLL_BUILD_OPTIONS.ReleaseIos)
        {
            build_opt_e = DLL_BUILD_OPTIONS.ReleaseIos_2022;
        }
        if (build_opt_e == DLL_BUILD_OPTIONS.ReleaseAndroid)
        {
            build_opt_e = DLL_BUILD_OPTIONS.ReleaseAndroid_2022;
        }
#endif

#if UNITY_EDITOR_OSX
#else
        //如果不存在，在列表里遍历
        if (!File.Exists(ms_command))
        {
            foreach(var dev in ms_DevenvPathList)
            {
                var tmp =  GetFullPath(dev);
                if(File.Exists(tmp))
                {
                    ms_command=tmp;
                    break;
                }
            }
        }
        //检查devenv.com
        FileInfo myfir = new FileInfo(ms_command);
        if (myfir.Exists)
        {
            Debug.Log("已找到编译器ms_command："+ms_command);
            SaveDenenvCache(ms_command);
        }
        else
        {
            //Debug.Log("未找到devenv.com!");
            Debug.LogError("未能找到编译器:" + ms_command);
            return false;
        }
        //检查CSharp的.dll编译项目
        if (SetCSharpPath(ms_CSProjectPathBuffer))
        {
            ms_CSProjectPathBuffer = prj_path;
        }
        else
        {
            //Debug.Log("未找到CSharp项目!");
            Debug.LogError("未能找到CSharp项目!" + prj_path);
            return false;
        }
#endif
        Debug.Log("ms_CSProjectPathBuffer:" + ms_CSProjectPathBuffer);
        Debug.Log("build_opt_e:" + build_opt_e);
        Debug.Log("prj_path:" + prj_path);
        Debug.Log("ms_command:" + ms_command);

        return BuildDllCommand(ms_command, prj_path, build_opt_e);
    }

    static string GetFullPath(string path)
    {
        if (string.IsNullOrEmpty(path))
        {
            return path;
        }
        return Path.GetFullPath(path).Replace("\\", "/");
    }

//    public static void ScriptCompilingCB()
//    {
//        Init();
//        var compilingState = LoadScriptCompilingState();
//        Debug.Log("Compiling State:" + compilingState);
//        switch (compilingState)
//        {
//            case 0:
//                {
//                    EditorUtility.ClearProgressBar();
//                    return;
//                }
//#pragma warning disable CS0162 // 检测到无法访问的代码
//                break;
//#pragma warning restore CS0162 // 检测到无法访问的代码
//            case 1:
//                {
//                    EditorUtility.ClearProgressBar();
//                    firstPass = false;
//                    BuildDLL();
//                }
//                break;
//            case 2:
//                {
//                    EditorUtility.ClearProgressBar();
//                    SaveScriptCompilingState(0);
//                    HotfixInject();
//                }
//                break;
//        }
//    }

    public static void ModifyProjectSettingMacro(bool bEnable, string macroTag, BuildTargetGroup buildTargetGroup)
    {
        PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup, out string[] defines);
        var containWXWeChatTag = defines.Contains(macroTag);
        if (bEnable)
        {
            if (!containWXWeChatTag)
            {
                defines = defines.Append(macroTag).ToArray();
                PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, defines);
                AssetDatabase.Refresh();
            }
        }
        else
        {
            if (containWXWeChatTag)
            {
                defines = defines.Where(val => val != macroTag).ToArray();
                PlayerSettings.SetScriptingDefineSymbolsForGroup(buildTargetGroup, defines);
                AssetDatabase.Refresh();
            }
        }
        
        PlayerSettings.GetScriptingDefineSymbolsForGroup(buildTargetGroup, out defines);
        containWXWeChatTag = defines.Contains(macroTag);
        Debug.Log($"ModifyProjectSettingMacro:{macroTag}={containWXWeChatTag}");
    }

    public static bool BuildDllCommand(string ms_command, string prj_path, DLL_BUILD_OPTIONS build_opt_e)
    {
        FixCopyCmd();

        //获取.cs输出路径,script路径
        string luaGeneratePath = CSObjectWrapEditor.GeneratorConfig.common_path;
        string csharp_gen_path = prj_path.Replace("\\", "/") + "/Script/XLua/Gen/";
        string scripts_path = Application.dataPath + "/Scripts/";

        luaGeneratePath = GetFullPath(luaGeneratePath);
        csharp_gen_path = GetFullPath(csharp_gen_path);
        scripts_path = GetFullPath(scripts_path);

        //检查编译类型
        string build_opt = "ReleaseAndroid";
        switch (build_opt_e)
        {
            case DLL_BUILD_OPTIONS.Debug:
                build_opt = "Debug";
                break;
            case DLL_BUILD_OPTIONS.Release:
                build_opt = "Release";
                break;
            case DLL_BUILD_OPTIONS.ReleaseAndroid:
                build_opt = "ReleaseAndroid";
                break;
            case DLL_BUILD_OPTIONS.ReleaseAndroidProfiler:
                build_opt = "ReleaseAndroidProfiler";
                break;
            case DLL_BUILD_OPTIONS.ReleaseIos:
                build_opt = "ReleaseIos";
                break;
            case DLL_BUILD_OPTIONS.ReleasePc:
                build_opt = "ReleasePc";
                break;
            case DLL_BUILD_OPTIONS.ReleaseIos_2022:
                build_opt = "ReleaseIos_2022";
                break;
            case DLL_BUILD_OPTIONS.ReleaseAndroid_2022:
                build_opt = "ReleaseAndroid_2022";
                break;
            default:
                break;
        }
        var isEditor = true;
        if (build_opt == "ReleaseAndroid" || build_opt == "ReleaseIos" || build_opt_e == DLL_BUILD_OPTIONS.ReleaseIos_2022)
        {
            isEditor = false;
        }
        Debug.LogWarning("build dll options:" + build_opt + ",firstPass:" + firstPass + ",isEditor:" + isEditor);

        SaveSetting(build_opt_e);

        #region 修改工程。不引用旧的wrap以及facebook库依赖
        //修改Script.csproj，删除Gen目录下的项
        var csproject_document = new XmlDocument();
        var script_cs_path = prj_path + "\\Script\\Script.csproj";
        script_cs_path = GetFullPath(script_cs_path);

        csproject_document.Load(script_cs_path);
        XmlElement root = csproject_document.DocumentElement;
        XmlNodeList xmlNodeList = root.GetElementsByTagName("Compile");
        XmlNode parentNode = xmlNodeList[0].ParentNode;
        //获取dll文件输出路径
        string dll_path = (prj_path + "\\Script\\" + root.GetElementsByTagName("OutputPath")[0].InnerText).Replace("\\", "/");
        dll_path = GetFullPath(dll_path);

        //修改FaceBook.Unity.dll路径
        XmlNodeList RefNodeList = root.GetElementsByTagName("Reference");
        foreach (XmlElement xl1 in RefNodeList)
        {
            if (xl1.GetAttribute("Include").Equals("Facebook.Unity"))
            {
                XmlNode FBElement = xl1.GetElementsByTagName("HintPath")[0];
                string fb_path = prj_path + "\\Script\\" + FBElement.InnerText.Substring(0, FBElement.InnerText.IndexOf("\\Facebook.Unity.dll"));
                string local_fb_path = Application.dataPath + "/FacebookSDK/Plugins";

                prj_path = GetFullPath(prj_path);
                local_fb_path = GetFullPath(local_fb_path);

                if (isEqualPath(fb_path, local_fb_path))
                {
                }
                else
                {
                    FBElement.InnerText = local_fb_path.Replace("/", "\\") + "\\Facebook.Unity.dll";
                }
                //Debug.Log("FACEBOOK PATH IS: " + fb_path);
                break;
            }
        }
        var xluaGenLList = new List<XmlElement>();
        foreach (XmlElement xl1 in xmlNodeList)
        {
            if (xl1.GetAttribute("Include").Contains("XLua\\Gen\\"))
            {
                xluaGenLList.Add(xl1);
            }
        }
        foreach(var xmlNode in xluaGenLList)
        {
            parentNode.RemoveChild(xmlNode);
        }
        xluaGenLList.Clear();

        File.Delete(script_cs_path);
        csproject_document.Save(script_cs_path);
        #endregion

        #region 上次生成的wrap文件删除
        DirectoryInfo mydir = new DirectoryInfo(prj_path + "/Script/XLua/Gen/");
        if (mydir.Exists)
        {
            FileInfo[] files = mydir.GetFiles("*.*");
            for (int i = 0; i < files.Length; i++)
            {
                files[i].Delete();
            }
        }
        #endregion

        // 根据不同unity版本替换dll引用
        ModifyDlls(isEditor);

        var lua_dll_path = prj_path + "/Script/XLua/Src/LuaDLL.cs";
        var code = File.ReadAllText(lua_dll_path);
        code = code.Replace("#define _BUILD_EDITOR", "#define BUILD_EDITOR");
        File.WriteAllText(lua_dll_path, code);
        
        var lua_dll_path1 = prj_path + "/AOT/Aot/LuaDLL.cs";
        var code1 = File.ReadAllText(lua_dll_path1);
        code1 = code1.Replace("#define _BUILD_EDITOR", "#define BUILD_EDITOR");
        File.WriteAllText(lua_dll_path1, code1);

        #region 第一次编译dll
        //启动devenv.com，编译.dll文件
        var logfile = "devenv_compiler";
        if (!firstPass)
        {
            logfile = "devenv_compiler2";
        }
        var devenv_log_path = $"{prj_path}/../../../BinClient/Client/{logfile}.txt";
        devenv_log_path = Path.GetFullPath(devenv_log_path).Replace("\\", "/");
        Debug.LogWarning($"build dll compiler log path:{devenv_log_path}");

        System.Diagnostics.Process p = new System.Diagnostics.Process();
        p.StartInfo.FileName = ms_command;
        p.StartInfo.Arguments = string.Format("{0}/Client.sln /rebuild {1} /out {2}", prj_path, build_opt, devenv_log_path);
#if UNITY_EDITOR_OSX
		p.StartInfo.FileName = "open";
		var param = string.Format ("-a /Library/Frameworks/Mono.framework/Versions/Current/bin/msbuild  -n --args {0}\\Client.sln /t:Build /p:Configuration={1} -fl -flp:logfile={2};verbosity=diagnostic", prj_path, build_opt,devenv_log_path);
		param = param.Replace ("\\", "/");
        /*if (build_opt_e == DLL_BUILD_OPTIONS.ReleaseAndroid || build_opt_e == DLL_BUILD_OPTIONS.ReleaseIos || build_opt_e == DLL_BUILD_OPTIONS.ReleaseIos2022)
        {
            param = param + string.Format(" /p:DefineConstants={0}", build_opt_e == DLL_BUILD_OPTIONS.ReleaseAndroid ? "UNITY_ANDROID" : "UNITY_IOS");
        }*/
		p.StartInfo.Arguments = param;
		Debug.Log (param);
        
        p.StartInfo.UseShellExecute = true;
        p.StartInfo.RedirectStandardInput = false;
        p.StartInfo.RedirectStandardOutput = false;
        p.StartInfo.RedirectStandardError = false;
        p.StartInfo.CreateNoWindow = false;
#else
        p.StartInfo.UseShellExecute = false;
        p.StartInfo.RedirectStandardInput = false;
        p.StartInfo.RedirectStandardOutput = true;
        p.StartInfo.RedirectStandardError = false;
        p.StartInfo.CreateNoWindow = false;
#endif

        System.Console.InputEncoding = System.Text.Encoding.UTF8;
        if (firstPass)
        {
            try
            {
                //AssetDatabase.StartAssetEditing();

                p.Start();

#if !UNITY_EDITOR_OSX
                UnityEngine.Debug.Log(p.StandardOutput.ReadToEnd());
#endif
                p.WaitForExit();

                var exitCode = p.ExitCode;
                msLogF = "第一次编译: " + exitCode;
                SaveLog(msLogF, true);
                //outlog = UTF8Convertion(outlog);
                Debug.Log("第一次编译输出：\n" + msLogF);
                Debug.Log("first pass buidl:\n" + msLogF);
                p.Close();

                if (exitCode != 0)
                {
                    Debug.Log("first compile Error: " + exitCode);
                    return false;
                }
                if (isEqualPath(dll_path, scripts_path))
                {
                    //Debug.Log("DLL PATH AND SCRIPTS PATH ARE SAME!");
                }
                else
                {
                    //Debug.Log("DLL PATH AND SCRIPTS PATH ARE NOT SAME!");
                    MoveAllFiles(dll_path, scripts_path, "*.dll");
                }
                SaveScriptCompilingState(1);
                EditorUtility.DisplayProgressBar("Hold On", "Script Compiling...wait.", 0.1f);
            }
            catch (Exception e)
            {
                Debug.LogError("生成第一遍 dll 失败:" + e);
                return false;
            }
            finally
            {
                //AssetDatabase.StopAssetEditing();
                Debug.Log("StopAssetEditing");
            }

            //刷新导入
            AssetDatabase.Refresh();
            //AssetDatabase.ImportAsset("Assets/Scripts/Editor/Other/BuildScript.cs", ImportAssetOptions.ForceUpdate);
            //#if UNITY_EDITOR_OSX
            return true;
            //#endif
        }
        #endregion


        //清理XLua生成的接口
        CSObjectWrapEditor.Generator.ClearAll();
        if (build_opt_e != DLL_BUILD_OPTIONS.Debug)
        {
            #region 生成XLua并加入工程
            //生成Gen下的.cs文件
            CSObjectWrapEditor.Generator.GenAll();

            bool isIos = build_opt.Contains("Ios");

            ///替换GameObjectWrap SetActive
            var GameObjectWrapPath = csharp_gen_path + "/UnityEngine_GameObjectWrap.cs";
            if (File.Exists(GameObjectWrapPath))
            {
                var gowContent = File.ReadAllText(GameObjectWrapPath);
                gowContent = gowContent
                    .Replace("using XLua;", "using XLua;\nusing War.Script;").
                    Replace("gen_to_be_invoked.SetActive(", "gen_to_be_invoked.SetActiveEx(");
                if (isIos)
                {
                    gowContent = gowContent.
                         Replace("var gen_ret = gen_to_be_invoked.GetComponent( _type );", "var gen_ret = gen_to_be_invoked.GetComponentEx( _type );")
                    ;
                }

                File.WriteAllText(GameObjectWrapPath, gowContent);
            }
            else
            {
                "".Print("Error:GameObjectWrapPath not found", GameObjectWrapPath);
            }

            if (isIos)
            {
                ///替换UnityEngineComponentWrap GetComponent
                var componentWrapPath = csharp_gen_path + "/UnityEngine_ComponentWrap.cs";
                if (File.Exists(componentWrapPath))
                {
                    var gowContent = File.ReadAllText(componentWrapPath);
                    gowContent = gowContent
                            .Replace("using XLua;", "using XLua;\nusing War.Script;").Replace(
                                "var gen_ret = gen_to_be_invoked.GetComponent( _type );",
                                "var gen_ret = gen_to_be_invoked.GetComponentEx( _type );")
                        ;
                    File.WriteAllText(componentWrapPath, gowContent);
                }
                else
                {
                    "".Print("Error:componentWrapPath not found", componentWrapPath);
                }
            }

            ///替换UnityEngine_Events_UnityEventWrap
            /////G:\xHero\Src\Client\CSharp\Script\XLua\Gen\UnityEngine_Events_UnityEventWrap.cs
            var ButtonWrapPath = csharp_gen_path + "/UnityEngine_Events_UnityEventWrap.cs";
            if (File.Exists(ButtonWrapPath))
            {
                var gowContent = File.ReadAllText(ButtonWrapPath);
                gowContent = gowContent
                    .Replace("using XLua;", "using XLua;\nusing War.Script;").
                    Replace("gen_to_be_invoked.RemoveListener(", "gen_to_be_invoked.RemoveListenersEx(");
                File.WriteAllText(ButtonWrapPath, gowContent);
            }
            else
            {
                "".Print("Error:ButtonWrapPath not found", ButtonWrapPath);
            }

            if (isEqualPath(luaGeneratePath, csharp_gen_path))
            {
                //Debug.Log("LUA PATH AND GEN PATH ARE SAME!");
            }
            else
            {
                //Debug.Log("LUA PATH AND GEN PATH ARE NOT SAME!");
                MoveAllFiles(luaGeneratePath, csharp_gen_path, "*.cs");
            }
            //修改Script.csproj，添加Gen目录下的项
            if (mydir.Exists)
            {
                FileInfo[] files = mydir.GetFiles("*.cs");
                for (int i = 0; i < files.Length; i++)
                {
                    XmlElement newelement = csproject_document.CreateElement("Compile", parentNode.NamespaceURI);
                    newelement.SetAttribute("Include", "XLua\\Gen\\" + files[i].Name);
                    parentNode.AppendChild(newelement);
                }
            }


            // 根据不同unity版本替换dll引用
            ModifyDlls(isEditor);
            //修改谷歌广告 GoogleMobileAds 引用
            ModifyGoogleMobileAdsReference(root, csproject_document);

            csproject_document.Save(script_cs_path);
            #endregion

            code = File.ReadAllText(lua_dll_path);
            code = code.Replace("#define BUILD_EDITOR", "#define _BUILD_EDITOR");
            File.WriteAllText(lua_dll_path, code);
            
            code1 = File.ReadAllText(lua_dll_path1);
            code1 = code1.Replace("#define BUILD_EDITOR", "#define _BUILD_EDITOR");
            File.WriteAllText(lua_dll_path1, code1);

            #region swap code block
            SwapCodeBlock();
            #endregion

            try
            {
                AssetDatabase.StartAssetEditing();

                #region 第二次编译dll
                //再次启动denenv.com，编译.dll文件
                p.Start();

#if !UNITY_EDITOR_OSX
                UnityEngine.Debug.Log(p.StandardOutput.ReadToEnd());
#endif

                p.WaitForExit();

                var exitCode = p.ExitCode;
                msLogS = "第二次编译: " + exitCode;
                SaveLog(msLogS, false);
                Debug.Log("第二次编译输出: \n" + msLogS);
                p.Close();
                if (isEqualPath(dll_path, scripts_path))
                {
                    //Debug.Log("DLL PATH AND SCRIPTS PATH ARE SAME!");
                }
                else
                {
                    //Debug.Log("DLL PATH AND SCRIPTS PATH ARE NOT SAME!");
                    MoveAllFiles(dll_path, scripts_path, "*.dll");
                }
                #endregion

#if HOTFIX_ENABLE && (UNITY_EDITOR_OSX || UNITY_EDITOR_WIN)
                // Mac机上mdb比较奇怪，使得注入失败。所以在这删除了事。
                var all_mdb = Directory.GetFiles(scripts_path, "*.mdb", SearchOption.TopDirectoryOnly);
                foreach (var f in all_mdb)
                {
                    File.Delete(f);
                }
#endif
                SaveScriptCompilingState(2);
                EditorUtility.DisplayProgressBar("Hold On", "Script Compiling...wait.", 0.1f);
            }
            catch (Exception e)
            {
                Debug.LogError("生成第二遍 dll 失败:" + e);
                return false;
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            //刷新导入
            AssetDatabase.Refresh();
        }
        return true;
    }
    /// <summary>
    /// 根据外部参数修改google广告依赖库
    /// </summary>
    /// <param name="root"></param>
    /// <param name="document"></param>
    public static void ModifyGoogleMobileAdsReference(XmlElement root, XmlDocument document)
    {
        var isEnableAdmob = IsEnableAdMob();
        
        //XmlNodeList refNodeList = root.GetElementsByTagName("Reference");
        //XmlNode admobParentNode, admobNode;
        //admobNode = null;
        //admobParentNode = refNodeList[0].ParentNode;

        if(isEnableAdmob)
        {
            try
            {
                var listGMA = new string[]
                {
                    //"GoogleMobileAds",
                    "GoogleMobileAds.Common",
                    "GoogleMobileAds.Core",
                    "GoogleMobileAds",
                };
                var openDic = new Dictionary<string, bool>();

                foreach (var dllmark in listGMA)
                {
                    openDic[dllmark] = true;
                }
                ModifyDllReference(root, document, listGMA, openDic);

            }
            catch (Exception e)
            {
                "".Print("Error", e.ToString());
            }
        }

        //foreach (XmlElement refNode in refNodeList)
        //{

        //    string dllname = refNode.GetAttribute("Include");
        //    var sps = dllname.Split(',');
        //    if (sps.Length > 0)
        //    {
        //        dllname = sps[0];
        //    }
        //    if (dllname.Equals("GoogleMobileAds"))
        //    {
        //        admobNode = refNode;
        //        break;
        //    }
        //}

        //if (isEnableAdmob)
        //{
        //    if (admobNode == null)
        //    {
        //        var _refNode = document.CreateElement("Reference");
        //        XmlAttribute plistAt = document.CreateAttribute("Include");
        //        plistAt.Value = "GoogleMobileAds";
        //        _refNode.Attributes.Append(plistAt);
        //        admobParentNode.AppendChild(_refNode);

        //        var hitPath = document.CreateNode("element", "HintPath", "");
        //        hitPath.InnerText = "..\\UnityDlls\\GoogleMobileAds.dll";
        //        _refNode.AppendChild(hitPath);

        //        var _private = document.CreateNode("element", "Private", "");
        //        _private.InnerText = "False";
        //        _refNode.AppendChild(_private);
        //    }
        //}
        //else
        //{
        //    if (admobNode != null)
        //    {
        //        var tmpParentNode = admobNode.ParentNode;
        //        tmpParentNode.RemoveChild(admobNode);
        //    }
        //}
    }

    // 是否开启admob
    public static bool IsEnableAdMob()
    {
        bool isEnableAdmob = false;
        if (File.Exists(build_config))
        {
            var strBuildConfig = File.ReadAllText(build_config);
            var buildConfig = (Dictionary<string, object>)MiniJSON.Json.Deserialize(strBuildConfig);
            isEnableAdmob = War.Base.BuildScript.IsEnableSwitch(buildConfig, "ENABLE_ADMOB");
            //return;
        }
        else
        {
            isEnableAdmob = Directory.Exists(Application.dataPath + "/Plugins/Admob/GoogleMobileAds");
        }

        return isEnableAdmob;
    }

    // 在开启admob时dll_folder添加_admob后缀
    public static void CheckDllFolder(ref string folder)
    {
        if(!string.IsNullOrEmpty(folder) && !folder.EndsWith("_admob") && IsEnableAdMob())
        {
            folder += "_admob"; // 添加_admob后缀
        }
    }

    /// <summary>
    /// 根据当前unity版本修改对应dll依赖库
    /// </summary>
    /// <param name="root"></param>
    /// <param name="document"></param>
    public static void ModifyDlls(bool isEditor = true)
    {

        var csprojPaths = new string[]
        {
            "../../Src/Client/CSharp/Base/Base.csproj",
            "../../Src/Client/CSharp/UI/UI.csproj",
            "../../Src/Client/CSharp/Sound/Sound.csproj",
            "../../Src/Client/CSharp/Scene/Scene.csproj",
            "../../Src/Client/CSharp/Render/Render.csproj",
            "../../Src/Client/CSharp/Game/Game.csproj",
            "../../Src/Client/CSharp/Editor/Editor.csproj",
            "../../Src/Client/CSharp/Controller/Controller.csproj",
            "../../Src/Client/CSharp/Common/Common.csproj",
            "../../Src/Client/CSharp/Battle/Battle.csproj",
            "../../Src/Client/CSharp/Script/Script.csproj",
        };

        var list2018 = new string[]
        {
            //"GoogleMobileAds",
//"UnityEditor",
"UnityEngine.AccessibilityModule",
"UnityEngine.AIModule",
"UnityEngine.AnimationModule",
"UnityEngine.ARModule",
"UnityEngine.AssetBundleModule",
"UnityEngine.AudioModule",
"UnityEngine.BaselibModule",
"UnityEngine.ClothModule",
"UnityEngine.ClusterInputModule",
"UnityEngine.ClusterRendererModule",
"UnityEngine.CoreModule",
"UnityEngine.CrashReportingModule",
"UnityEngine.DirectorModule",
//"UnityEngine",
"UnityEngine.FileSystemHttpModule",
"UnityEngine.GameCenterModule",
"UnityEngine.GridModule",
"UnityEngine.HotReloadModule",
"UnityEngine.ImageConversionModule",
"UnityEngine.IMGUIModule",
"UnityEngine.InputModule",
"UnityEngine.JSONSerializeModule",
"UnityEngine.LocalizationModule",
"UnityEngine.ParticleSystemModule",
"UnityEngine.PerformanceReportingModule",
"UnityEngine.Physics2DModule",
"UnityEngine.PhysicsModule",
"UnityEngine.ProfilerModule",
"UnityEngine.ScreenCaptureModule",
"UnityEngine.SharedInternalsModule",
"UnityEngine.SpatialTrackingModule",
"UnityEngine.SpriteMaskModule",
"UnityEngine.SpriteShapeModule",
"UnityEngine.StreamingModule",
"UnityEngine.StyleSheetsModule",
"UnityEngine.SubstanceModule",
"UnityEngine.TerrainModule",
"UnityEngine.TerrainPhysicsModule",
"UnityEngine.TextCoreModule",
"UnityEngine.TextRenderingModule",
"UnityEngine.TilemapModule",
//"UnityEngine.Timeline",
"UnityEngine.TimelineModule",
"UnityEngine.TLSModule",
//"UnityEngine.UI",
"UnityEngine.UIElementsModule",
"UnityEngine.UIModule",
"UnityEngine.UmbraModule",
"UnityEngine.UNETModule",
"UnityEngine.UnityAnalyticsModule",
"UnityEngine.UnityConnectModule",
"UnityEngine.UnityTestProtocolModule",
"UnityEngine.UnityWebRequestAssetBundleModule",
"UnityEngine.UnityWebRequestAudioModule",
"UnityEngine.UnityWebRequestModule",
"UnityEngine.UnityWebRequestTextureModule",
"UnityEngine.UnityWebRequestWWWModule",
"UnityEngine.VehiclesModule",
"UnityEngine.VFXModule",
"UnityEngine.VideoModule",
"UnityEngine.VRModule",
"UnityEngine.WindModule",
"UnityEngine.XRModule",
//"UnityEditor.Graphs",


"UnityEngine",
"UnityEngine.Timeline",
"UnityEngine.UI",
"UnityEditor",
"UnityEditor.Graphs",
        };


        var list2017 = new string[]
        {
            //"GoogleMobileAds",
"UnityEngine",
"UnityEngine.Timeline",
"UnityEngine.UI",
"UnityEditor",
"UnityEditor.Graphs",
        };
        var list2020 = new string[]
        {
            //"GoogleMobileAds",
"UnityEngine",
"UnityEngine.Timeline",
"UnityEngine.UI",
"UnityEditor",
"UnityEditor.Graphs",
        };
        var list2020_3_23 = new string[]
        {
            //"GoogleMobileAds",
            "UnityEngine",
            "UnityEngine.Timeline",
            "UnityEngine.UI",
            "UnityEditor",
            "UnityEditor.Graphs",
        };
        var list2022_3_58 = new string[]
        {
                        //"GoogleMobileAds",
            "UnityEngine",
            "UnityEngine.Timeline",
            "UnityEngine.UI",
            "UnityEditor",
            "UnityEditor.Graphs",
        };
        var listdlls = new string[][]
        {
            list2017,
            list2018,
            list2020,
            list2020_3_23,
            list2022_3_58,
        };
        var listdllpaths = new string[]
        {
            "UnityDlls",
            "Unity2018Dlls",
            "Unity2020Dlls",
            "Unity2020.3.23Dlls",
            "Unity2022DLLs",
        };

        var listDefinesPaths = new List<string>[]
        {
            new List<string>{

            },
            new List<string>{
            "UNITY_2018_1_OR_NEWER;",
            },
            new List<string>{
            "UNITY_2018_1_OR_NEWER;",
            "UNITY_2020_1_OR_NEWER;",
            },
            new List<string>{
            "UNITY_2018_1_OR_NEWER;",
            "UNITY_2020_1_OR_NEWER;",
            },
                        new List<string>{
            "UNITY_2018_1_OR_NEWER;",
            "UNITY_2020_1_OR_NEWER;",
            "UNITY_2022_1_OR_NEWER;",
            "UNITY_2022_3_OR_NEWER;",
            },
        };


        var curlist = listdlls[0];
        var curpath = listdllpaths[0];
        var curdefines = listDefinesPaths[0];
        var ind = 0;
#if UNITY_2017
        ind = 0;
#endif
#if UNITY_2018
        ind = 1;
#endif
#if UNITY_2020
        ind = 2;
#endif
#if UNITY_2020_3_23
        ind = 3;
#endif
#if UNITY_2022_3_58
        ind = 4;
#endif
        //ind = 0;
        curlist = listdlls[ind];
        curpath = listdllpaths[ind];
        curdefines = listDefinesPaths[ind];

        "".Print("DISABLE_UNIWEBVIEW", JenkinsEnv.Instance.GetBool("DISABLE_UNIWEBVIEW", false));

        if (JenkinsEnv.Instance.GetBool("DISABLE_UNIWEBVIEW", false))
        {
            "".Print("Add DISABLE_UNIWEBVIEW");
            curdefines.Add("DISABLE_UNIWEBVIEW;");
        }


        var openDic = new Dictionary<string, bool>();
        foreach (var list in listdlls)
        {
            var benable = list == curlist;
            foreach (var mark in list)
            {
                openDic[mark] = benable || (openDic.ContainsKey(mark) && openDic[mark]);
                if (!isEditor)
                {
                    if (mark.Contains("UnityEditor"))
                    {
                        openDic[mark] = false;
                    }
                }
            }
        }

        "".Print("ModifyDlls", Application.unityVersion);

        foreach (var csprpath in csprojPaths)
        {
            XmlDocument csproject_document = new XmlDocument();
            var script_cs_path = csprpath;
            script_cs_path = Path.GetFullPath(script_cs_path);

            csproject_document.Load(script_cs_path);
            XmlElement root = csproject_document.DocumentElement;
#if !UNITY_2020_1_OR_NEWER
            ModifyDllReference(root, csproject_document, list2018, openDic);
#endif

            var c = csproject_document.InnerXml;
            foreach (var dir in listdllpaths)
            {
                c = c.Replace(dir, "UnityDlls");
            }
            c = c.Replace("UnityDlls", curpath);

            // 清理高版本宏
            foreach (var cds in listDefinesPaths)
            {
                foreach (var d in cds)
                {
                    c = c.Replace(d, "");
                }
            }

            // 设置高版本宏
            foreach (var d in curdefines)
            {
                c = c.Replace("UNITY_2017_1_OR_NEWER;", "UNITY_2017_1_OR_NEWER;" + d);
            }


            File.WriteAllText(script_cs_path, c);
        }
        "".Print("ModifyDlls End");
    }
    public static void ModifyDllReference(XmlElement root, XmlDocument document, string[] marks, Dictionary<string, bool> openDic)
    {
        XmlNodeList refNodeList = root.GetElementsByTagName("Reference");
        XmlNode xmlParentNode, xmlNode;
        xmlNode = null;
        //var mark = marks[0];

        xmlParentNode = refNodeList[0].ParentNode;
        var dicRefs = new Dictionary<string, XmlNode>();
        foreach (XmlElement refNode in refNodeList)
        {
            string dllname = refNode.GetAttribute("Include");
            var sps = dllname.Split(',');
            if (sps.Length > 0)
            {
                dllname = sps[0];
            }

            dicRefs[dllname] = refNode;
        }


        bool isEnableDll;
        foreach (var mark in marks)
        {
            dicRefs.TryGetValue(mark, out xmlNode);
            openDic.TryGetValue(mark, out isEnableDll);

            if (isEnableDll && xmlNode == null)
            {
                var _refNode = document.CreateElement("Reference");
                XmlAttribute plistAt = document.CreateAttribute("Include");
                plistAt.Value = mark;
                _refNode.Attributes.Append(plistAt);
                xmlParentNode.AppendChild(_refNode);

                var hitPath = document.CreateNode("element", "HintPath", "");
                hitPath.InnerText = string.Format("..\\UnityDlls\\{0}.dll", mark);
                _refNode.AppendChild(hitPath);

                var _private = document.CreateNode("element", "Private", "");
                _private.InnerText = "False";
                _refNode.AppendChild(_private);
                continue;
            }
            if (!isEnableDll && xmlNode != null)
            {
                var tmpParentNode = xmlNode.ParentNode;
                tmpParentNode.RemoveChild(xmlNode);
                continue;
            }
        }

    }

    [MenuItem("Assets/aSwap")]
    public static void SwapCodeBlock()
    {
        var time = System.DateTime.UtcNow;
        var exe_command = "";
        var unity_exe_key = "";
        var unity_exe_value = "";
        var cmdArgs = System.Environment.GetCommandLineArgs();
        var cmdCount = cmdArgs.Length;
        for (int i = 0; i < cmdCount; i++)
        {
            var str = cmdArgs[i];
            Debug.Log("GetCommandLineArgs:" + str);
            if (str.Contains("dotnet"))
            {
                exe_command = str;
            }
            if (str.Contains("-unity_exe"))
            {
                unity_exe_key = str;
                if (i < cmdCount - 1)
                {
                    unity_exe_value = cmdArgs[i + 1];
                    if (string.IsNullOrEmpty(unity_exe_value) || !File.Exists(unity_exe_value))
                    {
                        unity_exe_value = "";
                    }
                }
            }
        }

        if (string.IsNullOrEmpty(exe_command))
        {
            exe_command = System.Environment.GetCommandLineArgs()[0];
            exe_command = Path.GetDirectoryName(exe_command);
            if (string.IsNullOrEmpty(exe_command) && !string.IsNullOrEmpty(unity_exe_value))
            {
                exe_command = unity_exe_value;
                exe_command = Path.GetDirectoryName(exe_command);
            }

#if UNITY_EDITOR_WIN
            exe_command = Path.GetDirectoryName(exe_command);
            exe_command = Path.GetDirectoryName(exe_command);
            var sourceTopDictorys = Directory.GetDirectories(exe_command, "dotnet*", SearchOption.TopDirectoryOnly);
            if (sourceTopDictorys.Length == 0)
            {
                return;
            }
            Debug.Log("UNITY_EDITOR_WIN~~~~11111:" + (sourceTopDictorys.Length > 0 ? sourceTopDictorys[0] : ""));
            var exes = Directory.GetFiles(sourceTopDictorys[0], "dotnet.exe", SearchOption.AllDirectories);
#else
            exe_command = Path.GetDirectoryName(exe_command);
            Debug.Log("GetCommandLineArgs~~~~22222222222:" + exe_command);
            var exes = Directory.GetFiles(exe_command, "dotnet*", SearchOption.AllDirectories);
#endif
            if (exes.Length > 0)
            {
                exe_command = exes[0];
            }
            else
            {
                Debug.LogError("Couldnt find dotnet exe");
                return;
            }
        }
        //return;
        string dataPath = Application.dataPath;
        var obf_exe = dataPath + "/../../../Tool/Obscure/";
        System.Diagnostics.Process p = new System.Diagnostics.Process();

        Debug.Log($"exe_command:{exe_command}");

        p.StartInfo.FileName = exe_command;// "D:/Program Files/Unity Hub/Unity1940/Editor/Data/NetCore/Sdk-2.2.107/dotnet.exe"; 

        string rootPath = dataPath.Substring(0, dataPath.LastIndexOf("/Bin"));
        //修改configBase配置路径~~~~~~~~~~~~~~~~~~~~~~~
        //原始代码路径        
        string in_dir_path = rootPath + "/Src/Client/CSharp";
        //混淆代码输出路径        
        string out_dir_path = rootPath + "/Src/Client/CSharp1";

        string configBasePath = rootPath + "/Tools/Obscure/config_base.txt";
        string configBaseText = File.ReadAllText(configBasePath);
        configBaseText = configBaseText.Replace("*in_dir*", in_dir_path);
        configBaseText = configBaseText.Replace("*out_dir*", out_dir_path);
        //配置修改写入文件
        using (FileStream fs = new FileStream(configBasePath, FileMode.OpenOrCreate))
        {
            byte[] bytes = Encoding.UTF8.GetBytes(configBaseText);
            fs.Write(bytes, 0, bytes.Length);
            fs.Flush();
            fs.Close();
            Debug.Log("配置写入完成:" + configBaseText + "\n~~~~~~~~~~~~~~~~~~~~configBaseOver~~~~~~~~~~~~~~~~~~~~~~~~");
        }

        string obfDllPath = rootPath + "/Tools/Obscure/publish/Obf.dll";
        // /Users/<USER>/Roguelike_Projects/zHero_ios_xhero2_trunk/BinClient/Client/Assets/logSwap.txt
        // 日志不放到 Assets 目录下去，避免重复触发 Unity 导入,改到 BinClient/Client/logSwap.txt
        string logSwapPath = dataPath + "/../logSwap.txt";

        //using (FileStream fs = new FileStream(logSwapPath, FileMode.OpenOrCreate))
        //{
        //    Debug.Log("创建log文件" + logSwapPath);
        //}
        p.StartInfo.Arguments = string.Format("{0} > {1}", obfDllPath, logSwapPath);
        Debug.Log("Process----- FileName:" + p.StartInfo.FileName + "----Arguments:" + p.StartInfo.Arguments);
#if UNITY_EDITOR_OSX
        
        p.StartInfo.UseShellExecute = true;
        p.StartInfo.RedirectStandardInput = false;
        p.StartInfo.RedirectStandardOutput = false;
        p.StartInfo.RedirectStandardError = false;
        p.StartInfo.CreateNoWindow = false;
#else
        p.StartInfo.UseShellExecute = false;
        p.StartInfo.RedirectStandardInput = false;
        p.StartInfo.RedirectStandardOutput = true;
        p.StartInfo.RedirectStandardError = false;
        p.StartInfo.CreateNoWindow = false;
#endif
        System.Console.InputEncoding = System.Text.Encoding.UTF8;
        p.Start();
        p.WaitForExit();
        //string logSwapText = File.ReadAllText(logSwapPath);
        Debug.Log("SwapCodeBlock:" + (System.DateTime.UtcNow - time).Seconds); //"\n" + logSwapText);
    }

    public static bool HotfixInject()
    {
#if HOTFIX_ENABLE
        return XLua.Hotfix.HotfixInject();
#else
        return false;
#endif
    }

    public static void FixCopyCmd()
    {
        Debug.Log(Application.platform);
#if UNITY_EDITOR_OSX
        var dirs = new System.Collections.Generic.List<string> () {
		//	"../../BattleLogic",
			"../../Src/Client",
		};
		var echoYCopy = "echo y | copy";
		var echoYCp = "echo y | cp -r";
		foreach (var dir in dirs) {
			var path = Path.GetFullPath (dir);
			var files = Directory.GetFiles (path, "*.*proj", SearchOption.AllDirectories);
			foreach (var f in files) {

				var ft = File.ReadAllText (f);
				var bContain = ft.Contains (echoYCopy);
				if(bContain==false){
					continue;
				}
				ft = ft.Replace (echoYCopy, echoYCp);
				File.WriteAllText (f, ft);
				Debug.Log (bContain);
			}
		} 
#endif
    }
    private static string UTF8Convertion(string input)
    {
        //System.Text.UTF8Encoding utf8 = new System.Text.UTF8Encoding();
        byte[] buffer = System.Text.Encoding.GetEncoding("GBK").GetBytes(input);
        return System.Text.Encoding.GetEncoding("utf-8").GetString(buffer);
    }

    private static bool SetCSharpPath(string newpath)
    {
        if (IsCSharpPath(newpath))
        {
            ms_CSharpPathWarning = "已找到CSharp项目!";
            ms_ProjectPath = newpath;
            SaveCSharpPathCache(newpath);
            return true;
        }
        else
        {
            return CheckCurrentCSharpPath();
        }
    }

    private static bool CheckCurrentCSharpPath()
    {
        if (IsCSharpPath(ms_ProjectPath))
        {
            ms_CSharpPathWarning = "已找到CSharp项目!";
            return true;
        }
        else
        {
            return CheckDefaultCSharpPath();
        }
    }


    private static bool CheckDefaultCSharpPath()
    {
        if (IsCSharpPath(ms_ProjectPath))
        {
            ms_CSharpPathWarning = "已找到CSharp项目!";
            return true;
        }
        string default_path = Application.dataPath;
        default_path += "/../../../Src/Client/CSharp";
        DirectoryInfo tmpinfo = new DirectoryInfo(default_path);
        //Debug.Log(tmpinfo.FullName);
        FileInfo myfile = new FileInfo(default_path + "/Client.sln");
        if (myfile.Exists)
        {
            ms_CSharpPathWarning = "已找到CSharp项目!";
            ms_ProjectPath = default_path;
            return true;
        }
        else
        {
            ms_CSharpPathWarning = "未找到CSharp项目!";
            return false;
        }
    }

    private static bool IsCSharpPath(string path)
    {
        if (path.Equals(""))
        {
            return false;
            
        }
        else
        {
            FileInfo myfile = new FileInfo(path + "/Client.sln");
            if (myfile.Exists && Directory.Exists(path + "/Script"))
            {
                return true;
            }
            else
            {
                return false;
            }
        }
    }

    private static bool isEqualPath(string pathA, string pathB)
    {
        pathA.Replace("\\", "/");
        pathB.Replace("\\", "/");
        DirectoryInfo DirA = new DirectoryInfo(pathA);
        DirectoryInfo DirB = new DirectoryInfo(pathB);
        if (DirA.Exists && DirB.Exists && DirA.FullName.Equals(DirB.FullName))
        {
            // Debug.Log("Path A is: " + DirA.FullName + "\nPath B is: " + DirB.FullName);
            return true;
        }
        else
        {
            // Debug.Log("Path A is: " + DirA.FullName + "\nPath B is: " + DirB.FullName);
            return false;
        }
    }

    public static void MoveAllFiles(string src, string dst, string searchPattern = "*.*")
    {
        DirectoryInfo srcinfo = new DirectoryInfo(src);
        if (srcinfo.Exists)
        {
            dst = dst.Replace("\\", "/");
            FileInfo[] files = srcinfo.GetFiles(searchPattern);
            for (int i = 0; i < files.Length; i++)
            {

                if (File.Exists(dst + "/" + files[i].Name))
                {
                    File.Delete(dst + "/" + files[i].Name);
                }
                File.Move(files[i].FullName, dst + "/" + files[i].Name);
            }
        }
    }

    public static string GetDenenvPath(XmlDocument xmlDoc)
    {
        var denenv_path = xmlDoc.SelectSingleNode("paths").SelectSingleNode("denenv").InnerText;
        if (!denenv_path.Equals("") && (denenv_path.Contains("devenv.com") || denenv_path.Contains("devenv.exe")) && File.Exists(denenv_path))
        {
            return denenv_path;
        }
        return "";
    }

    private static void LoadCache()
    {
        RefreshCacheFile();
        denenvPath_Buffer = GetDenenvPath(cachefile);
        if (!string.IsNullOrEmpty(denenvPath_Buffer))
        {
            ms_DevenvPath = denenvPath_Buffer;
        }
        projectPath_Buffer = cachefile.SelectSingleNode("paths").SelectSingleNode("csharp").InnerText;

#if UNITY_EDITOR_OSX
        if (string.IsNullOrEmpty(projectPath_Buffer))
        {
            projectPath_Buffer = "/Users/<USER>/Desktop/Roguelike/Roguelike20181018_AN/Src/Client/CSharp";
        }
#endif
        if (IsCSharpPath(projectPath_Buffer))
        {
            ms_ProjectPath = projectPath_Buffer;
        }
    }

    private static void SaveDenenvCache(string newstr)
    {
        RefreshCacheFile();
        if (!newstr.Equals(denenvPath_Buffer))
        {
            denenvPath_Buffer = newstr;
            cachefile.SelectSingleNode("paths").SelectSingleNode("denenv").InnerText = newstr;
            SaveCacheFile();
            //save
        }
    }

    private static void SaveCSharpPathCache(string newstr)
    {
        RefreshCacheFile();
        if (!newstr.Equals(projectPath_Buffer))
        {
            projectPath_Buffer = newstr;
            //RefreshCacheFile();
            cachefile.SelectSingleNode("paths").SelectSingleNode("csharp").InnerText = newstr;
            SaveCacheFile();
            //save
        }
    }

    private static void SaveLog(string newstr, bool first)
    {
        RefreshCacheFile();
        if (first)
        {
            cachefile.SelectSingleNode("paths").SelectSingleNode("first").InnerText = newstr;
            SaveCacheFile();
        }
        else
        {
            cachefile.SelectSingleNode("paths").SelectSingleNode("second").InnerText = newstr;
            SaveCacheFile();
        }
    }

    private static string LoadLog(bool first)
    {
        RefreshCacheFile();
        if (first)
        {
            return cachefile.SelectSingleNode("paths").SelectSingleNode("first").InnerText;
        }
        else
        {
            return cachefile.SelectSingleNode("paths").SelectSingleNode("second").InnerText;
        }
    }

    /// <summary>
    /// 保存脚本编译状态
    /// </summary>
    /// <param name="state">0 没有编译状态   1 第一次编译  2 第二次编译</param>
    private static void SaveScriptCompilingState(int state)
    {
        RefreshCacheFile();
        XmlNode root = cachefile.SelectSingleNode("paths");
        XmlNode nodel = root.SelectSingleNode("ScriptCompilingState");
        if (nodel == null)
        {
            nodel = cachefile.CreateElement("ScriptCompilingState");
            root.AppendChild(nodel);
        }
        nodel.InnerText = state.ToString();
        SaveCacheFile();
    }

    /// <summary>
    /// 0 没有编译状态   1 第一次编译  2 第二次编译
    /// </summary>
    /// <returns></returns>
    private static int LoadScriptCompilingState()
    {
        XmlNode root = cachefile.SelectSingleNode("paths");
        XmlNode nodel = root.SelectSingleNode("ScriptCompilingState");
        if (nodel == null)
        {
            return 0;
        }
        return int.Parse(cachefile.SelectSingleNode("paths").SelectSingleNode("ScriptCompilingState").InnerText);
    }

    private static DLL_BUILD_OPTIONS LoadSetting()
    {
        RefreshCacheFile();
        string token = cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText;

        if (token.Equals("D"))
        {
            return DLL_BUILD_OPTIONS.Debug;
        }
        else if (token.Equals("R"))
        {
            return DLL_BUILD_OPTIONS.Release;
        }
        else if (token.Equals("RPAndroid"))
        {
            return DLL_BUILD_OPTIONS.ReleaseAndroidProfiler;
        }
        else if (token.Equals("RIos"))
        {
            return DLL_BUILD_OPTIONS.ReleaseIos;
        }
        else if (token.Equals("RIos22"))
        {
            return DLL_BUILD_OPTIONS.ReleaseIos_2022;
        }
        else if (token.Equals("RPc"))
        {
            return DLL_BUILD_OPTIONS.ReleasePc;
        }
        else
        {
#if UNITY_2022_1_OR_NEWER
            return DLL_BUILD_OPTIONS.ReleaseAndroid_2022;
#else
            return DLL_BUILD_OPTIONS.ReleaseAndroid;
#endif

        }
    }

    private static void SaveSetting(DLL_BUILD_OPTIONS option)
    {
        RefreshCacheFile();
        switch (option)
        {
            case DLL_BUILD_OPTIONS.Debug:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "D";
                break;
            case DLL_BUILD_OPTIONS.Release:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "R";
                break;
            case DLL_BUILD_OPTIONS.ReleaseAndroid:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RAndroid";
                break;
            case DLL_BUILD_OPTIONS.ReleaseAndroidProfiler:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RPAndroid";
                break;
            case DLL_BUILD_OPTIONS.ReleaseIos:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RIos";
                break;
            case DLL_BUILD_OPTIONS.ReleaseIos_2022:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RIos22";
                break;
            case DLL_BUILD_OPTIONS.ReleasePc:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RPc";
                break;
#if UNITY_2022_1_OR_NEWER
            case DLL_BUILD_OPTIONS.ReleaseAndroid_2022:
                cachefile.SelectSingleNode("paths").SelectSingleNode("set").InnerText = "RAndroid2022";
                break;
#endif
            default:
                break;
        }
        SaveCacheFile();
    }

    public static void CreateDefaultCacheFile()
    {
        Debug.Log($"start CreateDefaultCacheFile path: {cachepath}");
        cachefile = new XmlDocument();
        XmlElement root = cachefile.CreateElement("paths");
        XmlElement devenvelement = cachefile.CreateElement("denenv");
        devenvelement.InnerText = " ";
        root.AppendChild(devenvelement);

        XmlElement csharpelement = cachefile.CreateElement("csharp");
        csharpelement.InnerText = " ";
        root.AppendChild(csharpelement);

        XmlElement log1 = cachefile.CreateElement("first");
        log1.InnerText = "first";
        root.AppendChild(log1);

        XmlElement log2 = cachefile.CreateElement("second");
        log2.InnerText = "second";
        root.AppendChild(log2);

        XmlElement Setting = cachefile.CreateElement("set");
        Setting.InnerText = "RAndroid";
        root.AppendChild(Setting);

        XmlElement Version = cachefile.CreateElement("version");
        Version.InnerText = ToolVersion;
        root.AppendChild(Version);

        cachefile.AppendChild(root);
        SaveCacheFile();
        Debug.Log("==> end CreateDefaultCacheFile");
        //Debug.Log(xml.SelectSingleNode("paths").SelectSingleNode("denenv").InnerText);
    }

    private static void SaveCacheFile()
    {
        try
        {
            cachefile.Save(cachepath);
        }
        catch(Exception e)
        {
            Debug.LogError($"SaveCacheFile:{e}");
            cachefile = null;
            refreshCount++;

            if (refreshCount < EXCEPTION_LIMIT)
            {
                RefreshCacheFile();
            }
            else
            {
                refreshCount = 0;
            }
        }
    }

    // 测试函数
    //public static void TestRefreshCacheFile()
    //{
    //    cachepath = "";
    //    RefreshCacheFile();
    //}

    private static int refreshCount = 0;  // 异常次数
    private const int EXCEPTION_LIMIT = 5;  // 最大异常次数
    private static void RefreshCacheFile()
    {
        Debug.LogWarning($"RefreshCacheFile:{refreshCount}");
        if (cachefile == null)
        {
            if (!File.Exists(cachepath) || File.ReadAllBytes(cachepath).Length == 0)
            {
                CreateDefaultCacheFile();
            }
            else
            {
                try
                {
                    cachefile = new XmlDocument();
                    cachefile.Load(cachepath);
                }
                catch (Exception e)
                {
                    Debug.LogError($"RefreshCacheFile 777: {e}");
                    cachefile = null;
                    refreshCount++;
                }
                finally
                {
                    if(refreshCount < EXCEPTION_LIMIT)
                    {
                        RefreshCacheFile();
                    }
                    else
                    {
                        refreshCount = 0;
                    }
                }
            }
        }
        else if (cachefile.SelectSingleNode("paths").SelectSingleNode("version") == null || !cachefile.SelectSingleNode("paths").SelectSingleNode("version").InnerText.Equals(ToolVersion))
        {
            CreateDefaultCacheFile();
        }
    }

    public static void iosInnerBuild()
    {
        //预先生成&删除一次lua防止之后iOS报错
        CSObjectWrapEditor.Generator.GenAll();
        CSObjectWrapEditor.Generator.ClearAll();
        //生成ios动态库并导入至Unity项目

        //生成Gen下的.cs文件
        CSObjectWrapEditor.Generator.GenAll();

        //移动.cs文件，重新生成ios动态库并导入至Unity项目
    }

    public static void BatchGenerateXLua()
    {
        AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        CSObjectWrapEditor.Generator.ClearAll();
        CSObjectWrapEditor.Generator.GenAll();
    }
}
