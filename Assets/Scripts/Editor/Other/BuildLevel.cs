using com.xhero;
using System.Collections.Generic;
using System.IO;
using System.Text;
using UI.UGUIExtend;
using UnityEditor;
using UnityEngine;

namespace War.Base
{
    public class BuildLevel
    {
        /// <summary>
        /// 创建 lua 执行时所需要的场景数据配置
        /// </summary>
        [MenuItem("Assets/Level/BuildConfig")]
        static public void BuildLevelConfig()
        {
            var selectActiveObj = Selection.activeGameObject;
            if(selectActiveObj == null)
            {
                Debug.LogError("请选择需要生成的场景的根节点");
            }
            var componentSet = selectActiveObj.GetComponentsInChildren<BaseComponent>(true);
            if(componentSet.Length == 0)
            {
                //场景中没有添加任何组件
                Debug.LogError("场景中没有添加任何组件");
                //return;
            }

            var rootObj = GetRootParent(selectActiveObj);
            string prefabPath = GetPrefabFilePath(selectActiveObj);
            if (string.IsNullOrEmpty(prefabPath))
            {
                prefabPath = GetPrefabFilePath(selectActiveObj);
                if (string.IsNullOrEmpty(prefabPath))
                {
                    Debug.LogError("选择的物体不是预制体，请先生成预制体");
                    return;
                }
            }

            // 为与外网版本兼容，不创建新 C# 组件，使用 ScrollRectItem 保存数据
            var scrollItem = selectActiveObj.GetComponent<ScrollRectItem>();
            if(scrollItem == null)
            {
                scrollItem = selectActiveObj.AddComponent<ScrollRectItem>();
            }
            if(scrollItem.names == null)
            {
                scrollItem.names = new List<string>();
            }
            scrollItem.names.Clear();
            scrollItem.monos = null;

            StringBuilder cfgBuilder = new StringBuilder();
            cfgBuilder.Append("return {\n");
            var entityCount = 0;
            for (var idx = 0; idx < componentSet.Length; idx++)
            {
                var component = componentSet[idx];
                if(!component.enabled)
                {
                    continue;
                }

                scrollItem.names.Add(component.name);
                AddMonos(scrollItem, entityCount, component.gameObject);

                var baseComponentSet = component.componentSet;
                for (var componentIdx = 0; componentIdx< baseComponentSet.Count; componentIdx++)
                {
                    var baseComponent = baseComponentSet[componentIdx];
                    cfgBuilder.Append("{\ncomponentType=");
                    cfgBuilder.Append("\"" + baseComponent.componentType + "\"");
                    cfgBuilder.Append(",\ndata=");
                    cfgBuilder.Append(baseComponent.data);
                    cfgBuilder.Append(",\nidx=");
                    cfgBuilder.Append(entityCount);
                    cfgBuilder.Append(",\n},\n");
                }
                entityCount++;
            }
            cfgBuilder.Append("}\n");

            //创建 lua 配置数据
            var configPath = Path.GetDirectoryName(prefabPath) + "/levelCfg.txt";
            try
            {
                var configFileExists = File.Exists(configPath);
                File.WriteAllText(configPath, cfgBuilder.ToString());
                if(!configFileExists)
                {
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }
            }
            catch(System.Exception e)
            {
                Debug.LogError(e);
            }

            scrollItem.names.Add("levelCfg");
            var count = scrollItem.monos.Length;
            var cfgAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(configPath);
            AddMonos(scrollItem, count, cfgAsset);

            EditorUtility.SetDirty(selectActiveObj);
            EditorUtility.SetDirty(cfgAsset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        /// <summary>
        /// 通过预制体查找路径
        /// </summary>
        /// <returns></returns>
        static public string GetPrefabFilePath(GameObject prefab)
        {
            Object parentObject = PrefabUtility.GetPrefabParent(prefab);
            return AssetDatabase.GetAssetPath(parentObject);
        }

        static public GameObject GetRootParent(GameObject gameObject)
        {
            Transform rootParent;
            var curTrans = gameObject.transform;
            rootParent = curTrans.parent;
            while (rootParent != null)
            {
                curTrans = rootParent;
                rootParent = rootParent.parent;
            }
            return curTrans.gameObject;
        }

        /// <summary>
        /// 向 ScrollRectItem 添加 mono 项
        /// </summary>
        /// <param name="refer"></param>
        /// <param name="i"></param>
        /// <param name="obj"></param>
        static public void AddMonos(ScrollRectItem refer, int i, UnityEngine.Object obj)
        {
            List<UnityEngine.Object> monos = null;

            if (refer.monos != null)
                monos = new List<UnityEngine.Object>(refer.monos);
            else
                monos = new List<UnityEngine.Object>();


            if (i < 0)
            {
                monos.Add(obj);
            }
            else
            {
                while (monos.Count <= i)
                    monos.Add(null);
                monos[i] = obj;
            }
            refer.monos = monos.ToArray();
        }
    }
}
