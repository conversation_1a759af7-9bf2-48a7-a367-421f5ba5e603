using UnityEngine;
using System.IO;
using System.Collections.Generic;
using Newtonsoft.Json;

public class JenkinsEnv
{
    static JenkinsEnv instance;

    public static JenkinsEnv Instance
    {
        get
        {
            if (instance == null)
            {
                instance = new JenkinsEnv();
            }
            return instance;
        }
    }
    string cfgPath = Application.dataPath + "/../jenkins_param.txt";
    Dictionary<string, string> cache;
    public Dictionary<string, string> config
    {
        get
        {
            if (cache == null)
            {
                cache = new Dictionary<string, string>();
                cfgPath = Path.GetFullPath(cfgPath);
                if (File.Exists(cfgPath) == false)
                {
                    return cache;
                }
                var cfgText = File.ReadAllText(cfgPath);
                var lines = cfgText.Split('\n');
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i];
                    var vals = line.Split('=');
                    if (vals.Length >= 2)
                    {
                        cache[vals[0]] = vals[1];
                    }
                }
                Debug.Log(string.Format("jenkins_param:{0}", ToJson(cache)));
            }
            return cache;
        }
    }
    public void Reload()
    {
        cache = null;
    }
    public bool GetBool(string key,bool defaultvalue = false)
    {
        cache = config;
        var s = "";
        if(cache.TryGetValue(key, out s))
        {
            return s.ToLower() == "true";
        }
        return defaultvalue;
    }
    public string Get(string key,string defaultvalue="")
    {
        cache = config;
        var s = "";
        cache.TryGetValue(key, out s);
        return s ?? defaultvalue;
    }
    public void Set(string key, string defaultvalue = "")
    {
        if (cache == null) cache = config;
        if (!cache.ContainsKey(key)) cache.Add(key, defaultvalue);
        else cache[key] = defaultvalue;
    }
    public int GetInt(string key,int defI = -1)
    {
        cache = config;
        var s = "";
        cache.TryGetValue(key, out s);
        var i = defI;
        if (!int.TryParse(s, out i))
        {
            i = defI;
        }
        Debug.Log(string.Format("JenkinsEnv:{0}={1}", key, i));
        return i;
    }
    public static string ToJson(object o)
    {
        return Newtonsoft.Json.JsonConvert.SerializeObject(o, Formatting.Indented);
    }
    public static T ToObj<T>(string o)
    {
        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(o);
    }
}