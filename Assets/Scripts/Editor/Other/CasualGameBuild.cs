using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using War.Common;

namespace War.Base
{
    /// <summary>
    /// 提供小游戏打包的操作
    /// </summary>
    public class CasualGameBuild : Singleton<CasualGameBuild>
    {
        private List<string> collectionGameInPackageList = new List<string>(5);//打进包内非独立热更小游戏
        private Dictionary<string, List<string>> collectionGameInPackageStandAloneDic = new Dictionary<string, List<string>>(5);
        public List<string> shareLuaFolder = new List<string>();
        public const string game_root = "Assets/CasualGame";
        private const string PREFIX_CGAME_AB = "assets/AssetBundles/Android/casualgame/";
        // 生成分包剔除配置
        public void GenExcludeConfigs(Dictionary<string, string[]> key2mark)
        {
            string allmake_key = "allmarks";
            if (!key2mark.TryGetValue(allmake_key, out var allmarks)) return;
            Dictionary<string, string[]> key2exclude = new Dictionary<string, string[]>();
            var allmarkSet = new HashSet<string>();
            allmarkSet.UnionWith(allmarks);

            var prefix = PREFIX_CGAME_AB;
            foreach (var kv in key2mark)
            {
                string key = kv.Key;
                string[] value = kv.Value;
                if (key == allmake_key) continue;

                //if (!Directory.Exists(game_root + "/" + key)) continue;

                var set = new HashSet<string>(allmarkSet);
                set.ExceptWith(value);

                var exclude = new List<string>();
                foreach (var m in set)
                {
                    exclude.Add(prefix + m.ToLower());
                }

                key2exclude[key] = exclude.ToArray();
            }

            //allmarks内没有专门定标签的资源也需要添加到移除列表
            foreach (var k in allmarks)
            {
                string key = k;
                if (key2exclude.ContainsKey(key)) continue;
                //if (!Directory.Exists(game_root + "/" + key)) continue;

                string[] value = new string[] { k };
                var set = new HashSet<string>(allmarkSet);
                set.ExceptWith(value);

                var exclude = new List<string>();
                foreach (var m in set)
                {
                    exclude.Add(prefix + m.ToLower());
                }

                key2exclude[key] = exclude.ToArray();
            }
            var allexclude = new HashSet<string>();
            foreach (var kv in key2exclude)
            {
                allexclude.UnionWith(kv.Value);
            }
            key2exclude["All"] = new List<string>(allexclude).ToArray();

            var key2markpath = game_root + "/key2exclude.json";
            File.WriteAllText(key2markpath,ToolUti.ToJson(key2exclude));
        }
        // 1.分包剔除列表拷贝到streamingasset目录下
        // 2.出包前剔除非当前游戏资源ab
        public void Copy2Package()
        {
            BuilDirectoryByJson();
            DeleteOtherMarkRes();
        }

        //根据当前mark删除abres
        public static void DeleteOtherMarkRes()
        {
            LogHelp.Instance.Log("DeleteOtherMarkRes-start");

            var key2markpath = game_root + "/key2exclude.json";
            if (File.Exists(key2markpath))
            {
                File.Copy(key2markpath, key2markpath.Replace(game_root, UnityEngine.Application.streamingAssetsPath), true);
            }
            //根据当前mark删除abres

            if (File.Exists(key2markpath) == false)
            {
                "".Print("key2markpath no exist!");
                LogHelp.Instance.Log("DeleteOtherMarkRes-end");

                return;
            }
            var key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(File.ReadAllText(key2markpath));

            var prefix = PREFIX_CGAME_AB;
            var mark = JenkinsEnv.Instance.Get("res_key", "");
            var collection_reskey = JenkinsEnv.Instance.Get("collection_res_key", "");//合集小游戏res_key
            //小游戏合集在Jenkins上配置之后，res_key与p_stand_alone_res_key不能填写
            if (!string.IsNullOrEmpty(collection_reskey))
            {
                mark = collection_reskey;
            }

            var pRoot = $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}";
            var hc = ModifyAB.Instance.GetHashCheckForFilesTxtInPath(pRoot);

            "".Print("GetHashCheckFromFolder", pRoot, hc?.name, hc?.ToJson());

            var configpath = game_root + "/abs.json";
            if (File.Exists(configpath) == false)
            {
                "".Print("Error file not exist", configpath);
                return;
            }
            var absJson = File.ReadAllText(configpath);
            var absDic = ToolUti.ToObj<Dictionary<string, object>>(absJson);
            var mark2list = ToolUti.ToObj<Dictionary<string, List<string>>>(absDic["mark2list"]?.ToString());

            var lowerMark2list = new Dictionary<string, List<string>>();

            foreach (var kv in mark2list)
            {
                lowerMark2list[kv.Key.ToLower()] = kv.Value;
            }

            var keyList = new List<string>(hc?.list.Keys);
            if (key2exclude.TryGetValue(mark, out var removelist))
            {
                foreach (var path in removelist)
                {
                    var rpath = path.Replace(prefix, $"{Application.streamingAssetsPath}/{BuildScript.AssetBundlesOutputPath}/{BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)}/casualgame/");
                    "".Print("Copy2Package delete", mark, rpath, Directory.Exists(rpath));
                    if (Directory.Exists(rpath))
                    {
                        Directory.Delete(rpath, true);
                    }


                    // 删除files对应文件
                    if (hc && mark2list != null)
                    {
                        //var mmark = mark.Replace(PREFIX_CGAME_AB, "casualgame/");
                        var mmark = PREFIX_CGAME_AB + mark;
                        var remove_mark = path.Replace(PREFIX_CGAME_AB, "");

                        "".Print("remove filesx ", mark, remove_mark, keyList?.Count);
                        if (remove_mark != "allmarks" && lowerMark2list.ContainsKey(remove_mark))
                        {
                            var remove_ablist = lowerMark2list[remove_mark];

                            foreach (var k in remove_ablist)
                            {
                                hc.list.Remove(k);
                                "".Print("remove in files ", mark, k);
                            }
                        }
                    }
                }
                if (hc)
                {
                    File.WriteAllText(hc.name, hc.ToJson());
                }
            }
            else
            {
                "".Print("Copy2Package res_key==null");
                //默认包含所有小游戏资源，为兼容到时分包方式，可以在Jenkins上直接移除res_key配置
            }
            LogHelp.Instance.Log("DeleteOtherMarkRes-end");
        }

        public void BuilDirectoryByJson()
        {
            var key2excludepath = game_root + "/key2exclude.json";
            string key2excludeJson = null;
            Dictionary<string, string[]> key2exclude = new Dictionary<string, string[]>();
            if (File.Exists(key2excludepath))
            {
                try
                {
                    key2excludeJson = File.ReadAllText(key2excludepath);
                    key2exclude = ToolUti.ToObj<Dictionary<string, string[]>>(key2excludeJson);
                    Dictionary<string, string[]> dic = new Dictionary<string, string[]>();
                    string currPath = UnityEngine.Application.streamingAssetsPath + "/Pack";//获取当前文件夹路径
                    if (false == System.IO.Directory.Exists(currPath))//检查是否存在文件夹
                    {
                        System.IO.Directory.CreateDirectory(currPath);//创建指定文件夹
                    }
                    foreach (var mark in key2exclude)
                    {
                        if (mark.Value.Length > 0)
                        {
                            //Debug.LogError("mark.Key:" + mark.Key + ",mark.Value:" + mark.Value);
                            //TODO:将key2exclude.json各个key值生成文件夹，在对应的文件夹里再次生成json文件，json文件里仅保留对应key值内容，并且json文件中的key值改成TestDelete
                            string subPath = currPath + "/" + mark.Key;
                            if (false == System.IO.Directory.Exists(subPath))//检查是否存在文件夹
                            {
                                System.IO.Directory.CreateDirectory(subPath);//创建指定文件夹
                            }
                            string subJsonPath = subPath + "/" + mark.Key + ".json";
                            if (key2exclude.ContainsKey(mark.Key))
                                dic["TestDelete"] = new List<string>(mark.Value).ToArray();
                            File.WriteAllText(subJsonPath, ToolUti.ToJson(dic));
                            "".Print("dic", ToolUti.ToJson(dic));
                        }
                    }
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }
        }

        // 生成分版本资源配置
        public void BuildABList()
        {
            var folders = Directory.GetDirectories(game_root);
            var configpath = game_root + "/abs.json";
            Dictionary<string, object> dic = new Dictionary<string, object>();

            foreach (var f in folders)
            {
                "".Print(f);
                //if(File.Exists(configpath))
                //{
                //    File.Delete(configpath);
                //}
                var btfF = f.Replace("\\", "/");
                var assets = AssetDatabase.FindAssets("", new string[] { f });
                HashSet<string> set = new HashSet<string>();
                foreach (var guid in assets)
                {
                    var apath = AssetDatabase.GUIDToAssetPath(guid);

                    var ab = AssetDatabase.GetImplicitAssetBundleName(apath);
                    var paths = AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                    foreach (var p in paths)
                    {
                        if (p.StartsWith(btfF) == false)
                        {
                            "".Print("BuildABList error", f, ab, p);
                            ab = null;
                            break;
                        }
                    }
                    if (string.IsNullOrEmpty(ab)) continue;
                    set.Add(ab);
                }
                if (set.Count > 0)
                {
                    dic[Path.GetFileNameWithoutExtension(f)] = set;
                }
            }

            var key2markpath = game_root + "/key2mark.json";
            string key2markJson = null;
            Dictionary<string, string[]> key2mark = new Dictionary<string, string[]>();
            if (File.Exists(key2markpath))
            {

                try
                {
                    key2markJson = File.ReadAllText(key2markpath);
                    key2mark = ToolUti.ToObj<Dictionary<string, string[]>>(key2markJson);

                    //foreach (var mark in key2mark)
                    //{
                    //    if (mark.Value.Length > 0)
                    //    {
                    //        dic[mark.Key] = mark.Value;
                    //    }
                    //}
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }

            var folderslist = new List<string>(folders);
            var allMarks = folderslist.ConvertAll((m) => Path.GetFileNameWithoutExtension(m).Replace("\\", "/")).FindAll(p => !string.IsNullOrEmpty(p));

            key2mark["allmarks"] = allMarks.ToArray();
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2mark));

            var collectionMarkPath = game_root + "/collectionMark.json";
            Dictionary<string, object> collectionMarkDic = new Dictionary<string, object>();
            if (File.Exists(collectionMarkPath))
            {
                Debug.LogError(collectionMarkPath + " exist");
                var collectionMarkJsonStr = File.ReadAllText(collectionMarkPath);
                if (string.IsNullOrEmpty(collectionMarkJsonStr)) return;
                collectionMarkDic = ToolUti.ToObj<Dictionary<string, object>>(collectionMarkJsonStr);
            }

            File.WriteAllText(configpath, ToolUti.ToJson(new Dictionary<string, object>() {
                { "mark2list",dic },
                { "key2mark", key2mark},
                { "collection2mark", collectionMarkDic},
            }));

            GenExcludeConfigs(key2mark);
            //File.WriteAllText(configpath, ToolUti.ToJson(dic));
            AssetDatabase.Refresh();
        }
        private Dictionary<string, string[]> GetJsonData(string path)
        {
            if (!string.IsNullOrEmpty(path))
            {
                try
                {
                    var jsonData = File.ReadAllText(path);
                    if (!string.IsNullOrEmpty(path))
                    {
                        var data = ToolUti.ToObj<Dictionary<string, string[]>>(jsonData);
                        return data;
                    }
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }
            return new Dictionary<string, string[]>();
        }


        public void AddCommonResKey()
        { //多开小游戏
            var key2markpath = game_root + "/key2mark.json";
            var commonkeypath = game_root + "/commonkey.json";
            var key2mark = GetJsonData(key2markpath);
            var commonkey = GetJsonData(commonkeypath);
            Dictionary<string, string[]> key2mark_temp = new Dictionary<string, string[]>();
            // var mult_casualgame = JenkinsEnv.Instance.Get("mult_casualgame", "");
            var mult_casualgame_arr = new string[0];
            if (commonkey.ContainsKey("common"))
            {
                mult_casualgame_arr = commonkey["common"];
            }
            "".Print("BuildLuaAB  mult_casualgame_arr.Length:", mult_casualgame_arr.Length,ToolUti.ToJson(mult_casualgame_arr));

            var mult_casualgame_arr_list = new List<string>();
            foreach (var key in key2mark.Keys)
            {
                string[] value = key2mark[key];
                for (int i = 0; i < mult_casualgame_arr.Length; i++)
                {
                    if (mult_casualgame_arr[i].ToLower() == key.ToLower())
                    {
                        for (int value_i = 0; value_i < value.Length; value_i++)
                        {
                            if (mult_casualgame_arr_list.Contains(value[value_i])) continue;
                            mult_casualgame_arr_list.Add(value[value_i]);
                        }
                    }
                }
            }
            foreach (var key in key2mark.Keys)
            {
                "".Print("BuildLuaAB  key2mark.Keys:", key);
                var value_list = new List<string>();
                mult_casualgame_arr_list.ForEach(i => value_list.Add(i));
                string[] value = key2mark[key];
                for (int value_i = 0; value_i < value.Length; value_i++)
                {
                    if (value_list.Contains(value[value_i])) continue;
                    value_list.Add(value[value_i]);
                }
                key2mark_temp.Add(key, value_list.ToArray());
            }
            "".Print("BuildLuaAB  key2mark.temp:", ToolUti.ToJson(key2mark_temp));
            "".Print("BuildLuaAB  key2mark.mult_casualgame_arr_list:", ToolUti.ToJson(mult_casualgame_arr_list));
            //将多开的小游戏文件名添加到key2mark中
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2mark_temp));
        }

        private bool HasValue(string[] arr, string str)
        {
            for (int i = 0; i < arr.Length; i++)
            {
                "".Print("BuildLuaAB arr[i]:", arr[i], "string.IsNullOrEmpty(arr[i]):", string.IsNullOrEmpty(arr[i]));
                if (!string.IsNullOrEmpty(arr[i]) && arr[i].ToLower() == str.ToLower())
                {
                    return true;
                }
            }
            return false;
        }

        //判断目录下是否存在某个文件
         bool FileExistsInDirectory(string path, string fileName)
        {
            try
            {
                return Directory.EnumerateFiles(path, fileName, SearchOption.AllDirectories).Any();
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        
        
        //根据每个res_key 打对应lua ResKey ,命名为{res_key}_exec.asset
        public void BuildLuaAB()
        {
            var key2markpath = game_root + "/key2mark.json";
            if (File.Exists(key2markpath) == false)
            {
                "".Print("BuildLuaAB exit no key2mark.json");
                return;
            }
            LogHelp.Instance.Log("BuildLuaAB+");

            var key2markJson = File.ReadAllText(key2markpath);
            var key2mark = ToolUti.ToObj<Dictionary<string, string[]>>(key2markJson);
            var listToImport = new List<string>();
            
            var exist_lua = new HashSet<string>();
            var mainLuaPath = "Assets/Lua/";
            
            foreach (var key in key2mark.Keys)
            {
                if (key == JenkinsEnv.Instance.Get("collection_res_key", "")) continue;

                var exec_file_name = key + "_exec.asset";
                string markRoot = game_root + "/" + key + "/";
                var exc_asset_path = markRoot + exec_file_name;
                Common.ResKey rk = null;
                
                
                if (File.Exists(exc_asset_path))
                {
                    File.Delete(exc_asset_path);
                }
                
                if (!System.IO.File.Exists(exc_asset_path))
                {
                    EditorHelp.CheckDir(exc_asset_path);
                    rk = ScriptableObject.CreateInstance<ResKey>();
                    rk.keys = new System.Collections.Generic.List<ResKey.KeyValuePair>();
                    UnityEditor.AssetDatabase.CreateAsset(rk, exc_asset_path);
                    //ToolUti.SetABNameByPath(exc_asset_path);
                    //AssetDatabase.Refresh();
                }

                rk = AssetDatabase.LoadAssetAtPath<ResKey>(exc_asset_path);

                var marks = key2mark[key];
                var luas = new HashSet<string>();
                foreach (var mark in marks)
                {
                    if (string.IsNullOrEmpty(mark))
                    {
                        continue;
                    }

                    var nmarkRoot = game_root + "/" + mark + "/";
                    if (Directory.Exists(nmarkRoot) == false) continue;
                    var ls = Directory.GetFiles(nmarkRoot, "*.txt", SearchOption.AllDirectories);
                    luas.UnionWith(ls);
                    //"".Print(key, ToolUti.ToJson(ls), ToolUti.ToJson(luas));
                }

                // 已经处理过此模块名,如果冲突,则可能是因为存在同名文件，需要手动处理
                Dictionary<string, string> existModuleSet = new Dictionary<string, string>();

                var bDirty = false;
                foreach (var txtPath in luas)
                {
                    //"".Print(txtPath);

                    if (txtPath.Contains("/Lua") == false) continue;
                    var fstr = System.IO.File.ReadAllText(txtPath, System.Text.Encoding.UTF8);
                    var bytes = System.Text.Encoding.UTF8.GetBytes(fstr);
                    var filename = Path.GetFileNameWithoutExtension(txtPath);

                    //检测Lua目录是否存在该文件
                    if (FileExistsInDirectory(mainLuaPath, filename + ".txt") )
                    {
                        "".Print("BuildLuaAB FileExistsInDirectory false:", filename);
                        exist_lua.Add(filename);
                        continue;
                    }
                    
                    // 检查是否存在冲突文件名
                    if (existModuleSet.TryGetValue(filename, out var modlePath))
                    {
                        Debug.LogWarning("Exist the same file:" + modlePath + "\n" + txtPath);
                    }
                    else
                    {
                        existModuleSet[filename] = txtPath;
                    }
                    
                    
                    
                    
                    var pvalue = rk.keys.Find((kv) => kv.key == filename);
                    // 检查二进制是否变化，变化则更新或者添加
                    if (pvalue != null)
                    {
                        if (!ToolUti.CompareBytes(pvalue.value, bytes))
                        {
                            pvalue.value = bytes;
                            bDirty = true;
                        }
                    }
                    else
                    {
                        rk.keys.Add(new ResKey.KeyValuePair()
                        {
                            key = filename,
                            value = bytes
                        });
                        bDirty = true;
                    }

                }
                "".Print("exc_asset_path", exc_asset_path, markRoot, rk.keys.Count);

                if (rk.keys.Count == 0)
                {
                    "".Print("delete count=0", exc_asset_path);
                    File.Delete(exc_asset_path);
                    continue;
                }
                var ai = AssetImporter.GetAtPath(exc_asset_path);
                if (ai)
                {
                    ai.assetBundleName = exc_asset_path.Substring(exc_asset_path.IndexOf("/") + 1);
                    listToImport.Add(exc_asset_path);
                }
                if (bDirty)
                {
                    EditorUtility.SetDirty(rk);
                }
            }
            if (exist_lua.Count > 0)
                "".Print("duplicate：", ToolUti.ToJson(exist_lua));
            Debug.Log(ToolUti.ToJson(listToImport));
            UIHelper.ImportAssets(listToImport.ToArray());

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            LogHelp.Instance.Log("BuildLuaAB-");
        }


        public void Check()
        {
            //var abns = AssetDatabase.GetAllAssetBundleNames();

            //foreach (var abn in abns)
            //{
            //    if(abn.Contains("asset"))
            //}
            //var ss = AssetDatabase.GetAssetPathsFromAssetBundle("")

            var dirs = Directory.GetDirectories(Application.dataPath, "*", SearchOption.AllDirectories);
            foreach (var dir in dirs)
            {
                var metaPath = dir + ".meta";
                if (File.Exists(metaPath))
                {
                    var lines = File.ReadAllLines(metaPath);
                    var l = ArrayUtility.Find(lines, (line) =>
                    {
                        return line.Contains("assetBundleName:");
                    });
                    if (l == null) continue;
                    if (l.Trim().Length > "assetBundleName:".Length)
                    {
                        "".Print(metaPath, l);
                    }
                }
            }

        }

        public void CheckScript()
        {
            var scrPath = "../../Src/Client/CSharp/Script";
            var fs = Directory.GetFiles(scrPath, "*.cs", SearchOption.AllDirectories);
            var list = new List<string>();
            foreach (var f in fs)
            {
                var content = File.ReadAllText(f);
                if (content.Contains("CSharpCallLua"))
                //if(content.Contains("MonoBehaviour"))
                {
                    list.Add(f);
                }
            }
            "".Print("mono", ToolUti.ToJson(list));
        }


        [MenuItem("Assets/UI/GetFolderABs")]
        public static void GetFolderABs()
        {
            var sels = Selection.objects;
            var abdic = new Dictionary<string, bool>();
            foreach (var sel in sels)
            {
                var p = AssetDatabase.GetAssetPath(sel);
                if (!Directory.Exists(p)) continue;
                var files = Directory.GetFiles(p, "*", SearchOption.AllDirectories);
                foreach (var f in files)
                {
                    if (f.CEndsWith(".meta")) continue;
                    var ab = AssetDatabase.GetImplicitAssetBundleName(f);
                    abdic[ab] = true;
                }
            }
            "".Print("GetFolderABs:", ToolUti.ToJson(abdic));
            LogHelp.clipboard = ToolUti.ToJson(abdic.Keys);
        }



        /// <summary>
        /// 向files里面添加小游戏所有资源配置
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        public List<string> InsertCasualGameRes2files(string destination)
        {
            //拷贝小游戏配置 
            var abRootPath = Path.GetDirectoryName(destination);
            var f = ModifyAB.Instance.GetHashCheckFromFolder(abRootPath);
            var listInsert = CasualGameBuild.Instance.InsertCasualGameRes2files(f);
            if (listInsert.Count == 0)
            {
                "".PrintError("InsertCasualGameRes2files error,", abRootPath);
            }
            else
            {
                var filePath = f.name;
                "".Print("InsertCasualGameRes2files finish,", listInsert.Count);
                if (string.IsNullOrEmpty(filePath) == false)
                {
                    "".Print("InsertCasualGameRes2files write file,", filePath);
                    File.WriteAllText(filePath, f.ToJson());
                }
            }
            return listInsert;
        }



        /// <summary>
        /// 向files里面添加小游戏所有资源配置
        /// </summary>
        /// <param name="f"></param>
        /// <returns></returns>
        public List<string> InsertCasualGameRes2files(hashCheck f)
        {
            var listInsert = new List<string>();
            if (f == null) return listInsert;

            var patch_mark = JenkinsEnv.Instance.Get("patch_mark");
            hashCheck h = null;
            var path = "";

            if (!string.IsNullOrEmpty(patch_mark))
            {

                path = BuildScript.GetNewestFilePath(patch_mark);
            }
            else
            {

                //在生成的 ab 资源路径下的files.txt 文件
                path = string.Format("{0}/files.txt",
                    Path.Combine(BuildScript.AssetBundlesOutputPath,
                       BuildScript.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget)));

            }

            if (File.Exists(path))
            {
                var c = File.ReadAllText(path);
                try
                {
                    var fFolder = Path.GetDirectoryName(path);
                    h = ModifyAB.Instance.GetHashCheckFromFolder(fFolder);
                }
                catch (System.Exception e)
                {
                    "".PrintError("InsertCasualGameRes2files path error:", path, e.ToString());
                }
            }
            if (h == null)
            {
                return listInsert;
            }
            var list = new List<string>(h.list.Keys);
            string path_mark = "casualgame/";
            foreach (var item in list)
            {
                if (item.StartsWith(path_mark))
                {
                    listInsert.Add(item);
                }
            }

            foreach (var item in listInsert)
            {
                f.Update(item, h.list[item]);
            }
            //"".Print("InsertCasualGameRes2files", listInsert.Count, UIHelper.ToJson(list), UIHelper.ToJson(listInsert));
            return listInsert;
        }

        public void BuildCombineMark()
        {
            var key2markpath = game_root + "/key2mark.json";
            File.WriteAllText(key2markpath, ToolUti.ToJson(GetKey2MarkWithCombineJson()));
        }

        public Dictionary<string, string[]> GetCombineMark()
        {
            var combineMarkPath = game_root + "/CombineMark.json";
            var combineConfig = GetJsonData(combineMarkPath);
            return combineConfig;
        }

        public Dictionary<string, string[]> GetKey2MarkWithCombineJson()
        {
            var key2markpath = game_root + "/key2mark.json";
            var combineMarkPath = game_root + "/CombineMark.json";
            var key2mark = GetJsonData(key2markpath);
            var combineConfig = GetJsonData(combineMarkPath);

            Dictionary<string, HashSet<string>> combineGameWithDependence = new Dictionary<string, HashSet<string>>();

            foreach (var keyValuePair in combineConfig)
            {
                if (!keyValuePair.Key.StartsWith("Combine"))
                {
                    continue;
                }
                HashSet<string> dependence = new HashSet<string>();
                foreach (var casualGameKey in keyValuePair.Value)
                {
                    GetDependence(key2mark, casualGameKey, ref dependence);
                }
                combineGameWithDependence.Add(keyValuePair.Key, dependence);
            }

            foreach (var keyValuePair in combineGameWithDependence)
            {
                key2mark[keyValuePair.Key] = keyValuePair.Value.ToArray();
            }

            return key2mark;
        }

        private void GetDependence(Dictionary<string, string[]> key2Mark, string key, ref HashSet<string> result)
        {
            if (!key2Mark.ContainsKey(key))
            {
                return;
            }
            foreach (var resourceName in key2Mark[key])
            {
                if (!result.Contains(resourceName))
                {
                    result.Add(resourceName);
                    GetDependence(key2Mark, resourceName, ref result);
                }
            }
        }

        public Dictionary<string, List<string>> GetCollectionStandAloneInPackage()
        {
            return collectionGameInPackageStandAloneDic ?? null;
        }
        /// <summary>
        /// 把小游戏资源写入到key2mark.json中（这里处理小游戏合集内容）
        /// </summary>
        public void AddCollectionGameRes2Key2Mark()
        {
            var key2markpath = game_root + "/key2mark.json";
            var collectionMarkPath = game_root + "/collectionMark.json";
            if (!File.Exists(collectionMarkPath))
            {
                Debug.LogError(collectionMarkPath + " not exist");
                return;
            }
            var key2mark = GetJsonData(key2markpath);
            var collectionMarkJsonStr = File.ReadAllText(collectionMarkPath);
            if (string.IsNullOrEmpty(collectionMarkJsonStr)) return;
            collectionGameInPackageStandAloneDic.Clear();
            var collectionMarkDic = ToolUti.ToObj<Dictionary<string, object>>(collectionMarkJsonStr);
            foreach (var collectionMarkDicItem in collectionMarkDic)
            {
                Debug.Log("collectionMarkDicItem.Key:" + collectionMarkDicItem.Key);
                List<string> collectionGameInPackageStandAloneList = new List<string>();
                List<string> marks = new List<string>();
                Queue<string> dependence = new Queue<string>();
                Dictionary<string, List<string>> combineGameWithDependence = new Dictionary<string, List<string>>();
                collectionGameInPackageList.Clear();
                var collectionMarkList = ToolUti.ToObj<Dictionary<string, object>>(collectionMarkDicItem.Value?.ToString());
                foreach (var item in collectionMarkList)
                {
                    Debug.Log("collectionMarkList  Key : " + item.Key + "   value:" + item.Value);
                    if (item.Key == "package")
                    {
                        var packageList = ToolUti.ToObj<Dictionary<string, List<string>>>(collectionMarkList["package"]?.ToString());
                        foreach (var package_game in packageList)
                        {
                            if (package_game.Key == "res_key")
                            {
                                foreach (var packageItem in package_game.Value)
                                {
                                    Debug.Log("打进包内的小游戏名称：" + packageItem);
                                    collectionGameInPackageList.Add(packageItem);
                                    marks.Add(packageItem);
                                    dependence.Enqueue(packageItem);
                                }
                            }
                            else if (package_game.Key == "stand_alone")
                            {
                                foreach (var packageItem in package_game.Value)
                                {
                                    Debug.Log("打进包内的独立热更小游戏名称：" + packageItem);
                                    collectionGameInPackageStandAloneList.Add(packageItem);
                                }
                            }
                        }
                        while (dependence.Count > 0)
                        {
                            var dequeue = dependence.Dequeue();
                            if (key2mark.ContainsKey(dequeue))
                            {
                                foreach (var mark in key2mark[dequeue])
                                {
                                    if (marks.Contains(mark))
                                    {
                                        continue;
                                    }
                                    marks.Add(mark);
                                    dependence.Enqueue(mark);
                                }
                            }
                        }
                        combineGameWithDependence.Add(collectionMarkDicItem.Key, marks);
                        foreach (var keyValuePair in combineGameWithDependence)
                        {
                            key2mark[keyValuePair.Key] = keyValuePair.Value.ToArray();
                        }
                    }
                    if (item.Key.Equals("download"))
                    {
                        var packageList = ToolUti.ToObj<Dictionary<string, List<string>>>(collectionMarkList["download"]?.ToString());
                        foreach (var package_game in packageList)
                        {
                            if (package_game.Key == "res_key")
                            {
                                foreach (var packageItem in package_game.Value)
                                {
                                    Debug.Log("需要下载的小游戏名称：" + packageItem);
                                }
                            }
                            else if (package_game.Key == "stand_alone")
                            {
                                foreach (var packageItem in package_game.Value)
                                {
                                    Debug.Log("需要下载的独立热更小游戏名称：" + packageItem);
                                }
                            }
                        }
                    }
                }

                if (collectionGameInPackageStandAloneList.Count > 0)
                {
                    for (int i = 0; i < collectionGameInPackageStandAloneList.Count; i++)
                    {
                        collectionGameInPackageStandAloneDic.Add(collectionMarkDicItem.Key, collectionGameInPackageStandAloneList);
                    }
                }
                else
                {
                    Debug.Log("没有需要 打进包内的独立热更小游戏");
                }

            }
            File.WriteAllText(key2markpath, ToolUti.ToJson(key2mark));
        }

        /// <summary>
        /// 生成小游戏合集分包删除列表（需要放在GenExcludeConfigs 与 CasualGameBuildNew.UpdateJson 接口之后）
        /// </summary>
        public void GetCollectionExcludeConfig()
        {
            var key2excludepath = Application.streamingAssetsPath + "/key2exclude.json";
            var key2markpath = game_root + "/key2mark.json";
            //var casualgameexcludepath = Application.streamingAssetsPath + "/casualgameclude.json";
            var collectionMarkPath = game_root + "/collectionMark.json";
            var collectionexcludePath = Application.streamingAssetsPath + "/collectionexclude.json";
            var prefix = PREFIX_CGAME_AB;
            if (!File.Exists(key2excludepath))
            {
                "".Print(@"not exist {0}", key2excludepath);
                return;
            }
            if (!File.Exists(collectionexcludePath))
            {
                "".Print(@"not exist {0}", collectionexcludePath);
                File.Create(collectionexcludePath);
                "".Print(@"create new file: {0}", collectionexcludePath);
                "".Print(@"Whether the new file exists : {0}", File.Exists(collectionexcludePath));
            }
            if (!File.Exists(collectionMarkPath))
            {
                "".Print(@"not exist {0}", collectionMarkPath);
                return;
            }

            Dictionary<string, string[]> key2excludeDic = GetJsonData<string[]>(key2excludepath);
            Dictionary<string, object> collectionMarkDic = GetJsonData<object>(collectionMarkPath);
            AddCollectionGameRes2Key2Mark();
            if (!key2excludeDic.TryGetValue("All", out var allRes)) return;
            Dictionary<string, string[]> key2markDic = GetJsonData<string[]>(key2markpath);
            var exclude = new List<string>();
            Dictionary<string, string[]> collectionExcludeDic = new Dictionary<string, string[]>();
            foreach (var collection_item in collectionMarkDic)
            {
                Debug.Log("collection_item.Key = " + collection_item.Key);
                var set = new HashSet<string>(allRes);
                if (!key2markDic.TryGetValue(collection_item.Key, out var cruCollectionRes))
                {
                    Debug.Log("key2markDic not key:" + collection_item.Key + "   " + key2markDic.ContainsKey(collection_item.Key));
                    continue;
                }
                else
                {
                    Debug.Log("cruCollectionRes.Length = " + cruCollectionRes.Length);
                }
                for (int i = 0; i < cruCollectionRes.Length; i++)
                {
                    //"".Print(i + " cruCollectionRes " + cruCollectionRes[i]);
                    cruCollectionRes[i] = prefix + cruCollectionRes[i].ToLower();
                    //"".Print(i + " -------cruCollectionRes " + cruCollectionRes[i]);
                }
                List<string> collectionGameInPackageStandAloneList = new List<string>();
                if (collectionGameInPackageStandAloneDic.ContainsKey(collection_item.Key))
                {
                    for (int i = 0; i < collectionGameInPackageStandAloneDic[collection_item.Key].Count; i++)
                    {
                        collectionGameInPackageStandAloneList.Add(collection_item.Key);
                    }
                }
                else
                {
                    Debug.LogError("collectionGameInPackageStandAloneDic not contains key: " + collection_item.Key);
                }
                Debug.Log(collection_item.Key + " --InPackageStandAloneList.Count = " + collectionGameInPackageStandAloneList.Count);
                for (int i = 0; i < collectionGameInPackageStandAloneList.Count; i++)
                {
                    //"".Print(i + " collectionGameInPackageStandAloneList " + collectionGameInPackageStandAloneList[i]);
                    collectionGameInPackageStandAloneList[i] = Path.Combine("assets", "AssetBundles", CasualGameBuildNew.GetManifestName(), collectionGameInPackageStandAloneList[i]).Replace("\\", "/");
                    //"".Print(i + " collectionGameInPackageStandAloneList " + collectionGameInPackageStandAloneList[i]);
                }
                set.ExceptWith(cruCollectionRes);
                //"".Print("ExceptWith  cruCollectionRes", ToolUti.ToJson(collectionExcludeDic));
                set.ExceptWith(collectionGameInPackageStandAloneList);
                exclude.Clear();
                foreach (var m in set)
                {
                    exclude.Add(m);
                }
                collectionExcludeDic[collection_item.Key] = exclude.ToArray();
            }

            File.WriteAllText(collectionexcludePath, ToolUti.ToJson(collectionExcludeDic));
            "".Print("collectionExcludeDic", ToolUti.ToJson(collectionExcludeDic));
        }

        private Dictionary<string, T> GetJsonData<T>(string jsonFilePath)
        {
            Dictionary<string, T> jsonDataDic = new Dictionary<string, T>();
            if (File.Exists(jsonFilePath))
            {
                try
                {
                    var temp_json = File.ReadAllText(jsonFilePath);
                    jsonDataDic = ToolUti.ToObj<Dictionary<string, T>>(temp_json);
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }
            return jsonDataDic;
        }

    }
}
