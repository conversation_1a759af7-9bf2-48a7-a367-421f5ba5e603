using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using UnityEditor.Sprites;
using War.Base;
using System.Linq;
using FtpTest1;
using ICSharpCode.SharpZipLib.Zip;
using CLeopardZip;
using System.Text;
using System.Text.RegularExpressions;

using War.Common;
using XLua.LuaDLL;
using Sirenix.OdinInspector.Demos;
using Random = System.Random;
using System.Net;
using System.Runtime.Serialization.Formatters.Binary;


namespace War.Base
{
    public class BuildScriptMenuItem

    { 
        
        [MenuItem("AssetBundles/StartBuildLuaPatch")]
        public static void StartBuildLuaPatchInEditor()
        {
            //JenkinsEnv.Instance.Set("version_of_check_lua_patch", "136");
            BuildLuaScriptPatch.BuildLuaPatch(true);
        }
        [MenuItem("AssetBundles/StartBuildLuaMisc")]
        public static void StartBuildLuaMiscInEditor()
        {
            JenkinsEnv.Instance.Set(BuildLuaScriptPatch.patch_misc_ver_key, "0");
            BuildLuaScriptPatch.BuildLuaPatch(true);
        }

          [MenuItem("AssetBundles/BuildLuascriptV")]
        public static void BuildLuascriptV()
        {
            BuildLuaScriptPatch.BuildLuascriptVice(null);
        }

        [MenuItem("AssetBundles/PrintLuascriptV")]
        public static void PrintLuascriptV()
        {
            string[] luaFiles =
                AssetDatabase.GetAssetPathsFromAssetBundle(War.Script.LuaManager.LuaScriptAssetBundleName);

            Debug.Log(UIHelper.ToJson(luaFiles));
        }

        [MenuItem("AssetBundles/CommentTMP_PostProcessor")]
        public static void CommentTMP_PostProcessor()
        {
            var files = Directory.GetFiles("Library/PackageCache", "TMPro_TexturePostProcessor.cs",
                SearchOption.AllDirectories);
            "".Print("CommentTMP_PostProcessor", UIHelper.ToJson(files));

            if (files.Length > 0)
            {
                var f = files[0];
                var s = File.ReadAllText(f);
                s = s.Replace(": AssetPostprocessor", "//: AssetPostprocessor");
                "".Print("CommentTMP_PostProcessor", s);

                File.WriteAllText(f, s);
                AssetDatabase.Refresh();
            }
        }
        
        #region 测试url
        // [NUnit.Framework.Test]
        // public static void TestGetStreamingAssetsPathUrl()
        // {
        //     var key = "&url";
        //     var value = War.Base.UtilityTool.GetStreamingAssetsPathUrl(key);
        //     if (value == null)
        //     {
        //         Debug.LogError("not key");
        //         return;
        //     }
        //     Debug.Log("value : " + value);
        // }
		#endregion
        [NUnit.Framework.Test]
        public static void TestGetNewestPathFile()
        {
            var patchmark = "rogue_pc_zhero_branch";
            BuildScript.GetNewestPatchFile(patchmark);
        }

        [MenuItem("AssetBundles/ExtractAssetBundleFromFtp &2")]
        public static void ExtractAssetBundleFromFtp()
        {
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp start");
            /*#region svn获取
            System.Diagnostics.Process p = new System.Diagnostics.Process();
            //设置要启动的应用程序
            p.StartInfo.FileName = "cmd.exe";
            //p.StartInfo.Arguments = "svn info";
            //是否使用操作系统shell启动
            p.StartInfo.UseShellExecute = false;
            // 接受来自调用程序的输入信息
            p.StartInfo.RedirectStandardInput = true;
            //输出信息
            p.StartInfo.RedirectStandardOutput = true;
            // 输出错误
            p.StartInfo.RedirectStandardError = true;
            //不显示程序窗口
            p.StartInfo.CreateNoWindow = true;
            //启动程序
            p.Start();

            //向cmd窗口发送输入信息
            p.StandardInput.Flush();
            p.StandardInput.WriteLine("chcp 936");
            p.StandardInput.WriteLine("svn info | findstr \"^Relative\"&exit");
            p.StandardInput.AutoFlush = true;

            //等待程序执行完退出进程
            p.WaitForExit();
            p.Close();
            string svn_url = Regex.Match(p.StandardOutput.ReadToEnd(), @"Relative[^/\n]* ?/(\w+)/").Groups[1].Value;
#endregion*/

            string svn_url = "RTS20180306";
            if (File.Exists("svn_url"))
            {
                svn_url = File.ReadAllText("svn_url");
            }

            Debug.Log(svn_url);
            var asspath = ConstPaths.AssetBundlesOutputPath;
            asspath = Path.GetFullPath(asspath);
            //			var plat = GetPlatformFolderForAssetBundles (BuildTarget.iOS);
            var plat = ComFuncs.GetPlatformFolderForAssetBundles(EditorUserBuildSettings.activeBuildTarget);
            var platFolder = asspath + "/" + plat;
            var platZip = string.Format("{0}/{1}.zip", asspath, plat);

            if (File.Exists(platZip))
            {
                File.Delete(platZip);
            }

            EditorHelp.CheckDir(platZip);
            //ftp://*************:2121/Assetbundle/Android/
            var fw = new FtpWeb("***********:2121", string.Format("Assetbundle/{1}/{0}", plat, svn_url), "user",
                "user");
            fw.Download(asspath, plat + ".zip");
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp Download end");

            if (Directory.Exists(platFolder))
            {
                Directory.Delete(platFolder, true);
            }

            ZipHelper.UnZipFile(platZip, asspath);
            LogHelp.Instance.Log("ExtractAssetBundleFromFtp UnZipFile end");
            //			System.Diagnostics.Process.Start ("explorer.exe", asspath);
        }

        [MenuItem("AssetBundles/G_Patch &3")]
        public static void G_Patch()
        {
            var isOsx = Application.platform == RuntimePlatform.OSXEditor;

            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var platform = ComFuncs.GetPlatformFolderForAssetBundles(buildTarget);

            var cmd = @"
cd ..\..\Tools\Patch\
python -OO Patch.py {0}
{1} -r {0}/* war_update/{0}
python -m SimpleHTTPServer 80
{1}";
            var patch_path = "../../Patch/Public";
            var replace_str = string.Format("cd {0} {1}", patch_path, isOsx ? "cp" : "copy");
            cmd = string.Format(cmd, platform, replace_str, isOsx ? "" : "pause");

            if (isOsx)
            {
                File.WriteAllText(patch_path + "/" + "start_server.sh", cmd);
            }
            else
            {
                var patchPublicstartServerbat = "../../Patch/Public/startServer.bat";
                patchPublicstartServerbat = Path.GetFullPath(patchPublicstartServerbat);
                System.Diagnostics.Process.Start(patchPublicstartServerbat, platform);
            }
        }
        
    }
}
