using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class UWAGameBuild //: Singleton<UWAGameBuild>
{

    public static UWAGameBuild instance;
    public static UWAGameBuild Instance
    {
        get{
            if (instance == null)
            {
                instance = new UWAGameBuild();
            }
            return instance;
        }
    }
    
    private static readonly string[] DEFINES = new string[] { "DISABLE_UWA_SDK" };
    
    // [MenuItem("Zone111/Test")]
    static void Test()
    {
        UWAGameBuild.Instance.SetUWADefineSymbols();
    } 
    
    private static void EnsureScriptingDefineSymbol(bool isOff)
    {
        var currentTarget = EditorUserBuildSettings.selectedBuildTargetGroup;

        if (currentTarget == BuildTargetGroup.Unknown)
        {
            return;
        }
       
        var definesString = PlayerSettings.GetScriptingDefineSymbolsForGroup(currentTarget).Trim();
        
        var defines = definesString.Split(';');

        bool changed = false;
        Debug.Log("UWAGameBuild:EnsureScriptingDefineSymbol:" + isOff);
        if (isOff)
        {
            foreach (var define in DEFINES)
            {
                if (!defines.Contains(define))
                {
                    if (!definesString.EndsWith(";", StringComparison.InvariantCulture))
                    {
                        definesString += ";";
                    }

                    definesString += define;
                    changed = true;
                }
            }
        }
        else
        {
            List<string> removeList = new List<string>();
            definesString = "";
            foreach (var define in DEFINES)
            {
                if (defines.Contains(define))
                {
                    removeList.Add(define);
                    changed = true;
                }
            }

            if (changed)
            {
                for (int i = 0; i < defines.Length; i++)
                {
                    if (removeList.Contains(defines[i]))
                    {
                        continue;
                    }

                    if (!string.IsNullOrEmpty(definesString)
                        && !definesString.EndsWith(";", StringComparison.InvariantCulture))
                    {
                        definesString += ";";
                    }

                    definesString += defines[i];
                }
            }
        }
        if (changed)
        {
            PlayerSettings.SetScriptingDefineSymbolsForGroup(currentTarget, definesString);
        }
    }

    public bool IsUWABuild()
    {
        return JenkinsEnv.Instance.GetBool("UWA_Build", false);
    }

    public void SetUWADefineSymbols()
    {
        bool isDisableUwa = !IsUWABuild();
        EnsureScriptingDefineSymbol(isDisableUwa);
    }
    
}
