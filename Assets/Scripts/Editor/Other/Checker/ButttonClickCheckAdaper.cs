using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace Checker
{
    class ButttonClickCheckAdaper : CheckerBase
    {
        // 匹配单行注释
        const string singleLineComments = @"--[^\r\n]*";

        // 匹配多行注释
        const string multiLineComments = @"--\[\[(.|\n)*?\]\]";

        private int UnNormalCount;

        private bool WhiteToogle;

        private bool CoroutineStepsTurnOn;

        static StringBuilder report = new StringBuilder();
        //重复注册事件
        static StringBuilder report_mutiple_RegisterEvent = new StringBuilder();

        private int UnNormalCount_mutiple_RegisterEvent;
        protected Vector2 scrollPos_mutiple_RegisterEvent = Vector2.zero;

        string dataPath;
        private Dictionary<string, string[]> whiteList = new Dictionary<string, string[]>
        {
        };

        public ButttonClickCheckAdaper(string titleStr) : base(titleStr)
        {
            dataPath = Application.dataPath;
            ModeName = CheckerEnum.BUTTONCLICK_AddIntervalListener_AND_REGISTE_EVENT_MULTIPLE;
            document = $"\n1.用来查找Lua脚本里,使用button.onClick事件.AddIntervalListener注册函数,且未添加过对应反注册.RemoveIntervalListener事件的异常调用" +
                       "\n2.self:RegisterEvent重复注册事件";
        }

        public void ShowWhiteListGUI()
        {
            GUILayout.Space(10);
            if (whiteList.Keys.Count == 0)
                return;

            WhiteToogle = EditorGUILayout.Toggle("使用白名单", WhiteToogle);
            if (WhiteToogle)
            {
                EditorGUILayout.LabelField($"白名单");
                foreach (var _name in whiteList)
                {
                    EditorGUILayout.LabelField($"\t {_name.Key}.txt => { string.Join("&&", _name.Value)}");
                }
            }
        }

        public override void Reset()
        {
            base.Reset();

            report.Clear();
            report_mutiple_RegisterEvent.Clear();
            UnNormalCount = 0;
            UnNormalCount_mutiple_RegisterEvent = 0;
        }

        public override void ShowSeachType(Action action)
        {
            base.ShowSeachType(action);

            GUILayout.Space(10);

            if (!analyzing && GUILayout.Button("分析Lua文件夹"))
            {
                AnalyzeStart(action);
            }
            GUILayout.Space(10);
            ShowResult();
        }

        public override void ShowResult()
        {
            if (analyzing)
            {
                EditorGUILayout.LabelField("正在分析文件夹，请稍候...");
                GUILayout.Space(10);
                UpdateProgress();
                EditorGUILayout.LabelField($"当前搜索结果：总计分析{maxIndex}个lua文件，收集 {UnNormalCount} 个异常event");
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(report.ToString());
                EditorGUILayout.EndScrollView();
            }

            if (analyzed)
            {
                EditorGUILayout.LabelField($"分析完成,搜索结果：总计分析{maxIndex}个lua文件");
                //EditorGUILayout.LabelField("注：结果列表的文件中，可能有多个对象未Dispose", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField($"1.收集 {UnNormalCount} 个异常, button.onClick未反注册事件");
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(report.ToString());
                EditorGUILayout.EndScrollView();
                GUILayout.Space(10);

                if (GUILayout.Button("复制未反注册事件的Excel格式到剪贴板"))
                {
                    EditorGUIUtility.systemCopyBuffer = report.ToString();
                    Debug.Log("已复制到剪贴板！");
                }

                GUILayout.Space(10);
                EditorGUILayout.LabelField($"2.收集 {UnNormalCount_mutiple_RegisterEvent} 个异常, self:RegisterEvent");
                scrollPos_mutiple_RegisterEvent = EditorGUILayout.BeginScrollView(scrollPos_mutiple_RegisterEvent);
                EditorGUILayout.TextArea(report_mutiple_RegisterEvent.ToString());
                EditorGUILayout.EndScrollView();

                if (GUILayout.Button("复制重复注册事件的Excel格式到剪贴板"))
                {
                    EditorGUIUtility.systemCopyBuffer = report_mutiple_RegisterEvent.ToString();
                    Debug.Log("已复制到剪贴板！");
                }
            }

            if (analyzing || analyzed)
            {
                GUILayout.Space(10);
                if (GUILayout.Button("重置"))
                {
                    Reset();
                }
            }
        }

        public override void AnalyzeStart(Action callback)
        {
            Reset();

            CoroutineStepsTurnOn = true;

            analyzing = true;

            curFileList = new List<LuaFileInfo>();

            CheckLuaEventByPath(folderPath, callback);

        }

        public void CheckLuaEventByPath(string path,Action callback = null)
        {
            var findPath = Path.GetFullPath(path);
            if (!Directory.Exists(findPath)) 
            {
                Debug.LogError($"not find lua path : {findPath}");
                EditorUtility.ClearProgressBar();
                return;
            }

            string[] _luas = Directory.GetFiles(path, "*.txt", SearchOption.AllDirectories);
            if (_luas.Length > 0) 
            {
                maxIndex = _luas.Length;
            }
            CheckLuaEventAllFile(_luas, callback);
            AnalyzeCompleted();
        }

        public void CheckLuaEventAllFile(string[] luaFiles, Action callback)
        {
            try
            {
                AssetDatabase.StartAssetEditing();
                var filesInfo = new Dictionary<string, string>();
                foreach (var _lua in luaFiles)
                {
                    var _luaPath = _lua.Replace("\\", "/");
                    _luaPath = _luaPath.Replace(dataPath, "Assets");
                    TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(_luaPath);

                    filesInfo[_luaPath] = textAsset.text;
                }
                if (filesInfo.Count > 0)
                {
                    ParallelExec.ResourcesParallel(filesInfo, DoExeLuaFile, ParallelExec.UpdateDownload, "查询Lua事件");
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            callback?.Invoke();
        }

        public void DoExeLuaFile(string _luaPath, string content)
        {
            curIndex = curIndex < maxIndex ? ++curIndex : curIndex;
            //_luaPath = _luaPath.Replace("\\", "/");
            //_luaPath = _luaPath.Replace(dataPath, "Assets");

            try
            {
                // 使用 AssetDatabase 加载 TextAsset
                //TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>(_luaPath);
                //if (textAsset == null)
                //{
                //    throw new FileNotFoundException($"文件 {_luaPath} 未找到。");
                //}
                string _fileName = Path.GetFileName(_luaPath);
                // 将 TextAsset 内容拆分为行
                string[] lines = content.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                // 去除注释
                string contentWithoutComments = Regex.Replace(content, singleLineComments, "");
                contentWithoutComments = Regex.Replace(contentWithoutComments, multiLineComments, "");

                // 用于记录 onClick:AddIntervalListener 的调用位置
                Dictionary<string, List<(int LineNumber, string LineContent)>> listeners = new Dictionary<string, List<(int LineNumber, string LineContent)>>();
                // 用于记录 onClick:RemoveIntervalListener 的调用位置
                Dictionary<string, List<(int LineNumber, string LineContent)>> removers = new Dictionary<string, List<(int LineNumber, string LineContent)>>();

                // 正则表达式模式
                Regex addPattern = new Regex(@"(\w+)\.onClick:AddIntervalListener\(");
                Regex removePattern = new Regex(@"(\w+)\.onClick:RemoveIntervalListener\(");

                // 遍历每一行
                for (int i = 0; i < lines.Length; i++)
                {
                    string line = lines[i].Trim();
                    if (line.StartsWith("--"))
                    {
                        continue;
                    }
                    // 查找 onClick:AddIntervalListener
                    Match addMatch = addPattern.Match(line);
                    if (addMatch.Success)
                    {
                        string key = addMatch.Groups[1].Value;
                        if (!listeners.ContainsKey(key))
                        {
                            listeners[key] = new List<(int, string)>();
                        }
                        // 记录行号
                        listeners[key].Add((i + 1, line));
                    }

                    // 查找 onClick:RemoveIntervalListener
                    Match removeMatch = removePattern.Match(line);
                    if (removeMatch.Success)
                    {
                        string key = removeMatch.Groups[1].Value;
                        if (!removers.ContainsKey(key))
                        {
                            removers[key] = new List<(int, string)>();
                        }
                        // 记录行号
                        removers[key].Add((i + 1, line));
                    }
                }
                foreach (var entry in removers)
                {
                    string key = entry.Key;
                    var addSet = entry.Value;
                    if (listeners.ContainsKey(key)) 
                    {
                        listeners.Remove(key);
                    }
                }

                // 报告泄漏
                if (listeners.Count > 0)
                {
                    foreach (var entry in listeners)
                    {
                        foreach (var item in entry.Value)
                        {
                            UnNormalCount++;
                            string rp = $"{_fileName} => \t {item.LineContent}\t \n\r";
                            report.Append(rp);
                        }
                    }
                }

                #region 处理
                string pattern = @"self:RegisterEvent\(([^,]+),\s*([^)]+)\)";

                Regex regex = new Regex(pattern);
                MatchCollection matches = regex.Matches(contentWithoutComments);

                Dictionary<string, string> uniqueEvents = new Dictionary<string, string>();

                Dictionary<string, string> uniqueEvents1 = new Dictionary<string, string>();

                foreach (Match match in matches)
                {
                    if (match.Groups.Count > 2)
                    {
                        string eventKey = match.Groups[1].Value.Trim();
                        string eventValue = match.Groups[0].Value;

                        if (!uniqueEvents1.ContainsKey(eventKey))
                        {
                            uniqueEvents1[eventKey] = eventValue;
                        }
                        else
                        {
                            uniqueEvents[eventKey] = eventValue;
                        }
                    }
                }
                if (uniqueEvents.Count > 0)
                {
                    foreach (var kvp in uniqueEvents)
                    {
                        UnNormalCount_mutiple_RegisterEvent++;
                        string rp = $"{_fileName} => \t {kvp.Key} \t \n\r";
                        report_mutiple_RegisterEvent.Append(rp);
                    }
                }
                #endregion
            }
            catch (Exception ex)
            {
                Debug.LogError($"发生错误: {ex.Message}");
            }
        }

        public void PublicCheck_MenuItem(string path, string outpath, string multpleEventOutPath)
        {
            CoroutineStepsTurnOn = false;

            WhiteToogle = true;

            CheckLuaEventByPath(path);

            OutputReport(report, outpath);
            OutputReport(report_mutiple_RegisterEvent, multpleEventOutPath);
            
        }
        public void OutputReport(StringBuilder stringBuilder, string outpath)
        {
            if (stringBuilder.Length == 0)
                return;
            outpath = outpath.Replace(@"\", "/");
            File.WriteAllText(outpath, stringBuilder.ToString(), Encoding.UTF8);
        }
    }
}