
using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Text;
using System.Text.RegularExpressions;
using System.Collections.Generic;
using War.Base;

public class LuaGlobalDefineCheck
{
    static List<string> ErrorData = new List<string>();
    static List<string> whiteTab = new List<string>() { "hero_prop_mgr", "ui_bindprompt_impl" };
    public static List<string> ExecuteLuaSrcDietAll(Dictionary<string, byte[]> DictLuaBs)
    {
        ErrorData.Clear();
        // 如果数据为空，先获取所有 lua 文件
        if (DictLuaBs == null)
        {
            var LuaPath = "Assets/Lua";

            string[] luaFiles = Directory.GetFiles(LuaPath + "/", "*.txt", SearchOption.AllDirectories);
            DictLuaBs = new Dictionary<string, byte[]>();
            foreach (var luaFile in luaFiles)
            {
                string luaPath = Path.GetFullPath(luaFile).Replace("\\", "/");
                if (!luaPath.Contains("Tables/")) 
                {
                    var fstr = File.ReadAllText(luaFile, Encoding.UTF8);
                    var bytes = Encoding.UTF8.GetBytes(fstr);
                    DictLuaBs.Add(luaFile, bytes);
                }
            }
        }
        ParallelExec.ResourcesParallel(DictLuaBs, ExecuteLuaSrcDiet, ParallelExec.UpdateDownload, "Lua 全局变量检测");
        return ErrorData;
    }


    public static void ExecuteLuaSrcDiet(string luaPath, byte[] data)
    {
        if(IsInWhiteTable(luaPath))return;
        luaPath = Path.GetFullPath(luaPath).Replace("\\", "/");
        var luaCheckExePath = $"./Assets/../../../Tools/CheckLuaGlobalDefine/bin/Debug/net6.0/CheckLuaGlobalDefine.exe";
        luaCheckExePath = Path.GetFullPath(luaCheckExePath).Replace("\\", "/");
        if (!File.Exists(luaCheckExePath)) 
        {
            Debug.LogError($"{luaCheckExePath} not found");
            return;
        }
        var arg = $"{luaPath}";
        using (System.Diagnostics.Process process = new System.Diagnostics.Process())
        {
            process.StartInfo.FileName = luaCheckExePath;
            process.StartInfo.Arguments = arg;
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.RedirectStandardInput = true;
            process.StartInfo.RedirectStandardOutput = true;
            process.StartInfo.RedirectStandardError = true;
            process.StartInfo.CreateNoWindow = true;
            process.Start();

            // 读取标准输出
            string output = process.StandardOutput.ReadToEnd();
            string errorOutput = process.StandardError.ReadToEnd();
            process.WaitForExit();
            if (!string.IsNullOrEmpty(output))
            {
                ErrorData.Add(output);
                Debug.LogError($"output {output}");
            }
            // 获取进程的返回值
            int exitCode = process.ExitCode;
            if (exitCode != 0)
            {
                Debug.LogError($"Process {luaPath} failed with exit code: {exitCode}, message:{errorOutput}");
            }
        }
    }

    public static bool IsInWhiteTable(string luaPath) 
    {
        bool isexit = false;
        for (int i = 0; i < whiteTab.Count; i++)
        {
            if (luaPath.Contains(whiteTab[i])) 
            {
                isexit = true;
                break;
            }
        }
        return isexit;
    }
}
