using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

namespace Checker
{
    class EventCheckAdaper : CheckerBase
    {
        // event类型
        private string eventAddName = "event.Register"; // 输入的 Class 名称
        private string eventRemoveName = "event.Unregister";

        // 定义匹配event函数起点的正则表达式
        const string eventPattern = @"event\.Register\(event\..*,";

        // 匹配单行注释
        const string singleLineComments = @"--[^\r\n]*";

        // 匹配多行注释
        const string multiLineComments = @"--\[\[(.|\n)*?\]\]";

        static StringBuilder report = new StringBuilder();

        private int UnNormalCount;

        private bool WhiteToogle;

        private bool CoroutineStepsTurnOn;


        private Dictionary<string,string[]> whiteList = new Dictionary<string, string[]>
        {
            {"ui_window_mgr" ,           new string[] { "UI_MODULE_INIT_BASE"}},
            {"screen_util" ,             new string[] { "CSUpdate"}},
            {"entity_monitor" ,          new string[] { "SCENE_DESTROY_NEW"}},
            {"luaLangMgr" ,              new string[] { "LANGUAGE_SETTING_CHANGED"}},
            {"appsflyer" ,               new string[] { "FIREBASE_TOKEN_RECEIVED"}},
            {"ui_reload" ,               new string[] { "NET_RECORD_STOP"}},
            {"ui_hook_level_tournament" ,new string[] { "SCENE_DESTROY"}},
            {"ui_loading_impl" ,         new string[] { "STATE_ENTER"}},
            {"ui_lobby" ,                new string[] { "RETURN_PLAYERDATA_NTF"}},
            {"ui_menu_bot" ,             new string[] { "UPDATE_SESSION_PROP","UPDATE_FRIEND_HELP_BUBBLE"}},
            {"starwar_net_simulate" ,    new string[] { "CSUpdate"}},
            {"scroll_rect_anim" ,        new string[] { "UI_MODULE_PREPARE_SHOW","UI_MODULE_PREPARE_HIDE","UI_MODULE_PREPARE_CLOSE"}},
            {"force_guide_system" ,      new string[] { "CHECK_FORCE_GUIDE_SYSTEM"}},
            {"ui_hero_base" ,            new string[] { "SKILL_UPDATE"}},
            {"ui_illusion_tower" ,       new string[] { "FACTION_LEVELUP"}},
            {"star_war_scene" ,          new string[] { "CLOSE_ALL_SCENE","SCENE_DESTROY"}},
            {"ui_timearea_main" ,        new string[] { "ON_MSG_MATE_UPDATE_NTF"}},
            {"peak_scene_mgr" ,          new string[] { "SCENE_DESTROY","CLOSE_ALL_SCENE"}},
        };



        public EventCheckAdaper(string titleStr) : base(titleStr)
        {
            ModeName = CheckerEnum.EVENT;
            document = "用来查找Lua脚本里,使用event事件.Register注册函数,且未添加过对应反注册.Unregister事件的异常调用," +
                       "白名单内的事件名不检测";
        }

        public override void AnalyzeStart(Action callback)
        {
            Reset();

            CoroutineStepsTurnOn = true;

            analyzing = true;

            curFileList = new List<LuaFileInfo>();

            //_hs_events = new HashSet<string>();

            //report.Clear();
            if (CoroutineStepsTurnOn)
            {
                coroutineSteps.Enqueue(() => CheckLuaEventByPath(folderPath, callback));
            }
            else
            {
                CheckLuaEventByPath(folderPath, callback);
            }
        }

        public override void AnalyzeCompleted(Action callback = null)
        {
            analyzing = false;
            analyzed = true;
            callback?.Invoke();
            EditorUtility.ClearProgressBar();
        }

        public override void ShowSeachType(Action action)
        {
            base.ShowSeachType(action);

            ShowWhiteListGUI();

            GUILayout.Space(10);

            if (!analyzing && GUILayout.Button("分析Lua文件夹"))
            {
                AnalyzeStart(action);
            }
            GUILayout.Space(10);
            ShowResult();
        }

        public override void ShowResult()
        {
            if (analyzing)
            {
                EditorGUILayout.LabelField("正在分析文件夹，请稍候...");
                GUILayout.Space(10);
                UpdateProgress();
                EditorGUILayout.LabelField($"当前搜索结果：总计分析{maxIndex}个lua文件，收集 {UnNormalCount} 个异常event");
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(report.ToString());
                EditorGUILayout.EndScrollView();
            }

            if (analyzed)
            {
                EditorGUILayout.LabelField("分析完成！");
                //EditorGUILayout.LabelField("注：结果列表的文件中，可能有多个对象未Dispose", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField($"搜索结果：总计分析{maxIndex}个lua文件，收集 {UnNormalCount} 个异常event");
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(report.ToString());
                EditorGUILayout.EndScrollView();
                GUILayout.Space(10);

                if (GUILayout.Button("复制Excel格式到剪贴板"))
                {
                    EditorGUIUtility.systemCopyBuffer = report.ToString();
                    Debug.Log("已复制到剪贴板！");
                }
            }

            if (analyzing || analyzed)
            {
                GUILayout.Space(10);
                if (GUILayout.Button("重置"))
                {
                    Reset();
                }
            }
        }

        public void ShowWhiteListGUI()
        {
            GUILayout.Space(10);
            if (whiteList.Keys.Count == 0)
                return;

            WhiteToogle = EditorGUILayout.Toggle("使用白名单", WhiteToogle);
            if (WhiteToogle)
            {
                EditorGUILayout.LabelField($"白名单");
                foreach (var _name in whiteList)
                {
                    EditorGUILayout.LabelField($"\t {_name.Key}.txt => { string.Join("&&", _name.Value)}");
                }
            }
        }

        public override void Reset()
        {
            maxIndex = 0f;
            curIndex = 0f;

            report.Clear();
            analyzing = false;
            analyzed = false;
            UnNormalCount = 0;
            coroutineSteps = new Queue<Func<bool>>();
            EditorUtility.ClearProgressBar();
        }

        //[SuperTools.SuperTMenuItem(SuperTools.EMenuType.Main, "Assets/CheckLuaEventIsNormal")]
        
        public void PublicCheck_MenuItem(string path,string outpath)
        {
            CoroutineStepsTurnOn = false;

            WhiteToogle = true;

            CheckLuaEventByPath(path);
            
            OutputReport(outpath);
        }

        public bool CheckLuaEventByPath(string path,Action callback = null)
        {
            if (!string.IsNullOrEmpty(Path.GetExtension(path)))
                return true;

            string[] _luas = Directory.GetFiles(path, "*.txt", SearchOption.AllDirectories);
            maxIndex = _luas.Length;
            CheckLuaEventAllFile(_luas, callback);
            if (CoroutineStepsTurnOn)
            {
                coroutineSteps.Enqueue(() =>
                {
                    AnalyzeCompleted();
                    return true;
                });
            }
            else
            {
                AnalyzeCompleted();
            }
            return true;
        }

        public void CheckLuaEventAllFile(string[] luaFiles,Action callback)
        {
            foreach (var _lua in luaFiles)
            {
                if (CoroutineStepsTurnOn)
                    coroutineSteps.Enqueue(() => CheckLuaOnceFile(_lua, callback));
                else
                    CheckLuaOnceFile(_lua, callback);
            }
        }

        public bool CheckLuaOnceFile(string _luaPath,Action callback)
        {
            curIndex = curIndex < maxIndex ? ++curIndex : curIndex;
            _luaPath = _luaPath.Replace("\\", "/");
            _luaPath = _luaPath.Replace(Application.dataPath,"Assets");
            TextAsset _text = AssetDatabase.LoadAssetAtPath<TextAsset>(_luaPath);
            string _fileName = Path.GetFileName(_luaPath);

            if (_text is TextAsset)
            {
                string textStr = (_text as TextAsset).text;

                var singleR = new Regex(singleLineComments);

                var multiR = new Regex(multiLineComments);

                textStr = singleR.Replace(textStr, "");
                textStr = multiR.Replace(textStr, "");

                List<string> _fs = CheckerUtil.GetLuaFuncStr(textStr, _fileName);

                Match[] event_mc = GetAllEventMatchInFunList(_fs);

                HashSet<string> _hs_events = new HashSet<string>();

                AddMatchCollectionToHash(_fileName,textStr, _hs_events, event_mc);

                ContainsUnRegister(textStr, _hs_events, _fileName, _text);

                callback?.Invoke();
            }
            return true;
        }

        public void OutputReport(string outpath)
        {
            if (report.Length == 0)
                return;
            outpath = outpath.Replace(@"\", "/");
            File.WriteAllText(outpath, report.ToString(),Encoding.UTF8);
        }

        public Match[] GetAllEventMatchInFunList(List<string> _fs)
        {
            Regex _regex = new Regex(eventPattern);
            List<Match> matchCollections = new List<Match>();
            for (int i = 0; i < _fs.Count; i++)
            {
                MatchCollection matchCollection = _regex.Matches(_fs[i]);
                foreach (Match match in matchCollection)
                {
                    //if (!CheckInAnnotation(_fs[i], match.Index))
                    //{
                    matchCollections.Add(match);
                    //}
                }
            }
            return matchCollections.ToArray();
        }

        public void ContainsUnRegister(string content, HashSet<string> register_list, string _fileName = "", Object obj = null)
        {
            foreach (var item in register_list)
            {
                string _unRegister = item.Replace("Register", "Unregister");
                if (!content.Contains(_unRegister))
                {
                    UnNormalCount++;
                    string eventName = _unRegister.Replace("event.Unregister(event.", "");
                    string rp = $"{_fileName} => \t event => {eventName} 不包含Unregister函数 , \t 请检查 \n\r";
                    Debug.LogError(rp, obj);
                    report.Append(rp);
                }
            }
        }

        public void AddMatchCollectionToHash(string fileName,string content, HashSet<string> hs_events, Match[] matchCollection)
        {
            foreach (Match match in matchCollection)
            {
                for (int i = 0; i < match.Groups.Count; i++)
                {
                    if (WhiteToogle)
                    {
                        if (!IsWhite(fileName, match.Groups[i].Value))
                        {
                            hs_events.Add(match.Groups[i].Value);
                        }
                    }
                    else
                    {
                        hs_events.Add(match.Groups[i].Value);
                    }
                }
            }
        }
       
        private bool IsWhite(string name,string context)
        {
            bool had = false;
            foreach (var _Name in whiteList)
            {
                if (name.Contains( _Name.Key))
                {
                    for (int i = 0; i < _Name.Value.Length; i++)
                    {
                        if (context.Contains(_Name.Value[i]))
                        {
                            had = true;
                        }
                    }
                }
            }
            return had;
        }

    }
}
