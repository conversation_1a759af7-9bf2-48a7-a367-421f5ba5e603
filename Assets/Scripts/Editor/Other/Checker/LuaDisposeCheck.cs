using UnityEngine;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using System;
using System.Text;
using System.Linq;
#if UNITY_EDITOR
using UnityEditor;
#endif
namespace Checker
{
    public class LuaDisposeCheck : EditorWindow
    {
        List<CheckerBase> checkers;
        CheckerBase CurChecker;

        CheckerEnum seachType;

        [MenuItem("Tools/脚本检测器")]
        public static void ShowWindow()
        {
            LuaDisposeCheck window = GetWindow<LuaDisposeCheck>("脚本检测器");
            window.minSize = new Vector2(500, 600);
            //folderPath = Path.GetFullPath(Path.Combine(Application.dataPath, "Lua"));
        }
        private void OnEnable()
        {
            Init();
        }

        private void OnDisable()
        {
            // 当窗口关闭时取消订阅
            if (CurChecker!=null)
            {
                EditorApplication.update -= CurChecker.CoroutineUpdate;
            }
        }

        private void Init()
        {
            checkers = new List<CheckerBase>();
            checkers.Add(new ModuleCheckAdaper("文件名,未Dispose对象"));
            checkers.Add(new ClassCheckAdaper("文件名,调用的类,未Dispose对象"));
            checkers.Add(new EventCheckAdaper("文件名,event未反注册对象"));
            checkers.Add(new ButttonClickCheckAdaper("文件名,button.AddIntervalListener未反注册对象或单类self:RegisterEvent重复注册事件"));
            seachType = CheckerEnum.MODULE;
            SelectCheckMode(seachType);
        }


        private void OnGUI()
        {
            // 使用EnumPopup创建下拉菜单
            seachType = (CheckerEnum)EditorGUILayout.EnumPopup("选择查找的类型:", seachType);
            if (seachType!=CurChecker.ModeName)
            {
                SelectCheckMode(seachType);
            }
            if (CurChecker!=null)
            {
                CurChecker.ShowSeachType(() => { Repaint(); });
            }
        }

        private void SelectCheckMode(CheckerEnum checkerEnum)
        {

            for (int i = 0; i < checkers.Count; i++)
            {
                if (checkerEnum == checkers[i].ModeName)
                {
                    try
                    {
                        EditorApplication.update -= CurChecker.CoroutineUpdate;//兼容首次
                    }
                    catch (Exception)
                    {
                    }
                    CurChecker = checkers[i];
                    EditorApplication.update += CurChecker.CoroutineUpdate;
                }
            }

        }

    }
}