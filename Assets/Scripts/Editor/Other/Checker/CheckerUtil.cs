using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEngine;

namespace Checker
{
    static class CheckerUtil
    {
        // 定义匹配Lua函数起点的正则表达式
        const string functionPattern = @"function\s+(?:\w+\.)?\w*(?::|\.)?\w*\s*\([^)]*\)\s*";
        /// <summary>
        /// 获取对象是否调用Dispose
        /// </summary>
        /// <param name="fileContent"></param>
        /// <param name="assetName"></param>
        /// <returns></returns>
        public static bool HasDisposeMethodCall(string fileContent, string objectName)
        {
            // 转义对象名称，避免正则表达式中的特殊字符问题
            string escapedObjectName = Regex.Escape(objectName);
            // Debug.LogWarning("---HasDisposeMethodCallDelay:" + escapedObjectName);
            if (HasDisposeMethodCallDelay(fileContent))
            {
                return false;
            }
            // Debug.LogWarning("---0escapedObjectName:" + escapedObjectName);
            // 模式 1: 直接调用 Dispose 方法
            string directCallPattern = $@"\b{escapedObjectName}\:Dispose\(\)";
            if (Regex.IsMatch(fileContent, directCallPattern))
            {
                return true;
            }
            // Debug.LogWarning("---1escapedObjectName:" + escapedObjectName);
            // 模式 2: 通过索引或属性访问的调用 Dispose 方法
            if (HasAssignDisposeMethodCall(fileContent, escapedObjectName))
            {
                return true;
            }
            // Debug.LogWarning("---2escapedObjectName:" + escapedObjectName);
            // 模式 3: 检测对象被加入到列表中并调用了 Dispose 方法
            if (HasAddedToListAndDisposed(fileContent, escapedObjectName))
            {
                return true;
            }
            // Debug.LogWarning("---3escapedObjectName:" + escapedObjectName);
            // 模式 4: 检查 scroll_rect_item.data 对象
            if (HasDisposeMethodCallForScrollRectItemData(fileContent, escapedObjectName))
            {
                return true;
            }
            // Debug.LogWarning("---4escapedObjectName:" + escapedObjectName);
            // 模式 5: CreateFaceItem 和 GetRewardItem 等特殊写法的盘查
            if (HasDisposeMethodCallCItem(fileContent))
            {
                return true;
            }
            // Debug.LogWarning("---5escapedObjectName:" + escapedObjectName);
            // 如果所有模式都没匹配到，则返回 false 表示没有找到 Dispose 方法的调用
            return false;
        }

        /// <summary>
        /// 获取被赋值了图集的对象名
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="localObjectName"></param>
        /// <returns></returns>
        public static string GetDeclaredObjectForCreateMethod(string filePath, string localObjectName,string funcName)
        {
            string methodPattern = $@"\b{localObjectName}\.{funcName}\w*\(";
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                string pattern = $@"(?<objName>\w+)\s*=\s*{methodPattern}";
                Match match = new Regex(pattern).Match(line);
                if (match.Success)
                {
                    return match.Groups["objName"].Value;
                }
            }
            return null;
        }

        private static bool HasAssignDisposeMethodCall(string fileContent, string escapedObjectName)
        {
            bool hasDisposeCall = false;
            // 模式 2: 检查是否存在赋值给其他变量且在 Close 方法内调用 Dispose 的情况
            string assignPatterns = $@"(?:(?:local\s+)?(\w+)|(\w+\.[\w\[\]\.]+)|(\w+\[[""']?\w+[""']?\]))\s*=\s*{escapedObjectName}\b";
            Regex assignRegex = new Regex(assignPatterns);
            MatchCollection assignMatches = assignRegex.Matches(fileContent);

            foreach (Match assignMatch in assignMatches)
            {
                string assignedVarName;
                if (assignMatch.Groups[1].Success)
                {
                    // local otherVar = objectName
                    assignedVarName = assignMatch.Groups[1].Value;
                }
                else if (assignMatch.Groups[2].Success)
                {
                    // self.otherVar = objectName
                    assignedVarName = assignMatch.Groups[2].Value;
                }
                else if (assignMatch.Groups[3].Success)
                {
                    // scroll_rect_item.data["items"] = objectName OR scroll_rect_item.data.items[i] = objectName
                    assignedVarName = assignMatch.Groups[3].Value;
                }
                else
                {
                    continue;
                }
                // Debug.LogWarning("---101escapedObjectName:" + assignedVarName);
                string directCallPattern = $@"\b{assignedVarName}\:Dispose\(\)";
                hasDisposeCall = Regex.IsMatch(fileContent, directCallPattern);
                if (hasDisposeCall) break;
            }

            return hasDisposeCall;
        }

        /// <summary>
        /// 检测对象被加入到列表中并调用了 Dispose 方法
        /// </summary>
        /// <param name="fileContent"></param>
        /// <param name="objectName"></param>
        /// <returns></returns>
        private static bool HasAddedToListAndDisposed(string fileContent, string objectName)
        {
            // 模式 1: 检测对象被加入到列表中
            // 正则表达式匹配 "table.insert" 语句，并捕获列表名称
            string addedToListPattern = $@"table\.insert\s*\(\s*([_\w.]+)\s*,\s*{Regex.Escape(objectName)}\s*\)";
            // 正则表达式匹配 "items[i] = objName" 语句，并捕获列表名称
            string assignToListPattern = $@"([_\w.]+)\s*\[\s*i\s*\]\s*=\s*{Regex.Escape(objectName)}";

            Match match1 = new Regex(addedToListPattern).Match(fileContent);
            Match match2 = new Regex(assignToListPattern).Match(fileContent);
            if (match1.Success || match2.Success)
            {
                // 成功提取列表名称
                string listName = match1.Success ? match1.Groups[1].Value : match2.Groups[1].Value;
                // Debug.LogWarning("---201escapedObjectName:" + listName);
                if (!string.IsNullOrEmpty(listName))
                {
                    // 模式 2: 检测列表中的对象是否遍历并调用了 Dispose 方法
                    string disposeInLoopPattern = $@"for(.*?){Regex.Escape(listName)}(.*?)do[\s\S]*?:Dispose\(\)[\s\S]*?end";
                    // Debug.LogWarning("---202escapedObjectName:" + listName);
                    Match matchDisposeInLoop = Regex.Match(fileContent, disposeInLoopPattern, RegexOptions.Singleline);
                    if (matchDisposeInLoop.Success) return true;
                }
            }

            return false;
        }


        /// <summary>
        /// 根据提供的文件内容和对象名称，检查 scroll_rect_item.data 对象被赋值并且是否后续调用了 Dispose 方法
        /// </summary>
        /// <param name="fileContent">代码文件内容</param>
        /// <param name="objectName">要检查的对象名称</param>
        /// <returns>如果存在 dispose 调用，则返回 true；否则返回 false</returns>
        private static bool HasDisposeMethodCallForScrollRectItemData(string fileContent, string objectName)
        {
            // 转义对象名称，避免正则表达式中的特殊字符问题
            string escapedObjectName = Regex.Escape(objectName);
            // 构建查找 scroll_rect_item.data 被赋值对象的模式
            string scrollRectDataPattern = $@"scroll_rect_item\.data(?:\.\w+|\[\w+\])?\s*=\s*{escapedObjectName}";
            // 使用正则表达式检查 scroll_rect_item.data 是否赋值
            Regex scrollRectDataRegex = new Regex(scrollRectDataPattern);
            if (scrollRectDataRegex.IsMatch(fileContent))
            {
                // 构建查找 dispose 方法调用的模式
                string disposePattern = $@"{escapedObjectName}\s*:Dispose\(\)";
                // Debug.LogWarning("---301escapedObjectName:" + escapedObjectName);
                // 使用正则表达式检查对象的 Dispose 方法是否被调用
                Regex disposeRegex = new Regex(disposePattern);
                return disposeRegex.IsMatch(fileContent);
            }
            // 如果没有找到赋值，则不必查找 Dispose 调用
            return false;
        }
        /// <summary>
        /// 特殊的判断
        /// </summary>
        /// <param name="fileContent"></param>
        /// <returns></returns>
        private static bool HasDisposeMethodCallCItem(string fileContent)
        {
            string itemCallPattern1 = $@"function CreateFaceItem\(";
            string itemCallPattern2 = $@"function CreateHeroItem\(";
            string itemCallPattern3 = $@"function GetRewardItem\(";

            return Regex.IsMatch(fileContent, itemCallPattern1) || Regex.IsMatch(fileContent, itemCallPattern2) || Regex.IsMatch(fileContent, itemCallPattern3);
        }
        private static bool HasDisposeMethodCallDelay(string fileContent)
        {
            string itemCallPattern = @"util\.DelayCall\((?:(?!end).)*:Dispose\(\)(?:(?!end).)*end";
            return Regex.IsMatch(fileContent, itemCallPattern);
        }

        /// <summary>
        /// 获取lua里的所有function
        /// </summary>
        /// <param name="luaContent">检测的文本</param>
        /// <returns></returns>
        public static List<string> GetLuaFuncStr(string luaContent, string _fileName = "")
        {

            // 查找所有函数定义的起点
            Regex regex = new Regex(functionPattern, RegexOptions.Singleline);

            MatchCollection matches = regex.Matches(luaContent);

            List<string> functions = new List<string>();

            foreach (Match match in matches)
            {
                int startIndex = match.Index;

                //if (CheckInAnnotation(luaContent, startIndex))//正则匹配的可能包含已经注释的fun开头代码，这里进行纠错
                //    startIndex += GetAnnotationLength(luaContent, startIndex - 5);

                int endIndex = FindFunctionEndIndex(luaContent, startIndex);

                if (endIndex != -1)
                {
                    string functionBody = luaContent.Substring(startIndex, endIndex - startIndex + 1);

                    functions.Add(functionBody);
                }
            }
            return functions;
        }

        /// <summary>
        /// 判断当前文本是不是在注释内
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="startIndex">开始位置</param>
        /// <returns></returns>
        public static bool CheckInAnnotation(string content, int startIndex)
        {
            bool _isAnnotation = false;
            for (int i = startIndex; i >= 0; i--)
            {
                if (i - 1 >= 0 && content[i] == '\n' && content[i - 1] == '\r')
                {
                    break;
                }
                else if (i - 1 >= 0 && content[i] == '-' && content[i - 1] == '-')
                {
                    _isAnnotation = true;
                    break;
                }
            }
            //return startIndex - checkRange > 0 && content.Substring(startIndex - checkRange, checkRange).Contains("--");
            return _isAnnotation;
        }

        /// <summary>
        /// 传入function起点，返回function终点
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="startIndex">开始位置</param>
        /// <returns></returns>
        static int FindFunctionEndIndex(string content, int startIndex)
        {
            int level = 0;
            bool inString = false; // 是否在字符串中
            bool inComment = false; // 是否在注释中
            int otherActionScope = 0;

            for (int i = startIndex; i < content.Length; i++)
            {
                if (inComment)
                {
                    if (content[i] == '\n')
                    {
                        inComment = false; // 注释结束
                    }
                    continue;
                }

                if (content[i] == '-' && i + 1 < content.Length && content[i + 1] == '-')
                {
                    inComment = true; // 进入注释
                    continue;
                }

                if (content[i] == '"')
                {
                    if (i > 0 && content[i - 1] != '\\')
                    {
                        inString = !inString; // 切换字符串状态
                    }
                    continue;
                }

                if (content[i] == '\n') continue;
                if (content[i] == '\r') continue;

                if (inString) continue; // 忽略字符串内的内容

                if (isLogicalOperator(content, i, "for"))
                {
                    otherActionScope++;
                }
                if (isLogicalOperator(content, i, "if"))
                {
                    otherActionScope++;
                }

                if (isLogicalOperator(content, i, "function"))
                {
                    level++;
                }

                if (isFunEnd(content, startIndex, i, "end"))
                {
                    if (otherActionScope == 0)
                    {
                        level--;
                        if (level == 0)
                        {
                            return i + 2; // 结束' end' 的索引
                        }
                    }
                    else
                    {
                        otherActionScope--;
                    }
                }
            }
            return -1; // 未找到相应的结束
        }

        /// <summary>
        /// 检测当前是否为闭包函数 closureLeft = "function" ，closureRight = "()" 或者 closureLeft = "end" closureRight = ")"
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="curIndex">开始位置</param>
        /// <param name="closureLeft">闭包左边字段</param>
        /// <param name="closureRight">闭包左边字段</param>
        /// <returns></returns>
        public static bool isClosureOperator(string content, int curIndex, string key)
        {
            return content[curIndex] == key[0]
                && curIndex + key.Length < content.Length
                && content.Substring(curIndex, key.Length) == key
                && curIndex - 1 >= 0
                && !Char.IsLetter(content[curIndex - 1]);
        }

        /// <summary>
        /// content里第curIndex的字符是否为 自定义关键字 key
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="key">用来对比的参考字</param>
        /// <param name="curIndex">位置</param>
        /// <returns></returns>
        public static bool isLogicalOperator(string content, int curIndex, string key)
        {
            bool canPass = content[curIndex] == key[0]
                && curIndex + key.Length < content.Length
                && content.Substring(curIndex, key.Length) == key
                && curIndex - 1 >= 0
                && !Char.IsLetter(content[curIndex - 1])
                && !Char.IsLetter(content[curIndex + key.Length]);

            return canPass;
        }

        /// <summary>
        /// content里第curIndex的字符是否为 函数结尾 end
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="startIndex">开始的位置</param>
        /// <param name="curIndex">检测的关键字</param>
        /// <returns></returns>
        public static bool isFunEnd(string content, int startIndex, int curIndex, string passKey)
        {
            if (curIndex == content.Length - passKey.Length)
            {
                bool isEnd = content[curIndex] == passKey[0]
                        && content.Substring(curIndex, passKey.Length) == passKey
                        && content.Substring(startIndex, curIndex - startIndex).Contains("\r\n")
                        && !Char.IsLetter(content[curIndex - 1]);
                return isEnd;
            }
            else
            {
                if (startIndex - 1 < 0 || curIndex + passKey.Length >= content.Length)
                    return false;

                bool isEnd = content[curIndex] == passKey[0]
                        && content.Substring(curIndex, passKey.Length) == passKey
                        && content.Substring(startIndex, curIndex - startIndex).Contains("\r\n")
                        && !Char.IsLetter(content[curIndex - 1])
                        && !Char.IsLetter(content[curIndex + 3]);

                return isEnd;
            }
        }

        /// <summary>
        /// 获取注释文本的长度
        /// </summary>
        /// <param name="content">检测的文本</param>
        /// <param name="startIndex">开始的位置</param>
        /// <returns></returns>
        public static int GetAnnotationLength(string content, int startIndex)
        {
            int length = 0;
            bool inComment = false;
            for (int i = startIndex; i < content.Length; i++)
            {
                if (i + 1 >= content.Length)
                    break;

                if (inComment)
                {
                    if (content[i] == '\n')
                    {
                        inComment = false; // 注释结束
                    }
                    continue;
                }

                if (content[i] == '-' && i + 1 < content.Length && content[i + 1] == '-')
                {
                    inComment = true; // 进入注释
                    length++;
                    continue;
                }

                if (inComment)
                {
                    length++;
                }
            }
            return length;
        }

    }
}
