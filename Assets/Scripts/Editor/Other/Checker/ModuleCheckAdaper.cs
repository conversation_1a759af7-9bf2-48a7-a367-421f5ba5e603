using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace Checker
{
    class ModuleCheckAdaper : CheckerBase 
    {
        // module类型
        private string assetName = "card_sprite_asset"; // 输入的 Asset 名称
        private string funcName = "Create"; // 输入的 函数 关键字

        public ModuleCheckAdaper(string titleStr) : base(titleStr)
        {
            ModeName = CheckerEnum.MODULE;
            document = "用来查看图集的引用和销毁情况，输入图集require名和函数的关键字";
        }

        public override void AnalyzeStart(Action callback)
        {
            Reset();
            analyzing = true;

            curFileList = new List<LuaFileInfo>();

            coroutineSteps.Enqueue(() => ProcessFilesCoroutine(folderPath, callback));
        }


        public override void ShowSeachType(Action callback)
        {
            base.ShowSeachType(callback);
            GUILayout.Space(10);
            EditorGUILayout.LabelField("module名称:（填入require的module名称）");
            assetName = EditorGUILayout.TextField(assetName);
            EditorGUILayout.LabelField("函数关键字名称:（填入函数包含的关键字）");
            funcName = EditorGUILayout.TextField(funcName);
            GUILayout.Space(10);
            if (!analyzing && GUILayout.Button("分析Lua文件夹"))
            {
                AnalyzeStart(callback);
            }
            GUILayout.Space(10);
            ShowResult();
        }

     
        private bool ProcessFilesCoroutine(string folderPath,Action callback)
        {
            DirectoryInfo directoryInfo = new DirectoryInfo(folderPath);
            FileInfo[] luaFiles = directoryInfo.GetFiles("*.txt", SearchOption.AllDirectories);
            maxIndex += luaFiles.Length;
            foreach (var fileInfo in luaFiles)
            {
                coroutineSteps.Enqueue(() =>
                {
                    return ProcessSingleFileCoroutine(fileInfo, callback);
                });
            }

            coroutineSteps.Enqueue(() =>
            {
                AnalyzeCompleted();
                return true;
            });

            return true;
        }
        private bool ProcessSingleFileCoroutine(FileInfo fileInfo,Action callback)
        {
            LuaFileInfo luaFileInfo = new LuaFileInfo();
            luaFileInfo.FilePath = fileInfo.FullName;
            curIndex++;

            CheckFilesForDispose(luaFileInfo, assetName, funcName);

            GenerateCSVData();

            callback?.Invoke();
            return true;
        }
        /// <summary>
        /// 开始检测
        /// </summary>
        private void CheckFilesForDispose(LuaFileInfo fileInfo, string assetName, string funcName)
        {
            string fileContent = "";
            string objModuleName = GetObjModuleName(fileInfo.FilePath, assetName);
            if (!string.IsNullOrEmpty(objModuleName))
            {
                fileContent = File.ReadAllText(fileInfo.FilePath);
                if (IsMethodCalled(fileContent, objModuleName, funcName))
                {
                    string objectName =CheckerUtil.GetDeclaredObjectForCreateMethod(fileInfo.FilePath, objModuleName,funcName);
                    if (!string.IsNullOrEmpty(objectName))
                    {
                        if (!CheckerUtil.HasDisposeMethodCall(fileContent, objectName))
                        {
                            fileInfo.ObjectName = objectName;
                            if (!curFileList.Contains(fileInfo)) curFileList.Add(fileInfo);
                        }
                    }
                }
            }
        }
        private void GenerateCSVData()
        {
            StringBuilder csvBuilder = new StringBuilder();
            csvBuilder.AppendLine(titleStr);
            foreach (LuaFileInfo fileInfo in curFileList)
            {
                csvBuilder.AppendLine($"{Path.GetFileName(fileInfo.FilePath)},{fileInfo.ObjectName}");
            }
            csvData = csvBuilder.ToString();
        }

        /// <summary>
        /// 判断是否有调用包含"Create"字段的函数
        /// </summary>
        /// <param name="fileContent"></param>
        /// <param name="localObjectName"></param>
        /// <returns></returns>
        protected bool IsMethodCalled(string fileContent, string localObjectName, string funcName)
        {
            string pattern = $@"\b{localObjectName}\.{funcName}\w*\(";
            return new Regex(pattern).IsMatch(fileContent);
        }

    }
}
