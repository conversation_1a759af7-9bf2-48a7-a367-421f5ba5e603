using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;

namespace Checker
{
    abstract class CheckerBase : IChecker
    {
        public CheckerEnum ModeName;
        protected bool analyzing;
        protected bool analyzed;

        protected float curIndex = 0f;
        protected float maxIndex = 0f;
        protected List<LuaFileInfo> curFileList = new List<LuaFileInfo>();

        protected Vector2 scrollPos = Vector2.zero;
        protected string csvData = string.Empty;

        protected string titleStr;

        protected string folderPath = "";

        protected string document = "";//说明

        // 存储协程步骤的队列
        protected Queue<Func<bool>> coroutineSteps = new Queue<Func<bool>>();

        public void CoroutineUpdate()
        {
            // 如果有协程步骤在队列中
            if (coroutineSteps.Count > 0)
            {
                if (coroutineSteps.Peek().Invoke()) // 调用队列头部的方法，并传递其返回值（若为true，表示当前步骤完成）
                {
                    coroutineSteps.Dequeue(); // 如果步骤完成，从队列中移除
                }
            }
        }

        protected CheckerBase(string titleStr)
        {
            this.titleStr = titleStr;
            folderPath = Path.GetFullPath(Path.Combine(Application.dataPath, "Lua"));
            Debug.LogWarning("---folderPath:" + folderPath);
        }

        public virtual void AnalyzeCompleted(Action callback = null) {
            //maxIndex = 0f;
            curIndex = 0f;
            analyzing = false;
            analyzed = true;
            callback?.Invoke();
            EditorUtility.ClearProgressBar();
        }

        public abstract void AnalyzeStart(Action callback);


        public virtual void Reset() {

            maxIndex = 0f;
            curIndex = 0f;

            coroutineSteps = new Queue<Func<bool>>();
            curFileList.Clear();
            csvData = "";
            analyzing = false;
            analyzed = false;
            EditorUtility.ClearProgressBar();
        }


        public virtual void ShowResult() {
            if (analyzing)
            {
                EditorGUILayout.LabelField("正在分析文件夹，请稍候...");
                GUILayout.Space(10);
                UpdateProgress();
                EditorGUILayout.LabelField("搜索结果：总计 " + curFileList.Count + " 项");
                GenerateCSVData();
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(csvData);
                EditorGUILayout.EndScrollView();
            }

            if (analyzed)
            {
                EditorGUILayout.LabelField("分析完成！");
                //EditorGUILayout.LabelField("注：结果列表的文件中，可能有多个对象未Dispose", EditorStyles.miniBoldLabel);
                EditorGUILayout.LabelField("搜索结果：总计 " + curFileList.Count + " 项");
                scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
                EditorGUILayout.TextArea(csvData);
                EditorGUILayout.EndScrollView();
                GUILayout.Space(10);

                if (GUILayout.Button("复制Excel格式到剪贴板"))
                {
                    EditorGUIUtility.systemCopyBuffer = csvData.Replace(",", "\t");
                    Debug.Log("已复制到剪贴板！");
                }
            }

            if (analyzing || analyzed)
            {
                GUILayout.Space(10);
                if (GUILayout.Button("重置"))
                {
                    Reset();
                }
            }
        }
        public void ShowDocumentGUI()
        {
            GUILayout.Space(10);
            GUIStyle labelStyle = new GUIStyle(EditorStyles.label)
            {
                wordWrap = true
            };
            EditorGUILayout.LabelField($"使用说明 : {document}", labelStyle);
        }

        protected void ShowCheckPath()
        {
            GUILayout.Space(10);
            EditorGUILayout.LabelField($"当前分析路径: {folderPath}");
        }
        public virtual void UpdateProgress()
        {
            float barNum = maxIndex != 0f ? curIndex / maxIndex : 0f;
            EditorUtility.DisplayProgressBar("分析中", "正在分析文件夹，请稍候..." + (barNum * 100).ToString("F2") + "%", barNum);
        }
        public virtual void ShowSeachType(Action action)
        {
            ShowDocumentGUI();
            ShowCheckPath();
        }
        protected void GenerateCSVData()
        {
            StringBuilder csvBuilder = new StringBuilder();
            csvBuilder.AppendLine(titleStr);
            foreach (LuaFileInfo fileInfo in curFileList)
            {
                csvBuilder.AppendLine($"{Path.GetFileName(fileInfo.FilePath)},{fileInfo.ObjectName}");
            }
            csvData = csvBuilder.ToString();
        }

        /// <summary>
        /// 获取require的对象名
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="moduleName">模块名</param>
        /// <returns>本地对象名或null</returns>
        protected string GetObjModuleName(string filePath, string moduleName)
        {
            // 确保文件存在，避免读取错误
            if (!File.Exists(filePath)) return null;

            // 逐行读取文件内容
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                // 检查行是否包含特定的require语句
                if (line.Contains($"require \"{moduleName}\""))
                {
                    // 查找等号'='的位置
                    int equalIndex = line.IndexOf('=');
                    // 在'='之前查找最后一个"local"
                    int localIndex = line.LastIndexOf("local", equalIndex);
                    if (equalIndex > 0 && localIndex >= 0)
                    {
                        // 提取对象名
                        string objectName = line.Substring(localIndex + 5, equalIndex - (localIndex + 5)).Trim();
                        // 使用正则表达式移除对象名周围的非字母数字字符
                        objectName = Regex.Replace(objectName, @"^\W*|\W*$", "");
                        return objectName;
                    }
                }
            }
            // 如果没有找到，返回null
            return null;
        }



    }
}
