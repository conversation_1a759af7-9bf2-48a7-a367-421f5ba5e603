using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEditor;
using UnityEngine;

namespace Checker
{
    class ClassCheckAdaper : CheckerBase
    {
        // class类型
        private string className = "base_game_object"; // 输入的 Class 名称
        private List<LuaFileInfo> classList = new List<LuaFileInfo>();
        private Regex classRegex = null;
        private Regex moduleRegex = null;

        public ClassCheckAdaper(string titleStr) : base(titleStr)
        {
            ModeName = CheckerEnum.CLASS;
            document = "用来查看继承后子类的引用和销毁情况，输入父类的类名";
        }


        public override void AnalyzeStart(Action callback)
        {
            Reset();
            analyzing = true;

            curFileList = new List<LuaFileInfo>();

            coroutineSteps.Enqueue(() => ProcessClassFilesCoroutine(folderPath, callback));
        }


        public override void ShowSeachType(Action callback)
        {
            base.ShowSeachType(callback);
            EditorGUILayout.LabelField("Class名称:（填入父类的Class名称）");
            className = EditorGUILayout.TextField(className);
            GUILayout.Space(10);
            if (!analyzing && GUILayout.Button("分析Lua文件夹"))
            {
                AnalyzeStart(callback);
            }
            GUILayout.Space(10);
            ShowResult();
        }

        private bool ProcessClassFilesCoroutine(string folderPath,Action callback)
        {
            DirectoryInfo directoryInfo = new DirectoryInfo(folderPath);
            FileInfo[] luaFiles = directoryInfo.GetFiles("*.txt", SearchOption.AllDirectories);
            maxIndex = luaFiles.Length * 2;

            // 正则表达式用于匹配 class(base_game_object 开头的类定义
            string classPattern = $@"\b(\w+)\s*=\s*class\s*\(\s*{Regex.Escape(className)}\s*(?:,\s*[^)]*)?\)";
            classRegex = new Regex(classPattern, RegexOptions.Compiled);
            // 正则表达式用于匹配 module("module_name") 
            string modulePattern = @"module\s*\([""'](\w+)[""']\)";
            moduleRegex = new Regex(modulePattern);

            foreach (var fileInfo in luaFiles)
            {
                coroutineSteps.Enqueue(() =>
                {
                    return ProcessClassSingleFileCoroutine(fileInfo, callback);
                });
            }

            foreach (var fileInfo in luaFiles)
            {
                coroutineSteps.Enqueue(() =>
                {
                    return ProcessSingleFileCoroutine(fileInfo, callback);
                });
            }

            coroutineSteps.Enqueue(() =>
            {
                AnalyzeCompleted();
                return true;
            });

            return true;
        }

        private bool ProcessSingleFileCoroutine(FileInfo fileInfo,Action callback)
        {
            LuaFileInfo luaFileInfo = new LuaFileInfo();
            luaFileInfo.FilePath = fileInfo.FullName;
            curIndex++;

            CheckClassFilesForDispose(luaFileInfo);

            GenerateCSVData();

            callback?.Invoke();
            return true;
        }
        /// <summary>
        /// 开始检测
        /// </summary>
        private void CheckClassFilesForDispose(LuaFileInfo fileInfo)
        {
            Match match = null;
            Regex regex = null;
            string fileContent = File.ReadAllText(fileInfo.FilePath);

            foreach (LuaFileInfo classInfo in classList)
            {
                // 步骤 2 - 获取所有调用了这些对象的文件
                // 只有在文件内容中存在类名时才继续处理
                if (fileContent.Contains(classInfo.ObjectName))
                {
                    string objModuleName = GetObjModuleName(fileInfo.FilePath, classInfo.FilePath);
                    if (!string.IsNullOrEmpty(objModuleName))
                    {
                        // 步骤 3 - 获取调用这些方法的对象
                        string objectPattern = $@"\b(?<objectName>\w+)\s*=\s*{Regex.Escape(objModuleName)}\.{Regex.Escape(classInfo.ObjectName)}\s*\(\)";
                        regex = new Regex(objectPattern);
                        match = regex.Match(fileContent);
                        if (match.Success)
                        {
                            string objectName = match.Groups["objectName"].Value;
                            // 步骤 4 - 搜索这个对象是否调用了 Dispose()
                            if (!string.IsNullOrEmpty(objectName))
                            {
                                if (!CheckerUtil.HasDisposeMethodCall(fileContent, objectName))
                                {
                                    // 如果没有调用 Dispose() 方法，标记此变量
                                    fileInfo.ObjectName = classInfo.ObjectName + "," + objectName;
                                    if (!curFileList.Contains(fileInfo)) curFileList.Add(fileInfo);
                                }
                            }
                        }
                    }
                }
            }
        }

       
        private bool ProcessClassSingleFileCoroutine(FileInfo fileInfo,Action callback)
        {
            LuaFileInfo luaFileInfo = new LuaFileInfo();
            luaFileInfo.FilePath = fileInfo.FullName;
            UpdateLuaFileClassList(luaFileInfo);

            curIndex++;
            callback?.Invoke();
            return true;
        }

        /// <summary>
        /// 符合条件的所有class列表
        /// </summary>
        /// <returns></returns>
        private void UpdateLuaFileClassList(LuaFileInfo fileInfo)
        {
            // 步骤 1 - 识别所有 class(base_item_object 的声明
            string fileContent = File.ReadAllText(fileInfo.FilePath);
            Match match1 = classRegex.Match(fileContent);
            Match match2 = moduleRegex.Match(fileContent);
            if (match1.Success && match2.Success)
            {
                string class_name = match1.Groups[1].Value;
                string module_name = match2.Groups[1].Value;
                if (!string.IsNullOrEmpty(class_name) && !string.IsNullOrEmpty(module_name))
                {
                    LuaFileInfo classSave = new LuaFileInfo();
                    classSave.FilePath = module_name;
                    classSave.ObjectName = class_name;
                    if (!classList.Contains(classSave)) classList.Add(classSave);
                }
            }
        }


    }
}
