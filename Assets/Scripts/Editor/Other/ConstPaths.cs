using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
namespace War.Base
{
    public class ConstPaths
    {
        public const string AssetBundlesOutputPath = "AssetBundles";
        public static readonly string editor_build_config = "../../Tools/GameConfig/custom/custom_editor_build_config.json";
        public static readonly string build_config = "../../Tools/GameConfig/custom/custom_jenkins.json";

        public const string LuaPatchFilesOutputPath = "LuaPatchFiles";

        public const string
            initialLuaMd5Name = "initial_lua_md5_files.txt"; //lua md5记录文件；打包luascript或者exec时，会自动生成当前所有lua的md5信息

        public const string LuaPatchMd5Name = "lua_patch_md5_files.txt"; //打包lua补丁文件时生成，当前所有lua的md5信息（只用于参考，没有实质用处）
        public const string LuaPatchChangedMd5Name = "changed_luas.txt"; //打包lua补丁文件时生成，相较对比版本，有变化的文件列表
        public const string LuaPatchAssetName = "lua_patch.asset"; //打包lua补丁文件时生成，相较对比版本，有变化的文件列表
       
        public const string LuaMiscMd5Name = "lua_misc_md5_files.txt"; //打包修复lua补丁文件时生成，当前所有lua的md5信息（只用于参考，没有实质用处）
        public const string LuaMiscChangedMd5Name = "lua_misc_changed.txt"; //打包修复lua补丁文件时生成，相较对比版本，有变化的文件列表
        public const string LuaMiscAssetName = "patch_misc.asset"; //打包修复lua问题补丁文件时生成，相较对比版本

        public const string
            LastBuilLuaPatchChangedMd5Name =
                "lua_patch_changed_md5.txt"; //最后一次打包lua补丁文件时生成，记录打包补丁变化文件的md5值（由所有变化文件的MD5值累加后，计算相应md5）

        public const string
            luaPatchMd5List = "lua_patch_md5_list.txt"; //所有lua补丁md5记录文件,如果当前要生成的lua补丁md5和历史生成的补丁相同，则使用已生成补丁


        public const string luaTableAbMark = "Assets/Lua/Tables";
        public const string luaTablePatchAbMark = "/tablePatch/";
        public const string luaBinaryPBABMark = "/protomsg_new/";
        public static List<string> excludeABFiles = new List<string>()
        {
            "update.json",
            "files.txt",
            "files2.txt",
            "files.txt.bytes",
            ".DS_Store",
            ".manifest",
            ".dll",
            ".mdb",
            "package.json"
        };

        public enum SEL_TABLE
        {
            cn,
            en,
            en_oumei,
            en_gangaotai,
        }
    }
}