
using UnityEngine;
using UnityEditor;
using System.IO;
using System;
using System.Collections.Generic;
using System.Linq;

namespace War.Base
{
    public class Subpackage
    {
        //[MenuItem("Tools/SubpackageCasualGame")]
        /// <summary>
        /// 聚合分包处理
        /// </summary>
        public static void SubpackageBuild()
        {
            //测试
            //string combineMarkJsonInStreamingPath = Path.Combine(Application.streamingAssetsPath, "CombineMark.json");
            //SubpackageCasualGameCombineMark(combineMarkJsonInStreamingPath);

            bool isHasGradleDirPathArgs = false;
            var abSavePath = "";
            var buildTarget = EditorUserBuildSettings.activeBuildTarget;
            Dictionary<string, string> argsInfoDic = new Dictionary<string, string>();
            string _key, _value;
            _key = _value = null;
            var args = Environment.GetCommandLineArgs();
            foreach (var param in args)
            {
                if (param.Length > 0 && param.ElementAt(0) == '-')
                {
                    if (param != _key)
                    {
                        if (param == "-ab_save_path")
                        {
                            isHasGradleDirPathArgs = true;
                        }
                        Debug.Log("_key = " + _key);
                        if (!string.IsNullOrEmpty(_key))
                        {
                            if (!argsInfoDic.ContainsKey(_key))
                            {
                                argsInfoDic.Add(_key, _value);
                            }
                            else
                            {
                                argsInfoDic[_key] = _value;
                            }
                        }

                        _key = param;
                        _value = "";
                    }
                }
                else
                {
                    _value = param;
                }
                Debug.Log("param:" + param);
            }
            Debug.Log("----_key = " + _key);
            if (!argsInfoDic.ContainsKey(_key))
            {
                argsInfoDic.Add(_key, _value);
            }
            else
            {
                argsInfoDic[_key] = _value;
            }
            if (isHasGradleDirPathArgs && string.IsNullOrEmpty(abSavePath))
            {
                abSavePath = argsInfoDic["-ab_save_path"];
            }
            Debug.Log("abSavePath = " + abSavePath);

            Debug.Log("gradle_project_ab_path = " + argsInfoDic["-gradle_project_ab_path"]);

            foreach (var item in argsInfoDic)
            {
                Debug.Log("argsInfoDic.key = " + item.Key + "   value =" + item.Value);
            }

            if (BuildScript.CheckChannelInABResource())
            {
                var aboffset = BuildScript.ABOffset.ParseOffsetConfig();
                if (aboffset != null)
                {
                    "".print("baseOffset:" + aboffset.baseOffset);
                    "".print("nameMod:" + aboffset.nameMod);
                }
            }

            AbResOffset(abSavePath);

            SubpackageCasualGameCombineMark(argsInfoDic["-gradle_project_ab_path"]);
        }

        /// <summary>
        /// 聚合分包ip小游戏配置处理（CombineMark中标识独立热更与非独立热更)
        /// </summary>
        public static void SubpackageCasualGameCombineMark(string gradleProjectAbPath)
        {
            "".print("SubpackageCasualGameCombineMark--start");
            if (!Directory.Exists(gradleProjectAbPath))
            {
                "".PrintError(gradleProjectAbPath + " not exist!!! ");
                EditorApplication.Exit(1);
            }
            string combineMarkJsonInGradleProjectPath = Path.Combine(gradleProjectAbPath, "CombineMark.json");
            string combineMarkJsonInAssetPath = "Assets/CasualGame/CombineMark.json";
            Dictionary<string, object> combineMarkNew = new Dictionary<string, object>();

            if (File.Exists(combineMarkJsonInAssetPath))
            {
                Dictionary<string, string[]> combineMarkDic = AssetBundleManager.ToObj<Dictionary<string, string[]>>(File.ReadAllText(combineMarkJsonInAssetPath));
                foreach (var combineMark in combineMarkDic)
                {
                    List<string> standAloneList = new List<string>();
                    List<string> notStandAloneList = new List<string>();
                    foreach (var item in combineMark.Value)
                    {
                        if (BuildScript.CheckReskeyIsSupportStandAlong(item))
                        {
                            standAloneList.Add(item);
                            "".print(combineMark.Key + " standAloneList item:" + item);
                        }
                        else
                        {
                            notStandAloneList.Add(item);
                            "".print(combineMark.Key + " notStandAloneList item:" + item);
                        }
                    }
                    combineMarkNew.Add(combineMark.Key, new Dictionary<string, object>{
                        {"stand_alone", standAloneList },
                        {"res_key", notStandAloneList }
                    });
                }
                File.WriteAllText(combineMarkJsonInGradleProjectPath, AssetBundleManager.ToJson(combineMarkNew));
            }
            else
            {
                "".PrintError(combineMarkJsonInAssetPath + " not exist!!!");
                EditorApplication.Exit(1);
            }

            "".print("SubpackageCasualGameCombineMark--end");
        }


        public static void AbResOffset(string ab_save_path)
        {
            //偏移方式混淆 AB 资源
            if (BuildScript.CheckChannelInABResource())
            {
                BuildScript.OffsetAbs(ab_save_path);
                Debug.Log("BuildAssetBundles OffsetAbs");
            }
            else
            {
                Debug.Log("BuildAssetBundles encrypt ab is not");
            }
        }
    }
}