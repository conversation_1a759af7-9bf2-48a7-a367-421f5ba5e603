using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using War.UI;

public class SubtitleEditor : EditorWindow
{
    [MenuItem("Designer/Video")]
    static void Init()
    {
        // Get existing open window or if none, make a new one:
        SubtitleEditor window = (SubtitleEditor)EditorWindow.GetWindow(typeof(SubtitleEditor));
        window.Show();
    }

    private void OnGUI()
    {
        if (GUILayout.Button("Import srt"))
        {
            string path = EditorUtility.OpenFilePanel("xx", "", "srt");
            if (path != null && path != "")
            {
                SubtitleData subtitle = SRTImporter.Import(path);
                if (subtitle)
                {
                    string save = EditorUtility.SaveFilePanelInProject("xx", "SubtitleData", "asset", "");
                    if (save != null && save != "")
                    {
                        SubtitleData data = AssetDatabase.LoadAssetAtPath<SubtitleData>(save);
                        if (data == null)
                        {
                            AssetDatabase.CreateAsset(subtitle, save);
                            AssetDatabase.SaveAssets();
                        }
                        else
                        {
                            data.data = subtitle.data;
                            EditorUtility.SetDirty(data);
                            AssetDatabase.SaveAssets();
                        }
                    }
                }
            }
        }
    }
}

public class SRTImporter
{
    enum State
    {
        state_blank,
        state_time,
        state_text,
    }

    class ImportContext
    {
        public SubtitleData subtitle;
        public SubtitleData.Data temp = new SubtitleData.Data();
        public State state = State.state_blank; 
    }

    public static SubtitleData Import(string srtPath)
    {
        string[] lines = System.IO.File.ReadAllLines(srtPath);
        if (lines != null && lines.Length > 0)
        {
            ImportContext context = new ImportContext();
            context.subtitle = ScriptableObject.CreateInstance<SubtitleData>();

            foreach (string line in lines)
            {
                string trimed = line.Trim();
                switch (context.state)
                {
                    case State.state_time:
                        context.state = ParseTime(trimed, context);
                        break;
                    case State.state_text:
                        context.state = ParseText(trimed, context);
                        break;
                    case State.state_blank:
                        context.state = ParseBlank(trimed, context);
                        break;
                }
            }
            return context.subtitle;
        }
        else
            return null;
    }

    private static State ParseBlank(string line, ImportContext context)
    {
        int id;
        if (line == "")
            return State.state_blank;
        else if (int.TryParse(line, out id))
            return State.state_time;
        else
            return State.state_blank;
    }

    private static State ParseTime(string line, ImportContext context)
    {
        string[] times = line.Split(' ', '-', '>', ':', ',');
        if (times.Length == 12)
        {
            int start_hh = int.Parse(times[0]);
            int start_mm = int.Parse(times[1]);
            int start_ss = int.Parse(times[2]);
            int start_mi = int.Parse(times[3]);

            int end_hh = int.Parse(times[8]);
            int end_mm = int.Parse(times[9]);
            int end_ss = int.Parse(times[10]);
            int end_mi = int.Parse(times[11]);

            context.temp.start = start_hh * 3600 * 1000 + start_mm * 60 * 1000 + start_ss * 1000 + start_mi;
            context.temp.end = end_hh * 3600 * 1000 + end_mm * 60 * 1000 + end_ss * 1000 + end_mi;
            return State.state_text;
        }
        else
        {
            return State.state_time;
        }
    }

    private static State ParseText(string line, ImportContext context)
    {
        if (line != "")
        {
            if (context.temp.text == null)
                context.temp.text = line;
            else
                context.temp.text += "\r\n" + line;
            return State.state_text;
        }
        else
        {
            context.subtitle.data.Add(context.temp);
            context.temp = new SubtitleData.Data();
            return State.state_blank;
        }
    }
}
