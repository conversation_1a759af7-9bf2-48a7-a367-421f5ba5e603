using UnityEngine;
using UnityEditor;
using System.IO;
using War.Battle;
using LitJson;
using System.Collections.Generic;
using UnityEngine.SceneManagement;
using System.Collections;
using System.Linq;

public  class ModelViewEditorWin:EditorWindow
{
    private static ModelViewEditorWin win;

    [InitializeOnLoadMethod]
    static void RegisterFunc()
    {
        SkillEditHelper.staticEditorFunc = Init;
    }

   
    [MenuItem("苍穹Tools/模型选择器")]
	public  static void Init()
    {
        if(win == null)
        {
            win = EditorWindow.CreateWindow<ModelViewEditorWin>();
        }
        win.Show();
    }

    private void OnGUI()
    {
       
        if (GUILayout.Button("更新模型配置"))
        {
            UpdateCfg();
        }
        DrawHeroModelPreview();
    }

    private string heroModelPrefabPathCfg;
    //private string heroModelPath;
    private string heroModelPath_new;
    private SkillEditHelper skillEditorHelper;
    private void OnEnable()
    {
        heroModelPrefabPathCfg = Application.dataPath.Replace("Assets", "") + "heroModelPrefabPathCfg.json";
        //heroModelPath = "Assets/Art_old/Characters/";
        heroModelPath_new = "Assets/Art/Characters/";
    }
    private bool loadingFlag = false;
    public bool normalOnly = true;
    public bool slgOnly = true;
    public bool fastMode = true;
    private void UpdateCfg()
    {
        
        var jsonDt = new JsonData();
        //string[] dirs1 = Directory.GetDirectories(heroModelPath);
        string[] dirs2 = Directory.GetDirectories(heroModelPath_new);
        List<string> dirls = new List<string>();
        //for(int i = 0; i < dirs1.Length; i++)
        //{
        //    dirls.Add(dirs1[i]);
        //}
        for (int i = 0; i < dirs2.Length; i++)
        {
            dirls.Add(dirs2[i]);
        }
        string[] dirs = dirls.ToArray();
        EditorUtility.DisplayProgressBar("更新中", "0/"+ dirs.Length, 0);
        for (int i = 0; i < dirs.Length; i++)
        {
            string prFolder = dirs[i] + "/Prefabs";
            if (Directory.Exists(prFolder)) //资源路径下有预制体文件夹
            {
                string[] files = Directory.GetFiles(prFolder, "*.prefab");

                for(int j = 0; j < files.Length; j++)
                {
                    string filePath = files[j]; 
                    GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(filePath);
                    if (go.GetComponent<CardConfig>())
                    {
                        var js = new JsonData();
                        js["prefabPath"] = filePath.Replace("\\", "/");
                        js["prefabName"] = go.name;
                        jsonDt.Add(js);
                    }
                }
            }
            EditorUtility.DisplayProgressBar("更新中", i+"/" + dirs.Length, i*0.1f/dirs.Length);
        }

        EditorUtility.ClearProgressBar();
        File.WriteAllText(heroModelPrefabPathCfg, jsonDt.ToJson()); 
    }
 
    private E_ModelDrawItem[] drawModelItem;
    private int curSelectedHeroIndex = -1; //当前选择需要上阵的英雄，选中后可以设置加载到战场
    private Vector2 heroModelScrollPos;
    private string heroFilterTxt = "";
    private void DrawHeroModelPreview()
    {
        EditorGUILayout.BeginHorizontal(GUILayout.Width(200));
        EditorGUILayout.LabelField(new GUIContent("搜索"),GUILayout.Width(50));
        heroFilterTxt = EditorGUILayout.TextField(heroFilterTxt);
        //showOnly_Use = false; 
        normalOnly = EditorGUILayout.Toggle("只展示普通", normalOnly);
        slgOnly = EditorGUILayout.Toggle("只展示slg", slgOnly);
        fastMode = EditorGUILayout.Toggle("极速模式", fastMode);
        EditorGUILayout.EndHorizontal();
        if (drawModelItem == null)
        {
            if (!File.Exists(heroModelPrefabPathCfg))
            {
                UpdateCfg();
            }
            if(drawModelItem == null)
            {
                JsonData heroModelJson = JsonMapper.ToObject(File.ReadAllText(heroModelPrefabPathCfg));
                drawModelItem = new E_ModelDrawItem[heroModelJson.Count];
                for(int i = 0; i < drawModelItem.Length; i++)
                {
                    var item = new E_ModelDrawItem();

                    item.prefabName = ((string)heroModelJson[i]["prefabName"]);
                    item.prefabPath = ((string)heroModelJson[i]["prefabPath"]);
                    drawModelItem[i] = item;
                }
            } 
 
            //EditorCoroutineRunner.StartEditorCoroutine(LoadModel());
          
        }
        GUILayout.BeginArea(new Rect(0, 100, 600, 600));
        heroModelScrollPos = GUILayout.BeginScrollView(heroModelScrollPos,GUILayout.Width(600));
        GUILayout.BeginHorizontal();
        int index = 0;
        for (int i = 0; i < drawModelItem.Length; i++)
        {
            if (!string.IsNullOrEmpty(heroFilterTxt) && !drawModelItem[i].prefabName.Contains(heroFilterTxt))
            {
                continue;
            }
            if (normalOnly)
            {
                string[] keywords = new string[] {"_Use", "_Stand", "_Simple", "_ShowStand"};
                if (keywords.Any(drawModelItem[i].prefabName.Contains))
                {
                    continue;
                }
            }
            if (slgOnly)
            {
                string[] keywords = new string[] {"aerwaleisi03", "aideruike", "andelie01", "bahamute", "beien", "bingfengwangshou", "boruiasi", "conglinzhige", "dannisi", "feierrui01", "feierrui03", "feinikesi01", "guimianxiuluowang", "gutoulong", "kelisiduo", "kexila", "kuangdianquanwang", "laina", "longnv", "longsha02", "malisha", "maojia", "milana", "monika", "moshuxiehun", "qiangweizhiwang", "sangshi", "shangshi", "shengcaizhe", "sibada", "suofeiya", "tiyamate", "tulongzhe", "tuolun", "yalun", "yinghuo05", "zhanzhengnvshen"};
                if (!keywords.Contains(drawModelItem[i].prefabName))
                {
                    continue;
                }
            }

            DrawModelItemByIndex(i);
            
            if ((index + 1) % 3 == 0)
            {
                GUILayout.EndHorizontal();
                GUILayout.BeginHorizontal();
            }
            index++;
        }
        GUILayout.EndHorizontal(); 
        GUILayout.EndScrollView();
        GUILayout.EndArea();
        GUILayout.BeginArea(new Rect(600, 20, 600, 600));
        
        if (SceneManager.GetActiveScene().name !="SkillEditor")
        {
            GUILayout.Label("打开技能编辑器场景可以选择布阵");
        }
        else
        {
            DrawHeroScenePos();
        } 

        GUILayout.EndArea();
      
    }
    private GUIContent[] posContent = new GUIContent[] { new GUIContent("8"), new GUIContent("10"), new GUIContent("9"), new GUIContent(" "), new GUIContent("7"), new GUIContent("6"), new GUIContent(" "), new GUIContent("4"), new GUIContent("3"), new GUIContent("5"), new GUIContent("1"), new GUIContent("0") };
    private int[] teamMarkIndex = new int[] {2, 4, 3, 6, 1, 0, 6, 4, 3, 5, 1, 0};
    private void DrawHeroScenePos()
    {
        if (skillEditorHelper == null)
        {
            skillEditorHelper = GameObject.Find("director").GetComponent<SkillEditHelper>();
        }
        if (skillEditorHelper != null)
        {
            if (curSelectedHeroIndex != -1)
            {
                var cfg = drawModelItem[curSelectedHeroIndex].config;
                if (fastMode)
                {
                    skillEditorHelper.team_1[4] = cfg;
                    curSelectedHeroIndex = -1;
                }
                else
                {
                    EditorGUILayout.BeginHorizontal();
                    for(int i = 0; i < posContent.Length; i++)
                    {
                        if (teamMarkIndex[i] == 6)
                        {
                            GUILayout.Button(posContent[i],GUILayout.Width(100), GUILayout.Height(100));
                        }
                        else
                        {
                            if (GUILayout.Button(posContent[i],GUILayout.Width(100), GUILayout.Height(100)))
                            {
                                
                                if (cfg == null)
                                {
                                    curSelectedHeroIndex = -1;
                                    return;
                                }
                                if (i > 5)
                                {
                                    skillEditorHelper.team_1[teamMarkIndex[i]] = cfg;
                                }
                                else
                                {
                                    skillEditorHelper.team_2[teamMarkIndex[i]] = cfg;
                                } 
                                curSelectedHeroIndex = -1;
                            }
                        }

                        if ((i + 1) % 3 == 0)
                        {
                            EditorGUILayout.EndHorizontal();
                            EditorGUILayout.BeginHorizontal();
                        }
                    }
                    EditorGUILayout.EndHorizontal();
                }

                if (EditorApplication.isPlaying && curSelectedHeroIndex == -1)
                {
                    skillEditorHelper.Load();
                }
            }  
        }
    }

   
    private E_ModelDrawItem FindMatchCfg(Card cfg)
    {
        for(int i = 0; i < drawModelItem.Length; i++)
        {
            if(drawModelItem[i].config == cfg)
            {
                return drawModelItem[i];
            }
        }
        return null;
    }

  

    private void DrawModelItemByIndex(int index)
    {
        var content = drawModelItem[index];
        if (content.previewContent != null)
        {
            GUILayout.BeginVertical();
            GUILayout.Label(new GUIContent(content.prefabName));
            if (GUILayout.Button(content.previewContent))
            {
                curSelectedHeroIndex = index;
            }
            GUILayout.EndVertical();
        }
        else //加入到加载队列中等待
        {
            loadingQueue.Enqueue(content); 
            EditorCoroutineRunner.StartEditorCoroutine(LoadModel());
        }
    }
    private Queue<E_ModelDrawItem> loadingQueue = new Queue<E_ModelDrawItem>();
    IEnumerator LoadModel()
    {
        if (loadingFlag)
        {
            yield return null;
        }
        loadingFlag = true;


        if (loadingQueue.Count > 0)
        {
            var item = loadingQueue.Dequeue();
            while (item != null)
            {
                item.InitByRes();
                yield return new WaitForSeconds(1);
                if (loadingQueue.Count > 0)
                {
                    item = loadingQueue.Dequeue();
                }
                else { break; }
                
            }
        }
        
       
        yield return null;
        loadingFlag = false;
    }

}


class E_ModelDrawItem
{ 
    public string prefabPath;
    public string prefabName;
    public Card config;
    public GUIContent previewContent;
    public void InitByRes()
    {
        if (previewContent != null)
        {
            return;
        }
        GameObject go = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
        if(go == null)
        {
            Debug.LogError("资源有变动，请重新更新资源引用配置");
        }

        previewContent = new GUIContent(AssetPreview.GetAssetPreview(go)); 
        config = go.GetComponent<Card>();
    }
}