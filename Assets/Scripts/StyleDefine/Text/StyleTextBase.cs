/****************************************************
 *  Copyright © 2018-2022 冰川网络  All rights reserved.
 *------------------------------------------------------------------------
 *  文件：StyleTextBase
 *  作者：wjy
 *  日期：2022-03-01 06:05
 *  功能：用于还原美术的text样式
*****************************************************/

#if UNITY_EDITOR

using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;
using Gradient = War.UI.Gradient;

public class StyleTextBase : MonoBehaviour
{
    public enum FontFile
    {
        ClashRoyale,
        FZY4JW
    }


    public string fontPath = string.Empty;
    public string textColorHtml = string.Empty;
    public string outLineColorHtml = string.Empty;
    public string gradientTopColorHtml = string.Empty;
    public string gradientBottomColorHtml = string.Empty;

    public int fontSize = 24;
    public int outLineDistance = 2;
    public FontStyle fontStyle = FontStyle.Normal;

    public Font font;
    public Text text;
    public Gradient gradient;
    public Outline outLine;


    public Dictionary<FontFile, string> fontFile2Path = new Dictionary<FontFile, string>()
    {
        {FontFile.ClashRoyale, "Assets/UI/Fonts/ClashRoyale.ttf"},
        {FontFile.FZY4JW, "Assets/UI/Fonts/FZY4JW.TTF"}
    };


    public virtual void Reset()
    {
        OnStylePrepare();
        font = AssetDatabase.LoadAssetAtPath<Font>(fontPath);
        text = GetComponent<Text>();
        gradient = GetComponent<War.UI.Gradient>();
        outLine = GetComponent<Outline>();
    }


    [ContextMenu("SetupFormat")]
    public void SetupFormat()
    {
        OnStyleSetup();
        OnStyleFinish();
    }

    public virtual void OnStylePrepare()
    {
        fontPath = fontFile2Path[FontFile.ClashRoyale];
    }

    public virtual void OnStyleSetup()
    {
        //设置text
        if (text != null)
        {
            text.font = this.font;
            text.fontStyle = this.fontStyle;

            if (!ColorUtility.TryParseHtmlString(textColorHtml, out var textColor))
            {
                Debug.LogError("text颜色转换失败，请检查一下颜色的html值");
                return;
            }

            text.color = textColor;
            text.fontSize = fontSize;
            text.SetAllDirty();
        }
        else
        {
            Debug.LogError("gameobject上没有text组件，是否用错了？");
            return;
        }

        //设置描边
        if (string.IsNullOrEmpty(outLineColorHtml))
        {
            if (this.outLine != null)
            {
                DestroyImmediate(outLine);
            }
        }
        else
        {
            if (outLine == null)
            {
                outLine = gameObject.AddComponent<Outline>();
            }

            if (!ColorUtility.TryParseHtmlString(outLineColorHtml, out var outLineColor))
            {
                Debug.LogError("颜色转换失败");
            }

            this.outLine.effectColor = outLineColor;
            this.outLine.effectDistance = Vector2.one * outLineDistance;
        }


        //设置渐变
        if (string.IsNullOrEmpty(gradientTopColorHtml) || string.IsNullOrEmpty(gradientBottomColorHtml))
        {
            if (gradient)
            {
                // DestroyImmediate(gradient);
                gradient.enabled = false;
            }
        }
        else
        {
            if (gradient == null)
            {
                gradient = gameObject.AddComponent<Gradient>();
            }

            if (!ColorUtility.TryParseHtmlString(gradientTopColorHtml, out var topColor))
            {
                Debug.LogError("颜色转换失败");
            }
            else
            {
                gradient.TopColor = topColor;
            }

            if (!ColorUtility.TryParseHtmlString(gradientBottomColorHtml, out var bottomColor))
            {
                Debug.LogError("颜色转换失败");
            }
            else
            {
                gradient.BottomColor = bottomColor;
            }
        }
    }

    public virtual void OnStyleFinish()
    {
    }
}
#endif