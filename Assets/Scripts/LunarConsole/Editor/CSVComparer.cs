using UnityEngine;
using UnityEditor;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Linq;

public class EncodingCSVComparer : EditorWindow
{
    #region 编码配置
    private List<Encoding> supportedEncodings = new List<Encoding>
    {
        Encoding.GetEncoding("GB2312"),
        Encoding.GetEncoding("GB18030"),
        Encoding.UTF8,
    };

    private int selectedEncodingIndex = 0;
    private string[] encodingDisplayNames;
    #endregion

    #region UI变量
    private string filePathA = "", filePathB = "";
    private List<string> headersA = new List<string>();
    private List<string> headersB = new List<string>();
    private List<bool> fieldSelections = new List<bool>();
    private Vector2 scrollPos;
    private List<string> defaultSelectedFields = new List<string>()
    {
        "\"err\"",
        "\"trackback\"",
    };
    #endregion

    [MenuItem("Tools/数数CSV增量对比工具")]
    public static void ShowWindow()
    {
        GetWindow<EncodingCSVComparer>("多编码CSV对比");
    }

    void OnEnable()
    {
        // 生成显示名称
        encodingDisplayNames = supportedEncodings
            .Select(e => $"{e.EncodingName} ({e.CodePage})")
            .ToArray();
    }

    void OnGUI()
    {
        DrawEncodingSelector();
        DrawFileSelection();
        DrawFieldSelection();
        DrawCompareButton();
    }

    void DrawEncodingSelector()
    {
        GUILayout.Label("编码设置", EditorStyles.boldLabel);
        selectedEncodingIndex = EditorGUILayout.Popup(
            "文件编码格式:",
            selectedEncodingIndex,
            encodingDisplayNames);
    }

    void DrawFileSelection()
    {
        GUILayout.Label("文件选择", EditorStyles.boldLabel);
        DrawFilePicker("原文件A:", ref filePathA, ref headersA);
        DrawFilePicker("新文件B:", ref filePathB, ref headersB);
    }

    // 修改读取headersB时的字段初始化逻辑
    void DrawFilePicker(string label, ref string path, ref List<string> headers)
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label(label, GUILayout.Width(60));
        path = GUILayout.TextField(path);
        if (GUILayout.Button("选择", GUILayout.Width(60)))
        {
            path = EditorUtility.OpenFilePanel("选择CSV文件", "", "csv");
            if (label.Contains("B") && !string.IsNullOrEmpty(path))
            {
                headersB = ReadCSVHeaders(path);
                // 初始化勾选状态（带智能默认）
                fieldSelections = headersB.Select(header => 
                    defaultSelectedFields.Any(keyword =>
                        header.IndexOf(keyword, StringComparison.OrdinalIgnoreCase) >= 0
                    )).ToList();
            }
        }
        GUILayout.EndHorizontal();
    }

    List<string> ReadCSVHeaders(string path)
    {
        try
        {
            using var reader = new StreamReader(path, GetSelectedEncoding());
            return reader.ReadLine()?.Split(',').ToList() ?? new List<string>();
        }
        catch
        {
            return new List<string>();
        }
    }

    void DrawFieldSelection()
    {
        GUILayout.Space(10);
        GUILayout.Label("提示：");
        GUILayout.Space(10);
        GUILayout.Label("1，支持对比AB文件，输出B相对于A新增的数据行\n 2，对比的参数支持多选，当前默认勾选err、trackback字段");
        GUILayout.Space(10);
        GUILayout.Label("匹配字段选择（可多选）:");
        
        scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
        if (headersB.Count > 0)
        {
            while (fieldSelections.Count < headersB.Count)
                fieldSelections.Add(false);

            for (int i = 0; i < headersB.Count; i++)
            {
                fieldSelections[i] = EditorGUILayout.ToggleLeft(
                    headersB[i], 
                    fieldSelections[i]);
            }
        }
        EditorGUILayout.EndScrollView();
    }

    void DrawCompareButton()
    {
        if (!GUILayout.Button("开始对比", GUILayout.Height(40))) return;

        try
        {
            if (fieldSelections.Count(s => s) == 0)
                throw new Exception("请至少选择一个匹配字段");

            var dataA = ParseCSV(filePathA);
            var dataB = ParseCSV(filePathB);
            var result = CompareData(dataA, dataB);
            SaveResultCSV(result);

            EditorUtility.DisplayDialog("完成", 
                $"成功导出{result.Count}条新增数据\n编码格式：{GetSelectedEncoding().EncodingName}", 
                "确定");
        }
        catch (Exception e)
        {
            EditorUtility.DisplayDialog("错误", e.Message, "确定");
        }
    }

    List<Dictionary<string, string>> ParseCSV(string path)
    {
        var list = new List<Dictionary<string, string>>();
        using var reader = new StreamReader(path, GetSelectedEncoding());
        
        var headers = reader.ReadLine()?.Split(',');
        if (headers == null) return list;

        while (!reader.EndOfStream)
        {
            var line = reader.ReadLine();
            if (string.IsNullOrEmpty(line)) continue;

            var values = ParseCSVLine(line);
            var dict = new Dictionary<string, string>();
            for (int i = 0; i < headers.Length && i < values.Count; i++)
            {
                dict[headers[i]] = values[i];
            }
            list.Add(dict);
        }
        return list;
    }

    List<string> ParseCSVLine(string line)
    {
        var result = new List<string>();
        var sb = new StringBuilder();
        bool inQuotes = false;

        foreach (char c in line)
        {
            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                result.Add(sb.ToString().Trim());
                sb.Clear();
            }
            else
            {
                sb.Append(c);
            }
        }
        result.Add(sb.ToString().Trim());
        return result;
    }

    List<Dictionary<string, string>> CompareData(
        List<Dictionary<string, string>> dataA, 
        List<Dictionary<string, string>> dataB)
    {
        var selectedFields = headersB
            .Where((_, i) => fieldSelections[i])
            .ToList();

        var keySet = new HashSet<string>(dataA.Select(row => 
            string.Join("|", selectedFields.Select(f => row.ContainsKey(f) ? row[f] : ""))
        ));

        return dataB.Where(row =>
        {
            var currentKey = string.Join("|", selectedFields.Select(f => row.ContainsKey(f) ? row[f] : ""));
            return !keySet.Contains(currentKey);
        }).ToList();
    }

    void SaveResultCSV(List<Dictionary<string, string>> data)
    {
        if (data.Count == 0)
            throw new Exception("没有检测到新增数据");

        var savePath = EditorUtility.SaveFilePanel("保存结果", "", "new_data.csv", "csv");
        if (string.IsNullOrEmpty(savePath)) return;

        using var writer = new StreamWriter(savePath, false, GetSelectedEncoding());
        writer.WriteLine(string.Join(",", headersB));
        data.ForEach(row => 
            writer.WriteLine(string.Join(",", headersB.Select(h => EscapeCSV(row.ContainsKey(h) ? row[h] : ""))))
        );
    }

    // 修改后的编码获取方法
    Encoding GetSelectedEncoding()
    {
        return supportedEncodings[selectedEncodingIndex];
    }


    string EscapeCSV(string value)
    {
        if (value.Contains(",") || value.Contains("\"") || value.Contains("\n"))
            return $"\"{value.Replace("\"", "\"\"")}\"";
        return value;
    }
}
