#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using Newtonsoft.Json;
    using System.Linq;
    using System.IO;
    using UnityEngine.U2D;
    using UnityEditor.Sprites;
    using UnityEditor.Callbacks;
    using System.Text;
    using System.Text.RegularExpressions;
    using War.Base;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class CheckBigPic : GlobalConfig<CheckBigPic>
    {
        [Serializable]
        public class Proto { }

        [Serializable]
        public class StrProto :Proto{

            [HideLabel]
            [HorizontalGroup]
            public string name;
            [HideLabel]
            [HorizontalGroup]
            public string info;

            [HorizontalGroup(50)]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }
            [HorizontalGroup(50)]
            [Button]
            void Pack()
            {
                string path = name;
                TextureImporter assetImporter = AssetImporter.GetAtPath(name) as TextureImporter;
                if (assetImporter == null)
                {
                    return;
                }
                // 添加NonPacktag 标签
                List<string> lables = new List<string>();
                lables.AddRange(AssetDatabase.GetLabels(assetImporter));
                if (lables.IndexOf("NonPacktag") == -1)
                {
                    lables.Add("NonPacktag");
                }
                AssetDatabase.SetLabels(assetImporter, lables.ToArray());
                //清空spritePackingTag
                assetImporter.spritePackingTag = "";

                //设置assetBundleName为path
                string assetBundleName = path.Substring(path.IndexOf("/") + 1);
                assetImporter.assetBundleName = assetBundleName;
                LogHelp.clipboard = assetBundleName.ToLower();
            }
            [HorizontalGroup(40)]
            [Button]
            void _1024()
            {
                var ai = AssetImporter.GetAtPath(name) as TextureImporter;
                if (ai)
                {
                    var ti = AssetDatabase.LoadAssetAtPath<Sprite>(name) as Sprite;
                    if (ti)
                    {
                        //Debug.LogError(ti.name);
                        ai.maxTextureSize = 1024;
                        ai.SaveAndReimport();
                    }
                }
            }
            [HorizontalGroup(40)]
            [Button]
            void _512()
            {
                var ai = AssetImporter.GetAtPath(name) as TextureImporter;
                if (ai)
                {
                    var ti = AssetDatabase.LoadAssetAtPath<Sprite>(name) as Sprite;
                    if (ti)
                    {
                        //Debug.LogError(ti.name);
                        ai.maxTextureSize = 512;
                        ai.SaveAndReimport();
                    }
                }
            }
            [HorizontalGroup(40)]
            [Button]
            void _256()
            {
                var ai = AssetImporter.GetAtPath(name) as TextureImporter;
                if (ai)
                {
                    var ti = AssetDatabase.LoadAssetAtPath<Sprite>(name) as Sprite;
                    if (ti)
                    {
                        //Debug.LogError(ti.name);
                        ai.maxTextureSize = 256;
                        ai.SaveAndReimport();
                    }
                }
            }

        }
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase)|| s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);
        //Func<string, bool> isGo = (s) => s.EndsWith(".prefab", StringComparison.CurrentCultureIgnoreCase);
        Func<string, bool> isAss = (s) => s.EndsWith(".asset", StringComparison.CurrentCultureIgnoreCase);

        private string ToJson(object o)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(o, Formatting.Indented);
        }
        private string CheckIsBig(string path)
        {
            var ti = AssetDatabase.LoadAssetAtPath<Texture2D>(path) as Texture2D;
            if (ti != null)
            {
                var b = Mathf.Max(ti.width, ti.height) > sizeThreshold;
                if (b)
                {
                    var ai = AssetImporter.GetAtPath(path) as TextureImporter;
                    if(ai.textureType== TextureImporterType.Sprite)
                    {
                        return ti.width + "_" + ti.height + "_" + ai.spritePackingTag;
                    }
                }
            }
            return "";
        }
        private int SortFun(KeyValuePair<string, string> l1, KeyValuePair<string, string> l2) {

            var ti = AssetDatabase.LoadAssetAtPath<Texture2D>(l1.Key) as Texture2D;
            var ti2 = AssetDatabase.LoadAssetAtPath<Texture2D>(l2.Key) as Texture2D;
            var m1 = Mathf.Max(ti.width, ti.height);
            var m2 = Mathf.Max(ti2.width, ti2.height);

            if (m1 == m2)
            {
                m1 = Mathf.Min(ti.width, ti.height);
                m2 = Mathf.Min(ti2.width, ti2.height);
            }

            return m2 - m1;
        }

        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/UI",
        };
        public bool onlyUse = true;
        public int sizeThreshold = 512;

        public List<StrProto> BigPicListRough = new List<StrProto>();
        public List<StrProto> BigPicList;

        [Button()]
        void Check()
        {
            _CheckAndPack(false);
        }

        [Button()]
        void CheckAndPackAll()
        {
            _CheckAndPack(true);
        }

        [Button()]
        void Clear()
        {
            BigPicList.Clear();
            BigPicListRough.Clear();
        }

        private void _CheckAndPack(bool isPack)
        {
            var allSet = new Dictionary<string, string>();
            var usedSet = new Dictionary<string, string>();
            var assetDependSet = new Dictionary<string, string>();
            var finda = AssetDatabase.FindAssets("", findassets.ToArray());
            EditorApplication.update = delegate ()
            {
                try
                {
                    int count = finda.Length;
                    for (int i = 0; i < count/*finda.Length*/; i++)
                    {
                        string uid = finda[i];
                        var ob = AssetDatabase.GUIDToAssetPath(uid);


                        bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", ob, (float)i / (float)finda.Length);

                        if (isPng(ob))
                        {
                            string tInfo = CheckIsBig(ob);
                            if (tInfo != "")
                            {
                                allSet[ob] = tInfo;
                            }
                        }
                        else
                        {
                            var dependences = AssetDatabase.GetDependencies(ob, true);
                            foreach (var dep in dependences)
                            {
                                if (isPng(dep))
                                {
                                    string tInfo = CheckIsBig(dep);
                                    if (tInfo != "")
                                    {
                                        if (!usedSet.ContainsKey(dep))
                                        {
                                            if (tInfo.Contains("ui.bigPic"))
                                            {
                                                BigPicListRough.Add(new StrProto() { name = dep, info = tInfo });
                                            }
                                        }
                                        usedSet[dep] = tInfo;
                                    }
                                }
                            }

                            if (isPack && isAss(ob))
                            {
                                var depen = AssetDatabase.GetDependencies(ob, true);
                                foreach (var dep in dependences)
                                {
                                    if (isPng(dep))
                                    {
                                        assetDependSet[dep] = "";
                                    }
                                }
                            }

                        }


                        if (isCancel || i >= finda.Length - 1)
                        {
                            //sort
                            var allList = allSet.ToList();
                            allList.Sort(SortFun);
                            var useList = usedSet.ToList();
                            useList.Sort(SortFun);

                            //log info
                            Debug.LogError(allList.Count);
                            Debug.LogError(ToJson(allList));
                            Debug.LogError(useList.Count);
                            Debug.LogError(ToJson(useList));


                            var tList = onlyUse ? useList : allList;

                            BigPicList = new List<StrProto>();

                            for (int j = 0; j < tList.Count; j++)
                            {
                                var item = tList[j];
                                if (item.Value.Contains("ui.bigPic"))
                                {
                                    BigPicList.Add(new StrProto() { name = item.Key, info = item.Value });
                                }
                            }

                            if (isPack)
                            {
                                var sett = new HashSet<string>(usedSet.Keys);
                                sett.ExceptWith(assetDependSet.Keys);
                                Debug.LogError(sett.Count);
                                Debug.LogError(ToJson(sett));
                                var num = 1;
                                foreach (var item in sett)
                                {
                                    bool isCancel2 = EditorUtility.DisplayCancelableProgressBar("packingTag...", ob, (float)num / (float)sett.Count);
                                    if (isCancel2) break;

                                    //var ai = AssetImporter.GetAtPath(item) as TextureImporter;
                                    //var ti = AssetDatabase.LoadAssetAtPath<Sprite>(item) as Sprite;
                                    //if (ai && ti)
                                    //{
                                    //    //Debug.LogError(ti.name);
                                    //    ai.spritePackingTag = "ui.bigPic" + num + ti.name;
                                    //    num = num + 1;
                                    //    ai.SaveAndReimport();  
                                    //}
                                    string path = item;
                                    TextureImporter assetImporter = AssetImporter.GetAtPath(item) as TextureImporter;
                                    if (assetImporter == null)
                                    {
                                        return;
                                    }
                                    // 添加NonPacktag 标签
                                    List<string> lables = new List<string>();
                                    lables.AddRange(AssetDatabase.GetLabels(assetImporter));
                                    if (lables.IndexOf("NonPacktag") == -1)
                                    {
                                        lables.Add("NonPacktag");
                                    }
                                    AssetDatabase.SetLabels(assetImporter, lables.ToArray());
                                    //清空spritePackingTag
                                    assetImporter.spritePackingTag = "";

                                    //设置assetBundleName为path
                                    string assetBundleName = path.Substring(path.IndexOf("/") + 1);
                                    assetImporter.assetBundleName = assetBundleName;
                                    LogHelp.clipboard = assetBundleName.ToLower();
                                }
                            }



                            EditorUtility.ClearProgressBar();
                            EditorApplication.update = null;
                            Debug.Log("匹配结束");

                            return;
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError(e.ToString());
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                }

            };
        }


    }
}
#endif
