#if UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using UnityEditor;
using UnityEditor.U2D;
using UnityEngine;
using UnityEngine.U2D;
using War.Base;

public class ToolUti
{ 
    public static Texture2D SliceTex2Tex(Texture2D tex, float xmin, float xmax, float ymin, float ymax)
    {
        var oldReadable = true;
        var texpath = AssetDatabase.GetAssetPath(tex);
        var ai = AssetImporter.GetAtPath(texpath) as TextureImporter;
        if (ai)
        {
            oldReadable = ai.isReadable;
            ai.isReadable = true;
            ai.SaveAndReimport();
        }

        var oX = tex.width;
        var oY = tex.height;

        var xMin = Mathf.FloorToInt(oX * xmin);
        var xMax = Mathf.CeilToInt(oX * xmax);
        var yMin = Mathf.FloorToInt(oY * ymin);
        var yMax = Mathf.CeilToInt(oY * ymax);

        var xCenterSize = 4;
        var yCenterSize = 4;

        var xSize = xMin + oX - xMax + xCenterSize;
        var ySize = yMin + oY - yMax + yCenterSize;

        if (xSize == xCenterSize)
        {
            xMin = xMax = xCenterSize = 0;
            xSize = oX;
        }
        if (ySize == yCenterSize)
        {
            yMin = yMax = yCenterSize = 0;
            ySize = oY;
        }


        var tex_new = new Texture2D(xSize, ySize, TextureFormat.RGBA32, false);

        var xlist = new int[]
        {
                0,xMin,xMax,oX,
        };
        var ylist = new int[]
        {
                0,yMin,yMax,oY,
        };
        var txlist = new int[]
        {
                0,xMin,xMin+xCenterSize,xSize,
        };
        var tylist = new int[]
        {
                0,yMin,yMin+yCenterSize,ySize,
        };
        var xlen = xlist.Length;
        var ylen = xlist.Length;
        for (int i = 0; i < xlen; i++)
        {
            if (i + 1 >= xlen) continue;
            for (int j = 0; j < ylen; j++)
            {
                if (j + 1 >= ylen) continue;

                var x1 = xlist[i];
                var x2 = xlist[i + 1];
                var y1 = ylist[j];
                var y2 = ylist[j + 1];

                var tx1 = txlist[i];
                var tx2 = txlist[i + 1];
                var ty1 = tylist[j];
                var ty2 = tylist[j + 1];

                if ((x2 - x1) * (y2 - y1) == 0) continue;
                if ((x2 - x1) - (tx2 - tx1) != 0)
                {
                    var xcopywide = tx2 - tx1;
                    var ycopywide = ty2 - ty1;
                    var xStart = x1 + xcopywide / 2;
                    var psl = tex.GetPixels(xStart, y1, 1, y2 - y1);

                    var pureps = new Color[xcopywide * ycopywide];
                    for (int k = 0; k < pureps.Length; k++)
                    {
                        //var xx = k % xcopywide;
                        var yy = Mathf.FloorToInt(k / xcopywide);

                        pureps[k] = psl[yy];
                    }
                    tex_new.SetPixels(tx1, ty1, xcopywide, ycopywide, pureps, 0);
                    continue;
                }
                if ((y2 - y1) - (ty2 - ty1) != 0)
                {
                    var xcopywide = tx2 - tx1;
                    var ycopywide = ty2 - ty1;
                    var yStart = y1 + ycopywide / 2;
                    var psl = tex.GetPixels(x1, yStart, x2 - x1, 1);

                    var pureps = new Color[xcopywide * ycopywide];
                    for (int k = 0; k < pureps.Length; k++)
                    {
                        var xx = k % xcopywide;
                        //var yy = Mathf.FloorToInt(k / xcopywide);

                        pureps[k] = psl[xx];
                    }
                    tex_new.SetPixels(tx1, ty1, xcopywide, ycopywide, pureps, 0);
                    continue;
                }

                var ps = tex.GetPixels(x1, y1, x2 - x1, y2 - y1);
                tex_new.SetPixels(tx1, ty1, tx2 - tx1, ty2 - ty1, ps, 0);
            }
        }
        tex_new.Apply();

        ai = AssetImporter.GetAtPath(texpath) as TextureImporter;
        if(ai)
        {
            ai.isReadable = oldReadable;
            ai.SaveAndReimport();
        }
        return tex_new;

    }

    public static Texture2D[] GetAtlasPreviewTexes(SpriteAtlas atlas)
    {
        string buildTarget = EditorUserBuildSettings.activeBuildTarget.ToString();
        var setting = atlas.GetPlatformSettings(buildTarget);
        setting.textureCompression = TextureImporterCompression.Uncompressed;
        atlas.SetPlatformSettings(setting);

        var obj = System.Type
            .GetType("UnityEditor.U2D.SpriteAtlasExtensions, UnityEditor")
            .GetMethod("GetPreviewTextures", BindingFlags.NonPublic | BindingFlags.Static)
            .Invoke(null, new object[] { atlas });

        return (Texture2D[])obj;
    }
    public static Sprite[] GetAtlasPreviewSprites(SpriteAtlas atlas)
    {
        string buildTarget = EditorUserBuildSettings.activeBuildTarget.ToString();
        var setting = atlas.GetPlatformSettings(buildTarget);
        setting.textureCompression = TextureImporterCompression.Uncompressed;
        atlas.SetPlatformSettings(setting);

        var obj = System.Type
            .GetType("UnityEditor.U2D.SpriteAtlasExtensions, UnityEditor")
            .GetMethod("GetPackedSprites", BindingFlags.NonPublic | BindingFlags.Static)
            .Invoke(null, new object[] { atlas });

        return (Sprite[])obj;
    }
    public static void SaveSpriteAtlasTexture2d(SpriteAtlas atlas, string path = null)
    {
        //PackAtlas(atlas);
        //var texs = GetAtlasPreviewTexes(atlas);
        var atlasPath = AssetDatabase.GetAssetPath(atlas);
        var folder = Path.GetDirectoryName(atlasPath);
        var atlasfilename = Path.GetFileName(atlasPath);
        var edit = new Texture2D(0,0, TextureFormat.ARGB32, false);
        var sps = GetAtlasPreviewSprites(atlas);

        Texture2D[] textures = new List<Sprite>(sps).ConvertAll((s) => EditorHelp.CloneTex(s.texture as Texture2D)).ToArray();
        var rs = edit.PackTextures(textures, 4, 2048, false);

        var dic = new Dictionary<string, string>();
        var texpath = "";

        for (int i = 0; i < 1; i++)
        {
            Texture2D tex = edit;
            Texture2D target = new Texture2D(tex.width, tex.height, TextureFormat.ARGB32, false);
            EditorHelp.ApplyUnReadTex____(tex, target);

            path = path ?? string.Format("{0}/{1}", folder, atlasfilename);
            texpath =  string.Format("{0}_{1}.png", path, i);
            dic[tex.name] = texpath;
            File.WriteAllBytes(texpath, target.EncodeToPNG());
        }
        AssetDatabase.Refresh();

        //var sps = GetAtlasPreviewSprites(atlas);
        var dicSprites= new Dictionary<string, List<Sprite>>();

        for (int i = 0; i < sps.Length; i++)
        {
            Sprite sp = (Sprite)sps[i];
            //var texpath = "";
            if(true || dic.TryGetValue(sp.texture.name, out texpath))
            {
                //var tex = AssetDatabase.LoadMainAssetAtPath(texpath) as Texture2D;
                //if (tex == null) {
                //    "".PrintError("tex == null", texpath);
                //    continue;
                //}
                var rect = rs[i];

                var nsp = Sprite.Create(edit, rect, sp.pivot,sp.pixelsPerUnit);
                if(!dicSprites.ContainsKey(texpath))
                {
                    dicSprites[texpath] = new List<Sprite>();
                }
                dicSprites[texpath].Add(nsp);
            }
            else
            {
                "".PrintError("texpath == null", sp.texture.name);

            }
        }

        foreach (var k in dicSprites.Keys)
        {
            var l = dicSprites[k];
            foreach (var s in l)
            {
                AssetDatabase.AddObjectToAsset(s, k);
            }
        }
        AssetDatabase.Refresh();
    }

    public static string ToJson(object o)
    {
        return Newtonsoft.Json.JsonConvert.SerializeObject(o, Newtonsoft.Json.Formatting.Indented);
    }

    public static T ToObj<T>(string o)
    {
        return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(o);
    }

    public static void PackAtlas(SpriteAtlas atlas)
    {
        SpriteAtlasUtility.PackAtlases(new[] { atlas }, EditorUserBuildSettings.activeBuildTarget);
    }


    public static List<string> CalDiff(List<string> l1, List<string> l2)
    {
        var list = new List<string>();
        if (l1 == null || l2 == null) return list;
        var itList = l1;
        var itList2 = l2;
        var count = l1.Count;
        if(l2.Count>count)
        {
            itList = l2;
            itList2 = l1;
        }
        var set = new HashSet<string>();
        set.UnionWith(l2);
        set.UnionWith(l1);
        var set2 = new HashSet<string>();
        set2.UnionWith(l2);
        set2.IntersectWith(l1);

        set.ExceptWith(set2);

        //"".Print("cal");
        //"".Print(ToolUti.ToJson(l1));
        //"".Print(ToolUti.ToJson(l2));
        //"".Print(ToolUti.ToJson(set));
        list.AddRange(set);

        return list;
    }
    public static bool CompareBytes(byte[] b1, byte[] b2)
    {
        if (b1 == null || b2 == null)
        {
            return false;
        }
        if (b1.Length != b2.Length) return false;

        for (int i = 0; i < b1.Length; i++)
        {
            if (b1[i] != b2[i]) return false;
        }
        return true;
    }
    public static byte[] GetTexBytes(Texture2D tex)
    {
        var oldReadable = true;
        var texpath = AssetDatabase.GetAssetPath(tex);
        var ai = AssetImporter.GetAtPath(texpath) as TextureImporter;
        if (ai)
        {
            oldReadable = ai.isReadable;
            ai.isReadable = true;
            ai.SaveAndReimport();
        }

        byte[] bs = tex.EncodeToPNG(); 
        
        ai = AssetImporter.GetAtPath(texpath) as TextureImporter;
        if (ai)
        {
            ai.isReadable = oldReadable;
            ai.SaveAndReimport();
        }

        return bs;
    }

    static public System.Diagnostics.Stopwatch GetStopwatch()
    {
        return System.Diagnostics.Stopwatch.StartNew();
    }

    //将制定文件内容转化为MD5
    static public string String2MD5(string origin)
    {
        string res = null;
        var data = System.Text.Encoding.UTF8.GetBytes(origin);
        //byte[] data = File.ReadAllBytes(path);
        var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
        byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
        md5.Clear();

        var destString = "";
        for (int i = 0; i < md5Data.Length; i++)
        {
            destString += string.Format("{0:x2}", md5Data[i]);
        }
        destString = destString.PadLeft(32, '0');
        res = destString;

        return res;
        }
        //将制定文件内容转化为MD5

    static public string File2MD5(string path)
    {
//#if UNITY_EDITOR_WIN
//        return File3MD5(path);
//#endif
        string res = null;
        if (File.Exists(path))
        {
            byte[] data = File.ReadAllBytes(path);
                var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
                md5.Clear();

                var destString = "";
                for (int i = 0; i < md5Data.Length; i++)
                {
                    destString += string.Format("{0:x2}", md5Data[i]);
                }
                destString = destString.PadLeft(32, '0');
                res = destString;
            }

        return res;
    }

    #region 更改MD5方法

    //调用C++库
    [System.Runtime.InteropServices.DllImport(@"MD5CPP.dll", EntryPoint = "GetMD5",
        CharSet = System.Runtime.InteropServices.CharSet.Ansi,
        CallingConvention = System.Runtime.InteropServices.CallingConvention.Cdecl,
        ExactSpelling = false)]
    static private extern int GetMD5(string str, ref byte res);

    static public byte[] md5data = new byte[1024];
    static public string File3MD5(string path)
    {
        string res = null;
        if (File.Exists(path))
        {
            int l = GetMD5(path, ref md5data[0]);
            if (l > md5data.Length)
                return null;
            else
            {
                res = System.Text.Encoding.Default.GetString(md5data, 0, l);
            }
        }

        return res;
    }
    //将制定文件内容转化为CRC32
    static public long File2CRC32(string path)
    {
        long res = 0;
        if (File.Exists(path))
        {
            byte[] data = File.ReadAllBytes(path);
            var crc32 = new ICSharpCode.SharpZipLib.Checksums.Crc32();
            crc32.Update(data);

            res = crc32.Value;
        }

        return res;
    }
    
    // static public string File4MD5(string path)
    // {
    //     string res = null;
    //
    //     if (File.Exists(path))
    //     {
    //         byte[] s = new byte[1024];
    //         int l = GetMD5(path, ref s[0]);
    //         byte[] data = null;
    //
    //         if (l > s.Length)
    //             return null;
    //         else
    //         {
    //             data = new byte[l];
    //             System.Buffer.BlockCopy(s, 0, data, 0, l);
    //             res = Encoding.Default.GetString(data, 0, data.Length);
    //         }
    //     }
    //
    //     //Debug.Log("md5码：" + res);
    //     return res;
    // }
    #endregion

        public static void MoveDir(string rootPath, string targetDir, string moveFile)
        {
        try
        {

            var item = moveFile;
            var target = item.Replace(rootPath, targetDir);
            EditorHelp.CheckDir(target);
            EditorHelp.CopyFile(item, target);
            EditorHelp.CopyFile(item + ".meta", target + ".meta");
            System.IO.File.Delete(item);
            System.IO.File.Delete(item + ".meta");
        }
        catch (System.Exception e)
        {
            UnityEngine.Debug.LogError(e.ToString());
        }
    }


    public static List<string> Layout2PackingTags(string path)
    {
        var list = new List<string>();
        var deps = AssetDatabase.GetDependencies(path, true);

        foreach (var dep in deps)
        {
            if (dep.EndsWith(".png") || dep.EndsWith(".tga"))
            {
                list.Add(dep);
            }
        }
        return list;
    }

    public static List<string> Layout2Tex(string path)
    {
        var go = AssetDatabase.LoadMainAssetAtPath(path) as GameObject;
        if(go)
        {

        }

        var list = new List<string>();
        var deps = AssetDatabase.GetDependencies(path, true);

        foreach (var dep in deps)
        {
            if (dep.EndsWith(".png") || dep.EndsWith(".tga"))
            {
                list.Add(dep);
            }
        }
        return list;
    }

    public static void Iter(string[] paths, System.Func<string, string> ac, System.Action finish=null,  string title = "title")
    {
            float proccess = 0;
            int length = paths.Length;
            string current = "begin..";
            var iterv = Mathf.Max(Mathf.CeilToInt(length / 100),1);

            if (length == 0)
            { 
                finish?.Invoke();
                return;
            }


            for (int i = 0; i < length; i++)
            {
                try
            {
                var selgo = paths[i];
                proccess = i / (float)length;

                try
                {
                    if (ac != null)
                    {
                        current = ac(selgo);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.ToString());
                }
                if(i% iterv == 0)
                {
                    var cancel =EditorUtility.DisplayCancelableProgressBar(title, string.Format("{1}%\n...{0}", current, (proccess * 100).ToString("F2")), proccess);
                    if(cancel)
                    {
                        break;
                    }
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError(ex.ToString());
            }
        }
        if(finish!=null)
        {
            finish();
        }
        EditorUtility.ClearProgressBar();
    }

    public static void IterUpdate(string[] paths, System.Func<string, string> ac, string title = "title", int count = 10)
    {
        float proccess = 0;
        int length = paths.Length;
        string current = "begin..";
        var i = 0;
        EditorApplication.update = delegate ()
        {
            for (int iiii = 0; iiii < count; iiii++)
            {
                var cancel = EditorUtility.DisplayCancelableProgressBar(title, string.Format("{1}%\n...{0}", current, (proccess * 100).ToString("F2")), proccess);
                if (cancel || i >= length)
                {
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                    return;
                }
                var selgo = paths[i];
                proccess = i / (float)length;

                try
                {
                    if (ac != null)
                    {
                        current = ac(selgo);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.ToString());
                }
                i++;

            }
        };

    }
    public static void IterUpdateFinish(string[] paths, System.Func<string, string> ac, System.Action finish = null, string title = "title", int count = 10)
    {
        float proccess = 0;
        int length = paths.Length;
        string current = "begin..";
        var i = 0;

        if (length == 0)
        {
            finish?.Invoke();
            return;
        }


        EditorApplication.update = delegate ()
        {
            for (int iiii = 0; iiii < count; iiii++)
            {
                var cancel = EditorUtility.DisplayCancelableProgressBar(title, string.Format("{1}%\n...{0}", current, (proccess * 100).ToString("F2")), proccess);
                if (cancel || i >= length)
                {
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                    if(finish!=null)
                    {
                        finish();
                    }
                    return;
                }
                var selgo = paths[i];
                proccess = i / (float)length;

                try
                {
                    if (ac != null)
                    {
                        current = ac(selgo);
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.ToString());
                }
                i++;

            }
        };

    }
    /// <summary>
    /// window系统资源管理器打开 
    /// </summary>
    /// <param name="tFolder"></param>
    public static void Locate(string tFolder)
    {
        if (!System.IO.Directory.Exists(tFolder))
        {
            return;
        }
#if UNITY_EDITOR_WIN
        System.Diagnostics.Process.Start("explorer.exe", tFolder);
#endif
    }
    /// <summary>
    /// 加载prefab到场景并定位到指定物体
    /// </summary>
    /// <param name="goPath"></param>
    /// <param name="relPath"></param>
    /// <returns></returns>
    public static GameObject LocateGameObject(string goPath,string relPath="")
    {
        var go = AssetDatabase.LoadMainAssetAtPath(goPath) as GameObject;
        if(!go)
        {
            Debug.LogError("找不到goPath:"+ goPath);
            return null;
        }
        var p = PrefabIntoCanvas(go);
        if (!p)
        {
            return null;
        }
        if(string.IsNullOrEmpty(relPath))
        {
            return p;
        }
        var ch = p.transform.Find(relPath);
        if (!ch)
        {
            Debug.LogError("找不到relPath:" + relPath);
            return p;
        }
        return ch.gameObject;
    }

    public static GameObject PrefabIntoCanvas(GameObject prefab)
    {
        var parentC = GameObject.Find("UIRoot/CanvasWithMesh");
        if (null == parentC)
        {
            Debug.LogError("请打开ui场景在进行处理");
            return null;
        }
        Transform parent = parentC.transform;

        var PrefabObject = GameObject.Find(prefab.name);
        if (null == PrefabObject)
        {
            PrefabObject = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            PrefabObject.transform.SetParent(parent,false);
        }

        return PrefabObject;
    }
    /// <summary>
    /// 物体相对某父节点路径
    /// </summary>
    /// <param name="sel"></param>
    /// <param name="root"></param>
    /// <returns></returns>
    public static string CopyPathTo(Transform sel, Transform root)
    {
        if (root == null)
        {
            root = sel.transform.root;
        }
        var t = sel.transform;
        var str = "";
        var count = 20000;
        do
        {
            count--;
            if (count < 0) break;
            str = t.name + "/" + str;
            if(t==root)
            {
                break;
            }
            t = t.parent;
        } while (t != null);
        str = str.TrimEnd('/');
        return str;
    }
    static List<string> AssemblyNames = new List<string>()
    {
        "UnityEngine",
        "UnityEngine.UI",
    };

    public static void RemoveABNameByPath(string oPath)
    {
        if (File.Exists(oPath) == false) return;
        var assetPath = oPath;
        var assetImporter = AssetImporter.GetAtPath(assetPath);
        assetImporter.assetBundleName = "";
    }

        public static void SetABNameByPath(string oPath)
        {
            if (File.Exists(oPath) == false) return;
            var assetPath = oPath;
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
            assetImporter.assetBundleName = assetBundleName;
    }
    public static bool DownloadFile(string URL, string filename, int timeout = 1)
    {
        try
        {

            HttpWebRequest Myrq = (System.Net.HttpWebRequest)System.Net.HttpWebRequest.Create(URL);

            HttpWebResponse myrp = (System.Net.HttpWebResponse)Myrq.GetResponse();
            Stream st = myrp.GetResponseStream();
            EditorHelp.CheckDir(filename);
            Stream so = new System.IO.FileStream(filename, System.IO.FileMode.Create);
            byte[] by = new byte[1024];
            int osize = st.Read(by, 0, (int)by.Length);
            st.ReadTimeout = timeout;
            var unow = System.DateTime.Now;
            while (osize > 0)
            {
                so.Write(by, 0, osize);
                osize = st.Read(by, 0, (int)by.Length);
            }
            so.Close();
            st.Close();
            myrp.Close();
            Myrq.Abort();
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError(e.ToString());
            return false;
        }
    }
    public static void CheckDir(string path)
    {
        var dir = Path.GetDirectoryName(path);
        if (!Directory.Exists(dir))
        {
            Directory.CreateDirectory(dir);
        }
    }
    public static bool DownloadFileW(string URL, string filename, int timeout = 1)
    {
        try
        {
            var url = URL;
            if (!URL.Contains("http"))
            {
                if (File.Exists(URL) == false) return false;
                url = "file://" + Path.GetFullPath(URL);
            }
            var w = new UnityEngine.WWW(url);

            System.DateTime l = System.DateTime.Now;
            while (!w.isDone)
            {
                if ((System.DateTime.Now - l).TotalSeconds > 6) break;
            }
            if (string.IsNullOrEmpty(w.error) == false)
            {
                "".PrintError(url, filename, w.error);
                return false;
            }
            CheckDir(filename);
            if(w.isDone)
            {
                File.WriteAllBytes(filename, w.bytes);
            }
            else
            {
                "".PrintError("IsNullOrEmpty_url",url);
                return false;
            }
            
            return true;
        }
        catch (System.Exception e)
        {
            Debug.LogError(e.ToString());
            return false;
        }
    }
    public static AssetBundle DownloadAB(string URL, int timeout = 1)
    {
        try
        {
            var url = URL;
            if (!URL.Contains("http"))
            {
                if (File.Exists(URL) == false) return null;
                url = "file://" + Path.GetFullPath(URL);
            }
            var w = new UnityEngine.WWW(url);

            System.DateTime l = System.DateTime.Now;
            while (!w.isDone)
            {
                var urlTime = (System.DateTime.Now - l).TotalSeconds;
                if (urlTime > timeout)
                {
                    Debug.LogError($"DownloadAB url :[{url}] timeout:{urlTime}.");
                    break;
                }
            }
            if (string.IsNullOrEmpty(w.error) == false)
            {
                "".PrintError(url, w.error);
                return null;
            }
            return w.assetBundle;
        }
        catch (System.Exception e)
        {
            Debug.LogError(e.ToString());
            return null;
        }
    }
    static Dictionary<string, object> cacheDic = new Dictionary<string, object>();
    public static object GetValue(string mark)
    {
        object ob = null;
        cacheDic.TryGetValue(mark, out ob);
        return ob;
    }
    public static void SetValue(string mark, object ob = null)
    {
        cacheDic[mark] = ob;
    }
    public static void ClearValue(string prefix, object ob = null)
    {
        var listRm = new List<string>();
        foreach (var item in cacheDic)
        {
            if (item.Key.StartsWith(prefix))
            {
                listRm.Add(item.Key);
            }
        }
        foreach (var item in listRm)
        {
            cacheDic.Remove(item);
        }
    }
}
#endif
