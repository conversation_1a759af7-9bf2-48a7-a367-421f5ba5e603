#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using UnityEditor;
    using UnityEngine;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Editor/Config")]
    public class CustomCommandTool : GlobalConfig<CustomCommandTool>
    {
      
  private const string message = "主要做一些资源校验，如：ab命名，是否有引用丢失";

        [InfoBox(message, message)] public string destriptions = "";
        [BoxGroup(CheckAudioTrackTC.TIPS_CHECK_AUDIO)]
        public CheckAudioTC mCheckAudio = new CheckAudioTC();
        [BoxGroup(CheckAudioTrackTC.TIPS_CHECK_AUDIO)]
        public CheckAudioTrackTC mCheckAudioTrack = new CheckAudioTrackTC();
        

        [BoxGroup(CheckMissComponentTC.TIPS)]
        public CheckMissComponentTC mCheckMiss = new CheckMissComponentTC();

        [BoxGroup(CheckMissAnimatorControllerTC.TIPS)]
        public CheckMissAnimatorControllerTC mCheckMissAnimatorControllerTC = new CheckMissAnimatorControllerTC();
        [BoxGroup(CheckCharacterErrorAbnameTC.TIPS)]
        public CheckCharacterErrorAbnameTC mCheckCharacterErrorAbnameTC = new CheckCharacterErrorAbnameTC();
        [BoxGroup(Slice9ToolTC.TIPS)]
        public Slice9ToolTC mSlice9ToolTC = new Slice9ToolTC();
        [BoxGroup(ResizeTexToolTC.TIPS)]
        public ResizeTexToolTC mResizeTexToolTC = new ResizeTexToolTC();
        [BoxGroup(CheckMaterialTexToolTC.TIPS)]
        public CheckMaterialTexToolTC mCheckMaterialTexToolTC = new CheckMaterialTexToolTC();

        [BoxGroup(Adjust2OneAtlasTC.TIPS)]
        [BoxGroup(Adjust2OneAtlasTC.TIPS)] public Adjust2OneAtlasTC mAdjust2OneAtlasTC = new Adjust2OneAtlasTC();
        [BoxGroup(CheckFeedbackTC.TIPS)] public CheckFeedbackTC mCheckFeedbackTC = new CheckFeedbackTC();
        [BoxGroup(AtlasFilter.TIPS)] public AtlasFilter mAtlasFilter = new AtlasFilter();
        
        [BoxGroup(CatchLog.TIPS)] public CatchLog mCatchLog = new CatchLog();
    }
    
}
#endif
