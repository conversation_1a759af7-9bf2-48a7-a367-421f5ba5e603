#if UNITY_EDITOR
using Sirenix.OdinInspector.Demos;
using Sirenix.OdinInspector;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class FileSizeConfigTC {
        //[ShowInInspector]
        [LabelText("update.json tmp path:")]
        public string UpdatePath
        {
            get
            {
                return FileSizeMgr.UpdatePath;
            }
            set
            {
                FileSizeMgr.UpdatePath = value;
            }
        }
        [ShowInInspector]
        [LabelText("update.json url:")]
        public string UpdateUrl
        {
            get
            {
                return FileSizeMgr.UpdateUrl;
            }
            set
            {
                FileSizeMgr.UpdateUrl = value;
            }
        }
        //[ShowInInspector]
        [LabelText("file.txt tmp path:")]
        public string filePath
        {
            get
            {
                return FileSizeMgr.filePath;
            }
            set
            {
                FileSizeMgr.filePath = value;
            }
        }
        [But<PERSON>("修改后请点击刷新")]
        void Reset()
        {
            bool change = ExtractScriptTool.Instance.CheckChangeUpdateUrl();
            if (!change)
            {
                return;   
            }
            FileSizeMgr.Reset();
            UnityEngine.Debug.Log($"刷新完成，当前url: [{UpdateUrl}] ");
        }
    }
}
#endif
