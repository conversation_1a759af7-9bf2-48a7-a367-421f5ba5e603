#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.Reflection;
    using System.IO;
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class FindScrTool : GlobalConfig<FindScrTool>
    {
        [Serializable]
        public class StrProto
        {

            [HideLabel]
            [HorizontalGroup]
            public string go;
            [HideLabel]
            [HorizontalGroup]
            public string path;
            [HideLabel]
            [HorizontalGroup]
            public string tpath;

            [HorizontalGroup("2")]
            [Button]
            void Locate()
            {
                var ass = ToolUti.LocateGameObject(go, path);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }
            [HorizontalGroup("2")]
            [Button]
            void SelectTarget()
            {
                var sel = Selection.activeGameObject;
                if (sel)
                {
                    var t = sel.transform as RectTransform;
                    if (t)
                    {
                        var rootPath = Path.GetFileNameWithoutExtension(go);

                        var root = GameObject.Find(rootPath);

                        tpath = ToolUti.CopyPathTo(t, root ? root.transform : t.root);

                    }
                }
            }
        }
        [InfoBox("查找UI物体引用脚本情况")]
        [Tooltip("标记,作为打印使用")]
        public string mark;
        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/UI",
        };
        [Tooltip("要查找的脚本名")]
        [OnValueChanged("OnFind")]
        public string scr;
        [Tooltip("要查找的脚本名")]
        public string scrfind;

        void OnFind()
        {

            var t = typeof(SpriteMask).Assembly.GetType(scr, false, true);
            if (t != null)
            {
                scrfind = t.FullName;
            }
            else
            {
                //scrfind = typeof(SpriteMask).FullName;
            }
        }

        public List<StrProto> list = new List<StrProto>();

        [Button("查找所有引用")]
        void Find()
        {
            list.Clear();
            var t = typeof(SpriteMask);
            var finds = AssetDatabase.FindAssets("t:GameObject", findassets.ToArray());
            ToolUti.Iter(finds, (f) =>
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(f);
                var g = AssetDatabase.LoadMainAssetAtPath(assetPath) as GameObject;
                if (g)
                {
                    var ls = g.GetComponentsInChildren(t, true);
                    foreach (var sc in ls)
                    {
                        list.Add(new StrProto()
                        {
                            go = assetPath,
                            path = ToolUti.CopyPathTo(sc.transform, sc.transform.root),
                        });
                    }
                }
                return assetPath;
            });

        }

        [Button("打印到日志框")]
        void PrintBoard()
        {
            //blackboard += string.Format("{0}:{1}=>{2}\n", mark, hexString1, intValue);
        }
        [TextArea]
        public string blackboard = "";
    }

}
#endif
