#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using UnityEngine.UI;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using Newtonsoft.Json;
    using System.Linq;
    using System.IO;
    using UnityEngine.U2D;
    using UnityEditor.Sprites;
    using UnityEditor.Callbacks;
    using System.Text;
    using System.Text.RegularExpressions;
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]

    // 找出Assets/UI下所有的大图，找到大图引用的预制体，Image修改为RawImage, 修改图片textureType为Default
    public class BigImageToRawImage : GlobalConfig<BigImageToRawImage>
    {
        [Serializable]
        public class Proto { }

        [Serializable]
        public class StrProto :Proto{

            [HideLabel]
            [HorizontalGroup]
            public string path;

            //[HideLabel]
            //[HorizontalGroup]
            //public UnityEngine.Object obj;
            // 大图片被引用的预制体
            [VerticalGroup]
            public List<UnityEngine.Object> uis;

            [HorizontalGroup(50)]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(path);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }


            [HorizontalGroup(100)]
            [Button]
            void ToRawImage()
            {
                Debug.Log("修改图片>>>>>>>>>>>>>>>>>>" + path);
                for (int i = 0, uiCount = uis.Count; i < uiCount; i++)
                {
                    var s = uis[i];
                    if (s is GameObject)
                    {
                        // 显示在Canvans中
                        Ondo((GameObject)s, path);
                        // 替换预制体中Image为RawImage
                        ChangeToRawImage(path, (GameObject)s);
                        Debug.Log("修改图片完成>>>>>>>>>>>>>>>>>>");
                    }
                    else
                    {
                        Debug.LogError("不是预制体");
                    }
                }
            }

            
            
        }
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase)|| s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);

        private static List<GameObject> glist = new List<GameObject>();
        private static void Ondo(GameObject prefab, string path)
        {
            glist.Clear();
            Texture sp = AssetDatabase.LoadAssetAtPath(path, typeof(Texture2D)) as Texture2D;

            var parentC = GameObject.FindObjectOfType<UnityEngine.Canvas>();
            if (null == parentC)
            {
                EditorUtility.DisplayDialog("提示", "请打开ui场景在进行处理", "ok", "cancel");
                return;
            }
            Transform parent = parentC.transform;


            GameObject PrefabObject = GameObject.Find(prefab.name);
            if (null == PrefabObject)
            {
                PrefabObject = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            }


            PrefabObject.transform.SetParent(parent, false);

            if (null == PrefabObject) { return; }
            Component[] tras = PrefabObject.GetComponentsInChildren(typeof(Transform), true);
            foreach (Component com in tras)
            {

                var comTf = com as Transform;

                Graphic ig = comTf.GetComponent<Graphic>();
                SpriteRenderer spr = comTf.GetComponent<SpriteRenderer>();
                Button btn = comTf.GetComponent<Button>();
                Toggle tog = comTf.GetComponent<Toggle>();

                if (null != ig)
                {
                    if (ig.mainTexture == sp)
                    {
                        glist.Add(comTf.gameObject); continue;
                    }
                }
                if (null != spr)
                {
                    if (CompareSpriteAndTex(spr.sprite, sp))
                    {
                        glist.Add(comTf.gameObject); continue;
                    }
                }
                if (null != btn)
                {
                    SpriteState state = btn.spriteState;
                    if (CompareSpriteAndTex(state.disabledSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                    if (CompareSpriteAndTex(state.highlightedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                    if (CompareSpriteAndTex(state.pressedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                }
                if (null != tog)
                {
                    SpriteState state = tog.spriteState;
                    if (CompareSpriteAndTex(state.disabledSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                    if (CompareSpriteAndTex(state.highlightedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                    if (CompareSpriteAndTex(state.pressedSprite, sp)) { glist.Add(comTf.gameObject); continue; }
                }
            }
        }

        private static bool CompareSpriteAndTex(Sprite s, Texture tex)
        {
            if (s == null || tex == null)
            {
                return false;
            }
            return s.texture == tex;
        }


        private static void ChangeToRawImage(string path, GameObject s)
        { 
            Texture sp = AssetDatabase.LoadAssetAtPath(path, typeof(Texture2D)) as Texture2D;
            foreach (GameObject go in glist)
            {
                var image = go.GetComponent<Image>();
                if (image == null)
                {
                    Debug.LogError("修改图片失败" + s.name + "--" + go.name + ", texturePath:" + path);
                    return;
                }
                var ray = image.raycastTarget;
                var c = image.color;
                if (image)
                {
                    GameObject.DestroyImmediate(image);
                }
                var ri = go.AddComponent<RawImage>();
                ri.raycastTarget = ray;
                ri.color = c;
                ri.texture = sp;
                //EditorUtility.SetDirty(go);
                var prefabPath = AssetDatabase.GetAssetPath(go);
                var newPrefab = PrefabUtility.InstantiatePrefab(go) as GameObject;
                //PrefabUtility.ApplyPrefabInstance(newPrefab, InteractionMode.UserAction);
                //PrefabUtility.SaveAsPrefabAsset(newPrefab, prefabPath, out isSuccess);
                Debug.Log("修改预制体:" + s.name + "--" + go.name);
            }

            // 修改Texture类型为Default 及相关属性
            var ai = AssetImporter.GetAtPath(path) as TextureImporter;
            ai.textureType = TextureImporterType.Default;
            ai.mipmapEnabled = false;
            //ai.npotScale = TextureImporterNPOTScale.ToNearest;
            ai.SaveAndReimport();
        }

        private string ToJson(object o)
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(o, Formatting.Indented);
        }
        private string CheckIsBig(string path)
        {
            var ti = AssetDatabase.LoadAssetAtPath<Texture2D>(path) as Texture2D;
            if (ti != null)
            {
                var b = Mathf.Max(ti.width, ti.height) > sizeThreshold;
                if (b)
                {
                    var ai = AssetImporter.GetAtPath(path) as TextureImporter;
                    if(ai.textureType== TextureImporterType.Sprite)
                    {
                        return ti.width + "_" + ti.height + "_" + ai.spritePackingTag;
                    }
                }
            }
            return "";
        }


        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/UI",
        };
        public int sizeThreshold = 512;

        public List<StrProto> BigPicList = new List<StrProto>();

        [Button()]
        void CheckBigPic()
        {
            var allSet = new Dictionary<string, string>();
            var usedSet = new Dictionary<string, string>();
            var finda = AssetDatabase.FindAssets("", findassets.ToArray());
            EditorApplication.update = delegate ()
            {
                try
                {
                    BigPicList = new List<StrProto>();
                    int count = finda.Length;
                    for (int i = 0; i < count/*finda.Length*/; i++)
                    {
                        string uid = finda[i];
                        var ob = AssetDatabase.GUIDToAssetPath(uid);
                        bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", ob, (float)i / (float)finda.Length);

                        if (isPng(ob))
                        {
                            string tInfo = CheckIsBig(ob);
                            if (tInfo != "")
                            {
                                allSet[ob] = tInfo;
                            }
                        }
                        else
                        {
                            var dependences = AssetDatabase.GetDependencies(ob, true);
                            foreach (var dep in dependences)
                            {
                                if (isPng(dep))
                                {
                                    string tInfo = CheckIsBig(dep);
                                    if (tInfo != "")
                                    {
                                        if(!usedSet.ContainsKey(dep))
                                        {
                                            // 获取图片所有被引用的预制体
                                            Texture m_Sprite = AssetDatabase.LoadAssetAtPath(dep, typeof(Texture2D)) as Texture2D;
                                            List<UnityEngine.Object> ui = FindPlayerUseImage(m_Sprite);
                                            if (ui.Count > 0 )
                                            {
                                                BigPicList.Add(new StrProto() { path = dep, uis = ui });
                                            }
                                            usedSet[dep] = tInfo;
                                        }                                     
                                    }
                                }
                            }
                        }

                        if (isCancel || i >= finda.Length - 1)
                        {
                            EditorUtility.ClearProgressBar();
                            EditorApplication.update = null;
                            Debug.Log("匹配结束");
                            return;
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError(e.ToString());
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                }

            };
        }

        private List<UnityEngine.Object> FindPlayerUseImage(Texture sp)
        {
            List<UnityEngine.Object> ui = new List<UnityEngine.Object>();
            var paths = Directory.GetFiles(Application.dataPath + "/UI", "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab") || s.EndsWith(".asset")).ToArray();
            var spPath = AssetDatabase.GetAssetPath(sp);
            for (int i = 0; i < paths.Length; i++)
            {
 
                string path = paths[i].Replace('\\', '/');
                path = path.Substring(path.IndexOf("Assets/"));

                UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(UnityEngine.Object));
                var PrefabObject = prefab as UnityEngine.Object;
                if (null == PrefabObject) { continue; }

                var findassets = AssetDatabase.GetDependencies(path);
                foreach (var item in findassets)
                {
                    if (item == spPath)
                    {
                        ui.Add(PrefabObject); break;
                    }
                }
            }
            return ui;
        }

        [Button()]
        void AllToRawImage()
        {
            List<string> prefabsName = new List<string>();
            for (int j = 0; j < BigPicList.Count; j++)
            {
                var path = BigPicList[j].path;
                List<UnityEngine.Object> uis = BigPicList[j].uis;

                Debug.Log("修改图片>>>>>>>>>>>>>>>>>>" + path);
                for (int i = 0; i < uis.Count; i++)
                {
                    var s = BigPicList[j].uis[i];
                    if (s is GameObject)
                    {
                        // 显示在Canvans中
                        Ondo((GameObject)s, path);
                        // 替换预制体中Image为RawImage
                        ChangeToRawImage(path, (GameObject)s);
                        prefabsName.Add(s.name);
                        Debug.Log("修改图片完成>>>>>>>>>>>>>>>>>>");
                    }
                    else
                    {
                        //Debug.LogError("不是预制体");
                    }
                }
            }

            Debug.LogError(ToJson(prefabsName));

        }

    }
}
#endif
