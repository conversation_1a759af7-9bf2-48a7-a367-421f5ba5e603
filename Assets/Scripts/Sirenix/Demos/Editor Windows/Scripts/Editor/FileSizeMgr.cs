#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using War.Base;
    using System.IO;
    using UnityEditor;
    public class FileSizeMgr
    {
        private const string C_UpdatePath = "Assets/EditorConfig/FileSize/update.json";
        private const string C_UpdateUrl = "http://172.16.126.185:3010/rogue_patch2/Android/update.json";
        private const string C_filePath = "Assets/EditorConfig/FileSize/files.txt";
        public const string KEY_UPDATE_PATH = "FileSizeMgr_UpdatePath";
        public const string KEY_FILE_TXT = "FileSizeMgr_filePath";
        public const string KEY_UPATE_URL = "FileSizeMgr_UpdateUrl";

        public int TimeOut = 30;

        public static string UpdatePath
        {
            get
            {
                return EditorPrefs.GetString(KEY_UPDATE_PATH, C_UpdatePath);
            }
            set
            {
                EditorPrefs.SetString(KEY_UPDATE_PATH, value);
            }
        }
        public static string UpdateUrl
        {
            get
            {
                return EditorPrefs.GetString(KEY_UPATE_URL, C_UpdateUrl);
            }
            set
            {
                EditorPrefs.SetString(KEY_UPATE_URL, value);
            }
        }
        public static string filePath
        {
            get
            {
                return EditorPrefs.GetString(KEY_FILE_TXT, C_filePath);
            }
            set
            {
                EditorPrefs.SetString(KEY_FILE_TXT, value);
            }
        }
        static FileSizeMgr instance;

        public static FileSizeMgr Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new FileSizeMgr();
                }
                return instance;
            }
        }

        public static void Reset()
        {
            instance = null;
        }

        public bool isFilesTxt = false;

        public UpdateInfo UpdateInfo
        {
            get
            {
                if (updateInfo == null)
                {
                    string MARK = "UpdateInfo_mark";
                    var umark = EditorPrefs.GetInt(MARK, -1) == System.DateTime.Now.Second;
                    if (!umark )
                    {
                        if (ToolUti.DownloadFileW(UpdateUrl, UpdatePath, TimeOut))
                        {
                            EditorPrefs.SetInt(MARK, -1);  
                            var updatejson = File.ReadAllText(UpdatePath);
                            "".Print("updatejson", updatejson);
                            updateInfo = UIHelper.ToObj<UpdateInfo>(updatejson);
                            var FilesUrl = updateInfo.FilesUrl;
                        }
                        else
                        {
                            EditorPrefs.SetInt(MARK, System.DateTime.Now.Second);
                        }
                    }
                }
                return updateInfo;
            }
        }

        public hashCheck HashC
        {
            get
            {
                if (hashC == null)
                {
                    if (UpdateInfo != null)
                    {
                        var updateI = UpdateInfo;
                        var FilesUrl = updateI.FilesUrl;
                        if (!isFilesTxt)
                        {
                            FilesUrl = FilesUrl.Replace("files.txt", "files2.txt");
                        }

                        string MARK = "hashCheck_mark";
                        var umark = EditorPrefs.GetInt(MARK, -1) == System.DateTime.Now.Second;
                        if (!umark)
                        {
                            if (ToolUti.DownloadFileW(FilesUrl, filePath, TimeOut))
                            {
                                var Filesjson = File.ReadAllText(filePath);
                                "".Print("Filesjson", Filesjson);

                                hashC = UIHelper.ToObj<hashCheck>(Filesjson);
                            }
                            else
                            {
                                EditorPrefs.SetInt(MARK, System.DateTime.Now.Second);
                            }
                        }
                    }
                }
                return hashC;
            }

        }
        public int GetSize(string ab)
        {
            return HashC != null ? hashC.GetSize(ab) : 0;
        }


        public string GetResUrl(string assetbundleName)
        {
            if (UpdateInfo == null)
            {
                UnityEngine.Debug.LogError("GetResUrl_updateinfo is null.");
                return null;
            }
            if (HashC == null)
            {
                UnityEngine.Debug.LogError("GetResUrl_hashc is null.");
                return null;
            }
            if (hashC.list.ContainsKey(assetbundleName) == false)
            {
                UnityEngine.Debug.Log($"GetResUrl_hashC.list dont contains key:{assetbundleName}");
                return null;
            }
            var resUrl = updateInfo.ResourceUrl.Replace("$file_ver", hashC.GetVer(assetbundleName)) + assetbundleName;
            //UnityEngine.Debug.Log($"GetResUrl_resUrl:[{resUrl}]");
            return resUrl;
        }
        hashCheck hashC;
        UpdateInfo updateInfo;
    }
}
#endif
