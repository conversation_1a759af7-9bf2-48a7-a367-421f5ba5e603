#if UNITY_EDITOR
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Demos;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEditor;
using UnityEngine;
using War.Base;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    public class ToolObj
    {
    }
    [System.Serializable]
    public class GoTObj
    {
        public static List<Object> dirtyList = new List<Object>();
        public string path;
        public string subpath;
        [System.NonSerialized]
        public Object _da;
        [ShowInInspector]
        public Object da
        {
            set
            {
                var assetpath = AssetDatabase.GetAssetPath(value);
                if (string.IsNullOrEmpty(assetpath)) return;
                path = assetpath;
                var go = AssetDatabase.LoadMainAssetAtPath(assetpath);
                if (!go) return;
                subpath = ToolUti.CopyPathTo((value as GameObject).transform, (go as GameObject).transform);
            }
            get
            {
                if (_da) return _da;

                if (string.IsNullOrEmpty(subpath)) return null;
                var subgo = GameObject.Find(subpath);
                if(!subgo)
                {
                    if (string.IsNullOrEmpty(subpath)) return null;
                    var prefab = AssetDatabase.LoadMainAssetAtPath(path);
                    if(prefab)
                    {
                        var canvasRoot = GameObject.Find("/UIRoot/Canvas");
                        Canvas parentC = null;
                        if (canvasRoot)
                        {
                            parentC = canvasRoot.GetComponent<Canvas>();

                        }
                        if(!parentC)
                        {
                            parentC =  GameObject.FindObjectOfType<UnityEngine.Canvas>();
                        }
                        if (null == parentC)
                        {
                            EditorUtility.DisplayDialog("提示", "请打开ui场景在进行处理", "ok", "cancel");
                            return null;
                        }
                        Transform parent = parentC.transform;


                        var subt = parent.Find(subpath);
                        if (!subt)
                        {
                            var g = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
                            var bactive = true;
                            if (!g.activeSelf)
                            {
                                Debug.LogError("该物体默认为不显示：".Append(path).Append("|").Append(subpath), g);
                                g.SetActive(true);
                                bactive = false;
                            }

                            g.transform.SetParent(parent, false);

                            subgo = GameObject.Find(subpath);
                            if (!bactive)
                            {
                                g.SetActive(bactive);
                            }

                        }
                        else
                        {
                            subgo = subt.gameObject;
                        }

                    }
                }
                if(subgo)
                {
                    dirtyList.Add(subgo);
                }
                _da = subgo;
                return subgo;
            }
        }
    }

    [System.Serializable]
    public class AssetTObj
    {
        public string path;
        [ShowInInspector]
        public Object da
        {
            set
            {
                var _path = AssetDatabase.GetAssetPath(value);
                path = _path;
            }
            get
            {
                var ob = AssetDatabase.LoadMainAssetAtPath(path);
                return ob;
            }
        }
    }
    [System.Serializable]
    public class ABTObj
    {
        public string name;
        [ShowInInspector]
        public int size
        {
            get
            {
                var ob = FileSizeMgr.Instance.GetSize(name);
                return ob;
            }
        }
    }

    [System.Serializable]
    public class MarkAssetsTObj
    {
        public string mark = "";
        [ShowInInspector]
        [UnityEngine.SerializeField]
        public List<AssetTObj> listAssets = new List<AssetTObj>();
    }

    [System.Serializable]
    public class CheckABTc
    {
        [PropertyOrder(0)]
        [ReadOnly]
        public string Tips = "遍历检查Abname,是否有空格";

        [ShowInInspector]
        public List<MarkAssetsTObj> display = new List<MarkAssetsTObj>();
        [Button]
        void Check()
        {
            CheckABExe(out display);
        }
        public static bool CheckABExe(out List<MarkAssetsTObj> display)
        {
            display = new List<MarkAssetsTObj>();
            AssetDatabase.RemoveUnusedAssetBundleNames();
            var abs = AssetDatabase.GetAllAssetBundleNames();
            foreach (var ab in abs)
            {
                if (ab.Contains(" "))
                {
                    MarkAssetsTObj item = new MarkAssetsTObj()
                    {
                        mark = ab,
                    };
                    var assets = AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                    item.listAssets = assets.ToList().ConvertAll((a) =>
                    {
                        return new AssetTObj() { path = a };
                    });
                    display.Add(item);
                }
            }
            return display.Count > 0;
        }

        [InitializeOnLoadMethod]
        public static void StaticCheckABExe()
        {
            "".Print("StaticCheckABExe");

            var display = new List<MarkAssetsTObj>();
            if(CheckABExe(out display))
            {
                //var ll = display.ConvertAll((mp) =>
                // {
                //     var lstr = string.Join("\n\t", mp.listAssets.ConvertAll((p) => p.path).ToArray());

                //     return string.Format("abname:\n{0} ,\ndeps:\n{1}", mp.mark, lstr);
                // });
                //var llstr = string.Join("\n", ll.ToArray());
                //"".Print("StaticCheckABExe Count",display.Count,"\n",llstr);
                //"".Print("StaticCheckABExe Count", UIHelper.ToJson(display));
                var count = 0;
                var list = new List<string>();
                foreach (var l in display)
                {
                    foreach (var ll in l.listAssets)
                    {
                        count++;
                        list.Add(ll.path);
                    }
                }
                if(count>0)
                {
                    "".PrintError("CHECK_AB:", UIHelper.ToJson(list));
                    foreach (var item in list)
                    {
                        Debug.LogError(item,AssetDatabase.LoadMainAssetAtPath(item));
                    }

                    EditorUtility.DisplayDialog("资源命名检查", string.Format("检查到{0}个资源命名异常，详情看日志窗口",count),"ok");

                }
            }

            // 检查中文
            var findlist = new List<string>();

            AssetDatabase.RemoveUnusedAssetBundleNames();
            var abs = AssetDatabase.GetAllAssetBundleNames();
            ToolUti.Iter(abs, (f) =>
            {
                for (int i = 0; i < f.Length; i++)
                {
                    if(IsChineseLetter(f,i))
                    {
                        findlist.Add(f);
                        break;
                    }
                }
                return f;
            }, () =>
            {
                foreach (var f in findlist)
                {
                    var ass = AssetDatabase.GetAssetPathsFromAssetBundle(f);
                    if(ass.Length>0)
                    {
                        var a = AssetDatabase.LoadMainAssetAtPath(ass[0].Replace(Application.dataPath, "Assets/"));
                        Debug.LogError(f, a);
                    }
                }
            }, "检查中文");

        }

        static int chfrom = System.Convert.ToInt32("4e00", 16);    //范围（0x4e00～0x9fff）转换成int（chfrom～chend）
        static int chend = System.Convert.ToInt32("9fff", 16);
        public static bool IsChineseLetter(string input, int index)
        {
            int code = 0;
            if (input != "")
            {
                code = System.Char.ConvertToUtf32(input, index);    //获得字符串input中指定索引index处字符unicode编码

                if (code >= chfrom && code <= chend)
                {
                    return true;     //当code在中文范围内返回true

                }
                else
                {
                    return false;    //当code不在中文范围内返回false
                }
            }
            return false;
        }
    }


    [System.Serializable]
    public class AssetDependenceTc
    {
        [PropertyOrder(0)]
        [ReadOnly]
        public string Tips = "拖拉资源获得所有依赖项";

        public PackRes assets = new PackRes();

        public List<string> dependences = new List<string>();
        public List<string> ablist = new List<string>();
        [Button]
        void Fill()
        {
            var dic = new Dictionary<string, bool>();
            foreach (var a in assets.list)
            {
                var deps = AssetDatabase.GetDependencies(a.path);
                foreach (var d in deps)
                {
                    dic[d] = true;
                }
            }
            dependences = new List<string>(dic.Keys);
            assets.Exe((hs) =>
            {
                ablist = new List<string>(hs);
            }, false);
        }
    } 

    [System.Serializable]
    public class ABIterationTc
    {
        [PropertyOrder(0)]
        [ReadOnly]
        public string Tips = "所有ABName检索";
        public bool replace = true;

        [ValueDropdown("SearchL")]
        [OnValueChanged("OnSearch")]
        public string search;
        public List<string> searches;
        public List<string> excludes = new List<string>();

        IEnumerable<string> SearchL()
        {
            return AssetDatabase.GetAllAssetBundleNames();
        }
        void OnSearch()
        {
            if(replace)
            {
                searches.Clear();
                searches.Add(search);
            }
            else
            {
                if(searches.Contains(search))
                {
                    //return;
                }
                else
                {
                    searches.Add(search);
                }
            }
            var dic = new HashSet<string>();
                deplist = new List<ABTObj>();
            foreach (var search in searches)
            {
                var deps = AssetDatabase.GetAssetBundleDependencies(search, true);
                foreach (var dep in deps)
                {
                    if (dic.Contains(dep)) continue;
                    if (excludes.Contains(dep)) continue;
                    dic.Add(dep);
                    deplist.Add(new ABTObj()
                    {
                        name = dep
                    });
                }

            }
            deplist.Sort((a1, a2) =>
            {
                return a2.size - a1.size;
            });
        }
        //[System.NonSerialized]
        [ShowInInspector]
        [ShowIf("IfSearch")]
        public List<ABTObj> deplist;
        bool IfSearch()
        {
            return !string.IsNullOrEmpty(search);
        }
        [ShowInInspector]
        public int total
        {
            get
            {
                var t = 0;
                if(deplist!=null)
                foreach (var a in deplist)
                {
                    t += FileSizeMgr.Instance.GetSize(a.name);
                }
                return t;
            }
        }
    }
    #region PackList

    [System.Serializable]
    public class PackOneRes
    {
        public string path;

        [ShowInInspector]
        public Object da
        {
            set
            {
                var _path = AssetDatabase.GetAssetPath(value);
                path = _path;
            }
            get
            {
                var ob = AssetDatabase.LoadMainAssetAtPath(path);
                return ob;
            }
        }
    }
    [System.Serializable]
    public class PackRes
    {
        [OnValueChanged("ClearCache")]
        [ShowInInspector]
        public bool useCache
        {
            get
            {
                return EditorPrefs.GetBool("ResModuleTool_useCache", false);
            }
            set
            {
                EditorPrefs.SetBool("ResModuleTool_useCache", value);
            }
        }
        public bool includeDependences = false;
        public List<PackOneRes> list = new List<PackOneRes>();
        [OnValueChanged("MulAdd")]
        [LabelText("多数量拖拉添加")]
        public List<Object> _Empty = new List<Object>();

        public PackRes(bool _includeDependences = false)
        {
            includeDependences = _includeDependences;
        }

        void MulAdd()
        {
            foreach (var v in _Empty)
            {
                var _path = AssetDatabase.GetAssetPath(v);
                list.Add(new PackOneRes()
                {
                    path = _path
                });

            }
            _Empty.Clear();
        }

        public void Exe(System.Action<HashSet<string>> cb, bool sync = false)
        {
            var dic = new Dictionary<string, string>();
            var useCache = EditorPrefs.GetBool("ResModuleTool_useCache", false);

            System.Action<string> addAssets = (path) =>
            {
                var p = path.Replace("\\", "/");
                dic[p] = "";
            };

            foreach (var por in list)
            {
                var path = por.path;
                if (System.IO.Directory.Exists(por.path))
                {
                    var files = System.IO.Directory.GetFiles(path, "*.*", System.IO.SearchOption.AllDirectories);
                    foreach (var f in files)
                    {
                        if (f.EndsWith(".meta")) continue;
                        addAssets(f);
                    }
                }
                else
                {
                    addAssets(path);
                }
            }
            var listAb = new List<string>(dic.Keys);
            this.Print(UIHelper.ToJson(listAb));
            var dic2 = new Dictionary<string, string>();


            var abs = AssetDatabase.GetAllAssetBundleNames();
            var cacheDic = new Dictionary<string, string[]>();
            LogHelpBase.LogMessage("ResModuleTool-start");

            System.Func<string, string> ac = (ab) =>
            {
                string[] paths = null;
                if (useCache)
                {
                    paths = (string[])(ToolUti.GetValue("ResModuleTool_CacheDic" + ab)) ?? AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                    ToolUti.SetValue("ResModuleTool_CacheDic" + ab, paths);
                }
                else
                {
                    paths = AssetDatabase.GetAssetPathsFromAssetBundle(ab);
                }
                foreach (var p in paths)
                {
                    if (dic.ContainsKey(p))
                    {
                        dic2[ab] = "";
                        if (includeDependences)
                        {
                            string[] abdeps = null;
                            if (useCache)
                            {
                                abdeps = (string[])(ToolUti.GetValue("ResModuleTool_DepsCacheDic" + ab)) ?? AssetDatabase.GetAssetBundleDependencies(ab, true);
                                ToolUti.SetValue("ResModuleTool_DepsCacheDic" + ab, abdeps);
                            }
                            else
                            {
                                abdeps = AssetDatabase.GetAssetBundleDependencies(ab, true);
                            }
                            foreach (var dep in abdeps)
                            {
                                dic2[dep] = "";
                            }
                        }
                        break;
                    }
                }
                return ab;

            };
            System.Action finish = () =>
            {
                cb(new HashSet<string>(dic2.Keys));
                LogHelpBase.LogMessage("ResModuleTool-end");
            };

            if (!sync)
            {
                ToolUti.Iter(abs, ac, finish);
            }
            else
            {
                foreach (var ab in abs)
                {
                    ac(ab);
                }
                finish();
            }

            //this.Print(UIHelper.ToJson(dic2.Keys));
        }

        [FoldoutGroup("State")]
        [Button("导出到粘贴板")]
        public void ExportClipBoard()
        {
            var str = "";
            foreach (var l in list)
            {
                str = string.Concat(str, l.path, "\n");
            }
            LogHelp.clipboard = str;
            this.Print("已经添加到粘贴板");
        }
        [FoldoutGroup("State")]
        [Button("导出Abs到粘贴板")]
        public void ExportAbsClipBoard()
        {
            Exe((hs) =>
            {
            var str = new StringBuilder();
                foreach (var l in hs)
                {
                    str.Append(l);
                    str.Append( "\n");
                }
                LogHelp.clipboard = str.ToString();
            }, false);

            this.Print("已经添加到粘贴板");
        }
        [FoldoutGroup("State")]
        [Button("从粘贴板导入")]
        public void ImportClipBoard()
        {
            var str = LogHelp.clipboard;
            var listA = str.Normalize().Split(' ');
            var pkr = new PackRes();
            foreach (var l in listA)
            {
                list.Add(new PackOneRes()
                {
                    path = l
                });
            }
        }
        [FoldoutGroup("State")]
        void ClearCache()
        {
            ToolUti.ClearValue("ResModuleTool_CacheDic", null);
            ToolUti.ClearValue("ResModuleTool_DepsCacheDic", null);
        }
    }

#endregion
}

#endif