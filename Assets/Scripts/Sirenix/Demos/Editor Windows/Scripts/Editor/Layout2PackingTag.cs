#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class Layout2PackingTag : GlobalConfig<Layout2PackingTag>
    {

        [Serializable]
        public class LayoutProto
        {
            public string layout;
            public List<AtlasProto> list;
        }
        [Serializable]
        public class AtlasProto
        {
            public string atlas;
            public List<StrProto> list;
        }

        [Serializable]
        public class StrProto
        {

            [HideLabel]
            [HorizontalGroup]
            public string name;
            [HorizontalGroup]
            public string spritePackingTag;
            [HorizontalGroup("2")]
            [HideLabel]
            public int filterMode;

            [HorizontalGroup]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }
            public static StrProto Parse(string path)
            {
                var sp = new StrProto();
                var ai = AssetImporter.GetAtPath(path) as TextureImporter;
                sp.name = path;
                if (ai)
                {
                    sp.spritePackingTag = ai.textureType == TextureImporterType.Sprite ? ai.spritePackingTag : string.Empty;
                    if (string.IsNullOrEmpty(sp.spritePackingTag))
                    {
                        sp.spritePackingTag = path;
                    }
                    sp.filterMode = (int)ai.filterMode;
                }
                return sp;
            }
        }
        [Sirenix.OdinInspector.FilePath] 
        public List<string> assetPaths = new List<string>{
            "Assets/UI",
            "Assets/Art/Maps"
        };

        public List<LayoutProto> SpriteList;

        //[OnValueChanged("OnFilter")]
        [HorizontalGroup("3")]
        public string filter;
        [HorizontalGroup("3")]
        [Button()]
        void OnFilter()
        {
            if (string.IsNullOrEmpty(filter)) return;
            var _filter = filter.ToLower();
            FilterSpriteList = SpriteList.FindAll((s) => s.layout.ToLower().Contains(_filter));
        }

        [TextArea]
        public string blackboard = "";

        public List<LayoutProto> FilterSpriteList;
        [Button()]
        public void FindUsed()
        {
            SpriteList.Clear();
            var findassets = AssetDatabase.FindAssets("t:GameObject", assetPaths.ToArray());


            ToolUti.Iter(findassets, (gid) =>
            {
                var path = AssetDatabase.GUIDToAssetPath(gid);
                var go = AssetDatabase.LoadMainAssetAtPath(path) as GameObject;
                if (go)
                {
                    var lp = new LayoutProto()
                    {
                        layout = path,
                    };
                    var l = ToolUti.Layout2PackingTags(path);
                    var dic = new Dictionary<string, List<StrProto>>();

                    foreach (var p in l)
                    {
                        var sp = StrProto.Parse(p);
                        List<StrProto> lsp = null;
                        dic.TryGetValue(sp.spritePackingTag, out lsp);
                        if (lsp == null)
                        {
                            dic[sp.spritePackingTag] = new List<StrProto>();
                            lsp = dic[sp.spritePackingTag];
                        }
                        lsp.Add(sp);
                    }
                    var lout = new LayoutProto()
                    {
                        layout = path,
                        list = new List<AtlasProto>(),
                    };
                    SpriteList.Add(lout);

                    foreach (var item in dic.Keys)
                    {
                        lout.list.Add(new AtlasProto()
                        {
                            atlas = item,
                            list = dic[item],
                        });
                    }
                }
                return path;
            }, () => {
                SpriteList.Sort((l1, l2) => l2.list.Count - l1.list.Count);
            });

        }

        [Button("output filter data")]
        void PrintBoard()
        {
            blackboard = "预制体\t贴图列表\t图集列表\t图集详情\n";
            for (int i = 0; i < FilterSpriteList.Count; i++)
            {
                //图集，贴图名字
                bool isAtlas = false;
                string spriteListStr = "";
                string textrueListStr = "";
                LayoutProto item = FilterSpriteList[i];
                if(item == null)
                {
                    continue;
                }

                string prefabName = item.layout;
                List<String> atlasSpriteInfos = new List<string>();
                //遍历预制体依赖的资源
                foreach(AtlasProto atlasProtoItem in item.list)
                {
                    
                    isAtlas = false;
                    if(atlasProtoItem.list.Count > 0)
                    {
                        var ai = AssetImporter.GetAtPath(atlasProtoItem.list[0].name) as TextureImporter;
                        if(ai)
                        {
                            if (ai.textureType == TextureImporterType.Sprite && ai.spritePackingTag != string.Empty)
                            {
                                isAtlas = true;
                            }
                            //Debug.Log("sprite!!!!!!!!!!!" + atlasProtoItem.list[0].name + "--" + ai.textureType + "--" + TextureImporterType.Sprite);
                        }
                    }

                    string sizeStr = "";
                    if (isAtlas)
                    {
                        spriteListStr += atlasProtoItem.atlas + "\n";

                        string spriteNamsStr = atlasProtoItem.atlas + "\n";
                        //遍历图集精灵信息
                        foreach(StrProto spriteItem in atlasProtoItem.list)
                        {
                            //图集sprite优化建议：256*256=65536 总像素大于256*256的需要优化
                            sizeStr = GetTextureSizeData(spriteItem.name, 65536);
                            spriteNamsStr += spriteItem.name + " " + sizeStr + "\n";
                        }
                        spriteNamsStr = "\"" + spriteNamsStr + "\"";
                        atlasSpriteInfos.Add(spriteNamsStr);
                    }
                    else
                    {
                        //var ai = AssetImporter.GetAtPath(atlasProtoItem.atlas) as TextureImporter;
                        //单张贴图优化建议： 512*512=262144 总像素大于512*512的需要优化
                        sizeStr = GetTextureSizeData(atlasProtoItem.atlas, 262144, true);
                        textrueListStr += atlasProtoItem.atlas + " " + sizeStr + "\n";
                    }
                }
                spriteListStr = "\"" + spriteListStr + "\"";
                textrueListStr = "\"" + textrueListStr + "\"";
                blackboard += string.Format("{0}\t{1}\t{2}\t", prefabName, textrueListStr, spriteListStr);
                //添加图集精灵依赖信息
                foreach(string atlasSpriteStr in atlasSpriteInfos)
                {
                    blackboard += atlasSpriteStr + "\t";
                }
                blackboard += "\n";
            }

            Debug.Log(blackboard);
        }

        private string GetTextureSizeData(string path, double warningPixLength, bool hasEctData = false)
        {
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(path);
            if(texture)
            {
                double allPixLength = texture.width * texture.height;
                bool isNeedFix = allPixLength > warningPixLength; //256*256=65536 总像素大于256*256的需要优化
                string fixTag =  isNeedFix ? "X" : "O";

                string output = string.Format("-{0}*{1} -size>{2}",texture.width, texture.height, fixTag);
                if (hasEctData)
                {
                    //检测etc格式条件，宽高是否可以被4整除
                    var fixedWidth = (int)(Mathf.RoundToInt (texture.width /4)*4); 
				    var fixedHeight = (int)(Mathf.RoundToInt (texture.height /4)*4);
                    bool canUseEtc = fixedWidth == texture.width && fixedHeight == texture.height;
                    string etcTag = canUseEtc ? "O" : "X";
                    output += " -Ect>" + etcTag;
                }
                return output;

            }
            return "";
        }


    }
}
#endif
