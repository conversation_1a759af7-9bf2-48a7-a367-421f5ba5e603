#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj;
    using Sirenix.Utilities;
    using System.Collections;
    using System.Collections.Generic;
    using System.IO;
    using System.Text.RegularExpressions;
    using UnityEditor;
    using UnityEditor.PackageManager.Requests;
    using UnityEngine;
    using UnityEngine.WSA;
    using War.Common;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ExtractScriptTool : GlobalConfig<ExtractScriptTool>
    {
        [Title("解析lua步骤： ")]
        [Title("1.借助多开脚本【生成多开项目_extract.bat】创建一个[Client_extract_project]工程，建议在这个简易工程下进行ExtractScript操作(比普通全量工程快)")]
        [Title("2.在update.json url中填入当前目标的update.json路径,例如：http://**************:3116/rogue_pc_zhero_ljc/Android/update.json")]
        [Title("3.点击“修改后请点击刷新”按钮")]
        [Title("4.在ServerMark中写入热更标记,打包提交时作为前缀,最后生成{ServerMark}_luascript.zip,用来区分不同包资源;可不修改")]
        [Title("5.点击“ 开始解压All ”按钮,解压luascript.zip和所有的Exec资源到Assets/LuaVice文件夹")]
        [Title("6.点击“ 开始打包提交Server ”按钮,将修改后的lua打包成luascript.zip发送到指定ftp")]


        [FolderPath]
        [ReadOnly]
        [System.NonSerialized]
        public string output = "Assets/LuaVice";
        [ReadOnly]
        public bool isUseNewExtractExec = true;

        bool isOpt = true;  //使用新方法import

        #region luascript

        [ShowInInspector]
        [FoldoutGroup("Set the update config")]
        [LabelText("ServerMark(热更标记,默认不修改)")]
        public string ServerMark
        {
            get
            {
                return SelectBuilds.Instance.ServerMark;
            }
            set
            {
                SelectBuilds.Instance.ServerMark = value;
            }
        }

        [ShowInInspector]
        [FoldoutGroup("Set the update config")]
        [LabelText("IsFilesTxt(是否使用files.txt，默认为files2.txt)")]
        public bool IsFilesTxt { get {
                return FileSizeMgr.Instance.isFilesTxt;
            } 
            set { 
                if (FileSizeMgr.Instance.isFilesTxt != value)
                {
                    FileSizeMgr.Reset();
                    FileSizeMgr.Instance.isFilesTxt = value;
                }

            }
        }
        private int TimeOut = 30;

        [FoldoutGroup("Set the update config")]
        [Title("设置update.json配置")]
        public FileSizeConfigTC mFileSizeConfigTC = new FileSizeConfigTC();

        public bool CheckChangeUpdateUrl() {
            var isEmpty = string.IsNullOrEmpty(output);
            if ((!isEmpty) && Directory.Exists(output))  //如果有切换地址，且有luaVice目录，里面有文件则做解压提示，会先清理再下载
            {
                var files = Directory.GetFiles(output);
                if (files.Length > 0)
                {
                    int selectCode = EditorUtility.DisplayDialogComplex("提示", "本地有之前拉取的luaVice代码，确认切换会先【删除本地luaVice目录】，确认要切换吗？", "确认", "取消", "");
                    if (selectCode == 0)
                    {
                        Directory.Delete(output, true);
                    }
                    else
                        return false;
                }
            }
            if(!Directory.Exists(output))
            {
                Directory.CreateDirectory(output);
            }
            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
            return true;
        }

        [FoldoutGroup("Set the update config")]
        [Button("开始解压All")]
        void ExtractAll()
        {
            LogHelp.Instance.Log($"ExtractAll start..");
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();

            if (string.IsNullOrEmpty(output) || Path.GetDirectoryName(output) == "Assets")
            {

            }
            else
            {
                if (Directory.Exists(output))
                {
                    Directory.Delete(output, true);
                }
            }

            Extract();
            ExtractExec();
            UpdateFileUids();   //解压完后会生成一份基础文件列表缓存

            sw.Stop();
            Debug.Log($"ExtractAll_use_time:{sw.Elapsed.TotalSeconds}");
        }

        void UpdateFileUids() {
            LogHelp.Instance.Log($"ExtractAll ex..");

            var f = new FileUids();
            f.UpdateFolder(output);
            f.Save();

            LogHelp.Instance.Log($"ExtractAll end..");
        }

        //[Button("检查fixscr")]
        void InjectFixscr() {
            SelectBuilds.Instance.InjectFixscr();
        }

        [FoldoutGroup("Set the update config")]
        [Button("开始打包提交Server")]
        void buildSendFtp()
        {
            var isEmpty = string.IsNullOrEmpty(output);
            if (!isEmpty && Directory.Exists(output)) 
            {
                var files = Directory.GetFiles(output);
                if(files.Length == 0) {
                    Debug.LogError($"[{output}] dont have files.");
                    return;
                }
            }
            else {
                Debug.LogError($"dont exist directory:{output}");
                return;
            }
            
            BuildTarget buildTarget = GetBuildTargetByUrl(mFileSizeConfigTC.UpdateUrl);
            Debug.Log($" UpdateUrl:[{mFileSizeConfigTC.UpdateUrl}], targetPlat:[{buildTarget.ToString()}]");
            SelectBuilds.Instance.build(buildTarget);
        }

        //https://q1doc.yuque.com/kiob3t/gynxh4/phwgm63xevk48lbo#Hedp  参考解析关键字对应的渠道
        BuildTarget GetBuildTargetByUrl(string url) {
            BuildTarget buildTarget = EditorUserBuildSettings.activeBuildTarget;
            var strLower = url.ToLower();
            if (strLower.Contains("ios"))
            {
                buildTarget = BuildTarget.iOS;
            }
            else if (strLower.Contains("android"))
            {
                buildTarget = BuildTarget.Android;
            }
            else {
                Debug.LogError($"【error】未解析成功，回退为当前平台，地址发到渠道程序:{url}");
            }
            return buildTarget;
        }

        // [FoldoutGroup("Luascript")]
        // [Title("解压luascript工具")]
        // [Title(". LuaScriptZipPath表示指定要解压缩的文件路径/网络上luascript.zip链接,可选，默认解压updateconfig指定路径")]
        // [Tooltip("指定要解压缩的文件路径/网络上luascript.zip链接,可选，默认解压updateconfig指定路径")]
        // [LabelText("LuaScriptZipPath")]
        //[Sirenix.OdinInspector.FilePath] 
        private string luascriptZipPath;

        // [FoldoutGroup("Luascript")]
        // [Title(". 解压后的lua目录")]
        private List<string> luas = new List<string>();
        // [FoldoutGroup("Luascript")]
        // [Button("在资源管理器打开解压目录")]
        void LocateOutput()
        {
            string tFolder = UnityEngine.Application.dataPath + "/../" + output + "/luascript";
            tFolder = Path.GetFullPath(tFolder);
            ToolUti.Locate(tFolder);
        }

        // [FoldoutGroup("Luascript")]
        // [Button("开始解压")]
        void Extract()
        {
            var url = luascriptZipPath; 
            if(string.IsNullOrEmpty(luascriptZipPath))
            {
                url = FileSizeMgr.Instance.GetResUrl("luascript.zip");
            }
            "".Print("url", url);

            if (string.IsNullOrEmpty(url))
            {
                Debug.LogError($"{mFileSizeConfigTC.UpdateUrl}下的url is empty.");
                return;
            }

            if (!url.Contains("http"))
            {
                if (File.Exists(url) == false)
                {
                    Debug.Log($"no {url}.");
                    return;
                }
                url = "file://" + Path.GetFullPath(url);
            }

            var w = new WWW(url);
             
            System.DateTime l = System.DateTime.Now;
            while (!w.isDone)
            {
                var urlTime = (System.DateTime.Now - l).TotalSeconds;
                if (urlTime > TimeOut)
                {
                    Debug.LogError($"Timeout exceeded for :[{url}] time:{urlTime}.");
                    break;
                }
            }
            if (w.isDone)
            {
                luas.Clear();

                AssetBundle.UnloadAllAssetBundles(true);

                var bs = Decryptor(w.bytes, "luascript");
                var ab = AssetBundle.LoadFromMemory(bs);
                var ts = ab.LoadAllAssets<TextAsset>();
                foreach (var asset in ts)
                {
                    var textAsset = asset as TextAsset;
                    string fPath = string.Format(output + "/{0}/{1}.txt", "luascript", asset.name);
                    War.Base.AssetBundleManager.CheckDir(fPath);
                    System.IO.File.WriteAllBytes(fPath, asset.bytes);
                    luas.Add(fPath);
                }
                ab.Unload(false);

                Debug.Log("done");
                w.Dispose();
            }
            Debug.Log($"Extract_use_time:{(System.DateTime.Now - l).TotalSeconds}");
        }
        byte[] Decryptor(byte[] content, string assetBundleName)
        {
            return War.Common.Aes.Decryptor(content, War.Script.MainLoop.AssetBundleEncryptKey,
                                        War.Common.AlgorithmUtils.HashUtf8MD5(assetBundleName).Substring(8, 16));
        }
        #endregion

        #region exec
        // [FoldoutGroup("Exec")]
        // [Title(". Exec中解压的资源目录")]
        private List<string> additive_execlist = new List<string>()
        {
            //"exec0.asset",
            //"exec1.asset",
            //"exec2.asset",
            //"exec3.asset",
            //"exec4.asset",
            "lua_patch.asset",
        };
        // [FoldoutGroup("Exec")]
        // [Title("具体解压数据源根据FileSizeConfig配置update.json")]
        // [Button("解压Exec文件")]
        void ExtractExec()
        {
            System.DateTime l = System.DateTime.Now;
            AssetBundle.UnloadAllAssetBundles(true);
            List<string> files = new List<string>();

            string tFolder = output + "/";
            fill_normal_execs();

            foreach (var a in additive_execlist)
            {

                var url = FileSizeMgr.Instance.GetResUrl(a);
                "".Print("exec", a, url);
                if (string.IsNullOrEmpty(url))continue;

                var ab = ToolUti.DownloadAB(url, TimeOut);

                var exc_asset_path = tFolder + a;
                EditorHelp.CheckDir(exc_asset_path);
                var rk = ab.LoadAsset<War.Common.ResKey>(Path.GetFileName(a));
                rk = Object.Instantiate(rk);

                if (System.IO.File.Exists(exc_asset_path))
                {
                    System.IO.File.Delete(exc_asset_path);

                    var path_meta = exc_asset_path + ".meta";
                    //对应的meta文件也需要删除，否则下面创建时会冲突
                    if (File.Exists(path_meta))
                    {
                        File.Delete(path_meta);
                    }
                    AssetDatabase.Refresh();
                }
                UnityEditor.AssetDatabase.CreateAsset(rk, exc_asset_path);

                var fs = UnityEngine.UI.Extensions.ResKeyEditor.Export(tFolder + Path.GetFileNameWithoutExtension(a), rk, false);
                files.AddRange(fs);
            }
            System.Diagnostics.Stopwatch sw = new System.Diagnostics.Stopwatch();
            sw.Start();
            if (isOpt)
            {
                UIHelper.ImportAssetsFast(files);
            }
            else
            {
                UIHelper.ImportAssets(files.ToArray());
            }
            sw.Stop();
            Debug.Log($"import_assets_use_time:{sw.Elapsed.TotalSeconds}, opt:{isOpt}, filesCount:{files.Count}");

            AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
        }


        private void fill_normal_execs()
        {
            if (isUseNewExtractExec)
            {
                ////新版exec解压
                var execConfig = GetExecNameConfig();
                for (int i = 0; i < execConfig.Length; i++)
                {
                    string assetbundleName = $"{execConfig[i]}.asset";
                    var url = FileSizeMgr.Instance.GetResUrl(assetbundleName);
                    if (string.IsNullOrEmpty(url) == false)
                    {
                        if (additive_execlist.Contains(assetbundleName) == false)
                        {
                            additive_execlist.Add(assetbundleName);
                        }
                    }
                }
            }
            else
            {
                //旧版exec解压
                for (int i = 0; i < 10; i++)
                {
                    string assetbundleName = $"exec{i}.asset";
                    var url = FileSizeMgr.Instance.GetResUrl(assetbundleName);
                    if (string.IsNullOrEmpty(url) == false)
                    {
                        if (additive_execlist.Contains(assetbundleName) == false)
                        {
                            additive_execlist.Add(assetbundleName);
                        }
                    }
                }
            }
        }

        public static string[] GetExecNameConfig()
        {
            var filePath = UnityEngine.Application.dataPath + "/Scripts/Editor/Other/exec_split.json";
            if (!File.Exists(filePath))
            {
                Debug.LogError($"exec_split.json is not exist /Scripts/Editor/Other/ !");
                return null;
            }
            var dataList = GetJsonData(filePath);

            return dataList["execName"];
        }

        public static Dictionary<string, string[]> GetJsonData(string path)
        {
            if (!string.IsNullOrEmpty(path))
            {
                try
                {
                    var jsonData = File.ReadAllText(path);
                    if (!string.IsNullOrEmpty(path))
                    {
                        var data = ToolUti.ToObj<Dictionary<string, string[]>>(jsonData);
                        return data;
                    }
                }
                catch (System.Exception e)
                {
                    "".Print("error:", e.ToString());
                }
            }
            return new Dictionary<string, string[]>();
        }

        // [FoldoutGroup("Exec")]
        // [Title("根据assetPaths列表中得文件夹生成与Abname一致得exec文件,输出路径为OutPut底下的exec文件夹")]
        // [Button("生成Exec文件")]
        void StartBuildExec()
        {
            var folderName = output + "/exec";
            if (!Directory.Exists(folderName))
            {
                Directory.CreateDirectory(folderName);
            }
            fill_normal_execs();

            foreach (var abname in additive_execlist)
            //foreach (var item in assetPaths)
             {
                var ab_folder = output + "/" + abname;
                Debug.LogErrorFormat(" 遍历传入文件列表 abname{0}", abname);
                if (string.IsNullOrEmpty(abname))
                {
                    //abname = null;
                    continue;
                }

                var cullLuas = new Dictionary<string, byte[]>();
                var normal = new Regex(@"[^\r\n]*");
                //Dictionary<string, byte[]> originLuaContents = new Dictionary<string, byte[]>();
                var LuaPath = ab_folder;
                string[] luaFiles = Directory.GetFiles(LuaPath, "*.txt", SearchOption.AllDirectories);
                var DictLuaBs = new Dictionary<string, byte[]>();
                foreach (var luaFile in luaFiles)
                {
                    var fstr = System.IO.File.ReadAllText(luaFile, System.Text.Encoding.UTF8);
                    var bytes = System.Text.Encoding.UTF8.GetBytes(fstr);
                    DictLuaBs.Add(luaFile, bytes);
                }

                var listLuaInZip = new Dictionary<string, bool>();
                var listLuaOutOfZip = new Dictionary<string, bool>();
                foreach (var ii in DictLuaBs)
                {
                    Debug.LogErrorFormat(" 文件得key {0}", ii.Key);
                    listLuaOutOfZip[ii.Key] = true;
                }

                System.Func<string, int> getPathNameV = delegate (string s)
                {
                    int length = s.Length;
                    var num = 0;
                    for (int i = 0; i < length; i++)
                    {
                        num += s[i];
                    }
                    return num;
                };
                var listAssets = new List<List<string>>();
                var listToImport = new List<string>();
                var splitCount = 1;
                for (int i = 0; i < splitCount; i++)
                {
                    listAssets.Add(new List<string>());
                }
                foreach (var l in listLuaOutOfZip.Keys)
                {
                    var n = getPathNameV(l);
                    listAssets[n % splitCount].Add(l);
                }

                Dictionary<string, string> existModuleSet = new Dictionary<string, string>();
                Debug.LogErrorFormat(" listAssets得长度 {0}", listAssets.Count);
                for (int i = 0; i < listAssets.Count; i++)
                {
                    var lll = listAssets[i];

                    // exec asset 
                    var exec_file_name = abname;
                    var exc_asset_path = folderName + '/' + exec_file_name;
                    if (!File.Exists(exc_asset_path))
                    {
                        EditorHelp.CheckDir(exc_asset_path);
                        var rk = ScriptableObject.CreateInstance<ResKey>();
                        rk.keys = new List<ResKey.KeyValuePair>();
                        UnityEditor.AssetDatabase.CreateAsset(rk, exc_asset_path);
                    }

                    string modlePath;
                    var rk1 = AssetDatabase.LoadAssetAtPath<ResKey>(exc_asset_path);
                    Debug.LogErrorFormat(" exc_asset_path = {0}  ", exc_asset_path);
                    rk1.keys.Clear();

                    foreach (var iii in lll)
                    {
                        var filename = Path.GetFileNameWithoutExtension(iii);
                        if (existModuleSet.TryGetValue(filename, out modlePath))
                        {
                            Debug.LogError($"Exist the same file:{modlePath},{abname},{ab_folder}");
                        }
                        else
                        {
                            existModuleSet[filename] = iii;
                        }

                        rk1.keys.Add(new ResKey.KeyValuePair()
                        {
                            key = filename,
                            value = DictLuaBs[iii]
                        });
                    }

                    var ai = AssetImporter.GetAtPath(exc_asset_path);
                    if (ai)
                    {
                        ai.assetBundleName = abname;
                        listToImport.Add(exc_asset_path);
                    }
                    EditorUtility.SetDirty(rk1);
                }

                AssetDatabase.SaveAssets();
            }
        }

        #endregion
    }


    public class FileUids
    {
        [System.NonSerialized]
        public string localPath = "Assets/LuaVice/FileUids.json";
        public Dictionary<string, List<string>> ids = new Dictionary<string, List<string>>();

        public string GetUid(string filename)
        {
            if (!ids.TryGetValue(filename, out var idStr)) return null;
            return idStr[0];
        }
        public string GetFilePath(string filename)
        {
            if (!ids.TryGetValue(filename, out var idStr)) return null;
            return idStr[1];
        }
        public void Clear()
        {
            ids.Clear();
        }
        public void Save()
        {
            var json = UIHelper.ToJson(this, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(localPath, json);
        }
        public List<string> Diff(FileUids f)
        {
            var list = new List<string>();
            foreach (var k in ids.Keys)
            {
                if (!f.ids.TryGetValue(k, out var v))
                {
                    //list.Add(k);
                    continue;
                }
                var sv = ids[k];

                if (sv[0].CompareTo(v[0])!=0)
                {
                    list.Add(k);
                }
            }
            return list;
        }
        public void Update(string ff, string id)
        {
            var filename = Path.GetFileNameWithoutExtension(ff);

            if (!ids.TryGetValue(filename, out var idStr))
            {
                idStr = new List<string>();
                ids[filename] = idStr;
            }
            idStr.Clear();
            idStr.Add(id);
            idStr.Add(ff);
        }

        public void UpdateFolder(string folder)
        {
            var files = Directory.GetFiles(folder, "*.txt", SearchOption.AllDirectories);
            foreach (var f in files)
            {
                var md5 = ToolUti.File2MD5(f);
                Update(f,md5);  
            }
        }
    }
}
#endif
