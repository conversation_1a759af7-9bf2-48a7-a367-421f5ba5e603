#if UNITY_EDITOR
using Sirenix.OdinInspector;
using SuperTools.Editor;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CheckAudioTC
    {
        [HideInInspector]
        public const string TIPS_CHECK_AUDIO = "检查Audio配置";

        public List<AssetTObj> fix_list = new List<AssetTObj>();
        [Button]
        void check_audio()
        {
            string path = @"Assets\Art\Sound\Skill";
            var guids = UnityEditor.AssetDatabase.FindAssets("t:audioclip", new string[] {   });

            foreach (var g in guids)
            {
                var asset_path = AssetDatabase.GUIDToAssetPath(g);
                var ai = AssetImporter.GetAtPath(asset_path) as AudioImporter;
                if (
                    ai
                    && ai.defaultSampleSettings.loadType == UnityEngine.AudioClipLoadType.DecompressOnLoad
                    )
                {
                    fix_list.Add(new AssetTObj()
                    {
                        path = asset_path
                    });
                    "".Print("check_audio loadtype == UnityEngine.AudioClipLoadType.DecompressOnLoad", asset_path);
                }
            }
        }
        [Button]
        void fix_audio()
        {

            var list = new List<string>();
            foreach (var g in fix_list)
            {
                var ai = AssetImporter.GetAtPath(g.path) as AudioImporter;
                if (
                    ai
                    
                    )
                {
                    var setting = ai.defaultSampleSettings;

                    setting.loadType = AudioClipLoadType.Streaming;
                    ai.defaultSampleSettings = setting;
                    list.Add(g.path);
                }
            }
            "".Print("fix audio", UIHelper.ToJson(list));

            UIHelper.ImportAssets(list.ToArray());
            AssetDatabase.Refresh();
        }
    }
}

#endif