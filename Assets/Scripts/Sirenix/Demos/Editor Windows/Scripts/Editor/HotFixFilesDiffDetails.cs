/****************************************************
 *  文件：HotFixFilesDiffDetails.cs
 *  作者：WJY
 *  日期：2022/1/11
 *  功能：对比两个版本的热更资源的差异，主要列出新改资源的总大小跟资源列表
*****************************************************/


using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using NUnit.Framework;
using Sirenix.Utilities;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEngine;
using War.Base;
using Object = System.Object;
using WebRequest = System.Net.WebRequest;

#if UNITY_EDITOR

namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class HotFixFilesDiffDetails : GlobalConfig<HotFixFilesDiffDetails>
    {
        public string hotUrlOld = "https://dl-wm2.cool-play.com/xhero_update/wm2_2020/Android/resource/384/files2.txt";
        public string hotUrlNew = "https://dl-wm2.cool-play.com/xhero_update/wm2_2020/Android/resource/385/files2.txt";

        public string sizeDiff = "";
        public List<string> resKeys;

        [Serializable]
        public class DiffDetails
        {
            public string size;
            public List<string> res;
        }

        [FolderPath] public string diffOutputPath = "";

        [Button()]
        void CalculateDiff()
        {
            var oldText = ReadUrlText(hotUrlOld);
            hashCheck oldFile = null, newFile = null;
            if (!string.IsNullOrEmpty(oldText))
            {
                oldFile = UIHelper.ToObj<hashCheck>(oldText);
            }

            var newData = ReadUrlText(hotUrlNew);
            if (!string.IsNullOrEmpty(newData))
            {
                newFile = UIHelper.ToObj<hashCheck>(newData);
            }


            var compareResult = getCompareResult(oldFile, newFile);

            resKeys = compareResult.allAssetChange.Keys.ToList();
            sizeDiff = $"新增文件大小为：{SizeSuffix(compareResult.totalSize)}，原字节数为:{compareResult.totalSize}";

            DiffDetails details = new DiffDetails()
            {
                size = sizeDiff,
                res = resKeys,
            };

            string json = compareResult.outputString;
            if (diffOutputPath.Length > 0)
            {
                File.WriteAllText(this.diffOutputPath + "/DiffDetails.json", json);
                Debug.Log($"对比完成，已生成文件到" + this.diffOutputPath + "/DiffDetails.json");
            }
            else
            {
                Clipboard.Copy(json);
                Debug.Log("输出文件夹未设置，已将结果复制到粘贴板");
            }
            Debug.Log(json);
            AssetDatabase.Refresh();
        }

        public delegate void OnDiff(KeyValuePair<string, string[]> aaa);

        public static void CompareHashCheck(hashCheck oldHashCheck, hashCheck newHashCheck, OnDiff onDiff)
        {
            if (oldHashCheck != null && newHashCheck != null)
            {
                foreach (var newInfo in newHashCheck.list)
                {
                    //两个文件同时存在对应资源
                    if (oldHashCheck.list.ContainsKey(newInfo.Key))
                    {
                        var oldInfo = oldHashCheck.list[newInfo.Key];
                        if (IsArrAble(oldInfo) && IsArrAble(newInfo.Value))
                        {
                            if (oldInfo[1] != newInfo.Value[1])
                            {
                                onDiff(newInfo);
                            }
                        }
                    }
                    else
                    {
                        onDiff(newInfo);
                    }
                }
            }
        }

        public static Dictionary<string, long> GetMiniGameSize(Dictionary<string, long> folderChange)
        {
            Dictionary<string, long> miniGameSize = new Dictionary<string, long>();
            using (StreamReader st = File.OpenText("Assets/CasualGame/key2mark.json"))
            {
                dynamic array = JsonConvert.DeserializeObject(st.ReadToEnd());
                foreach (var v in array)
                {
                    JProperty property = ((JProperty) v);
                    if (property.Name.Equals("allmarks"))
                        continue;
                    miniGameSize.Add(property.Name, 0);
                    foreach (var jToken in property.Value)
                    {
                        string folder = jToken.Value<string>().ToLower();
                        if (folderChange.ContainsKey(folder))
                        {
                            miniGameSize[property.Name] += folderChange[folder];
                        }
                    }
                }
            }

            return miniGameSize;
        }


        /// <summary>
        /// 该资源是否是规范的
        /// </summary>
        /// <param name="arr"></param>
        /// <returns></returns>
        public static bool IsArrAble(string[] arr)
        {
            return arr != null && arr.Length >= 5;
        }

        /// <summary>
        /// 读取url的file文件
        /// </summary>
        /// <param name="url"></param>
        /// <returns>text</returns>
        public string ReadUrlText(string url)
        {
            string target = string.Empty;
            //去除2边空格符
            url = url.Trim();
            if (url.StartsWith("http"))
            {
                using Stream stream = WebRequest.Create(new Uri(url)).GetResponse().GetResponseStream();
                using (StreamReader file_content = new StreamReader(stream))
                {
                    target = file_content.ReadToEnd();
                    file_content.Close();
                }

                stream.Close();
            }
            else
            {
                string path = url.Replace("\\", "/");
                if (File.Exists(path))
                {
                    target = File.ReadAllText(path); // 添加对本地文件支持
                }
            }

            return target;
        }


        static readonly string[] SizeSuffixes =
            {"bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"};

        public static string SizeSuffix(long value, int decimalPlaces = 1)
        {
            if (decimalPlaces < 0)
            {
                throw new ArgumentOutOfRangeException("decimalPlaces");
            }

            if (value < 0)
            {
                return "-" + SizeSuffix(-value, decimalPlaces);
            }

            if (value == 0)
            {
                return string.Format("{0:n" + decimalPlaces + "} bytes", 0);
            }

            int mag = (int) Math.Log(value, 1024);
            decimal adjustedSize = (decimal) value / (1L << (mag * 10));
            if (Math.Round(adjustedSize, decimalPlaces) >= 1000)
            {
                mag += 1;
                adjustedSize /= 1024;
            }

            return string.Format("{0:n" + decimalPlaces + "} {1}",
                adjustedSize,
                SizeSuffixes[mag]);
        }

        public class CompareResult
        {
            public long totalSize = 0;
            public long casualGameSize = 0;
            public long assetSize = 0;
            public long execSize = 0;
            public long gamescp_langSize = 0;
            public long luascriptSize =0;
            public long assetOtherSize = 0;
            public Dictionary<string, long> miniGameChange; //小游戏资源变化，以小游戏整个小游戏为单位
            public Dictionary<string, long> assetChange; //除小游戏外的资源变化
            public Dictionary<string, long> allAssetChange; //全部资源变化

            public string outputString;

            public CompareResult(long totalSize, long casualGameSize, long assetSize,long gamescp_langSize,long execSize, long luascriptSize,long assetOtherSize,
                Dictionary<string, long> miniGameChange, Dictionary<string, long> assetChange,
                Dictionary<string, long> allAssetChange, string outputString)
            {
                this.totalSize = totalSize;
                this.casualGameSize = casualGameSize;
                this.assetSize = assetSize;
                this.gamescp_langSize = gamescp_langSize;
                this.execSize = execSize;
                this.luascriptSize =  luascriptSize;
                this.assetOtherSize = assetOtherSize;
                this.miniGameChange = miniGameChange;
                this.assetChange = assetChange;
                this.allAssetChange = allAssetChange;
                this.outputString = outputString;
            }
        }

        public static CompareResult getCompareResult(hashCheck oldHashCheck, hashCheck newHashCheck)
        {
            Dictionary<string, long> casualGameChangeSize = new Dictionary<string, long>();
            Dictionary<string, List<string>> casualGameChangeKey = new Dictionary<string, List<string>>();
            Dictionary<string, long> assetChange = new Dictionary<string, long>();
            Dictionary<string, long> allAssetChange = new Dictionary<string, long>();
            long totalSize = 0;
            long casualGameSize = 0;
            long gamescp_langSize = 0;
            long luascriptSize = 0;
            long execSize = 0;
            long assetOtherSize = 0;
            CompareHashCheck(oldHashCheck, newHashCheck, (item) =>
            {
                string key = item.Key;
                if (long.TryParse(item.Value[2], out long newSize))
                {
                    totalSize += newSize;
                    allAssetChange[key] = newSize;
                    if (key.StartsWith("casualgame/"))
                    {
                        casualGameSize += newSize;
                        int next = key.IndexOf('/', "casualgame/".Length);
                        string folderName = key.Substring("casualgame/".Length, next - "casualgame/".Length).ToLower();
                        if (!casualGameChangeSize.ContainsKey(folderName))
                        {
                            var list = new List<string>();
                            list.Add(key);
                            casualGameChangeKey[folderName] = list;
                            casualGameChangeSize[folderName] = newSize;
                        }
                        else
                        {
                            casualGameChangeKey[folderName].Add(key);
                            casualGameChangeSize[folderName] += newSize;
                        }
                    }
                    else
                    {
                        if (assetChange.ContainsKey(key))
                        {
                            assetChange[key] = newSize;
                        }
                        else
                        {
                            assetChange[key] = newSize;
                        }
                        if (key.StartsWith("gamescp_lang"))
                        {
                            gamescp_langSize += newSize;
                        }
                        else if (key.StartsWith("exec"))
                        {
                            execSize += newSize;
                        }
                        else if(key.StartsWith("luascript"))
                        {
                            luascriptSize += newSize;
                        }
                        else
                        {
                            assetOtherSize += newSize;
                        }
                    }
                }
                else
                {
                    Debug.LogError($"解析失败key={key}");
                }
            });
            Dictionary<string, long> miniGameSize = GetMiniGameSize(casualGameChangeSize);

            Dictionary<string, Object> output = new Dictionary<string, Object>();
            Dictionary<string, Object> diffSize = new Dictionary<string, Object>();
            Dictionary<string, Object> diffSizeCasual = new Dictionary<string, Object>();
            Dictionary<string, Object> diffList = new Dictionary<string, Object>();
            Dictionary<string, Object> diffListCasual = new Dictionary<string, Object>();
            Dictionary<string, Object> diffListAsset = new Dictionary<string, Object>();

            output["diffSize"] = diffSize;
            output["diffList"] = diffList;

            diffSize["casualGame"] = SizeSuffix(casualGameSize, 2);
            miniGameSize=miniGameSize.OrderBy(k => -k.Value).ToDictionary(o => o.Key, p => p.Value);
            if (miniGameSize.Count > 0)
            {
                diffSize["casualGames"] = diffSizeCasual;
                foreach (var keyValuePair in miniGameSize)
                {
                    if (keyValuePair.Value > 0)
                    {
                        diffSizeCasual.Add(keyValuePair.Key, SizeSuffix(keyValuePair.Value, 2));
                    }
                }
            }

            diffSize["asset"] = SizeSuffix(totalSize - casualGameSize, 2);

            diffList["casualDiff"] = diffListCasual;
            diffList["assetDiff"] = diffListAsset;
            
            foreach (var keyValuePair in casualGameChangeKey)
            {
                keyValuePair.Value.Sort((s1,s2)=>(int) (allAssetChange[s2]-allAssetChange[s1]));
                Dictionary<string, string> casualDiff = new Dictionary<string, string>();
                foreach (var s in keyValuePair.Value)
                {
                    casualDiff[s] = SizeSuffix(allAssetChange[s],2);
                }
                
                diffListCasual[keyValuePair.Key] = casualDiff;
            }

            assetChange = assetChange.OrderBy(o => -o.Value).ToDictionary(o => o.Key, p => p.Value);
            foreach (var keyValuePair in assetChange)
            {
                diffListAsset[keyValuePair.Key] = SizeSuffix(keyValuePair.Value, 2);
            }
            
            string json = UIHelper.ToJson(output);

            return new CompareResult(totalSize, casualGameSize, totalSize - casualGameSize, gamescp_langSize,execSize,luascriptSize,assetOtherSize,casualGameChangeSize, assetChange,
                allAssetChange, json);
        }
    }
}
#endif