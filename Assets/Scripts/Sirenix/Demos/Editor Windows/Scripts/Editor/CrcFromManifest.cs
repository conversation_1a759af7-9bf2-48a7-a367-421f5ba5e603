#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using Sirenix.Utilities;
    using System.IO;
    using System.Text.RegularExpressions;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class CrcFromManifest : GlobalConfig<CrcFromManifest>
    {
        [InfoBox("抓取manifest的crc值")]
        [Toolt<PERSON>("标记,作为打印使用")]
        public string mark;
        [Toolt<PERSON>("manifest路径")]
        [Sirenix.OdinInspector.FilePath] 
        public string path;
        [Tooltip("对应Crc值")]
        public string crcUnity;

 
        [But<PERSON>("提取Crc")]
        void Cal()
        {
            var regexCrc = new Regex(@"CRC: (\d+)");

            if (File.Exists(path))
            {
                var manifestContent = File.ReadAllText(path);
                var match = regexCrc.Match(manifestContent);
                if (match.Groups.Count > 1)
                {
                    crcUnity = match.Groups[1].ToString();
                }
            }
        }

        [Button("打印到日志框")]
        void PrintBoard()
        {
            blackboard += string.Format("{0}:{1}=>{2}\n", mark,path,crcUnity);
        }
        [TextArea]
        public string blackboard = "";
    }
}
#endif
