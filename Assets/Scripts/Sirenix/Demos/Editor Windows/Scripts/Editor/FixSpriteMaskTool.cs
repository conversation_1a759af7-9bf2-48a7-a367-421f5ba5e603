#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class FixSpriteMaskTool : GlobalConfig<FixSpriteMaskTool>
    {
        [Serializable]
        public class StrProto
        {

            [HideLabel]
            [HorizontalGroup]
            public string go;
            [HideLabel]
            [HorizontalGroup]
            public string path;
            [HideLabel]
            [HorizontalGroup]
            public string tpath;

            [HorizontalGroup("2")]
            [Button]
            void Locate()
            {
                var ass = ToolUti.LocateGameObject(go, path);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }
            [HorizontalGroup("2")]
            [Button]
            void SelectTarget()
            {
                var ab = AssetDatabase.GetImplicitAssetBundleName(go);
                if (Lconfig.fixSpriteMask.ContainsKey(ab) == false) return;
                var list = Lconfig.fixSpriteMask[ab];
                foreach (var item in list)
                {
                    if(path.EndsWith(item[0]))
                    {
                        tpath = item[1];
                        var ass = ToolUti.LocateGameObject(go, tpath);
                        if (ass)
                        {
                            Selection.activeObject = ass;
                        }
                        break;
                    }
                }
            }

            static LayoutSmConfig lconfig;

            public static LayoutSmConfig Lconfig
            {
                get
                {
                    if(lconfig==null)
                    {
                        EditorHelp.CheckDir(configpath);
                        if (File.Exists(configpath))
                        {
                            var cStr = File.ReadAllText(configpath);
                            lconfig = UIHelper.ToObj<LayoutSmConfig>(cStr);
                        }
                        else
                        {
                            return null;
                        }

                    }
                    return lconfig;
                } 
            }
            //[HorizontalGroup("2")]
            //[Button]
            //void SelectTarget()
            //{
            //    var sel = Selection.activeGameObject;
            //    if (sel)
            //    {
            //        var t = sel.transform as RectTransform;
            //        if (t)
            //        {
            //            var rootPath = Path.GetFileNameWithoutExtension(go);

            //            var root = GameObject.Find(rootPath);

            //            tpath = ToolUti.CopyPathTo(t, root ? root.transform : t.root);

            //        }
            //    }
            //}
        }

        public static string configpath = "Assets/EditorConfig/FixSpriteMask/c.bytes";

        [InfoBox("查找UI物体引用脚本情况")]
        [Tooltip("标记,作为打印使用")]
        public string mark;
        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/UI",
        };
        [Tooltip("要查找的脚本名")]
        [OnValueChanged("OnFind")]
        public string scr;
        [Tooltip("要查找的脚本名")]
        public string scrfind;

        void OnFind()
        {

            var t = typeof(SpriteMask).Assembly.GetType(scr, false, true);
            if (t != null)
            {
                scrfind = t.FullName;
            }
            else
            {
                //scrfind = typeof(SpriteMask).FullName;
            }
        }
        
        public List<StrProto> list = new List<StrProto>();

        [Button("查找所有引用")]
        void Find()
        {
            list.Clear();
            var t = typeof(SpriteMask);
            var finds = AssetDatabase.FindAssets("t:GameObject", findassets.ToArray());
            ToolUti.Iter(finds, (f) =>
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(f);
                var g = AssetDatabase.LoadMainAssetAtPath(assetPath) as GameObject;
                if (g)
                {
                    var ls = g.GetComponentsInChildren(t, true);
                    foreach (var sc in ls)
                    {
                        list.Add(new StrProto()
                        {
                            go = assetPath,
                            path = ToolUti.CopyPathTo(sc.transform, sc.transform.root),
                        });
                    }
                }
                return assetPath;
            });

        }

        [Button()]
        void FixConfig()
        {
            EditorHelp.CheckDir(configpath);
            LayoutSmConfig smC = null;
            if (File.Exists(configpath))
            {
                var cStr = File.ReadAllText(configpath);
                smC = UIHelper.ToObj<LayoutSmConfig>(cStr);
            }
            else
            {
                return;
            }
            var canvasGO = GameObject.Find("/UIRoot/CanvasWithMesh");
            Canvas c = canvasGO.GetComponent<Canvas>();
            var cHeight = (c.transform as RectTransform).rect.height;

            var cH = GetWorldH((c.transform as RectTransform));
            Debug.Log(cHeight);
            foreach (var k in smC.fixSpriteMask.Keys)
            {
                var items = smC.fixSpriteMask[k];
                foreach (var item in items)
                {
                    if (item.Count == 2)
                    {
                        item.Add("1280");
                    }
                    var abs = AssetDatabase.GetAssetPathsFromAssetBundle(k);

                    var gT = ToolUti.LocateGameObject(abs[0], item[1]);
                    if (gT == null) continue;
                    var rt = gT.transform as RectTransform;
                    //var rH = GetWorldH(rt);

                    //var h = 1280 * rH / cH;
                    var h = rt.rect.height;


                    item[2] =  h + "";
                    //item[2] = (gT.transform as RectTransform).rect.height.ToString();
                    Debug.Log(k + ":"+rt.name+ ":" + h + ":" + rt.offsetMax.y + ":" + rt.offsetMin.y);

                }
            }
            File.WriteAllText(configpath, UIHelper.ToJson(smC));
            AssetDatabase.Refresh();
        }

        float GetWorldH(RectTransform r)
        {
            Vector3[] lc = new Vector3[4];
            r.GetWorldCorners(lc); 
            var h = lc[2].y - lc[3].y;

            return h;
        }
        [Button("打印到日志框")]
        void PrintBoard()
        {
            //blackboard += string.Format("{0}:{1}=>{2}\n", mark, hexString1, intValue);
        }
        [TextArea]
        public string blackboard = "";
         
        [MenuItem("GameObject/UI/AddFixSpriteMask &l")]
        public static void FixSpriteMaskCmd()
        {
            //if (Application.isPlaying) return;

            var sels = Selection.gameObjects;
            if (sels.Length < 2) return;
            var gSm = sels[0];
            var gT = sels[1];
            foreach (var sel in sels)
            {
                if(sel.GetComponent<SpriteMask>())
                {
                    gSm = sel;
                }
                else
                {
                    gT = sel;
                }
            }

            UnityEngine.Object assetObject = PrefabUtility.GetPrefabParent(gSm);
            var smPath = AssetDatabase.GetAssetPath(assetObject);

            var rootTName = Path.GetFileNameWithoutExtension(smPath);
            var rootG = GameObject.Find(rootTName);
            if(rootG==null)
            {
                Debug.LogError("rootT is null:" + smPath);
                return;
            }
            var rootT = rootG.transform;
            var gSmPath = ToolUti.CopyPathTo(gSm.transform, rootT);
            var gTPath = ToolUti.CopyPathTo(gT.transform, rootT);
            gSmPath = gSmPath.Substring(gSmPath.IndexOf('/')+1);
            gTPath = gTPath.Substring(gTPath.IndexOf('/')+1);
            var tRect = (gT.transform as RectTransform).rect;

            Debug.LogFormat("gSm:{0} gT:{1} {2}", gSm.name, gT.name, smPath);

             
            EditorHelp.CheckDir(configpath);
            LayoutSmConfig smC = null;
            if (File.Exists(configpath))
            {
                var cStr = File.ReadAllText(configpath);
                smC = UIHelper.ToObj<LayoutSmConfig>(cStr);
            }
            else
            {
                smC = new LayoutSmConfig();
            }
            var abname = AssetDatabase.GetImplicitAssetBundleName(smPath);
            if (string.IsNullOrEmpty(abname))
            {
                Debug.LogWarning(smPath + " doesnt have abname");
                return;
            }
            List<List<string>> layoutlist = null;
            if(smC.fixSpriteMask.TryGetValue(abname, out layoutlist) == false)
            {
                layoutlist = new List<List<string>>();
            }
            smC.fixSpriteMask[abname] = layoutlist;

            int ind = -1;
            for (int i = layoutlist.Count - 1; i >= 0; i--)
            {
                var list = layoutlist[i];
                if (list.Count < 1) continue;
                if (list[0] ==gSmPath)
                {
                    list[1] = gTPath;
                    list[2] = tRect.height.ToString();
                    ind = i;
                }
            }
            if(ind == -1)
            {
                layoutlist.Add(new List<string>()
                {
                    gSmPath,
                    gTPath,
                    tRect.height.ToString()
                });
            }
            File.WriteAllText(configpath, UIHelper.ToJson(smC));
            AssetDatabase.Refresh();
            ToolUti.SetABNameByPath(configpath);
        }
        /// <summary>
        ///   {
        ///     "fixSpriteMask": {
        ///       "ui/prefabs/ui7daychallenge.prefab": [
        ///         [
        ///           "maskObj/GameObject",
        ///           "TargetList"
        ///         ]
        ///       ]
        ///     }
        ///   }
        /// </summary>
        public class LayoutSmConfig
        {
            public Dictionary<string, List<List<string>>> fixSpriteMask = new Dictionary<string, List<List<string>>>();
        } 
    }

}
#endif
