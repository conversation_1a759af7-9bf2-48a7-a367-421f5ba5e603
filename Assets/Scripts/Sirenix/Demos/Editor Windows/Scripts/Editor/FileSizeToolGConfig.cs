#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using UnityEditor;
    using UnityEngine;
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class FileSizeToolGConfig : GlobalConfig<FileSizeToolGConfig>
    {
        [ReadOnly]
        public string Title = "Assetbundle Size Cal";
        [OnValueChanged("onListChange")]
        [LabelText("参与计算的ab列表")]
        public List<string> ablist;
        [LabelText("实际参与计算的ab列表")]
        public List<string> alllist;
        public bool includeDependences = false;
        public bool sortAllist = false;
        public string size;
        [Button("计算Size")]
        void onListChange()
        {
            alllist = ablist;
            
            if(includeDependences)
            {
                var dic = new Dictionary<string, bool>();
                foreach (var ab in ablist)
                {
                    var abs = AssetDatabase.GetAssetBundleDependencies(ab, true);
                    foreach (var a in abs)
                    {
                        dic[a] = true;
                    }
                    dic[ab] = true;
                }
                alllist = new List<string>();
                alllist.AddRange(dic.Keys);
            }

            var total = 0;
            foreach (var ab in alllist)
            {
                var s = FileSizeMgr.Instance.GetSize(ab);
                total += s;
            }
            if(sortAllist)
            {
                alllist.Sort((a, b) => FileSizeMgr.Instance.GetSize(b) - FileSizeMgr.Instance.GetSize(a));

                var nlist = alllist.ConvertAll((a) => a + " " + Mathf.Round(FileSizeMgr.Instance.GetSize(a) / 1024f / 1024) + "M");
                "".Print("\n",string.Join("\n", nlist.ToArray()));
                
            }    
            size = Mathf.Round(total / 1024f / 1024) + "M";
        }
        [FoldoutGroup("State")]
        [Button("导出到粘贴板")]
        public void ExportClipBoard()
        {
            var str = string.Join("\n", alllist.ToArray());
            LogHelp.clipboard = str;
            this.Print("已经添加到粘贴板");
        }
        [FoldoutGroup("State")]
        [Button("从粘贴板导入")]
        public void ImportClipBoard()
        {
            var str = LogHelp.clipboard;
            var list = str.Normalize().Split(' ');
            var set = new HashSet<string>(list);
            ablist = new List<string>(set);
            onListChange();
        }



        [FoldoutGroup("Config")]
        [ShowInInspector]
        [LabelText("update.json tmp path:")]
        public string UpdatePath
        {
            get
            {
                return FileSizeMgr.UpdatePath;
            }
            set
            {
                FileSizeMgr.UpdatePath = value;
            }
        }
        [FoldoutGroup("Config")]
        [ShowInInspector]
        [LabelText("update.json url:")]
        public string UpdateUrl
        {
            get
            {
                return FileSizeMgr.UpdateUrl;
            }
            set
            {
                FileSizeMgr.UpdateUrl = value;
            }
        }
        [FoldoutGroup("Config")]
        [ShowInInspector]
        [LabelText("file.txt tmp path:")]
        public string filePath
        {
            get
            {
                return FileSizeMgr.filePath;
            }
            set
            {
                FileSizeMgr.filePath = value;
            }
        }
    }
}
#endif
