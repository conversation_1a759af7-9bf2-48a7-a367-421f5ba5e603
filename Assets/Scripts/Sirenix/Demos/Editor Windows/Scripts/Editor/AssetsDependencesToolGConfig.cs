#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using UnityEngine;
    using War.Base;
    using SuperTools.Editor;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class AssetsDependencesToolGConfig : GlobalConfig<AssetsDependencesToolGConfig>
    {
        public  AssetDependenceTc ad;
        public List<ABIterationTc> abs;
        
    }


}
#endif
