#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System.IO;
    using System.Text;
    using UnityEditor;
    using UnityEngine;
    [GlobalConfig("Art/Maps/ToT")]
    public class TotEventConfig : GlobalConfig<TotEventConfig>
    {
        private const string EVENT_KEY_NAME = "sid";
        public const string TOT_CONFIG_PATH = "Assets/Art/Maps/ToT";
        public const string TOT_CONFIG_MAP_PATH = "Assets/Art/Maps/ToT/MapConfig/";

        [System.Serializable]
        public class DisplayAtt
        {
            public string eType;
            [SerializeField]
            public List<DisplayKey> att = new List<DisplayKey>();
        }
        [System.Serializable]
        public class DisplayKey
        {
            public string keyName;
            public string csvName;
            public string des;
            public bool show = true;
            [ValueDropdown("scopeDropdown")]
            public string _scope = "s";
            [ValueDropdown("vTypeDropdown")]
            public string _vtype = "string";
            [ValueDropdown("arrtypeDropdown")]
            public string _arrtype = "";
            public string _tooltips = "";


            static List<string> vTypeDropdown()
            {
                return TotEventConfig.Instance._vTypeAttOps;

            }
            static List<string> scopeDropdown()
            {
                return TotEventConfig.Instance._scopeAttOps;
            }
            static List<string> arrtypeDropdown()
            {
                return TotEventConfig.Instance._vTypeAttOps;
            }
        }
        [System.Serializable]
        public class KeyValue
        {
            public string key;
            public string value;
        }
        [System.Serializable]
        public class SpriteVO
        {
            public int id;
            public int type;
            public Sprite sprite;
        }

        [System.Serializable]
        public class TotIndexState
        {
            public int index;

            public int state=0;
            public const byte VISIBLE = 0x1;
            public const byte OBSTACLE = 0x2;
            public const byte CRASH = 0x4;
            public const byte HIDE = 0x8;

            public static List<int> SerializeList = new List<int>()
            {
                VISIBLE ,
                OBSTACLE ,
                CRASH ,
                HIDE,
            };
            public bool GetState(int v)
            {
                return (state & v) > 0;
            }
            public void SetState(int v)
            {
                state = (byte) (state | v);
            }
        }
            [System.Serializable]
        public class TotEvent
        {
            /// <summary>
            /// pos
            /// </summary>
            [LabelText("index")]
            public int index
            {
                get
                {
                    var iid = -1;
                    int.TryParse(this["index"], out iid);
                    return iid;
                }
                set
                {
                    this["index"] = value.ToString();
                }
            }
            [UnityEngine.SerializeField]
            public List<KeyValue> props = new List<KeyValue>();

            public string this[string key]
            {
                get
                {
                    foreach (var k in props)
                    {
                        if(k.key==key)
                        {
                            return k.value;
                        }
                    }
                    return default(string);
                }
                set
                {
                    var f = props.Find(k => k.key == key);
                    if (f != null)
                    {
                        if(f.value != value)
                        {
                            f.value = value;
                        }
                    }
                    else
                    {
                        props.Add(new KeyValue() { key = key, value = value });
                    }
                }
            }
            public TotEvent clone()
            {
                var n = new TotEvent();
                n.index = this.index;
                n.props = new List<KeyValue>();
                foreach (var p in props)
                {
                    n.props.Add(new KeyValue()
                    {
                        key = p.key,
                        value = p.value
                    });
                }
                return n;
            }
        }
        [System.Serializable]
        public class TotMap
        {
            public TotEventConfig root;
            [LabelText("最大Index")]
            public int maxIndex;

            [LabelText("Map")]
            public int id;


            public bool dealEvent;

            /// <summary>
            /// 可见sprite列表
            /// </summary>
            public List<Sprite> visibleSpriteList = new List<Sprite>();

            /// <summary>
            /// 事件列表
            /// </summary>
            public List<TotEvent> events = new List<TotEvent>();
            /// <summary>
            /// 地图state列表
            /// </summary>
            public List<TotEvent> indexStates = new List<TotEvent>();

            
        }
        public int mapid;

        TotMap _map;
        public TotMap map
        {
            get
            {
                if(_map!=null && _map.id == mapid)
                {
                    return _map;
                }
                _map = GetMap(mapid);
                return _map;
            }
            set
            {

            }
        }
        
        [LabelText("所有地图事件配置")]
        public List<TotMap> maps;
        [LabelText("临时所有可选事件配置")]
        public List<SpriteVO> sprites;
        [LabelText("导出刷新表字段配置")]
        public List<DisplayAtt> displayAtts;
        [LabelText("导出地图字段配置")]
        public List<DisplayAtt> mapsAtts;
        [LabelText("导出服务器客户端使用配置")]
        public List<string> _scopeAttOps = new List<string>()
                {
                    "",
                    "c",
                    "s",
                    "sc",
                    "sco",
                };
        [LabelText("字段类型可选值")]
        public List<string> _vTypeAttOps = new List<string>()
                {
                    "",
                    "string",
                    "int32",
                    "bit1",
                    "bit2",
                    "bit3",
                    "bit4",
                    "bit5",
                    "bit6",
                    "bit7",
                    "bit8",
                    "bool",
                };
        /// <summary>
        /// 只作为保存界面临时配置用
        /// </summary>
        private TotEvent tEvent;
        /// <summary>
        /// 只作为编辑地图标记是否可见配置用
        /// </summary>
        public List<Sprite> defVisibleSpriteList=new List<Sprite>();
        public List<Sprite> visibleSpriteList
        {
            get
            {
                if (map!=null)
                {
                    return map.visibleSpriteList;
                }

                return defVisibleSpriteList;
            }
        }

        public TotEvent TEvent
        {
            get
            {
                if(tEvent==null)
                {
                    tEvent = new TotEvent();
                }
                return tEvent;
            }

            set
            {
                tEvent = value;
            }
        }

        public TotMap GetMapByPath(string configpath,int levelId=0)
        {
            if (!File.Exists(configpath))
            {
                return null;
            }
            var nConfig = AssetDatabase.LoadMainAssetAtPath(configpath) as TotEventConfig;
            if (nConfig == null || nConfig.maps == null)
            {
                return null;
            }
            TotMap m = null;
            if(nConfig.maps.Count>0)
            {
                m = nConfig.maps[nConfig.maps.Count-1];
            }
            //var m = nConfig.maps.Find((te) =>
            //{
            //    return (te.id == levelId);
            //});
            if(m!=null)
            {
                m.root = nConfig;
            }
            return m;

        }


        public TotMap GetMap(int mapid)
        {
            string configpath = GetMapPath(mapid);
            var m = GetMapByPath(configpath);
            return m;
        }

        public void UpdateMaxIndex(int mapid, int maxIndex)
        {
            if (map != null)
            {
                string configpath = GetMapPath(mapid);
                if (File.Exists(configpath))
                {
                    this.mapid = mapid;
                    this.map.maxIndex = maxIndex;
                    AssetDatabase.Refresh();
                }

            }

        }
        public void AddMap(int mapid)
        {
            //if (map.id == mapid)
            //{
            //    var tmap = maps.Find((m) =>
            //    {
            //        return m.id == map.id;
            //    });
            //    if(map==tmap)
            //    {
            //        return;
            //    }
            //}

            //var eInd = maps.FindIndex((te) =>
            //{
            //    return (te.id == mapid);
            //});
            //if (eInd > -1)
            //{
            //    map = maps[eInd];
            //}
            //else
            this.mapid = mapid;

            if(map==null)
            {
                string configpath = GetMapPath(mapid);
                if (!File.Exists(configpath))
                {
                    var newConfig = ScriptableObject.CreateInstance<TotEventConfig>();

                    EditorHelp.CheckDir(configpath);
                    AssetDatabase.CreateAsset(newConfig, configpath);
                    AssetDatabase.Refresh();
                }
                var nConfig = AssetDatabase.LoadMainAssetAtPath(configpath) as TotEventConfig;

                var m = new TotMap()
                {
                    id = mapid
                };
                nConfig.maps = nConfig.maps ?? new List<TotMap>();
                nConfig.maps.Add(m);
            }
        }

        private static string GetMapPath(int mapid)
        {
            return string.Format("{0}map{1}.asset", TOT_CONFIG_MAP_PATH, mapid);
        }

        public void RemoveEvent(int pos, List<TotEvent> evs = null)
        {
            evs = evs ?? map.events;

            var tmap = map;
            if (tmap == null)
            {
                return;
            }
            var eInd = evs.FindIndex((te) =>
            {
                return (te.index == pos);
            });
            if (eInd > -1)
            {
                evs.RemoveAt(eInd);
                TEvent = TEvent.clone();
            }

        }
        public void SyncEvent(int pos,List<TotEvent> evs = null) {
            evs = evs ?? map.events;
            var ttEvent  = map.events.Find((te) =>
            {
                return (te.index == pos);
            });
            if(ttEvent!=null)
            {
                TEvent = ttEvent.clone();
            }
            else
            {
                TEvent.props.Clear();
                TEvent.index = pos;
            }
        }
        public void UpdateEvent(int pos, string key, string value)
        {
            if (pos == -1)
            {
                TEvent[key] = value;
                return;
            }

            var tmap = map;
            if (tmap == null)
            {
                return;
            }
            var ttEvent = tmap.events.Find((te) =>
            {
                return (te.index == pos);
            });
            if (ttEvent == null)
            {
                ttEvent = TEvent.clone();
                ttEvent.index = pos;
                tmap.events.Add(ttEvent);
            }
            if (key == EVENT_KEY_NAME)
            {
                SetSid(value);
            }
            ttEvent[key] = value;
        }
        public void UpdateIndState(int pos, string key, string value)
        {
            if (pos == -1)
            {
                TEvent[key] = value;
                return;
            }

            var tmap = map;
            if (tmap == null)
            {
                return;
            }
            var ttEvent = tmap.indexStates.Find((te) =>
            {
                return (te.index == pos);
            });
            if (ttEvent == null)
            {
                ttEvent = TEvent.clone();
                ttEvent.index = pos;
                tmap.indexStates.Add(ttEvent);
            }
            ttEvent[key] = value;
        }

        void SetSid(string sid)
        {
            if (!string.IsNullOrEmpty(sid))
            {
                TEvent[EVENT_KEY_NAME] = sid;
                var id = 0;
                int.TryParse(sid, out id);
                var spv = sprites.Find(da => da.id == id);
                TEvent["type"] = spv != null ? spv.type.ToString() : "0";
            }

        }

        public TotEvent GetTotEvent(int index,bool dealEvent=true)
        {
            if (!dealEvent) return GetTotIndState(index);
            var m = map;
            if (map == null) return null;
            var te = map.events.Find(e => e.index == index);
            return te;
        }
        public TotEvent GetTotIndState(int index)
        {
            var m = map;
            if (map == null) return null;
            var te = map.indexStates.Find(e => e.index == index);
            return te;
        }
        //public int ConvertToByte(TotEvent te)
        //{
        //    te.props
        //    return 0;
        //}

        public class ModuleVO
        {
            public int key;
            public string value;
            public int type;
        }
        [Button("从Event表中更新所有新的事件")]
        public void fillSprites()
        {
            var go = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Maps/ToT/tot_tool/tot_tool.prefab");
            var luaScr = go.GetComponent<LuaBehaviourInEditor>();
            //luaScr.ExeScript("");
            var list = new List<string>();
            //foreach (var e in events)
            //{
            //    list.Add(e.id.ToString());
            //}
            var command = new Dictionary<string, object>();
            command["cmd"] = "get_event_res";
            command["param"] = list;

            var returns = luaScr.ExeScript(string.Format("return onevent(\'{0}\')", UIHelper.ToJson(command, 0)));
            Debug.Log(returns);
            var returnstr = returns[0].ToString();
            Debug.Log(returnstr);

            var spList = UIHelper.ToObj<List<ModuleVO>>(returnstr);
            sprites.Clear();
            foreach (var p in spList)
            {
                var sv = new SpriteVO()
                {
                    id = p.key,
                    type = p.type,
                };
                sprites.Add(sv);
                var ass = AssetDatabase.GetAssetPathsFromAssetBundle(p.value);
                foreach (var assp in ass)
                {
                    var sGo = AssetDatabase.LoadAssetAtPath<GameObject>(assp);
                    if (!sGo) continue;
                    var spr = sGo.GetComponentsInChildren<SpriteRenderer>();
                    if (spr.Length > 0)
                    {
                        sv.sprite = spr[0].sprite;
                    }
                    else
                    {
                        Debug.LogFormat(sGo, "find no SpriteRenderer in {0}", p.key);
                    }
                }
            }
        }
        [Button("清除无用临时事件配置")]
        public void ClearTemp()
        {
            var sps = sprites;
            var list = new List<int>();
            int count = sps.Count;
            for (int i = 0; i < count; i++)
            {
                SpriteVO sp = sps[i];
                if (sp.sprite) continue;
                list.Add(i);
            }

            Debug.Log("总共失效:" + UIHelper.ToJson(list));
            for (int i = list.Count - 1; i >= 0; i--)
            {
                sps.RemoveAt(i);
            }
            SaveAssets();
        }
        public int ID_PRESERVE_NUM = 100000;
        [Button("生成PeakOfTimeRefresh表")]
        public void BuildCsv()
        {
            //var mmap = maps.Find((te) =>
            //{
            //    return (te.id == map.id);
            //});
            //if (mmap == null) return;
            List<TotEvent> evs = new List<TotEvent>();

            var mapfiles = Directory.GetFiles(TOT_CONFIG_MAP_PATH, "*.asset");

            var maps = new List<TotMap>();
            foreach (var mfile in mapfiles)
            {
                var map = GetMapByPath(mfile);
                if(map!=null)
                {
                    maps.Add(map);
                }
            }

            foreach (var mmap in maps)
            {
                var mapid = mmap.id + "";
                List<TotEvent> curEvents = new List<TotEvent>();
                foreach (var ev in mmap.events)
                {
                    int _maxvalue = mmap.maxIndex == 0 ? int.MaxValue : mmap.maxIndex;
                    if (_maxvalue > ev.index)
                    {
                        ev["iLevelID"] = mapid;
                        ev["ID"] = (mmap.id * ID_PRESERVE_NUM + ev.index) + "";
                        curEvents.Add(ev);
                    }
                    else
                    {
                        Debug.LogError("Map"+ mmap.id + "事件："+ ev["sid"] + " -> Index = ["+ ev.index + "]超出格子最大范围["+ _maxvalue + "]！将屏蔽处理！");
                    }
                }
                evs.AddRange(curEvents);
                if (curEvents.Count == 0) continue;
            }

            Dictionary<string, bool> dic = new Dictionary<string, bool>();

            List<DisplayKey> keylist = new List<DisplayKey>();
            foreach (var da in displayAtts)
            {
                foreach (var att in da.att)
                {
                    if (dic.ContainsKey(att.keyName)) continue;
                    dic[att.keyName] = true;
                    keylist.Add(att);
                }
            }

            keylist.RemoveAll(
                (dk) =>
                string.IsNullOrEmpty(dk.keyName)
            || string.IsNullOrEmpty(dk._scope)
            );
            evs.RemoveAll(
                (dk) =>
                dk[EVENT_KEY_NAME] == "-1"
            );
            List<string> c = new List<string>
            {
                "name",
                "type",
                "desc",
                "filter",
                "array",
                "arraytype",
                "index"
            };
            Dictionary<string, System.Func<DisplayKey, string>> func = new Dictionary<string, System.Func<DisplayKey, string>>()
            {
                { "name",
                    (att)=> {
                        return string.IsNullOrEmpty(att.csvName) ? att.keyName : att.csvName;
                    }
                },
                { "type",
                    (att)=> {
                        return   att._vtype;
                    }
                },
                { "desc",
                    (att)=> {
                        return   att.des + att._tooltips;
                    }
                },
                { "filter",
                    (att)=> {
                        return att._scope;
                    }
                },
                { "array",
                    (att)=> {
                        var key = string.IsNullOrEmpty(att._arrtype) ? "" : att._scope;
                        return key;
                    }
                },
                { "arraytype",
                    (att)=> {
                        return att._arrtype;
                    }
                },
                { "index",
                    (att)=> {
                        var key = string.IsNullOrEmpty(att.csvName) ? att.keyName : att.csvName;
                        if(mainkeys.IndexOf(key)>-1)return "1";
                        return "";
                    }
                },

            };
            StringBuilder sb = new StringBuilder();
            var bAddPuntation = false;

            for (int i = 0; i < c.Count; i++)
            {
                var tag = c[i];
                bAddPuntation = false;
                sb.Append("{" + tag + "}");
                var fun = func[tag];
                for (int j = 0; j < keylist.Count; j++)
                {
                    if (bAddPuntation)
                    {
                        sb.Append(",");
                        bAddPuntation = false;
                    }
                    var att = keylist[j];
                    var key = fun(att);
                    sb.Append(key);
                    bAddPuntation = true;
                }
                sb.Append("\r\n");
            }
            bAddPuntation = false;

            for (int iE = 0; iE < evs.Count; iE++)
            {
                var ev = evs[iE];
                bAddPuntation = false;

                for (int i = 0; i < keylist.Count; i++)
                {
                    var att = keylist[i];

                    if (bAddPuntation)
                    {
                        sb.Append(",");
                        bAddPuntation = false;
                    }
                    var key = ev[att.keyName];
                    sb.Append(key);
                    bAddPuntation = true;
                }
                sb.Append("\r\n");
            }
            EditorHelp.CheckDir(MAP_REFRESH_PATH);
            try
            {
                File.WriteAllText(MAP_REFRESH_PATH, sb.ToString(), System.Text.Encoding.GetEncoding("gb2312"));
                //File.WriteAllText(MAP_REFRESH_PATH, sb.ToString());
                if (EditorUtility.DisplayDialog("导出配置", "已生成PeakOfTimeRefresh表！\n是否打开配置表目录?", "打开"))
                {
                    EditorHelp.ExplorePath(Instance.MAP_REFRESH_PATH, true);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError(e.ToString());
                if (!EditorUtility.DisplayDialog("Warning", "请检查PeakOfTimeRefresh表是否已经打开,请关闭后重试", "关闭"))
                {
                    //BuildCsv();
                }
            }
            Debug.Log(sb.ToString());
            AssetDatabase.Refresh();
        }
        [LabelText("PeakOfTimeRefresh表主键指定")]
        public List<string> mainkeys = new List<string>()
            {
                "ID",
            };
        [LabelText("PeakOfTimeRefresh表生成路径")]
        public string MAP_REFRESH_PATH = "../../Tools/csv_script/PeakOfTimeRefresh.csv";

        [LabelText("地图行走区域数据生成路径")]
        public string MAP_BYTES_PATH = "../../Bin/Server/BattleServer/Data/peakmap/level{0}.bytes";
        [LabelText("地图行走区域数据客户端生成路径")]
        public string MAP_BYTES_CLI_PATH = "Assets/Art/Maps/ToT/res/level{0}.bytes";

        //public string MAP_REFRESH_PATH = "/Assets/Art/Maps/ToT/res/event.csv";
        [Button("打开表目录")]
        public void OpenCsvFolder()
        {

            try
            {
                var path = Path.GetFullPath(Application.dataPath + "/../" + MAP_REFRESH_PATH);
                path = path.Replace("/", "\\");
                System.Diagnostics.Process.Start("explorer.exe", path);
            }
            catch (System.ComponentModel.Win32Exception e)
            {
                // tried to open win explorer in mac
                // just silently skip error
                // we currently have no platform define for the current OS we are in, so we resort to this
                e.HelpLink = ""; // do anything with this variable to silence warning about not using it
            }
        }


        [Button("SaveProject")]
        public void SaveAssets()
        {
            EditorUtility.SetDirty(this);
            if (map != null)
            {
                EditorUtility.SetDirty(map.root);
            }
            AssetDatabase.SaveAssets();
        }
        [Button("SplitMap")]
        public void SplitMap()
        {
            foreach (var map in maps)
            {
                AddMap(map.id);
                GetMap(map.id).root.maps = new List<TotMap>()
                {
                    map
                };
                this._map = null;
                EditorUtility.SetDirty(GetMap(map.id).root);
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }
        [Button("ExtendWidth")]
        public void ExtendWidth()
        {
            //var preserve = 100000;
            foreach (var map in maps)
            {
                foreach (var ev in map.events)
                {
                    //int count = ev.props.Count;
                    //for (int i = 0; i < count; i++)
                    //{
                    //    var p = ev.props[i];

                    //}
                    if (string.IsNullOrEmpty(ev["arrRelateRefreshID"])) continue;
                    List<string> iarrRelateRefreshIDs = new List<string>();

                    var sps = ev["arrRelateRefreshID"].Split('#');

                    for (int i = 0; i < sps.Length; i++)
                    {
                        var iarrRelateRefreshID = -1;

                        if (int.TryParse(sps[i], out iarrRelateRefreshID))
                        {
                            if (iarrRelateRefreshID > ID_PRESERVE_NUM) continue;
                            var mapid = iarrRelateRefreshID / 1000;
                            var ind = iarrRelateRefreshID % 1000;
                            iarrRelateRefreshIDs.Add((mapid * ID_PRESERVE_NUM + ind)+"");
                        }
                    }

                    ev["arrRelateRefreshID"] = string.Join("#", iarrRelateRefreshIDs.ToArray());
                }
            }
            SaveAssets();
        }
    }

}
#endif
