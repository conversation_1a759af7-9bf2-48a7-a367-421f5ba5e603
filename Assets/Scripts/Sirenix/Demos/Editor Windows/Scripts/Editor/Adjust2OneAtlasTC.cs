#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.U2D;
using System.IO;
using UnityEditor.U2D;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class Adjust2OneAtlasTC
    {
        [HideInInspector]
        public const string TIPS = "Adjust2OneAtlasTC（将选择的精灵设置为一个图集）";

        public string newAtlasName = "newAtlasName";

        public List<Object> layoutList = new List<Object>();

        public List<SpriteAtlas> atlases = new List<SpriteAtlas>();

        [Button]
        public void Filter()
        {
            List<string> tags = new List<string>();
            HashSet<string> sprites = new HashSet<string>();

            foreach (var layout in layoutList)
            {
                var deps = AssetDatabase.GetDependencies(AssetDatabase.GetAssetPath(layout),true);

                "".Print(layout.name, "deps", UIHelper.ToJson(deps));

                foreach (var dep in deps)
                {
                    if(dep.StartsWith("Assets/UI/"))
                    if(dep.EndsWith(".png"))
                    {
                         sprites.Add(dep);
                    }
                }
                "".Print(layout.name, "sprites", sprites.Count, UIHelper.ToJson(sprites));
            }

            var ui_atals_root = CreateSpriteAtlas.ui_atlas_root;

            var atlas = Directory.GetFiles(ui_atals_root, "*.spriteatlas", SearchOption.AllDirectories);
            List<Object> spriteobs = new List<Object>();

            foreach (var sp in sprites)
            {
                spriteobs.Add(AssetDatabase.LoadAssetAtPath<Sprite>(sp));
            }

            //var dicExclude = new HashSet<SpriteAtlas>();
            //foreach (var at in atlases)
            //{
            //    dicExclude.Add(at);
            //}

            var sprite_rm_list = spriteobs.ToArray();
            "".Print( "sprite_rm_list", sprite_rm_list.Length, UIHelper.ToJson(sprite_rm_list));
            foreach (var atl in atlas)
            {
                var sa = AssetDatabase.LoadMainAssetAtPath(atl) as SpriteAtlas;
                //if (dicExclude.Contains(sa)) continue;
                SpriteAtlasExtensions.Remove(sa,sprite_rm_list);
            }

            List<string> listSprite = new List<string>(sprites);
            var spa = CreateSpriteAtlas.CreateAtlasBySprites(newAtlasName, listSprite, true);


            foreach (var sp in listSprite)
            {
                var ai = AssetImporter.GetAtPath(sp);
                if (ai)
                {
                    var aai = ai as TextureImporter;
                    aai.spritePackingTag = newAtlasName;
                    //aai.SaveAndReimport();
                }
            }
            UIHelper.ImportAssets(listSprite.ToArray());

            AssetDatabase.SaveAssets();
        }
    }
}

#endif