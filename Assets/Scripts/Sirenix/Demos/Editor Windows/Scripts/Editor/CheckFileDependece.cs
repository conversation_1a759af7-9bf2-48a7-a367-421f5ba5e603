#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine.Profiling;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using UnityEngine;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class CheckFileDependece : GlobalConfig<CheckFileDependece>
    {
        [Serializable]
        public class Proto 
        {
            [HideLabel]
            [HorizontalGroup]
            public string name;

            [HorizontalGroup]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }

        }


        [Serializable]
        public class StrProto : Proto
        {
            [HideLabel]
            [HorizontalGroup]
            public string abname;
            [HideLabel]
            [HorizontalGroup]
            public string  format;  //格式

            [HorizontalGroup]
            public int size;

            [HorizontalGroup]
            public int count;

            [HideLabel]
            [HorizontalGroup]
            public int width;

            [HideLabel]
            [HorizontalGroup]
            public int height;
            
            [HideLabel]
            [HorizontalGroup]
            public int sizeMulCount;//大小乘引用数，运行时所占内存总大小


            [HideLabel]
            [HorizontalGroup]
            public int widthMulHeight;//大小乘引用数，运行时所占内存总大小

            [HideLabel]
            [HorizontalGroup]
            public bool mipMap; //是否开启mipmap

            [HideLabel]
            [HorizontalGroup]
            public bool rW; //是否开启像素读写

            public List<Proto> dependencies;
            
            [HorizontalGroup]
            [Button]
            void LogDependencies()
            {
                List<string> dependenciesStr = null;
                
                Debug.Log(name + CheckFileDependece.Instance.dependenciesList.Count);
                
                if (CheckFileDependece.Instance.dependenciesList.TryGetValue(name, out dependenciesStr))
                {
                    dependencies = new List<Proto>();
                    string log = "";
                    foreach(string value in dependenciesStr)
                    {
                        dependencies.Add(new Proto{name = value});
                        log = log + "\n" + value;
                    }
                    Debug.Log(log);
                }
            }

        }
        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/Art/Effects/Textures",
        };

        public List<string> dependenciesAssets = new List<string>{
            "Assets/Art/Effects",
        };
        [InfoBox("size==0:没有外部引用")]
        public List<StrProto> fList = new List<StrProto>();
            
        //[InfoBox("引用图片列表")]
        //public List<string> dependencies = new List<string>();
        public Dictionary<string, List<string>> dependenciesList = new Dictionary<string, List<string>>();

        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);
        [Button]
        void Filter()
        {
            var fas = AssetDatabase.FindAssets("", findassets.ToArray());
            dependenciesList = new Dictionary<string, List<string>>();
            var abnames = new Dictionary<string, int>();
            //Debug.Log("所有文件列表：" + string.Join("/n", fas));
            ToolUti.Iter(fas, (fa) =>
            {
                var ap = AssetDatabase.GUIDToAssetPath(fa);
                var ai = AssetImporter.GetAtPath(ap);
                if (isPng(ap))
                {
                    //GetTextureSizeBy(ap);
                    abnames[ap] = 0;
                    dependenciesList[ap] = new List<string>();
                    Debug.Log("长度" + ap + name + dependenciesList.Count);
                }

                //if (string.IsNullOrEmpty(ai.assetBundleName)) return ap;
                //abnames[ai.assetBundleName] = 0;
                return ap;
            }, (Action)(() => {
                var allnames = AssetDatabase.FindAssets("", dependenciesAssets.ToArray());

                ToolUti.Iter(allnames, (abname) =>
                {
                    //var abs = AssetDatabase.GetAssetBundleDependencies(abname, true);
                    var ap = AssetDatabase.GUIDToAssetPath(abname);
                    var ai = AssetImporter.GetAtPath(ap);
                    if (string.IsNullOrEmpty(ai.assetBundleName))
                    {
                        //Debug.Log("被依赖文件：" + ap + "没有abname!!!!!!!!!!!!!!!!!!!!!!!");
                        return ap;
                    }
                    var abs = AssetDatabase.GetDependencies(ap, true);
                    foreach (var ab in abs)
                    {
                        var num = 0;
                        //Debug.Log(abname + "依赖项：" + ab);
                        if (ab == ap)
                        {
                            continue;
                        }

                        if (abnames.TryGetValue(ab, out num))
                        {
                            if (dependenciesList[ab].IndexOf(ap) < 0)
                            {
                                abnames[ab] = num + 1;
                                dependenciesList[ab].Add(ap);
                            }
                            //if (abnames[ab] >= 2)
                            //{
                            //    Debug.LogError(ab +"---- \n" + string.Join("\n", dependenciesList[ab].ToArray()));
                            //}
                        }
                    }
                    return ap;
                }, (Action)(() => {
                    Debug.Log(UIHelper.ToJson(abnames));
                    fList.Clear();
                    foreach (var abn in abnames.Keys)
                    {
                        //var ai = AssetImporter.GetAtPath(abn);
                        TextureImporter ai = AssetImporter.GetAtPath(abn) as TextureImporter;
                        var texture = AssetDatabase.LoadAssetAtPath<Texture2D> (abn) as Texture2D;
                        var abName = ai.assetBundleName;
                        var format = GetTextureFormmat(abn);
                        var size = GetTextureSizeBy(abn);
                        fList.Add(new StrProto()
                        {
                            name = abn,
                            abname = abName,
                            format = format.ToString(),
                            count = abnames[abn],
                            size = size,
                            mipMap = ai.mipmapEnabled,
                            rW = ai.isReadable,
                            sizeMulCount = size * abnames[abn],
                            width = texture.width,
                            height = texture.height,
                            widthMulHeight = texture.width * texture.height,
                        });

                    }
                }), "Find Dependence");
            }), "Find Tex");
        }



        TextureImporterFormat GetTextureFormmat(string assetPath)
        {
            TextureImporter ai = AssetImporter.GetAtPath(assetPath) as TextureImporter;
            TextureImporterPlatformSettings platFormat = ai.GetPlatformTextureSettings("Android");
            return platFormat.format; //压缩格式一个像素对应的byte大小
        }

        int GetTextureSizeBy(string assetPath)
        {
            var texture = AssetDatabase.LoadAssetAtPath<Texture2D> (assetPath) as Texture2D;
			if (texture == null) 
            {
                return 0;
			}
            //long memoryUsed = Profiler.GetRuntimeMemorySizeLong(texture); //获取图片内存占用
            //float newMemoryUsed  = memoryUsed / 1024; //转为kb
            TextureImporter ai = AssetImporter.GetAtPath(assetPath) as TextureImporter;
            TextureImporterPlatformSettings platFormat = ai.GetPlatformTextureSettings("Android");
            float onePixelSize = GetOnePixelSizeByFormat(platFormat.format); //压缩格式一个像素对应的byte大小
            int size = (int)(texture.width * texture.height * onePixelSize);
            //Debug.Log(assetPath + "一个像素size:" + onePixelSize + "压缩格式" + platFormat.format + " 总size:" + size/1024 + "kb");
            return size;
        }

        //获取压缩格式对应的一个像素点大小
        float GetOnePixelSizeByFormat(TextureImporterFormat formatType)
        {
            switch (formatType)
            {

                case TextureImporterFormat.ARGB32: return 4;
                case TextureImporterFormat.ARGB16: return 2;
                case TextureImporterFormat.RGB24: return 3f;
                case TextureImporterFormat.RGB16: return 2f;
                
                case TextureImporterFormat.ETC_RGB4: return 0.5f;
                case TextureImporterFormat.ETC2_RGB4: return 0.5f;
                case TextureImporterFormat.ETC2_RGBA8: return 1f;
                case TextureImporterFormat.ETC2_RGB4_PUNCHTHROUGH_ALPHA: return 0.5f;
                
                case TextureImporterFormat.PVRTC_RGB2: return 0.25f;
                case TextureImporterFormat.PVRTC_RGB4: return 0.5f;
                case TextureImporterFormat.PVRTC_RGBA2: return 0.25f;
                case TextureImporterFormat.PVRTC_RGBA4: return 0.5f;

                case TextureImporterFormat.AutomaticCompressed: return 1f; //自动压缩默认选择etc2,8bit,1byte
            }
            return 4;
        }



        [Button("按abName和引用数排序")]
        void SortabNameAndDep()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { 
                
                if (f2.abname == "" && f1.abname == "")
                {
                    return (int)(f2.count - f1.count);
                }
                else if (f2.abname == "")
                {
                    return 1;
                }
                else if (f1.abname == "")
                {
                    return -1;
                }
                return (int)(f2.count - f1.count);
                }));
        }

        [Button("按abName和大小排序")]
        void SortabNameAndSize()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { 
                
                if (f2.abname == "" && f1.abname == "")
                {
                    return (int)(f2.size - f1.size);
                }
                else if (f2.abname == "")
                {
                    return 1;
                }
                else if (f1.abname == "")
                {
                    return -1;
                }
                return (int)(f2.size - f1.size);
                }));
        }

        [Button("按abName,大小和引用排序")]
        void SortabNameAndSizeAndDep()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { 
                
                if (f2.abname == "" && f1.abname == "")
                {
                    return (int)(f2.size * f2.count - f1.size * f1.count);
                }
                else if (f2.abname == "")
                {
                    return 1;
                }
                else if (f1.abname == "")
                {
                    return -1;
                }
                return (int)(f2.size * f2.count - f1.size * f1.count);
                }));
        }


        [Button("按abName,图片尺寸，大小和引用排序")]
        void SortabNameAndSizeAndDepAndWH()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { 
                
                var texture1 = AssetDatabase.LoadAssetAtPath<Texture2D> (f1.name) as Texture2D;
                int texture1Size = 0;
                if (texture1)
                {
                    texture1Size = texture1.width * texture1.height;
                }
                var texture2 = AssetDatabase.LoadAssetAtPath<Texture2D> (f2.name) as Texture2D;
                int texture2Size = 0;
                if (texture2)
                {
                    texture2Size = texture2.width * texture2.height;
                }
                //排序优先级 abname为空，图片尺寸，内存大小和引用乘积
                if (f2.abname == "" && f1.abname == "")
                {
                    if (texture1Size == texture2Size)
                    {
                        return (int)(f2.size * f2.count - f1.size * f1.count);
                    }

                    return texture2Size - texture1Size;

                }
                else if (f2.abname == "")
                {
                    return 1;
                }
                else if (f1.abname == "")
                {
                    return -1;
                }

                //文件abname都不为空的处理
                if (texture1Size == texture2Size)
                {
                    return (int)(f2.size * f2.count - f1.size * f1.count);
                }

                return texture2Size - texture1Size;
                }));
        }

        
        [Button("按引用非0， abName非空, 图片尺寸，大小排序")]
        void SortDepAndabNameAndSizeAndWH()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => {
                var texture1 = AssetDatabase.LoadAssetAtPath<Texture2D> (f1.name) as Texture2D;
                int texture1Size = texture1.width * texture1.height;
                var texture2 = AssetDatabase.LoadAssetAtPath<Texture2D> (f2.name) as Texture2D;
                int texture2Size = texture2.width * texture2.height;
                
                if (f1.count > 0 && f2.count > 0 && f2.abname == "" && f1.abname == "")
                {
                    if (texture1Size == texture2Size)
                    {
                        return (int)(f2.size * f2.count - f1.size * f1.count);
                    }

                    return texture2Size - texture1Size;
                }
                else if (f2.count > 0 && f2.abname == "")
                {
                    return 1;
                }
                else if (f1.count > 0 && f1.abname == "")
                {
                    return -1;
                }
                
                //排序优先级 abname为空，图片尺寸，内存大小和引用乘积
                if (f2.abname == "" && f1.abname == "")
                {
                    if (texture1Size == texture2Size)
                    {
                        return (int)(f2.size * f2.count - f1.size * f1.count);
                    }

                    return texture2Size - texture1Size;
                }
                else if (f2.abname == "")
                {
                    return 1;
                }
                else if (f1.abname == "")
                {
                    return -1;
                }

                //文件abname都不为空的处理
                if (texture1Size == texture2Size)
                {
                    return (int)(f2.size * f2.count - f1.size * f1.count);
                }

                return texture2Size - texture1Size;
                }));
        }


        [Button("按外部引用排序")]
        void SortDependence()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { return (int)(f2.count - f1.count); }));
        }
        [Button("按文件大小排序")]
        void SortSize()
        {
            for (int i = 0; i < fList.Count; i++)
            {
                var fl = fList[i];
                fl.size = GetTextureSizeBy(fl.name);
            }
            fList.Sort((Comparison<StrProto>)((f1, f2) => { return (int)(f2.size - f1.size); }));
        }
        [Button("按文件大小*外部引用排序")]
        void SortSizeXDependence()
        {
            fList.Sort((Comparison<StrProto>)((f1, f2) => { return (int)(f1.size * f1.count - f2.size * f2.count); }));
        }
        [OnValueChanged("CalTotalInd")]
        public int ind;
        public int totalInd;
        public int totalSouceSize;
        [Button("前ind个文件大小*外部引用汇总")]
        void CalTotalInd()
        {
            var len = Mathf.Min(ind, fList.Count);
            var total = 0;
            var souceSize = 0;
            for (int i = 0; i < len; i++)
            {
                var fl = fList[i];
                total += fl.size * fl.count;
                souceSize += fl.size;
            }
            totalInd = total / 1024;
            totalSouceSize = souceSize / 1024;
            Debug.Log("总占用内存" + totalInd + "资源原始内存" + totalSouceSize);
        }

        [Button("打印到日志框")]
        void PrintBoard()
        {
            for (int i = 0; i < fList.Count; i++)
            {
                blackboard += string.Format("{0}\t{1}\t{2}\t{3}\t{4}\t{5}\t{6}\t{7}\t{8}\t{9}\t{10}\n", 
                fList[i].name, fList[i].abname != "", fList[i].size, fList[i].format, fList[i].width, fList[i].height,
                fList[i].widthMulHeight, fList[i].count, fList[i].sizeMulCount,fList[i].mipMap, fList[i].rW);
            }

            Debug.Log(blackboard);
        }

        [Button("批量设置贴图abname")]
        void setTextureAbnameOfInput()
        {
            Debug.Log(blackboard);
            blackboard = blackboard.Replace("\r", "");
            string[] result = blackboard.Split(new string[] {"\n"}, StringSplitOptions.None);

            int length = result.Length;
            if (length <= 0)
            {
                return;
            }
            string itemPath;
            foreach (var item in result)
            {
                Debug.Log("itemPath:" + item);
            }
            for (int i = length - 1; i >= 0 ; i--)
            {
                itemPath = result[i];
                if (string.IsNullOrEmpty(itemPath))
                {
                    continue;
                }
                TextureImporter ai = AssetImporter.GetAtPath(itemPath) as TextureImporter;
                string assetBundleName = itemPath.Substring(itemPath.IndexOf("/") + 1);
                Debug.Log("itemPath:" + itemPath + "设置abname:" + assetBundleName);
                Debug.Log("itemPath abname: " + ai.assetBundleName);
			    ai.assetBundleName = assetBundleName;
                
            }

        }
        [TextArea]
        public string blackboard = "";
    }
}
#endif
