#if UNITY_EDITOR
//导出正式特效资源
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEngine.UI;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.IO;
    using System.Security.Cryptography;
    using System.Collections.Generic;
    using System;
    using System.Linq;
    using War.UI;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ExtractCommonUI : GlobalConfig<ExtractCommonUI>
    {
        #region 检查冗余
        [FoldoutGroup("ExtractCommonUI")]
        [FolderPath]
        public string originalCommonPath = "Assets/UI";

        [FoldoutGroup("ExtractCommonUI")]
        public int curReservedCount = 1;

        [FoldoutGroup("ExtractCommonUI")]
        public List<string> originalCommonPackingTags = new List<string>();

        [FoldoutGroup("ExtractCommonUI")]
        public List<string> ignoreCheckPackingTags = new List<string>();

        [FoldoutGroup("ExtractCommonUI")]
        public List<string> ignoreCheckUIFiles = new List<string>();

        [FoldoutGroup("ExtractCommonUI")]
        [FolderPath]
        public string checkPath = "";

        [FoldoutGroup("ExtractCommonUI")]
        [FolderPath]
        public string targetExportPath = "";

        [FoldoutGroup("ExtractCommonUI")]
        [FolderPath]
        public string prefabDependenciesPath = "";

        // [TextArea]
        // public string blackboard = "";

        // //[Bool]
        // [InfoBox("是否创建重复sprite的引用资源对象")]
        // [HideLabel]
        // public bool isCreateSameSpriteQuoteAsset = false; //是否创建重复sprite的引用资源对象，引用目标sprite为程序第一个遍历到的同md5文件

        // //[HorizontalGroup]
        // [InfoBox("是否删除已创建引用spriteAsset文件的原始文件")]
        // [HideLabel]
        // public bool isDeleteSameSprite = false; //是否删除已创建引用spriteAsset文件的原始文件

        // //[HorizontalGroup]
        // [InfoBox("是否替换cardAssets中重复的sprite")]
        // [HideLabel]
        // public bool isReplaceSameCardAssets = false; //是否替换cardAssets中重复的sprite
        //[FoldoutGroup("ExportEffectAssets")]
        //public List<ResObject> dependencies = new List<ResObject>();
        [FoldoutGroup("ExtractCommonUI")]
        //需要筛选的后缀名
        public List<string> exts = new List<string>();

        // public Dictionary<string, Rect> textureAtlasRects = new Dictionary<string, Rect>();

        private string currentAtlasTexture = "";
        [FoldoutGroup("ExtractCommonUI")]
        public Dictionary<string, string> extAndTargetPath = new Dictionary<string, string>() {
                { ".fbx", "Models" },
                { ".anim", "Animations" },
                { ".controller", "Animations" },
                { ".mat", "Materials" },
                { ".prefab", "Prefabs" },
                { ".tga", "Textures" },
                { ".png", "Textures" },
            };

        private Dictionary<UnityEngine.Object, string> newAssets = new Dictionary<UnityEngine.Object, string>();
        private List<ExportCSV.PatientStatisticsOutputDto> saveDeleteImgPTList = new List<ExportCSV.PatientStatisticsOutputDto>();
        private List<ExportCSV.PatientStatisticsOutputDto> saveResourcesImgPTList = new List<ExportCSV.PatientStatisticsOutputDto>();
        private ExportCSV exportCSV = new ExportCSV();
        private Dictionary<string, List<string>> commonImageMD5 = new Dictionary<string, List<string>>();
        // [ShowInInspector]
        // public List<string> newAssetList = new List<string>();


        [FoldoutGroup("ExtractCommonUI")]
        [Button("开始提取资源")]
        void startExtractCommonUI()
        {
            newAssets.Clear();
            saveDeleteImgPTList.Clear();
            saveResourcesImgPTList.Clear();
            commonImageMD5.Clear();
            // string fileInfos = "";
            // long allfileCount = 0;
            Debug.Log("checkPath:" + checkPath);
            GetCommonPathMD5();
            // dependencies.Clear();
            StartCheckImageOfSameMd5(checkPath);
            // GameObject.StartCoroutine(StartCheckAllFile(checkPath));
            // string directorySizeInfo = string.Format("{0}\t{1}\t{2}\t\tFile folder\t{3}", checkPath, allSize,
            //  EditorUtility.FormatBytes(allSize), allfileCount);
            // fileInfos = string.Format("{0}\n{1}", directorySizeInfo, fileInfos);
            // blackboard = fileInfos;
        }

        /// <summary>
        /// 收集公共文件夹图片资源
        /// </summary>
        private void GetCommonPathMD5()
        {
            EditorUtility.DisplayProgressBar("开始收集公共资源", "正在加载公共资源", 0);
            string[] extensions = new string[] { "png", "jpg", "jpeg" };
            List<string> files = new List<string>();
            for (int i = 0; i < extensions.Length; i++)
            {
                files.AddRange(Directory.GetFiles(originalCommonPath, "*." + extensions[i], SearchOption.AllDirectories));
            }
            if (files.Count <= 0)
            {
                EditorUtility.DisplayProgressBar("", "", 1f);
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("", "公共文件夹选取路径文件为空，请重新输入……", "确认");
                return;
            }
            EditorUtility.DisplayProgressBar("开始收集公共资源", "正在加载公共资源", 0.1f);
            for (int i = 0; i < files.Count; i++)
            {
                string path = files[i].Replace("UI/", "UI\\");
                string str = files[i].Replace("\\", "/");
                TextureImporter source_Importer = AssetImporter.GetAtPath(str) as TextureImporter;
                if (source_Importer == null || !originalCommonPackingTags.Contains(source_Importer.spritePackingTag))
                {
                    continue;
                }
                string md5 = File2MD5(path);

                if (commonImageMD5.ContainsKey(md5))
                {
                    if (commonImageMD5[md5] == null)
                    {
                        commonImageMD5[md5] = new List<string>();
                    }
                    commonImageMD5[md5].Add(path);
                }
                else
                {
                    commonImageMD5.Add(md5, new List<string>());
                    commonImageMD5[md5].Add(path);
                }
            }
            Debug.LogError("收集到公共图片(checkCommonPaths)文件夹下" + commonImageMD5.Count + "份资源");
            EditorUtility.DisplayProgressBar("", "收集公共资源完成……", 0.2f);
        }

        /// 获取预制件依赖 <summary>
        /// 
        /// </summary>
        /// <typeparam name="T">欲获取的类型</typeparam>
        /// <param name="go"></param>
        /// <returns></returns>
        static List<T> GetPrefabDepe<T>(GameObject go)
        {
            List<T> results = new List<T>();
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            foreach (UnityEngine.Object dependObj in dependObjs)
            {
                if (dependObj != null && dependObj.GetType() == typeof(T))
                {
                    results.Add((T)System.Convert.ChangeType(dependObj, typeof(T)));
                }
            }

            return results;
        }

        /// 获取预制件所有依赖 <summary>
        /// 
        /// </summary>
        /// <param name="go"></param>
        /// <returns></returns>
        static UnityEngine.Object[] GetPrefabAllDepe(UnityEngine.Object go)
        {
            UnityEngine.Object[] roots = new UnityEngine.Object[] { go };
            UnityEngine.Object[] dependObjs = EditorUtility.CollectDependencies(roots);
            return dependObjs;
        }

        public List<string> LoadImages(string dirPath)
        {
            string path = dirPath;
            List<string> files = new List<string>();
            List<Texture> images = new List<Texture>();
            string[] extensions = new string[] { "png", "jpg", "jpeg" };
            for (int i = 0; i < extensions.Length; i++)
            {
                files.AddRange(Directory.GetFiles(path, "*." + extensions[i], SearchOption.AllDirectories));

                //for (int j = 0; j < files.Count; j++)
                //{
                //    Debug.LogError("loadImages:" + j + " path:" + files[j] + " exten:" + extensions[i]);
                //}

            }
            for (int i = 0; i < files.Count; i++)
            {
                var sourceObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(files[i]) as Texture;
                images.Add(sourceObject);
            }
            List<string> ignoreCheckPaths = new List<string>();
            for (int i = 0; i < ignoreCheckPackingTags.Count; i++)
            {
                ignoreCheckPaths.Add(ignoreCheckPackingTags[i]);
            }
            string[] ss = targetExportPath.Split('/');
            for (int i = 0; i < ss.Length; i++)
            {
                if (ss[i] == "Assets" || ss[i] == "UI")
                    continue;
                ignoreCheckPaths.Add("ui." + ss[i].ToLower());
            }
            //需要跳过的图集(例如Card)
            for (int i = 0; i < files.Count; i++)
            {
                string str = files[i].Replace("\\", "/");
                for (int j = 0; j < ignoreCheckUIFiles.Count; j++)
                {
                    if (str.Contains(ignoreCheckUIFiles[j]))
                    {
                        files.RemoveAt(i);
                        i--;
                        break;
                    }
                }
                TextureImporter source_Importer = AssetImporter.GetAtPath(str) as TextureImporter;
                if (source_Importer != null)
                {
                    if (ignoreCheckPaths.Contains(source_Importer.spritePackingTag))
                    {
                        files.RemoveAt(i);
                        i--;
                    }
                }
            }
            return files;
        }

        //将制定文件内容转化为MD5
        public static string File2MD5(string path)
        {
            string res = null;
            if (File.Exists(path))
            {
                byte[] data = File.ReadAllBytes(path);
                res = GetMD5HashFromFile(data);
            }

            return res;
        }

        public static string GetMD5HashFromFile(byte[] data)
        {
            var md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
            md5.Clear();

            var destString = "";
            for (int i = 0; i < md5Data.Length; i++)
            {
                destString += string.Format("{0:x2}", md5Data[i]);
            }

            destString = destString.PadLeft(32, '0');
            return destString;
        }

        public void StartCheckImageOfSameMd5(string dirPath)
        {
            tempPath.Clear();
            //判断给定的路径是否存在,如果不存在则退出
            if (!Directory.Exists(dirPath))
            {
                // yield break;
                return;
            }
            try
            {

                Dictionary<string, List<string>> imageMd5Dic = new Dictionary<string, List<string>>();
                string md5;
                List<string> sameImages;
                List<string> allImages = LoadImages(dirPath);
                EditorUtility.DisplayProgressBar("", "检查冗余图片中……", 0.3f);
                //筛查相同md5贴图
                foreach (string item in allImages)
                {
                    md5 = File2MD5(item);
                    if (imageMd5Dic.ContainsKey(md5))
                    {
                        sameImages = imageMd5Dic[md5];
                        sameImages.Add(item); //AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(item) as Texture);
                        imageMd5Dic[md5] = sameImages;
                    }
                    else
                    {
                        sameImages = new List<string>();
                        sameImages.Add(item);// AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(item) as Texture);
                        imageMd5Dic.Add(md5, sameImages);
                    }
                }
                EditorUtility.DisplayProgressBar("", "开始合并图集……", 0.4f);
                Dictionary<string, UnityEngine.Object> sourceImages = new Dictionary<string, UnityEngine.Object>();
                float index = 0;
                //移除不重复ui
                foreach (string key in imageMd5Dic.Keys)
                {
                    index++;
                    List<string> images = imageMd5Dic[key];
                    //Debug.Log("md5key:" + key + " count: " + images.Count);
                    if (images.Count > curReservedCount)
                    {
                        //common已有，使用common里
                        string sourcePath = images[0];
                        if (commonImageMD5.ContainsKey(key))
                        {
                            sourcePath = commonImageMD5[key][0];
                        }
                        string path = CopyRes(key, sourcePath, targetExportPath);
                        //Debug.LogError("path--->" + path);
                        //continue;
                        tempPath.Add(key, path);
                        //创建公共图集ui
                        //AssetDatabase.ImportAsset(path);
                        //AssetDatabase.Refresh();
                        //设置公共贴图信息
                        //替换贴图引用
                        //获取原始贴图所有引用组件
                        foreach (string imagePath in images)
                        {
                            //Debug.Log("!!!!!!!!!!!!!!!!!!!!!imagePath:" + imagePath);
                            string assetPath = imagePath;
                            UnityEngine.Object go = AssetDatabase.LoadAssetAtPath<Sprite>(assetPath) as UnityEngine.Object;
                            if (go == null)
                            {
                                continue;
                            }
                            if (newAssets.ContainsKey(go))
                            {
                                newAssets[go] = path;
                            }
                            else
                                newAssets.Add(go, path);
                        }
                    }
                    EditorUtility.DisplayProgressBar("", "检查冗余图片引用，md5 ---->" + key + "(" + index + "/" + imageMd5Dic.Count + ")", index / (float)imageMd5Dic.Count * 0.2f + 0.4f);
                }

                //遍历预制体引用信息
                string[] paths = Directory.GetFiles(prefabDependenciesPath, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab") || s.EndsWith(".asset")).ToArray();
                int le = paths.Length;
                for (int i = 0; i < le; i++)
                {
                    string path = paths[i].Replace('\\', '/');
                    path = path.Substring(path.IndexOf("Assets/"));

                    UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(UnityEngine.Object));
                    var PrefabObject = prefab as UnityEngine.Object;
                    if (null == PrefabObject)
                    {
                        continue;
                    }
                    SetNewDependencies(path);
                    EditorUtility.DisplayProgressBar("", "更换资源引用中，path ---->" + path + "(" + i + "/" + paths.Length + ")", i / (float)paths.Length * 0.2f + 0.6f);
                }

                sourceImages.Clear();
                EditorUtility.DisplayProgressBar("", "删除冗余图片中", 0.9f);
                foreach (string key in imageMd5Dic.Keys)
                {
                    List<string> images = imageMd5Dic[key];
                    if (images.Count > curReservedCount)
                    {
                        string path = tempPath[key];
                        for (int j = 0; j < images.Count; j++)
                        {
                            if (images[j].Replace("\\", "/") != path.Replace("\\", "/"))
                            {
                                TextureImporter source_Importer = AssetImporter.GetAtPath(images[j]) as TextureImporter;
                                TextureImporter source_Importer2 = AssetImporter.GetAtPath(path) as TextureImporter;
                                ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                                patientStatisticsOutputDto.datas.Add(source_Importer.spritePackingTag);
                                patientStatisticsOutputDto.datas.Add(images[j]);
                                patientStatisticsOutputDto.datas.Add(source_Importer2.spritePackingTag);
                                patientStatisticsOutputDto.datas.Add(path);
                                saveDeleteImgPTList.Add(patientStatisticsOutputDto);
                                AssetDatabase.DeleteAsset(images[j]);
                            }
                        }
                    }
                }
                exportCSV.ExportPatientStatisticsDetails(saveDeleteImgPTList, Application.dataPath + "/Scripts/Sirenix/CSV/ExtractCommonUI删除的图片.csv", "待替换图集,待替换路径,替换后图集,替换后路径,是否删除(0删除)");
                exportCSV.ExportPatientStatisticsDetails(saveResourcesImgPTList, Application.dataPath + "/Scripts/Sirenix/CSV/ExtractCommonUI替换位置.csv", "路径名,节点名,替换前资源名,替换后资源名");
                AssetDatabase.Refresh();
                EditorUtility.DisplayProgressBar("", "检查完成", 1f);
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("", "检查完成", "OK");
                ExportCSV.OpenDirectory(Application.dataPath + "/Scripts/Sirenix/CSV");

            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("", "替换错误" + e, "OK");
                throw;
            }
        }

        Dictionary<string, string> tempPath = new Dictionary<string, string>();
        //拷贝资源到导出目录
        public string CopyRes(string md5, string sourcePath, string targetRootPath)
        {
            if (!Directory.Exists(targetRootPath))
            {
                try
                {
                    Directory.CreateDirectory(targetRootPath);
                }
                catch (System.Exception ex)
                {

                    throw new System.Exception("创建目标失败：" + ex.Message);
                }
            }
            bool isBigImage = false;
            TextureImporter source_Importer = AssetImporter.GetAtPath(sourcePath) as TextureImporter;
            if (source_Importer.spritePackingTag == "")
            {
                isBigImage = true;
            }
            string targetPath = Path.Combine(targetRootPath, Path.GetFileName(sourcePath));
            if (originalCommonPackingTags.Contains(source_Importer.spritePackingTag) || ignoreCheckPackingTags.Contains(source_Importer.spritePackingTag))
            {

                sourcePath = sourcePath.Replace("UI\\", "UI/");
                return sourcePath;
            }
            //Debug.Log("CopyRes:" + sourcePath + " new:" + targetPath);
            File.Copy(sourcePath, targetPath, true);
            AssetDatabase.Refresh();
            // 设置Atlas Texture属性
            TextureImporter atlas_Importer = AssetImporter.GetAtPath(targetPath) as TextureImporter;
            atlas_Importer.textureType = TextureImporterType.Sprite;
            atlas_Importer.spriteImportMode = SpriteImportMode.Single;
            //atlas_Importer.textureCompression = TextureImporterCompression.Uncompressed;
            atlas_Importer.mipmapEnabled = false;
            if (source_Importer.spriteBorder != null && atlas_Importer.spriteBorder != null)
            {
                atlas_Importer.spriteBorder = source_Importer.spriteBorder;
            }
            // atlas_Importer.spritesheet = atlasSheets;
            if (isBigImage)
            {
                ResetBigTexturePackingTagAndAbName(targetPath);
            }
            else
            {
                atlas_Importer.spritePackingTag = System.IO.Path.GetDirectoryName(targetPath).ToLower().Replace("assets\\", "").Replace('\\', '.');
                //设置abname
                ToolUti.SetABNameByPath(targetPath);
            }
            atlas_Importer.SaveAndReimport();

            //Debug.Log("新资源信息 完整路径：" + targetPath);
            return targetPath;
        }

        public static void ResetBigTexturePackingTagAndAbName(string path)
        {
            //string path = AssetDatabase.GetAssetPath (path);
            TextureImporter assetImporter = AssetImporter.GetAtPath(path) as TextureImporter;

            // 添加NonPacktag 标签
            List<string> lables = new List<string>();
            lables.AddRange(AssetDatabase.GetLabels(assetImporter));
            if (lables.IndexOf("NonPacktag") == -1)
            {
                lables.Add("NonPacktag");
            }
            AssetDatabase.SetLabels(assetImporter, lables.ToArray());
            //清空spritePackingTag
            assetImporter.spritePackingTag = "";

            //设置assetBundleName为path
            string assetBundleName = path.Substring(path.IndexOf("/") + 1);
            assetImporter.assetBundleName = assetBundleName;
            //LogHelp.clipboard = assetBundleName.ToLower();
        }

        public void SetNewDependencies(string targetPath, bool IsSignalResource = false)
        {
            bool isTemp = false;
            var spa = AssetDatabase.LoadAssetAtPath<SpriteAssets>(targetPath);
            if (spa != null)
            {
                if (spa.m_SpriteList.Count > 0)
                {
                    Sprite img;
                    for (int i = 0; i < spa.m_SpriteList.Count; i++)
                    {
                        img = spa.m_SpriteList[i];
                        Sprite newImage = IsSignalResource ? GetNewResourceBy<Sprite>(img) : GetNewAssetBy<Sprite>(img);
                        if (newImage != null)
                        {
                            ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                            patientStatisticsOutputDto.datas.Add(targetPath);
                            patientStatisticsOutputDto.datas.Add(spa.name);
                            patientStatisticsOutputDto.datas.Add(img.name);
                            patientStatisticsOutputDto.datas.Add(newImage.name);
                            saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                            //Debug.Log("替换SpriteAssets ui：" + newImage.name + " old:" + oldImage.name);
                            spa.m_SpriteList[i] = newImage;
                            isTemp = true;
                        }
                    }
                    if(isTemp)
                    {
                        UnityEditor.EditorUtility.SetDirty(spa);
                        UnityEditor.AssetDatabase.SaveAssets();
                        UnityEditor.AssetDatabase.Refresh();
                    }
                }
                return;
            }
            var go = AssetDatabase.LoadAssetAtPath<GameObject>(targetPath);
            if (go == null)
            {
                return;
            }
            Sprite oldImage;
            RawImage[] rawImages = go.GetComponentsInChildren<RawImage>(true);
            if (rawImages != null)
            {
                foreach (RawImage image in rawImages)
                {
                    if (image.texture == null)
                    {
                        continue;
                    }

                    Texture t = image.texture;
                    Texture newImage = IsSignalResource ? GetNewResourceBy<Texture>(t) : GetNewAssetBy<Texture>(t);
                    if (newImage != null)
                    {
                        ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                        patientStatisticsOutputDto.datas.Add(targetPath);
                        patientStatisticsOutputDto.datas.Add(image.gameObject.name);
                        patientStatisticsOutputDto.datas.Add(image.texture.name);
                        patientStatisticsOutputDto.datas.Add(newImage.name);
                        saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                        //Debug.Log("替换rawImages  ui：" + newImage.name + " old:" + oldImage.name);
                        image.texture = newImage;
                        isTemp = true;
                    }
                }
            }
            Image[] images = go.GetComponentsInChildren<Image>(true);
            if (images != null)
            {
                foreach (Image image in images)
                {
                    if (image.sprite == null)
                    {
                        continue;
                    }

                    oldImage = image.sprite;
                    Sprite newImage = IsSignalResource ? GetNewResourceBy<Sprite>(oldImage) : GetNewAssetBy<Sprite>(oldImage);
                    if (newImage != null)
                    {
                        ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                        patientStatisticsOutputDto.datas.Add(targetPath);
                        patientStatisticsOutputDto.datas.Add(image.gameObject.name);
                        patientStatisticsOutputDto.datas.Add(image.sprite.name);
                        patientStatisticsOutputDto.datas.Add(newImage.name);
                        saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                        //Debug.Log("替换Image  ui：" + newImage.name + " old:" + oldImage.name);
                        if (IsSignalResource)
                        {
                            string p = GetNewResourcePath(oldImage);
                            SetWidthAndHeight(image.GetComponent<RectTransform>(), newImage, p);
                        }
                        image.sprite = newImage;
                        isTemp = true;
                    }
                }
            }
            SpriteSwitcher[] spriteSwitchers = go.GetComponentsInChildren<SpriteSwitcher>(true);
            if (spriteSwitchers != null)
            {
                foreach (SpriteSwitcher image in spriteSwitchers)
                {
                    if (image == null || image.SpriteList == null)
                    {
                        continue;
                    }
                    for (int i = 0; i < image.SpriteList.Length; i++)
                    {
                        oldImage = image.SpriteList[i];
                        Sprite newImage = IsSignalResource ? GetNewResourceBy<Sprite>(oldImage) : GetNewAssetBy<Sprite>(oldImage);
                        if (newImage != null)
                        {
                            ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                            patientStatisticsOutputDto.datas.Add(targetPath);
                            patientStatisticsOutputDto.datas.Add(image.gameObject.name);
                            patientStatisticsOutputDto.datas.Add(image.SpriteList[i].name);
                            patientStatisticsOutputDto.datas.Add(newImage.name);
                            saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                            //Debug.Log("替换SpriteSwitcher ui：" + newImage.name + " old:" + oldImage.name);
                            image.SpriteList[i] = newImage;
                            isTemp = true;
                        }
                    }
                }
            }
            SpriteRenderer[] spriteRenderers = go.GetComponentsInChildren<SpriteRenderer>(true);
            if (spriteRenderers != null)
            {
                foreach (SpriteRenderer image in spriteRenderers)
                {
                    if (image.sprite == null)
                    {
                        continue;
                    }

                    oldImage = image.sprite;
                    Sprite newImage = IsSignalResource ? GetNewResourceBy<Sprite>(oldImage) : GetNewAssetBy<Sprite>(oldImage);
                    if (newImage != null)
                    {
                        ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                        patientStatisticsOutputDto.datas.Add(targetPath);
                        patientStatisticsOutputDto.datas.Add(image.gameObject.name);
                        patientStatisticsOutputDto.datas.Add(image.sprite.name);
                        patientStatisticsOutputDto.datas.Add(newImage.name);
                        saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                        //Debug.Log("替换SpriteRenderer ui：" + newImage.name + " old:" + oldImage.name);
                        image.sprite = newImage;
                        isTemp = true;
                    }
                }
            }
            if (isTemp)
            {
                UnityEditor.EditorUtility.SetDirty(go);
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.AssetDatabase.Refresh();
            }
        }

        private void SetWidthAndHeight(RectTransform t, Sprite newSp, string p)
        {
            Vector2 offset = Vector2.zero;
            if (resourceVecDic.ContainsKey(p))
                offset = resourceVecDic[p];
            Debug.Log("替换Image  ui：" + offset.x + "   " +offset.y + "   " + p);
            float scale = t.rect.size.x / newSp.rect.size.x;
            if (t.rect.size.x < t.rect.size.y)
            {
                scale = t.rect.size.y / newSp.rect.size.y;
            }
            t.sizeDelta = new Vector2(scale * newSp.rect.size.x + offset.x, scale * newSp.rect.size.y + offset.y);
        }

        public T GetNewAssetBy<T>(UnityEngine.Object component) where T : UnityEngine.Object
        {
            // Dictionary<int, string> paths = newAssets[classType];
            // var sourceAssetPath = AssetDatabase.GetAssetPath(component);
            if (!newAssets.ContainsKey(component))
            {
                return null;
            }
            // var go = newAssets[component] as T;
            var go = AssetDatabase.LoadAssetAtPath<T>(newAssets[component]);
            return go;
        }
        #endregion

        #region CheckDepeCount
        [FoldoutGroup("CheckDepeCount")]
        public List<string> findassets = new List<string>{
            "Assets/Art/Effects/Textures",
        };
        [FoldoutGroup("CheckDepeCount")]
        public List<string> dependenciesAssets = new List<string>{
            "Assets/Art/Effects",
        };
        [FoldoutGroup("CheckDepeCount")]
        public List<StrProto> fList = new List<StrProto>();
        [FoldoutGroup("CheckDepeCount")]
        public Dictionary<string, List<string>> dependenciesList = new Dictionary<string, List<string>>();
        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);
        //检测引用信息
        [FoldoutGroup("CheckDepeCount")]
        [Button("检测引用信息")]
        void CheckDepeCount()
        {
            var fas = AssetDatabase.FindAssets("", findassets.ToArray());
            dependenciesList = new Dictionary<string, List<string>>();
            var abnames = new Dictionary<string, int>();
            //Debug.Log("所有文件列表：" + string.Join("/n", fas));
            // ToolUti.Iter(fas, (fa) =>
            // {
            foreach (var fa in fas)
            {
                var ap = AssetDatabase.GUIDToAssetPath(fa);
                var ai = AssetImporter.GetAtPath(ap);
                if (isPng(ap))
                {
                    //GetTextureSizeBy(ap);
                    abnames[ap] = 0;
                    dependenciesList[ap] = new List<string>();
                    Debug.Log("长度" + ap + name + dependenciesList.Count);
                }
            }

            var allnames = AssetDatabase.FindAssets("", dependenciesAssets.ToArray());
            foreach (var abname in allnames)
            {
                var ap = AssetDatabase.GUIDToAssetPath(abname);
                var ai = AssetImporter.GetAtPath(ap);
                // if (string.IsNullOrEmpty(ai.assetBundleName))
                // {
                //     //Debug.Log("被依赖文件：" + ap + "没有abname!!!!!!!!!!!!!!!!!!!!!!!");
                //     return ap;
                // }
                var abs = AssetDatabase.GetDependencies(ap, true);
                foreach (var ab in abs)
                {
                    var num = 0;
                    //Debug.Log(abname + "依赖项：" + ab);
                    if (ab == ap)
                    {
                        continue;
                    }

                    if (abnames.TryGetValue(ab, out num))
                    {
                        if (dependenciesList[ab].IndexOf(ap) < 0)
                        {
                            abnames[ab] = num + 1;
                            dependenciesList[ab].Add(ap);
                        }
                        //if (abnames[ab] >= 2)
                        //{
                        //    Debug.LogError(ab +"---- \n" + string.Join("\n", dependenciesList[ab].ToArray()));
                        //}
                    }
                }
            }


            Debug.Log(UIHelper.ToJson(abnames));
            fList.Clear();
            foreach (var abn in abnames.Keys)
            {
                //var ai = AssetImporter.GetAtPath(abn);
                TextureImporter ai = AssetImporter.GetAtPath(abn) as TextureImporter;
                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(abn) as Texture2D;
                var abName = ai.assetBundleName;
                fList.Add(new StrProto()
                {
                    abname = abName,
                    assetSource = texture,
                    count = abnames[abn],
                    width = texture.width,
                    height = texture.height,
                    widthMulHeight = texture.width * texture.height,
                });
            }

            fList.Sort((Comparison<StrProto>)((f1, f2) =>
            {
                return (int)(f2.count - f1.count);
            }));

        }

        #endregion

        #region CheckAndChangeTwoDeference
        [FoldoutGroup("ChangeTwoDeference")]
        [FolderPath]
        public string checkResourcePath = "Assets/UI";
        [FoldoutGroup("ChangeTwoDeference")]
        public List<CheckResourceData> newCheckResourceLs = new List<CheckResourceData>();

        private Dictionary<UnityEngine.Object, string> resourceDic = new Dictionary<UnityEngine.Object, string>();
        private Dictionary<string, Vector2> resourceVecDic = new Dictionary<string, Vector2>();
        [FoldoutGroup("ChangeTwoDeference")]
        [Button("更换对应引用")]
        public void ChangeResourceDeference()
        {
            try
            {
                saveResourcesImgPTList.Clear();
                resourceDic.Clear();
                SetDeferenceAndTemp();
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError(e);
                throw;
            }
        }

        private void SetDeferenceAndTemp()
        {

            for (int i = 0; i < newCheckResourceLs.Count; i++)
            {
                string newPath = AssetDatabase.GetAssetPath(newCheckResourceLs[i].newObj);
                for (int j = 0; j < newCheckResourceLs[i].oldObjs.Count; j++)
                {
                    string oldPath = AssetDatabase.GetAssetPath(newCheckResourceLs[i].oldObjs[j]);
                    UnityEngine.Object go = AssetDatabase.LoadAssetAtPath<Sprite>(oldPath) as UnityEngine.Object;
                    if (resourceDic.ContainsKey(go))
                    {
                        resourceDic[go] = newPath;
                    }
                    else
                    {
                        resourceDic.Add(go, newPath);
                    }
                    if (!resourceVecDic.ContainsKey(newPath))
                    {
                        resourceVecDic.Add(newPath, newCheckResourceLs[i].offect);
                    }
                }
            }
            string[] paths = Directory.GetFiles(checkResourcePath, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab") || s.EndsWith(".asset")).ToArray();
            int le = paths.Length;
            for (int i = 0; i < le; i++)
            {
                string path = paths[i].Replace('\\', '/');
                path = path.Substring(path.IndexOf("Assets/"));

                UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath(path, typeof(UnityEngine.Object));
                var PrefabObject = prefab as UnityEngine.Object;
                if (null == PrefabObject)
                {
                    continue;
                }
                SetNewDependencies(path, true);
                EditorUtility.DisplayProgressBar("", "更换资源引用中，path ---->" + path + "(" + i + "/" + paths.Length + ")", i / (float)paths.Length * 0.2f + 0.6f);
            }
            exportCSV.ExportPatientStatisticsDetails(saveResourcesImgPTList, Application.dataPath + "/Scripts/Sirenix/CSV/ChangeTwoDeference替换位置.csv", "路径名,节点名,替换前资源名,替换后资源名");
            ExportCSV.OpenDirectory(Application.dataPath + "/Scripts/Sirenix/CSV");

            EditorUtility.DisplayProgressBar("", "检查完成", 1f);
            EditorUtility.ClearProgressBar();
            EditorUtility.DisplayDialog("", "检查完成", "OK");
        }

        public T GetNewResourceBy<T>(UnityEngine.Object component) where T : UnityEngine.Object
        {
            if (!resourceDic.ContainsKey(component))
            {
                return null;
            }
            //Debug.LogError(component.name + "              " + resourceDic[component]);
            var go = AssetDatabase.LoadAssetAtPath<T>(resourceDic[component]);
            return go;
        }
        public string GetNewResourcePath(UnityEngine.Object component)
        {
            if (!resourceDic.ContainsKey(component))
            {
                return "";
            }
            return resourceDic[component];
        }
        #endregion

        #region 检查旧图
        [FoldoutGroup("ChangeOldPictureDeference")]
        [FolderPath]
        public string checkOldPicturePath = "Assets/UI";
        [FoldoutGroup("ChangeOldPictureDeference")]
        [FolderPath]
        public List<string> checkAssetsType = new List<string> { ".prefab",".asset"  };
        [FoldoutGroup("ChangeOldPictureDeference")]
        public List<string> checkAssetsPathLs = new List<string>();
        [FoldoutGroup("ChangeOldPictureDeference")]
        [InfoBox("是否删除，不选择则导出引用")]
        [HideLabel]
        public bool  isDelete = false;
        [FoldoutGroup("ChangeOldPictureDeference")]
        [Button("检查并删除旧图")]
        public void CheckAndDelete()
        {
            Dictionary<string, List<string>> gos = new Dictionary<string, List<string>>();
            List<string> checkpls = new List<string>();
            List<string> delectfls = new List<string>();
            List<string> fas = new List<string>();
            for (int i = 0; i < checkAssetsPathLs.Count; i++)
            {
                for (int j = 0; j < checkAssetsType.Count; j++)
                {
                    fas.AddRange(Directory.GetFiles(checkAssetsPathLs[i], "*" + checkAssetsType[j], SearchOption.AllDirectories));
                }
            }

            foreach (var fa in fas)
            {
                //var ap = AssetDatabase.GUIDToAssetPath(fa);
                string ap = fa.Replace("\\", "/");
                string[] p = AssetDatabase.GetDependencies(ap);
                for (int i = 0; i < p.Length; i++)
                {
                    if (gos.ContainsKey(p[i].Replace("\\", "/")))
                    {
                        List<string> ls = gos[p[i].Replace("\\", "/")];
                        ls.Add(ap);
                        gos[p[i].Replace("\\", "/")] = ls;
                    }
                    else
                    {
                        gos.Add(p[i].Replace("\\", "/"),new List<string> { ap });
                    }
                }
            }
            string[] extensions = new string[] { "png", "jpg", "jpeg" };
            List<string> files = new List<string>();
            for (int i = 0; i < extensions.Length; i++)
            {
                files.AddRange(Directory.GetFiles(checkOldPicturePath, "*." + extensions[i], SearchOption.AllDirectories));
            }

            for (int i = 0; i < files.Count; i++)
            {
                string p = files[i].Replace("\\", "/");
                TextureImporter textureImporter = AssetImporter.GetAtPath(p) as TextureImporter;
                TextureImporterPlatformSettings textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.iOS.ToString());
                if(textureImporterPlatformSettings.maxTextureSize == 32)
                {
                    textureImporterPlatformSettings = textureImporter.GetPlatformTextureSettings(BuildTarget.Android.ToString());
                    if (textureImporterPlatformSettings.maxTextureSize == 32)
                    {
                        delectfls.Add(p);
                    }
                }
            }

            for (int i = 0; i < delectfls.Count; i++)
            {
                if (gos.ContainsKey(delectfls[i]))
                {
                    for (int j = 0; j < gos[delectfls[i]].Count; j++)
                    {
                        ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                        patientStatisticsOutputDto.datas.Add(Path.GetFileName(gos[delectfls[i]][j]).Split('.')[0]);
                        patientStatisticsOutputDto.datas.Add(Path.GetFileName(delectfls[i]));
                        patientStatisticsOutputDto.datas.Add(delectfls[i]);
                        saveResourcesImgPTList.Add(patientStatisticsOutputDto);
                    }
                    checkpls.Add(delectfls[i]);
                    delectfls.RemoveAt(i);
                    i--;
                }
            }
            if (isDelete)
            {
                for (int i = 0; i < delectfls.Count; i++)
                {
                    ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                    patientStatisticsOutputDto.datas.Add(Path.GetFileName(delectfls[i]));
                    patientStatisticsOutputDto.datas.Add(delectfls[i]);
                    saveDeleteImgPTList.Add(patientStatisticsOutputDto);
                    AssetDatabase.DeleteAsset(delectfls[i]);
                }
            }

            exportCSV.ExportPatientStatisticsDetails(saveResourcesImgPTList, Application.dataPath + "/Scripts/Sirenix/CSV/ChangeOldPictureDeference还有引用的旧图.csv", "预制体,图,路径");
            exportCSV.ExportPatientStatisticsDetails(saveDeleteImgPTList, Application.dataPath + "/Scripts/Sirenix/CSV/ChangeOldPictureDeference删除的图片.csv", "图,路径");
            ExportCSV.OpenDirectory(Application.dataPath + "/Scripts/Sirenix/CSV");
            AssetDatabase.Refresh();
        }

        #endregion
    }

    [Serializable]
    public class CheckResourceData
    {
        public UnityEngine.Object newObj;
        public List<UnityEngine.Object> oldObjs;
        public Vector2 offect = new Vector2(0, 0);
    }
}
#endif