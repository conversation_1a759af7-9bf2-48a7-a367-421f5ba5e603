#if UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class SVNTool : Editor
{
    [MenuItem("Assets/SVN/提交资源以及依赖", false, 0)]
    static void SVNCommitDep()
    {
        List<string> pathList = new List<string>();
        var sel = Selection.objects;
        if (sel == null)
        {
            return;
        }
        for (int j = 0; j < sel.Length; j++)
        {
            var path = AssetDatabase.GetAssetPath(sel[j]);
            if (path != null)
            {
                if (!File.Exists(path))
                {
                    path = Path.GetFullPath(path);
                    CheckAddPath(pathList, path);
                }
                else
                {
                    var dps = GetCollectDependences(path);
                    path = Path.GetFullPath(path);
                    CheckAddPath(pathList, path);
                    for (int i = 0; i < dps.Length; i++)
                    {
                        if (string.IsNullOrEmpty(dps[i]))
                        {
                            continue;
                        }
                        var fullPath = Path.GetFullPath(dps[i]);
                        CheckAddPath(pathList, fullPath);
                    }
                }

            }
        }
        if (ExportWindow.CheckResPath(pathList) == false)
        {
            return;
        }
        //for(int i = 0; i < pathList.Count; i++)
        //{
        //    string op = ProcessCommandWithOutput("svn.exe", "status " + pathList[i]); 
        //    Debug.LogError(op);
        //}
        //如果这里出现字符超限的情况，需要分段判断并筛选按需要提交
        //pathList是依赖的资源
        string op = ProcessCommandWithOutput("svn.exe", "status --verbose " + string.Join(" ", pathList)).Replace("\\", "/");
        int ix = pathList.Count; 


        List<string> newResLs = new List<string>(); //新增资源
        List<string> newResFolder = new List<string>(); //新增资源对应的文件夹
        //筛选出新增的资源
        while (ix > 0)
        {
            ix--;
            if (!op.Contains(pathList[ix])) //没有的就是新增的资源
            {
                newResLs.Add(pathList[ix]);
            }
        }
        string opx = ProcessCommandWithOutput("svn.exe", "status " + string.Join(" ", pathList)).Replace("\\", "/");
        ix = pathList.Count;
        //筛选掉不需要提交的文件夹内容
        while (ix > 0)
        {
            ix--;
            if (!opx.Contains(pathList[ix])) //没有的就是新增的资源
            {
                pathList.RemoveAt(ix);
            }
        }
        count = 0;
        for (int i = 0; i < newResLs.Count; i++)
        {
            string file = newResLs[i];
            if (!pathList.Contains(file))
            {
                pathList.Add(newResLs[i]);
            }
            RvDirTo(newResFolder, file, "/Assets");

        }
        if (newResFolder.Count > 0)
        {
            opx = ProcessCommandWithOutput("svn.exe", "status " + string.Join(" ", newResFolder)).Replace("\\", "/");
            string[] arr = opx.Split('\n');
            newResFolder.Clear();
            for (int i = 0; i < arr.Length; i++)
            {
                if (arr[i].StartsWith("?"))
                {
                    string dirName = arr[i].Replace("?       ", "").Replace("\r", "");
                    if (!newResFolder.Contains(dirName))
                    {
                        newResFolder.Add(dirName);
                    }
                }
            }
        }
   
        //筛选新增的文件夹，添加到列表 
        for (int i = 0; i < newResFolder.Count; i++)
        {
            pathList.Add(newResFolder[i]);
        }
        if (pathList.Count > 0)
        {
            var pathArr = pathList.ToArray();
            string commitPath = string.Join("*", pathArr);
            ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
        }
        else
        {
            Debug.LogError("没有资源需要提交");
        }
       
    }

    static int count = 0;
    /// <summary>
    /// 回溯资源文件夹直到Art下的目录，将文件夹加入列表
    /// </summary>
    /// <param name="dirLs"></param>
    /// <param name="curPath"></param>
    /// <param name="destDir"></param>
    private static void RvDirTo(List<string> dirLs, string curPath, string destDir)
    {
        if (string.IsNullOrEmpty(curPath))
        {
            return;
        }
        string dir = Path.GetDirectoryName(curPath);
        if (string.IsNullOrEmpty(dir))
        {
            return;
        }
        dir = dir.Replace("\\", "/");

        if (dir.EndsWith(destDir))
        {
            return;
        }
        if (!dirLs.Contains(dir))
        {
            dirLs.Add(dir);
            dirLs.Add(dir + ".meta");
        }
        RvDirTo(dirLs, dir, destDir);

    }
    /// <summary>
    /// 将特效资源合并到制定目录下
    /// </summary>
    [MenuItem("Assets/SVN/导出资源合并到", false, 0)]
    public static void MergeDpTo()
    {
        string exportToFolder = EditorUtility.OpenFolderPanel("导出目录", GetSaveMergeToPath(), "");
        if (string.IsNullOrEmpty(exportToFolder))
        {
            return;
        }
        SetSaveMergeToPath(exportToFolder);
        List<string> pathList = new List<string>();
        var sel = Selection.objects;
        if (sel == null)
        {
            return;
        }
        for (int j = 0; j < sel.Length; j++)
        {
            var path = AssetDatabase.GetAssetPath(sel[j]);
            if (path != null)
            {
                if (!File.Exists(path))
                {


                    var fileLs = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                    for (int k = 0; k < fileLs.Length; k++)
                    {
                        var fName = fileLs[k];
                        if (fName.EndsWith(".meta"))
                        {
                            continue;
                        }
                        fName = fName.Replace("\\", "/");
                        fName = fName.Replace("//", "/");
                        fName = fName.Replace(Application.dataPath.Replace("Assets", ""), "/");
                        fName = Path.GetFullPath(fName);
                        CheckAddPath(pathList, fName);
                    }
                }
                else
                {
                    var dps = GetCollectDependences(path);
                    path = Path.GetFullPath(path);
                    CheckAddPath(pathList, path);
                    for (int i = 0; i < dps.Length; i++)
                    {
                        if (string.IsNullOrEmpty(dps[i]))
                        {
                            continue;
                        }
                        var fullPath = Path.GetFullPath(dps[i]);
                        CheckAddPath(pathList, fullPath);
                    }
                }

            }
        }
        for (int i = pathList.Count - 1; i > 0; i--)
        {
            string p = pathList[i].Replace("\\", "/");
            if (!p.Contains("Art/") && !p.Contains("Animations/"))
            {
                pathList.RemoveAt(i);
            }
            if (pathList[i].Contains(" "))
            {
                if (pathList[i].Contains(".shader"))
                {
                    pathList.RemoveAt(i);
                }
                else
                {
                    EditorUtility.DisplayDialog("警告", "资源名称不允许带空格" + pathList[i], "确定");
                    return;
                }
            }
        }
        var pathArr = pathList.ToArray();
        for (int i = 0; i < pathArr.Length; i++)
        {
            var newPath = exportToFolder + pathArr[i].Replace(Application.dataPath.Replace("Assets", ""), "/");
            var dir = Path.GetDirectoryName(newPath);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            File.Copy(pathArr[i], newPath, true);
        }
    }

    /// <summary>
    /// 将资源导出合并到制定文件夹
    /// </summary>
    /// <param name="fileLs"></param>
    public static bool MergeDpTo(List<string> exportFiles)
    {
        string exportToFolder = EditorUtility.OpenFolderPanel("导出目录", GetSaveMergeToPath(), "");
        if (string.IsNullOrEmpty(exportToFolder))
        {
            return false;
        }
        SetSaveMergeToPath(exportToFolder);
        List<string> pathList = new List<string>();
        for (int j = 0; j < exportFiles.Count; j++)
        {
            var path = exportFiles[j];
            if (path != null)
            {
                if (!File.Exists(path))
                {
                    var fileLs = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                    for (int k = 0; k < fileLs.Length; k++)
                    {
                        var fName = fileLs[k];
                        if (fName.EndsWith(".meta"))
                        {
                            continue;
                        }
                        fName = fName.Replace("\\", "/");
                        fName = fName.Replace("//", "/");
                        fName = fName.Replace(Application.dataPath.Replace("Assets", ""), "/");
                        fName = Path.GetFullPath(fName);
                        CheckAddPath(pathList, fName);
                    }
                }
                else
                {
                    var dps = GetCollectDependences(path);
                    path = Path.GetFullPath(path);
                    CheckAddPath(pathList, path);
                    for (int i = 0; i < dps.Length; i++)
                    {
                        if (dps[i].Contains("Assets/Art") == false)
                        {
                            continue;
                        }
                        var fullPath = Path.GetFullPath(dps[i]);
                        CheckAddPath(pathList, fullPath);
                    }
                }

            }
        }
        for (int i = pathList.Count - 1; i > 0; i--)
        {
            if (pathList[i].Contains(" "))
            {
                if (pathList[i].Contains(".shader"))
                {
                    pathList.RemoveAt(i);
                }
                else
                {
                    EditorUtility.DisplayDialog("警告", "资源名称不允许带空格" + pathList[i], "确定");
                    return false;
                }
            }
        }
        var pathArr = pathList.ToArray();
        for (int i = 0; i < pathArr.Length; i++)
        {
            var newPath = exportToFolder + pathArr[i].Replace(Application.dataPath.Replace("Assets", ""), "/");
            var dir = Path.GetDirectoryName(newPath);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            File.Copy(pathArr[i], newPath, true);
        }
        return true;

    }
    [MenuItem("Assets/SVN/添加到批量导出列表", false, 0)]
    public static void Batch_Merge()
    {
        BatchMergeExportDp.OpenWindow();
        List<string> pathList = new List<string>();
        var sel = Selection.objects;
        if (sel == null)
        {
            return;
        }
        for (int i = 0; i < sel.Length; i++)
        {
            var path = AssetDatabase.GetAssetPath(sel[i]);
            if (path != null)
            {
                if (!File.Exists(path))
                {
                    var fileLs = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                    for (int k = 0; k < fileLs.Length; k++)
                    {
                        var fName = fileLs[k];
                        if (fName.EndsWith(".meta"))
                        {
                            continue;
                        }
                        fName = fName.Replace("\\", "/");
                        fName = fName.Replace("//", "/");
                        fName = fName.Replace(Application.dataPath.Replace("Assets", ""), "/");
                        fName = Path.GetFullPath(fName);
                        CheckAddPath(pathList, fName);
                    }
                }
                else
                {
                    CheckAddPath(pathList, path);
                }

            }
        }
        return;

        Debug.LogError($"pathList {pathList.Count}");
        for (int i = pathList.Count - 1; i > 0; i--)
        {
            if (pathList[i].Contains(" "))
            {
                if (pathList[i].Contains(".shader"))
                {
                    pathList.RemoveAt(i);
                }
                else
                {
                    EditorUtility.DisplayDialog("警告", "资源名称不允许带空格" + pathList[i], "确定");
                    return;
                }
            }
        }
        BatchMergeExportDp.AddToBatchExport(pathList);
    }
    /// <summary>
    /// 获取上一次选择的保存路径
    /// </summary>
    /// <returns></returns>
    public static string GetSaveMergeToPath()
    {
        string mergeToPath = PlayerPrefs.GetString("EDITOR_SVNTOOL_MERGE_PATH", Application.dataPath.Replace("Assets", ""));
        return mergeToPath;
    }
    public static void SetSaveMergeToPath(string path)
    {
        PlayerPrefs.SetString("EDITOR_SVNTOOL_MERGE_PATH", path);
    }
    private static void CheckAddPath(List<string> pathLs, string path)
    {
        string p = path.Replace("\\", "/");
        p = p.Replace("//", "/");
        if (File.Exists(p))
        {
            if (!pathLs.Contains(p) || Directory.Exists(p))
            {
                pathLs.Add(p);
                pathLs.Add(p + ".meta");
            }
        }

    }
    /// <summary>
    /// 获取资源相关依赖项
    /// </summary>
    /// <param name="path"></param>
    /// <returns></returns>
    private static string[] GetCollectDependences(string path)
    {

        var obj = AssetDatabase.LoadAssetAtPath<Object>(path);
        var objs = new Object[] { obj };
        var dpObjs = EditorUtility.CollectDependencies(objs);
        string[] dps = new string[dpObjs.Length];
        for (int i = 0; i < dpObjs.Length; i++)
        {
            var existFile = AssetDatabase.GetAssetPath(dpObjs[i]);
            if (File.Exists(existFile))
            {
                dps[i] = existFile;
            }
        }
        return dps;
    }
    [MenuItem("Assets/SVN/提交", false, 1)]
    static void SVNCommit()
    {
        List<string> pathList = new List<string>();
        //string basePath = SVNProjectPath + "/Assets";
        //pathList.Add(basePath);
        //pathList.Add(SVNProjectPath + "/ProjectSettings");

        var sel = Selection.activeObject;
        var path = AssetDatabase.GetAssetPath(sel);
        if (path != null)
        {
            path = Path.GetFullPath(path);
            path = path.Replace("\\\\", "/");
            pathList.Add(path);
            string commitPath = string.Join("*", pathList.ToArray());
            ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
        }

    }

    [MenuItem("Assets/SVN/提交Client", false, 1)]
    static void SVNCommitClient()
    {
        List<string> pathList = new List<string>();
        string basePath = SVNProjectPath + "/Assets";
        pathList.Add(basePath);
        pathList.Add(SVNProjectPath + "/ProjectSettings");

        string commitPath = string.Join("*", pathList.ToArray());
        ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
    }

    [MenuItem("Assets/SVN/更新", false, 2)]
    static void SVNUpdate()
    {
        ProcessCommand("TortoiseProc.exe", "/command:update /path:" + SVNProjectPath + " /closeonend:0");
    }

    [MenuItem("Assets/SVN/", false, 3)]
    static void Breaker() { }

    [MenuItem("Assets/SVN/清理缓存", false, 4)]
    static void SVNCleanUp()
    {
        ProcessCommand("TortoiseProc.exe", "/command:cleanup /path:" + SVNProjectPath);
    }

    [MenuItem("Assets/SVN/打开日志", false, 5)]
    static void SVNLog()
    {
        ProcessCommand("TortoiseProc.exe", "/command:log /path:" + SVNProjectPath);
    }
    [MenuItem("Assets/SVN/Relocate", false, 5)]
    static void SVNRelocate()
    {
        ProcessCommand("TortoiseProc.exe", "/command:switch --relocate  /path:" + SVNProjectPath);
    }
    //172.16.0.198 svn switch --relocate 
    public static string SVNProjectPath
    {
        get
        {
            System.IO.DirectoryInfo parent = System.IO.Directory.GetParent(Application.dataPath);
            return parent.ToString();
        }
    }

    public static string ProcessCommandWithOutput(string command, string argument)
    {
        "".Print("ProcessCommandWithOutput", command, argument);
        System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo(command);
        startInfo.Arguments = argument;
        startInfo.CreateNoWindow = true;
        startInfo.RedirectStandardOutput = true;
        startInfo.UseShellExecute = false;
        System.Diagnostics.Process process = new System.Diagnostics.Process();
        process.StartInfo = startInfo;
        process.Start();

        // 读取输出
        string output = process.StandardOutput.ReadToEnd();
        process.WaitForExit();
        return output;

        //System.Diagnostics.ProcessStartInfo info = new System.Diagnostics.ProcessStartInfo(command);
        //info.Arguments = argument;
        //info.CreateNoWindow = false;
        //info.ErrorDialog = false;
        //info.UseShellExecute = false;

        //info.RedirectStandardOutput = true;
        //info.RedirectStandardError = true;
        //info.RedirectStandardInput = true;
        //info.StandardOutputEncoding = System.Text.UTF8Encoding.UTF8;
        //info.StandardErrorEncoding = System.Text.UTF8Encoding.UTF8;

        //System.Diagnostics.Process process = System.Diagnostics.Process.Start(info);
        //var output = process.StandardOutput.ReadToEnd();
        //process.WaitForExit();

        //Debug.LogError(output);
        //System.Console.WriteLine("按下回车键以继续...");
        //System.Console.ReadLine();
        //process.Close(); 
        return "";
    }
    public static void ProcessCommand(string command, string argument)
    {
        "".Print("ProcessCommand", command, argument);

        System.Diagnostics.ProcessStartInfo info = new System.Diagnostics.ProcessStartInfo(command);
        info.Arguments = argument;
        info.CreateNoWindow = false;
        info.ErrorDialog = true;
        info.UseShellExecute = true;

        if (info.UseShellExecute)
        {
            info.RedirectStandardOutput = false;
            info.RedirectStandardError = false;
            info.RedirectStandardInput = false;
        }
        else
        {
            info.RedirectStandardOutput = true;
            info.RedirectStandardError = true;
            info.RedirectStandardInput = true;
            info.StandardOutputEncoding = System.Text.UTF8Encoding.UTF8;
            info.StandardErrorEncoding = System.Text.UTF8Encoding.UTF8;
        }

        System.Diagnostics.Process process = System.Diagnostics.Process.Start(info);

        if (!info.UseShellExecute)
        {
            Debug.Log(process.StandardOutput);
            Debug.Log(process.StandardError);
        }

        process.WaitForExit();
        process.Close();
    }
    public class BatchMergeExportDp : EditorWindow
    {
        public static BatchMergeExportDp window;
        public static List<string> batchExportLs = new List<string>();
        /// <summary>
        /// 添加到批量导出列表
        /// </summary>
        /// <param name="path"></param>
        public static void AddToBatchExport(List<string> paths)
        {
            for (int i = 0; i < paths.Count; i++)
            {
                if (!batchExportLs.Contains(paths[i]))
                {
                    batchExportLs.Add(paths[i]);
                }
            }
        }

        public static void OpenWindow()
        {
            if (window)
            {
                window.Close();
            }
            if (window == null)
            {
                window = EditorWindow.CreateWindow<BatchMergeExportDp>();

            }
            window.Show();
        }

        private static Vector2 scrollPos = Vector2.zero;
        private void OnGUI()
        {
            Object selectedObj = null;
            selectedObj = EditorGUILayout.ObjectField(selectedObj, typeof(UnityEngine.Object)) as UnityEngine.Object;
            if (selectedObj != null)
            {
                var path = AssetDatabase.GetAssetPath(selectedObj);
                if (!string.IsNullOrEmpty(path))
                {
                    List<string> addPath = new List<string>();
                    if (File.Exists(path))
                    {
                        addPath.Add(path);
                    }
                    else if (Directory.Exists(path))
                    {
                        var files = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                        for (int i = 0; i < files.Length; i++)
                        {
                            if (files[i].EndsWith(".meta"))
                            {
                                continue;
                            }
                            addPath.Add(files[i]);
                        }
                    }
                    BatchMergeExportDp.OpenWindow();
                    AddToBatchExport(addPath);
                }
            }
            scrollPos = EditorGUILayout.BeginScrollView(scrollPos);
            int rmIndex = -1;
            for (int i = 0; i < batchExportLs.Count; i++)
            {
                EditorGUILayout.BeginHorizontal();
                if (batchExportLs[i].EndsWith(".meta"))
                {
                    continue;
                }

                if (GUILayout.Button("-", GUILayout.Width(50)))
                {
                    rmIndex = i;
                }
                GUILayout.Label(batchExportLs[i]);

                EditorGUILayout.EndHorizontal();
            }
            if (rmIndex != -1)
            {
                batchExportLs.RemoveAt(rmIndex);
            }
            EditorGUILayout.EndScrollView();

            if (GUILayout.Button("清除选择"))
            {
                if (EditorUtility.DisplayDialog("提示", "确定清除当前选中内容？", "确定", "取消"))
                {
                    batchExportLs.Clear();
                }
            }
            if (GUILayout.Button("导出"))
            {
                if (SVNTool.MergeDpTo(batchExportLs))
                {
                    batchExportLs.Clear();
                }

            }

        }
        static ExportWindow win;
        [MenuItem("Assets/SVN/加载文件列表", false, 0)]
        static void ExportFileWindow()
        {
            if (win == null)
                win = EditorWindow.GetWindow<ExportWindow>("ExportFile");
        }
    }
    class ExportWindow : EditorWindow
    {
        private string filePath = "请选择要导出的文件";
        private string folderPath = "请选择Client文件夹";
        private string fileContent;
        private Vector2 scrollPosition;

        private string CheckEffectsPath = "Assets/Art/Effects/Effects";
        private string CheckEffectsCommonPath = "Assets/Art/Effects_Source/Prefabs/common";
        void OnGUI()
        {
            EditorGUILayout.BeginVertical();

            GUILayout.Label("检查导出特效路径：" + CheckEffectsPath);
            GUILayout.Label("检查common特效路径：" + CheckEffectsCommonPath);


            if (GUILayout.Button("选择导出的文件夹"))
            {
                string p = EditorUtility.OpenFolderPanel("Select Folder", folderPath, "");
                if (!string.IsNullOrEmpty(p))
                {
                    folderPath = p;
                }
            }
            GUILayout.Label("导出文件夹：" + folderPath);
            // 创建选择资源文件的按钮
            if (GUILayout.Button("选择准备好的文件列表"))
            {
                string p = EditorUtility.OpenFilePanel("Select File", filePath, "");
                if (!string.IsNullOrEmpty(p))
                {
                    filePath = p;
                    fileContent = System.IO.File.ReadAllText(filePath);
                    fileContent = CheckPrefabPath(fileContent);
                }
            }
            GUILayout.Label("File Path: " + filePath);

            EditorGUILayout.Space();

            if (GUILayout.Button("导出特效"))
            {
                CopyToNew(folderPath, fileContent);
            }
            EditorGUILayout.Space();

            // 创建可滑动的面板内容
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            GUILayout.Label("导出列表:");
            GUILayout.TextArea(fileContent);
            EditorGUILayout.EndScrollView();

            EditorGUILayout.EndVertical();
        }

        string CheckPrefabPath(string fileStr)
        {
            string[] strs = fileStr.Replace("\r", "").Split('\n');
            List<string> effPaths = Directory.GetFiles(CheckEffectsPath, "*.prefab", SearchOption.AllDirectories).ToList();
            for (int i = 0; i < effPaths.Count; i++)
            {
                string name = Path.GetFileName(effPaths[i]).Replace(".prefab", "");
                for (int j = 0; j < strs.Length; j++)
                {
                    if (name == strs[j])
                    {
                        strs[j] = effPaths[i];
                    }
                }
            }
            effPaths.Clear();
            effPaths = Directory.GetFiles(CheckEffectsCommonPath, "*.prefab", SearchOption.AllDirectories).ToList();
            for (int i = 0; i < effPaths.Count; i++)
            {
                string name = Path.GetFileName(effPaths[i]).Replace(".prefab", "");
                for (int j = 0; j < strs.Length; j++)
                {
                    if (name == strs[j])
                    {
                        strs[j] = effPaths[i];
                    }
                }
            }
            fileStr = "";
            for (int i = 0; i < strs.Length; i++)
            {
                fileStr += strs[i] + '\n';
            }
            return fileStr.Replace("\\", "/");
        }

        void CopyToNew(string exportToFolder, string fileStr)
        {
            if (string.IsNullOrEmpty(exportToFolder) || string.IsNullOrEmpty(fileStr))
            {
                return;
            }
            SetSaveMergeToPath(folderPath);

            string[] strs = fileStr.Split('\n');
            List<string> pathList = new List<string>();
            for (int j = 0; j < strs.Length; j++)
            {
                var path = strs[j];
                if (string.IsNullOrEmpty(path) == false)
                {
                    if (!File.Exists(path))
                    {
                        var fileLs = Directory.GetFiles(path, "*", SearchOption.AllDirectories);
                        for (int k = 0; k < fileLs.Length; k++)
                        {
                            var fName = fileLs[k];
                            if (fName.EndsWith(".meta"))
                            {
                                continue;
                            }
                            fName = fName.Replace("\\", "/");
                            fName = fName.Replace("//", "/");
                            fName = fName.Replace(Application.dataPath.Replace("Assets", ""), "/");
                            fName = Path.GetFullPath(fName);
                            CheckAddPath(pathList, fName);
                        }
                    }
                    else
                    {
                        var dps = GetCollectDependences(path);
                        path = Path.GetFullPath(path);
                        CheckAddPath(pathList, path);
                        for (int i = 0; i < dps.Length; i++)
                        {
                            var fullPath = Path.GetFullPath(dps[i]);
                            CheckAddPath(pathList, fullPath);
                        }
                    }

                }
            }
            for (int i = pathList.Count - 1; i > 0; i--)
            {
                if (pathList[i].Contains(".shader"))
                {
                    pathList.RemoveAt(i);
                }
                else if (pathList[i].Contains(" "))
                {
                    EditorUtility.DisplayDialog("警告", "资源名称不允许带空格" + pathList[i], "确定");
                    return;
                }
            }
            List<string> newPathLs = new List<string>();
            var pathArr = pathList.ToArray();
            for (int i = 0; i < pathArr.Length; i++)
            {
                var newPath = exportToFolder + pathArr[i].Replace(Application.dataPath.Replace("Assets", ""), "/");
                newPath = newPath.Replace("\\", "/");
                if (newPath.Contains("Art/") == false && newPath.Contains("Animations/") == false)
                {
                    continue;
                }
                var dir = Path.GetDirectoryName(newPath);
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                if (!Directory.Exists(newPath))
                {
                    File.Copy(pathArr[i], newPath, true);
                }

                newPathLs.Add(newPath);
            }
            //SVNTool.commonDepToSVN(newPathLs);
        }

        /// <summary>
        /// 检查路径
        /// </summary>
        /// <param name="pathList"></param>
        /// <returns></returns>
        public static bool CheckResPath(List<string> pathList)
        {
            for (int i = pathList.Count - 1; i > 0; i--)
            {
                string p = pathList[i].Replace("\\", "/");
                if (!p.Contains("Art/") && !p.Contains("Animations/"))
                {
                    pathList.RemoveAt(i);
                    continue;
                }
                if (pathList[i].Contains(" "))
                {
                    if (pathList[i].Contains(".shader"))
                    {
                        pathList.RemoveAt(i);
                    }
                    else
                    {
                        EditorUtility.DisplayDialog("警告", "资源名称不允许带空格" + pathList[i], "确定");
                        return false;
                    }
                }
            }
            return true;
        }
    }
    
    [MenuItem("Assets/Tot一键提交", false, 0)]
    static void SVNTotCommit()
    {
        string rootPath = System.Environment.CurrentDirectory;
        //Debug.LogError(rootPath);
        rootPath = rootPath.Replace(@"\Bin\Client", "");
        var excelPath = rootPath + "/Tools/csv_script/cn/PeakOfTimeRefresh.csv";
        var totPath = rootPath + "/Bin/Client/Assets/Art/Maps/ToT";
        List<string> pathList = new List<string>();
        
        if (File.Exists(excelPath))
        {
            pathList.Add(excelPath);
        }
        DirectoryInfo directoryInfo = new DirectoryInfo(totPath);
        if (directoryInfo.Exists)
        {
            FileInfo[] files = directoryInfo.GetFiles("*", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                pathList.Add(files[i].FullName);
            }
            if (pathList.Count > 0)
            {
                var pathArr = pathList.ToArray();
                string commitPath = string.Join("*", pathArr);
                ProcessCommand("TortoiseProc.exe", "/command:commit /path:" + commitPath);
            }
        }
        
    }
    //需要子菜单 Ignoring menu item Tot一键提交 because it is in no submenu!
    //[MenuItem("Tot一键提交", false, 0)]
    public static void SVNTotCommit2()
    {
        SVNTotCommit();
    }

    static string svnProjectName = null;//默认运行时不会变,不用多次取
    //[MenuItem("Assets/SVN/获取svn当前项目名")]
    [MenuItem("Build/获取svn当前项目名")]
    public static string SVNGetProjectName() {
        if(svnProjectName != null) return svnProjectName;
        string output = ProcessCommandWithOutput("svn", "info " + SVNProjectPath);
        var match = System.Text.RegularExpressions.Regex.Match(output, @"Relative URL:\s+\^/([^/]+)");
        if(!match.Success) match = System.Text.RegularExpressions.Regex.Match(output, @"URL:\s+(?:https?|svn|file)://[^/]+/[^/]+/[^/]+/([^/]+)");
        svnProjectName = match.Success ? match.Groups[1].Value : null;
        Debug.Log(svnProjectName + "\n" + output);
        return svnProjectName;
    }
    //是否项目主干
    public static bool IsSVNTrunk() {
        var p = SVNGetProjectName();
        return p == "oHero" || p == "vHero220223"   //超能海外,国内主干
            || p == "zHero211229" || p == "zHero"   //位面海外,国内主干
            || p == "yHero220726" || p == "TowerD"  //苍穹主干,塔防主干
            ;
    }
}
#endif

