/****************************************************
 *  文件：ConvertHotFixFilesDiff.cs
 *  作者：Henry
 *  日期：2023/10/23
 *  功能：对比两个版本的热更资源的差异，批量转换为文本格式，方便对比排查属性修改变化
*****************************************************/


using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Sirenix.Utilities;
using UnityEditor;
using UnityEngine;
using War.Base;

#if UNITY_EDITOR

namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ConvertHotFixFilesDiff : GlobalConfig<ConvertHotFixFilesDiff>
    {
        [BoxGroup("对比差异资源")]
        public string hserverResourcePath = "https://dl-wm2.cool-play.com/xhero_update/wm2_2020/Android/resource/";

        [BoxGroup("对比差异资源")]
        public string hotUrlOld = "384/files2.txt";

        [BoxGroup("对比差异资源")]
        public string hotUrlNew = "385/files2.txt";

        [BoxGroup("对比差异资源")]
        [FolderPath(AbsolutePath = true, UseBackslashes = false)]
        public string tempResourceFolder;

        [ReadOnly]
        int oldVersion;
        [ReadOnly]
        int newVersion;
        readonly Regex regexFilesVersion = new Regex(@"/resource/(\d+)/(files[2]*.txt)");

        private void Awake()
        {
            tempResourceFolder = $"{Application.dataPath}/../ConvertResourceFolder/";
            tempResourceFolder = Path.GetFullPath(tempResourceFolder).Replace("\\", "/");
        }

        int ParseVersion(string fileUrl, out string fileName)
        {
            fileUrl = fileUrl.Trim();
            fileName = null;

            var versionMatch = regexFilesVersion.Match(fileUrl);
            if (!versionMatch.Success || versionMatch.Groups.Count <= 2)
            {
                Debug.LogError($"files 路径格式不正确:{fileUrl}");
                return -1;
            }
            if (!int.TryParse(versionMatch.Groups[1].Value, out int fileVersion))
            {
                Debug.LogError($"files 路径版本格式不正确:{fileUrl}");
                return -1;
            }
            fileName = versionMatch.Groups[2].Value;
            return fileVersion;
        }

        public void DownLoadFile(string fileUrl, string savePath)
        {
            // TODO: 测试文件不能下载
            WebClient client = new WebClient
            {
                Credentials = new NetworkCredential("", "")
            };
            savePath = Path.GetFullPath(savePath);
            var folder = Path.GetDirectoryName(savePath);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            client.DownloadFile(fileUrl, savePath);
        }

        static public string GetResUrl(string assetbundleName, hashCheck hashCheck, string resUrl)
        {
            if (hashCheck?.list.ContainsKey(assetbundleName) == false)
            {
                return null;
            }
            return resUrl + "/" + hashCheck.GetVer(assetbundleName) + "/" + assetbundleName;
        }

        void UpdateDownload(ParallelInfo parallelDownloadInfo, int version)
        {
            if (parallelDownloadInfo.taskCompletionSource != null && parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
            {
                parallelDownloadInfo.taskCompletionSource = null;
                Debug.Log("下载更新完成");
                return;
            }

            if (parallelDownloadInfo.taskCompletionSource != null)
            {
                var bCancle = EditorUtility.DisplayCancelableProgressBar($"下载[{version}]版本资源", parallelDownloadInfo.displayTxt, parallelDownloadInfo.currentIdx / (float)parallelDownloadInfo.totalCount);
                if (bCancle && !parallelDownloadInfo.cancel)
                {
                    Debug.LogWarning($"手动取消执行");
                    parallelDownloadInfo.cancel = true;
                }
            }
        }

        void UpdateDeserializationAssetBundleFile(ParallelInfo parallelDownloadInfo, string version)
        {
            if (parallelDownloadInfo.taskCompletionSource != null && parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
            {
                parallelDownloadInfo.taskCompletionSource = null;
                Debug.Log("AssetBundle 解析完成");
                return;
            }

            if (parallelDownloadInfo.taskCompletionSource != null)
            {
                var bCancle = EditorUtility.DisplayCancelableProgressBar($"解析[{version}]版本 AssetBundle 资源", parallelDownloadInfo.displayTxt, parallelDownloadInfo.currentIdx / (float)parallelDownloadInfo.totalCount);
                if (bCancle && !parallelDownloadInfo.cancel)
                {
                    Debug.LogWarning($"手动取消执行");
                    parallelDownloadInfo.cancel = true;
                }
            }
        }

        class ParallelInfo
        {
            public System.Threading.Tasks.TaskCompletionSource<bool> taskCompletionSource;
            public int currentIdx = 0;
            public string displayTxt = string.Empty;
            public int totalCount = 1;
            public bool cancel = false;
        }

        void DownloadResourcesParallel<T>(Dictionary<string, T> resourcesSet, hashCheck resourcesHashCheck, int version, string versionFolder)
        {
            ParallelInfo parallelDownloadInfo = new ParallelInfo
            {
                taskCompletionSource = new System.Threading.Tasks.TaskCompletionSource<bool>(),
                currentIdx = 0,
                totalCount = resourcesSet.Count,
                cancel = false
            };

            try
            {
                var stopWatch = System.Diagnostics.Stopwatch.StartNew();

                ParallelOptions po = new ParallelOptions
                {
                    MaxDegreeOfParallelism = Environment.ProcessorCount
                };

                System.Threading.Tasks.Task.Run(() =>
                {
                    Parallel.ForEach(resourcesSet, po, (keyPair, loopState) =>
                    {
                        var abName = keyPair.Key;
                        try
                        {
                            if (parallelDownloadInfo.cancel)
                            {
                                loopState.Break();
                            }

                            var downloadUrl = GetResUrl(abName, resourcesHashCheck, hserverResourcePath);
                            if (downloadUrl != null)
                            {
                                string savePath = string.Empty;
                                if (string.IsNullOrEmpty(versionFolder))
                                {
                                    savePath = $"{tempResourceFolder}/{version}/{abName}";
                                }
                                else
                                {
                                    savePath = $"{tempResourceFolder}/{versionFolder}/{abName}";
                                }
                                DownLoadFile($"{downloadUrl}", savePath);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogException(ex);
                        }
                        finally
                        {
                            parallelDownloadInfo.displayTxt = abName;
                            parallelDownloadInfo.currentIdx++;
                        }
                    });

                    parallelDownloadInfo.taskCompletionSource.SetResult(true);
                });

                while (true)
                {
                    if (parallelDownloadInfo.taskCompletionSource != null && parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
                    {
                        stopWatch.Stop();
                        Debug.LogWarning($"下载[{version}]版本资源耗时:{stopWatch.ElapsedMilliseconds} ms");
                        break;
                    }
                    else
                    {
                        UpdateDownload(parallelDownloadInfo, version);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                if (parallelDownloadInfo.taskCompletionSource != null && !parallelDownloadInfo.taskCompletionSource.Task.IsCompleted)
                {
                    parallelDownloadInfo.taskCompletionSource.SetResult(true);
                }
            }
        }

        [BoxGroup("对比差异资源")]
        [Button("下载版本差异资源")]
        public void GetVersionResource()
        {
            hserverResourcePath = hserverResourcePath.Trim();
            if (!hserverResourcePath.EndsWith("/"))
            {
                hserverResourcePath += "/";
            }
            hotUrlNew = hotUrlNew.Trim();
            hotUrlOld = hotUrlOld.Trim();

            newVersion = ParseVersion($"{hserverResourcePath}{hotUrlNew}", out string newFileName);
            oldVersion = ParseVersion($"{hserverResourcePath}{hotUrlOld}", out string oldFileName);
            if (newVersion < 0 || oldVersion < 0)
            {
                return;
            }

            try
            {
                var newVersionFilePath = $"{tempResourceFolder}/{newVersion}/{newFileName}";
                var newVersionFileUrl = $"{hserverResourcePath}{hotUrlNew}";
                EditorUtility.DisplayProgressBar("下载资源列表文件", newVersionFileUrl, 0);
                DownLoadFile(newVersionFileUrl, newVersionFilePath);

                var oldVersionFilePath = $"{tempResourceFolder}/{oldVersion}/{oldFileName}";
                var oldVersionFileUrl = $"{hserverResourcePath}{hotUrlOld}";
                EditorUtility.DisplayProgressBar("下载资源列表文件", oldVersionFileUrl, 0);
                DownLoadFile(oldVersionFileUrl, oldVersionFilePath);

                string filesContent = File.ReadAllText(newVersionFilePath);
                var newHashCheck = hashCheck.Parse(filesContent);

                filesContent = File.ReadAllText(oldVersionFilePath);
                var oldHashCheck = hashCheck.Parse(filesContent);

                var diffResult = HotFixFilesDiffDetails.getCompareResult(oldHashCheck, newHashCheck);

                DownloadResourcesParallel(diffResult.allAssetChange, newHashCheck, newVersion, null);
                DownloadResourcesParallel(diffResult.allAssetChange, oldHashCheck, oldVersion, null);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }

            //foreach (var diff in diffResult.allAssetChange)
            //{
            //    currentIdx++;
            //    var abName = diff.Key;
            //    var bCancle = EditorUtility.DisplayCancelableProgressBar("下载新版本中的差异化资源", abName, currentIdx / (float)count);
            //    if (bCancle)
            //    {
            //        Debug.LogWarning($"手动取消执行");
            //        break;
            //    }

            //    var downloadUrl = GetResUrl(abName, newHashCheck, hserverResourcePath);
            //    var savePath = $"{tempResourceFolder}/{newVersion}/{abName}";
            //    DownLoadFile($"{downloadUrl}", savePath);
            //}
        }

        [BoxGroup("对比差异资源")]
        [Button("解析版本资源差异")]
        public void DeserializationAssetBundleVersionFolder()
        {
            var newVersionFilePath = $"{tempResourceFolder}/{newVersion}/";
            DeserializationAssetBundleFolderParallel(newVersionFilePath, null, newVersion.ToString());

            var oldVersionFilePath = $"{tempResourceFolder}/{oldVersion}/";
            DeserializationAssetBundleFolderParallel(oldVersionFilePath, newVersionFilePath, oldVersion.ToString());
        }

        [BoxGroup("对比差异资源")]
        [Button("下载 & 解析版本资源差异")]
        public void GetVersionResourceAndDeserializationAssetBundleVersionFolder()
        {
            GetVersionResource();
            DeserializationAssetBundleVersionFolder();
        }

        public bool DeserializationAssetBundleFolderParallel(string folderPath, string bcompareAssetBundleFolder, string version)
        {
            var unityExePath = EditorApplication.applicationPath;
            var editorInstallPath = Path.GetDirectoryName(unityExePath);
            string webExtractPath = editorInstallPath + "/Data/Tools/WebExtract.exe";
            webExtractPath = Path.GetFullPath(webExtractPath);
            string binary2textPath = editorInstallPath + "/Data/Tools/binary2text.exe";
            binary2textPath = Path.GetFullPath(binary2textPath);

            folderPath = Path.GetFullPath(folderPath);
            if (!Directory.Exists(folderPath))
            {
                Debug.LogError($"[{folderPath}] 路径不存在。");
                return false;
            }

            string[] files = Directory.GetFiles(folderPath, "*.*", SearchOption.AllDirectories);

            var stopWatch = System.Diagnostics.Stopwatch.StartNew();

            ParallelOptions po = new ParallelOptions
            {
                MaxDegreeOfParallelism = Environment.ProcessorCount
            };
            ParallelInfo parallelDeserializationABInfo = new ParallelInfo
            {
                taskCompletionSource = new System.Threading.Tasks.TaskCompletionSource<bool>(),
                currentIdx = 0,
                totalCount = files.Length,
                cancel = false
            };

            try
            {
                System.Threading.Tasks.Task.Run(() =>
                {
                    Parallel.ForEach(files, po, (filePath, loopState) =>
                    {
                        try
                        {
                            if (parallelDeserializationABInfo.cancel)
                            {
                                loopState.Break();
                            }

                            DeserializationAssetBundleFile(folderPath, filePath, webExtractPath, binary2textPath, bcompareAssetBundleFolder);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogException(ex);
                        }
                        finally
                        {
                            parallelDeserializationABInfo.displayTxt = filePath;
                            parallelDeserializationABInfo.currentIdx++;
                        }
                    });

                    parallelDeserializationABInfo.taskCompletionSource.SetResult(true);
                });

                while (true)
                {
                    if (parallelDeserializationABInfo.taskCompletionSource != null && parallelDeserializationABInfo.taskCompletionSource.Task.IsCompleted)
                    {
                        stopWatch.Stop();
                        Debug.LogWarning($"解析[{version}]版本 AssetBundle 资源:{stopWatch.ElapsedMilliseconds} ms");
                        break;
                    }
                    else
                    {
                        UpdateDeserializationAssetBundleFile(parallelDeserializationABInfo, version);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                return false;
            }
            finally
            {
                EditorUtility.ClearProgressBar();
                if (parallelDeserializationABInfo.taskCompletionSource != null && !parallelDeserializationABInfo.taskCompletionSource.Task.IsCompleted)
                {
                    parallelDeserializationABInfo.taskCompletionSource.SetResult(true);
                }
            }

            return true;
        }

        public bool DeserializationAssetBundleFile(string folderPath, string filePath, string webExtractPath, string binary2textPath, string bcompareAssetBundleFolder)
        {
            try
            {
                if (Directory.Exists(filePath) && filePath.EndsWith("__data"))
                {
                    // 已解析过
                    return true;
                }
                if (filePath.EndsWith("manifest"))
                {
                    // 不是 AB，不需要处理
                    return true;
                }
                if (filePath.Contains("files.txt") || filePath.Contains("files2.txt") || filePath.Contains("files2.bytes")
                    || filePath.Contains("_data\\CAB-"))
                {
                    // 不是 AB，不需要处理
                    return true;
                }
                if (!string.IsNullOrEmpty(bcompareAssetBundleFolder))
                {
                    // 如果对比的文件夹路径不为空，则先判断对比文件夹中是否存在同名文件，若不存在，则不需要处理  AssetBundle 信息
                    var bCompareFile = filePath.Replace(folderPath, bcompareAssetBundleFolder);
                    if (!File.Exists(bCompareFile))
                    {
                        // 需要对比差异的资源不存在，可以不处理
                        return true;
                    }
                }
                using (System.Diagnostics.Process process = new System.Diagnostics.Process())
                {
                    process.StartInfo.FileName = webExtractPath;
                    process.StartInfo.Arguments = Path.GetFullPath(filePath);
                    //设置不在新窗口中启动新的进程
                    process.StartInfo.CreateNoWindow = true;
                    //不使用操作系统使用的shell启动进程
                    process.StartInfo.UseShellExecute = false;
                    //将输出信息重定向
                    process.StartInfo.RedirectStandardOutput = true;
                    process.Start();
                    process.WaitForExit();
                }

                using (System.Diagnostics.Process process = new System.Diagnostics.Process())
                {
                    var fileDataFolder = filePath + "_data/";
                    if (!Directory.Exists(fileDataFolder))
                    {
                        // webextract 执行失败，未生成对应的 xx_data 文件夹
                        return false;
                    }
                    string[] dataFileSet = Directory.GetFiles(fileDataFolder, "*", SearchOption.AllDirectories);
                    foreach (string dataFile in dataFileSet)
                    {
                        if (!string.IsNullOrEmpty(Path.GetExtension(dataFile)))
                        {
                            // 不处理 .resS 文件
                            continue;
                        }
                        process.StartInfo.FileName = binary2textPath;
                        process.StartInfo.Arguments = Path.GetFullPath(dataFile);
                        //设置不在新窗口中启动新的进程
                        process.StartInfo.CreateNoWindow = true;
                        //不使用操作系统使用的shell启动进程
                        process.StartInfo.UseShellExecute = false;
                        //将输出信息重定向
                        process.StartInfo.RedirectStandardOutput = true;
                        process.Start();
                        process.WaitForExit();
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }

            return true;
        }

        [BoxGroup("解析文件夹")]
        [FolderPath(AbsolutePath = true, UseBackslashes = false)]
        public string resourcesFolder;

        [BoxGroup("解析文件夹")]
        [ReadOnly]
        public string resourcesSrcUrl;

        [Button("下载新版本完整资源")]
        [BoxGroup("解析文件夹")]
        [HorizontalGroup("解析文件夹/Load & Deserialization Folder")]
        public void GetVersionFullResource()
        {
            hserverResourcePath = hserverResourcePath.Trim();
            if (!hserverResourcePath.EndsWith("/"))
            {
                hserverResourcePath += "/";
            }
            hotUrlNew = hotUrlNew.Trim();

            newVersion = ParseVersion($"{hserverResourcePath}{hotUrlNew}", out string newFileName);
            if (newVersion < 0)
            {
                return;
            }

            try
            {
                resourcesFolder = $"{tempResourceFolder}/{newVersion}_full/";
                resourcesFolder = Path.GetFullPath(resourcesFolder);
                resourcesSrcUrl = $"{hserverResourcePath}/{newVersion}";

                var newVersionFilePath = $"{tempResourceFolder}/{newVersion}_full/{newFileName}";
                var newVersionFileUrl = $"{hserverResourcePath}{hotUrlNew}";
                EditorUtility.DisplayProgressBar("下载资源列表文件", newVersionFileUrl, 0);
                DownLoadFile(newVersionFileUrl, newVersionFilePath);

                string filesContent = File.ReadAllText(newVersionFilePath);
                var newHashCheck = hashCheck.Parse(filesContent);

                DownloadResourcesParallel(newHashCheck.list, newHashCheck, newVersion, $"{newVersion}_full");
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            finally
            {
                EditorUtility.ClearProgressBar();
            }
        }

        [Button("解析文件夹 AssetBundle 资源")]
        [BoxGroup("解析文件夹")]
        [HorizontalGroup("解析文件夹/Load & Deserialization Folder")]
        public void OnDeserializationAssetBundleFolder()
        {
            if(string.IsNullOrEmpty(resourcesFolder))
            {
                Debug.LogError($"请先选择文件夹路径");
                return;
            }
            var folderName = Path.GetFileName(resourcesFolder);
            DeserializationAssetBundleFolderParallel(resourcesFolder, null, folderName);
        }
    }
}

#endif