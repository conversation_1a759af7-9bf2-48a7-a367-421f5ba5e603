#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.IO;
using System.Text;
using UnityEditor;
using UnityEngine;
using War.Script;
using XLua;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class LuaExecTC
    {
        [HideInInspector]
        public const string TIPS = "执行lua逻辑（可独立运行某个lua脚本）";

        [System.Serializable]
        public class Injection
        {
            public string name;
            public Object value;
        }
        [PropertyOrder(1)]
        public string title;
        [PropertyOrder(2)]
        public Injection[] injections;
        [PropertyOrder(3)]
        [FoldoutGroup("codeBoard")]
        public string[] loaders = new string[] {
        "Assets/Lua"
    };
        //[FoldoutGroup("codeBoard")]
        [PropertyOrder(5)]
        [Button]
        void CreateCodeFile()
        {

            if (injections.Length > 0 && injections[0].value is TextAsset)
            {
            }
            else
            {
                if(string.IsNullOrEmpty(title))
                {
                    title = string.Format("{0}", System.DateTime.Now.ToUniversalTime()).Replace("/","_").Replace(" ", "_").Replace(":", "-");
                }
                var filename = string.Format("{0}{1}.txt", "Assets/Scripts/ExecLua/", title);
                ToolUti.CheckDir(filename);
                File.WriteAllText(filename, "");
                AssetDatabase.Refresh();
                ArrayUtility.Add(ref injections, new Injection()
                {
                    name = "code",
                    value = AssetDatabase.LoadMainAssetAtPath(filename),
                });
            }
            var codet = ArrayUtility.Find(injections,(i) =>
            {
                return i.name == "code";
            });
            if(codet!=null)
            {
                Selection.activeObject = codet.value;
            }
            else
            {
                "".Print("Error no code");
            }
        }
        [PropertyOrder(6)]
        [Button]
        void Exec()
        {
            XLua.LuaEnv luaEnv = null;
            if(LuaManager.Instance)
            {
                luaEnv = LuaManager.Instance.luaEnv ;
            }
            LuaTable scriptEnv = null;
            if (luaEnv==null)
            {
                luaEnv = new LuaEnv(); 

                scriptEnv = (luaEnv.NewTable()); 
                // 为每个脚本设置一个独立的环境，可一定程度上防止脚本间全局变量、函数冲突
                LuaTable meta = luaEnv.NewTable();
                meta.Set("__index", luaEnv.Global);
                scriptEnv.SetMetaTable(meta);

                foreach (var injection in injections)
                {
                    scriptEnv.Set(injection.name, injection.value);
                }
                luaEnv.AddLoader((ref string module) =>
                {
                    foreach (var folder in loaders)
                    {

                        var files = System.IO.Directory.GetFiles(folder, module + ".txt", System.IO.SearchOption.AllDirectories);
                        if (files.Length > 0)
                        {
                            var bs = File.ReadAllText(files[0], UTF8Encoding.UTF8);
                            return System.Text.UTF8Encoding.UTF8.GetBytes(bs);
                        }
                    }
                    return null;
                });
            }
            var code = codeBoard;
            if(!string.IsNullOrEmpty(code))
            {
                var res = luaEnv.DoString(code, "chunk", scriptEnv);
                "".Print(res);
            }

            if (injections.Length > 0 && injections[0].value is TextAsset)
            {
                code = (injections[0].value as TextAsset).text;
                var res = luaEnv.DoString(code, "chunk", scriptEnv);
                "".Print(res);
            }
        }
        [PropertyOrder(4)]
        [FoldoutGroup("codeBoard")]
        [TextArea(20,50)]
        public string codeBoard;

    }
}

#endif