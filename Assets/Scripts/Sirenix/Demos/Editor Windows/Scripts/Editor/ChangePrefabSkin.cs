using System.IO;
using System.Reflection;
using System.Text;
using UnityEngine;
using UnityEngine.Profiling;

#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using System;
    using System.Collections.Generic;
    using UnityEditor;


    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ChangePrefabSkin : GlobalConfig<ChangePrefabSkin>
    {
        [Serializable]
        public class SkinInfos
        {
            [HorizontalGroup()] [InlineEditor(InlineEditorModes.LargePreview)]
            public Texture oldTexture;

            [HorizontalGroup()] [InlineEditor(InlineEditorModes.LargePreview)]
            public Texture newTexture;

            [Button]
            void ChangeSkin()
            {
                if (oldTexture != null && newTexture != null)
                {
                    string oldPath = AssetDatabase.GetAssetPath(oldTexture);
                    string newPath = AssetDatabase.GetAssetPath(newTexture);
                    File.Copy(newPath, oldPath, true);
                    SetTextureSize2048(oldPath, BuildTarget.Android, BuildTarget.iOS, BuildTarget.StandaloneWindows,
                        BuildTarget.StandaloneWindows64);
                }
                else
                {
                    Debug.Log("存在贴图为空，无法替换");
                }
            }

            public void SetTextureSize2048(string t2dPath, params BuildTarget[] targets)
            {
                var textureImporter = TextureImporter.GetAtPath(t2dPath) as TextureImporter;

                foreach (var buildTarget in targets)
                {
                    TextureImporterPlatformSettings textureImporterPlatformSettings =
                        textureImporter.GetPlatformTextureSettings(buildTarget.ToString());
                    textureImporterPlatformSettings.overridden = true;
                    textureImporterPlatformSettings.maxTextureSize = 2048;
                    textureImporter.SetPlatformTextureSettings(textureImporterPlatformSettings);
                    EditorUtility.SetDirty(textureImporter);
                }

                textureImporter.SaveAndReimport();
                AssetDatabase.Refresh();
            }
        }


        public GameObject prefab;

        public List<SkinInfos> skins;

        [Button]
        void LoadSkins()
        {
            var prefabPath = AssetDatabase.GetAssetPath(prefab);
            var deps = AssetDatabase.GetDependencies(prefabPath);
            skins = new List<SkinInfos>(2 << 7);
            foreach (var dep in deps)
            {
                if (Path.GetExtension(dep).ToLower() == ".png")
                {
                    Debug.Log(dep);
                    skins.Add(new SkinInfos()
                    {
                        oldTexture = AssetDatabase.LoadAssetAtPath<Texture>(dep)
                    });
                }
            }
        }
    }
}
#endif