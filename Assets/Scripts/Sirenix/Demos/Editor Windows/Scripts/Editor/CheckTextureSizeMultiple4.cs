
using System.IO;
using System.Reflection;
using System.Text;
using UnityEngine;
using UnityEngine.Profiling;

#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
	using Sirenix.Utilities;
	using System;
	using System.Collections.Generic;
	using UnityEditor;


	[GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
	public class CheckTextureSizeMultiple4 : GlobalConfig<CheckTextureSizeMultiple4>
	{
		
		[Serializable]
		public class StrProto
		{
			[HideLabel]
			public string name;
			[LabelText("NativeScale")]
			public string NativeScale;

			[LabelText("NativeSize")] 
			public string NativeSizeLab;

			private float _nativeSize = 0f;
			public float NativeSize
			{
				set
				{
					_nativeSize = value;
					NativeSizeLab = EditorUtility.FormatBytes((int)_nativeSize);
				}
				get
				{
					return _nativeSize;
				}
			}
			[LabelText("FixedScale")]
			public string FixedScale;

			[LabelText("FixedSize")] 
			public string FixedSizeLab;

			private float _fixedSize = 0f;

			public float FixedSize
			{
				set
				{
					_fixedSize = value;
					FixedSizeLab = EditorUtility.FormatBytes((int)_fixedSize);
				}
				get
				{
					return _fixedSize;
				}
			}

			public Texture2D t2d;
			[Button]
			public void Fixted()
			{
				var alpha0 = new Color (0, 0, 0, 0);
				var wid = (int)(Mathf.RoundToInt (t2d.width /4)*4); 
				var hei = (int)(Mathf.RoundToInt (t2d.height /4)*4);

				var p = AssetDatabase.GetAssetPath (t2d);
				var ip = AssetImporter.GetAtPath (p) as TextureImporter;
				if(wid!=t2d.width || hei!=t2d.height)
				{
					Texture2D tTex = null;
					tTex = TexCache.Instance.CacheTex (wid, hei); 

					EditorHelp.ApplyUnReadTex (t2d, tTex);

					var bs = p.ToLower ().Contains (".jpg") ? tTex.EncodeToJPG () : tTex.EncodeToPNG ();
					File.WriteAllBytes (p, bs);
					ip.SaveAndReimport ();
				}

			}

		}
		
		[FolderPath]
		public List<string> findassets = new List<string>{
		};
        [Button]
        void Check()
        {
	        if (findassets.Count <= 0)
	        {
		        Debug.Log("请选择路径");
		        return;
	        }
	        int startIndex = 0;
	        SpriteList.Clear();

	        var textureList = AssetDatabase.FindAssets("t:texture2D", findassets.ToArray());
	        EditorApplication.update = delegate ()
	        {

		        try
		        {
			        int count = textureList.Length;
			        for (int i = 0; i < count; i++)
			        {

				        string gid = textureList[i];
				        var path = AssetDatabase.GUIDToAssetPath(gid);
				        bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", path, (float)startIndex / (float)textureList.Length);
				        var texture = AssetDatabase.LoadAssetAtPath<Texture2D> (path);
				        var textureImporter = TextureImporter.GetAtPath(AssetDatabase.GetAssetPath(texture)) as TextureImporter;
				        startIndex++;
				        var fixedWidth = (int)(Mathf.RoundToInt (texture.width /4)*4); 
				        var fixedHeight = (int)(Mathf.RoundToInt (texture.height /4)*4);
				        bool iscan = !(textureImporter.textureType == TextureImporterType.Sprite 
				                       && !textureImporter.spritePackingTag.Equals(""));
				        if (iscan)
				        {
					        iscan = !(fixedWidth == texture.width
					                 && fixedHeight == texture.height);
				        }
				        if (iscan)
				        {
					        var nativeSize = texture.width * texture.height * GetOnePixelSizeByFormat(textureImporter.textureFormat);

					        var fixedSize = fixedWidth * fixedHeight * GetOnePixelSizeByFormat(TextureImporterFormat.ETC2_RGBA8);
					        var sp = new StrProto() { name = path,
						        NativeScale = string.Format("{0} x {1}",texture.width,texture.height),
						        NativeSize = nativeSize,
						        FixedScale = string.Format("{0} x {1}",fixedWidth,fixedHeight),
						        FixedSize =  fixedSize,
						        t2d = texture
					        };
					        SpriteList.Add(sp);
				        }


				        if (isCancel || startIndex >= textureList.Length)
				        {
					        EditorUtility.ClearProgressBar();
					        EditorApplication.update = null;
					        startIndex = 0;

					        Debug.Log("匹配结束");

					        return;
				        }
			        }
		        }
		        catch (Exception e)
		        {
			        Debug.LogError(e.ToString());
			        EditorApplication.update = null;
			        EditorUtility.ClearProgressBar();
		        }

	        };
	        
        }
        
        public List<StrProto> SpriteList = new List<StrProto>();
        float GetOnePixelSizeByFormat(TextureImporterFormat formatType)
        {
	        switch (formatType)
	        {
		        case TextureImporterFormat.ARGB32:
			        return 4f;
		        case TextureImporterFormat.ARGB16:
			        return 2f;
		        case TextureImporterFormat.RGB24:
			        return 3f;
		        case TextureImporterFormat.RGB16:
			        return 2f;
		        case TextureImporterFormat.ETC_RGB4:
			        return 0.5f;
		        case TextureImporterFormat.ETC2_RGB4:
			        return 0.5f;
				case TextureImporterFormat.ETC2_RGBA8:
					return 1f;
				case TextureImporterFormat.ETC2_RGB4_PUNCHTHROUGH_ALPHA:
					return 0.5f;
				case TextureImporterFormat.PVRTC_RGB2:
					return 0.25f;
				case TextureImporterFormat.PVRTC_RGB4:
					return 0.5f;
				case TextureImporterFormat.PVRTC_RGBA2:
					return 0.25f;
				case TextureImporterFormat.PVRTC_RGBA4:
					return 0.5f;
				case TextureImporterFormat.Automatic:
					return 1f;
						
		        
	        }

	        return 4f;
        }

        [Button("一键修复")]
        void AllFixed()
        {
	        PrintBoard();

	        for (int i = 0; i < SpriteList.Count; i++)
	        {
		        var info = SpriteList[i];
		        info.Fixted();
	        }
			
	        Check();
        }
        
        [Button("打印到日志框")]
        void PrintBoard()
        {
	        blackboard = "";
	        StringBuilder stringBuilder = new StringBuilder();
	        float nativeAllSize = 0f;
	        float fixedAllSize = 0f;
	        for (int i = 0; i < SpriteList.Count; i++)
	        {
		        var info = SpriteList[i];
		        nativeAllSize += info.NativeSize;
		        fixedAllSize += info.FixedSize;
		        stringBuilder.Append(String.Format("{0}\t{1}\t{2}\t{3}\t{4}\n",info.name,info.NativeScale,
			        EditorUtility.FormatBytes((int)info.NativeSize),info.FixedScale,EditorUtility.FormatBytes((int)info.FixedSize)));
	        }

	        stringBuilder.Append(String.Format("{0}\t{1}\t{2}\t{3}\t{4}\t{5}\n",
		        "总计","",EditorUtility.FormatBytes((int)nativeAllSize),"",
		        EditorUtility.FormatBytes((int)fixedAllSize),"节省："+EditorUtility.FormatBytes((int)(nativeAllSize-fixedAllSize))));
	        blackboard = stringBuilder.ToString();
        }
        [TextArea]
        public string blackboard = "";
	}
	
}
#endif