#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CheckMaterialTexToolTC
    {
        [System.Serializable]
        public class materialCfg
        {
            public string path;
            [SerializeField]
            public Dictionary<string, string> attrs = new Dictionary<string, string>();
        }
        [HideInInspector]
        public const string TIPS = "CheckMaterialTexToolTC";

        [FolderPath]
        public string sourceMaterialPath= "Assets/Art/Effects/Materials";
        [SerializeField]
        public Dictionary<string, materialCfg> sourceMaterialsConfigs = new Dictionary<string, materialCfg>();

        [Button]
        void Exec()
        {
            sourceMaterialsConfigs.Clear();
            var guids = AssetDatabase.FindAssets("t:Material", new string[] { sourceMaterialPath });
            var n = 2;
            ToolUti.IterUpdateFinish(guids, (g) =>
            {
                //if (--n < 0) return g;
                var f = AssetDatabase.GUIDToAssetPath(g);
                var mat = AssetDatabase.LoadMainAssetAtPath(f) as Material;
                if (mat)
                {
                    var cfg = new materialCfg()
                    {
                        path = f,
                    };
                    var pnames = mat.GetTexturePropertyNames();
                    //"".Print(UIHelper.ToJson(pnames));

                    foreach (var pname in pnames)
                    {
                        Texture texture = mat.GetTexture(pname);
                        var texpath = AssetDatabase.GetAssetPath(texture);
                        if(string.IsNullOrEmpty(texpath))
                        {
                            continue;
                        }
                        cfg.attrs[pname] = texpath;
                    }
                    "".Print(f, cfg.attrs.Count,UIHelper.ToJson(cfg.attrs));

                    sourceMaterialsConfigs[f] = cfg;
                }
                return g;
            }, () =>
            {
            }, TIPS);
        }

        [Button]
        void ExportJson()
        {
            string filename = "MaterialTexConfig.json";
            var path = EditorUtility.OpenFolderPanel(TIPS, "Assets/Json", "");
            if (string.IsNullOrEmpty(path) == false)
            {
                File.WriteAllText(path + "/" + filename, UIHelper.ToJson(sourceMaterialsConfigs));
            }
        }

        [Button]
        void CheckMat()
        {
            string filename = "MaterialTexConfig.json";
            var path = EditorUtility.OpenFilePanel(TIPS, "Assets/Json","json");
            if (string.IsNullOrEmpty(path)) return;
            var str = File.ReadAllText(path);
            "".Print(str);

            sourceMaterialsConfigs = UIHelper.ToObj<Dictionary<string, materialCfg>>(str);
            var n = 2;

            var list = new List<string>();
            foreach (var kv in sourceMaterialsConfigs)
            {

                var f = kv.Key;
                var mat = AssetDatabase.LoadMainAssetAtPath(f) as Material;

                if(!mat)
                {
                    "".Print("Error mat not exist");
                    continue;
                }
                var bdirty = false;
                foreach (var kkv in kv.Value.attrs)
                {
                    if (string.IsNullOrEmpty(kkv.Value))
                    {
                        "".Print("Warning checkMat value empty", f, kkv.Key);
                        continue;
                    }
                    if (mat.GetTexture(kkv.Key) == null)
                    {
                        if(File.Exists(kkv.Value)==false)
                        {
                            "".Print("Error checkMat value not exist", f, kkv.Key, kkv.Value);
                            continue;
                        }
                        //if (--n < 0) continue;

                        var tex = AssetDatabase.LoadMainAssetAtPath(kkv.Value) as Texture2D;
                        mat.SetTexture(kkv.Key, tex);
                        EditorUtility.SetDirty(mat);
                        bdirty = true;
                    }
                    else
                    {
                        //"".Print("Skip", f, kkv.Key, kkv.Value);
                    }
                }
                if (bdirty)
                {
                    list.Add(f);
                }
            }
            UIHelper.ImportAssets(list.ToArray());
            "".Print("Deal CheckMat", list.Count,UIHelper.ToJson(list));
        }
    }
}

#endif