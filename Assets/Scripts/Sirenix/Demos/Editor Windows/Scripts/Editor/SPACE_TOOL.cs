#if UNITY_EDITOR
using UnityEditor;
using War.Common;

namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.OdinInspector.Editor;
    using System.Linq;
    using Sirenix.Utilities.Editor;
    using Sirenix.Serialization;
    using UnityEditor;
    using System;
    using System.Reflection;

    // 
    // Be sure to check out OdinMenuStyleExample.cs as well. It shows you various ways to customize the look and behaviour of OdinMenuTrees.
    // 

    public class SPACE_TOOL : OdinMenuEditorWindow
    {

        [MenuItem("Tools/Odin Inspector/DevMode ")]
        private static void DevMode()
        {
            var b = EditorPrefs.GetBool("DeveloperMode", false);
            EditorPrefs.SetBool("DeveloperMode", !b);
        }
        [MenuItem("Tools/Odin Inspector/SPACE_TOOL &7")]
        private static void OpenWindow()
        {
            var window = GetWindow<SPACE_TOOL>(true);
            //window.position = GUIHelper.GetEditorWindowRect().AlignCenter(800, 600);
        }

        protected override OdinMenuTree BuildMenuTree()
        {
            
               OdinMenuTree tree = new OdinMenuTree(supportsMultiSelect: true)
            {
                { "Home",                       this,                           EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/SpriteAtlasViewer",   SpriteAtlasViewer.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/SpritePackingTag/Sprite2PackingTag",   Sprite2PackingTag.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/SpritePackingTag/Layout2PackingTag",   Layout2PackingTag.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/SpritePackingTag/LayoutSpriteList",   LayoutSpriteList.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/ExludeFile(把没用到的图片扫出来)",          ExludeFile.Instance,            EditorIcons.Link                       }, // Draws the this.someData field in this case.  
                { "Editor/UI/CheckBigPic(设定阈值，搜索出大图，并每个大图打上独立的图集)",         CheckBigPic.Instance,           EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Res/SelectBuilds/BuildCertainAb(可以选择特定路径跟后缀进行打包，打成一个整包)",   SelectBuilds.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Res/SelectBuilds/ExtractScriptTool(快速解析修改包内lua脚本)",         ExtractScriptTool.Instance,           EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Res/SelectBuilds/FileSizeConfig",         FileSizeConfig.Instance,           EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Res/FileSize",   FileSizeToolGConfig.Instance,     EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Res/ResModuleTool",  ResModuleTool.Instance,    EditorIcons.Link              }, // 
                { "Editor/Res/CharacterResConfig(获取热更文件的信息，并过滤出角色的预设体,与其依赖)",  CharacterResConfig.Instance,    EditorIcons.Link                       }, // Draws the this.someData field in this case.
                { "Editor/Res/HotFixFilesDiffDetails（对比两个版本的ab包的差异，并输出差异信息）",  HotFixFilesDiffDetails.Instance,    EditorIcons.Link                       },
                { "Editor/Res/ConvertHotFixFilesDiff（转换版本的 AB 资源为文本格式，对比属性修改变化）",  ConvertHotFixFilesDiff.Instance,    EditorIcons.MagnifyingGlass                       },
                { "Editor/Res/AssetsDependencesToolGConfig(查看当前ab包的引用于依赖)",  AssetsDependencesToolGConfig.Instance,    EditorIcons.Link                       }, // Draws the this.someData field in this case.
                { "Editor/Res/CheckFileDependece(资源依赖检查，附带各种方式排序)",  CheckFileDependece.Instance,    EditorIcons.Link              }, // 
                { "Editor/Res/CheckFolderFileSizes()",  CheckFolderFileSizes.Instance,    EditorIcons.Link              }, // 
                { "Editor/Res/ExportEffectAssets",  ExportEffectAssets.Instance,    EditorIcons.Link        }, // 特效资源导出工具
                { "Editor/Res/ExtractCommonUI",  ExtractCommonUI.Instance,    EditorIcons.Link        }, // ExtractCommonUI
                { "Editor/Res/ModifyPictureSize",  ModifyPictureSize.Instance,    EditorIcons.Link        }, // 修改图片尺寸
                { "Editor/Res/ReplaceOldEffectsUI",  ReplaceOldEffectsUI.Instance,    EditorIcons.Link        }, // 特效资源更换工具
                { "Editor/Res/OptimizeAnimationClipTool",  OptimizeAnimationClipTool.Instance,    EditorIcons.Link        }, 
                { "Editor/Res/ShadervariantsExport",  ShadervariantsExport.Instance,    EditorIcons.Link        }, 
                { "Editor/Lit/HexTool(进制转换工具)",             HexTool.Instance,               EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/Lit/CheckFunc(抓取manifest的crc值)",           CrcFromManifest.Instance,             EditorIcons.Link                       }, // Draws the this.someData field in this case. 
                { "Editor/UI/BigImageToRawImage(检索出大图，并用rawImage替代显示)",  BigImageToRawImage.Instance,    EditorIcons.Link              }, // 大图image直接修改成rawimage工具
                { "Editor/UI/FindScrTool(查找预设体中，SpriteMask的情况)",  FindScrTool.Instance,    EditorIcons.Link              }, // 大图image直接修改成rawimage工具
                { "Editor/UI/FixSpriteMaskTool(查找预设体中SpriteMask的情况，并提供统一recttransform设置)",  FixSpriteMaskTool.Instance,    EditorIcons.Link              }, // SpriteMask 自适应添加配置
                { "Editor/UI/CheckTextureSizeMultiple4(一键搜索出所有不是4倍数的图片，并提供一键修正跟日志信息)",  CheckTextureSizeMultiple4.Instance,    EditorIcons.Link              }, // 
                 { "Editor/UI/CheckPrefabZAndScale",  CheckPrefabZAndScale.Instance,    EditorIcons.Link              }, //  
                
				{ "Editor/UI/ChangeTextureSizeTo32",  ChangeTextureSizeTo32.Instance,    EditorIcons.Link              }, // 
                { "Editor/Tot/config_editor",  TotEventConfigEditor.Instance,    EditorIcons.Link              }, // 
                { "Editor/Tot/res_key_editor",  ResKeyTool.Instance,    EditorIcons.Link              }, // 
                { "Editor/Cmd/custom_command_tool(资源校验工具集)",  CustomCommandTool.Instance,    EditorIcons.Link              }, // 
                { "Editor/Cmd/lua_command_tool(可在编辑器直接运行lua脚本)",  LuaCommandTool.Instance,    EditorIcons.Link              }, //
                { "Editor/UI/ChangePrefabSkin",  ChangePrefabSkin.Instance,    EditorIcons.Link              }, // 
                { "Editor/Res/AutoExportEffectAssets",  AutoExportEffectAssets.Instance,    EditorIcons.Link              }, // 
                { "Editor/Res/DeviceLevelCSV2Json",  DeviceLevelCSV2Json.Instance,    EditorIcons.Link              }, // 
                


            };

            tree.SortMenuItemsByName();
            return tree;
        }

    }
}


//[CustomEditor(typeof(ResKey))]
//public class ResKeyInspector : UnityEditor.Editor
//{ 
     
//    public override void OnInspectorGUI()
//    {
//        var t = target as ResKey;
//        EditorGUILayout.LabelField("Key:"+t.keys);
//        EditorGUILayout.LabelField("Length:"+ t);

//    }
//}
#endif