#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj;
    using Sirenix.Utilities;
    using System.Collections.Generic;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Editor/Config")]
    public class LuaCommandTool : GlobalConfig<LuaCommandTool>
    { 
        private const string message = "可在编辑器直接执行lua脚本";

        [InfoBox(message)] public string destriptions = "";
        
        
        [BoxGroup(LuaExecTC.TIPS)]
        public List<LuaExecTC> mLuaExecTC = new List<LuaExecTC>();

        
    }
}
#endif
