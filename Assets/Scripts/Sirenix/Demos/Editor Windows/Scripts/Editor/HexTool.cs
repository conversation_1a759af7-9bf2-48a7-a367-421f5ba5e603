#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class HexTool : GlobalConfig<HexTool>
    {
        [InfoBox("简单16进制10进制数转换")]
        [Toolt<PERSON>("标记,作为打印使用")]
        public string mark;
        [<PERSON><PERSON><PERSON>("16进制数值")]
        public string hexString1;
        [<PERSON>lt<PERSON>("10进制数值")]
        public string intValue;

        [<PERSON><PERSON>("通过16进制计算10进制")]
        void CalInt()
        {
            if (string.IsNullOrEmpty(hexString1) == false)
            {
                var intV = long.Parse(hexString1, System.Globalization.NumberStyles.HexNumber);
                intValue = intV.ToString();
            }
        }

        [But<PERSON>("通过10进制计算16进制")]
        void CalHex()
        { 
            var longV = long.Parse(intValue);
            hexString1 = longV.ToString("X");
        }
        [But<PERSON>("打印到日志框")]
        void PrintBoard()
        {
            var args = System.Environment.GetCommandLineArgs();
            Debug.Log(UIHelper.ToJson(args));


            blackboard += string.Format("{0}:{1}=>{2}\n", mark, hexString1, intValue);
        }
        [TextArea]
        public string blackboard = "";
    }
}
#endif
