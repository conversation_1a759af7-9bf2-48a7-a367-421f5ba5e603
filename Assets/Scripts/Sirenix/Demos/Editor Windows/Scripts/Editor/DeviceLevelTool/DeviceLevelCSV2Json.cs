using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.IO;
using System.Diagnostics;

using Sirenix.OdinInspector;
using Sirenix.Utilities;
using UnityEditor;

namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class DeviceLevelCSV2Json : GlobalConfig<DeviceLevelCSV2Json>
    {
        [FoldoutGroup("DeviceLevelCSV2Json")] [FolderPath]
        public string checkPath = "";

        [FoldoutGroup("DeviceLevelCSV2Json")]
        [Button("导出机型等级json文件")]
        public void Start2CSV()
        {
            // 读取CSV文件
            List<Dictionary<string, string>> rows =
                ReadCsv(checkPath + "/DeviceLevel.csv");
            Dictionary<string, Dictionary<string, string>> indexedRows =
                new Dictionary<string, Dictionary<string, string>>();
            for (int i = 0; i < rows.Count; i++)
            {
                Dictionary<string, string> indexColumns = new Dictionary<string, string>();
                string id = null;
                foreach (var item in rows[i])
                {
                    if (id == null)
                    {
                        id = item.Value;
                        continue;
                    }
                    indexColumns.Add(item.Key, item.Value);
                }
                indexedRows.Add(id, indexColumns);
            }

            // 将读取到的数据转换为JSON格式
            string jsonStr = JsonConvert.SerializeObject(indexedRows,  Formatting.Indented);

            // 将JSON字符串写入TXT文件
            if (File.Exists(checkPath + "/DeviceLevel.json"))
                File.Delete(checkPath + "/DeviceLevel.json");
            File.WriteAllText(checkPath + "/DeviceLevel.json", jsonStr);
            AssetDatabase.Refresh();
            AssetImporter im = AssetImporter.GetAtPath(checkPath + "/DeviceLevel.json");
            SetABNameByPath(checkPath + "/DeviceLevel.json");
            ExportCSV.OpenDirectory(checkPath);
        }
        
        public void SetABNameByPath(string assetPath)
        {
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
            assetImporter.assetBundleName = assetBundleName;
        }

        List<Dictionary<string, string>> ReadCsv(string filePath)
        {
            List<Dictionary<string, string>> rows = new List<Dictionary<string, string>>();
            int index = 0;
            using (var reader = new StreamReader(filePath))
            {
                string[] headers = reader.ReadLine().Split(',');
                while (!reader.EndOfStream)
                {
                    string[] values = reader.ReadLine().Split(',');
                    if (index > 0)
                    {
                        if (values[0] != "")
                        {
                            Dictionary<string, string> row = new Dictionary<string, string>();
                            for (int i = 0; i < headers.Length; i++)
                            {
                                row.Add(headers[i], values[i]);
                            }

                            rows.Add(row);
                        }
                        index++;
                    }
                    else
                    {
                        index++;
                    }
                }
            }
            return rows;
        }
    }
}