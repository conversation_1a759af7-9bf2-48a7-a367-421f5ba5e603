using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using UnityEngine;

namespace Sirenix.OdinInspector.Demos
{
    class PathUtil
    {        public static string GetRelativePath(string path)
        {
            string srcPath = path.Replace("\\", "/");
            var retPath = Regex.Replace(srcPath, @"\b.*Assets", "Assets");
            return retPath;
        }
        public static string GetAbsolutePath(string path)
        {
            string srcPath = path.Replace("\\", "/");
            var retPath = Regex.Replace(srcPath, @"\b.*Assets", Application.dataPath);
            return retPath;
        }
    }
}
