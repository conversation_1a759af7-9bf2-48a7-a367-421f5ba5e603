#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEngine.UI;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.IO;
    using System.Security.Cryptography;
    using System.Collections.Generic;
    using System;
    using System.Linq;
    using War.UI;
    using System.Text;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ExportCSV
    {
        public class PatientStatisticsOutputDto
        {
            public List<string> datas = new List<string>();
            public void ClearData()
            {
                datas.Clear();
            }
        }
        public bool ExportPatientStatisticsDetails(List<PatientStatisticsOutputDto> patientDetails, string targetPath, string handStr)
        {
            try
            {
                var patientList = patientDetails;

                if (File.Exists(targetPath))
                {
                    File.Delete(targetPath);
                }
                StringBuilder strColu = new StringBuilder();
                StringBuilder strValue = new StringBuilder();
                StreamWriter sw = new StreamWriter(new FileStream(targetPath, FileMode.CreateNew), Encoding.Default);
                strColu.Append(handStr);
                int length = handStr.Split(',').Length;
                sw.WriteLine(strColu);
                foreach (var dr in patientList)
                {
                    strValue.Remove(0, strValue.Length);//移出
                    for (int i = 0; i < dr.datas.Count; i++)
                    {
                        if (dr.datas.Count - 1 > i)
                            strValue.Append(dr.datas[i] + ",");
                        else
                            strValue.Append(dr.datas[i]);
                    }
                    sw.WriteLine(strValue);
                }
                sw.Close();
                return true;

            }
            catch (Exception e)
            {
                Debug.LogError("导出失败:" + e);
                return false;
            }

        }
        public static void OpenDirectory(string path)
        {
            if (string.IsNullOrEmpty(path)) return;

            path = path.Replace("/", "\\");
            if (!Directory.Exists(path))
            {
                Debug.LogError("No Directory: " + path);
                return;
            }

            System.Diagnostics.Process.Start("explorer.exe", path);
        }
    }
}
#endif