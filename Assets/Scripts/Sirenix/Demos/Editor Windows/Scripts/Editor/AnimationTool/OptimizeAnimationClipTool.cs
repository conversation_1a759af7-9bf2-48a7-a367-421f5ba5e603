#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using UnityEditor;
using Sirenix.Utilities;
using UnityEngine;
using War.Base;
using System.IO;
using System.Reflection;
using UnityEditor.Animations;

using UltimateGameTools.MeshSimplifier;
using System.Collections;
// using ToonyColorsPro.Utilities;
using War.Battle;
//降低动画精度，体积优化工具
namespace Sirenix.OdinInspector.Demos
{
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class OptimizeAnimationClipTool : GlobalConfig<OptimizeAnimationClipTool>
    {
        #region 优化anim
        List<AnimationOpt> _AnimOptList = new List<AnimationOpt>();
        Dictionary<string, AnimationOpt> _cachedOpts = new Dictionary<string, AnimationOpt>();
        List<string> _Errors = new List<string>();
        List<string> filterFolders = new List<string>();

        [FoldoutGroup("OptimizeAnimationClipTool")]
        public string filterExtensionStr = "3";
        [FoldoutGroup("OptimizeAnimationClipTool")]
        [Tooltip("是否去除curve")]
        public bool scaleOptCurve = false;
        [FoldoutGroup("OptimizeAnimationClipTool")]
        [FolderPath]
        public List<string> checkPaths = new List<string>();

        [FoldoutGroup("OptimizeAnimationClipTool")]
        public List<AnimationClip> animationClips = new List<AnimationClip>();
        [FoldoutGroup("OptimizeAnimationClipTool")]
        [Button("清理列表")]
        public void ClearAnimationClip()
        {
            animationClips.Clear();
        }
        [FoldoutGroup("OptimizeAnimationClipTool")]
        [Button("获取AnimationClip")]
        public void GetAnimationClip()
        {
            Finish();
            animationClips.Clear();
            for (int i = 0; i < checkPaths.Count; i++)
            {
                string path = checkPaths[i];
                string[] files = Directory.GetFiles(path, "*.anim",SearchOption.AllDirectories);
                for (int j = 0; j < files.Length; j++)
                {
                    string assetPath = files[j];

                    AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(assetPath);
                    animationClips.Add(clip);
                }
            }
        }

        [FoldoutGroup("OptimizeAnimationClipTool")]
        [Button("裁剪浮点数去除Scale")]
        public void Optimize()
        {
            if (animationClips.Count <= 0)
                GetAnimationClip();
            else
                Finish();
            _AnimOptList = FindAnims();
            ScanAnimationClip();
        }

        private void ScanAnimationClip()
        {
            for (int i = 0; i < _AnimOptList.Count; i++)
            {
                AnimationOpt _AnimOpt = _AnimOptList[i];
                _AnimOpt.Optimize_Scale_Float3(scaleOptCurve, uint.Parse(filterExtensionStr));
            }
            Debug.Log(string.Format("--优化完成--    错误数量: {0}    总数量: {1}/{2}    错误信息↓:\n{3}\n----------输出完毕----------", _Errors.Count, _AnimOptList.Count, _AnimOptList.Count, string.Join(string.Empty, _Errors.ToArray())));
            Finish();
        }

        private void Finish()
        {
            EditorUtility.ClearProgressBar();
            Resources.UnloadUnusedAssets();
            GC.Collect();
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            EditorApplication.update = null;
            _AnimOptList.Clear();
            _cachedOpts.Clear();
            filterFolders.Clear();
        }


        AnimationOpt _GetNewAOpt(string path)
        {
            AnimationOpt opt = null;
            if (!_cachedOpts.ContainsKey(path))
            {
                AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(path);
                if (clip != null && animationClips.Contains(clip))
                {
                    opt = new AnimationOpt(path, clip);
                    _cachedOpts[path] = opt;
                }
            }
            return opt;
        }

        List<AnimationOpt> FindAnims()
        {
            List<AnimationOpt> assets = new List<AnimationOpt>();
            for (int i = 0; i < checkPaths.Count; i++)
            {
                string path = checkPaths[i];
                string[] files = Directory.GetFiles(path, "*.anim", SearchOption.AllDirectories);
                for (int j = 0; j < files.Length; j++)
                {
                    string assetPath = files[j];
                    AnimationOpt animopt = _GetNewAOpt(assetPath);
                    if (animopt != null)
                    {
                        assets.Add(animopt);
                    }
                }
            }
            return assets;
        }
        public void AutoOptimizeAnimation(string path)
        {
            checkPaths.Clear();
            ClearAnimationClip();
            checkPaths.Add(path);
            GetAnimationClip();
            Optimize();
        }

        #endregion
        #region ArtFBX2Anim
        [FoldoutGroup("ArtFBX2Anim")]
        [FolderPath]
        public List<string> fbx2AnimationPaths = new List<string>();
        [FoldoutGroup("ArtFBX2Anim")]
        [FolderPath]
        public List<string> commonNoCheckPaths = new List<string>();
        [FoldoutGroup("ArtFBX2Anim")]
        public bool  isDeleteOrginalAnimation = true;
        [FoldoutGroup("ArtFBX2Anim")]
        public AnimatorController characters_baseAC;
        [FoldoutGroup("ArtFBX2Anim")]
        public bool isCreateNewAc = true;
        [FoldoutGroup("ArtFBX2Anim")]
        [Button("修改Animation引用")]

        public void ModifyAnimation()
        {
            try
            {
                for (int i = 0; i < fbx2AnimationPaths.Count; i++)
                {
                    Seperate(fbx2AnimationPaths[i], characters_baseAC);
                }
            }
            catch (Exception)
            {
                EditorUtility.ClearProgressBar();
                throw;
            }
        }

        private void Seperate(string path, AnimatorController baseAC)
        {
            path = PathUtil.GetAbsolutePath(path);
            //string[] files = Directory.GetFiles(path, "*.fbx", SearchOption.AllDirectories);
            Dictionary<UnityEngine.Object, UnityEngine.Object> newAssets = new Dictionary<UnityEngine.Object, UnityEngine.Object>();
            EditorUtility.DisplayProgressBar("动画优化", "获取引用文件中..", 0.2f);

            AnimationClip skill01ShowClip = new AnimationClip(); //储存生成出来的clip，用于后续替换skill01show状态机
            bool needSwapClip = false;
            
            string[] files = Directory.GetFiles(path, "*.overrideController", SearchOption.AllDirectories);
            List<string> filterFiles = new List<string>();
            foreach (string file in files)
            {
                bool isCommon = false;
                for (int i = 0; i < commonNoCheckPaths.Count; i++)
                {
                    if (file.Contains(commonNoCheckPaths[i]))
                    {
                        isCommon = true;
                        break;
                    }
                }
                if (isCommon || file.Contains("_ui"))
                    continue;
                string srcPath = PathUtil.GetRelativePath(file);
                AnimatorOverrideController aoc = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(srcPath);
                AnimatorController tempAC = ReplaceSimpleAOC(file, baseAC);
                if (tempAC == aoc.runtimeAnimatorController)
                {
                    filterFiles.Add(file);
                }
            }

            files = filterFiles.ToArray();

            foreach (string file in files)
            {
                bool isCommon = false;
                for (int i = 0; i < commonNoCheckPaths.Count; i++)
                {
                    if (file.Contains(commonNoCheckPaths[i]))
                    {
                        isCommon = true;
                        break;
                    }
                }
                if (isCommon || file.Contains("_ui"))
                    continue;
                string srcPath = PathUtil.GetRelativePath(file);
                
                AnimatorOverrideController aoc = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(srcPath);
                List<KeyValuePair<AnimationClip, AnimationClip>> n = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                aoc.GetOverrides(n);
                for (int i = 0; i < n.Count; i++)
                {
                    if (n[i].Value != null)
                    {
                        AnimationClip srcclip = n[i].Value;//AssetDatabase.LoadAssetAtPath(srcPath, typeof(AnimationClip)) as AnimationClip;

                        if (srcclip == null)
                            continue;

                        string[] str = srcPath.Split('/');
                        string dstPath = "";
                        for (int j = 0; j < str.Length - 1; j++)
                        {
                            dstPath += str[j] + "/";
                        }
                        char c = srcclip.name[0];
                        if (!(c >= 'A' && c <= 'Z'))
                        {
                            EditorUtility.DisplayDialog("错误警告！", "导出动画文件非大写开头，有异常，请检查异常情况，找大佬蔡解决", "确定");
                        } 
                        dstPath = dstPath + srcclip.name + ".anim";
                        AnimationClip dstclip = AssetDatabase.LoadAssetAtPath(dstPath, typeof(AnimationClip)) as AnimationClip;
                        AnimationClip tempclip;
                        if (isDeleteOrginalAnimation || dstclip == null)
                        {
                            tempclip = new AnimationClip();
                            bool isCreate = true;
                            if (dstclip != null)
                            {
                                tempclip = AssetDatabase.LoadAssetAtPath<AnimationClip>(dstPath);
                                isCreate = false; 
                                tempclip.ClearCurves();
                            }
                            EditorUtility.CopySerialized(srcclip, tempclip);
                            if (isCreate)
                            {
                                AssetDatabase.CreateAsset(tempclip, dstPath);
                            }
                            else
                            {
                                EditorUtility.SetDirty(tempclip);
                                AssetDatabase.SaveAssets();
                            }
                           
                            if(tempclip.name == "Skill01Show")
                            {
                                var assetImporter = AssetImporter.GetAtPath(dstPath);
                                if (string.IsNullOrEmpty(assetImporter.assetBundleName))
                                {
                                    string assetBundleName = dstPath.Substring(dstPath.IndexOf("/") + 1);
                                    assetImporter.assetBundleName = assetBundleName;
                                    LogHelp.clipboard = assetBundleName.ToLower();
                                }

                                needSwapClip = true;
                                skill01ShowClip = tempclip;
                            }
                        }
                        else
                        {
                            tempclip = dstclip;
                        }
                        if (!newAssets.ContainsKey(srcclip))
                        {
                            newAssets.Add(srcclip, tempclip);
                        }
                        else
                        {
                            newAssets[srcclip] = tempclip;
                        }
                    }
                }
            }
        
            string[] oCFiles = Directory.GetFiles(path, "*.overrideController", SearchOption.AllDirectories);

           filterFiles = new List<string>();
            foreach (string file in oCFiles)
            {
                if (file.Contains("_new") || file.Contains("_ui"))
                {
                    continue;
                }

                string srcPath = PathUtil.GetRelativePath(file);

                AnimatorOverrideController aoc = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(srcPath);
                AnimatorController tempAC = ReplaceSimpleAOC(file, baseAC);
                if (tempAC == aoc.runtimeAnimatorController)
                {
                    filterFiles.Add(file);
                }
            }

            oCFiles = filterFiles.ToArray();

            int index = 0;
            foreach (string file in oCFiles)
            {
                if (file.Contains("_new") || file.Contains("_ui"))
                {
                    continue;
                }
                index++;
                string p = PathUtil.GetRelativePath(file);
                string[] str = p.Split('/');
                string dstPath = "";
                for (int i = 0; i < str.Length - 1; i++)
                {
                    dstPath += str[i] + "/";
                }
                AnimatorOverrideController aoc = AssetDatabase.LoadAssetAtPath<AnimatorOverrideController>(p);

                string newAcPath = dstPath + Path.GetFileName(p).Split('.')[0] + "_new.controller";
                AnimatorController newAc = AssetDatabase.LoadAssetAtPath<AnimatorController>(newAcPath);

                //if (newAc != null)  普通美术资源，不自动替换ac,只有在用生成use的时候才会替换处理
                //{
                //    ReplaceAC2Aoc(dstPath, newAc, aoc);
                //}
                EditorUtility.DisplayProgressBar("动画优化", "修改controller引用中....." + index + "/" + oCFiles.Length, 0.2f + (float)index / (float)oCFiles.Length * 0.6f);

                Dictionary<AnimationClip, AnimationClip> animDict = new Dictionary<AnimationClip, AnimationClip>();
                List<KeyValuePair<AnimationClip, AnimationClip>> n = new List<KeyValuePair<AnimationClip, AnimationClip>>();
                aoc.GetOverrides(n);
                for (int i = 0; i < n.Count; i++)
                {
                    if (n[i].Value != null && newAssets.ContainsKey(n[i].Value))
                    {
                        animDict.Add(n[i].Key, GetNewAssetBy<AnimationClip>(newAssets, n[i].Value));
                    }
                    else
                    {
                        animDict.Add(n[i].Key, n[i].Value);
                    }
                }
                 
                if (newAc != null)
                { 
                    AssetDatabase.DeleteAsset(newAcPath);
                }
                newAcPath = newAcPath.Replace("_Override", "");
                AnimatorController curNewAc= AssetDatabase.LoadAssetAtPath<AnimatorController>(newAcPath);
                AnimatorController tempAC = ReplaceSimpleAOC(file, baseAC);
                if (isCreateNewAc)
                {
                    File.Copy(AssetDatabase.GetAssetPath(tempAC), newAcPath, true);
                    UnityEditor.AssetDatabase.Refresh();
                    curNewAc = AssetDatabase.LoadAssetAtPath<AnimatorController>(newAcPath);
                    ReplaceStateMotion(curNewAc, animDict);
                }
                //if (tempclip != null)
                //{
                //    ReplaceAoc2Ac(dstPath, aoc, tempclip);
                //}

                string[] prefabFiles = Directory.GetFiles(path, "*.prefab", SearchOption.AllDirectories);
                string[] splitSimple = tempAC.name.Split('_');
                string appendStr = "";
                if (splitSimple != null && splitSimple.Length > 2)
                {
                    appendStr = "_" + splitSimple[splitSimple.Length - 1];
                }
                string[] newAcPathSplitStrs = newAcPath.Split('/');
                string oriPrefabName = "";
                if(newAcPathSplitStrs!=null&& newAcPathSplitStrs.Length>0)
                {
                    string lastSpltStr = newAcPathSplitStrs[newAcPathSplitStrs.Length - 1];
                    int length = 0; 
                    if(appendStr=="")
                    {
                        length = lastSpltStr.IndexOf("_new");
                    }
                    else
                    {
                        length = lastSpltStr.IndexOf(appendStr);
                    }
                    oriPrefabName = lastSpltStr.Substring(0, length);
                }
                foreach (var prefabFile in prefabFiles)
                {
                    string relativePrefabFile = PathUtil.GetRelativePath(prefabFile);
                    var go =   AssetDatabase.LoadAssetAtPath<GameObject>(relativePrefabFile);
                    if(go!=null)
                    {
                        if (string.IsNullOrEmpty(oriPrefabName) || go.name != oriPrefabName)
                        {
                            if (relativePrefabFile.Contains("_") && appendStr == "")
                                continue;

                            if (relativePrefabFile.Contains("_") && !relativePrefabFile.Contains(appendStr))
                                continue;
                        }            

                        if(relativePrefabFile.Contains(appendStr))
                        {
                            var newGo = AssetDatabase.LoadAssetAtPath<GameObject>(relativePrefabFile);
                            var anim = newGo.GetComponent<Animator>();
                            if (anim != null)
                            {
                                anim.runtimeAnimatorController = curNewAc;
                            }

                            var assetImporter = AssetImporter.GetAtPath(relativePrefabFile);
                            string assetBundleName = relativePrefabFile.Substring(relativePrefabFile.IndexOf("/") + 1);
                            assetImporter.assetBundleName = assetBundleName;
                            LogHelp.clipboard = assetBundleName.ToLower();
                        }
                        else
                        {
                            //if (((go.layer | 0) == 0))
                            //{
                                string newPrefabFile = relativePrefabFile.Replace(".prefab", appendStr + ".prefab");
                                AssetDatabase.CopyAsset(relativePrefabFile, newPrefabFile);
                                var newGo = AssetDatabase.LoadAssetAtPath<GameObject>(newPrefabFile);
                                var anim = newGo.GetComponent<Animator>();
                                if (anim != null)
                                {
                                    anim.runtimeAnimatorController = curNewAc;
                                }

                                var assetImporter = AssetImporter.GetAtPath(newPrefabFile);
                                string assetBundleName = newPrefabFile.Substring(newPrefabFile.IndexOf("/") + 1);
                                assetImporter.assetBundleName = assetBundleName;
                                LogHelp.clipboard = assetBundleName.ToLower();
                            //}
                        }                    
                    }
                }
            }

            try
            {
                if (skill01ShowClip != null && needSwapClip)
                {
                    string[] aCFiles = Directory.GetFiles(path, "*.controller", SearchOption.AllDirectories);
                    foreach (string file in aCFiles)
                    {
                        string p = PathUtil.GetRelativePath(file);
                        if (p.Contains("_Skill01Show"))
                        {
                            AnimatorController animatorController =
                                AssetDatabase.LoadAssetAtPath<AnimatorController>(p);
                            if (animatorController == null)
                            {
                                continue;
                            }

                            int length = animatorController.animationClips.Length;
                            if (length > 0)
                            {
                                for (int i = 0; i < length; i++)
                                {
                                    if (animatorController.animationClips[i].name == skill01ShowClip.name)
                                    {
                                        for (int j = 0; j < animatorController.layers.Length; j++)
                                        {
                                            for (int k = 0;
                                                 k < animatorController.layers[j].stateMachine.states.Length;
                                                 k++)
                                            {
                                                animatorController.SetStateEffectiveMotion(
                                                    animatorController.layers[j].stateMachine.states[k].state,
                                                    skill01ShowClip);
                                            }
                                        }
                                    }
                                }
                            }

                            UnityEditor.EditorUtility.SetDirty(animatorController);
                            UnityEditor.AssetDatabase.SaveAssets();
                            UnityEditor.AssetDatabase.Refresh();
                        }

                    }

                }
            }
            catch (Exception e)
            {
                Debug.LogError("导出动作失败，可能是没有animation  " + e);
                throw;
            }

            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
            newAssets.Clear();
            EditorUtility.ClearProgressBar();
        }

        public static void ReplaceAC2Aoc(string dstPath, AnimatorController aoc, AnimatorOverrideController tempclip)
        {
            string[] prefabPaths = Directory.GetFiles(dstPath.Replace("Animations/", "Prefabs"), "*.prefab", SearchOption.AllDirectories);
            for (int i = 0; i < prefabPaths.Length; i++)
            {
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPaths[i]); //制定处理对应的预制体的animator
                if (prefab != null && prefab.GetComponentInChildren<Animator>() && prefab.GetComponentInChildren<Animator>().runtimeAnimatorController == aoc)
                {
                    prefab.GetComponentInChildren<Animator>().runtimeAnimatorController = tempclip;
                    UnityEditor.EditorUtility.SetDirty(prefab);
                    UnityEditor.AssetDatabase.SaveAssets();
                    UnityEditor.AssetDatabase.Refresh();
                }
            }
        }

        public static void ReplaceAoc2Ac(string dstPath, AnimatorOverrideController aoc, AnimatorController tempclip)
        {
            string[] prefabPaths = Directory.GetFiles(dstPath.Replace("Animations/", "Prefabs"), "*.prefab", SearchOption.AllDirectories);
            for (int i = 0; i < prefabPaths.Length; i++)
            {
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPaths[i]);
                if (prefab != null && prefab.GetComponentInChildren<Animator>() && prefab.GetComponentInChildren<Animator>().runtimeAnimatorController == aoc)
                {
                    prefab.GetComponentInChildren<Animator>().runtimeAnimatorController = tempclip;
                    UnityEditor.EditorUtility.SetDirty(prefab);
                    UnityEditor.AssetDatabase.SaveAssets();
                    UnityEditor.AssetDatabase.Refresh();
                }
            }
        }


        /// <summary>
        /// 替换动画控制器中使用的Clip
        /// </summary>
        public static void ReplaceStateMotion(AnimatorController ctrl, Dictionary<AnimationClip, AnimationClip>animDict)
        {
            foreach (AnimatorControllerLayer layer in ctrl.layers)
            {
                foreach (ChildAnimatorState childState in layer.stateMachine.states)
                {
                    AnimationClip alp = childState.state.motion as AnimationClip; 
                    if (animDict.ContainsKey(alp))
                    {
                        ctrl.SetStateEffectiveMotion(childState.state, animDict[alp]);
                    }
                }
                foreach (ChildAnimatorStateMachine childState in layer.stateMachine.stateMachines)
                {
                    foreach (ChildAnimatorState cs in childState.stateMachine.states)
                    {
                        AnimationClip alp = cs.state.motion as AnimationClip;
                 
                        if (animDict.ContainsKey(alp))
                        {
                            ctrl.SetStateEffectiveMotion(cs.state, animDict[alp]);
                        }
                    }
                }
            }
        }

        public T GetNewAssetBy<T>(Dictionary<UnityEngine.Object, UnityEngine.Object> newAssets, UnityEngine.Object component) where T : UnityEngine.Object
        {
            // Dictionary<int, string> paths = newAssets[classType];
            // var sourceAssetPath = AssetDatabase.GetAssetPath(component);
            if (!newAssets.ContainsKey(component))
            {
                return null;
            }
             var go = newAssets[component] as T;
            //var go = AssetDatabase.LoadAssetAtPath<T>(newAssets[component]);
            return go;
        }


        public static void AutoModifyAnimation(string path)
        {
            Instance.fbx2AnimationPaths.Clear();
            Instance.fbx2AnimationPaths.Add(path);
            Instance.commonNoCheckPaths.Clear();
            Instance.commonNoCheckPaths.Add("Assets/Art/Characters/common");
            Instance.isCreateNewAc = true;
            string[] files = Directory.GetFiles(path, "*.controller", SearchOption.AllDirectories);
            if (files.Length > 1)
            {
                Instance.isCreateNewAc = false;
            }
            Instance.isDeleteOrginalAnimation = true;
            Instance.characters_baseAC = AssetDatabase.LoadAssetAtPath<AnimatorController>("Assets/Art/Characters/common/Animations/Characters_BaseAC.controller");
            Instance.ModifyAnimation();
        }

        public static AnimatorController ReplaceSimpleAOC(string path, AnimatorController ac)
        {
            string acPath = AssetDatabase.GetAssetPath(ac);
            if (path.Contains("_Simple"))
            {
                acPath = acPath.Replace("Characters_BaseAC", "Characters_BaseAC_Simple");
            }
            return AssetDatabase.LoadAssetAtPath<AnimatorController>(acPath);
        }
        #endregion

        #region fbx模型修改
        [FoldoutGroup("FBXAnimationTool")]
        [FolderPath]
        public string fbxRootPath = "Assets/Art/Characters";
        [FoldoutGroup("FBXAnimationTool")]
        public bool isCreateNewMesh = true;
        [FoldoutGroup("FBXAnimationTool")]
        public bool isOnlyCreateNewMesh = true;
        [FoldoutGroup("FBXAnimationTool")]
        [Button]
        public void SetFbxAnimation()
        {
            string[] files = Directory.GetFiles(fbxRootPath, "*.FBX", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                string p = files[i].Replace("\\", "/");
                ModelImporter model = ModelImporter.GetAtPath(p) as ModelImporter;
                model.animationCompression = ModelImporterAnimationCompression.Optimal;
                //美术要求插屏动画保持高精度，其他战斗中的动画精度要求可以降低
                bool isShowAnim = p.Contains("Show") || p.Contains("Stand");
                model.animationRotationError = isShowAnim ? 0.1f : 2f;
                model.animationPositionError = isShowAnim ? 0.1f : 2f;
                model.animationScaleError = isShowAnim ? 0.1f : 2f;
                EditorUtility.SetDirty(model);
                AssetDatabase.ImportAsset(p);
              
                if (isCreateNewMesh && p.Contains("/Models"))
                {
                    string p1 = p.Replace("/Models", "/Prefabs");
                    p1 = p1.Replace(".FBX", ".prefab");
                    GameObject fbxMesh = AssetDatabase.LoadAssetAtPath(p, typeof(GameObject)) as GameObject;//fbx
                    #region mesh
                    SkinnedMeshRenderer [] skinnedMeshRenderers = fbxMesh.GetComponentsInChildren<SkinnedMeshRenderer>();
                    Debug.LogError(skinnedMeshRenderers.Length);
                    for (int j = 0; j < skinnedMeshRenderers.Length; j++)
                    {
                        Mesh mesh = skinnedMeshRenderers[j].sharedMesh;
                        string objectName = mesh.name;
                        string[] paths = Directory.GetFiles(p1.Replace("/" + Path.GetFileName(p1), ""), "*.prefab", SearchOption.AllDirectories);
                        UnityEngine.Object obj1 = AssetDatabase.LoadAssetAtPath(p.Replace(Path.GetFileName(p), objectName + "_mesh.mesh"), typeof(UnityEngine.Object)) as UnityEngine.Object;//fbx
                        for (int k = 0; k < paths.Length; k++)
                        {
                            GameObject objPrefab = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(paths[k]) as GameObject;
                            Debug.LogError(paths[k]);
                            for (int kk = 0; kk < objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>().Length; kk++)
                            {
                                if (obj1 && objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>()[kk].sharedMesh == mesh)
                                {
                                    objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>()[kk].sharedMesh = mesh;
                                }
                            }
                            if (objPrefab.GetComponentInChildren<CardConfig>())
                            {
                                for (int kk = 0; kk < objPrefab.GetComponentsInChildren<CardMeshSimplify>().Length; kk++)
                                {
                                    // if (objPrefab.GetComponentsInChildren<CardMeshSimplify>()[kk].originalMesh == mesh)
                                    // {
                                    //     objPrefab.GetComponentsInChildren<CardMeshSimplify>()[kk].originalMesh = mesh;
                                    //     EditorUtility.SetDirty(objPrefab);
                                    //     AssetDatabase.SaveAssets();
                                    // }
                                }
                            }
                        }
                        AssetDatabase.DeleteAsset(p.Replace(Path.GetFileName(p), objectName + "_mesh.mesh"));
                        Mesh combinedMesh = CreateNewMeshTool.CreateSmoothedMesh(mesh, "XYZ", CreateNewMeshTool.SmoothedNormalsChannel.VertexColors, CreateNewMeshTool.SmoothedNormalsUVType.FullXYZ, false);
                        AssetDatabase.CreateAsset(combinedMesh, p.Replace(Path.GetFileName(p), objectName + "_mesh.mesh"));

                        UnityEngine.Object obj = AssetDatabase.LoadAssetAtPath(p.Replace(Path.GetFileName(p), objectName + "_mesh.mesh"), typeof(UnityEngine.Object)) as UnityEngine.Object;//fbx
                        var assetPath = AssetDatabase.GetAssetPath(obj);
                        var assetImporter = AssetImporter.GetAtPath(assetPath);

                        string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1);
                        assetImporter.assetBundleName = assetBundleName.Replace(" ","");
                        LogHelp.clipboard = assetBundleName.ToLower();
                        AssetDatabase.ImportAsset(p.Replace(Path.GetFileName(p), objectName + "_mesh.mesh"));
                        AssetDatabase.Refresh();
                        for (int k = 0; k < paths.Length; k++)
                        {
                            GameObject objPrefab = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(paths[k]) as GameObject;
                            for (int kk = 0; kk < objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>().Length; kk++)
                            {
                                if (objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>()[kk].sharedMesh == mesh)
                                {
                                    objPrefab.GetComponentsInChildren<SkinnedMeshRenderer>()[kk].sharedMesh = combinedMesh;
                                    EditorUtility.SetDirty(objPrefab);
                                    AssetDatabase.SaveAssets();
                                }
                            }

                            if (objPrefab.GetComponentInChildren<CardConfig>())
                            {
                                for (int kk = 0; kk < objPrefab.GetComponentsInChildren<CardMeshSimplify>().Length; kk++)
                                {
                                    // if (objPrefab.GetComponentsInChildren<CardMeshSimplify>()[kk].originalMesh == mesh)
                                    // {
                                    //     objPrefab.GetComponentsInChildren<CardMeshSimplify>()[kk].simplifyMesh = combinedMesh;
                                    //     EditorUtility.SetDirty(objPrefab);
                                    //     AssetDatabase.SaveAssets();
                                    // }
                                }
                            }
                            //Avatar newAvatar = AssetDatabase.LoadAssetAtPath(avatarPath.Replace(Path.GetFileName(avatarPath).Split('.')[1], "asset"), typeof(Avatar)) as Avatar;
                            //objPrefab.GetComponentInChildren<Animator>().avatar = newAvatar;
                        }
                    }
                    #endregion
                }
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            if (isOnlyCreateNewMesh)
            {
                return;
            }
            fbx2AnimationPaths.Clear();
            fbx2AnimationPaths.Add(fbxRootPath);
            isDeleteOrginalAnimation = true;
            ModifyAnimation();
            AssetDatabase.Refresh();
            checkPaths.Clear();
            checkPaths.Add(fbxRootPath);
            ClearAnimationClip();
            GetAnimationClip();
            Optimize();
        }

        public static void AutoSetFbxAnimation(string path)
        {
            Instance.fbxRootPath = path;
            Instance.isCreateNewMesh = true;
            string[] files = Directory.GetFiles(path, "*.mesh", SearchOption.AllDirectories);
            for (int i = 0; i < files.Length; i++)
            {
                if (files[i].Contains("_mesh"))
                {
                    Instance.isCreateNewMesh = false;
                }
            }
            Instance.isOnlyCreateNewMesh = true;
            Instance.SetFbxAnimation();
        }
        #endregion

        public bool SaveMeshAssetsRecursive(GameObject root, GameObject gameObject, string strFile, bool bRecurseIntoChildren, bool bAssetAlreadyCreated)
        {
            MeshSimplify meshSimplify = gameObject.GetComponent<MeshSimplify>();
            UnityEditor.AssetDatabase.CreateAsset(meshSimplify.m_simplifiedMesh, strFile);
            if (UnityEditor.AssetDatabase.Contains(meshSimplify.m_simplifiedMesh) == false)
            {
                UnityEditor.AssetDatabase.AddObjectToAsset(meshSimplify.m_simplifiedMesh, strFile);
                UnityEditor.AssetDatabase.ImportAsset(UnityEditor.AssetDatabase.GetAssetPath(meshSimplify.m_simplifiedMesh));
            }
            return bAssetAlreadyCreated;
        }
        private IEnumerator ComputeMeshWithVertices(GameObject gameObject, MeshSimplify meshSimplify)
        {
            MeshFilter meshFilter = gameObject.GetComponentInChildren<MeshFilter>();
            SkinnedMeshRenderer skin = gameObject.GetComponentInChildren<SkinnedMeshRenderer>();

            if (meshSimplify && (meshFilter != null || skin != null))
            {
                Mesh newMesh = null;

                if (meshFilter != null)
                {
                    newMesh = Mesh.Instantiate(meshFilter.sharedMesh);
                }
                else if (skin != null)
                {
                    newMesh = Mesh.Instantiate(skin.sharedMesh);
                }

                if (meshSimplify.GetMeshSimplifier() != null)
                {
                    meshSimplify.GetMeshSimplifier().CoroutineEnded = false;

                    meshSimplify.GetMeshSimplifier().ComputeMeshWithVertexCount(gameObject, newMesh, Mathf.RoundToInt(meshSimplify.GetMeshSimplifier().GetOriginalMeshUniqueVertexCount()), meshSimplify.name);

                    while (meshSimplify.GetMeshSimplifier().CoroutineEnded == false)
                    {
                        yield return null;
                    }

                    if (meshFilter != null)
                    {
                        meshFilter.mesh = newMesh;
                    }
                    else if (skin != null)
                    {
                        skin.sharedMesh = newMesh;
                    }

                    meshSimplify.m_simplifiedMesh = newMesh;
                }

            }
        }
    }
}
#endif