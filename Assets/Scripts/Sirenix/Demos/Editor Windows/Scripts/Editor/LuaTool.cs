#if UNITY_EDITOR
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using War.Base;

namespace luaTool
{
    public class LuaTool:Singleton<LuaTool>
    {
        public Dictionary<string, object> toolDic = new Dictionary<string, object>();
        public object[] Exe(string goStr,string cmd, Dictionary<string, object> args)
        {
            var gs = AssetDatabase.FindAssets($"t:GameObject {goStr}");

            for (int i = 0; i < Mathf.Min(gs.Length,100); i++)
            {
                string g = gs[i];
                var go = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GUIDToAssetPath(g));
                if (go)
                {
                    var luaScr = go.GetComponent<LuaBehaviourInEditor>();
                    if (luaScr)
                    {
                        var json = "";
                        try
                        {
                            json = UIHelper.ToJson(args,0);
                        }
                        catch (System.Exception e)
                        {
                            "".PrintError(e);
                            return null ;
                        }

                        try
                        {
                            var returns = luaScr.ExeScript($"return {cmd}(\'{json}\')");
                            Debug.Log(returns);
                        }
                        catch (System.Exception e)
                        {
                            "".PrintError(e);
                            return null;
                        }

                    }

                }
                else
                {
                    "".PrintError($"!!LuaTool cant find {goStr} with cmd {cmd}");
                }
            }
            return null;
        }
    }
}
#endif
