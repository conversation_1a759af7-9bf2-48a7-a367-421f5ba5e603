#if UNITY_EDITOR
using Sirenix.OdinInspector;
using SuperTools.Editor;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{ 

    [System.Serializable]
    public class CheckMissComponentTC
    {
        [HideInInspector]
        public const string TIPS = "检查脚本丢失";
        public bool within_ab = false;
        [FolderPath]
        public string folder = "";
        public List<GoTObj> fix_list = new List<GoTObj>();

        [Button]
        void Check()
        {
            if(string.IsNullOrEmpty(folder))
            {
                folder = "Assets";
            }
            fix_list.Clear();
            var guids = UnityEditor.AssetDatabase.FindAssets("t:GameObject", new string[] { folder });
            ToolUti.Iter(guids, (g) =>
            {
                var asset_path = AssetDatabase.GUIDToAssetPath(g);

                if(within_ab)
                {
                    var aip = AssetImporter.GetAtPath(asset_path);
                    var abname = AssetDatabase.GetImplicitAssetBundleName(asset_path);
                    if (string.IsNullOrEmpty(abname))
                    {
                        return asset_path;
                    }
                }

                var ai = AssetDatabase.LoadMainAssetAtPath(asset_path) as GameObject;
                if (
                    ai
                    )
                {
                    var tfs = ai.GetComponentsInChildren<Transform>();

                    foreach (var tf in tfs)
                    {
                        if (tf)
                        {
                            var coms = tf.GetComponents<Component>();
                            foreach (var c in coms)
                            {
                                if (c == null)
                                {
                                    var tobj = new GoTObj();
                                    tobj.da = tf.gameObject;
                                    fix_list.Add(tobj);
                                    break;
                                }
                            }
                        }
                    }
                }
                return asset_path;
            }, () =>
            {
                "".Print("finish");

            });

        }
    }
}

#endif