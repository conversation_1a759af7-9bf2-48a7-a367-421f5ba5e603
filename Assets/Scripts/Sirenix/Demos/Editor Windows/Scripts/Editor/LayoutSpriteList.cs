#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class LayoutSpriteList : GlobalConfig<LayoutSpriteList>
    {

        [Serializable]
        public class LayoutProto
        {
            //[HorizontalGroup]
            [PropertyOrder(0)]
            public string layout;
            //[HorizontalGroup]
            [PropertyOrder(1)]
            [Button]
            void MoveToScene()
            {
                GameObject glayout = GameObject.Find(Path.GetFileNameWithoutExtension(layout));
                if (glayout) {
                    Selection.activeGameObject = glayout;
                    return;
                }
                var asset = AssetDatabase.LoadMainAssetAtPath(layout) as GameObject;
                if(asset)
                {
                    var canvas = GameObject.FindObjectOfType<Canvas>();
                    
                    var go = PrefabUtility.InstantiatePrefab(asset) as GameObject;
                    go.transform.SetParent(canvas ? canvas.transform : null, false);

                    go.name = go.name.Replace("(Clone)","");
                    Selection.activeGameObject = go;
                }
            }
            [PropertyOrder(2)]
            public List<AtlasProto> list;
        }
        [Serializable]
        public class AtlasProto
        {
            public string atlas;
            //[HideIf("ifTexEmpty")]
            //public string tex;
            //bool ifTexEmpty()
            //{
            //    return string.IsNullOrEmpty(tex) || atlas == tex;
            //}
            public List<SpriteProto> list;
        }
        [Serializable]
        public class SpriteProto
        {
            public string tex;

            public List<StrProto> list;
        }

        [Serializable]
        public class StrProto
        {

            [HideLabel]
            [HorizontalGroup]
            public string name;  

            [HorizontalGroup]
            [Button]
            void Locate()
            { 
                    Selection.activeObject = GameObject.Find(name);
            }
            public static StrProto Parse(string path)
            {
                var sp = new StrProto();
                sp.name = path;
                
                return sp;
            }
        }
        [Sirenix.OdinInspector.FilePath] 
        public List<string> assetPaths = new List<string>{
            "Assets/UI",
            "Assets/Art/Maps"
        };

        public List<LayoutProto> SpriteList;

        //[OnValueChanged("OnFilter")]
        [HorizontalGroup("3")]
        public string filter;
        [HorizontalGroup("3")]
        [Button()]
        void OnFilter()
        {
            if (string.IsNullOrEmpty(filter)) return;
            var fs = filter.Split(' ', ',', '|');

            var setDic = new Dictionary<int, LayoutProto>();
            var fl = new List<LayoutProto>(SpriteList);
            foreach (var f in fs)
            {
                var _filter = f.ToLower();
                fl = fl.FindAll(
                    (s) => s.layout.ToLower().Contains(_filter)
                    || s.list.Find((ap) => ap.atlas.ToLower().Contains(_filter)) != null
                    ); 
            }
            FilterSpriteList = fl;
        }
        public List<LayoutProto> FilterSpriteList;
        [Button()]
        public void FindUsed()
        {
            SpriteList.Clear();
            var findassets = AssetDatabase.FindAssets("t:GameObject", assetPaths.ToArray());

#if UNITY_2018_1_OR_NEWER
            Sprite empty = Sprite.Create(null,Rect.zero,Vector2.zero);

#else
            Sprite empty = new Sprite();
#endif

            //empty.name = "[empty]";
            ToolUti.Iter(findassets, (gid) =>
            {
                var path = AssetDatabase.GUIDToAssetPath(gid);
                var go = AssetDatabase.LoadMainAssetAtPath(path) as GameObject;
                if (go)
                {
                    var lp = new LayoutProto()
                    {
                        layout = path,
                    };
                    var dic = new Dictionary<string, List<StrProto>>();

                    var lImgs = go.GetComponentsInChildren(typeof(UnityEngine.UI.Image), true);
                    foreach (var img in lImgs)
                    {

                        var spri = (img as UnityEngine.UI.Image).sprite;
                        var spr = "empty";
                        if (spri == null)
                        {
                            spri = empty;
                        }
                        else
                        {
                            spr = AssetDatabase.GetAssetPath(spri);
                        }

                        var sp = StrProto.Parse(ToolUti.CopyPathTo(img.transform, img.transform.root));

                        List<StrProto> lsp = null;
                        dic.TryGetValue(spr, out lsp);
                        if (lsp == null)
                        {
                            dic[spr] = new List<StrProto>();
                            lsp = dic[spr];
                        }
                        lsp.Add(sp);
                    } 

                    var lout = new LayoutProto()
                    {
                        layout = path,
                        list = new List<AtlasProto>(),
                    };
                    SpriteList.Add(lout);
                    var dicAtlas = new Dictionary<string, AtlasProto>();

                    foreach (var item in dic.Keys)
                    {

                        var ap = item;
                        TextureImporter ai = null;
                        var atlas = item;
                        if (item != "empty" && string.IsNullOrEmpty(ap) == false)
                        {
                            ai = AssetImporter.GetAtPath(ap) as TextureImporter;
                            if(ai)
                            {
                                atlas = ai.textureType == TextureImporterType.Sprite ? ai.spritePackingTag : item;
                            }
                        }

                        AtlasProto item1 = null;
                        if(dicAtlas.ContainsKey(atlas))
                        {
                            item1 = dicAtlas[atlas];
                        }
                        else
                        {
                            item1 = new AtlasProto()
                            {
                                //tex = item,
                                atlas = atlas,
                                list = new List<SpriteProto>(),
                            };
                            dicAtlas[atlas] = item1;
                            lout.list.Add(item1);
                        }
                        item1.list.Add(new SpriteProto() { 
                            tex = item,
                            list = dic[item],
                        });

                    }
                }
                return path;
            }, () => {
                SpriteList.Sort((l1, l2) => l2.list.Count - l1.list.Count);
            });

        }

        [Button]
        void SplitWindow()
        {
            SingleWindow.Create(this);
        }
    }
}
#endif
