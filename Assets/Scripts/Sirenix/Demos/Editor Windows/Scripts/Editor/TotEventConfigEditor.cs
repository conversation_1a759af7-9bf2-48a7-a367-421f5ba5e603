#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using UnityEditor;
    using UnityEngine;

    [GlobalConfig("Art/Maps/ToT")]
    public class TotEventConfigEditor : GlobalConfig<TotEventConfigEditor>
    {
        [LabelText("还原有风险劝君需谨慎!!")]
        [ReadOnly]
        public string Warning;
        [LabelText("要导入的PeakOfTimeRefresh.csv路径")]
        [Sirenix.OdinInspector.FilePath] 
        public string configPath;

        [LabelText("地图ID")]
        public List<int> mapIDs;

        [Button("开始还原地图数据")]
        public void Recover()
        {
            var go = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Art/Maps/ToT/tot_tool/tot_tool.prefab");
            var luaScr = go.GetComponent<LuaBehaviourInEditor>();
            var list = new List<string>();

            var command = new Dictionary<string, object>();
            command["cmd"] = "recover_data";
            command["param"] = mapIDs;
            command["refresh_path"] = configPath;

            var returns = luaScr.ExeScript(string.Format("return onevent(\'{0}\')", UIHelper.ToJson(command, 0)));
            Debug.Log(returns);
            //var returnstr = returns[0].ToString();
            //Debug.Log(returnstr);
        }
    }

}
#endif
