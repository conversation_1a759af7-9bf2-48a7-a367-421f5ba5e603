#if true || UNITY_EDITOR

using System;
using System.Collections;
using Sirenix.OdinInspector;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Policy;
using System.Text;
using CLeopardZip;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEditor.Timeline.Actions;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Serialization;
using XLua;
using Debug = UnityEngine.Debug;
using Sirenix.Utilities;

using System.Threading;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class CatchLog
    {
        // 解析csv 获取下载链接，下载里面的log.zip 
        [HideInInspector] public const string TIPS = "CatchLog ";


        [Serializable]
        public class BugFeedback
        {
            public List<string> logTimes;
            public List<string> UrlPaths;
        }

        // [Title("操作说明")] 
        // [ReadOnly]
        // [HideLabel]
        // [MultiLineProperty(4)]
        // [SerializeField]
        // private string manual = "1.配置从平台导出的 Bug反馈 CSV 文件路径\n" +
        //                         "2.配置从下载的Zip文件存放目录\n" +
        //                         "3.点击Download Log按钮下载Zip\n"+
        //                         "4.勾选Is Unzip 会在下载zip后在当前目录解压缩所有的Zip文件";


        [Title("2.点击Download Log按钮 下载Zip & 解压缩zip & 生成报告")]
        [Title("1.从平台导出的一份 Bug反馈.CSV      (不要用WPS打开当前csv,文件会被占用导致无法读取！！！)")]
        [Title("操作说明↑")]
        [Title("是否解压缩所有下载的zip")]
        [ToggleLeft]
        public bool isUnzip = true;

        [Title("根据解压缩的文件在控制台显示报告Log")] [EnableIf("isUnzip")] [ToggleLeft]
        public bool isCheckFeedbackTC = true;

        [InlineButton("SelectCSVFilePath", "Select .CSV")] [LabelText("需要解析的Csv文件路径")]
        public string csvFilePath;

        [InlineButton("SelectSaveUnzipDirectory", "Select Directory")] [LabelText("下载和解压缩文件保存的根路径")]
        public string saveFilesDirectory;


        private void SelectCSVFilePath()
        {
            var path = EditorUtility.OpenFilePanel("选择CSV读取文件", csvFilePath, "csv");
            if (string.IsNullOrEmpty(path) == false)
                csvFilePath = path;
        }

        private void SelectSaveUnzipDirectory()
        {
            var path = EditorUtility.OpenFolderPanel("选择下载日志存放目录", saveFilesDirectory, string.Empty);
            if (string.IsNullOrEmpty(path) == false)
                saveFilesDirectory = path;
        }


        private void ToMainThread(EditorApplication.CallbackFunction action)
        {
            EditorApplication.delayCall += action;
        }


        [Button("DownloadLog")]
        private void DownloadLog()
        {
            if (File.Exists(csvFilePath) == false)
            {
                SelectCSVFilePath();
                return;
            }

            if (Directory.Exists(saveFilesDirectory) == false)
            {
                SelectSaveUnzipDirectory();
                return;
            }

                var tPath = $"{saveFilesDirectory}/{Path.GetFileName(csvFilePath)}";
            // 检测csv占用
            try
            {
                File.Copy(csvFilePath, tPath, true);

                var tempAllLines = File.ReadAllLines(tPath); 
                //var tempAllLines = ReadAllLine(csvFilePath);  
            }
            catch (Exception e)
            {
                EditorUtility.DisplayDialog("Tips", $"Read file error,Please check file!!  {csvFilePath}", "ok");
                Debug.LogError($"Read file error,Please check file!!  {csvFilePath} {e}");
            }

            BugFeedback bugFeedback = CreateBugFeedback(tPath);
            CancellationTokenSource tk = null;
            dataPath = Application.dataPath;
            try
            {
                var urls = bugFeedback.UrlPaths;
                int length = urls.Count;
                // 下载全部zip
                string time = DateTime.Now.ToString("yyyy_MM_dd__hh_mm_ss");
                var savePath2 = Path.Combine(saveFilesDirectory, "CatchLog" + time);
                if (Directory.Exists(savePath2) == false)
                    Directory.CreateDirectory(savePath2);



                Byte[] bytes = new byte[1024];
                List<string> zipPathList =  new List<string>();
                var zipPathList2 = Queue.Synchronized(new Queue());


                //for (int i = 0; i < length; i++)
                //{
                //    string logPath = urls[i];

                //    string fileName = Path.GetFileName(logPath);
                //    string zipPath = Path.Combine(savePath2, fileName);
                //    float progress = (float) i / length;

                //    if (EditorUtility.DisplayCancelableProgressBar("Catch Log",
                //            $"{i}:{length} Download file：" + fileName, progress))
                //        break;

                //    HttpDownload(logPath, zipPath, bytes);

                //    zipPathList.Add(zipPath);
                //}

                LogHelp.Instance.Log("download log");
                bool pause = true;
                int finishCount = length;
                tk = War.Base.ThreadScheduler.Instance.Schedule(
                    urls, (logPath2) =>
                    {
                        string logPath = (string)logPath2;
                        string fileName = Path.GetFileName(logPath);
                        string zipPath = Path.Combine(savePath2, fileName);
                        Byte[] bytes = new byte[1024];

                        LogHelp.Instance.Log("download log "+ finishCount  + logPath);

                        HttpDownload(logPath, zipPath, bytes);

                        zipPathList2.Enqueue(zipPath);
                        Interlocked.Decrement(ref finishCount);


                    }, () => {
                        LogHelp.Instance.Log("unzip log");
                        pause = false;

                        ToMainThread(() =>
                        {
                            EditorUtility.ClearProgressBar();
                            Debug.Log($"Download {length} zip end.");
                            zipPathList.AddRange(zipPathList2.Convert((o) => (string)o));
                            if (isUnzip)
                            {
                                var lst = new List<object>();
                                for (int i = 0; i < zipPathList.Count; i++)
                                {
                                    string zippath = zipPathList[i];
                                    lst.Add(new List<object>() { zippath, bugFeedback.logTimes[i] });
                                }
                                War.Base.ThreadScheduler.Instance.Schedule((IEnumerable<object>)lst,
                                    (zp) =>
                                    {
                                        var kp = (List<object>) zp;
                                        UnzipOne((string)kp[0], (string)kp[1]);
                                    }, () =>
                                    {
                                        ToMainThread(() =>
                                        {
                                            LogHelp.Instance.Log("isCheckFeedbackTC log");

                                            if (isCheckFeedbackTC)
                                            {
                                                EditorUtility.DisplayCancelableProgressBar("Call CheckFeedbackTC", "Please Wait.", 0);
                                                CheckFeedbackTC checkFeedbackTC = new CheckFeedbackTC();
                                                checkFeedbackTC.path = savePath2;
                                                var result = checkFeedbackTC.Check();
                                                if (!string.IsNullOrEmpty(result))
                                                {
                                                    File.WriteAllText(savePath2 + "/result.json", result);
                                                }
                                            }
                                            LogHelp.Instance.Log("OpenURL log");

                                            Application.OpenURL("file://" + savePath2);
                                            EditorUtility.ClearProgressBar();

                                        });
                                    }
                                    );
                            }
                        });
                    }
                    );

                while (pause && !EditorUtility.DisplayCancelableProgressBar("download log", "Please Wait." + finishCount, 0)) ;
                EditorUtility.ClearProgressBar();

                //EditorUtility.ClearProgressBar();
                //Debug.Log($"Download {length} zip end.");

                //if (isUnzip)
                //{
                //    Unzip(zipPathList, bugFeedback.logTimes);
                //}

                //if (isCheckFeedbackTC)
                //{
                //    EditorUtility.DisplayCancelableProgressBar("Call CheckFeedbackTC", "Please Wait.", 0);
                //    CheckFeedbackTC checkFeedbackTC = new CheckFeedbackTC();
                //    checkFeedbackTC.path = savePath2;
                //    var result = checkFeedbackTC.Check();
                //    if(!string.IsNullOrEmpty(result))
                //    {
                //        File.WriteAllText(savePath2 + "/result.json", result);
                //    }
                //}

                //Application.OpenURL("file://" + savePath2);
            }
            catch (Exception e)
            {
                Debug.LogError($"Download zip failed! {e} {e.StackTrace}");
            }
            finally
            {
                tk?.Cancel();
                EditorUtility.ClearProgressBar();
            }
        }
        
        //[Button("CheckLocalLog")]
        private void CheckLocalLog()
        {
            string localLogTestDir = "G:/CatchLog/Log";
            if (!Directory.Exists(localLogTestDir))
            {
                Debug.LogError($"本地日志目录不存在{localLogTestDir}");
                return;
            }
            
            EditorUtility.DisplayCancelableProgressBar("Call CheckFeedbackTC", "Please Wait.", 0);
            CheckFeedbackTC checkFeedback = new CheckFeedbackTC();
            checkFeedback.path = localLogTestDir;
            var result = checkFeedback.Check();
            if (!string.IsNullOrEmpty(result))
            {
                File.WriteAllText(localLogTestDir + "/result.json", result);
            }
            EditorUtility.ClearProgressBar();
        }
        
        private static bool UnzipOne(string zipPath,string time)
        {
            
            string fileName = Path.GetFileName(zipPath);
            string unzipPath = Path.Combine(Path.GetDirectoryName(zipPath),time);
            bool isUnzip = false;
            var log = ZipHelper.UnZipFile(zipPath, unzipPath);
            if (log.StartsWith("1;"))
            {
                // Debug.Log(log);

                // 使用系统自带winrar
                isUnzip = UnRarOrZip(unzipPath, zipPath, true, null);
            }
            else
                isUnzip = true;

            return isUnzip;
        }

        private static void Unzip(List<string> zipPaths, List<string> times)
        {
            int length = zipPaths.Count;
            for (int i = 0; i < length; i++)
            {
                string zipPath = zipPaths[i];
                string fileName = Path.GetFileName(zipPath);
                float progress = (float) i / length;

                if (EditorUtility.DisplayCancelableProgressBar("Catch Log", $"{i}:{length}  Unzip file：" + fileName,
                        progress))
                    break;

                string unzipPath = Path.Combine(Path.GetDirectoryName(zipPath), times[i]);
                //Path.GetFileNameWithoutExtension(zipPath)

                bool isUnzip = false;
                var log = ZipHelper.UnZipFile(zipPath, unzipPath);
                if (log.StartsWith("1;"))
                {
                    // Debug.Log(log);

                    // 使用系统自带winrar
                    isUnzip = UnRarOrZip(unzipPath, zipPath, true, null);
                }
                else
                    isUnzip = true;
            }

            // foreach (var zipPath in zipPaths)
            // {
            //     File.Delete(zipPath);
            // }
            EditorUtility.ClearProgressBar();
        }

        private static BugFeedback CreateBugFeedback(string csvPath)
        {
            var bugFeedback = new BugFeedback();
            try
            {
                var targetText = File.ReadAllText(csvPath, Encoding.GetEncoding("gb2312"));
                var allLines = CustomCsvParse(targetText,',');
                var keyLines = allLines[0];
                int logKey = -1;
                for (int i = 0; i < keyLines.Length; i++)
                {
                    Debug.Log(keyLines[i]);

                    if (keyLines[i].Contains("日志地址链接"))
                    {
                        logKey = i;
                        "".Print("logKey", logKey);

                        break;
                    }
                }

                if (logKey != -1)
                {
                    List<string> urls = new List<string>(keyLines.Length);
                    List<string> times = new List<string>();
                    for (int i = 1; i < allLines.Count; i++)
                    {
                        var valueLines = allLines[i];
                        if (logKey < valueLines.Length)
                        {
                            var line = valueLines[logKey];
                            if (line.Length > 2)
                            {
                                line = line.Trim();
                                line = line.Trim('\"');
                                //line = line.Substring(1, line.Length - 2);
                                if (line.StartsWith("http"))
                                {
                                    // 解析url
                                    urls.Add(line);

                                    // 解析日期
                                    var temp = line.Split('/');
                                    if (temp.Length > 3)
                                    {
                                        var time = temp[temp.Length - 3] + temp[temp.Length - 2];
                                        times.Add(time);
                                    }
                                    else
                                    {
                                        times.Add(string.Empty);
                                    }
                                }
                            }
                        }
                    }

                    bugFeedback.UrlPaths = urls;
                    bugFeedback.logTimes = times;
                }
                else
                {
                    Debug.LogError("col not found");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to parse {csvPath},\n{e}");
            }

            return bugFeedback;
        }

        /// <summary>
        /// http下载文件
        /// </summary>
        /// <param name="url">下载文件地址</param>
        /// <param name="path">文件存放地址，包含文件名</param>
        /// <returns></returns>
        private static bool HttpDownload(string url, string path, byte[] bytes = null)
        {
            string tempFile = path + ".catchlLogDownload"; //临时文件
            if (File.Exists(tempFile))
                File.Delete(tempFile); //存在则删除
            FileStream fs = null;
            HttpWebRequest request = null;
            HttpWebResponse response = null;
            Stream responseStream = null;
            try
            {
                fs = new FileStream(tempFile, FileMode.Append, FileAccess.Write, FileShare.ReadWrite);
                // 设置参数
                request = WebRequest.Create(url) as HttpWebRequest;
                //发送请求并获取相应回应数据
                response = request.GetResponse() as HttpWebResponse;
                //直到request.GetResponse()程序才开始向目标网页发送Post请求
                responseStream = response.GetResponseStream();
                //创建本地文件写入流
                //Stream stream = new FileStream(tempFile, FileMode.Create);
                if (bytes == null)
                    bytes = new byte[1024];
                int size = responseStream.Read(bytes, 0, (int) bytes.Length);
                while (size > 0)
                {
                    //stream.Write(bArr, 0, size);
                    fs.Write(bytes, 0, size);
                    size = responseStream.Read(bytes, 0, (int) bytes.Length);
                }

                //stream.Close();
                fs.Close();
                responseStream.Close();
                response.Close();

                File.Move(tempFile, path);

                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Http download failed! {ex}");

                fs?.Close();
                responseStream?.Close();
                response?.Close();
                return false;
            }
        }
        static string dataPath ;

        /// <summary>
        /// 解压RAR和ZIP文件(需存在Winrar.exe(只要自己电脑上可以解压或压缩文件就存在Winrar.exe))
        /// </summary>
        /// <param name="unPath">解压后文件保存目录</param>
        /// <param name="rarPathName">待解压文件存放绝对路径（包括文件名称）</param>
        /// <param name="isCover">所解压的文件是否会覆盖已存在的文件(如果不覆盖,所解压出的文件和已存在的相同名称文件不会共同存在,只保留原已存在文件)</param>
        /// <param name="passWord">解压密码(如果不需要密码则为空)</param>
        /// <returns>true(解压成功);false(解压失败)</returns>
        private static bool UnRarOrZip(string unPath, string rarPathName, bool isCover, string passWord)
        {
            if (!Directory.Exists(unPath))
                Directory.CreateDirectory(unPath);
            Process process = new Process();
            process.StartInfo.FileName = dataPath + "/../../../Tools/WinRAR/WinRAR.exe";
            // process.StartInfo.FileName = "Winrar.exe";
            process.StartInfo.CreateNoWindow = true;
            process.StartInfo.WindowStyle = ProcessWindowStyle.Hidden; //隐藏解压缩的窗口
            string cmd = "";
            if (!string.IsNullOrEmpty(passWord) && isCover)
                //解压加密文件且覆盖已存在文件( -p密码 )
                cmd = string.Format(" x -p{0} -o+ {1} {2} -y", passWord, rarPathName, unPath);
            else if (!string.IsNullOrEmpty(passWord) && !isCover)
                //解压加密文件且不覆盖已存在文件( -p密码 )
                cmd = string.Format(" x -p{0} -o- {1} {2} -y", passWord, rarPathName, unPath);
            else if (isCover)
                //覆盖命令( x -o+ 代表覆盖已存在的文件)
                cmd = string.Format(" x -o+ {0} {1} -y", rarPathName, unPath);
            else
                //不覆盖命令( x -o- 代表不覆盖已存在的文件)
                cmd = string.Format(" x -o- {0} {1} -y", rarPathName, unPath);
            //命令
            process.StartInfo.Arguments = cmd;
            process.Start();
            process.WaitForExit(); //无限期等待进程 winrar.exe 退出
            //Process1.ExitCode==0指正常执行，Process1.ExitCode==1则指不正常执行
            if (process.ExitCode == 0)
            {
                process.Close();
                return true;
            }
            else
            {
                process.Close();
                return false;
            }
        }
        /// <summary>
        /// 自定义解析csv文件  因为目前CSV文件中存在字段默认带有换行，用读取一行当csv中一行会有bug
        /// 换了个思路，读取第一行来确定字段数，然后根据字段数来解析每一行（每一个字段都是用,分割的，且不能取""内部的,来分割字段）
        /// </summary>
        /// <param name="csvText"></param>
        /// <param name="splitChar"></param>
        /// <returns></returns>
        private static List<string[]> CustomCsvParse(string csvText,char splitChar)
        {
            var result = new List<string[]>();
            int targetFieldCount = -1;
            var currentRecord = new List<string>();
            var currentField = new StringBuilder();
            bool inQuotes = false;
            int lineNumber = 0;

            foreach (char c in csvText)
            {
                if (c == '\n') lineNumber++;

                // 引号状态切换
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                    currentField.Append(c); // 保留原始引号
                    continue;
                }

                // 字段结束判断
                if (c == splitChar && !inQuotes)
                {
                    currentRecord.Add(currentField.ToString());
                    currentField.Clear();
                    continue;
                }

                // 行结束判断（非引号状态）
                if ((c == '\n' || c == '\r') && !inQuotes)
                {
                    if (currentField.Length > 0 || currentRecord.Count > 0)
                    {
                        currentRecord.Add(currentField.ToString());
                        currentField.Clear();
                    }

                    // 首行确定字段数
                    if (targetFieldCount == -1)
                    {
                        targetFieldCount = currentRecord.Count;
                        result.Add(currentRecord.ToArray());
                    }
                    // 字段数匹配时提交记录
                    else if (currentRecord.Count == targetFieldCount)
                    {
                        result.Add(currentRecord.ToArray());
                    }
                    // 否则暂存等待后续合并
                    else
                    {
                        // 等待后续行补充字段
                        continue;
                    }

                    currentRecord.Clear();
                }
                else
                {
                    currentField.Append(c);
                }
            }

            // 处理最后一行
            if (currentField.Length > 0)
                currentRecord.Add(currentField.ToString());
            if (currentRecord.Count == targetFieldCount)
                result.Add(currentRecord.ToArray());
            return result;
        }
    }
}

#endif