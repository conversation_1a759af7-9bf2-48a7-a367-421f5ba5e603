#if UNITY_EDITOR
using UnityEngine;
using UnityEngine.UI;
using UnityEditor;
using Sirenix.Utilities;
using System.IO;
using System.Security.Cryptography;
using System.Collections.Generic;
using System;
using System.Linq;
using System.Text;
using War.UI;

namespace Sirenix.OdinInspector.Demos
{
    [ExecuteInEditMode]
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    class ReplaceOldEffectsUI : GlobalConfig<ReplaceOldEffectsUI>
    {
        #region 检查旧资源
        [FolderPath]
        public string oldEffectPath = "Assets/Art/Effects/Prefabs/UI";

        [FoldoutGroup("ReplaceOldEffectsUI")]
        [FolderPath]
        public string newEffectPath = "Assets/Art/Effects_Source/Prefabs/UI";

        [FoldoutGroup("ReplaceOldEffectsUI")]
        public List<string> checkReplacePath = new List<string>();

        Dictionary<string, string> oldEffectDict = new Dictionary<string, string>();
        Dictionary<string, string> newEffectDict = new Dictionary<string, string>();
        Dictionary<string, List<string>> prefabEffectNameDict = new Dictionary<string, List<string>>();
        protected ExportCSV exportCSV = new ExportCSV();
        protected List<ExportCSV.PatientStatisticsOutputDto> ConnectEffectList = new List<ExportCSV.PatientStatisticsOutputDto>();
        private bool isStartReplace = false;
        [FoldoutGroup("ReplaceOldEffectsUI")]
        [Button("检查进度并导出资源替换信息")]
        public void StartCheckEffectsUI()
        {
            isStartReplace = false;
            CheckPrefabs();
        }
        GameObject go;
        [FoldoutGroup("ReplaceOldEffectsUI")]
        [Button("替换资源")]
        public void StartReplaceEffectsUI()
        {
            go = new GameObject("TempGO");
            isStartReplace = true;
            CheckPrefabs();
        }

        private void CheckPrefabs()
        {
            try
            {
                resReferencesls.Clear();
                EditorUtility.DisplayProgressBar("", "收集新旧特效……", 0.1f);
                oldEffectDict.Clear();
                newEffectDict.Clear();
                prefabEffectNameDict.Clear();
                ConnectEffectList.Clear();
                GetEffectResName(oldEffectPath, oldEffectDict);
                GetEffectResName(newEffectPath, newEffectDict);
                CheckReferences();
                EditorUtility.DisplayProgressBar("", "收集特效引用中……", 0.1f);
                for (int i = 0; i < checkReplacePath.Count; i++)
                {
                    CheckResEffect(checkReplacePath[i]);
                    EditorUtility.DisplayProgressBar("", "收集特效引用中……", 0.1f + (float)i / (float)checkReplacePath.Count * 0.2f);
                }
                EditorUtility.DisplayProgressBar("", "修改或导出特效引用中……", 0.3f);
                int lengthNum = 0;
                List<GameObject> goChildLists = new List<GameObject>();
                foreach (string name in prefabEffectNameDict.Keys)
                {
                    lengthNum++;
                    EditorUtility.DisplayProgressBar("", "修改或导出特效引用中……" + lengthNum + "/" + prefabEffectNameDict.Count, 0.3f + (float)lengthNum / (float)prefabEffectNameDict.Count * 0.5f);
                    for (int i = 0; i < prefabEffectNameDict[name].Count; i++)
                    {
                        if (string.IsNullOrEmpty(prefabEffectNameDict[name][i]))
                        {
                            continue;
                        }

                        ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto1 = new ExportCSV.PatientStatisticsOutputDto();
                        patientStatisticsOutputDto1.datas.Add(" ");
                        patientStatisticsOutputDto1.datas.Add(name);
                        patientStatisticsOutputDto1.datas.Add(prefabEffectNameDict[name][i]);
                        string replaceInfo = "";
                        string n = prefabEffectNameDict[name][i].Substring(prefabEffectNameDict[name][i].LastIndexOf("/") + 1) + ".prefab";
                        int type = 0;
                        if (oldEffectDict.ContainsKey(n.ToLower()))
                        {
                            patientStatisticsOutputDto1.datas.Add(n);
                            patientStatisticsOutputDto1.datas.Add(oldEffectDict[n.ToLower()]);
                            if (newEffectDict.ContainsKey(("Effect_" + n).ToLower()))
                            {
                                patientStatisticsOutputDto1.datas.Add("Effect_" + n);
                                patientStatisticsOutputDto1.datas.Add(newEffectDict[("Effect_" + n).ToLower()]);
                                type = 3;
                            }
                            else
                            {
                                type = 2;
                            }
                        }
                        else
                        {
                            if (newEffectDict.ContainsKey(n.ToLower()))
                            {
                                patientStatisticsOutputDto1.datas.Add(n);
                                if (oldEffectDict.ContainsKey(n.ToLower()))
                                {
                                    patientStatisticsOutputDto1.datas.Add(oldEffectDict[n.ToLower()]);
                                }
                                else
                                {
                                    patientStatisticsOutputDto1.datas.Add(" ");
                                }
                                patientStatisticsOutputDto1.datas.Add(n);
                                patientStatisticsOutputDto1.datas.Add(newEffectDict[n.ToLower()]);
                                type = 5;
                            }
                            else
                            {
                                type = 4;
                            }
                        }
                        if (isStartReplace)
                        {
                            GameObject objPrefab = PrefabUtility.InstantiatePrefab(AssetDatabase.LoadAssetAtPath<UnityEngine.Object>("Assets/UI/Prefabs/" + name + ".prefab"), go.transform) as GameObject;
                            //GameObject objPrefab = PrefabUtility.InstantiatePrefab(AssetDatabase.LoadAssetAtPath<UnityEngine.Object>("Assets/UI/Prefabs/UIHeroSelect.prefab"), go.transform) as GameObject;
                            bool isComplete = GetIsComplete(objPrefab);
                            if (!isComplete)
                            {
                                PrefabUtility.UnpackPrefabInstance(objPrefab.gameObject, PrefabUnpackMode.Completely, InteractionMode.UserAction);
                            }
                            else
                            {
                                patientStatisticsOutputDto1.datas[0] = "界面引用，手动检查";
                                ConnectEffectList.Add(patientStatisticsOutputDto1);

                                continue;
                            }
                            string nodePath = prefabEffectNameDict[name][i];
                            Transform tr = objPrefab.transform;
                            string p = nodePath.Substring(nodePath.IndexOf("/") + 1);
                            Transform child = tr.transform.Find(p);
                            if (child.GetComponent<DynamicGameObjectLoader>() != null)
                            {
                                DynamicGameObjectLoader dynamicGameObjectLoader = child.gameObject.GetComponent<DynamicGameObjectLoader>();
                                if (dynamicGameObjectLoader.abnames[0].Contains("prefabs"))
                                {
                                    string effName = Path.GetFileName(dynamicGameObjectLoader.abnames[0].Split('.')[0]);
                                    if (!dynamicGameObjectLoader.abnames[0].Contains("_source"))
                                    {
                                        dynamicGameObjectLoader.abnames[0] = ("Art/Effects_Source/Prefabs/UI/Effect_" + effName + ".prefab").ToLower();
                                    }
                                    if (patientStatisticsOutputDto1.datas.Count >= 4)
                                        patientStatisticsOutputDto1.datas[3] = effName.Replace("effect_", "");
                                    else
                                        patientStatisticsOutputDto1.datas.Add(effName.Replace("effect_", ""));
                                    if (patientStatisticsOutputDto1.datas.Count >= 5)
                                        patientStatisticsOutputDto1.datas[4] = dynamicGameObjectLoader.abnames[0].Replace("_source/", "/").Replace("/effect_", "/");
                                    else
                                        patientStatisticsOutputDto1.datas.Add(dynamicGameObjectLoader.abnames[0].Replace("_source/", "/").Replace("/effect_", "/"));
                                    if (patientStatisticsOutputDto1.datas.Count >= 6)
                                        patientStatisticsOutputDto1.datas[5] = effName;
                                    else
                                        patientStatisticsOutputDto1.datas.Add(effName);
                                    if (patientStatisticsOutputDto1.datas.Count >= 7)
                                        patientStatisticsOutputDto1.datas[6] = dynamicGameObjectLoader.abnames[0];
                                    else
                                        patientStatisticsOutputDto1.datas.Add(dynamicGameObjectLoader.abnames[0]);
                                }
                                else if (dynamicGameObjectLoader.abnames[0].Contains("prefabs"))
                                {
                                    dynamicGameObjectLoader.abnames[0] = patientStatisticsOutputDto1.datas[6];
                                }
                            }
                            else if (type == 3)
                            {
                                if (child.GetComponent<ParticleSystem>())
                                {
                                    DestroyImmediate(child.GetComponent<ParticleSystem>());
                                }
                                if (child && child.GetComponent<DynamicGameObjectLoader>() == null)
                                {
                                    DynamicGameObjectLoader dynamicGameObjectLoader = child.gameObject.AddComponent<DynamicGameObjectLoader>();
                                    dynamicGameObjectLoader.abnames.Add(patientStatisticsOutputDto1.datas[6]);
                                    for (int k = child.childCount - 1; k >= 0; k--)
                                    {
                                        if (child.GetChild(k))
                                        {
                                            child.GetChild(k).gameObject.SetActive(false);
                                            goChildLists.Add(child.GetChild(k).gameObject);
                                        }
                                    }
                                }

                                for (int j = 0; j < goChildLists.Count; j++)
                                {
                                    DestroyImmediate(goChildLists[j], true);
                                }
                                goChildLists.Clear();
                                bool isSuccess;
                                PrefabUtility.SaveAsPrefabAsset(objPrefab, "Assets/UI/Prefabs/" + name + ".prefab", out isSuccess);
                                if (isSuccess)
                                {
                                    AssetDatabase.Refresh();
                                }
                            }
                        }
                        replaceInfo = SetReplaceInfo(type);
                        if (CheckIsReplace(name, prefabEffectNameDict[name][i], patientStatisticsOutputDto1) == 1)
                        {
                            patientStatisticsOutputDto1.datas[0] = "是";
                        }
                        else
                        {
                            patientStatisticsOutputDto1.datas[0] = replaceInfo;
                        }
                        ConnectEffectList.Add(patientStatisticsOutputDto1);
                    }
                }
                EditorUtility.DisplayProgressBar("", "生成引用信息中……", 0.9f);

                exportCSV.ExportPatientStatisticsDetails(ConnectEffectList, Application.dataPath + "/Scripts/Sirenix/CSV/ReplaceConnectEffectsUI.csv", "是否替换,Prefab名,特效节点,旧特效Prefab名,旧特效AssetBundleName,新特效Prefab名,新特效AssetBundleName");
                ExportCSV.OpenDirectory(Application.dataPath + "/Scripts/Sirenix/CSV");
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("提示", "自动检测已完成，请核对", "确认");
                if (go)
                {
                    DestroyImmediate(go);
                }
            }
            catch (Exception e)
            {
                EditorUtility.ClearProgressBar();
                Debug.LogError("收集失败，" + e);
                throw;
            }
        }



        private void CheckReferences()
        {
            string path = "Assets/UI";
            List<string> extensionsInclude = new List<string>() { ".prefab" };

            string[] files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories)
                //获取指定后缀的文件
                .Where(s => extensionsInclude.Contains(Path.GetExtension(s).ToLower())).ToArray();

            var list = new List<string>();
            for (int i = 0; i < files.Length; i++)
            {
                string file = files[i].Replace("\\", "/");
                resReferencesls.Add(Path.GetFileName(file).Split('.')[0]);
            }
        }
        List<string> resReferencesls = new List<string>();

        private string GetRelativeAssetsPath(string path)
        {
            return "Assets" + Path.GetFullPath(path).Replace(Path.GetFullPath(Application.dataPath), "").Replace('\\', '/');
        }

        private string SetReplaceInfo(int type)
        {
            string str = "是";
            if (type == 2 || type == 5)
            {
                str = "需要新资源";
            }
            else if (type == 3)
            {
                str = "可替换";
            }
            else if (type == 4)
            {
                str = "手动排查";
            }
            return str;
        }

        private int CheckIsReplace(string name, string nodePath, ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto1)
        {
            UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath("Assets/UI/Prefabs/" + name + ".prefab", typeof(UnityEngine.Object));
            if (prefab != null)
            {
                GameObject tr = prefab as GameObject;
                string p = nodePath.Substring(nodePath.IndexOf("/") + 1);
                Transform child = tr.transform.Find(p);
                if (child)
                {
                    if (child.GetComponent<DynamicGameObjectLoader>())
                    {
                        return 1;
                    }
                    else
                    {
                        return 0;
                    }
                }
                else
                {
                    return 0;
                }
            }
            return 0;
        }

        /// <summary>
        /// 收集特效完整路径
        /// </summary>
        /// <param name="path"></param>
        private void CheckResEffect(string path)
        {
            string[] files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab")).ToArray();
            for (int i = 0; i < files.Length; i++)
            {
                List<string> nodePaths = PrintChildNodeM2(files[i]);//有特效路径

                if (nodePaths.Count > 0)
                {
                    prefabEffectNameDict.Add(Path.GetFileName(files[i]).Split('.')[0], nodePaths);
                }
            }
        }

        /// <summary>
        /// 查找引用特效的节点
        /// </summary>
        /// <param name="path"></param>
        public List<string> PrintChildNodeM2(string path)
        {
            string p = path.Replace('\\', '/');
            p = p.Substring(p.IndexOf("Assets/"));
            UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath(p, typeof(UnityEngine.Object));
            GameObject obj = prefab as UnityEngine.GameObject;
            ParticleSystem[] ts = obj.transform.GetComponentsInChildren<ParticleSystem>(true);
            //todo改造不收集子节点
            List<string> nodePaths = new List<string>();
            if (ts != null && ts.Length > 0)
            {
                for (int i = 0; i < ts.Length; i++)
                {
                    Transform tr = ts[i].transform;
                    if (tr == null || (tr.parent != null && tr.parent.GetComponent<ParticleSystem>() != null))
                    {
                        continue;
                    }
                    string np = GetHierarchyRelation<ParticleSystem>(tr);
                    if (!resReferencesls.Contains(np))
                        nodePaths.Add(np);
                }
            }
            DynamicGameObjectLoader[] ds = obj.transform.GetComponentsInChildren<DynamicGameObjectLoader>(true);
            if (ds != null && ds.Length > 0)
            {
                for (int i = 0; i < ds.Length; i++)
                {
                    Transform tr = ds[i].transform;
                    if (tr == null || (tr.parent != null && tr.parent.GetComponent<ParticleSystem>() != null))
                    {
                        continue;
                    }
                    string np = GetHierarchyRelation<ParticleSystem>(tr);
                    if (!resReferencesls.Contains(np))
                        nodePaths.Add(np);
                }
            }
            return nodePaths;
        }

        private bool GetIsComplete(GameObject trans)
        {
            Transform[] gols = trans.GetComponentsInChildren<Transform>(true);
            for (int i = 1; i < gols.Length; i++)
            {
                if (resReferencesls.Contains(gols[i].name) && "AllHeroList" != gols[i].name)
                {
                    Debug.LogError(gols[i].name);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 子节点查找
        /// </summary>
        /// <param name="trans"></param>
        /// <returns></returns>
        private string GetHierarchyRelation<T>(Transform trans) where T : Component
        {
            StringBuilder hierarchyStrBuilder = new StringBuilder();
            hierarchyStrBuilder.Append(trans.name);
            string separator = "/";
            Transform currentTrans = trans;
            while (currentTrans.parent != null)
            {
                hierarchyStrBuilder.Insert(0, separator);
                hierarchyStrBuilder.Insert(0, currentTrans.parent.name);
                currentTrans = currentTrans.parent;
                if (currentTrans.GetComponent<T>())
                {
                    return "";
                }
            }
            string hierarchyStr = hierarchyStrBuilder.ToString();
            return hierarchyStr;
        }

        /// <summary>
        /// 从name获取abname
        /// </summary>
        private void GetEffectResName(string path, Dictionary<string, string> dict)
        {
            string[] files = Directory.GetFiles(path, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab")).ToArray();
            for (int i = 0; i < files.Length; i++)
            {
                string p = files[i].Replace('\\', '/');
                UnityEngine.Object prefab = AssetDatabase.LoadAssetAtPath(p, typeof(UnityEngine.Object));
                var assetPath = AssetDatabase.GetAssetPath(prefab);
                string assetBundleName = assetPath.Substring(assetPath.IndexOf("/") + 1).ToLower();
                string name = assetPath.Substring(assetPath.LastIndexOf("/") + 1);
                if (dict.ContainsKey(name.ToLower()))
                {
                    Debug.LogError("特效名字有重复,手动查看---------->" + name);
                }
                else
                {
                    //Debug.LogError("name-->" + name);
                    dict.Add(name.ToLower(), assetBundleName);
                }
            }
        }
        #endregion

        #region 替换已有abname
        [FoldoutGroup("ReplaceUIOldAbname")]

        [FolderPath]
        public string UIPath = "Assets/UI/Prefabs";

        [FoldoutGroup("ReplaceUIOldAbname")]
        [Button("替换特效引用")]
        private void GetPathObj()
        {
            ConnectEffectList.Clear();
            string[] files = Directory.GetFiles(UIPath, "*.*", SearchOption.AllDirectories).Where(s => s.EndsWith(".prefab")).ToArray();
            for (int i = 0; i < files.Length; i++)
            {
                string p = files[i].Replace('\\', '/');
                UnityEngine.Object g = AssetDatabase.LoadAssetAtPath(p, typeof(UnityEngine.Object));
                if (null == g)
                {
                    continue;
                }
                GameObject go = g as GameObject;
                DynamicGameObjectLoader[] dynamicGameObjectLoaders = go.GetComponentsInChildren<DynamicGameObjectLoader>(true);
                if (dynamicGameObjectLoaders != null)
                {
                    foreach (DynamicGameObjectLoader dynamicGame in dynamicGameObjectLoaders)
                    {
                        if (dynamicGame.abnames.Count <= 0)
                        {
                            continue;
                        }
                        for (int j = 0; j < dynamicGame.abnames.Count; j++)
                        {
                            string path = dynamicGame.abnames[j];
                            string name = Path.GetFileName(path).Split('.')[0];
                            if (!name.Contains("Effect_") && !name.Contains("effect_"))
                            {
                                name = "Effect_" + name;
                            }
                            string abname = "art/effects/effects/" + name.ToLower() + "/prefabs/" + name.ToLower() + ".prefab";
                            dynamicGame.abnames[j] = abname;
                            ExportCSV.PatientStatisticsOutputDto patientStatisticsOutputDto = new ExportCSV.PatientStatisticsOutputDto();
                            patientStatisticsOutputDto.datas.Add(go.name);
                            patientStatisticsOutputDto.datas.Add(dynamicGame.gameObject.name);
                            patientStatisticsOutputDto.datas.Add(path);
                            patientStatisticsOutputDto.datas.Add(abname);
                            ConnectEffectList.Add(patientStatisticsOutputDto);
                        }
                    }
                }

                UnityEditor.EditorUtility.SetDirty(go);
                UnityEditor.AssetDatabase.SaveAssets();
                UnityEditor.AssetDatabase.Refresh();
            }

            exportCSV.ExportPatientStatisticsDetails(ConnectEffectList, Application.dataPath + "/Scripts/Sirenix/CSV/ReplaceUIOldAbname替换位置.csv", "路径名,节点名,替换前资源名,替换后资源名");
            ExportCSV.OpenDirectory(Application.dataPath + "/Scripts/Sirenix/CSV");
        }
        #endregion

        #region 匹配替换代码路径
        [FoldoutGroup("ReplaceOldEffectsLuaPath")]
        [FolderPath]
        public string luaFilePath = "Assets/Lua";

        [FoldoutGroup("ReplaceOldEffectsLuaPath")]
        [FolderPath]
        public string newCheckEffectPath = "Assets/Art/Effects/Effects";

        [FoldoutGroup("ReplaceOldEffectsLuaPath")]
        public List<string> noCheckPath = new List<string>();

        [FoldoutGroup("ReplaceOldEffectsLuaPath")]
        public bool isSetNewPath = false;

        [FoldoutGroup("ReplaceOldEffectsLuaPath")]
        [Button("替换资源")]
        public void CheckFileReference()
        {
            ReplaceOldEffectsUI replaceOldEffectsUI = new ReplaceOldEffectsPath();
            replaceOldEffectsUI.ReadAndWriteFile(luaFilePath, newCheckEffectPath, noCheckPath, isSetNewPath);
        }

        public virtual void ReadAndWriteFile(string luaP, string newP, List<string> nocheckls, bool isSetNewPath)
        {

        }

        #endregion
    }
}
#endif