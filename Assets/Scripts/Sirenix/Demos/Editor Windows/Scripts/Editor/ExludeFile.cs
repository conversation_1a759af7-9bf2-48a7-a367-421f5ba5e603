#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;
    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class ExludeFile : GlobalConfig<ExludeFile>
    {
        [Serializable]
        public class Proto { }

        [Serializable]
        public class StrProto : Proto
        {

            [HideLabel]
            [HorizontalGroup]
            public string name;

            [HorizontalGroup]
            public string size;

            [HorizontalGroup]
            [Button]
            void Locate()
            {
                var ass = AssetDatabase.LoadMainAssetAtPath(name);
                if (ass)
                {
                    Selection.activeObject = ass;
                }
            }

        }
        [Sirenix.OdinInspector.FilePath] 
        public List<string> findassets = new List<string>{
            "Assets/UI",
            "Assets/Art/Maps"
        };

        public List<StrProto> UnusedList;

        Func<string, bool> isPng = (s) => s.EndsWith(".png", StringComparison.CurrentCultureIgnoreCase) || s.EndsWith(".tga", StringComparison.CurrentCultureIgnoreCase);
        [Button()]
        public void FindUnused()
        {
            Clear();
            var allSet = new Dictionary<string, bool>();
            var usedSet = new Dictionary<string, bool>();

            int startIndex = 0;
            var finda = AssetDatabase.FindAssets("", findassets.ToArray());
            EditorApplication.update = delegate ()
            {
                try
                {
                    int count = finda.Length;
                    for (int i = 0; i < count/*finda.Length*/; i++)
                    {
                        string uid = finda[startIndex];
                        var ob = AssetDatabase.GUIDToAssetPath(uid);


                        bool isCancel = EditorUtility.DisplayCancelableProgressBar("Apply匹配资源中", ob, (float)startIndex / (float)finda.Length);

                        if (isPng(ob))
                        {
                            allSet[ob] = true;
                        }
                        else
                        {
                            var dependences = AssetDatabase.GetDependencies(ob, true);
                            foreach (var dep in dependences)
                            {
                                if (isPng(dep))
                                {
                                    usedSet[dep] = true;
                                }
                            }
                        }

                        startIndex++;
                        if (isCancel || startIndex >= finda.Length)
                        {
                            EditorUtility.ClearProgressBar();
                            EditorApplication.update = null;
                            startIndex = 0;

                            Debug.LogError(usedSet.Count);
                            Debug.LogError(allSet.Count);
                            var sett = new HashSet<string>(allSet.Keys);
                            sett.ExceptWith(usedSet.Keys);
                            Debug.LogError(sett.Count);
                            UnusedList = new List<StrProto>(sett.Convert<StrProto>((p) => {
                                FileInfo info = new FileInfo(p.ToString());
                                return new StrProto() { name = p.ToString(), size = (info.Length / 1024).ToString() };
                            }));

                            Debug.Log("匹配结束");
                            Log();

                            return;
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError(e.ToString());
                    EditorApplication.update = null;
                    EditorUtility.ClearProgressBar();
                }

            };
        }

        public void Check()
        {
            Clear();
            var allSet = new Dictionary<string, bool>();
            var usedSet = new Dictionary<string, bool>();

            int startIndex = 0;
            var finda = AssetDatabase.FindAssets("", findassets.ToArray());
            int count = finda.Length;
            for (int i = 0; i < count/*finda.Length*/; i++)
            {
                string uid = finda[startIndex];
                var ob = AssetDatabase.GUIDToAssetPath(uid);

                if (isPng(ob))
                {
                    allSet[ob] = true;
                }
                else
                {
                    var dependences = AssetDatabase.GetDependencies(ob, true);
                    foreach (var dep in dependences)
                    {
                        if (isPng(dep))
                        {
                            usedSet[dep] = true;
                        }
                    }
                }

                startIndex++;
                if (startIndex >= finda.Length)
                {
                    EditorUtility.ClearProgressBar();
                    EditorApplication.update = null;
                    startIndex = 0;

                    Debug.LogError(usedSet.Count);
                    Debug.LogError(allSet.Count);
                    var sett = new HashSet<string>(allSet.Keys);
                    sett.ExceptWith(usedSet.Keys);
                    Debug.LogError(sett.Count);
                    UnusedList = new List<StrProto>(sett.Convert<StrProto>((p) => {
                        FileInfo info = new FileInfo(p.ToString());
                        return new StrProto() { name = p.ToString(), size = (info.Length / 1024).ToString() };
                    }));

                    Log();
                }
            }
        }

        void Clear()
        {
            if (UnusedList.Count > 0)
            {
                UnusedList.Clear();
            }
        }

        void Log()
        {
            string logPath = Directory.GetParent(Path.GetDirectoryName(Application.streamingAssetsPath)) + "/Log/Asset/ExludeFile.txt";
            string dir = Path.GetDirectoryName(logPath);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            else
            {
                File.Delete(logPath);
            }
            foreach (var item in UnusedList)
            {
                EditorHelp.WriteFile(logPath, item.name);
            }
        }

        [Button]
        void MoveToOutsideDirectory(string OutsidePath = "C:/XHero/RedundantUIAssetsAssets")
        {
            foreach (var item in UnusedList)
            {
                string newPath = OutsidePath + item.name;
                EditorHelp.CheckDir(newPath);
                EditorHelp.CopyFile(item.name, newPath);
                EditorHelp.CopyFile(item.name + ".meta", newPath + ".meta");
                File.Delete(item.name);
                File.Delete(item.name + ".meta");
            }
        }
    }
}
#endif
