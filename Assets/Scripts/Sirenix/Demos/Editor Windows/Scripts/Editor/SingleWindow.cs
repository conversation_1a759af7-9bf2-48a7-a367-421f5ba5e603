#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using Sirenix.OdinInspector.Editor;
    using UnityEngine;
    using Sirenix.Utilities.Editor;
    using UnityEditor;

    public class SingleWindow : OdinMenuEditorWindow
    {
        public ScriptableObject t;

        public SingleWindow(ScriptableObject t):base()
        {
            this.t = t;
        }
         
        public static void Create(ScriptableObject t)
        {
            var sw = new SingleWindow(t);
            sw.Show();
        }
        protected override OdinMenuTree BuildMenuTree()
        {
            OdinMenuTree tree = new OdinMenuTree(supportsMultiSelect: true)
            {
                { "Home",                       t,                           EditorIcons.Link                       }, // Draws the this.someData field in this case. 
            };


            tree.SortMenuItemsByName();
            return tree;
        }
    }
}
#endif
