#if UNITY_EDITOR
using Sirenix.OdinInspector;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEngine.U2D;
using System.IO;
using UnityEditor.U2D;

namespace Assets.Scripts.Sirenix.Demos.Editor_Windows.Scripts.Editor.ToolObj
{
    [System.Serializable]
    public class AtlasFilter
    {
        [HideInInspector]
        public const string TIPS = "AtlasFilter（检查图集中是否存在ab不一致的资源）";

        [Title("文件夹底下的图集全部筛选(可空)")]
        [FolderPath]
        public string folder = "";

        [Title("仅筛选目标图集（可空）")]
        public SpriteAtlas tarAtlas;

        public string tarABName;

        [Title("true:使用图集名作为abname   false:使用tarABName字段")]
        public bool isUseAtlasFileNameToBeABName;

        [Button]
        public void Filter()
        {
            if (!string.IsNullOrEmpty(folder))
            {
                // 指定图集筛选
                var atlas = Directory.GetFiles(folder, "*.spriteatlas", SearchOption.AllDirectories);
                foreach (var atl in atlas)
                {
                    var sa = AssetDatabase.LoadMainAssetAtPath(atl) as SpriteAtlas;
                    ExcuteAtlas(sa, sa.name);
                }
            }
            else
            {
                // 文件夹底下批量筛选
                if (isUseAtlasFileNameToBeABName)
                {
                    tarABName = tarAtlas.name;
                }
                ExcuteAtlas(tarAtlas, tarABName);
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void ExcuteAtlas(SpriteAtlas tarAtlas,string tarABName)
        {
            var objList = SpriteAtlasExtensions.GetPackables(tarAtlas);
            List<Object> removeList = new List<Object>();
            var spriteDic = new Dictionary<string, SpriteAtlas>();
            var objDic = new Dictionary<string, List<Object>>();

            foreach (var item in objList)
            {
                string testPath = AssetDatabase.GetAssetPath(item);
                var uid = AssetDatabase.AssetPathToGUID(testPath);
                string filePath = AssetDatabase.GUIDToAssetPath(uid);
                var importer = AssetImporter.GetAtPath(filePath);
                var abName = importer.assetBundleName;
                if (tarABName != abName)
                {
                    if (string.IsNullOrEmpty(abName))
                    {
                        Debug.LogErrorFormat(" abName 为空的资源 {0} ", item.name);
                        continue;
                    }
                    removeList.Add(item);
                    Debug.LogFormat(" abName {0} ", abName);
                    var test1 = abName.Replace('/', '.');
                    var test2 = test1.Remove(abName.Length - 11);

                    // 缓存abname不同的object
                    List<Object> _objList;
                    if (objDic.ContainsKey(test2))
                        objDic.TryGetValue(test2, out _objList);
                    else
                        _objList = new List<Object>();
                    _objList.Add(item);

                    objDic[test2] = _objList;

                    // 缓存abname不同的spriteatlas
                    if (!spriteDic.ContainsKey(test2))
                    {
                        var ui_atals_root = CreateSpriteAtlas.ui_atlas_root;
                        string atlasName = string.Format("{0}/{1}.spriteatlas", ui_atals_root, test2);
                        var atlas = AssetDatabase.LoadAssetAtPath<SpriteAtlas>(atlasName);
                        if (atlas == null)
                            Debug.LogErrorFormat(" abName与图集名不一致，筛选失败！！！ abname={0} 图集名={1} ", abName, tarAtlas.name);
                        else
                            spriteDic.Add(test2, atlas);
                    }
                }
            }

            // 老图集进行删除
            SpriteAtlasExtensions.Remove(tarAtlas, removeList.ToArray());

            // 新图集进行添加
            foreach (var item in spriteDic)
            {
                SpriteAtlas sa = item.Value as SpriteAtlas;
                string abName = item.Key;
                var allObj = SpriteAtlasExtensions.GetPackables(sa);
                List<Object> _objlist;
                objDic.TryGetValue(abName, out _objlist);
                foreach (var obj in allObj)
                {
                    if (_objlist.Contains(obj))
                    {
                        Debug.LogFormat(" 不需要进行添加的图集名 {0} 添加的资源名 {1} ", abName, obj.name);
                        _objlist.Remove(obj);
                    }
                    else
                        Debug.LogFormat(" 要进行添加的图集名 {0} 添加的资源名 {1} ", abName, obj.name);
                }
                SpriteAtlasExtensions.Add(sa, _objlist.ToArray());
            }
        }
    }
}

#endif