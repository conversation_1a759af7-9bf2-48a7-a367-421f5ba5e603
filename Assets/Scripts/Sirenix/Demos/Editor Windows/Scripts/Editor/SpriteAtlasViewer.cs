#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.Collections.Generic;
    using System;
    using System.IO;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class SpriteAtlasViewer : GlobalConfig<SpriteAtlasViewer>
    {
        [System.Serializable]
        public class PathTex
        {
            //[HorizontalGroup("PathTex")]
            [HideLabel]
            public string atlasName;
            public System.Action<string> callback;
            [HorizontalGroup("PathTex")]
            [Button]
            void Select()
            {
                if (callback != null)
                {
                    callback.Invoke(atlasName);
                }
            }
        } 
        [BoxGroup("Inner")]
        public List<PathTex> packList = new List<PathTex>();
        [BoxGroup("Inner")]
        [OnValueChanged("ChangeAtlas")]
        public string selectAtlas;

        [BoxGroup("Inner")]
        [Button("Pack")]
        void Pack()
        {
#if !UNITY_2022_3_OR_NEWER
            EditorSettings.spritePackerMode = SpritePackerMode.AlwaysOn;
            UnityEditor.Sprites.Packer.RebuildAtlasCacheIfNeeded(EditorUserBuildSettings.activeBuildTarget);
            //UnityEditor.Sprites.Packer.GetAlphaTexturesForAtlas()
#endif
            var atlasName = new List<string>(UnityEditor.Sprites.Packer.atlasNames);

            packList = atlasName.ConvertAll<PathTex>((aName) =>
            {
                return new PathTex()
                {
                    atlasName = aName,
                    callback = ChangeAtlas,
                };
            });
            if (string.IsNullOrEmpty(selectAtlas) && atlasName.Count > 0)
            {
                selectAtlas = atlasName[0];
            }
            ChangeAtlas(selectAtlas);
        }

        void ChangeAtlas(string v)
        {
            var atls = UnityEditor.Sprites.Packer.GetTexturesForAtlas(v);
            if (atls.Length > 0)
            {
                atlas = atls[0];
            }
        }

        [BoxGroup("Inner")]
        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D atlas;


        public string atlasfolder;

            [HorizontalGroup("PathTex")]
        public List<PathTex> atlasList = new List<PathTex>();
        
        [Button]
        void fillAtlas()
        {
            atlasList.Clear();
            var files = System.IO.Directory.GetFiles(atlasfolder, "*.png");
            foreach (var item in files)
            {
                atlasList.Add(new PathTex()
                {
                    atlasName = Path.GetFileNameWithoutExtension(item),
                    callback = DiplayTex,
                });
            }
        }
        void DiplayTex(string filename)
        {
            var path = atlasfolder + "/" + filename+".png";
            if (File.Exists(path) == false) return;
            var w = new WWW("file://" + Path.GetFullPath(path));
            int count = 2000000;
            while (!w.isDone)
            {
                count--;
                if (count < 1) break;
            }

            if(w.isDone)
            {
                tex = w.texture;
                w.Dispose();
            }
            Debug.Log(count);
        }
            [HorizontalGroup("PathTex")]
        [InlineEditor(InlineEditorModes.LargePreview)]
        public Texture2D tex;
    }
}
#endif
