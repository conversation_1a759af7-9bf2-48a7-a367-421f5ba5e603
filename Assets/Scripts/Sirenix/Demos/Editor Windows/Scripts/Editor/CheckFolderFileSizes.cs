#if UNITY_EDITOR
namespace Sirenix.OdinInspector.Demos
{
    using UnityEngine;
    using UnityEditor;
    using Sirenix.Utilities;
    using System.IO;
    using System.Security.Cryptography;
    using System.Collections.Generic;
    using System;
    using War.UI;

    [GlobalConfig("Scripts/Sirenix/Demos/Editor Windows/Scripts/Objects")]
    public class CheckFolderFileSizes : GlobalConfig<CheckFolderFileSizes>
    {

        [FolderPath]
        public string checkPath = "";

        [TextArea]
        public string blackboard = "";

        //[Bool]
        [InfoBox("是否创建重复sprite的引用资源对象")]
        [HideLabel]
        public bool isCreateSameSpriteQuoteAsset = false; //是否创建重复sprite的引用资源对象，引用目标sprite为程序第一个遍历到的同md5文件

        //[HorizontalGroup]
        [InfoBox("是否删除已创建引用spriteAsset文件的原始文件")]
        [HideLabel]
        public bool isDeleteSameSprite = false; //是否删除已创建引用spriteAsset文件的原始文件

        //[HorizontalGroup]
        [InfoBox("是否替换cardAssets中重复的sprite")]
        [HideLabel]
        public bool isReplaceSameCardAssets = false; //是否替换cardAssets中重复的sprite


        [Button]
        void checkFolderFileSizes()
        {
            string fileInfos = "";
            long allfileCount = 0;
            Debug.Log("checkPath:" + checkPath);
            long allSize = GetDirectoryLength(checkPath, out fileInfos, out allfileCount);
            string directorySizeInfo = string.Format("{0}\t{1}\t{2}\t\tFile folder\t{3}", checkPath, allSize,
             EditorUtility.FormatBytes(allSize), allfileCount);
            fileInfos = string.Format("{0}\n{1}", directorySizeInfo, fileInfos);
            blackboard = fileInfos;
        }

        public long GetDirectoryLength(string dirPath, out string subFileInfos, out long subFileCount)
        {
            //判断给定的路径是否存在,如果不存在则退出
            subFileInfos = "";
            subFileCount = 0;
            if (!Directory.Exists(dirPath))
            {
                return 0;
            }

            long len = 0;

            //定义一个DirectoryInfo对象
            DirectoryInfo di = new DirectoryInfo(dirPath);
            string fileInfo = "";
            string extension = "";
            FileInfo[] allFileInfos = di.GetFiles();
            subFileCount = allFileInfos.Length;
            //通过GetFiles方法,获取di目录中的所有文件的大小
            string md5 = "";
            Dictionary<string, string> md5Dic = new Dictionary<string, string>();

            string hasSameMd5 = "";
            string widthHeight = "";
            string sameMd5File = "";
            long totalPix = 0;
            foreach (FileInfo file in allFileInfos)
            {
                len += file.Length;
                extension = System.IO.Path.GetExtension(file.FullName);
                md5 = File2MD5(file.FullName);
                hasSameMd5 = "";
                widthHeight = "";
                sameMd5File = "";
                string assetPath = file.FullName.Substring(file.FullName.IndexOf("Assets\\"));
                if (md5Dic.ContainsKey(md5))
                {
                    hasSameMd5 = md5;
                    sameMd5File = md5Dic[md5];
                }
                else
                {
                    md5Dic[md5] = assetPath;
                }

                var texture = AssetDatabase.LoadAssetAtPath<Texture2D>(assetPath) as Texture2D;
                if (texture)
                {
                    widthHeight = string.Format("{0}*{1}", texture.width, texture.height);
                    totalPix = texture.width * texture.height;
                }
                fileInfo = string.Format("{0}\t{1}\t{2}\t{3}\t{4}\t{5}\t{6}\t{7}\t{8}", assetPath, file.Length,

                EditorUtility.FormatBytes(file.Length), widthHeight, totalPix, extension, md5, hasSameMd5, sameMd5File); //string.Format()
                subFileInfos = subFileInfos == "" ? fileInfo : string.Format("{0}\n{1}", subFileInfos, fileInfo);

                //重复文件处理
                if (hasSameMd5 != "" && assetPath != "")
                {
                    Sprite spriteAsset = null;
                    //创建精灵应用资源
                    if (isCreateSameSpriteQuoteAsset)
                    {
                        spriteAsset = CreateSpriteQuoteAsset(sameMd5File, assetPath);
                    }
                    //是否使用引用sprite资源替换掉CardAssets中的对象
                    if (isReplaceSameCardAssets && spriteAsset != null)
                    {
                        ReplaceCardAssetsSprite(spriteAsset, assetPath);
                    }

                    if (isDeleteSameSprite)
                    {
                        if (File.Exists(assetPath))
                        {
                            File.Delete(assetPath);
                            Debug.Log("删除相同md5文件：" + assetPath);
                        }
                    }
                }
            }


            //获取di中所有的文件夹,并存到一个新的对象数组中,以进行递归
            DirectoryInfo[] dis = di.GetDirectories();
            if (dis.Length > 0)
            {

                string directorySubFileInfo = "";
                string directorySizeInfo = "";
                long directorySize = 0;
                long fileCount = 0;
                string disFullName = "";
                for (int i = 0; i < dis.Length; i++)
                {
                    disFullName = dis[i].FullName;
                    directorySize = GetDirectoryLength(disFullName, out directorySubFileInfo, out fileCount);
                    len += directorySize;
                    string disPath = disFullName.Substring(disFullName.IndexOf("Assets\\"));
                    //文件夹大小信息
                    directorySizeInfo = string.Format("{0}\t{1}\t{2}\t\tFile folder\t{3}", disPath, directorySize, EditorUtility.FormatBytes(directorySize), fileCount); //string.Format()
                    subFileCount += fileCount;
                    directorySubFileInfo = string.Format("{0}\n{1}", directorySizeInfo, directorySubFileInfo);
                    //文件夹子文件信息
                    subFileInfos = subFileInfos == "" ? directorySubFileInfo : string.Format("{0}\n\n{1}", subFileInfos, directorySubFileInfo);
                }
            }
            return len;
        }

        //替换cardassets内同名的sprite
        private void ReplaceCardAssetsSprite(Sprite newSprite, string oldSpritePath)
        {
            SpriteAssets cardAssets = AssetDatabase.LoadAssetAtPath<SpriteAssets>("Assets/UI/Card/Card.asset") as SpriteAssets;
            if (cardAssets.m_SpriteMaps.ContainsKey(newSprite.name))
            {
                var oldSprite = cardAssets.m_SpriteMaps[newSprite.name];
                var spritePath = AssetDatabase.GetAssetPath(oldSprite);

                if (spritePath == oldSpritePath)
                {
                    Debug.Log("替换card spirte失败 原始路径不匹配：oldPath" + spritePath + " inputOldPath:" + oldSpritePath);
                    return;
                }
                Debug.Log("！！！！！！！！！！！！！！替换cardAssets中的spirte" + newSprite.name + " oldPath:" + oldSpritePath);

                //cardAssets.m_SpriteList.Remove(oldSprite);
                var index = cardAssets.m_SpriteList.IndexOf(oldSprite);
                cardAssets.m_SpriteList[index] = newSprite;
                //cardAssets.m_SpriteList.Add(spriteAsset);
                cardAssets.m_SpriteMaps[newSprite.name] = newSprite;
            }
            UnityEditor.EditorUtility.SetDirty(cardAssets);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
        }


        //将列表内的sprite添加到cardassets中
        [Button]
        private void AddSpriteToCardAssets()
        {
            SpriteAssets cardAssets = AssetDatabase.LoadAssetAtPath<SpriteAssets>("Assets/UI/Card/Card.asset") as SpriteAssets;

            blackboard = blackboard.Replace("\r", "");
            string[] fileList = blackboard.Split(new string[] { "\n" }, StringSplitOptions.None);

            List<string> inputFilePaths = new List<string> { };
            inputFilePaths.AddRange(fileList);
            var index = 0;
            //输入的列表文件，如果没有abname，使用文件路径作为abname
            foreach (var item in inputFilePaths)
            {
                if (string.IsNullOrEmpty(item))
                {
                    continue;
                }
                var asset = item;
                var sourceSprite = AssetDatabase.LoadAssetAtPath<Sprite>(asset) as Sprite;

                if (cardAssets.m_SpriteMaps.ContainsKey(sourceSprite.name))
                {
                    var oldSprite = cardAssets.m_SpriteMaps[sourceSprite.name];
                    index = cardAssets.m_SpriteList.IndexOf(oldSprite);
                }
                else
                {
                    index = cardAssets.m_SpriteList.Count - 1;
                }
                Debug.Log("！！！！！！！！！！！！！！添加cardAssets中的spirte" + asset + "   SpriteName" + sourceSprite.name + "index" + index);
                cardAssets.m_SpriteList[index] = sourceSprite;
                cardAssets.m_SpriteMaps[sourceSprite.name] = sourceSprite;
            }

            UnityEditor.EditorUtility.SetDirty(cardAssets);
            UnityEditor.AssetDatabase.SaveAssets();
            UnityEditor.AssetDatabase.Refresh();
        }

        //创建sprite引用资源文件
        //sourceSpritePath需要引用的目标sprite
        //targetPath创建的资源路径
        private Sprite CreateSpriteQuoteAsset(string sourceSpritePath, string targetPath)
        {
            //var texture = AssetDatabase.LoadAssetAtPath<Texture2D> (filePath) as Texture2D;
            var sourceSprite = AssetDatabase.LoadAssetAtPath<Sprite>(sourceSpritePath) as Sprite;

            Sprite spriteAsset = Sprite.Create(sourceSprite.texture, sourceSprite.textureRect, sourceSprite.pivot, sourceSprite.pixelsPerUnit);
            string extension = System.IO.Path.GetExtension(targetPath); //文件扩展名
            var assetPath = targetPath.Replace(extension, ".asset");
            // AssetDatabase.CreateAsset(spriteAsset, assetPath);
            // AssetImporter assetImporter = AssetImporter.GetAtPath (assetPath);
            // assetImporter.assetBundleName = "";

            // List<string> lables = new List<string>();
            // lables.AddRange(AssetDatabase.GetLabels(assetImporter));
            // if (lables.IndexOf("NonPacktag") == -1)
            // {
            // 	lables.Add("NonPacktag");
            // }
            // AssetDatabase.SetLabels(assetImporter, lables.ToArray());

            Debug.Log("创建sprite引用资源文件 引用资源：" + sourceSpritePath + " 创建文件:" + targetPath);
            return spriteAsset;

        }


        //将制定文件内容转化为MD5
        static private string File2MD5(string path)
        {
            string res = null;
            if (File.Exists(path))
            {
                byte[] data = File.ReadAllBytes(path);
                MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
                byte[] md5Data = md5.ComputeHash(data, 0, data.Length);
                md5.Clear();

                string destString = "";
                for (int i = 0; i < md5Data.Length; i++)
                {
                    destString += System.Convert.ToString(md5Data[i], 16).PadLeft(2, '0');
                }
                destString = destString.PadLeft(32, '0');
                res = destString;
            }

            return res;
        }

    }
}
#endif
