
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using GoogleMobileAds.Api;
using GoogleMobileAds.Common;
using UnityEngine;
using Q1;
using XLua;
using GoogleMobileAds.Ump.Api;
using com.adjust.sdk;

namespace GoogleMobileAds
{
    [XLua.CSharpCallLua]
    public static class AdmobCallLua
    {
        [XLua.CSharpCallLua]
        public static List<Type> CSharpCallLua = new List<Type>()
        {
            //typeof(EventHandler<AdErrorEventArgs>),
            typeof(EventHandler<AdFailedToLoadEventArgs>),
            typeof(EventHandler<Reward>),
            typeof(Action<RewardedAd>),
            typeof(Action<RewardedAd,string>),
            typeof(Reward),
        };
        [XLua.LuaCallCSharp]
        public static List<Type> LuaCallCSharp = new List<Type>()
        {
            typeof(Reward),
            typeof(RewardedAd),
            typeof(ResponseInfo),
            typeof(Action<RewardedAd>),
            typeof(Action<RewardedAd,string>),


        };
    }

    public class AdMob
    {
        public static AdMob instance;
        public System.Action<bool, int, int, string> adCallback;
        public LuaFunction initCallback;
        public void IsMenaNew(){}   //占位函数，在lua中通过判断该函数是否存在来区分新老中东包
        public void IsAdsNewPackage(){}   //占位函数，在lua中通过判断该函数是否是包含广告的新包
        // 初始化sdk
        public void Initialize(LuaFunction callback)
        {
            Debug.Log("Admob Initialize");
#if UNITY_IOS
            AudienceNetwork.AdSettings.SetAdvertiserTrackingEnabled(true);
#endif
            MobileAds.Initialize(HandleInitCompleteAction); //没加cmp隐私协议之前的逻辑
            initCallback = callback;
        }

        //下面是新增CMP隐私协议功能的初始化方法，新包生效才调用
        public void InitializeWithCMP(LuaFunction callback)
        {
            Debug.Log("Admob InitializeWithCMP");
#if UNITY_IOS
            AudienceNetwork.AdSettings.SetAdvertiserTrackingEnabled(true);
#endif
            CMPSDKCheckShow();
            initCallback = callback;
        }

        public void CMPSDKCheckShow()
        {
            ConsentRequestParameters request;
            var cmpDebugDeviceID = PlayerPrefs.GetString("cmpDebugDeviceID", "");
            Debug.Log($"cmpDebugDeviceID:{cmpDebugDeviceID}");
            //以下是开启调试加的，看后面需不需要特殊处理或注释
            if (!string.IsNullOrEmpty(cmpDebugDeviceID))
            {
                var debugSettings = new ConsentDebugSettings
                {
                    DebugGeography = DebugGeography.EEA,
                    TestDeviceHashedIds = new List<string> { cmpDebugDeviceID }
                };
                request = new ConsentRequestParameters
                {
                    TagForUnderAgeOfConsent = false,
                    ConsentDebugSettings = debugSettings,
                };
            }
            else
                request = new ConsentRequestParameters { TagForUnderAgeOfConsent = false, };

            ConsentInformation.Update(request, OnConsentInfoUpdated);
        }

        public void CMPSDKReset()
        {
            Debug.Log("SDKReset");
            ConsentInformation.Reset();
        }
        public void CMPSDKSetDeviceID(string deviceID)
        {
            Debug.Log($"SDKSetDeviceID:{deviceID}");
            PlayerPrefs.SetString("cmpDebugDeviceID", deviceID);
        }
        public void CMPSDKGetDeviceID()
        {
            Debug.Log($"SDKGetDeviceID:{PlayerPrefs.GetString("cmpDebugDeviceID", "")}");
        }
        public void CMPSDKSetDeviceIDAndReset(string deviceID)
        {
            CMPSDKSetDeviceID(deviceID);
            CMPSDKReset();
        }

        public void CMPSDKShowConsentDialog()
        {
            Debug.Log("CMPSDKShowConsentDialog");
            ConsentForm.LoadAndShowConsentFormIfRequired((FormError formError) =>
            {
                if (formError != null)
                {
                    // Consent gathering failed.
                    UnityEngine.Debug.LogError(formError);
                    SendDataBasedOnConsentStatus(ConsentInformation.ConsentStatus, true);
                    return;
                }
                // Consent has been gathered.
                if (ConsentInformation.CanRequestAds())
                {
                    MobileAds.Initialize(HandleInitCompleteAction);
                    //MobileAds.Initialize((InitializationStatus initstatus) =>
                }
            });
        }

        void OnConsentInfoUpdated(FormError consentError)
        {
            if (consentError != null)
            {
                UnityEngine.Debug.LogError(consentError);
                SendDataBasedOnConsentStatus(ConsentInformation.ConsentStatus, true);
                return;
            }
            Debug.Log($"ConsentStatus is :{ConsentInformation.ConsentStatus}");

            switch (ConsentInformation.ConsentStatus)
            {   
                case ConsentStatus.Unknown:
                case ConsentStatus.NotRequired:
                case ConsentStatus.Obtained:
                {
                    MobileAds.Initialize(HandleInitCompleteAction);
                    break;
                }
                case ConsentStatus.Required:
                    CMPSDKShowConsentDialog();
                    break;
                default:
                    break;
            }
        }
        private bool sendCmpData = false;
        public bool SendCmpData
        {
            get { return sendCmpData; } set { sendCmpData = value; }
        }
        public void SetSendCmpDataTure()
        {
            sendCmpData = true;
        }
        private void SendDataBasedOnConsentStatus(ConsentStatus consentStatus,bool isError = false)
        {
            if (sendCmpData)
            {
                if (isError)
                {
                    SendCmpDataToAdjust("0", "0", "0");
                    return;
                }
                switch (consentStatus)
                {
                    case ConsentStatus.Unknown:
                    case ConsentStatus.NotRequired:
                        {
                            SendCmpDataToAdjust("0", "1", "1");
                            break;
                        }
                    case ConsentStatus.Obtained:
                        {
                            SendCmpDataToAdjust("1", "1", "1");
                            break;
                        }
                    case ConsentStatus.Required:
                        {
                            if (ConsentInformation.IsConsentFormAvailable())
                                SendCmpDataToAdjust("1", "1", "1");
                            else
                                SendCmpDataToAdjust("1", "0", "1");
                            break;
                        }
                    default:
                        break;
                }
            }
            else Debug.Log("SendDataBasedOnConsentStatus sendCmpData is false!!!");
        }
        private void SendCmpDataToAdjust(string eea, string ad_personalization, string ad_user_data)
        {
            string properties = string.Join(";", "google_dma", "eea", eea, "google_dma", "ad_personalization", ad_personalization, "google_dma", "ad_user_data", ad_user_data);
            Debug.Log($"SendCmpDataToAdjust eea:{eea},ad_personalization:{ad_personalization},ad_user_data:{ad_user_data},properties:{properties}");
            Adjust.trackThirdPartySharing(true,properties,"");
        }
        private void HandleInitCompleteAction(InitializationStatus initstatus)
        {
            Debug.Log($"HandleInitCompleteAction ConsentStatus is :{ConsentInformation.ConsentStatus}");
            SendDataBasedOnConsentStatus(ConsentInformation.ConsentStatus);
            // Callbacks from GoogleMobileAds are not guaranteed to be called on
            // main thread.
            // In this example we use MobileAdsEventExecutor to schedule these calls on
            // the next Update() loop.
            MobileAdsEventExecutor.ExecuteInUpdate(() => {
                Dictionary<string, AdapterStatus> map = initstatus.getAdapterStatusMap();
                foreach (KeyValuePair<string, AdapterStatus> keyValuePair in map)
                {
                    string className = keyValuePair.Key;
                    AdapterStatus status = keyValuePair.Value;
                    Debug.Log("Adapter: AdapterStatusMap className" + className + "status" + status);
                    switch (status.InitializationState)
                    {
                        case AdapterState.NotReady:
                            // The adapter initialization did not complete.
                            Debug.Log("Adapter: " + className + " not ready.");
                            break;
                        case AdapterState.Ready:
                            // The adapter was successfully initialized.
                            Debug.Log("Adapter: " + className + " is initialized.");
                            break;
                    }
                }

                if (initCallback != null)
                {
                    initCallback.Action();
                }
            });

        }

        public static AdMob Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = new AdMob();
                }
                return instance;
            }
        }

        Dictionary <int,AdsEvent> adsEventDic=new Dictionary<int, AdsEvent>();

        public AdsEvent GetAdsEventByUuid(int uuid)
        {
            if(adsEventDic.ContainsKey(uuid))
            return adsEventDic[uuid];
            else
            {
                return null;
            }
        }

        public void RemoveAdsEventByUuid(int uuid)
        {
            if (adsEventDic.ContainsKey(uuid))
                adsEventDic.Remove(uuid);
        }


        // private AdRequest CreateAdRequest()
        // {
        //     return new AdRequest.Builder()
        //         .Build();
        // }

        //带奖励回调的播放广告
        public void ShowWithRewardAction(RewardedAd ad,Action rewardAction)
        {
            Action<Reward> tmpAction = (tmp) =>
            {
                rewardAction?.Invoke();
            };
            ad.Show(tmpAction);
        }

        public void RequestAndLoadRewardedAd(string adUnitId,int uuid,Action<RewardedAd> registerEvent)
            {
                //Debug.Log("adUnitId"+adUnitId );
                if (adsEventDic.ContainsKey(uuid))
                {
                    Debug.LogError($"uuid:{uuid}已经存在,不能创建!!");
                    return;
                }

                AdsEvent adsEvent=new AdsEvent();
                adsEventDic[uuid] = adsEvent;
                RewardedAd.Load(adUnitId, new AdRequest(),
                    (RewardedAd ad, LoadAdError loadError) =>
                    {
                        Debug.Log("register RewardedAd");
                        registerEvent(ad);
                        if (loadError != null)
                        {
                            PrintStatus("Rewarded ad failed to load with error: " +
                                        loadError.GetMessage());
                            adsEvent.InvokeOnAdFailedToLoad(ad,loadError.GetMessage());
                            return;
                        }
                        else if (ad == null)
                        {
                            adsEvent.InvokeOnAdFailedToLoad(ad,"load fail!!");
                            return;
                        }
                        
                        adsEvent.InvokeOnAdLoaded(ad);
                        PrintStatus("Rewarded ad loaded.");

                        ad.OnAdFullScreenContentOpened += () =>
                        {
                            PrintStatus("Rewarded ad opening.");
                            adsEvent.InvokeOnAdOpening(ad);
                        };
                        ad.OnAdFullScreenContentClosed += () =>
                        {
                            PrintStatus("Rewarded ad closed.");
                            adsEvent.InvokeOnAdClosed(ad);
                        };
                        ad.OnAdFullScreenContentFailed += (AdError error) =>
                        {
                            PrintStatus("Rewarded ad failed to show with error: " +
                                        error.GetMessage());
                            adsEvent.InvokeOnAdFailedToShow(ad,error.GetMessage());
                        };
                        ad.OnAdPaid += (AdValue adValue) =>
                        {
                            //Debug.Log("test OnAdPaid Rewarded:" + adValue.Precision);
                            double admobValue = adValue.Value;
#if UNITY_ANDROID
                            //这里需要统一除以1000000，如果是单独接入android、ios而不是unity sdk的话，只需要对android处理
                            admobValue /= 1000000f;
#endif

                            AdapterResponseInfo loadedAdapterResponseInfo = ad.GetResponseInfo().GetLoadedAdapterResponseInfo();
                            // send ad revenue info to Adjust
                            Adjust.trackAdRevenue(admobValue, adValue.CurrencyCode, loadedAdapterResponseInfo.AdSourceName, adUnitId,"",-1);

                            string msg = string.Format("{0} (currency: {1}, value: {2}","Rewarded ad received a paid event.",adValue.CurrencyCode,admobValue);
                            PrintStatus(msg);
                            adsEvent.InvokeOnUserEarnedRewardNew(ad,admobValue.ToString());
                            PAMRewardedAdValueTest(admobValue);
                        };
                    });
            }
        //测试用的，上报指定广告收入，将该账户分配到固定的价值层级
        public void PAMRewardedAdValueTest(double value)
        {
            Firebase.Analytics.FirebaseAnalytics.LogEvent("PAM_Rewarded_Ad_Impression_Value", "PAM_Rewarded_Ad_Impression_Value", value);
        }

        ///<summary>
        /// Log the message and update the status text on the main thread.
        ///<summary>
        private void PrintStatus(string message)
        {
            Debug.Log("message:"+message );
        }

        // 启动中介功能测试套件
        public static void ShowMediationTestSuite()
        {
            MobileAds.OpenAdInspector(error =>
            {
                // Error will be set if there was an issue and the inspector was not displayed.
                if(error!=null)
                    Debug.Log(error.GetMessage());
            });
        }


    }

}
public class AdsEvent
{
    //广告加载成功回调
    public event Action<RewardedAd> OnAdLoaded;
    //广告加载失败回调
    public event Action<RewardedAd,string> OnAdFailedToLoad;
    //广告显示成功回调
    public event Action<RewardedAd> OnAdOpening;
    //广告显示失败回调
    public event Action<RewardedAd,string> OnAdFailedToShow;
    //广告获取奖励回调
    public event Action<RewardedAd> OnUserEarnedReward;
    //广告关闭回调
    public event Action<RewardedAd> OnAdClosed;
    //广告关闭回调
    public event Action<RewardedAd,string> OnUserEarnedRewardNew;

    public void InvokeOnAdLoaded(RewardedAd ad)
    {
        OnAdLoaded?.Invoke(ad);
    }
    public void InvokeOnAdFailedToLoad(RewardedAd ad,string error)
    {
        OnAdFailedToLoad?.Invoke(ad,error);
    }
    public void InvokeOnAdOpening(RewardedAd ad)
    {
        OnAdOpening?.Invoke(ad);
    }
    public void InvokeOnAdFailedToShow(RewardedAd ad,string error)
    {
        OnAdFailedToShow?.Invoke(ad,error);
    }
    public void InvokeOnUserEarnedReward(RewardedAd ad)
    {
        OnUserEarnedReward?.Invoke(ad);
    }
    public void InvokeOnAdClosed(RewardedAd ad)
    {
        OnAdClosed?.Invoke(ad);
    }
    public void InvokeOnUserEarnedRewardNew(RewardedAd ad,string admobValue)
    {
        OnUserEarnedRewardNew?.Invoke(ad,admobValue);
    }
}