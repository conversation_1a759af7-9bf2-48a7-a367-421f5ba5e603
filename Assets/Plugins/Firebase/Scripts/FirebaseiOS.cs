
using System.Collections.Generic;
using UnityEngine;
using Firebase.Analytics;
using System;
using War.Base;

namespace com.firebase.sdk
{
    public class FirebaseiOS
    {
        private static bool s_isInit = false;
        private static Dictionary<string, object> newDic = new Dictionary<string, object>();

        public static void Init()
        {
//#if UNITY_IOS
            s_isInit = true;
            newDic = new Dictionary<string, object>();
            //#endif
        }


        public static void LogEventProxy(string key, string value)
        {
//#if UNITY_IOS
            if (s_isInit)
            {
                LogEvent(key, value);
            }
//#endif
        }

        public static void LogEvent(string eventName)
        {
            FirebaseAnalytics.LogEvent(eventName);
        }

        public static void TrackEvent(string ev, Dictionary<string, object> dic = null, JsonModule jsonModule = JsonModule.NewtonsoftJson)
        {
            Debug.Log("firebase_ios_LogEvent TrackEvent");
            AssetBundleManager.TrackEvent(ev, dic, jsonModule);
        }

        public static void LogEvent(string eventName, string data)
        {
            try
            {
                List<Parameter> param = new List<Parameter>();
                List<string> keyList = new List<string>();
                string sub = data.Substring(1, data.Length - 2);
                string[] subData = sub.Split(',');
                foreach (string s in subData)
                {
                    string[] pairs = s.Split('=');
                    string key = pairs[0].Substring(0, pairs[0].Length - 1);
                    if (!keyList.Contains(key))
                    {
                        keyList.Add(key);
                        string value_string = pairs[1].Substring(1, pairs[1].Length - 1);
                        double value_double = 0f;
                        long value_long = 0L;

                        if (long.TryParse(value_string, out value_long))
                        {
                            param.Add(new Parameter(key, value_long));
                        }
                        else if (double.TryParse(value_string, out value_double))
                        {
                            param.Add(new Parameter(key, value_double));
                        }
                        else
                        {
                            value_string = value_string.Substring(1, value_string.Length - 2);
                            param.Add(new Parameter(key, value_string));
                        }
                    }
                }
                FirebaseAnalytics.LogEvent(eventName, param.ToArray());
            }
            catch(Exception e)
            {
                Debug.LogError("iOS Firebase LogEvent Exception: " + e.Message);
            }
        }
        
        public static void RechargeSuccessTrackEvent(string eventName, string data,double value)
        {
            if (eventName == "Event_RechargeSuccess")
            {
                newDic.Clear();
                newDic.Add("data_string", data);
                newDic.Add("value", value);
                TrackEvent("firebase_ios_valueType", newDic);
            }
            FirebaseAnalytics.LogEvent(
                eventName,
                new Parameter(
                    "currency", "USD"),
                new Parameter(
                    "value", value)
            );
        }
        
        public static void LogEvent(string eventName, string key, double value)
        {
            FirebaseAnalytics.LogEvent(eventName, key, value);
        }

        public static void LogEvent(string eventName, string key, int value)
        {
            FirebaseAnalytics.LogEvent(eventName, key, value);
        }

        public static void LogEvent(string eventName, string key, long value)
        {
            FirebaseAnalytics.LogEvent(eventName, key, value);
        }

        public static void LogEvent(string eventName, string key, string value)
        {
            FirebaseAnalytics.LogEvent(eventName, key, value);
        }

        public static string GetToken()
        {
            string rt = string.Empty;
//#if UNITY_IOS
            
//#endif
            return rt;
        }
    }
}
