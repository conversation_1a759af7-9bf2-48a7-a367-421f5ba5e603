
using System.Collections.Generic;
using UnityEngine;

namespace com.firebase.sdk
{
    public class FirebaseAndroid
    {

        private static AndroidJavaClass j_FirebaseProxy;
        private static bool s_isInit = false;

        public static void Init()
        {
#if UNITY_ANDROID
            try
            {
                j_FirebaseProxy = new AndroidJavaClass("com.q1.Firebase.FirebaseProxy");
                j_FirebaseProxy.CallStatic("InitSdk");
                s_isInit = true;
            }
            catch (System.Exception e)
            {

                Debug.LogError(e.ToString());
            }
#endif
        }


        public static void LogEvent(string key, string value)
        {
#if UNITY_ANDROID
            if (s_isInit)
            {
                try
                {
                    j_FirebaseProxy.CallStatic("LogEvent", key, value);
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.ToString());
                }
            }
#endif
        }

        public static string GetToken()
        {
            string rt = string.Empty;
#if UNITY_ANDROID
            if (s_isInit)
            {
                try
                {
                    rt = j_FirebaseProxy.CallStatic<string>("GetToken");
                }
                catch (System.Exception e)
                {
                    Debug.LogError(e.ToString());
                }
            }
#endif
            return rt;
        }
    }
}
