---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/11/26 10:38
--- Desc: 新手引导章节数据
local require = require
local table = table
local pairs = pairs
local ipairs = ipairs

local force_guide_event = require "force_guide_event"
local gw_home_novice_util = require "gw_home_novice_util"

local puzzlegame_mgr = require "puzzlegame_mgr"
local ui_window_mgr = require "ui_window_mgr"
local novice_guide_home_func = require "novice_guide_home_func"
local log = require "log"
local gw_home_novice_const = require "gw_home_novice_const"
local event = require "event"
local GWConst = require "gw_const"

local gw_ed = require "gw_ed"
local data_mgr = require "data_mgr"
local game_scheme = require "game_scheme"

local prop_pb = require "prop_pb"
local player_mgr = require "player_mgr"
local topic_pb = require "topic_pb"
local net_prop_module = require "net_prop_module"
local tostring = tostring
local GWG = GWG
-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@class GWHomeNoviceChapterData
module("gw_home_novice_chapter_data")
local M = {}
local self = M --简化写法，静态类中直接也用Self获取自身
---所有数据存储
local _d = data_mgr:CreateData("gw_home_novice_chapter_data")
---非服务器数据存储
local mc = _d.mde.const
---服务器数据存储
local mn = _d.mde.net
function M.GetNoviceHomeOpen()
    local open = gw_home_novice_util.GetIsNovice()
    return open
end
---@public 获取新手 并且 通关了所以关卡
function M.GetNoviceAndCheckPassLevel()
    return M.GetNoviceHomeOpen() and M.CheckPassLevel()
end
---@public 获取新手 并且 没有通关了大本事件
function M.GetNoviceAndNoCheckPassLevel()
    if M.GetNoviceHomeOpen() then
        return not M.CheckPassEvent(gw_home_novice_const.NoviceHomeMainEventId)
    else
        return false
    end
end
---@public 获取新手 并且 没有通关全部事件
function M.GetNoviceAndNotCheckPassLevel()
    return M.GetNoviceHomeOpen() and not M.CheckPassLevel()
end

function M.Init()
    event.Register(event.NET_RECONNECT_SUCCESS_EVENT, M.OnReconnectSuccessEvent)
    if not self.GetNoviceHomeOpen() then
        mc.currentEvent = gw_home_novice_const.NoviceHomeEventMaxEndId
        return
    end
    player_mgr.AddInitEvent(prop_pb.PERSONPART_SUBJECT, M.OnLoginEvent)
    net_prop_module.AddTopicsEvent(topic_pb.TOPICNAME_NEWPLAYERACT_DATA, M.OnTopicEvent)
    event.Register(event.FIRST_ENTER_GAME, M.OnFirstEnterGame)
    event.Register(event.NEW_XYX_BUILD_SUCCESS, M.OnXYXBuildSuccess)
end
function M.OnFirstEnterGame()
    self.firstGame = true
    self.FirstTriggerEvent()
end

function M.OnXYXBuildSuccess()
--[[    local gw_ab_test_mgr = require "gw_ab_test_mgr"
    local buildType = gw_ab_test_mgr.GetHookLevelType()
    if buildType == 1 then
        return        
    end
    self.xyxBuildSuccess = true
    self.FirstTriggerEvent()]]
end

function M.FirstTriggerEvent()
    --[[    local gw_ab_test_mgr = require "gw_ab_test_mgr"
        local buildType = gw_ab_test_mgr.GetHookLevelType()
        if buildType ~= 1 then
            if not self.xyxBuildSuccess then
                return
            end
        end]]
    if not self.firstGame then
        return
    end
    if not self.GetNoviceHomeOpen() then
        mc.currentEvent = gw_home_novice_const.NoviceHomeEventMaxEndId
        return
    end
    if gw_home_novice_util.GetNoviceOneMiniGamePass() then
        return
    end
    --防止进入战斗了 还没有注册事件
    self:InitSceneEvent()
    local curEvent = M.GetCurrentEventPoint()
    if curEvent <= gw_home_novice_const.NoviceHomeEventAreaId then
        local func = novice_guide_home_func.GetFun("StartMiniGame")
        func();
        M.BattleStart()
    end
end

---@see 初始化
function M.InitData()
    if not self.GetNoviceHomeOpen() then
        return
    end
    mc._MainCityMapData = {}           --地图数据   {事件id = {地图id}，}
    mc._BuildEventData = {}        --事件区域数据{事件id,事件id,事件id}
    mc._MoveNextCompleteAction = false --下一步行动 移动动画 是否完成

    mc._EventEntityData = {}           --事件实体数据{事件id = {事件实体} }

    mc._LastEventId = 0                --上一次事件id

    mc._StartPointEventId = 0          --起点事件id
    mc._EndEventId = 0                 --终点事件id

    mc.isUnLock = false                --是否解锁

    mc.isMoveActionIng = false         --是否正在移动动画   
    mc._StartBattleEventId = 0         --开始事件id
    mc.playerEventId = 0               --玩家行走到的当前事件id
    mc._InitRegister = false
    self.InitCfgData()
end

function M.InitSceneEvent()
    if not self.GetNoviceHomeOpen() then
        return
    end
    --防止重复注册
    if mc._InitRegister then
        return
    end
    
    gw_home_novice_util.LogWarning("novice InitSceneEvent ")
    event.Register(event.NEW_BATTLE_START, M.BattleStart)
    event.Register(event.BATTLE_RESULT_CLOSE, M.OnBattleResultClose)
    event.Register(event.NEW_BATTLE_BACK, M.OnBattleResultBack)

    event.Register(event.NEW_XYX_START_LEVEL, M.BattleStart)
    event.Register(event.NEW_XYX_END_LEVEL, M.OnBattleResultClose)
    mc._InitRegister = true
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

---@public 初始化地图数据坐标
function M.InitCfgData()
    self.InitMainCityMapData()
    self.InitBuildEventData()
end

---@public 初始化地图数据
function M.InitMainCityMapData()
    --生成地图 根据发送的 数据
    local len = game_scheme:BuildMaincityMap_nums()
    for i = 0, len - 1 do
        local cityMapCfg = game_scheme:BuildMaincityMap(i)
        --判断是地图类型
        if cityMapCfg and cityMapCfg.type and cityMapCfg.type == GWConst.MapCityType.NoviceEvent then
            if cityMapCfg.buildingID and cityMapCfg.buildingID.data then
                mc._MainCityMapData[cityMapCfg.buildingID.data[0]] = cityMapCfg.MapID
            end
        end
    end
end
---@public 获取需要显示的事件
function M.GetBuildEventTypeList()
    mc._showBubbleBuildTypeList = {}
    for i, eventId in pairs(mc._BuildEventData) do
        if eventId < self.GetCurrentEventPoint() then
            local buildEventProCfg = game_scheme:BuildPreProcess_0(eventId)
            for j, typeId in pairs(buildEventProCfg.bubbleShow.data) do
                local data = {}
                data.typeId = typeId
                data.scale = buildEventProCfg.bubbleScal
                table.insert(mc._showBubbleBuildTypeList, data)
            end
        end
    end
    return mc._showBubbleBuildTypeList
end

---@public 获取地图id
---@param eventId number 事件id
function M.GetMainCityMapId(eventId)
    if not mc._MainCityMapData[eventId] then
        GWG.GWAdmin.SwitchUtility.Error("mc._MainCityMapData[eventId] = nil ", eventId)
        return 0
    end
    return mc._MainCityMapData[eventId]
end

---@public 初始化事件区域数据
function M.InitBuildEventData()
    local len = game_scheme:BuildPreProcess_nums()
    for i = 0, len - 1 do
        local buildEventProCfg = game_scheme:BuildPreProcess(i)
        if not buildEventProCfg then
            GWG.GWAdmin.SwitchUtility.Error("buildEventProCfg  = nil", i)
            return
        end
        local eventId = buildEventProCfg.EventID
        if buildEventProCfg and buildEventProCfg.type and buildEventProCfg.type == GWConst.BuildEventType.StartPoint then
            mc._StartPointEventId = eventId
        end
        mc._EndEventId = eventId
        table.insert(mc._BuildEventData, eventId)
    end
end

---@public 创建事件实体数据（有可能重复刷新）
function M.CreateEventEntity()
    --判断是否通过全部关卡
    local isCompleteAll = self.CheckPassLevel()
    if isCompleteAll then
        return
    end
    local currentId = self.GetCurrentEventPoint()
    for i, eventId in pairs(mc._BuildEventData) do
        local mapId = self.GetMainCityMapId(eventId)
        if mapId ~= 0 and currentId <= eventId then
            self.CreateEventModel(mapId, eventId)
        end
    end
end

---@public 刷新事件实体数据（有可能重复刷新）
function M.RefreshEventEntity()
    if not mc._EventEntityData then
        return
    end
    for i, eventEntity in pairs(mc._EventEntityData) do
        if eventEntity then
            eventEntity:RefreshData()
        end
    end
end

---@public 刷新老事件实体数据（有可能重复刷新）
function M.RefreshOldEventEntity(currentId)
    if not mc._EventEntityData then
        return
    end
    for i, eventEntity in pairs(mc._EventEntityData) do
        if i <= currentId and eventEntity then
            eventEntity:RefreshData()
        end
    end
end

---@public 刷新玩家模型（第一次关卡或者通过关卡之后执行）
function M.RefreshPlayerModel()
    if not mc.isUnLock then
        self.CreateEventEntity()
        self.RefreshAreaUnlock()
        mc._LastEventId = mc.currentEvent
    else
        mc._LastEventId = mc.currentEvent
        self.CreateEventEntity()
        self.CreatePlayerModel()
    end
end

function M.CheckJustPassEvent(eventId)
    local nextEventId = self.GetCurrentEventPoint()
    if nextEventId == 0 then
        return false
    end
    local nextEventCfg = game_scheme:BuildPreProcess_0(nextEventId)
    if nextEventCfg and nextEventCfg.PreEvent and nextEventCfg.PreEvent == eventId then
        return true
    end
    return false
end
---@public 判断玩家是否通过事件
function M.CheckPassEvent(eventId)
    if self.GetCurrentEventPoint() > eventId then
        return true
    end
    return false
end

function M.RefreshUpdateCheckEvent()
    self.UpdateCheckEventComplete()
    mc._LastEventId = mc.currentEvent
end

---@public 利用已有数据重新刷新表现
function M.RefreshShowByData()
    if not self.GetNoviceHomeOpen() then
        return
    end
    self.RefreshPlayerModel()
    self.EventRefreshBubbleShow(self.GetCurrentEventPoint())
end

---@public 获取当前要进行的事件
function M.GetCurrentEventPoint()
    if not mc.currentEvent then
        return gw_home_novice_const.NoviceHomeEventAreaId
    end
    if mc.currentEvent == mc._StartPointEventId then
        return gw_home_novice_const.NoviceHomeEventAreaId
    end
    return mc.currentEvent
end

function M.GetStartPoint()
    return mc._StartPointEventId
end

---@public 获取玩家事件id
function M.GetPlayerEventId()
    return mc.playerEventId
end

---@public 刷新区域解锁
function M.RefreshAreaUnlock()
    self.CreatePlayerModel()
    self.RefreshEventEntity()
end

---@public 刷新事件完成对应状态
function M.UpdateCheckEventComplete()
    if self.CheckPassLevel() then
        self.DisposeMoveNextAction()
        self.RefreshAreaUnlock()
        return
    end
    if mc.isMoveActionIng then
        self.DisposeMoveNextAction()
        return
    end
    --判断当前是否是最后一关
    if self.GetCurrentEventPoint() == mc._EndEventId then
        self.DisposeMoveNextAction()
        self.RefreshAreaUnlock()
        return
    end
    mc.isMoveActionIng = true
    --下一关的id
    local nextEventId = self.GetCurrentEventPoint()
    if nextEventId == 0 then
        self.DisposeMoveNextAction()
        return
    end
    local nextEventCfg = game_scheme:BuildPreProcess_0(nextEventId)
    if not nextEventCfg then
        GWG.GWAdmin.SwitchUtility.Error("UpdateCheckEventComplete nextEventCfg = nil", nextEventId)
        self.DisposeMoveNextAction()
        return
    end
    local curCompleteEventId = nextEventCfg.PreEvent
    local curCompleteEventCfg = game_scheme:BuildPreProcess_0(curCompleteEventId)
    if not curCompleteEventCfg then
        GWG.GWAdmin.SwitchUtility.Error("UpdateCheckEventComplete curCompleteEventCfg = nil")
        self.DisposeMoveNextAction()
        return
    end
    if curCompleteEventCfg.type == GWConst.BuildEventType.StartPoint then
        self.DisposeMoveNextAction()
        return
    end
    local curEventId = curCompleteEventCfg.PreEvent
    --第二次进入场景 动画执行
    self.CreatePlayerModel(curCompleteEventId)
    if curCompleteEventId == gw_home_novice_const.NoviceHomeMainEventId then
        self.DisposeEventModel(curCompleteEventId)
    end
    self.RefreshEventData(curEventId)
    self.RefreshOldEventEntity(nextEventId)
    self.DisposeMoveNextAction()
end


--region 事件实体管理

---@public 创建事件实体
---@param cityMapId number 地图id
---@param eventId number 事件id
function M.CreateEventModel(cityMapId, eventId)
    if mc._EventEntityData[eventId] then
        GWG.GWAdmin.SwitchUtility.Error("CreateEventModel mc._EventEntityData[eventId] 已经创建了", cityMapId, eventId)
        return
    end
    local id = GWG.GWIdMgr:AllocComponentId()
    local comp = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_novice_comp_event_base, id)
    if comp then
        comp.entityType = GWG.GWConst.EHomeEntityType.EventEntity
        comp:InitData(cityMapId, eventId)
        mc._EventEntityData[eventId] = comp
    else
        GWG.GWAdmin.SwitchUtility.Error("CreateEventModel comp = nil", cityMapId, eventId)
    end
end

---@public 销毁事件实体
---@param eventId number 事件id
function M.DisposeEventModel(eventId)
    if mc._EventEntityData[eventId] then
        GWG.GWAdmin.PushBSComponent(mc._EventEntityData[eventId])
        mc._EventEntityData[eventId] = nil
    end
end

function M.DeadEventModel(eventId, callback)
    local entity = M.GetEventEntity(eventId)
    local function deadCallback()
        if callback then
            callback()
        end
        self.DisposeEventModel(eventId)
    end
    if not entity then
        deadCallback()
        return
    end
    entity:DeadModel(deadCallback)
end

---@public 创建玩家模型
function M.CreatePlayerModel(eventId, isNotBubble)
    local isCompleteAll = self.CheckPassLevel()
    if isCompleteAll then
        self.DisposePlayer()
        return
    end
    if not eventId then
        local curEventId = self.GetCurrentEventPoint()
        if curEventId == 0 or curEventId == mc._EndEventId then
            self.DisposePlayer()
            return
        end
        if curEventId == gw_home_novice_const.NoviceHomeEventAreaId then
            eventId = curEventId
        else
            eventId = curEventId - 1
        end
    end
    if mc.PlayerComp then
        local playerEffect = true
        if isNotBubble ~= nil or isNotBubble == true then
            playerEffect = false
        end
        mc.PlayerComp:RefreshEventData(eventId, isNotBubble, playerEffect)
    else
        local id = GWG.GWIdMgr:AllocComponentId()
        local comp = GWG.GWAdmin.PopBSComponent(GWG.GWCompName.gw_home_novice_comp_event_player, id)
        if not comp then
            GWG.GWAdmin.LogError("CreatePlayerModel: Failed to populate player component with id " .. id)
            self.DisposePlayer()
            return
        end
        mc.PlayerComp = comp
        mc.PlayerComp.entityType = GWG.GWConst.EHomeEntityType.EventEntity
        mc.playerEventId = eventId
        local mapId = self.GetMainCityMapId(eventId)
        if mapId ~= 0 then
            comp:InitData(mapId, eventId, GWG.GWHomeNode.eventNode(), isNotBubble)
        end
    end
end

---@public 下一关播放动画
---@param curEventId number 当前事件id
---@param curCompleteEventId number 当前完成事件id
function M.NextLevelMove(curEventId, curCompleteEventId, callback)
    mc.isMoveActionIng = true
    --模型开始移动到下一个位置
    if not mc.PlayerComp then
        self.DisposeMoveNextAction()
        callback()
        return
    end
    mc.playerEventId = curCompleteEventId
    --播放动画
    mc.PlayerComp:MoveNextEvent(curEventId, curCompleteEventId, 1, function()
        self.DisposeMoveNextAction()
        callback()
    end)
end

function M.RefreshPlayerMove(callBack)
    local lastId = mc.playerEventId
    mc.playerEventId = self.GetCurrentEventPoint()
    mc.isMoveActionIng = true
    --播放动画
    mc.PlayerComp:MoveNextEvent(lastId, mc.playerEventId, 1, function()
        self.DisposeMoveNextAction()
        callBack()
    end)
end

function M.StartNoviceMove(callback)
    self.CreatePlayerModel(mc._StartPointEventId)
    local curEventId = mc._StartPointEventId
    mc.isMoveActionIng = true
    local curCompleteEventId = self.GetCurrentEventPoint()
    --模型开始移动到下一个位置
    mc.playerEventId = curCompleteEventId
    local overCallBack = function()
        self.DisposeMoveNextAction()
        callback()
    end
    --播放动画
    if mc.PlayerComp then
        local time = 1.5
        mc.PlayerComp:MoveNextEvent(curEventId, curCompleteEventId, time, overCallBack, true)
    end
end

---@public 释放下一关动画
function M.DisposeMoveNextAction()
    mc.isMoveActionIng = false
    mc._MoveNextCompleteAction = true
    GWG.GWAdmin.HomeSceneUtil.SetCameraOperate(true)
end

function M.GetMoveActionIng()
    return mc.isMoveActionIng
end

---@public 获取事件实体
---@param eventId number 事件id
function M.GetEventEntity(eventId)
    if not self.GetNoviceHomeOpen() then
        return nil
    end
    local eventComp = mc._EventEntityData[eventId]
    if not eventComp then
        return nil
    end
    return eventComp
end

---@public 刷新事件实体
function M.RefreshEventData(eventId, isHideEffect)
    local comp = self.GetEventEntity(eventId)
    if comp then
        comp:RefreshData(isHideEffect)
    end
end

--endregion
--region 服务器 and 本地事件通知
---@public 登录事件通知
function M.OnLoginEvent(data)
    local taskData = prop_pb.TSubjectPart()
    taskData:ParseFromString(data)
    if taskData.topicData then
        for i, value in ipairs(taskData.topicData) do
            if value.topicID == topic_pb.TOPICNAME_NEWPLAYERACT_DATA then
                self.UpdateTopicData(value.data[4], true)
            end
        end
    end
end

---@public Topic事件通知
function M.OnTopicEvent(data)
    if data.topicName == topic_pb.TOPICNAME_NEWPLAYERACT_DATA and data.topicKey == topic_pb.ETopicKey_NewPlayerAct_Data_Event1 then
        self.UpdateTopicData(data.value, false)
    end
end

---@public 关卡战斗开始
function M.BattleStart()
    log.Warning("novice BattleStart")
    if self.CheckPassLevel() then
        return
    end
    gw_home_novice_util.LogWarning("novice BattleStart1")
    local oneGamePass = gw_home_novice_util.GetNoviceOneMiniGamePass()
    mc._FirstGameEnter = not oneGamePass
    mc._StartBattleEventId = self.GetCurrentEventPoint()
    local force_guide_system = require "force_guide_system"
    force_guide_system.TriComEvent(force_guide_event.cEventNoviceEvent .. tostring(mc._StartBattleEventId))
    force_guide_system.TriEnterEvent("tEventNoviceEventAttack" .. tostring(mc._StartBattleEventId))
    --GWG.GWMgr.HideCurScene()
end

---@public 关卡战斗结束
function M.OnBattleResultClose(eventName, levelId)
    if mc._FirstGameEnter then
        ui_window_mgr:UnloadModule("ui_puzzle_game_level")
        novice_guide_home_func.StartHomeEvent1()
    end
    if mc._StartBattleEventId >= 2 then
        local force_guide_system = require "force_guide_system"
        force_guide_system.TriEnterEvent("tEventNoviceEventBattleOver" .. tostring(mc._StartBattleEventId))
        local guideId = force_guide_system.GetCurStep()
        if guideId == -1 or guideId == 852 or guideId == 853 or guideId == 854 or guideId == 855
                or guideId == 844 or guideId == 845 or guideId == 846 or guideId == 847 then
            force_guide_system.TriComEvent(force_guide_event.cEventNoviceEventBattleOver .. tostring(mc._StartBattleEventId))
            force_guide_system.Over()
            force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
        end
    end
    log.Warning("Novice_event UpdateCheckEventComplete  mc._StartBattleEventId", mc._STransformInspectortartBattleEventId, "mc.currentEvent = ", self.GetCurrentEventPoint())
    if mc._StartBattleEventId == gw_home_novice_const.NoviceHomeEventEndId then
        --判断引导
        local gw_home_novice_chapter_data = require "gw_home_novice_chapter_data"
        if gw_home_novice_chapter_data:CheckPassLevel() then
            local force_guide_system=require"force_guide_system"
            force_guide_system.TriEnterEvent(force_guide_event.tEventBomberBegin)
        end
    end
    if mc._StartBattleEventId ~= self.GetCurrentEventPoint() then
        self:RefreshUpdateCheckEvent()
        gw_ed.mgr:Trigger(gw_ed.GW_HOME_EVENT_UPDATE, mc.currentEvent)
        event.Trigger(gw_ed.GW_HOME_EVENT_UPDATE, mc.currentEvent)
        self.EventRefreshBubbleShow(mc._StartBattleEventId)
    end
end

function M.OnBattleResultBack()
    self.CreatePlayerModel(nil, true)
    if not mc.currentEvent then
        return
    end
    if mc.currentEvent == gw_home_novice_const.NoviceHomeEventMaxEndId then
        return
    end
    local force_guide_system = require "force_guide_system"
    force_guide_system.Over()
    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterNewHunt)
end

function M.GetStartMapCityId()
    return self.GetMainCityMapId(mc._StartPointEventId)
end

---@public 更新事件下一关的数据id
function M.UpdateTopicData(data, isLogin)
    if (mc.currentEvent ~= nil and mc.currentEvent ~= gw_home_novice_const.NoviceHomeEventEndId and mc.currentEvent ~= gw_home_novice_const.NoviceHomeEventMaxEndId) and data == gw_home_novice_const.NoviceHomeEventMaxEndId then
        --特殊处理 灵石跳过新手流程
        self.RefreshOldEventEntity(data)
        self.DisposePlayer()
        local force_guide_system = require "force_guide_system"
        force_guide_system.Over()

        local player_prefs = require "player_prefs"
        player_prefs.SetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 1)
    end
    mc.currentEvent = data
    if not self.GetNoviceHomeOpen() then
        mc.currentEvent = gw_home_novice_const.NoviceHomeEventMaxEndId
    end
    if isLogin then
        gw_ed.mgr:Trigger(gw_ed.GW_HOME_EVENT_UPDATE, mc.currentEvent)
        event.Trigger(gw_ed.GW_HOME_EVENT_UPDATE, mc.currentEvent)
        self.EventRefreshBubbleShow(mc.currentEvent - 1)
    end
    self.ReportEventEnd(data)
end

function M.ReportEventEnd(data)
    if mc._LastEventId == nil or mc._LastEventId == 0 then
        return
    end
    event.EventReport("NewbieEvent_Done", { EventID = mc._LastEventId })
end

function M.EventRefreshBubbleShow(eventId)
    --[[    local cfg = game_scheme:BuildPreProcess_0(eventId)
        if not cfg then
            GWG.GWAdmin.HomeSceneUtil.SetCameraCanZoom(true)
        else
            GWG.GWAdmin.HomeSceneUtil.SetCameraCanZoom(false)
        end]]
end

--endregion

---@public 检测是否通关
function M.CheckPassLevel()
    local nextEventId = self.GetCurrentEventPoint()
    local isPass = nextEventId == gw_home_novice_const.NoviceHomeEventMaxEndId
    return isPass
end

function M.NoviceTrigger6(isSingleCreateBubble)
    if mc.PlayerComp then
        mc.PlayerComp:NoviceTriggerRefresh(isSingleCreateBubble)
    end
end

-- - - - - - - - - - - - - - - - - - - - - 分割线 - - - - - - - - - - - - - - - - - - - -

function M.OnReconnectSuccessEvent()
    self.DisposeMoveNextAction()
end

function M.DisposePlayer()
    if mc.PlayerComp then
        GWG.GWAdmin.PushBSComponent(mc.PlayerComp)
        mc.PlayerComp = nil
    end
end

--- @public 释放实体
function M.DisposeEntity()
    --清理事件实体 实体已经被总管理器清楚完毕了
    mc._InitRegister = false
    mc._EventEntityData = {}         --事件实体数据{事件id = {事件实体} }
    event.Unregister(event.NEW_BATTLE_START, M.BattleStart)
    event.Unregister(event.BATTLE_RESULT_CLOSE, M.OnBattleResultClose)
    event.Unregister(event.NEW_BATTLE_BACK, M.OnBattleResultBack)
    event.Unregister(event.NEW_XYX_START_LEVEL, M.BattleStart)
    event.Unregister(event.NEW_XYX_END_LEVEL, M.OnBattleResultClose)
    self.DisposeMoveNextAction()
    self.DisposePlayer()
end

--- @public 数据清理
function M.Dispose()
    self.firstGame = false
    self.xyxBuildSuccess = false
    self.DisposeEntity()
    _d.mde:clear()
    mn = _d.mde.net
    mc = _d.mde.const
end

return M
