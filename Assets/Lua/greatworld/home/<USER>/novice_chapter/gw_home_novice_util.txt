---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by ha<PERSON><PERSON><PERSON>.
--- DateTime: 2024/12/6 16:14
--- Desc: 新手引导工具类
local require = require
local ipairs = ipairs
local files_version_mgr = require "files_version_mgr"
local player_mgr = require "player_mgr"
local game_scheme = require "game_scheme"
local val = require "val"
local gw_home_novice_const = require "gw_home_novice_const"
local event = require "event"
local logger = require("logger").new("sw_novice_print", 1)
module("gw_home_novice_util")

function GetIsNovice()
    local value = not val.IsTrue("sw_novice_chapter", 0) --or val.IsTrue("sw_open_guide", 0)
    local player_prefs = require "player_prefs"
    local jumpValue = player_prefs.GetCacheData(gw_home_novice_const.Novice_Jump_Base_Key, 0)
    if files_version_mgr.GetAutoMaticZoneSwitching() then
        return false
    end
    return value and (jumpValue == 0)
end

---@public 恢复相机缩放
function RecoverCameraZoom(time, callBack)
    local GWG = require "gw_g"
    local initBattlePropCft = game_scheme:InitBattleProp_0(8213)
    local default = 1050
    if initBattlePropCft then
        default = initBattlePropCft.szParam[1][0]
    end
    time = time or -999 --特殊设置
    GWG.GWAdmin.HomeSceneUtil.SetCameraDxf(default, time, callBack)
end

---@public 检查英雄选择引导 是否开启
function CheckHeroSelectGuide()
    local id = gw_home_novice_const.Novice_Hero_Select
    local cfg = game_scheme:InitBattleProp_0(id)
    if cfg and cfg.szParam.data then
        local level = cfg.szParam.data[0]
        return player_mgr.GetPlayerLV() < level
    end
    return false
end

---@public 是否在引导期间
---@return boolean 是否在引导期间(事件106之前都在引导期间)
function IsGuideIng()
    local force_guide_system = require "force_guide_system"
    local state = force_guide_system.GetState()
    if state == 2 then
        return true
    end
    local id = gw_home_novice_const.Novice_Home_Normal_EventId
    local gw_home_chapter_data = require "gw_home_chapter_data"
    local isNovice = GetIsNovice()
    return (not gw_home_chapter_data.CheckPassEvent(id) and isNovice)
end

---@public 获取第一关关卡ID
---@return number
function GetNoviceOneMiniGameLevel()
    local buildPreProCfg = game_scheme:BuildPreProcess_0(1)
    if buildPreProCfg and buildPreProCfg.SpecialParam1 then
        local param1 = buildPreProCfg.SpecialParam1
        if param1.count >= 2 then
            --进入士兵突击
            return  buildPreProCfg.SpecialParam1.data[1]
        end
    end
    return 1
end
---@public 进入第一关配置小游戏
function StartOneMiniGame()
    local buildPreProCfg = game_scheme:BuildPreProcess_0(1)
    if buildPreProCfg and buildPreProCfg.SpecialParam1 then
        local param1 = buildPreProCfg.SpecialParam1
        if param1.count >= 2 then
            --进入士兵突击
            local levelId = buildPreProCfg.SpecialParam1.data[0]
            local const = require "const"
            if const.OPEN_NEW_HOOK_SCENE then
                local new_hook_scene = require("new_hook_scene")
                new_hook_scene.OnFightClicked(levelId)
            else
                local laymain_top_scene = require("laymain_top_scene")
                laymain_top_scene.OnFightClicked(levelId)
            end
            --打点 特殊进入游戏打点 guideId = 1
            event.EventReport("Guide_trigger",{GuideID = 1,Guide_type = 1})
            return
        end
    end
end
---@public 获取第一关小游戏是否通关
function GetNoviceOneMiniGamePass()
    local buildPreProCfg = game_scheme:BuildPreProcess_0(1)
    if buildPreProCfg and buildPreProCfg.SpecialParam1 then
        local param1 = buildPreProCfg.SpecialParam1
        if param1.count >= 2 then
            --返回士兵突击
            local laymain_data = require "laymain_data"
            local passLevel = laymain_data.GetPassLevel()
            return passLevel >= buildPreProCfg.SpecialParam1.data[1]
        end
    end
end

IsLogLevel = logger.IsLogLevel
Warning = logger.Warning
Warning0 = logger.Warning0

function LogWarning(...)
    --logger.WarningWithoutLayerFun(...)
    Warning(2, ...)
end

function LogError(...)
    Warning0(1, ...)
end