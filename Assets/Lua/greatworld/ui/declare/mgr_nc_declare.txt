---
--- Created by: yuan<PERSON>.
--- DateTime: 2024/12/11.
--- Desc: 中立城池宣战管理器
---
local require = require
local string = string
local tostring = tostring
local table = table
local AllianceMgr = AllianceMgr
local os = os

local ui_window_mgr = require "ui_window_mgr"
local alliance_data = require "alliance_data"
local lang = require "lang"
local table_util = require "table_util"
local cfg_util = require "cfg_util"
local game_scheme = require "game_scheme"
local val = require "val"

module("mgr_nc_declare")

-- 规则风格 1：满足,2：不满足
local ruleLabelStyle = {
    [1] = { color = "#98D1AE", icon = "yes" },
    [2] = { color = "#F09F98", icon = "no" }
}

-- 宣战的城池规则，默认显示UI最上方，只显示为满足的一条
local cityConditions = {
    -- 占领上一级城池
    [1] = {
        check = function(data, regionCfg)
            local maxLevel = AllianceMgr.GetNCOccupyMaxLevel()
            if maxLevel and (maxLevel + 1) >= data.cfg.cityLevel then
                return true
            end
            return false
        end,
        getData = function(state, data, regionCfg)
            local needLevel = data.cfg.cityLevel - 1
            local langTxt = string.format2(lang.Get(667042), needLevel)
            local style = ruleLabelStyle[1]
            local list = nil
            if not state then
                style = ruleLabelStyle[2]
                list = {}
                local nums = game_scheme:SandMapRegion_nums()
                for i = 0, nums - 1 do
                    local cfg = game_scheme:SandMapRegion(i)
                    local cityCfg = game_scheme:SandMapCity_0(cfg.cityID)
                    if cityCfg and cityCfg.cityLevel == needLevel then
                        table.insert(list, cfg.regionID)
                    end
                end
                return { langTxt = langTxt, style = style, list = list }
            end
        end },
    -- 占领相邻城池
    [2] = {
        check = function(data, regionCfg)
            -- 是否占领周边的城池
            local linkScopes = cfg_util.ArrayToLuaArray(regionCfg.linkScope)
            local occupyList = alliance_data.GetAllianceOccupyNCRegionList()
            local occupyRegionList = {}
            for i = 1, #occupyList do
                table.insert(occupyRegionList, occupyList[i].regionID)
            end

            if #occupyList == 0 or table_util.ContainsDuplicates(linkScopes, occupyRegionList) then
                return true
            end
            return false
        end,
        getData = function(state, data, regionCfg)
            local langTxt = state and lang.Get(667040) or lang.Get(667041)
            local style = ruleLabelStyle[1]
            local list = nil
            if not state then
                style = ruleLabelStyle[2]
                list = cfg_util.ArrayToLuaArray(regionCfg.linkScope)
            end
            return { langTxt = langTxt, style = style, list = list }
        end },
}

-- 宣战的联盟条件
local allianceConditions = {
    -- 联盟已占领城池
    [1] = {
        check = function(data, regionCfg)
            local occupyList = AllianceMgr.GetNCOccupyList()
            local occupyMaxCount = AllianceMgr.GetNCOccupyMaxCount()
            return #occupyList < occupyMaxCount
        end,
        getData = function(state, data, regionCfg)
            local occupyList = AllianceMgr.GetNCOccupyList()
            local occupyMaxCount = AllianceMgr.GetNCOccupyMaxCount()
            local langTxt = string.format("%s%d/%d", lang.Get(667022), #occupyList, occupyMaxCount)
            local style = state and ruleLabelStyle[1] or ruleLabelStyle[2]
            return { langTxt = langTxt, style = style }
        end },
    -- 今日宣战次数
    [2] = {
        check = function(data, regionCfg)
            local declareCount = AllianceMgr.GetDeclareCount()
            local declareMaxCount = AllianceMgr.GetDeclareMaxCount()
            return declareCount < declareMaxCount
        end,
        getData = function(state, data, regionCfg)
            if state then
                return { langTxt = lang.Get(667036), style = ruleLabelStyle[1] }
            else
                return { langTxt = lang.Get(667037), style = ruleLabelStyle[2] }
            end
        end
    },
    --联盟成立时间
    [3] = {
        check = function(data, regionCfg)
            local createTime = AllianceMgr.GetAllianceCreateTime()
            local hours = (os.server_time() - createTime) / 3600
            local dataTable = cfg_util.ArrayToLuaArray(data.cfg.warconditions)
            local limitHour = dataTable[1]
            return hours >= limitHour
        end,
        getData = function(state, data, regionCfg)
            local dataTable = cfg_util.ArrayToLuaArray(data.cfg.warconditions)
            local limitHourStr = string.format(lang.Get(91), tostring(dataTable[1]))
            if state then
                return { langTxt = string.format2(lang.Get(667038), limitHourStr), style = ruleLabelStyle[1] }
            else
                return { langTxt = string.format2(lang.Get(667039), limitHourStr), style = ruleLabelStyle[2] }
            end
        end },
    --联盟成员数量
    [4] = {
        check = function(data, regionCfg)
            local userdata = alliance_data.GetUserAllianceData()
            local dataTable = cfg_util.ArrayToLuaArray(data.cfg.warconditions)
            local limitCount = dataTable[2]
            return userdata.count >= limitCount
        end,
        getData = function(state, data, regionCfg)
            local userdata = alliance_data.GetUserAllianceData()
            local dataTable = cfg_util.ArrayToLuaArray(data.cfg.warconditions)
            if state then
                return { langTxt = string.format2(lang.Get(667043), userdata.count, dataTable[2]), style = ruleLabelStyle[1] }
            else
                return { langTxt = string.format2(lang.Get(667044), userdata.count, dataTable[2]), style = ruleLabelStyle[2] }
            end
        end
    },
}

local function checkCityCondition(data, regionCfg)
    local cityState = true
    local cityConditionState = {}
    for i = 1, #cityConditions do
        local state = cityConditions[i].check(data, regionCfg)
        if cityState and not state then
            cityState = false
        end
        cityConditionState[i] = state
    end
    return cityState, cityConditionState
end

local function checkAllianceCondition(data, regionCfg)
    local allianceState = true
    local allianceConditionState = {}
    for i = 1, #allianceConditions do
        local state = allianceConditions[i].check(data, regionCfg)
        if allianceState and not state then
            allianceState = false
        end
        allianceConditionState[i] = state
    end
    return allianceState, allianceConditionState
end

local function GetConditionViewData(data, regionCfg, cityConditionState, allianceConditionState)
    local cityData = nil
    for i = 1, #cityConditionState do
        if not cityConditionState[i] then
            cityData = cityConditions[i].getData(cityConditionState[i], data, regionCfg)
            break
        end
    end
    if cityData == nil then
        cityData = cityConditions[#cityConditions].getData(true, data, regionCfg)
    end

    local allianceData = { }
    for i = 1, #allianceConditions do
        table.insert(allianceData, allianceConditions[i].getData(allianceConditionState[i], data, regionCfg))
    end
    return { cityData = cityData, allianceData = allianceData }
end

function OpenSandDeclareView(data)
    local cityPos = string.format("%s#%s", tostring(data.pos.x), tostring(data.pos.y))
    local regionCfg = game_scheme:SandMapRegion_1(cityPos)
    if regionCfg then
        if val.IsTrue("sw_skip_declare_check", 1) then
            ui_window_mgr:ShowModule("ui_nc_declare", nil, nil, data)
            return
        end
        
        local cityState, cityConditionState = checkCityCondition(data, regionCfg)
        local allianceState, allianceConditionState = checkAllianceCondition(data, regionCfg)
        if cityState and allianceState then
            ui_window_mgr:ShowModule("ui_nc_declare", nil, nil, data)
        else
            local viewData = GetConditionViewData(data, regionCfg, cityConditionState, allianceConditionState)
            ui_window_mgr:ShowModule("ui_nc_declare_fail", nil, nil, viewData)
        end
    end
end