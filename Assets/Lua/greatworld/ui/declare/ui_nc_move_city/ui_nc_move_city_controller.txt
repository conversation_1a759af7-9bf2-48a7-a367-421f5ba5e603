local require = require
local pairs = pairs
local AllianceMgr = AllianceMgr
local newClass = newclass
local type = type

local gw_sand_event_report_helper = require "gw_sand_event_report_helper"
local event = require "event"
local ui_util = require "ui_util"
local net_sandbox_module = require "net_sandbox_module"
local log = require "log"
local game_scheme = require "game_scheme"
local alliance_mgr_extend = require "alliance_mgr_extend"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"

--region Controller Life
module("ui_nc_move_city_controller")
local controller = nil
local UIController = newClass("ui_nc_move_city_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = {}
    if data and data.infoList then
        -- 迁城默认取第一个城池的数据
        self.CData.info = data.infoList[1]
        self:InitMoveCityData()
    else
        self:OnBtnCloseBtnClickedProxy()
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
    self.onGoodsUpdate = function(eventName, id, sid, num)
        if id and id == self.CData.goodsId then
            self:InitMoveCityData()
        end
    end
    self:RegisterEvent(event.UPDATE_GOODS_NUM_CHANGE, self.onGoodsUpdate)
end

function UIController:AutoUnsubscribeEvents()
    self.onGoodsUpdate = nil
end
--endregion

--region Controller Logic
function UIController:InitMoveCityData()
    if not self.CData.info then
        return
    end

    local cityCfg = game_scheme:SandMapCity_0(self.CData.info.nCityID)
    if cityCfg then
        self:TriggerUIEvent("SetFirstReward", self.CData.info.nFirst, cityCfg.FirstReward)
        self:TriggerUIEvent("SetContent", cityCfg.cityLevel, cityCfg.name)

        self.CData.freeMoveCity = alliance_mgr_extend.GetFreeMoveTimes() > 0
        if self.CData.freeMoveCity then
            self:TriggerUIEvent("SetButtonState", true)
        else
            local constCfg = game_scheme:GWMapConstant_0(23)
            if not constCfg then
                log.Error("[ui_nc_move_city_controller] not found GWMapConstant Cfg, id = 23")
                return
            end

            self.CData.goodsId = constCfg.szParam.data[0]
            local player_mgr = require "player_mgr"
            self.CData.goodsNum = player_mgr.GetPlayerOwnNum(self.CData.goodsId)
            self:TriggerUIEvent("SetButtonState", false, self.CData.goodsId, self.CData.goodsNum)
        end
    else
        log.Error("[ui_nc_move_city_controller] not found cityCfg " .. self.CData.info.nCityID)
    end
end

function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end

function UIController:OnBtnCancelClickedProxy()
    self:OnBtnCloseBtnClickedProxy()
end

function UIController:OnBtnMoveCityClickedProxy()
    -- 打开购买界面
    if not self.CData.freeMoveCity and self.CData.goodsNum <= 0 then
        ui_util.ShowSupply(self.CData.goodsId, 1)
        return
    end

    if self.CData.freeMoveCity then
        local data = {
            allianceId = AllianceMgr.GetUserAllianceId(),
            type = 1
        }
        net_sandbox_module.MSG_NEWPALYER_FREEMOVE_REQ(data)
    else
        local useData = {
            goodsId = self.CData.goodsId,
            count = 1,
            rewardId = nil
        }
        local evt_item_define = require "evt_item_define"
        event.Trigger(evt_item_define.Evt_UseItemOne, useData)
    end
    -- 点击迁城提示打点
    gw_sand_event_report_helper.RelocationSandBoxCity()
    self:OnBtnCloseBtnClickedProxy()
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
