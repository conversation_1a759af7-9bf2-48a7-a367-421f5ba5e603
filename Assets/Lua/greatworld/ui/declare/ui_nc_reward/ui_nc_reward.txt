local require = require
local pairs = pairs
local typeof = typeof
local string = string
local CanvasType = typeof(CS.UnityEngine.Canvas)
local SortingGroupType = typeof(CS.UnityEngine.Rendering.SortingGroup)
local type = type
local UIUtil = CS.Common_Util.UIUtil

local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local class = require "class"
local binding = require "ui_nc_reward_binding"

--region View Life
module("ui_nc_reward")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)

    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
end

function UIView:OnHide()
    self.__base:OnHide()
end

function UIView:Close()
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.VData = nil
    self.__base.Close(self)
    window = nil
end

function UIView:UpdateUI()
    local ui_window_mgr = require "ui_window_mgr"
    local mainSlg = ui_window_mgr:GetWindowObj("ui_main_slg")
    if mainSlg then
        local curOrder = mainSlg.curOrder > 10000 and mainSlg.curOrder or 15000

        local canvas = self.UIRoot.transform:GetComponent(CanvasType)
        if not canvas:IsNull() then
            canvas.sortingOrder = curOrder + 50
        end

        local sortingGroup = self.UIRoot.transform:GetComponent(SortingGroupType)
        if not sortingGroup:IsNull() then
            sortingGroup.sortingOrder = curOrder + 51
        end
    end
end
--endregion

--region View Logic
function UIView:SetCityInfo(cityLevel, cityName, uiIcon)
    self:CreateSubSprite("CreateGWSandmapAsset", self.img_city, uiIcon)
    self.txt_city.text = string.format("Lv.%d%s", cityLevel, lang.Get(cityName))
end

function UIView:SetViewParent(parent)
    if parent then
        UIUtil.SyncTransformPositionAndRotation(parent, self.rtf_entity)
    end
end

function UIView:SetViewActive(enable)
    if enable then
        self:UpdateUI()
    end
    UIUtil.SetActive(self.rtf_entity, enable)
end
--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
            window:LoadUIResource(tempPath, nil, tempParent, nil)
        else
            window:LoadUIResource(ui_path, nil, nil, nil)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
