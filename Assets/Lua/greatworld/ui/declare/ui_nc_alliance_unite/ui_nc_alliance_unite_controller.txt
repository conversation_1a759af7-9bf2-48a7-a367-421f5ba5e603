local require = require
local pairs = pairs
local math = math
local newClass = newclass
local type = type

local net_sandbox_module = require "net_sandbox_module"
local sand_ui_data = require "sand_ui_data"
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local event = require "event"

--region Controller Life
module("ui_nc_alliance_unite_controller")
local controller = nil
local UIController = newClass("ui_nc_alliance_unite_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self, view_name, controller_name)
    self.CData = { sid = data.nSID, regionID = data.regionID }
    if not data or data == 0 then
        self:OnBtnCloseBtnClickedProxy()
        return
    end
    self:InitEntityData()
end

function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close(data)
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    self.CData = nil

    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:AutoSubscribeEvents()
end

function UIController:AutoUnsubscribeEvents()
end
--endregion

--region Controller Logic
function UIController:InitEntityData()
    self.entity = sand_ui_data.GetEntityBySid(self.CData.sid)
    if self.entity then
        self:TriggerUIEvent("SetContent", self.entity.cfg.cityLevel, self.entity.cfg.name)
    end
end

function UIController:OnBtnCloseBtnClickedProxy()
    ui_window_mgr:UnloadModule(self.view_name)
end

function UIController:OnBtnCancelClickedProxy()
    self:OnBtnCloseBtnClickedProxy()
end

function UIController:OnBtnConfirmClickedProxy()
    if self.entity then
        net_sandbox_module.MSG_SANDBOX_NC_GETREGIOINPOS_REQ({ regionID = self.CData.regionID })
        self:OnBtnCloseBtnClickedProxy()
        --触发设置同盟聚集点提示 打点
        event.EventReport("SandMapCity_Set_Gathering", {})
    end
end

function UIController:RandomPosValue(startV, endV)
    local v = math.random(startV, endV)
    local syb = math.random(0, 1) == 1 and 1 or -1
    return v * syb
end
--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
