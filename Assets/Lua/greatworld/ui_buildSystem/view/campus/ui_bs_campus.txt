--region FileHead
--- ui_bs_campus.txt
-- author:  author
-- ver:     1.0
-- desc:    
-------------------------------------------------
--endregion 

--region Require
local require = require
local type = type
local pairs = pairs
local typeof = typeof
local table = table
local math = math
local GWG = GWG
local GWAdmin = GWAdmin
local GWConst = GWConst
local flow_text = require "flow_text"
local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem = CS.UI.UGUIExtend.ScrollRectItem
local enum_define = require "enum_define"
local class = require "class"
local lang = require "lang"
local ui_base = require "ui_base"
local module_scroll_list = require "scroll_list"
local Common_Util = CS.Common_Util.UIUtil
local e_handler_mgr = require "e_handler_mgr"
local game_scheme = require "game_scheme"
local card_assets = require "card_sprite_asset"
local soldiers_item = require "new_soldiers_item"
local RectTransform = CS.UnityEngine.RectTransform
local windowMgr = require "ui_window_mgr"
local gw_home_building_data = require "gw_home_building_data"
local RectTransformUtility  = CS.UnityEngine.RectTransformUtility
local Camera 				= CS.UnityEngine.Camera
local technology_data = require "technology_data"
--endregion 

--region ModuleDeclare
module("ui_bs_campus")
local ui_path = "ui/prefabs/gw/buildsystem/uibscampus.prefab"
local window = nil
local UIView = {}
local spriteAsset = nil;
--endregion 

--region WidgetTable
UIView.widget_table = {
    Title = { path = "Panel/TopBar/Title", type = "Text" },
    Tip = { path = "Attribute", type = "RectTransform" },
    Point = { path = "Point", type = "RectTransform" },
    CloseBtn = { path = "closeBtn", type = "Button", event_name = "OnBtnClose" },
    TipsParent = {path = "",type = "RectTransform"},
    
    Slider = { path = "Panel/Content/Capacity/Slider", type = "Slider" },
    SliderTex = { path = "Panel/Content/Capacity/Info", type = "Text" },
    SliderNum = { path = "Panel/Content/Capacity/Number", type = "Text" },
    SliderImg = { path = "Panel/Content/Capacity/Image", type = "Image" },

    InLock = { path = "Panel/Content/ScrollViewInside/Text", type = "Text" },
    Inline = { path = "Panel/Content/ScrollViewInside/InsideTex/Text", type = "Text" },
    OutLock = { path = "Panel/Content/ScrollViewOutside/Text", type = "Text" },
    Outline = { path = "Panel/Content/ScrollViewOutside/OutsideTex/Text", type = "Text" },
    
    rect_table = { path = "Attribute/content", type = ScrollRectTable },
    DefaultListItem = { path = "DefaultListItem", type = ScrollRectItem },
    
    InsideContentTran = { path = "Panel/Content/ScrollViewInside/Content", type = "RectTransform" },
    InsideContentGrid = { path = "Panel/Content/ScrollViewInside/Content", type = "GridLayoutGroup" },
    OutsideContentTran = { path = "Panel/Content/ScrollViewOutside/Content", type = "RectTransform" },

    TipsMask = {path = "TipsMask",type = "Button",event_name = "OnHideTips"},
}
--endregion

--region function 设置View-Controller模式的UI
-- return type  ---- 未定义/VC/纯V   
-- 注意，View-Controller模式的ui必须要重写这个接口
function UIView:SetVCTypeUI()
    return self.__base:SetVCTypeUI(enum_define.enum_ui_vc_type.vc)
end
--endregion 

--region WindowInit
--[[窗口初始化]]
function UIView:Init()
    self:SetVCTypeUI()
    self.__base:Init(self)
    self:SubscribeEvents()
    spriteAsset = spriteAsset or card_assets.CreateSpriteAsset()
    self:InitScrollRectTable()
    self.uiCamera = GameObject.Find("UIRoot/UICamera"):GetComponent(typeof(Camera))
    --考虑到城内城外的士兵id可能会有相同的，使用同一个ID扔同一个数组可能会造成部分无法释放
    self.insideProfile = {} --城内士兵头像列表
    self.outsideProfile = {} --城外士兵头像列表
    --region User
    --endregion 
end --///<<< function

--endregion 


--region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIView:OnShow()
    self.__base:OnShow()
end --///<<< function

--endregion 

--region WindowOnHide
--[[界面隐藏时调用]]
function UIView:OnHide()
    self.__base:OnHide()    
end --///<<< function

--endregion 


--region WindowClose
function UIView:Close()
    self.__base:Close()
    self:UnsubscribeEvents() 
    window = nil
    if spriteAsset then
        spriteAsset:Dispose()
        spriteAsset = nil
    end
    if self.insideProfile then
        for i,v in pairs(self.insideProfile) do
            v:Dispose()
            v = nil
        end
    end

    self.insideProfile = {}
    if self.outsideProfile then
        for i,v in pairs(self.outsideProfile) do
            v:Dispose()
            v = nil
        end
    end

    self.outsideProfile = {}
    --region User
    --endregion 
end --///<<< function
--endregion 
--region 事件注册
function UIView:SubscribeEvents()    
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了    
end
function UIView:UnsubscribeEvents()    
    
end

--endregion

--region 功能函数区
---********************功能函数区**********---
local maxCampusLv = 0 --最高兵营等级
local AttributeLimit = game_scheme:Soldier_0(320)
local AttributeLang = {
    [1] = {
        langId = 602009,
        type = "power",
        rate = 1
    },
    [2] = {
        langId = 602034,
        type = "morale",
        rate = 0.0001
    },
    [3] = {
        langId = 601261,
        type = "weights",
        rate = 1
    },
    [4] = {
        langId = 603005,
        type = "hp",
        rate = 0.0001
    },
    [5] = {
        langId = 603006,
        type = "atk",
        rate = 0.0001
    },
    [6] = {
        langId = 603007,
        type = "def",
        rate = 0.0001
    }
}

function UIView:InitScrollRectTable()
    --如果需要可以设置动画
    --local scrollAnim = require "scroll_rect_anim"
    --scrollAnim.SetAnimCfg(self.UIModuleName, self.rect_table, OnItemRender, nil, 0.1, 0.2, true, 0, true, 3, true, 0.9)
    --self.rect_table.onItemRender = scrollAnim.OnRenderItem
    self.rect_table.onItemRender = OnItemRender
    --一定在UI 销毁时处理rect_table的Dispose  eg： self.rect_table:ItemsDispose()
    self.rect_table.onItemDispose = function(scroll_rect_item, index)
       
    end
end

function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    --开始查找item 内部的属性 并进行相应的设置
    local CfgData = game_scheme:Soldier_0(dataItem) --dataItem 为 soldierId
    scroll_rect_item:Get("Text").text = lang.Get(AttributeLang[index].langId)
    scroll_rect_item:Get("Count").text = math.ceil(CfgData[AttributeLang[index].type] * AttributeLang[index].rate * 10 ) / 10
    local slider = scroll_rect_item:Get("Slider")
    local switch = scroll_rect_item:Get("switch")
    switch:Switch(index-1)
    slider.maxValue = AttributeLimit[AttributeLang[index].type]
    slider.value = CfgData[AttributeLang[index].type]
end

-- 刷新 基地内外 士兵列表
function UIView:OnSoldierItemRender(Item, index, dataItem,isInside)
    if not dataItem and Item then
        return
    end
    Common_Util.SetActive(Item,true)
    Item:Get("number").text = dataItem.soldierNum
    local promoteBtn = Item:Get("promoteBtn")
    local soldierCfg = game_scheme:Soldier_0(dataItem.soldierId)
    local isStateArrow = false
    if isInside then
        local nextSoldierCfg = game_scheme:Soldier_0(dataItem.soldierId + 1)
        if nextSoldierCfg ~= nil then
            local techDataExists = nextSoldierCfg.unlock[1][2] ~= nil
            local technologyLevel = techDataExists and technology_data.GetScientificLevelByIndex(nextSoldierCfg.unlock[1][2]) or nil
            local techLevelCheck = techDataExists and (technologyLevel >= nextSoldierCfg.unlock[1][3])
            isStateArrow = maxCampusLv >= nextSoldierCfg.unlock[1][1] and ((techDataExists and techLevelCheck) or not techDataExists) and dataItem.soldierNum > 0--箭头状态，即是否允许晋级
        end
        Common_Util.SetActive(promoteBtn,isStateArrow)
        self:AddOnClick(promoteBtn.onClick,function()
            arrowClickJump(soldierCfg,nextSoldierCfg)
        end)
    else
        Common_Util.SetActive(promoteBtn,false)
    end

    local parentTrans = Item:Get("parent").transform
    if isInside then
        if not self.insideProfile[index] then
            self.insideProfile[index] = soldiers_item.CSoldiersItem()
            self.insideProfile[index]:Init(parentTrans, nil, 1)
        end
        self.insideProfile[index]:SetSoldiers(soldierCfg, dataItem.soldierId, 0, false, false, false,
                function() clickCallback(index, dataItem,parentTrans ) end,
                function() arrowClickJump(soldierCfg,nextSoldierCfg) end
        )
    else
        if not self.outsideProfile[index] then
            self.outsideProfile[index] = soldiers_item.CSoldiersItem()
            self.outsideProfile[index]:Init(parentTrans, nil, 1)
        end
        self.outsideProfile[index]:SetSoldiers(soldierCfg, dataItem.soldierId, 0, false, false, false,
                function() clickCallback(index, dataItem,parentTrans ) end,
                function() arrowClickJump(soldierCfg,nextSoldierCfg) end
        )
    end
    return isStateArrow
end

function UIView:UpdateAttributeList(soldierID)
    if not soldierID then
        return
    end
    local data = {}
    local len = #AttributeLang
    self.rect_table.pageSize = len
    self.rect_table.renderPerFrames = len
    for i = 1, len do table.insert(data, soldierID) end
    self.rect_table.data = data
    self.rect_table:Refresh(-1, -1)
end

function UIView:RefreshData(data)
    local campusData = gw_home_building_data.GetBuildingDataListByBuildingID(6000)
    for _,v in pairs(campusData) do
        maxCampusLv = math.max(maxCampusLv, v.nLevel)
    end
    self.Slider.maxValue = data.capacity
    self.Slider.value = data.totalNum
    self.SliderTex.text = lang.Get(601268)
    self.SliderNum.text = data.totalNum .. "/" .. data.capacity
    self.Title.text = lang.Get(601006)
    self.Inline.text = lang.Get(601269)
    self.Outline.text = lang.Get(601270)
    self.InLock.text = lang.Get(601376)
    self.OutLock.text = lang.Get(601377)
    --spriteAsset:GetSprite(self.soldiersID, function(sp)
    --    self.SliderImg.sprite = sp
    --end)
    Common_Util.SetActive(self.InLock, #data.inSideSoldier == 0)
    Common_Util.SetActive(self.OutLock, #data.outSideSoldier == 0)
    local isStateArrow = false
    for k, v in pairs(data.inSideSoldier) do
        local rect_item = Common_Util.AddChild(self.InsideContentTran.gameObject, self.DefaultListItem.gameObject):GetComponent(typeof(ScrollRectItem))
        if self:OnSoldierItemRender(rect_item, k, v, true) then
            isStateArrow = true
        end
    end
    if isStateArrow then
        local cellSize = self.InsideContentGrid.cellSize
        cellSize.y = 210;
        self.InsideContentGrid.cellSize = cellSize;
    else
        local cellSize = self.InsideContentGrid.cellSize
        cellSize.y = 160;
        self.InsideContentGrid.cellSize = cellSize;
    end
    for k, v in pairs(data.outSideSoldier) do
        local rect_item = Common_Util.AddChild(self.OutsideContentTran.gameObject, self.DefaultListItem.gameObject):GetComponent(typeof(ScrollRectItem))
        self:OnSoldierItemRender(rect_item, k, v,false)
    end
end

function UIView:HideTips()
    Common_Util.SetActive(self.Tip, false)
    Common_Util.SetActive(self.TipsMask,false)
    Common_Util.SetActive(self.Point, false)
end

local TipIsOn = false
function ShowItemTip(k,rect)
    --if TipIsOn == false then
        Common_Util.SetActive(window.Tip, true)
        Common_Util.SetActive(window.Point, true)
        Common_Util.SetActive(window.TipsMask,true)
        TipIsOn = true

        local paddingX = 0
        local paddingY = 0
    
        local pointPaddingY = -9
        local redVec2 = RectTransformUtility.WorldToScreenPoint(window.uiCamera, rect.position)
        local bOk, localpoint = RectTransformUtility.ScreenPointToLocalPointInRectangle( window.TipsParent , redVec2, window.uiCamera)
        Common_Util.SetLocalPos(window.Tip, 0, localpoint.y + paddingY)
        Common_Util.SetLocalPos(window.Point, localpoint.x, localpoint.y + pointPaddingY)
        --if k < 6 then
        --    Common_Util.SetLocalPos(window.Point, -245 + (k-1) * 120, -105.7)
        --else
        --    Common_Util.SetLocalPos(window.Point, -245 + (k-1), -105.7 + 140)
        --end
    --else
    --    Common_Util.SetActive(window.Tip, false)
    --    TipIsOn = false
    --end
end

function clickCallback(index, dataItem, pos)
    ShowItemTip(index,pos)
    window:UpdateAttributeList(dataItem.soldierId)
end

function arrowClickJump(soldierCfg,nextSoldierCfg)
    local buildingId = nextSoldierCfg.unlock[1][0]
    local buildingLv = nextSoldierCfg.unlock[1][1]
    
    local buildingData = {}
    local buildingList = GWG.GWHomeMgr.buildingData.GetBuildingDataListByBuildingID(buildingId)
    
    table.sort( buildingList,function(a,b) return a.nLevel < b.nLevel end ) --跳转到指定的兵营是从最低等级的兵营开始检测是否满足条件
    for _,v in pairs(buildingList) do
        if v.nLevel >= buildingLv and not GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(v.uFlag, GWG.GWConst.enCityFlag.enCityFlag_MilitaryRecruit)
                and not GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(v.uFlag, GWG.GWConst.enCityFlag.enCityFlag_MilitaryUpgrade) then
            buildingData.sid = v.uSid --是空闲状态 且 兵营等级符合要求
            buildingData.fromCampus = true
            buildingData.selectSoldierIndex = soldierCfg.soldierID
            buildingData.selectSoldierLevel = soldierCfg.level
            
            GWAdmin.HomeCameraUtil.GWCameraDoMoveToGridPos(v.x, v.y, GWConst.CameraMoveTime, false, function()
                windowMgr:ShowModule("ui_bstrain_soldier", function() e_handler_mgr.TriggerHandler("ui_bstrain_soldier_controller", "OnIconUp") end, nil, buildingData)
            end)
            windowMgr:UnloadModule("ui_bs_campus")
            return
        end
    end
    flow_text.Add(lang.Get(601343))
    windowMgr:UnloadModule("ui_bs_campus")
end
---********************end功能函数区**********---
--endregion
--region WindowInherited
local CUIView = class(ui_base, nil, UIView)
--endregion 

--region static ModuleFunction 
-- 特别注意，当前并不是由controller层来驱动ui的生命流程的 
-- 当前因为需要view层 也就是ui_base来驱动ui的init  加载完成，show等流程，所以流程仍然保留，而controller层的流程逻辑受view流程影响，
-- view对应的Init/Show 加载完后，当前会通过事件同步调用controller层的Init/Show流程，controller层的流程逻辑才会执行。
--当前仍然保留了静态的Show，Close接口流程
function Show(data)
     if  data  and type(data) == "table" and data["uipath"] then
        ui_path = data["uipath"];   
    end  
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        window:LoadUIResource(ui_path, nil, nil, nil)
    end
    --这里调用window:Show()  会造成多次调用 但hide后却又需要 uiwindowmgr有问题 TODO
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close()
        window = nil
    end
end
--endregion
