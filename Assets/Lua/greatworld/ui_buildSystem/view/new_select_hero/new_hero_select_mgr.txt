--- Created by fgy.
--- DateTime: 2024/11/15 16:23
--- Des:处理各界面设置编队展示

local require = require
local sand_team_data = require "sand_team_data"
local common_pb = require "common_pb"
local table = table
local math = math
local log = require "log"
local ui_window_mgr = require "ui_window_mgr"
local net_city_module = require "net_city_module"

local flow_text = require "flow_text"
local lang  = require "lang"
local pairs = pairs
local ipairs = ipairs
local GWG = GWG
local gw_power_mgr = require "gw_power_mgr"
local intercity_trucks_data = require "intercity_trucks_data"
local battle_data = require "battle_data"
module("new_hero_select_mgr")

EnumTeamType = {
    --默认沙盘
    Default = 1,
    --城际货车
    InterCityTrucks = 2,
    --沙漠风暴
    DesertStorm = 3,
}

--外部设置data
local SetData = {
    truckId = nil,
}
--data数据就是服务器下发通用解析TSimpleTeam
local function GetSimpleTeamData(data,type)
    local teamList = {}
    if data then
        for i,v in pairs(data) do
            local hData2 = v.team;
            local temp = {};
            temp.index = i;
            if type == EnumTeamType.InterCityTrucks then
                hData2 = v
                temp.index = v.index
            end
            if hData2 ~= nil and hData2.pals ~= nil then
                --if hData2.weaponID ~= 0 or hData2.weaponID ~= nil then
                --    temp.usedWeapon = {
                --        weaponId = hData2.weaponID,
                --        stage = common_pb.Arena,
                --        --weaponId = weapon_data.GetLocalizedWeaponData(common_pb.Arena,teamID,common_pb.Champion),
                --        lineupIdx = i,
                --        arenaType1 = common_pb.Champion,
                --    }
                --end
                --temp.weaponId = hData2.weaponId;

                temp.order = hData2.order;
                temp.state = hData2.state;
                temp.isUnlocked = hData2.isUnlocked;
                temp.heroList = {};
                for j,k in ipairs(hData2.pals) do

                    local heroData = {}
                    heroData.heroID = k.heroID;
                    heroData.heroSid = k.heroSid;
                    heroData.skinPropID = k.skinID;

                    heroData.heroLevel = k.heroLevel;
                    heroData.heroPower = k.heroPower;
                    heroData.heroStar = k.heroStar;
                    heroData.remainHP = k.remainHP;

                    heroData.soldierCapacity = k.soldierCapacity;
                    heroData.soldierNum = k.soldierNum;
                    heroData.soldierValid = k.soldierValid;

                    local index = battle_data.GetIndexByRowAndCol(k.row,k.col)
                    --local col = k.col + 1;
                    --local row = k.row + 1;
                    --if row == 1 then
                    --    row = 0
                    --end
                    --local index = col + row;

                    temp.heroList[index] = heroData;
                end
            end
            teamList[i] = temp
        end
    end
    return teamList
end

local function GetDefaultHeroData()
   local lines = sand_team_data.GetAllTeamEntityList(false)--gw_home_common_data.GetAllSquadData(); --缓存用
    if lines then
       return GetSimpleTeamData(lines) or {}
    end
    return {}
end

local function GetDefaultHeroSave(teamId,TabData)
    local teamState = sand_team_data.GetTeamStateByIndex(teamId)
    if teamState == 0 then
        --服务器推送无效状态时，客户端不做拦截
        teamState = common_pb.enSandboxTeamState_Idle
    end
    if teamState ~= 0 then
        --状态值有效
        if teamState ~= common_pb.enSandboxTeamState_Idle then
            flow_text.Add(lang.Get(601396))
            return
        end
    end
    local msg = {};
    msg.lineUp = {};
    --下面是只传一个
    local selectData = TabData[teamId];
    local tempLine = {};
    --if selectData.usedWeapon and selectData.usedWeapon.weaponId then
    --    tempLine.weaponId = selectData.usedWeapon.weaponId
    --else
        tempLine.weaponId = 0
    --end
    tempLine.order = selectData.order or selectData.index;
    local homeland_mgr = require "homeland_mgr"
    local _power = gw_power_mgr.GetHeroGroupPowerById(selectData and selectData.selectedHero or {})
    local weaponPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower()
    --if not forSpaceGap and not forPlot then
    --    weaponPower = selectData.usedWeapon and homeland_mgr.GetWeaponPowerByID(selectData.usedWeapon["weaponId"]) or 0
    --end
    _power = _power + weaponPower
    tempLine.ce = _power;
    tempLine.palList = {}
    for i = 1,5 do
        if selectData.selectedHero[i-1] ~= nil then
            local row,col = battle_data.GetRowAndColByIndex(i)
            --local row = 0;
            --if i > 2 then
            --    row = 2;
            --end
            --local col = i - row;
            --if row == 0 then
            --    row = 1;
            --end

            local temp =
            {
                palId = selectData.selectedHero[i-1].heroSid or selectData.selectedHero[i-1].serverData.heroSid,
                row = row,
                col = col,
                heroId = selectData.selectedHero[i-1].heroID or selectData.selectedHero[i-1].serverData.heroCfgID,
            }
            if selectData.selectedHero[i-1].numProp then
                temp.lv = selectData.selectedHero[i-1].numProp.lv
            else
                temp.lv = selectData.selectedHero[i-1].heroLevel or selectData.selectedHero[i-1].serverData.heroLevel
            end
            table.insert(tempLine.palList,temp);
        end
    end
    msg.lineUp = tempLine;
    msg.teamIndex = teamId
    net_city_module.MSG_CITY_SAVE_TROOP_REQ(msg); --保存队伍请求
end

local function GetInterCityHeroData()
    if not intercity_trucks_data.GetTeamData() then
        local lines = sand_team_data.GetAllTeamEntityList(false)--gw_home_common_data.GetAllSquadData(); --缓存用
        if lines then
            return GetSimpleTeamData(lines)
        end
    end
    return GetSimpleTeamData(intercity_trucks_data.GetTeamData(),EnumTeamType.InterCityTrucks)
end

--获取沙漠风暴编队数据
local function GetDesertStormHeroData()
    
end

local function GetDesertStormHeroSave(teamId,TabData)
    local msg = {};
    msg.lineUp = {};
    --下面是只传一个
    local selectData = TabData[teamId];
    local tempLine = {};
    --if selectData.usedWeapon and selectData.usedWeapon.weaponId then
    --    tempLine.weaponId = selectData.usedWeapon.weaponId
    --else
        tempLine.weaponId = 0
    --end
    tempLine.order = selectData.order or selectData.index;
    local _power = gw_power_mgr.GetHeroGroupPowerById(selectData and selectData.selectedHero or {})
    local weaponPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower()
    --if not forSpaceGap and not forPlot then
    --    weaponPower = selectData.usedWeapon and homeland_mgr.GetWeaponPowerByID(selectData.usedWeapon["weaponId"]) or 0
    --end
    _power = _power + weaponPower
    tempLine.ce = _power;
    tempLine.palList = {}
    for i = 1,5 do
        if selectData.selectedHero[i-1] ~= nil  then
            local heroID = selectData.selectedHero[i-1].heroSid or selectData.selectedHero[i-1].serverData.heroSid
            local row,col = battle_data.GetRowAndColByIndex(i)
            local temp =
            {
                palId = heroID,
                row = row,
                col = col,
            }
            if heroID then
                table.insert(tempLine.palList,temp);
            end
        end
    end
    msg.lineUp = tempLine;
    msg.teamIndex = teamId
    --msg.truckid = SetData.truckId
    
    --TODO:这里加入保存的接口即可。
end

--目前真实编队下标对应的服务器（c，r）是：
--index，1 col，0 row:，0
--index，2 col，1 row:，0
--index，3 col，0 row:，1
--index，4 col，1 row:，1
--index，5 col，2 row:，1
local function GetInterCityHeroSave(teamId,TabData)
    --if intercity_trucks_data.GetTeamUsedList(teamId) then
    --    --TODO 不可编队飘窗缺少lang
    --    return
    --end
    local msg = {};
    msg.lineUp = {};
    --下面是只传一个
    local selectData = TabData[teamId];
    local tempLine = {};
    if selectData.usedWeapon and selectData.usedWeapon.weaponId then
        tempLine.weaponId = selectData.usedWeapon.weaponId
    else
        tempLine.weaponId = 0
    end
    tempLine.order = selectData.order or selectData.index;
    local homeland_mgr = require "homeland_mgr"
    local _power = gw_power_mgr.GetHeroGroupPowerById(selectData and selectData.selectedHero or {})
    local weaponPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower()
    --if not forSpaceGap and not forPlot then
    --    weaponPower = selectData.usedWeapon and homeland_mgr.GetWeaponPowerByID(selectData.usedWeapon["weaponId"]) or 0
    --end
    _power = _power + weaponPower
    tempLine.ce = _power;
    tempLine.palList = {}
    for i = 1,5 do
        if selectData.selectedHero[i-1] ~= nil  then
           local heroID = selectData.selectedHero[i-1].heroSid or selectData.selectedHero[i-1].serverData.heroSid
            local row,col = battle_data.GetRowAndColByIndex(i)
            local temp =
            {
                palId = heroID,
                row = row,
                col = col,
            }
            if heroID then
                table.insert(tempLine.palList,temp);
            end
        end
    end
    msg.lineUp = tempLine;
    msg.teamIndex = teamId
    --msg.truckid = SetData.truckId
    local net_carriage_module = require "net_carriage_module"
    net_carriage_module.MSG_CARRIAGE_SAVE_TROOP_REQ(msg); --保存队伍请求
end

local function InterCityCloseSaveFunc(teamId,TabData)
    GetInterCityHeroSave(teamId,TabData)
    local msg = {};
    msg.teamIndex = teamId
    msg.truckid = SetData.truckId
    local net_carriage_module = require "net_carriage_module"
    net_carriage_module.MSG_CARRIAGE_SET_TROOP_REQ(msg); --保存设置的小队
end

local function CheckInterCityTeamBusy(teamId)
    return intercity_trucks_data.GetTeamUsedList(teamId) ~= nil
end

local function CheckDefaultTeamBusy(teamId)
    return sand_team_data.CheckTeamIsBusy(teamId)
end

local function GetSelectHeroData()
    local selectHeroList = {}
    local allLineUpHero = GetDefaultHeroData()
    for i,v in pairs(allLineUpHero) do
        for j,k in pairs(v.heroList) do
            selectHeroList[k.heroID] = true
        end
    end
    return selectHeroList
end

local function GetUnselectHerodata()
    local heroListData = {}
    banSelectTips = true
    local allLineUpHero = GetSelectHeroData()
    local gw_hero_data = require "gw_hero_data"
    local tempData = gw_hero_data.GetOwnedHeroIDList()
    if tempData ~= nil then
        for sid, hero in pairs(tempData) do
            if not allLineUpHero[hero] then
                local heroData = gw_hero_data.GetHeroCfgId2Entity(hero)
                if heroData then
                    table.insert(heroListData, heroData)
                end

            end
        end
    end
    return heroListData
end

local function SaveHeroByHeroLine(teamId,teamData,skipState)
    local value = CheckDefaultTeamBusy(teamId)
    if not value or skipState then
        local msg = {};
        msg.lineUp = {};
        --下面是只传一个
        local selectData = teamData;
        local tempLine = {};
        tempLine.weaponId = 0
        tempLine.order = selectData.order or selectData.index or teamId;
        local _power = gw_power_mgr.GetHeroGroupPowerById(selectData and selectData.heroList or {})
        local weaponPower = GWG.GWHomeMgr.droneData.GetDroneLocalPower()

        _power = _power + weaponPower
        tempLine.ce = _power;
        tempLine.palList = {}
        for i = 1,5 do
            if selectData.heroList[i-1] ~= nil then
                local row,col = battle_data.GetRowAndColByIndex(i)
                local temp =
                {
                    palId = selectData.heroList[i-1].heroSid or selectData.heroList[i-1].serverData.heroSid,
                    row = row,
                    col = col,
                    heroId = selectData.heroList[i-1].heroID or selectData.heroList[i-1].serverData.heroCfgID,
                }
                if selectData.heroList[i-1].numProp then
                    temp.lv = selectData.heroList[i-1].numProp.lv
                else
                    temp.lv = selectData.heroList[i-1].heroLevel or selectData.heroList[i-1].serverData.heroLevel
                end
                table.insert(tempLine.palList,temp);
            end
        end
        msg.lineUp = tempLine;
        msg.teamIndex = teamId
        net_city_module.MSG_CITY_SAVE_TROOP_REQ(msg); --保存队伍请求
    end

end

--data table {index(设置队伍id),teamList(英雄数据),saveFunc(保存英雄数据回调)}
---@param teamType number EnumTeamType
---@param data table
---@param truckId number 货车id
---@public 外部调用设置队伍数据 data里面可以传teamList及saveFunc数据处理就不在该mgr定义
function ShowSelectPanel(teamType,data,truckId)
    if not teamType then
        teamType = EnumTeamType.Default
    end
    if teamType == EnumTeamType.Default then
        data.teamList = GetDefaultHeroData()
        data.saveFunc = GetDefaultHeroSave
        data.saveOnceFunc = GetDefaultHeroData
        data.closeSaveFunc = GetDefaultHeroSave
        data.checkTeamBusy = CheckDefaultTeamBusy
        data.value = false --是否显示保存按钮
    elseif teamType == EnumTeamType.InterCityTrucks then
        data.teamList = GetInterCityHeroData()
        data.saveFunc = GetInterCityHeroSave
        data.saveOnceFunc = GetInterCityHeroData
        data.closeSaveFunc = InterCityCloseSaveFunc
        data.checkTeamBusy = CheckInterCityTeamBusy
        data.value = true
        SetData.truckId = truckId
    elseif teamType == EnumTeamType.DesertStorm then --目前版本，沙漠风暴与城建数据走同一套逻辑
        data.teamList = GetDefaultHeroData()
        data.saveFunc = GetDefaultHeroSave
        data.closeSaveFunc = GetDefaultHeroSave
        data.saveOnceFunc = GetDefaultHeroData
        data.checkTeamBusy = CheckDefaultTeamBusy
        data.value = false
    else
        if not data.teamList or not data.saveFunc then
            log.Error("not data.teamList or not data.saveFunc")
            return
        end
    end
    data.teamType = teamType
    ui_window_mgr:ShowModule("ui_new_hero_select_panel",nil,nil,data)
end

--一键上阵
function QuickSelectHero(skipState) --skipState表示无视条件强行上阵
    local teamDataList = GetDefaultHeroData() or {}
    local unselectHero = GetUnselectHerodata()
    for i=1,4 do --可能存在跳过解锁编队的情况，所以必须这样做来确保顺序
        if (skipState and i == 1) or not CheckDefaultTeamBusy(i) then
            if not teamDataList[i] then
                teamDataList[i] = {
                    heroList = {}
                }
            end
            for j=0,4 do
                if not teamDataList[i].heroList[j] then
                    local hero = table.remove(unselectHero,1)
                    if hero then
                        teamDataList[i].heroList[j] = hero
                    end
                end
            end
            SaveHeroByHeroLine(i,teamDataList[i],skipState)
        end
    end
    
end

