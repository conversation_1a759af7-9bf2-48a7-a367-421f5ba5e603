
--- ui_new_hero_select_panel_controller.txt
--- Generated by Emmy<PERSON>ua(https://github.com/EmmyLua)
--- Created by nyz.
--- DateTime: 
--- desc:    
---
local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local string = string
local table = table
local newclass = newclass

local gw_power_mgr = require "gw_power_mgr"
local util                  = require "util"
local event = require "event"
local module_scroll_list = require "scroll_list"
local log = require "log"
local controller_base = require "controller_base"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local hero_trial_mgr = require"hero_trial_mgr"
local gw_home_common_data = require "gw_home_common_data"
local lang = lang
local sand_ui_event_define = require "sand_ui_event_define"
local event_carriage_define = require("event_carriage_define")
local net_city_module = require "net_city_module"

module("ui_new_hero_select_panel_controller")
--TODO  类的实现方式后续需要优化，或者不用基类 现在的class的继承很耗时，
local controller = nil
local UIController = newclass("ui_new_hero_select_panel_controller", controller_base)

HeroLineUpEnum ={
    LineUp1 = 1,
    LineUp2 = 2,
    LineUp3 = 3,
    LineUp4 = 4,
    LineUp5 = 5,
}

local TabData = {}
local heroCfgCache = {}
local isShowTip = false
--不能使用的英雄提示id
local UnUseHeroTipId = 73212
local isShowResetBtn = false
local fightBtnTxt = nil
local defaultHeros = nil -- 默认英雄
local curEnemyIndex = HeroLineUpEnum.LineUp1
local myPower = 0
local squadList = {}

--[[窗口初始化]]
---@param data table {index = 1}
function UIController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    self.data = data
    self.showMainPanel = data.showMainPanel or false
    curEnemyIndex = data.index;
    if data and data.teamList then
        for k,v in pairs(data.teamList) do
            SetSaveHeroData(v,nil,nil,nil,k);
        end
    end
    self.saveFunc = data.saveFunc
    self.saveOnceFunc = data.saveOnceFunc
    self.closeSaveFunc = data.closeSaveFunc
    self:TriggerUIEvent("SetCheckBusyEvent",data.checkTeamBusy);
    self:TriggerUIEvent("SetTeamType",data.teamType);
    self:TriggerUIEvent("SetTabData",TabData);
    self:TriggerUIEvent("RefreshToggleView");
    self:TriggerUIEvent("ShowOrHideSaveBtn",data.value);
    --OnGetTroopIsIdle(curEnemyIndex)
end

--[[界面被显示的时候调用]]
function UIController:OnShow()
    self.__base.OnShow(self)
end

function UIController:Close()
    self.__base.Close(self)
    controller = nil
    heroCfgCache = {}
    isShowTip = false
    UnUseHeroTipId = 73212
    isShowResetBtn = false
    fightBtnTxt = nil
    defaultHeros = nil
    curEnemyIndex = HeroLineUpEnum.LineUp1
    squadList = {}

    if self.showMainPanel then
        event.Trigger(sand_ui_event_define.GW_OPEN_SAND_MAIN)
    end
end

--会基类自动调用
function UIController:AutoSubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了  
    --接收武器装备信息
    self.onGetChooseWeapon = function (evenname,msg)
        local weapon_data = require("weapon_data")
        local usedWeapon = GetCurData().usedWeapon
        if not usedWeapon then return end
        local beenSet = {}
        for i = 1, #TabData do
            local key = "weaponId"..i
            local wid = 0
            if msg[key] and msg:HasField(key) and not beenSet[msg[key]] then --直接判断HasField一个不能存在的key会抛异常，导致后面代码不执行，ui没更新
                weapon_data.SetlocalizedWeaponData(usedWeapon.stage,msg[key],i-1,usedWeapon.arenaType1)
                wid = msg[key]
                beenSet[wid] = true
            else
                weapon_data.SetlocalizedWeaponData(usedWeapon.stage,0,i-1,usedWeapon.arenaType1)
            end
            if TabData[i] and TabData[i].usedWeapon then
                TabData[i].usedWeapon.weaponId = wid
            end
        end
        if usedWeapon then
            self:SetWeaponIcon()
            UpdatePower()
        end
    end
    event.Register(event.WEAPON_GET_CHOOSE_RSP,self.onGetChooseWeapon)
    self.updateSquad = function()
        self:SaveCurHero()
    end
    self.interCityUpdateSquad = function()
        local ui_new_hero_select_panel = require "ui_new_hero_select_panel"
        ui_new_hero_select_panel.UpdatePower(); --手动刷新一次战力
    end
    event.Register(event.UPDATE_SQUAD,self.updateSquad)
    event.Register(event_carriage_define.TMSG_CARRIAGE_SET_TROOP_RSP,self.interCityUpdateSquad)
    self.saveSquad = function()
        self:NewOnFight()
    end
    self:RegisterEvent(event.SAVE_SQUAD,self.saveSquad)
    self.OnSetTeamState = function(_,msg)
        
    end

    local new_weapon_combat_crystal_define = require "new_weapon_combat_crystal_define"
    self.updateCrystalIDText = function()
        self:TriggerUIEvent("UpdateCrystalIDText")
        local ui_new_hero_select_panel = require "ui_new_hero_select_panel"
        ui_new_hero_select_panel.UpdatePower()
    end
    self:RegisterEvent(new_weapon_combat_crystal_define.TMSG_DRONESTAR_TROOP_RSP,self.updateCrystalIDText)
end
--会基类自动调用
function UIController:AutoUnsubscribeEvents()
    event.Unregister(event.WEAPON_GET_CHOOSE_RSP,self.onGetChooseWeapon)
    event.Unregister(event.UPDATE_SQUAD,self.updateSquad)
    event.Unregister(event_carriage_define.TMSG_CARRIAGE_SET_TROOP_RSP,self.interCityUpdateSquad)
end
---********************功能函数区**********---
function UIController:OnCloseBtnClick()
    if self.closeSaveFunc ~= nil then
        local sand_team_data = require "sand_team_data"
        local value = sand_team_data.CheckTeamIsBusy(curEnemyIndex)
        if not value then
            local teamState = sand_team_data.GetTeamStateByIndex(curEnemyIndex)
            if teamState ~= 0 then
                if self.data.checkTeamBusy and self.data.checkTeamBusy(curEnemyIndex) then
                    --log.Error("Busy1")
                else
                    self.closeSaveFunc(curEnemyIndex,TabData)
                end
            end
        end

    end
    windowMgr:UnloadModule("ui_new_hero_select_panel")
end
--战斗按钮返回
function UIController:OnFight()
    if self.onFightEvent ~= nil and self.hasInit then
        local power = gw_power_mgr.GetHeroGroupPowerById(GetSelectedHero())
        tempPower = {mine = power,enemy = enemyPower}
        --TODO：如果是多队阵容且有一队没有上阵英雄提示
        if IsThreeLine() then
            if util.get_len(self:GetSelectedHero1()) == 0 and (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) and (self:GetSelectedHero3()~=nil and util.get_len(self:GetSelectedHero3()) == 0) then
                flow_text.Add(lang.Get(73364))
                return
            elseif util.get_len(self:GetSelectedHero1()) == 0 then
                flow_text.Add(lang.Get(73361))
                self:SwitchPage(HeroLineUpEnum.LineUp1)
                return
            elseif (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) then
                flow_text.Add(lang.Get(73362))
                self:SwitchPage(HeroLineUpEnum.LineUp2)
                return
            elseif (self:GetSelectedHero3()~=nil and util.get_len(self:GetSelectedHero3()) == 0) then
                flow_text.Add(lang.Get(73363))
                self:SwitchPage(HeroLineUpEnum.LineUp3)
                return
            elseif (self:GetSelectedHero4()~=nil and util.get_len(self:GetSelectedHero4()) == 0) then
                flow_text.Add(lang.Get(73364))
                self:SwitchPage(HeroLineUpEnum.LineUp4)
                return
            elseif (self:GetSelectedHero5()~=nil and util.get_len(self:GetSelectedHero5()) == 0) then
                flow_text.Add(lang.Get(73364))
                self:SwitchPage(HeroLineUpEnum.LineUp5)
                return
            end
        elseif IsMulti() then
            if util.get_len(self:GetSelectedHero1()) == 0 and (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) then
                flow_text.Add(lang.Get(73364))
                return
            elseif util.get_len(self:GetSelectedHero1()) == 0 and IsOneLine() then
                --星级迷航多队死亡队伍不会设置enemyHeroData，位面战纪不管是否死亡都会设置enemyHeroData
                local line1 = GetLine1()
                if line1 and line1.enemyHeroData ~= nil then
                    flow_text.Add(lang.Get(73361))
                    self:SwitchPage(HeroLineUpEnum.LineUp1)
                    return
                end
            elseif (self:GetSelectedHero2()~=nil and util.get_len(self:GetSelectedHero2()) == 0) and GetLine2() then
                flow_text.Add(lang.Get(73362))
                self:SwitchPage(HeroLineUpEnum.LineUp2)
                return
            end
        else
            if util.get_len(GetSelectedHero()) == 0 then
                local lang = require "lang"
                flow_text.Add(lang.Get(7110))
                return
            end
            --[[
            --TODO:挑战秘境需要阵营英雄限制
            if forMultiSecretPlace then
                -- --print("window.curfactionNum",window.curfactionNum,"window.factionNum",window.factionNum,"window.curReplaceNum",window.curReplaceNum)
                if window.curfactionNum and window.curReplaceNum and window.factionNum and window.curfactionNum+window.curReplaceNum<window.factionNum then
                    --local content = window.tipID and string.format(lang.Get(window.tipID),window.factionNum,window.curfactionNum,window.factionNum) or  ""
                    local content = string.format(lang.Get(93083),window.factionNum,lang.Get(gw_hero_mgr.Hero_Type_Name[window.factionID]))
                    flow_text.Add(content)
                    return
                end
            end
            ]]
        end
        local tempHeroData = self:GetFilteredHero(self.quality,self.campIndex)
        if util.get_len(tempHeroData) ~= 0 then
            self.onFightEvent(self,false)
        end
    end
end
--原返回方法过于复杂，先写个简单的，得空review的时候再处理吧。
function UIController:NewOnFight()
    local teamId = GetCurEnemyIndex()
    if self.saveFunc then
        self.saveFunc(teamId,TabData)
    end
end

function UIController:SaveCacheHero()
    self:TriggerUIEvent("SetTabData",TabData);
end

function UIController:SaveCurHero()
    -- 立即拉取一次
    --log.Error("拉取")
    if self.saveOnceFunc then
        local data = self.saveOnceFunc()
        for k,v in pairs(data) do
            SetSaveHeroData(v,nil,nil,nil,k);
        end
    end
    self:TriggerUIEvent("SetTabData",TabData);
    local ui_new_hero_select_panel = require "ui_new_hero_select_panel"
    ui_new_hero_select_panel.UpdatePower(); --手动刷新一次战力
end

function SetSaveHeroData(heroData,hpEnable,cData,isUpdateUI,line)
    --log.Error("Save")
    line = line or 1
    --curEnemyIndex = line;
    TabData[line] = TabData[line] or {}
    TabData[line].showHp = hpEnable
    TabData[line].cacheData = cData
    TabData[line].index = line;

    if heroData then
        if heroData.usedWeapon and heroData.usedWeapon.weaponId ~= 0 then
            TabData[line].usedWeapon = heroData.usedWeapon
        else
            TabData[line].usedWeapon = nil;
        end
        TabData[line].order = heroData.order or 0
        TabData[line].state = heroData.state
        TabData[line].isUnlocked = heroData.isUnlocked or false
        TabData[line].saveSelectedHero = {}
        if heroData.heroList ~= nil then
            for p, h in pairs(heroData.heroList) do
                if p >= 0 then
                    TabData[line].saveSelectedHero[p] = h
                end
            end
        end
    else
        TabData[line].usedWeapon = nil;
        TabData[line].order = 0;
        TabData[line].state = nil;
        TabData[line].isUnlocked = false;
        TabData[line].saveSelectedHero = {}
    end
    
    TabData[line].selectedHero = {}
    if heroData then
        if heroData.heroList ~= nil then
            for p, h in pairs(heroData.heroList) do
                if p >= 0 then
                    local dead = false
                    local isTrial = h.isTrial and h.isTrial == 1 or hero_trial_mgr.isTrialHeroBySid(h.heroSid)
                    if not IsTrialMode() and cData and cData[h.heroSid] and cData[h.heroSid].numProp.hp == 0 then
                        dead = true
                    end
                    if isTrial and (not dead) then--试用英雄GetTrialPrefabPath
                        if battleType then
                            local canTrial = hero_trial_mgr.GetTrialHeroBattleTimes_Can(battleType,h.heroSid)
                            if not canTrial or canTrial == 0 then
                                dead = true
                            end
                        else
                            log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO,"battleType is nil，试用英雄已自动下战，请检查是否传入战斗类型")
                            dead = true
                        end
                    end
                    h.isTrial = isTrial and 1 or 0
                    --if (not dead) and ( IsHeroIDSelected(h) == 0 or forMaze) then
                        TabData[line].selectedHero[p] = h
                    --end
                end
            end
        end
        
    end

    lastSelectHeros = TabData[line].selectedHero

    if isUpdateUI then
        self:TriggerUIEvent("UpdateUi");
    end
    isCanUseTrial = curType ~= DEFENCE
end

function GetTabDataByLine(teamIdx)
    if TabData[teamIdx] == nil then
        TabData[teamIdx] = {}
    end
    return TabData[teamIdx]
end

function OnGetTroopIsIdle(teamIndex)
    net_city_module.MSG_CITY_TROOP_IS_IDLE_REQ(teamIndex)
end

function GetCurData()
    if TabData[curEnemyIndex] == nil then
        TabData[curEnemyIndex] = {}
        TabData[curEnemyIndex].index = curEnemyIndex
    end
    return TabData[curEnemyIndex]
end

function GetSelectedHero()
    local data = GetCurData()
    if not data.selectedHero then
        data.selectedHero = {}
    end
    return data.selectedHero
end

function UIController:GetSelectedHero1()
    if not TabData[HeroLineUpEnum.LineUp1] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp1].selectedHero
end

function UIController:GetSelectedHero2()
    if not TabData[HeroLineUpEnum.LineUp2] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp2].selectedHero
end

function UIController:GetSelectedHero3()
    if not TabData[HeroLineUpEnum.LineUp3] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp3].selectedHero
end

function UIController:GetSelectedHero4()
    if not TabData[HeroLineUpEnum.LineUp4] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp4].selectedHero
end

function UIController:GetSelectedHero5()
    if not TabData[HeroLineUpEnum.LineUp5] then
        return nil
    end
    return TabData[HeroLineUpEnum.LineUp5].selectedHero
end

function UIController:ShowPowerTips()
    self:TriggerUIEvent("ShowPowerTips");
end

function UIController:OnCrystalButtonClick()
    local ui_window_mgr = require "ui_window_mgr"
    local win = ui_window_mgr:ShowModule("ui_new_magic_weapon_crystal_scheme_pop",nil,nil,{teamIndex = curEnemyIndex})
end

function UIController:HidePowerTips()
    self:TriggerUIEvent("HidePowerTips");
end

function GetCacheData()
    return GetCurData().cacheData
end

function SetIsShowTip(isShow)
    isShowTip = isShow
end

function GetIsShowTip()
    return isShowTip
end

function SetUnUseHeroTipId(id)
    UnUseHeroTipId = id
end

--设置是否显示重置按钮
function SetResetBtnVisible(bool)
    isShowResetBtn = bool
end

function GetResetBtnVisible()
    return isShowResetBtn
end

--设置挑战按钮文本
function SetFightBtnTxt(txt)
    fightBtnTxt = txt
end

function GetFightBtnTxt()
    return fightBtnTxt
end

--1.设置是否显示队伍调整按钮2.设置是否是主线
function SetAdjustBtnVisible(bool,bool1)
    isShowAdjustBtn = bool
    isMainStoryChallenge = bool1
end

--设置阵容类型
function SetBattleType(_type)
    curType = _type
    isCanUseTrial = curType == ATTACK
end

--设置英雄列表星级要求
function SetLimitHeroStar(isLimit,_starLvLimit)
    isLimitHeroStar = isLimit
    lowStarLvLimit = _starLvLimit
end

--设置竞技场左右边玩家信息
function SetRoleInfos(_leftRoleInfo, _rightRoleInfo, _getHeroFunc, _hideTeamInfo)
    leftRoleInfo = _leftRoleInfo
    rightRoleInfo = _rightRoleInfo
    getHeroFunc = _getHeroFunc
    hideTeamInfo = _hideTeamInfo
end

--设置队伍数量
function SetTeamCount(count)
    teamNum = count
end

--得到队伍数量
function GetLineCount()
    if teamNum and teamNum>0 then
        return teamNum
    end
    local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.WeekendArena then--时空擂台
        local myTeamCount = GetTeamCount()--队伍数量
        return myTeamCount
    end
    if IsThreeLine() then
        return 3
    end
    if IsMulti() then
        return 2
    end
    return 1
end

--设置默认英雄
function SetDefaultHeros(heros)
    defaultHeros = heros
end

function GetDefaultHeros()
    return defaultHeros
end

--得到上锁槽位信息
function GetSlotState()
    return GetCurData().slotState
end

--设置上锁槽位
function SetSlotState(slots,line)
    line = line or 1
    TabData[line] = TabData[line] or {}
    TabData[line].slotState = slots
end

function GetPowerFlowRoot()
    return GetCurData().powerFlowRoot
end

--获取时空擂台队伍数量
function GetTeamCount()
    if teamNum and teamNum>0 then
        return teamNum
    end
    local weekend_arena_mgr = require "weekend_arena_mgr"
    local common_new_pb = require "common_new_pb"
    if battleType == common_new_pb.WeekendArena then--时空擂台
        local myTeamCount = weekend_arena_mgr.GetCanSetTeamCount()--队伍数量
        return myTeamCount
    end
    return IsEnemyCfgLine()
end

function OneLineIsUnlock()
    return gw_home_common_data.GetSquadState(1,2);
end

--二队解锁了吗？
function SecondLineIsUnlock(isTip)
    return gw_home_common_data.GetSquadState(2,2,isTip);
end
--三队解锁了吗？
function ThirdLineIsUnlock(isTip)
    return gw_home_common_data.GetSquadState(3,2,isTip);--TabData[3] ~= nil;
end
--四队解锁了吗？
function ForthLineIsUnlock(isTip)
    local privilege_data = require "privilege_data"
    local privilege_define  = require "privilege_define"
    local value = gw_home_common_data.GetSquadState(4,2,isTip) and privilege_data.GetPrivilegeIsOpenByID(privilege_define.MONTH_FOUR_TEAM);--TabData[4] ~= nil;
    return value
end

--是否有单队
function IsOneLine()
    return GetTeamCount() > 0
end

--现在编队界面默认就是多队,4个
function IsMulti()
    return true;--GetTeamCount() > 1
end

--是否是三队
function IsThreeLine()
    return GetTeamCount() > 2
end

function IsEnemyCfgLine()
    local line = 0
    for i, v in ipairs(TabData) do
        if v.enemyHeroData then
            line = line + 1
        end
    end
    return line
end

--得到第一个切页数据
function GetLine1()
    return TabData[HeroLineUpEnum.LineUp1]
end

--得到第二切页数据
function GetLine2()
    return TabData[HeroLineUpEnum.LineUp2]
end

--得到第三切页数据
function GetLine3()
    return TabData[HeroLineUpEnum.LineUp3]
end

function SetCurEnemyIndex(index)
    curEnemyIndex = index
end

function GetCurEnemyIndex()
    return curEnemyIndex;
end

function SetPower(power)
    myPower = power;
end
-- ========================================= 勇者试炼相关 Start ==========================================

local isTrailMode = false

function GetTrialPrefabPath()
    return "ui/prefabs/uiheroselectfortriallevel.prefab"
end

-- 设置为勇者试炼状态
function SetTrialMode()
    isTrailMode = true
end

function IsTrialMode()
    return isTrailMode
end

function OnCloseTrialProcess()
    isTrailMode = false
end

-- ========================================== 勇者试炼相关 End ==========================================
-- ========================================== 优化相关 Start ==========================================

function GetHeroInCache(heroID)
    local heroCfg = nil
    if  heroID then
        heroCfg = heroCfgCache[heroID]
        if not heroCfg then
            heroCfg = game_scheme:Hero_0(heroID)
            if heroCfg then
                heroCfgCache[heroID] = heroCfg
            end
        end
    end
    return heroCfg
end
-- ========================================== 优化相关 End ==========================================
---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
