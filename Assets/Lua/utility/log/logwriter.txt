local require = require
local object = require "object"
local class = require "class"
local io = io or require "io"
local print = print
local os = os


module("logwriter")

local OPEN_LOG_FLAG = false

local LogWriter = 
{
    oFile = nil,
    oObj = nil,
}


function LogWriter:ctor()
    if not OPEN_LOG_FLAG then
        return
    end
    local now = os.time()
    local timeStr = os.date("%Y_%m_%d_%H_%M_%S", now)
    self.oFile = io.open("C:/log_"..timeStr..".txt", "w");
end


function LogWriter:Write(szContent)
    if OPEN_LOG_FLAG and self.oFile ~= nil then
        self.oFile:write(szContent.."\n")
        self.oFile:flush()
        --print(szContent)
    end
end


function LogWriter:Close(szContent)
    if self.oFile == nil then
        return
    end

    self.oFile:close()
end


local CLogWriter = class(object, nil, LogWriter)
return CLogWriter
