--device_level_controller.txt
local tostring = tostring
local string = string
local tonumber = tonumber
local math = math
local print = print
local require = require

local event = require "event"
local log = require "log"
local device_level_data = require "device_level_data"

local IsInEditor = CS.War.Script.Utility.IsInEditor
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local SystemInfo = CS.UnityEngine.SystemInfo
local Screen = CS.UnityEngine.Screen

local _M = {}
local originDeviceLevel
local resolution
local frame
local postProcessing
local hdr
local shadow
local light
local mesh
local particle
local renderTexture
local Quality
local viewCache
local heroCache
local systemQualityLevel
local preloadLevel,sandboxPreloadLevel
local resPoolLimitSize, memorySizeID

local midCfg
local gpuSizeCfg
local processorFrequencyCfg
local openglCfg
local gpuLevelCfg
local previewHeroCount
local cacheHeroCount
local cacheHeroView

local init

function _M.GetLevelID(cfgName)
    if device_level_data.CheckValueData() then
        return _M.GetDeviceData(cfgName)
    end
    device_level_data.GetDeviceLevelData(function()
        return _M.GetDeviceData(cfgName)
    end)
end

function _M.GetDeviceData(cfgName)
    if init and device_level_data.GetDeviceLevelData() then
        return _M.GetNumber(cfgName)
    end
    local mid = device_level_data.GetMemorySizeID() or 1007
    local gpuSize = device_level_data.GetGPUMemorySizeID() or 2002
    local processorFrequency = device_level_data.GetFrequencyID() or 3002
    local opengl = device_level_data.GetOpenGLCfgID() or 4002
    local gpuLevel = device_level_data.GetGPUID() or 6003
    if IsInEditor() then
        mid = 1007
        gpuSize = 2002
        processorFrequency = 3003
        opengl = 4003
        gpuLevel = 6003
    end
    memorySizeID = mid
    local data = device_level_data.GetDeviceLevelData()
    if data then
        init = true
        midCfg = data[tostring(mid)]
        gpuSizeCfg = data[tostring(gpuSize)]
        processorFrequencyCfg = data[tostring(processorFrequency)]
        openglCfg = data[tostring(opengl)]
        gpuLevelCfg = data[tostring(gpuLevel)]
        log.Warning("GetLevelID",cfgName,mid,gpuSize,processorFrequency,opengl,gpuLevel)
        _M.SendReportDeviceInfo()
        return _M.GetNumber(cfgName)
    else
        return nil
    end
end

function _M.GetNumber(cfgName)
    local data = device_level_data.GetDeviceLevelData()
    if not data then
        return nil
    end
    local numMid = tonumber(midCfg[cfgName])
    local numGpuSize = tonumber(gpuSizeCfg[cfgName])
    local numProcessorFrequency = tonumber(processorFrequencyCfg[cfgName])
    local numOpengl = tonumber(openglCfg[cfgName])
    local numGpuLevel = tonumber(gpuLevelCfg[cfgName])
    print("GetLevelID",cfgName,numMid,numGpuSize,numProcessorFrequency,numOpengl,numGpuLevel)
    return math.min(tonumber(midCfg[cfgName]),numMid,numGpuSize,numProcessorFrequency,numOpengl,numGpuLevel)
end

function _M.GetMemorySizeCfgValue(cfgName)
    if device_level_data.CheckValueData() then
        return tonumber(midCfg[cfgName])
    end
    device_level_data.GetDeviceLevelData(function()
        _M.GetDeviceData(cfgName)
        return tonumber(midCfg[cfgName])
    end)
end

function _M.GetOriginDeviceLevel()
    if not originDeviceLevel then
        originDeviceLevel = _M.GetLevelID("originDeviceLevel") or 2
    end
    return originDeviceLevel
end

function _M.GetPreloadLevel()
    if not preloadLevel then
        preloadLevel = _M.GetLevelID("preloadLevel") or 3
    end
    return preloadLevel
end

function _M.GetSandboxPreloadLevel()
    if not sandboxPreloadLevel then
        sandboxPreloadLevel = _M.GetLevelID("sandboxPreloadLevel") or 3
    end
    return sandboxPreloadLevel
end

function _M.GetResPoolLimitSize()
    if not resPoolLimitSize then
        local limitSize = _M.GetLevelID("resPoolLimitSize") or 1000
        resPoolLimitSize = 1024 * 1024 * limitSize
        log.Warning("GetResPoolLimitSize", resPoolLimitSize, limitSize)
    end
    return resPoolLimitSize
end

function _M.ReduceResPoolLimitSize()
    if memorySizeID then
        local id = memorySizeID - 1
        local strID = tostring(id)
        local data = device_level_data.GetDeviceLevelData()
        if data and data[strID] then
            resPoolLimitSize = 1024 * 1024 * data[strID].resPoolLimitSize
            memorySizeID = id
        end
    end
end

function _M.OpenResolutionController()
    if not resolution then
        resolution = _M.GetLevelID("resolution") or 1
    end
    return resolution
end

function _M.OpenFrameController()
    if not frame then
        frame = _M.GetLevelID("frame")
    end
    return frame and frame or 30
end

function _M.OpenHdrController()
    if not hdr then
        hdr = _M.GetLevelID("hdr") or 0
    end
    return hdr ~= 1
end

function _M.OpenShadowController()
    if not shadow then
        shadow = _M.GetLevelID("shadow") or 0
    end
    return shadow ~= 1
end

function _M.OpenLightController()
    if not light then
        light = _M.GetLevelID("light") or 0
    end
    return light == 1
end

function _M.OpenMeshController()
    if not mesh then
        mesh = _M.GetLevelID("mesh") or 4
    end
    return mesh
end

function _M.OpenParticleController()
    if not particle then
        particle = _M.GetLevelID("particle") or 2
    end
    return particle
end

function _M.OpenRenderTextureController()
    if not renderTexture then
        renderTexture = _M.GetLevelID("renderTexture") or 2
    end
    return renderTexture
end

function _M.OpenQualityController()
    if not Quality then
        Quality = _M.GetLevelID("Quality") or 3
    end
    return Quality
end

function _M.OpenViewCacheController()
    if not viewCache then
        viewCache = _M.GetLevelID("viewCache") or 6
    end
    return viewCache
end

function _M.OpenHeroCacheController()
    if not heroCache then
        heroCache = _M.GetLevelID("heroCache") or 9
    end
    return heroCache
end

function _M.OpenSystemQualityLevel()
    if not systemQualityLevel then
        systemQualityLevel = _M.GetLevelID("systemQualityLevel") or 2
    end
    return systemQualityLevel
end

function _M:GetPreviewHero()
    if not previewHeroCount then
        previewHeroCount = _M.GetMemorySizeCfgValue("previewHeroCount") or 6
    end
    if Application.platform == RuntimePlatform.IPhonePlayer and previewHeroCount == 6 then
        return true
    else
        return false
    end
    return false
end

function _M:GetCacheHeroCount()
    if not cacheHeroCount then
        cacheHeroCount = _M.GetMemorySizeCfgValue("cacheHeroCount") or 6
    end
    return cacheHeroCount
end

function _M:GetCacheHeroView()
    if not cacheHeroView then
        cacheHeroView = _M.GetMemorySizeCfgValue("cacheHeroView") or 4
    end
    return cacheHeroView
end
function _M.SendReportDeviceInfo()
    local json = require "dkjson"
    local level = 1
    if gpuLevelCfg then
        level = gpuLevelCfg.originDeviceLevel
    end
    local json_str = json.encode({ 
        deviceLevel =  device_level_data.GetReportLevel() and 999 or level,
        graphicsDeviceName = SystemInfo.graphicsDeviceName,--GPU名称
        deviceModel = SystemInfo.deviceModel,--设备模型
        deviceName = SystemInfo.deviceName,--设备名称
        operatingSystem = SystemInfo.operatingSystem,--操作系统
        resolution = string.format("%sx%s", Screen.width,  Screen.height)
    })
    print("report_device_info:",json_str)
    event.Trigger(event.GAME_EVENT_REPORT, "report_device_info", json_str)
end

return _M