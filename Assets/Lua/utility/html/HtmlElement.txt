---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by huangjr22011203.
--- DateTime: 2023/7/17 15:25
---

local require = require
local table = table
local string = string

module("HtmlElement")

local HtmlElement = {}

function HtmlElement:ctor()
    self.name = ""
    self.parent = nil
    self.attribute = nil
    self.children = nil
    self.content = nil
end

function HtmlElement:Init(name, parent, attribute, content)
    self.name = name
    self.parent = parent
    self.attribute = attribute
    self.content = content
    return self
end

function HtmlElement:GetChildren()
    return self.children or {}
end

function HtmlElement:GetName()
    return self.name
end

function HtmlElement:GetParent()
    return self.parent
end

function HtmlElement:GetAttribute()
    return self.attribute
end

function HtmlElement:GetAttributeValue(key)
    if not self.attributeValue then
        self:InitAttributeValue()
    end
    return self.attributeValue[key]
end

function HtmlElement:InitAttributeValue()
    self.attributeValue = {}
    if self.attribute then
        local nextIndex = 0
        local eqIndex = string.find(self.attribute, "=", nextIndex)
        while eqIndex and eqIndex > 0 do
            local key = string.gsub(string.sub(self.attribute, nextIndex, eqIndex - 1)," ","")
            local endIndex = string.find(self.attribute, '"', eqIndex + 2)
            local value = string.sub(self.attribute, eqIndex + 2, endIndex-1)
            self.attributeValue[key] = value
            
            nextIndex = endIndex + 1
            eqIndex = string.find(self.attribute, "=", nextIndex)
        end
    end
end

function HtmlElement:SetAttributeValue(key, value)
    if not self.attributeValue then
        self:InitAttributeValue()
    end
    self.attributeValue[key] = value
end

function HtmlElement:GetContent()
    return self.content or ""
end

function HtmlElement:SetParent(parent)
    self.parent = parent
end

function HtmlElement:SetContent(content)
    self.content = content
end

function HtmlElement:AddChild(child)
    if not child then
        return
    end
    if not self.children then
        self.children = {}
    end
    child:SetParent(self)
    table.insert(self.children, child)
end

local class = require "class"
local object = require "object"
CHtmlElement = class(object, nil, HtmlElement)