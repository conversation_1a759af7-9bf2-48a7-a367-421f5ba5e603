local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_hero_kelisiduo_weapon_entity = bc_Class("cysoldierssortie_comp_hero_kelisiduo_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local UnityEngine = CS.UnityEngine
local math = math
local Mathf = CS.UnityEngine.Mathf
local PoolObject = NeeGame.PoolObject
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set
local isAsync = isAsync
function cysoldierssortie_comp_hero_kelisiduo_weapon_entity:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
end

local skillParam = {}
function cysoldierssortie_comp_hero_kelisiduo_weapon_entity:FireBullet(centerAngle,angleSpread)
    -- Generate a random angle within the fan
    local randomAngle = centerAngle - (angleSpread / 2) + math.random() * angleSpread

    -- Convert angle to direction vector
    local radians = Mathf.Deg2Rad * randomAngle
    local direction = bc_CS_Vector3(math.cos(radians), 0, math.sin(radians))
    
    -- Spawn the attack at the correct position and direction
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    
    local function callback(skillGo)
        if skillGo then
            SetTransformPositionByTransform(skillGo.transform,self._character._weaponRoot)
            skillGo.transform.forward = direction
            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)
            skillParam.character=self._character
            skillParam.ballisiticVelocity = self._weaponData._ballisiticVelocity
            skillParam.ballisiticRange = self._weaponData._ballisiticRange
            skillParam.attack = self._character._attack * (self._weaponData._damageCoefficient/10000)
            local coe =  self._character:GetStarLvAddCoefficient(self._weaponData)
            if coe then
                skillParam.attack = skillParam.attack + skillParam.attack * coe
            end
            skillParam.damageRange = self._weaponData._damageRange
            skillParam.attackPentration = self._weaponData._pierce
            skillParam.critical = self._weaponData._criticalHit
            skillParam.bulletScale = self._weaponData._bulletScale
            skillParam.skillPath = self._weaponData._skillEffectPath
            skillParam.dstEffectsPath = self._weaponData._dstEffectsPath
            skillParam.skillID = self._weaponData._skillID
            skillParam.skillView = true
            skillParam.maxDamageNum = self._weaponData._maxDamageNum
            skillParam.damageType = self._weaponData._damageType
            skillParam.isUltra = self._weaponData._isUltra
            skillEntity:CreateData(skillParam)
        end
    end
    if isAsync then
        poolMgr:AcquireObjAsync("cysoldierssortie_comp_base_bullet_entity",callback,poolMgr.transform)
    else
        local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity",poolMgr.transform)
        callback(skillGo)
    end
end

local delayTime
function cysoldierssortie_comp_hero_kelisiduo_weapon_entity:Fire()
    delayTime = self._release_skill_time
    if self._weaponData._strAttackAction  then
        self._character:PlayAnim(self._weaponData._strAttackAction)
        delayTime = 0.5
    end
    
    self._fire_timer = cysoldierssortie_DelayCallOnce(delayTime, function()
        self._character:PlayAnim(cysoldierssortie_hero_anim_set.Ability)
        cysoldierssortie_comp_base_weapon_entity.Fire(self)
        local angleSpread = 30       -- Total spread angle in degrees
        -- Calculate the center angle (character's forward direction)
        local x,y,z = ApiHelper.GetTransformForwardXYZ(self._character._weaponRoot)
        local centerAngle = math.atan2(z, x) * Mathf.Rad2Deg

        local minBullets = 6           -- Minimum number of bullets
        local maxBullets = 8           -- Maximum number of bullets
        local bulletCount = self._weaponData._nSkillRepeatCnt --math.random(minBullets, maxBullets)
        for i = 1, bulletCount do
            cysoldierssortie_DelayCallOnce((i-1)*self._weaponData._repeatMinInterval, function()
                self:FireBullet(centerAngle,angleSpread)
            end)
        end
    end)

end
return cysoldierssortie_comp_hero_kelisiduo_weapon_entity