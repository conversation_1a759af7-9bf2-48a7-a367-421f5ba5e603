local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_hero_drone_weapon_entity = bc_Class("cysoldierssortie_comp_hero_drone_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local UnityEngine = CS.UnityEngine
local math = math
local Mathf = CS.UnityEngine.Mathf
local PoolObject = NeeGame.PoolObject
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local SetTransformPositionByTransform = ApiHelper.SetTransformPositionByTransform
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local Quaternion = CS.UnityEngine.Quaternion
local bc_load_mgr = require "cysoldierssortie_load_mgr"
local cysoldierssortie_IsNil=cysoldierssortie_IsNil
local GWG = GWG
local tonumber = tonumber
local minigame_mgr = require "minigame_mgr"
local isAsync = isAsync

function cysoldierssortie_comp_hero_drone_weapon_entity:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
end

local skillParam = {}
function cysoldierssortie_comp_hero_drone_weapon_entity:FireBullet(centerAngle,angleSpread,weaponRoot,bulletCount,weaponR)
    -- Generate a random angle within the fan
    local randomAngle = centerAngle - (angleSpread / 2) + math.random() * angleSpread

    -- Convert angle to direction vector
    local radians = Mathf.Deg2Rad * randomAngle
    local direction = bc_CS_Vector3(math.cos(radians), 0, math.sin(radians))

    -- Spawn the attack at the correct position and direction
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)

    local function callback(skillGo)
        if skillGo then
        -- SetTransformPositionByTransform(skillGo.transform,self._character._weaponRoot)--self._character._weaponRoot
            ApiHelper.SetTransformPositionXYZ(skillGo.transform,weaponRoot.x+2,weaponRoot.y,weaponRoot.z)

            skillGo.transform.forward = direction
            skillGo.transform.localRotation = Quaternion.Euler(weaponR.x, weaponR.y, weaponR.z)
            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)
            skillParam.character=self._character
            skillParam.ballisiticVelocity = self._weaponData._ballisiticVelocity
            skillParam.ballisiticRange = self._weaponData._ballisiticRange
            local damageCoefficient = self._weaponData._damageCoefficient or 10000
            local droneCfg=GWG.GWHomeMgr.droneData.OnGetDroneCfgData()
            if droneCfg and droneCfg.strParam1 then
                self.game_scheme =self.game_scheme or require "game_scheme"
            local SkillCfg2= self.game_scheme:Skill_0(tonumber(droneCfg.strParam1))
                if SkillCfg2 and SkillCfg2.nAttackDamageScale then
                    damageCoefficient=tonumber(SkillCfg2.nAttackDamageScale)*damageCoefficient/10000
                end
            end
            skillParam.attack = self._character._attack * (damageCoefficient/10000)
            local coe =  self._character:GetStarLvAddCoefficient(self._weaponData)
            if coe then
                skillParam.attack = skillParam.attack + skillParam.attack * coe
            end
            if bulletCount and bulletCount>0 then
                skillParam.attack=skillParam.attack/bulletCount
                if skillParam.attack<1 then
                    skillParam.attack=1
                end
            end
            skillParam.damageRange =600-- self._weaponData._damageRange
            skillParam.attackPentration = self._weaponData._pierce
            skillParam.critical = self._weaponData._criticalHit
            skillParam.bulletScale = self._weaponData._bulletScale
            skillParam.skillPath = self._weaponData._skillEffectPath
            skillParam.dstEffectsPath = self._weaponData._dstEffectsPath
            skillParam.skillID = self._weaponData._skillID
            skillParam.onlyTriggerGround = true
            skillParam.damageType = self._weaponData._damageType
            skillParam.isUltra = self._weaponData._isUltra
            skillEntity:CreateData(skillParam)
        end
    end
    if isAsync then
        poolMgr:AcquireObjAsync("cysoldierssortie_comp_base_bullet_entity",callback,poolMgr.transform)
    else
        local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity",poolMgr.transform) 
        callback(skillGo)
    end
end

--- 无尽模式用，重置状态，防止下一关开启时自动释放技能
function cysoldierssortie_comp_hero_drone_weapon_entity:ResetStatus()
    self.isOnePlay = nil
end

function cysoldierssortie_comp_hero_drone_weapon_entity:Fire()
  --  local log = require "log"
 --  log.Error("cysoldierssortie_comp_hero_drone_weapon_entity="..self._release_skill_time)
    self._release_skill_time=1.2
    if self._weaponData and self._weaponData._skillID==1002 then --第二个神兽
        self._release_skill_time=2.2
    end
    --self:PlayShowEffectXYZ()

    if not self.isOnePlay then
        self.isOnePlay=true
        minigame_mgr.SetWeaponProgress(self._weaponData._attackSpeed)
        return
    end
    minigame_mgr.ShowWeaponAnim(true)
    local skillCdTimer=0.2
    self._fire_timer = cysoldierssortie_DelayCallOnce(self._release_skill_time, function()
        minigame_mgr.SetWeaponProgress(self._weaponData._attackSpeed-self._release_skill_time)
      --  minigame_mgr.ShowWeaponAnim(false)
        cysoldierssortie_comp_base_weapon_entity.Fire(self)
        local angleSpread = 75       -- Total spread angle in degrees
        -- Calculate the center angle (character's forward direction)
        local minBullets = 10           -- Minimum number of bullets
        local maxBullets = 10           -- Maximum number of bullets
        local bulletCount = math.random(minBullets, maxBullets)
        local weaponR=bc_CS_Vector3(100,90,-60)
        if self._weaponData and self._weaponData._skillID==1002 then --第二个神兽
           -- bulletCount=1
           -- weaponR=bc_CS_Vector3(90,0,0)
        end
        for i = 1, bulletCount do
            cysoldierssortie_DelayCallOnce((i-1)*skillCdTimer, function()
                local rX = math.random(-3, 3)
                local rZ = math.random(7, 18)
                if bulletCount==1 then
                    rX=0
                    rZ=11
                end
                local x=rX
                local y=9
                local z=rZ
                local weaponRoot=bc_CS_Vector3(rX,9,rZ)
               -- self._character._weaponRoot.transform.localPosition=weaponRoot
                --local x,y,z = ApiHelper.GetTransformForwardXYZ(self._character._weaponRoot)
                local centerAngle = math.atan2(z, x) * Mathf.Rad2Deg
                self:FireBullet(centerAngle,angleSpread,weaponRoot,bulletCount,weaponR)
            end)
        end
        cysoldierssortie_DelayCallOnce((bulletCount-1)*skillCdTimer+0.7, function()
            minigame_mgr.ShowWeaponAnim(false)
       end)
    end)

end



function cysoldierssortie_comp_hero_drone_weapon_entity:PlayShowEffectXYZ()
    self.levelMgr = self.levelMgr or cysoldierssortie_GetMgr(cysoldierssortie_MgrName.level)
    self.curLevel=self.levelMgr.curLevel
    local path=self._weaponData._showEffectsPath
    if  path==nil or path=="" then
        return
    end
    --播放大招特效
    bc_load_mgr.LoadRes(path,function(obj)
        if obj  then
            local function callback(gameObject)
                local pX, pY, pZ= ApiHelper.GetTransformPositionXYZ(self.curLevel.playerLua.transform)
                ApiHelper.SetTransformPositionXYZ(gameObject.transform,0,0,pZ)
                cysoldierssortie_DelayCallOnce(2,function()
                    if not  cysoldierssortie_IsNil(gameObject) then
                        UnityEngine.GameObject.Destroy(gameObject)
                    end

                end)
            end
            if isAsync then
                local AsyncOp = ApiHelper.InstantiateAsync(obj, self.curLevel.transform, false)
                AsyncOp:completed("+", function()
                    local gameObject = AsyncOp.Result[0]
                    callback(gameObject)
                end)
            else
                local gameObject= UnityEngine.GameObject.Instantiate(obj, self.curLevel.transform, false)
                callback(gameObject)
            end
        end

    end)

end


return cysoldierssortie_comp_hero_drone_weapon_entity