local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_hero_fire_weapon_entity = bc_Class("cysoldierssortie_comp_hero_fire_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local CSUpdateEvent = cysoldierssortie_EventName.CSUpdate
local bc_Time = bc_Time
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetTransformByTransform = ApiHelper.SetTransformByTransform
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local SetTransformLocalPositionAndLocalRotation = ApiHelper.SetTransformLocalPositionAndLocalRotation
local SetTransformLocalScale = ApiHelper.SetTransformLocalScale
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local cysoldierssortie_damage_entity_shape_type = cysoldierssortie_damage_entity_shape_type
local isAsync = isAsync

local DamageFrequency = 0.1 --伤害频率 0.1s一次造成伤害
local FireTime = 3 --喷火总间隔
local FireState = 
{
    Idle = 1,
    Attack = 2,
}
function cysoldierssortie_comp_hero_fire_weapon_entity.__init(self)
    local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    eventMgr:RegisterEvt(self,CSUpdateEvent)
end

function cysoldierssortie_comp_hero_fire_weapon_entity.__delete()
    local eventMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.event)
    eventMgr:UnRegisterEvt(self,CSUpdateEvent)
    self:ReleaseEffect()
end

function cysoldierssortie_comp_hero_fire_weapon_entity:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
    self._damageFrequency = self._weaponData._typeParameter1 and (self._weaponData._typeParameter1 / 10000) or DamageFrequency
    self._fireTime = self._weaponData._typeParameter2 and (self._weaponData._typeParameter2 / 10000) or FireTime
    self._damageShapeType = data.damageShapeType
end

local paramCache = {}
function cysoldierssortie_comp_hero_fire_weapon_entity:CreateDamageEntity()
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local root = self._character.transform

    local function callback(damageEntity)
        if damageEntity then
            if self._damageShapeType and self._damageShapeType == cysoldierssortie_damage_entity_shape_type.Rect then
                local zOffset =   self._weaponData._damageRange / 100 / 2
                SetTransformLocalPositionAndLocalRotation(damageEntity.transform,0,self._character._boundHeight,zOffset,0,0,0)
            else
                SetTransformLocalPositionAndLocalRotation(damageEntity.transform,0,self._character._boundHeight,0,0,0,0)
            end
                
                paramCache.character = self._character
                paramCache.attack = self._character._attack * (self._weaponData._damageCoefficient/10000)
                paramCache.damageRange = self._weaponData._damageRange
                paramCache.recycle_time = math.max(bc_Time.deltaTime, self._damageFrequency - bc_Time.deltaTime)
                paramCache.critical = self._weaponData._criticalHit
                paramCache.maxDamageNum = self._weaponData._maxDamageNum
                paramCache.damageType = self._weaponData._damageType
                paramCache.isUltra = self._weaponData._isUltra
                paramCache.damageShapeType =  self._damageShapeType
                damageEntity:CreateData(paramCache)
        end
    end
    if isAsync then
        poolMgr:AcquireObjAsync("cysoldierssortie_comp_on_damage_entity", callback, root)
    else
        local damageEntity = poolMgr:AcquireObj("cysoldierssortie_comp_on_damage_entity",root)
        callback(damageEntity)
    end
end

function cysoldierssortie_comp_hero_fire_weapon_entity:CreateEffect()
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    local root = self._character._weaponRoot
    if poolMgr and self._weaponData._skillEffectPath then
        poolMgr:CreateEntityAsync(self._weaponData._skillEffectPath,root,function(go)
            self._effect = go
            local trans = self._effect.transform
            SetTransformLocalScale(trans,self._weaponData._bulletScale,self._weaponData._bulletScale,self._weaponData._bulletScale)
            if self._damageShapeType and self._damageShapeType == cysoldierssortie_damage_entity_shape_type.Rect then
                SetTransformLocalPositionAndLocalRotation(trans,0,0,0.8, 0,0,0)
            else
                SetTransformLocalPositionAndLocalRotation(trans,0,0,0, 0,0,0)
            end
        end)
    end
end

function cysoldierssortie_comp_hero_fire_weapon_entity:CSUpdate()
    if not self._fireState or self._fireState == FireState.Idle then
        return
    end
    
    if not self._fireTimer or bc_Time.time >= self._fireTimer  then
        self._fireTimer = bc_Time.time + self._damageFrequency
        self:CreateDamageEntity()
    end
end

function cysoldierssortie_comp_hero_fire_weapon_entity:ReleaseEffect()
    if self._effect then
        NeeGame.ReturnObject(self._effect,true,false)
        self._effect = nil
    end
end

function cysoldierssortie_comp_hero_fire_weapon_entity:FireBullet()
    self._fireState = FireState.Attack
    self:CreateEffect()
    cysoldierssortie_DelayCallOnce(self._fireTime,function()
        self._fireState = FireState.Idle
        self:ReleaseEffect()
    end)
end

function cysoldierssortie_comp_hero_fire_weapon_entity:Fire()
    self._fire_timer = cysoldierssortie_DelayCallOnce(self._release_skill_time,function()
        cysoldierssortie_comp_base_weapon_entity.Fire(self)
        self:FireBullet()
    end)
end

function cysoldierssortie_comp_hero_fire_weapon_entity:OnDisable()
    cysoldierssortie_comp_base_weapon_entity.OnDisable(self)
    self:ReleaseEffect()
end

return cysoldierssortie_comp_hero_fire_weapon_entity