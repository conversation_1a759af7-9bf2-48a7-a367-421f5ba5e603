local cysoldierssortie_lua_queue = bc_Class("cysoldierssortie_lua_queue") --类名用小游戏名加后缀保证全局唯一

function cysoldierssortie_lua_queue.__init(self,limitCapacity)
    self._tail = 0
    self._head = 1
    self._data = {}
    self._capacity = limitCapacity
    if not self._capacity then
        self._capacity = false
    end
end

local QUEUE_CAPCITY = 10
-- 入队（添加到队尾）
function cysoldierssortie_lua_queue:Enqueue(value)
    if self._capacity then
        local size = self:Size()
        if size > QUEUE_CAPCITY then
            return
        end
    end
    
    self._tail = self._tail + 1
    self._data[self._tail] = value
end

-- 出队（移除队头元素）
function cysoldierssortie_lua_queue:Dequeue()
    if self._head > self._tail then
        return nil  -- 空队列
    end
    local value = self._data[self._head]
    self._head = self._head + 1
    return value
end

-- 获取队列长度
function cysoldierssortie_lua_queue:Size()
    return self._tail - self._head + 1
end

return cysoldierssortie_lua_queue