local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_hero_saiweiyala_weapon_entity = bc_Class("cysoldierssortie_comp_hero_saiweiyala_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local math = math
local PoolObject = NeeGame.PoolObject
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName

local cysoldierssortie_hero_anim_set = cysoldierssortie_hero_anim_set

local ApiHelper = CS.XLuaUtil.LuaApiHelper
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local log = log
local isAsync = isAsync

function cysoldierssortie_comp_hero_saiweiyala_weapon_entity:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
end

local skillParam = {}
function cysoldierssortie_comp_hero_saiweiyala_weapon_entity:FireBullet()
    -- Generate a random angle within the fan
    local randomOffset = (math.random() *2-1)*0.5
    
    -- Spawn the attack at the correct position and direction
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)

    local function callback(skillGo)
        if skillGo then
            local posX,posY,posZ =  GetTransformPositionXYZ(self._character._weaponRoot)
            SetTransformPositionXYZ(skillGo.transform,posX+randomOffset,posY,posZ)
            skillGo.transform.rotation = self._character._weaponRoot.rotation

            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)
            skillParam.character=self._character
            skillParam.ballisiticVelocity = self._weaponData._ballisiticVelocity
            skillParam.ballisiticRange = self._weaponData._ballisiticRange
            skillParam.attack = self._character._attack * (self._weaponData._damageCoefficient/10000)
            local coe =  self._character:GetStarLvAddCoefficient(self._weaponData)
            if coe then
                skillParam.attack = skillParam.attack + skillParam.attack * coe
            end
            skillParam.damageRange = self._weaponData._damageRange
            skillParam.attackPentration = self._weaponData._pierce
            skillParam.critical  = self._weaponData._criticalHit
            skillParam.bulletScale = self._weaponData._bulletScale
            skillParam.skillPath = self._weaponData._skillEffectPath
            skillParam.dstEffectsPath = self._weaponData._dstEffectsPath
            skillParam.skillID = self._weaponData._skillID
            skillParam.maxDamageNum = self._weaponData._maxDamageNum
            skillParam.damageType = self._weaponData._damageType
            skillParam.isUltra = self._weaponData._isUltra
            skillEntity:CreateData(skillParam)
        end
    end
    if isAsync then
        poolMgr:AcquireObjAsync("cysoldierssortie_comp_base_bullet_entity",callback,poolMgr.transform)
    else
        local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity",poolMgr.transform)
        callback(skillGo)
    end
end

function cysoldierssortie_comp_hero_saiweiyala_weapon_entity:Fire()
    self._fire_timer = cysoldierssortie_DelayCallOnce(self._release_skill_time, function()
        cysoldierssortie_comp_base_weapon_entity.Fire(self)
        local minBullets = 5          -- Minimum number of bullets
        local maxBullets = 7           -- Maximum number of bullets
        local bulletCount = math.random(minBullets, maxBullets)
        for i = 1, bulletCount do
            cysoldierssortie_DelayCallOnce((i-1)*0.085, function()
                self:FireBullet()
            end)
        end
    end)

end
return cysoldierssortie_comp_hero_saiweiyala_weapon_entity