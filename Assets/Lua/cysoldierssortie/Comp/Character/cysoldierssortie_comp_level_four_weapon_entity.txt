local cysoldierssortie_comp_base_weapon_entity = require("cysoldierssortie_comp_base_weapon_entity")
local cysoldierssortie_comp_level_four_weapon_entity = bc_Class("cysoldierssortie_comp_level_four_weapon_entity",cysoldierssortie_comp_base_weapon_entity) --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local PoolObject = NeeGame.PoolObject
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local bc_CS_Vector3 = bc_CS_Vector3
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local isAsync = isAsync

function cysoldierssortie_comp_level_four_weapon_entity:CreateData(data)
    --self._class_type.super:CreateData(data)
    cysoldierssortie_comp_base_weapon_entity.CreateData(self,data)
end

local skillParam = {}
function cysoldierssortie_comp_level_four_weapon_entity:Fire()
    self._fire_timer = cysoldierssortie_DelayCallOnce(self._release_skill_time, function()
        for i=1,3 do
            cysoldierssortie_DelayCallOnce((i-1)*0.1,function()
                local poolMgr =  cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
                if not poolMgr then
                    return
                end
                for j=1 ,2 do
                    cysoldierssortie_comp_base_weapon_entity.Fire(self)

                    local function callback(skillGo)
                        if skillGo then
                            if j==1 then
                                skillGo.transform.position = self._character._weaponRoot.position+bc_CS_Vector3(-0.3,0,0)
                            else
                                skillGo.transform.position = self._character._weaponRoot.position+bc_CS_Vector3(0.3,0,0)
                            end
                            
                            skillGo.transform.rotation = self._character._weaponRoot.rotation

                            local skillEntity = cysoldierssortie_GetLuaComp(skillGo.gameObject)

                            skillParam.character=self._character
                            skillParam.ballisiticVelocity = self._weaponData._ballisiticVelocity
                            skillParam.ballisiticRange = self._weaponData._ballisiticRange
                            skillParam.attack = self._character._attack * (self._weaponData._damageCoefficient/10000)
                            local coe =  self._character:GetStarLvAddCoefficient(self._weaponData)
                            if coe then
                                skillParam.attack = skillParam.attack + skillParam.attack * coe
                            end
                            skillParam.damageRange = self._weaponData._damageRange
                            skillParam.attackPentration = self._weaponData._pierce
                            skillParam.critical = self._weaponData._criticalHit
                            skillParam.bulletScale = self._weaponData._bulletScale
                            skillParam.skillPath = self._weaponData._skillEffectPath
                            skillParam.dstEffectsPath = self._weaponData._dstEffectsPath
                            skillParam.skillID = self._weaponData._skillID
                            skillParam.damageType = self._weaponData._damageType
                            skillParam.isUltra = self._weaponData._isUltra
                            skillEntity:CreateData(skillParam)
                        end 
                    end
                    if isAsync then
                        poolMgr:AcquireObjAsync("cysoldierssortie_comp_base_bullet_entity",callback,poolMgr.transform)
                    else
                        local skillGo = poolMgr:AcquireObj("cysoldierssortie_comp_base_bullet_entity",poolMgr.transform)
                        callback(skillGo)
                    end
                end
            end)
        end
    end)
end

return cysoldierssortie_comp_level_four_weapon_entity