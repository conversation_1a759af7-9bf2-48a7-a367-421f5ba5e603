local cysoldierssortie_comp_base_bullet_entity = bc_Class("cysoldierssortie_comp_base_bullet_entity") --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local UnityEngine = CS.UnityEngine
local vector3_one = bc_CS_Vector3.one
local quaternion_identity = bc_CS_Quaternion.identity
local BulletSystem = CS.cysoldierssortie.BulletSystem

local GCPerf = GCPerf
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local ClosestPointXYZ = ApiHelper.ClosestPointXYZ
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local SetTransformLocalScale = ApiHelper.SetTransformLocalScale
local cysoldierssortie_comp_on_damage_entity = require("cysoldierssortie_comp_on_damage_entity")
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local PoolObject = NeeGame.PoolObject
local bullet_collider_size_set = bullet_collider_size_set
local cysoldierssortie_unit_target_layer_Int = cysoldierssortie_unit_target_layer_Int
local bc_Time = bc_Time
local cysoldierssortie_unit_layer = cysoldierssortie_unit_layer
local cysoldierssortie_PlaySkillDst = cysoldierssortie_PlaySkillDst
local cysoldierssortie_TagName = cysoldierssortie_TagName
local SetTransformLocalPositionAndLocalRotation = ApiHelper.SetTransformLocalPositionAndLocalRotation
local cysoldierssortie_LayerName = cysoldierssortie_LayerName
local log = log
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local GetTransformForwardXYZ = ApiHelper.GetTransformForwardXYZ
local bc_IsNotNull  = bc_IsNotNull
local SetLuaCompCache = SetLuaCompCache
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_StopDelayCall = cysoldierssortie_StopDelayCall
local cysoldierssortie_urp_ecs = cysoldierssortie_urp_ecs
local entity_manager = require("entity_manager")
local EntityHybridUtility = CS.Unity.Entities.EntityHybridUtility
local isAsync = isAsync
local cysoldierssortie_entity_loader_batch = require("cysoldierssortie_entity_loader_batch")

function cysoldierssortie_comp_base_bullet_entity.__init(self,parent,warmup)
    if warmup then
        return
    end
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(true)
        return
    end
    
    self._curParent = parent
    self._registerCollider = false
    if not bc_IsNotNull(self._curParent) then
        self._curParent = poolMgr.transform
    end
    
    if not isAsync then
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:CreateEntity(cysoldierssortie_PoolObjectName.BaseBulletEntity,self._curParent,
                function(bullet_go)
                    self.transform = poolMgr:GetTransform(bullet_go)
                    self.gameObject = bullet_go
                    self._modelRoot = poolMgr:GetModelRoot(bullet_go)
                    self._collider = poolMgr:GetCollider(bullet_go)
                    SetLuaCompCache(bullet_go,self)
                end)
        end

        self.OnTriggerEnterRegister= function(collider)
            self:OnTriggerEnter(collider)
        end
        
        self._registerCollider =poolMgr:ColliderListener(self.gameObject)
        if bc_IsNotNull(self._registerCollider) then
            self._registerCollider:RegisterTriggerEnter(self.OnTriggerEnterRegister)
        end
        self.cysoldierssortie_TroopClash = cysoldierssortie_TroopClash
        self.cysoldierssortie_firstRecharge_Normal = cysoldierssortie_firstRecharge_Normal
        self.cysoldierssortie_firstRecharge_Endless = cysoldierssortie_firstRecharge_Endless
    end
end

function  cysoldierssortie_comp_base_bullet_entity.__delete(self)
    self._invalid = true
    if self._recycleTimer then
        cysoldierssortie_StopDelayCall(self._recycleTimer)
        self._recycleTimer = nil
    end
    if self._isBulletSystem ~= false then
        BulletSystem.Instance:UnregisterBullet(self.transform) 
    end
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(false)
    end
    if self._entity then
        SetTransformPositionXYZ(self.transform,1000,1000,1000)
        EntityHybridUtility.SetEnable(self._entity,false)
    else
        if self._requestMapID then
            cysoldierssortie_entity_loader_batch.Dispose(self._requestMapID)
            self._requestMapID = nil
        end
    end
end

function cysoldierssortie_comp_base_bullet_entity:CreateBaseEntityAsync(cb)
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(true)
        return
    end
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    poolMgr:CreateEntityAsync(cysoldierssortie_PoolObjectName.BaseBulletEntity,self._curParent,
                function(bullet_go)
                    self.transform = poolMgr:GetTransform(bullet_go)
                    self.gameObject = bullet_go
                    self._modelRoot = poolMgr:GetModelRoot(bullet_go)
                    self._collider = poolMgr:GetCollider(bullet_go)
                    SetLuaCompCache(bullet_go,self)

                    self.OnTriggerEnterRegister= function(collider)
                        self:OnTriggerEnter(collider)
                    end
                    
                    self._registerCollider =poolMgr:ColliderListener(self.gameObject)
                    if bc_IsNotNull(self._registerCollider) then
                        self._registerCollider:RegisterTriggerEnter(self.OnTriggerEnterRegister)
                    end
                    self.cysoldierssortie_TroopClash = cysoldierssortie_TroopClash
                    self.cysoldierssortie_firstRecharge_Normal = cysoldierssortie_firstRecharge_Normal
                    self.cysoldierssortie_firstRecharge_Endless = cysoldierssortie_firstRecharge_Endless
                    if cb then
                        cb(self)
                    end
                end)
end

function cysoldierssortie_comp_base_bullet_entity:OnPopFormPool(parent)
    self.__init(self,parent)
end

function cysoldierssortie_comp_base_bullet_entity:OnPushIntoPool()
    self.__delete(self)
end

function cysoldierssortie_comp_base_bullet_entity:Clean()
    self:ReleaseEntity()
    
    if self._requestMapID then
        cysoldierssortie_entity_loader_batch.Dispose(self._requestMapID)
        self._requestMapID = nil
    end
end

function cysoldierssortie_comp_base_bullet_entity:InitLayer()
    if not self._cacheLayer or (self._cacheLayer ~= cysoldierssortie_unit_layer[self._character._unit_type]) then
        self._cacheLayer = cysoldierssortie_unit_layer[self._character._unit_type]
        self.gameObject.layer = self._cacheLayer
    end
end

function cysoldierssortie_comp_base_bullet_entity:CreateData(data)
    self._ballisiticVelocity = data.ballisiticVelocity
    self._ballisiticRange = data.ballisiticRange
    self._attack = data.attack
    self._damageRange = data.damageRange
    self._trigger_counter = 0
    self._recycle_time = (self._ballisiticRange/self._ballisiticVelocity)
    self._attackPentration = data.attackPentration
    self._critical = data.critical
    self._dstEffectsPath = data.dstEffectsPath
    self._skillPath = data.skillPath
    self._bullet_scale = data.bulletScale
    self._character = data.character
    self._skillID= data.skillID
    self._maxDamageNum = data.maxDamageNum
    self._onlyTriggerGround = data.onlyTriggerGround
    self._feedBack = data.feedBack
    self._invalid = false
    self._damageType = data.damageType
    self._isUltra = data.isUltra
    
    self:InitColliderData()
    self:CreateEntity()
    self:InitLayer()
    if self._isBulletSystem ~= false then
        BulletSystem.Instance:RegisterBullet(self.transform,self._ballisiticVelocity,bc_Time.time + self._recycle_time)
    end
    if self._isAutoRecycle ~= false then
        self._recycleTimer = cysoldierssortie_DelayCallOnce(self._recycle_time,function()
            local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
            if not poolMgr then
                return
            end
            poolMgr:ReleaseObj(self)
            self._recycleTimer = nil
        end)
    end
end

local NormalCenter = {x=0,y=0,z=0}
local NormalSize = {x=1,y=1,z=1}
function cysoldierssortie_comp_base_bullet_entity:InitColliderData()
    if not self._curSkillPath or self._skillPath ~= self._curSkillPath then
        local tmpCenter = bullet_collider_size_set[self._skillPath].center or NormalCenter
        local tmpSize = bullet_collider_size_set[self._skillPath].size or NormalSize
        if self.cysoldierssortie_TroopClash and tmpCenter.z > tmpSize.z * 0.5 then
            tmpCenter.z = tmpSize.z * 0.5
        end
        self._collider.center = tmpCenter
        self._collider.size = tmpSize
    end
end

function cysoldierssortie_comp_base_bullet_entity:CreateEntity()
    if not self._skillPath then
        return
    end

    if self._curSkillPath and (self._curSkillPath == self._skillPath) then
        if cysoldierssortie_urp_ecs and entity_manager.URP22 and  self._character._unit_type == cysoldierssortie_unit_type.Soldier then
            if self._entity then
                EntityHybridUtility.SetEnable(self._entity,true)
                self:OnCreateEffectEntityFinish()
                return
            end
        end
        if self._last_bullet_scale and (self._last_bullet_scale~=self._bullet_scale) then
            if bc_IsNotNull(self._effect) then
                SetTransformLocalScale(self._effect.transform,self._bullet_scale,self._bullet_scale,self._bullet_scale)
                self._last_bullet_scale = self._bullet_scale
            end
        end
        self:OnCreateEffectEntityFinish()
        return
    end
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if poolMgr then
        if cysoldierssortie_urp_ecs and entity_manager.URP22 and  self._character._unit_type == cysoldierssortie_unit_type.Soldier then
            self:ReleaseEntity()
                self._requestMapID =  poolMgr:CreateEntity(self._skillPath,self._modelRoot,
                        function(resPath,entity,rootGo)
                            self._curSkillPath = self._skillPath
                            self:ReleaseEntity()
                            self._entity = entity 
                            if self._requestMapID then
                                 self._requestMapID = nil
                            end
                            self._last_bullet_scale = self._bullet_scale
                            local localPos =  bullet_collider_size_set[self._skillPath].pos or vector3_one
                            SetTransformLocalScale(self._modelRoot,self._last_bullet_scale,self._last_bullet_scale,self._last_bullet_scale)
                            SetTransformLocalPositionAndLocalRotation(self._modelRoot,localPos.x,localPos.y,localPos.z,0,0,0)
                            EntityHybridUtility.AttachToTransform(entity,self._modelRoot) 
                            self:OnCreateEffectEntityFinish()
                        end,nil,nil,nil,true,nil,0)
        else
            self:ReleaseEntity()
            local splitFrame = false
            if self._character._unit_type == cysoldierssortie_unit_type.Soldier or self._character._unit_type == cysoldierssortie_unit_type.Soldier2 or
                    self._character._unit_type == cysoldierssortie_unit_type.NormalEnemy then
                splitFrame = true
            end
            poolMgr:CreateEntityAsync(self._skillPath,self._modelRoot,function(go)
                self._curSkillPath = self._skillPath
                self:ReleaseEntity()
                self._effect = go
                local trans = self._effect.transform
                self._last_bullet_scale = self._bullet_scale
                SetTransformLocalScale(trans,self._bullet_scale,self._bullet_scale,self._bullet_scale)
                local localPos =  bullet_collider_size_set[self._skillPath].pos or vector3_one
                local res = xpcall(function()
                    SetTransformLocalPositionAndLocalRotation(trans,localPos.x,localPos.y,localPos.z,0,0,0)
                end,debug.traceback)

                if not res then
                    trans.localRotation = quaternion_identity
                    trans.localPosition = bullet_collider_size_set[self._skillPath].pos or vector3_one
                end
                self:OnCreateEffectEntityFinish()
            end,false,splitFrame,self)
        end
    end
end

function cysoldierssortie_comp_base_bullet_entity:OnCreateEffectEntityFinish()
    
end

function cysoldierssortie_comp_base_bullet_entity:ReleaseEntity()
    if self._entity then
        if bc_IsNotNull(self._modelRoot) then
            SetTransformLocalScale(self._modelRoot,1,1,1)
            SetTransformLocalPositionAndLocalRotation(self._modelRoot,0,0,0,0,0,0)
        end
        local instanceID = EntityHybridUtility.DestroyEntity(self._entity)
        if instanceID ~= 0 then
            local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
            poolMgr:DisposeEntity(instanceID)
        end
        self._entity = nil
    end
    
    if self._effect then
        if isAsync then
            NeeGame.ReturnObjectAsync(self._effect)
        else
            NeeGame.ReturnObject(self._effect)
        end
        self._effect = nil
    end

end

local EffectParamCache = {}
function cysoldierssortie_comp_base_bullet_entity:PlayHitEffectXYZ(pX, pY, pZ)
    --播放通用击中特效
    local universal_hit_res_path = ""
    if self._dstEffectsPath then
        universal_hit_res_path = self._dstEffectsPath
        local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
        EffectParamCache.auto_release = true
        EffectParamCache.delay_release = 0.5
        EffectParamCache.effect_path = universal_hit_res_path
        EffectParamCache.callBack = function(hit_go)
            SetTransformPositionXYZ(hit_go.transform, pX, pY, pZ)
        end
        EffectParamCache.maxWeightLimit = true
        effect_mgr:CreateEffect(EffectParamCache)
    else
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if isAsync then
            poolMgr:AcquireObjAsync("cysoldierssortie_universal_behit",nil,pX,pY,pZ)
        else
            poolMgr:AcquireObj("cysoldierssortie_universal_behit",pX,pY,pZ)
        end
    end
end

local paramCache = {}
function cysoldierssortie_comp_base_bullet_entity:OnTriggerEnter(collider)
    local colliderLayer = collider.gameObject.layer
    -- 小兵大作战子弹忽略道具碰撞
    if (self.cysoldierssortie_TroopClash or self.cysoldierssortie_firstRecharge_Normal or self.cysoldierssortie_firstRecharge_Endless) and colliderLayer == cysoldierssortie_LayerName.Prop then
        return
    end

    if self._maxDamageNum and self._maxDamageNum >0 then
        if self._trigger_counter >= self._maxDamageNum then
            return
        end
    end

    if self._onlyTriggerGround then
        if colliderLayer ~= cysoldierssortie_LayerName.L12 then
            return
        end
    end

    if self._feedBack then
        self._feedBack()
    end
    
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if GCPerf then
        local targetGo = collider.gameObject
        local tag = targetGo.tag
        if tag == cysoldierssortie_TagName.missileBullet or tag == cysoldierssortie_TagName.invicibleTag then
            return
        end
        local takeDamagePointX, takeDamagePointY, takeDamagePointZ = GetTransformPositionXYZ(self.transform)--ClosestPointXYZ(collider, self.transform)
        local forwardX,forwardY,forwardZ = GetTransformForwardXYZ(self.transform)
        local size =  bullet_collider_size_set[self._skillPath].size
        takeDamagePointZ = takeDamagePointZ + size.z * forwardZ
        takeDamagePointX = takeDamagePointX + size.x * forwardX
        
        if self._damageRange<=2 then
            if targetGo.layer== cysoldierssortie_unit_target_layer_Int[self._character._unit_type] then
                local targetEntity =   cysoldierssortie_GetLuaComp(targetGo)
                cysoldierssortie_comp_on_damage_entity:OnDamage(tag,self._critical,self._attack,targetEntity,takeDamagePointX,takeDamagePointY,
                takeDamagePointZ,self._character,self._damageType,self._isUltra)
            end
        else
            local function callback(damageEntity)
                if self._character._isDrone then--神兽位置直接固定
                    SetTransformPositionXYZ(damageEntity.transform, 0, 0, 10)
                else
                    SetTransformPositionXYZ(damageEntity.transform, takeDamagePointX, takeDamagePointY, takeDamagePointZ)
                end
                
                if damageEntity then
                    paramCache.character = self._character
                    paramCache.attack = self._attack
                    paramCache.damageRange = self._damageRange
                    paramCache.recycle_time = 0.1
                    paramCache.critical = self._critical
                    paramCache.maxDamageNum = self._maxDamageNum
                    paramCache.damageType = self._damageType
                    paramCache.isUltra = self._isUltra
                    damageEntity:CreateData(paramCache)
                end
            end
            if isAsync then
                poolMgr:AcquireObjAsync("cysoldierssortie_comp_on_damage_entity",callback,poolMgr.transform)
            else
                local damageEntity = poolMgr:AcquireObj("cysoldierssortie_comp_on_damage_entity",poolMgr.transform)
                callback(damageEntity)
            end
        end
        
        self._trigger_counter = self._trigger_counter+1
        
        --播放通用击中特效
        self:PlayHitEffectXYZ(takeDamagePointX,takeDamagePointY,takeDamagePointZ)
        cysoldierssortie_PlaySkillDst(self._skillID)
        if self._trigger_counter>=self._attackPentration then
            if not poolMgr then
                return
            end
            poolMgr:ReleaseObj(self)
        end
    end
end


return cysoldierssortie_comp_base_bullet_entity