local cysoldierssortie_comp_on_damage_entity = bc_Class("cysoldierssortie_comp_on_damage_entity") --类名用小游戏名加后缀保证全局唯一
local NeeGame = CS.CasualGame.lib_ChuagnYi.NeeG.NeeGame
local UnityEngine = CS.UnityEngine
local SphereCollider = typeof(CS.UnityEngine.SphereCollider)
local typeof = typeof
local math = math
local cysoldierssortie_GetLuaComp = cysoldierssortie_GetLuaComp
local cysoldierssortie_GetMgr = cysoldierssortie_GetMgr
local cysoldierssortie_MgrName = cysoldierssortie_MgrName
local ApiHelper = CS.XLuaUtil.LuaApiHelper
local ClosestPointXYZ = ApiHelper.ClosestPointXYZ
local SetTransformPositionXYZ = ApiHelper.SetTransformPositionXYZ
local cysoldierssortie_DelayCallOnce = cysoldierssortie_DelayCallOnce
local cysoldierssortie_KillTimer = cysoldierssortie_KillTimer
local cysoldierssortie_TagName = cysoldierssortie_TagName
local bc_IsNotNull = bc_IsNotNull
local cysoldierssortie_unit_target_layer_Int = cysoldierssortie_unit_target_layer_Int
local cysoldierssortie_unit_layer = cysoldierssortie_unit_layer
local cysoldierssortie_unit_type = cysoldierssortie_unit_type
local GetTransformPositionXYZ = ApiHelper.GetTransformPositionXYZ
local minigame_mgr = require "minigame_mgr"
local minigame_buff_mgr = require "minigame_buff_mgr"
local log = require "log"
local cysoldierssortie_PoolObjectName = cysoldierssortie_PoolObjectName
local SetLuaCompCache = SetLuaCompCache
local cysoldierssortie_damage_entity_shape_type = cysoldierssortie_damage_entity_shape_type
local BoxCollider = CS.UnityEngine.BoxCollider
local isAsync = isAsync

function cysoldierssortie_comp_on_damage_entity.__init(self,parent)
    if bc_IsNotNull(self.gameObject) then
        if self._curParent ~=parent then
            ApiHelper.SetParent(self.transform,parent)
            self._curParent = parent
        end
        self.gameObject:SetActive(true)
        return
    end

    self._curParent = parent
    self._registerCollider = false
    if not bc_IsNotNull(self._curParent) then
        self._curParent = poolMgr.transform
    end

    if not isAsync then
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:CreateEntity(cysoldierssortie_PoolObjectName.TakeDamageEntity,self._curParent,
                    function(go)
                        self.transform = poolMgr:GetTransform(go)
                        self.gameObject = go
                        SetLuaCompCache(go,self)
                    end)
        end

        self.OnTriggerEnterRegister= function(collider)
            self:OnTriggerEnter(collider)
        end

        self._registerCollider =poolMgr:ColliderListener(self.gameObject)
        if bc_IsNotNull(self._registerCollider) then
            self._registerCollider:RegisterTriggerEnter(self.OnTriggerEnterRegister)
        end
    end
end


function  cysoldierssortie_comp_on_damage_entity.__delete(self)
    self:Dispose()
end

function cysoldierssortie_comp_on_damage_entity:CreateBaseEntityAsync(cb)
    if bc_IsNotNull(self.gameObject) then
        return
    end
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    poolMgr:CreateEntityAsync(cysoldierssortie_PoolObjectName.TakeDamageEntity,self._curParent,
                function(go)
                    self.transform = poolMgr:GetTransform(go)
                    self.gameObject = go
                    SetLuaCompCache(go,self)

                    self.OnTriggerEnterRegister= function(collider)
                        self:OnTriggerEnter(collider)
                    end
                
                    self._registerCollider =poolMgr:ColliderListener(self.gameObject)
                    if bc_IsNotNull(self._registerCollider) then
                        self._registerCollider:RegisterTriggerEnter(self.OnTriggerEnterRegister)
                    end

                    if cb then
                        cb(self)
                    end
                end)

end

function cysoldierssortie_comp_on_damage_entity:OnPopFormPool(parent)
    self.__init(self,parent)
end

function cysoldierssortie_comp_on_damage_entity:OnPushIntoPool()
    self.__delete(self)
end

-- Function to determine if the attack is critical
function cysoldierssortie_comp_on_damage_entity:IsCriticalHit(criticalChance)
    -- Generate a random number between 0 and 100
    local randomValue = math.random(0, 10000)
    -- Check if the random value is within the critical chance range
    return randomValue < criticalChance
end

local SizeCache = {}
function cysoldierssortie_comp_on_damage_entity:CreateData(data)
    self._attack = data.attack
    self._damageRange = (data.damageRange/100)
    self._recycle_time = data.recycle_time
    self._critical =  data.critical
    self._trigger_counter  = 0
    self._playHitEffect = data.playHitEffect
    self._ignore_pierce = data.ignore_pierce
    self._character=data.character
    self._maxDamageNum = data.maxDamageNum
    self._damageType = data.damageType
    self._isUltra = data.isUltra
    self._damageShapeType = data.damageShapeType or cysoldierssortie_damage_entity_shape_type.Sphere
    self:InitLayer()

    if not bc_IsNotNull(self._sphere_collider) then
        self._sphere_collider = self.gameObject:GetComponent(typeof(SphereCollider))
    end
    if self._damageShapeType == cysoldierssortie_damage_entity_shape_type.Sphere then
        self._sphere_collider.radius = self._damageRange
        if bc_IsNotNull(self._box_collider) then
            if not self._lastDamageShapeType or self._lastDamageShapeType~=self._damageShapeType then
                self._box_collider.enabled = false
                self._sphere_collider.enabled = true
            end
        end
    else
        if not bc_IsNotNull(self._box_collider) then
            self._box_collider =  self.gameObject:AddComponent(typeof(BoxCollider))
            self._box_collider.isTrigger = true
        end
        if bc_IsNotNull(self._sphere_collider) then
            if not self._lastDamageShapeType or self._lastDamageShapeType~=self._damageShapeType then
                self._box_collider.enabled = true
                self._sphere_collider.enabled = false
            end
        end
        SizeCache.x = self._character._boundSize
        SizeCache.y = self._character._boundHeight
        SizeCache.z = self._damageRange
        self._box_collider.size = SizeCache
    end
    self._lastDamageShapeType = self._damageShapeType
    
    self._recycle_timer = cysoldierssortie_DelayCallOnce(self._recycle_time,function()
        local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
        if poolMgr then
            poolMgr:ReleaseObj(self)
        end
        self._recycle_timer = nil
    end)
end

function cysoldierssortie_comp_on_damage_entity:Dispose()
    if bc_IsNotNull(self.gameObject) then
        self.gameObject:SetActive(false)
    end
    
    if self._recycle_timer then
        cysoldierssortie_KillTimer(self._recycle_timer)
        self._recycle_timer = nil
    end
end


local EffectParamCache = {}
function cysoldierssortie_comp_on_damage_entity:PlayHitEffectXYZ(pX, pY, pZ)
    --播放通用击中特效
    local universal_hit_res_path = "cysoldierssortie/shader/behitsequence/behitsheetanimation.prefab"
    if self._dstEffectsPath then
        universal_hit_res_path = self._dstEffectsPath
    end
    local effect_mgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.EffectMgr)
    EffectParamCache.auto_release = true
    EffectParamCache.delay_release = 0.27
    EffectParamCache.effect_path = universal_hit_res_path
    EffectParamCache.callBack = function(go)
        SetTransformPositionXYZ(go.transform, pX, pY, pZ)
    end
    EffectParamCache.maxWeightLimit = true
    effect_mgr:CreateEffect(EffectParamCache)
end

function cysoldierssortie_comp_on_damage_entity:OnDamage(tag,critical, attackValue,targetEntity,takeDamagePointX,takeDamagePointY,takeDamagePointZ,character,damageType,isUltra)
    local isCritical = false
    local propDoor = true
    local damage_type = damageType or 0
    if tag ~= cysoldierssortie_TagName.propsDoor_blue and tag ~= cysoldierssortie_TagName.propsDoor_red then
        isCritical =  self:IsCriticalHit(critical)
        propDoor = false
    end
    local attack = isCritical and attackValue * 2 or attackValue
    if targetEntity and targetEntity.character then
        if character._unit_type == cysoldierssortie_unit_type.Soldier or character._unit_type == cysoldierssortie_unit_type.Soldier2 then
            local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
            attackMgr:AddDamageBatch(targetEntity.character,attack)
            return
        end

        attack= minigame_buff_mgr.GetAddBossHarmAttribute(character,targetEntity.character,attack)
        targetEntity.character:BeHit(attack,isCritical,takeDamagePointX,takeDamagePointY,takeDamagePointZ,damage_type,isUltra)
    else
        if targetEntity and targetEntity.BeHit then
            if character._unit_type == cysoldierssortie_unit_type.Soldier or character._unit_type == cysoldierssortie_unit_type.Soldier2 then
                if not propDoor then
                    local attackMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.AttackMgr)
                    attackMgr:AddDamageBatch(targetEntity,attack)
                    return
                end
            end
            targetEntity:BeHit(attack,isCritical) 
        end
    end
 
    if character then
        minigame_mgr.AddHeroHurt(character,attack)
    end
end

function cysoldierssortie_comp_on_damage_entity:InitLayer()
    if not self._cacheLayer or (self._cacheLayer ~= cysoldierssortie_unit_layer[self._character._unit_type]) then
        self._cacheLayer = cysoldierssortie_unit_layer[self._character._unit_type]
        self.gameObject.layer = self._cacheLayer
    end
end
function cysoldierssortie_comp_on_damage_entity:OnTriggerEnter(collider)
    local poolMgr = cysoldierssortie_GetMgr(cysoldierssortie_MgrName.PoolMgr)
    if not self._damageRange or self._damageRange <= 0.1 then
        if self._trigger_counter >= 1 and not self._ignore_pierce then
            if not poolMgr then
                return
            end
            poolMgr:ReleaseObj(self)
            return
        end
    elseif  self._maxDamageNum  and self._maxDamageNum > 0 then
        if self._trigger_counter >=self._maxDamageNum then
            if not poolMgr then
                return
            end
            poolMgr:ReleaseObj(self)
            return
        end
    end
    local targetGo = collider.gameObject
    if targetGo.layer== cysoldierssortie_unit_target_layer_Int[self._character._unit_type] then
        local targetEntity =   cysoldierssortie_GetLuaComp(targetGo)
        if targetEntity then
            local tag = collider.gameObject.tag
            if tag == cysoldierssortie_TagName.missileBullet or tag == cysoldierssortie_TagName.invicibleTag then
                return
            end
            self._trigger_counter = self._trigger_counter + 1
            local takeDamagePointX, takeDamagePointY, takeDamagePointZ =  GetTransformPositionXYZ(collider.transform)
            self:OnDamage(tag,self._critical,self._attack,targetEntity,takeDamagePointX,takeDamagePointY,takeDamagePointZ,self._character,self._damageType,self._isUltra)

            if self._playHitEffect then
                self:PlayHitEffectXYZ(takeDamagePointX,takeDamagePointY,takeDamagePointZ)
            end
        end
    end
end

return cysoldierssortie_comp_on_damage_entity