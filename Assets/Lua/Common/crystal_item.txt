--简易头像 只有头像、品质框
local require = require
local typeof = typeof
local type = type
local game_scheme = require "game_scheme"
local sprite_asset = require "card_sprite_asset"
local log = require "log"
local util = require "util"
local typeof = typeof
local string = string
local table = table
local tostring = tostring
local Vector3 = CS.UnityEngine.Vector3
local UIUtil = CS.Common_Util.UIUtil
local SpriteSwitcher =       CS.War.UI.SpriteSwitcher

module("crystal_item")

local entityResPath = "ui/prefabs/gw/gw_common/crystal_item.prefab"

local M = {}

M.widget_table = 
{
	-- 通用UI
	newiconImg = {path = "icon", type = "Image"},                        -- 头像图标
	frame = {path = "Frame", type = "Image"},
    featureTypeFlag = {path = "featureTypeFlag", type = SpriteSwitcher},
    numText = {path = "numText", type = "Text"},
    button = {path = "", type = "Button"},
    starRoot = {path = "starRoot", type = "RectTransform"},
    mask = {path = "mask", type = "RectTransform"},
    planBg = {path = "planBg", type = "RectTransform"},
    planID = {path = "planBg/planID", type = "Text"},
    chooseMask = {path = "chooseMask", type = "RectTransform"},
    }


function M:ctor(selfType)
    
    faceSpriteAsset = faceSpriteAsset or sprite_asset.CreateSpriteAsset()
    self.crystalID = nil             -- 怪物配置id
    self.crystalCfg = nil            -- 怪物配置
    self.clickCallback = nil
    self.isShowNum = false
    self.isShowfeatureTypeFlag = false
    self.isShowStar = false
    self.isShowPlan = false
    self.isMask = false
    self.isChooseMask = false
    --self.actorLv = 0              -- 玩家等级
    --self.isShowLv = false		  -- 是否显示等级
end

function M:Init(parentTrans, callback, scale)
    self:LoadResource(entityResPath, "",
    function()
        if callback then
            callback()
        end
        self.scale = scale or 1
        if scale then
            self.transform.localScale = { x = scale, y = scale, z = scale }
        else
            self.transform.localScale = Vector3.one
        end

        self.transform.anchoredPosition3D = {x=0, y= 0, z= 0}
		self.transform.anchorMin = {x=0.5, y= 0.5}
		self.transform.anchorMax = {x=0.5, y= 0.5}

        if self.crystalID then
            self:DisplayInfo()
        end
        self:RegistEvents()
    end,
    true,
    parentTrans
    )
    return self
end

--[[设置头像数据
@param crystalID 头像配置ID
@param callback 点击回调函数
]]
function M:SetCrystalInfo(crystalSid,crystalID,num,level,clickCallback,otherData)
    self.crystalID = crystalID
    self.num = num
    self.level = level or 0
    self.crystalSid = crystalSid
    self.otherData = otherData
    if crystalSid then
        local player_mgr = require "player_mgr"
        self.entity = player_mgr.GetPacketPartDataBySid(crystalSid)
        if not self.entity then
            log.Error("crystal entity is nil,crystalSid ",crystalSid)
            return 
        end
        local props = self.entity:GetCrystalProps()
        self.crystalID = self.entity:GetGoodsID()
        self.level = props.starLv
    end
    self.crystalCfg = game_scheme:MagicWeaponCrystal_0(self.crystalID,self.level)
    if not self.crystalCfg then
        log.Error("crystalCfg is nil, crystalID = ",self.crystalID)
        return
    end
    if clickCallback then
        if type(clickCallback) == "function" then
            self.clickCallback = clickCallback
        end
    end
    if self.UIRoot then
        self:DisplayInfo()
    end
end

-- [[头像数据显示]]
function M:DisplayInfo()
    if not self.crystalID then
        return
    end
    self:SetIcon(self.crystalID)	
    self:ShowNumText(self.isShowNum)
    self:ShowFeatureTypeFlag(self.isShowfeatureTypeFlag)
    self:ShowStarRoot(self.isShowStar)
    self:ShowMask(self.isMask)
    self:ShowPlan(self.isShowPlan)
    self:ShowChooseMask(self.isChooseMask)
end

--[[更改头像图标]]
function M:SetIcon(_crystalID)
    self.crystalID = _crystalID
    if not self.UIRoot then
        return
    end
    local itemCfg = game_scheme:Item_0(_crystalID)
    if itemCfg then
        faceSpriteAsset:GetSprite(itemCfg.icon,
                function(sprite)
                    if self and self.newiconImg and not self.newiconImg:IsNull() then
                        self.newiconImg.sprite = sprite
                    end
                end)
    end
end

function M:ShowMask(isMask)
    self.isMask = isMask
    if not self.UIRoot then
        return
    end
    UIUtil.SetActive(self.mask,self.isMask)
end

function M:ShowChooseMask(isChooseMask)
    self.isChooseMask = isChooseMask
    if not self.UIRoot then
        return
    end
    UIUtil.SetActive(self.chooseMask,self.isChooseMask)
end

function M:ShowPlan(isShowPlan)
    self.isShowPlan = isShowPlan
    if not self.UIRoot then
        return
    end
    if self.otherData and self.otherData.planID then
        UIUtil.SetActive(self.planBg,self.isShowPlan)
        self.planID.text = tostring(self.otherData.planID)
    else
        UIUtil.SetActive(self.planBg,false)
    end

end

function M:ShowNumText(isShow)
    self.isShowNum = isShow
    if not self.UIRoot then
        return
    end
    UIUtil.SetActive(self.numText,isShow == true)
    if self.numText then
        if self.num and self.num > 1 then
            self.numText.text = string.format2("x{%s1}",self.num)
        else
            self.numText.text = ""
        end
    end
end

function M:ShowFeatureTypeFlag(isShow)
    self.isShowfeatureTypeFlag = isShow
    if not self.UIRoot then
        return
    end
    if self.featureTypeFlag and self.crystalCfg then
        if self.isShowfeatureTypeFlag then
            self.featureTypeFlag:Switch(self.crystalCfg.featureType-1)
        end
        UIUtil.SetActive(self.featureTypeFlag,self.isShowfeatureTypeFlag)
    end
end

function M:ShowStarRoot(isShow)
    self.isShowStar = isShow
    if not self.UIRoot then
        return
    end
    if self.starRoot then
        if self.isShowStar then
            if not self.spriteSwitchList then
                self.spriteSwitchList = {}
                local childCount = self.starRoot.transform.childCount
                for i = 0, childCount - 1 do
                    local child = self.starRoot.transform:GetChild(i).gameObject
                    local spriteSwitcher = child:GetComponent(typeof(SpriteSwitcher))
                    if spriteSwitcher ~= nil then
                        table.insert(self.spriteSwitchList, spriteSwitcher)
                    end
                end
            end
            
            local childCount = #self.spriteSwitchList
            if self.level <= childCount then
                for i = 1, childCount do
                    if i <= self.level then
                        UIUtil.SetActive(self.spriteSwitchList[i],true)
                        self.spriteSwitchList[i]:Switch(0)
                    else
                        UIUtil.SetActive(self.spriteSwitchList[i],false)
                    end
                end
            else
                local diff = self.level - childCount
                for i = 1, childCount do
                    UIUtil.SetActive(self.spriteSwitchList[i],true)
                    if i <= diff then
                        self.spriteSwitchList[i]:Switch(1)
                    else
                        self.spriteSwitchList[i]:Switch(0)
                    end
                end
            end
        end
        UIUtil.SetActive(self.starRoot,self.isShowStar)
    end
end

function M:RegistEvents()
    self.onClickMonster = function(...)
        if self.clickCallback then
            self.clickCallback(self)
        end
    end
    self.button.onClick:AddListener(self.onClickMonster)

    --event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

function M:Dispose()
    self.isDisposed = true
    self.crystalID = nil             -- 头像配置id
    self.clickCallback = nil
    self.crystalCfg = nil   
    self.isShowNum = false
    self.isShowfeatureTypeFlag = false
    self.isShowStar = false
    self.isShowPlan = false
    self.isMask = false
    self.isChooseMask = false
    --self.actorLv = 0              -- 玩家等级
    if self.onClickMonster and self.button and not util.IsObjNull(self.button) then
        self.button.onClick:RemoveListener(self.onClickMonster)
        self.onClickMonster = nil
    end
    self.__base:Dispose()
end

local class = require "class"
local base_game_object = require "base_game_object"
CCrystalItem = class(base_game_object, nil, M)