
local Transform = CS.UnityEngine.Transform
local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button


module("ui_line_info_pop_ui_template1_binding")

UIPath = "ui/prefabs/gw/gw_common/template/uitemplate/uilineinfopopuitemplate1.prefab"

WidgetTable ={
	tf_faceItem = { path = "panel/Main/MainPanel/roleInfo/tf_faceItem", type = Transform, },
	ssw_sexImage = { path = "panel/Main/MainPanel/roleInfo/roleInfo/image/ssw_sexImage", type = SpriteSwitcher, },
	txt_name_1 = { path = "panel/Main/MainPanel/roleInfo/roleInfo/Txt/txt_name_1", type = Text, },
	txt_fightPower = { path = "panel/Main/MainPanel/roleInfo/txt_fightPower", type = Text, },
	txt_lineInfoDes = { path = "panel/Main/MainPanel/lineInfo/txt_lineInfoDes", type = Text, },
	tf_Viewport = { path = "panel/Main/MainPanel/tf_Viewport", type = Transform, },
	btn_closeBtn = { path = "panel/closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},

}
