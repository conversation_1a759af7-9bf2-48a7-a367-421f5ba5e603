
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Slider = CS.UnityEngine.UI.Slider
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local RectTransform = CS.UnityEngine.RectTransform
local GameObject = CS.UnityEngine.GameObject


module("ui_gw_hang_up_binding")

UIPath = "ui/prefabs/gw/gw_hangup/uigwhangup.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_TitleBg = { path = "panelBg/contentBg/titleBg/txt_TitleBg", type = Text, },
	sld_Time = { path = "bgTime/sld_Time", type = Slider, value_changed_event = "OnSliderTimeValueChange"},
	txt_Time = { path = "bgTime/txt_Time", type = Text, },
	txt_TimeTitle = { path = "bgTime/txt_TimeTitle", type = Text, },
	txt_RewardTitle = { path = "RewardTitleBg/txt_RewardTitle", type = Text, },
	srt_RewardTable = { path = "RewradList/Viewport/srt_RewardTable", type = ScrollRectTable, },
	txt_RewardMax = { path = "RewradList/txt_RewardMax", type = Text, },
	btn_Reward = { path = "RewradList/btn_Reward", type = Button, event_name = "OnBtnRewardClickedProxy"},
	txt_Title = { path = "RewradList/btn_Reward/txt_Title", type = Text, },
	rtf_SpeedParent = { path = "rtf_SpeedParent", type = RectTransform, },
	obj_Speed = { path = "rtf_SpeedParent/Viewport/Content/obj_Speed", type = GameObject, },

}
