
local GameObject = CS.UnityEngine.GameObject
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button


module("item_activity_infinite_box_binding")

UIPath = "ui/prefabs/gw/activity/commonitem/item_activity_infinitebox.prefab"

WidgetTable ={
	item_activity_infiniteBox = { path = "", type = GameObject, },
	img_icon = { path = "img_icon", type = Image, },
	txt_title = { path = "txt_title", type = Text, },
	btn_preview = { path = "btn_preview", type = Button, event_name = "OnBtnPreviewClickedProxy"},
	txt_tip = { path = "txt_tip", type = Text, },
	obj_score = { path = "obj_score", type = GameObject, },
	img_scoreProgress = { path = "obj_score/img_scoreProgress", type = Image, },
	txt_scoreProgress = { path = "obj_score/txt_scoreProgress", type = Text, },
	img_score = { path = "obj_score/img_score", type = Image, },
	btn_get = { path = "btn_get", type = Button, event_name = "OnBtnGetClickedProxy"},
	btn_recharge = { path = "btn_recharge", type = Button, event_name = "OnBtnRechargeClickedProxy"},
	item_red = { path = "item_red", type = GameObject, },

}
