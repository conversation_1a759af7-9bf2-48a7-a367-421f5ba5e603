local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button
local Toggle = CS.UnityEngine.UI.Toggle
local Text = CS.UnityEngine.UI.Text


module("ui_war_zone_duel_main_binding")

UIPath = "ui/prefabs/gw/gw_warzoneduel/uiwarzoneduelmain.prefab"

WidgetTable ={
	rtf_pageParent = { path = "rtf_pageParent", type = RectTransform, },
	back_returnBtn = { path = "back_returnBtn", type = Button, event_name = "OnBtnReturnBtnClickedProxy", backEvent = true},
	tog_matchInfoToggle = { path = "warToggleGroup/tog_matchInfoToggle", type = Toggle, value_changed_event = "OnTogMatchInfoToggleValueChange"},
	txt_matchOn = { path = "warToggleGroup/tog_matchInfoToggle/txt_matchOn", type = Text, },
	txt_matchOff = { path = "warToggleGroup/tog_matchInfoToggle/txt_matchOff", type = Text, },
	tog_weekSituationToggle = { path = "warToggleGroup/tog_weekSituationToggle", type = Toggle, value_changed_event = "OnTogWeekSituationToggleValueChange"},
	txt_weekOn = { path = "warToggleGroup/tog_weekSituationToggle/txt_weekOn", type = Text, },
	txt_weekOff = { path = "warToggleGroup/tog_weekSituationToggle/txt_weekOff", type = Text, },
	tog_congressToggle = { path = "warToggleGroup/tog_congressToggle", type = Toggle, value_changed_event = "OnTogCongressToggleValueChange"},
	txt_congressOn = { path = "warToggleGroup/tog_congressToggle/txt_congressOn", type = Text, },
	txt_congressOff = { path = "warToggleGroup/tog_congressToggle/txt_congressOff", type = Text, },

}
