
local Text = CS.UnityEngine.UI.Text
local Button = CS.UnityEngine.UI.Button
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_vip_special_recharge_binding")

UIPath = "ui/prefabs/gw/gw_vip/uivipspecialrecharge.prefab"

WidgetTable ={
	txt_SurvivorName = { path = "TopContent/txt_SurvivorName", type = Text, },
	btn_TipBtn = { path = "TopContent/Tip/btn_TipBtn", type = Button, event_name = "OnBtnTipBtnClickedProxy"},
	srt_RechargeContent = { path = "Main/Scroll View/Viewport/srt_RechargeContent", type = ScrollRectTable, },
	btn_close = { path = "btn_close", type = Button, event_name = "OnBtnCloseClickedProxy"},

}
