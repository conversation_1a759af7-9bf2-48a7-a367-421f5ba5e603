
local Text = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject


module("ui_mail_entry_panel_binding")

UIPath = "ui/prefabs/gw/gw_mailnew/uimailentrypanel.prefab"

WidgetTable ={
	txt_Title = { path = "titleBg/txt_Title", type = Text, },
	srt_ItemContent = { path = "MainPanel/Scroll View/Viewport/srt_ItemContent", type = ScrollRectTable, },
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	btn_ReadAll = { path = "Bottom/btn_ReadAll", type = Button, event_name = "OnBtnReadAllClickedProxy"},
	btn_DeleteAll = { path = "Bottom/btn_DeleteAll", type = Button, event_name = "OnBtnDeleteAllClickedProxy"},
	obj_Ef = { path = "obj_Ef", type = GameObject, },

}
