local require = require
local typeof = typeof

local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local GameObject = CS.UnityEngine.GameObject
local InputField = CS.UnityEngine.UI.InputField
local RectTransform = CS.UnityEngine.RectTransform


module("ui_nc_declare_binding")

UIPath = "ui/prefabs/sandbox/uincdeclare.prefab"

WidgetTable ={
	back_closeBtn = { path = "back_closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	txt_title = { path = "content/title/txt_title", type = Text, },
	img_city = { path = "content/city/mask/img_city", type = Image, },
	txt_unionDeclares = { path = "content/city/declareInfo/txt_unionDeclares", type = Text, },
	txt_unionOccupys = { path = "content/city/occupyInfo/txt_unionOccupys", type = Text, },
	btn_helper = { path = "content/city/btn_helper", type = Button, event_name = "OnBtnHelperClickedProxy"},
	txt_timeTitle = { path = "content/time/txt_timeTitle", type = Text, },
	obj_time = { path = "content/time/obj_time", type = GameObject, },
	btn_switch = { path = "content/time/obj_time/btn_switch", type = Button, event_name = "OnBtnSwitchClickedProxy"},
	txt_dates = { path = "content/time/obj_time/dates/Image/txt_dates", type = Text, },
	txt_hours = { path = "content/time/obj_time/hours/Image/txt_hours", type = Text, },
	txt_minutes = { path = "content/time/obj_time/minutes/Image/txt_minutes", type = Text, },
	obj_ready = { path = "content/time/obj_ready", type = GameObject, },
	inp_notice = { path = "content/notice/inp_notice", type = InputField, value_changed_event = "OnInputNoticeValueChange" , end_edit_event = "OnInputNoticeEndEdit"},
	rtf_faceItem = { path = "content/notice/rtf_faceItem", type = RectTransform, },
	txt_alliesName = { path = "content/notice/Image/txt_alliesName", type = Text, },
	txt_declaration = { path = "content/notice/Image/txt_declaration", type = Text, },
	txt_declareNumber = { path = "content/declare/txt_declareNumber", type = Text, },
	btn_declare = { path = "content/declare/btn_declare", type = Button, event_name = "OnBtnDeclareClickedProxy"},

}
