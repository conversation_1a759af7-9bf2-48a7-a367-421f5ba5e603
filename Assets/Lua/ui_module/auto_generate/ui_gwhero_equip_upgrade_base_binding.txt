
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button


module("ui_gwhero_equip_upgrade_base_binding")

UIPath = "ui/prefabs/gw/gw_heroprefab/heroequip/uigwheroequipupgradebase.prefab"

WidgetTable ={
	txt_FullTitle = { path = "Image/title/txt_FullTitle", type = Text, },
	rtf_Parent = { path = "rtf_Parent", type = RectTransform, },
	rtf_ToggleGroup = { path = "bottom/rtf_ToggleGroup", type = RectTransform, },
	btn_return = { path = "bottom/btn_return", type = Button, event_name = "OnBtnReturnClickedProxy"},
	btn_Right = { path = "btn_Right", type = Button, event_name = "OnBtnRightClickedProxy"},
	btn_Left = { path = "btn_Left", type = Button, event_name = "OnBtnLeftClickedProxy"},

}
