
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local InputField = CS.UnityEngine.UI.InputField
local Transform = CS.UnityEngine.Transform


module("ui_alliance_notice_release_panel_binding")

UIPath = "ui/prefabs/gw/gw_chat/uialliancenoticereleasepanel.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	obj_releaseBtn = { path = "obj_releaseBtn", type = GameObject, },
	btn_urgentNotice = { path = "obj_releaseBtn/go_release/btn_urgentNotice", type = Button, event_name = "OnBtnUrgentNoticeClickedProxy"},
	txt_urgentTips = { path = "obj_releaseBtn/go_release/btn_urgentNotice/txt_urgentTips", type = Text, },
	btn_normalNotice = { path = "obj_releaseBtn/go_release/btn_normalNotice", type = Button, event_name = "OnBtnNormalNoticeClickedProxy"},
	obj_releaseInput = { path = "obj_releaseInput", type = GameObject, },
	txt_inputTitle = { path = "obj_releaseInput/TitleBg/txt_inputTitle", type = Text, },
	inp_content = { path = "obj_releaseInput/inp_content", type = InputField, value_changed_event = "OnInputContentValueChange" , end_edit_event = "OnInputContentEndEdit"},
	txt_wordNum = { path = "obj_releaseInput/txt_wordNum", type = Text, },
	btn_release = { path = "obj_releaseInput/btn_release", type = Button, event_name = "OnBtnReleaseClickedProxy"},
	txt_releaseBtn = { path = "obj_releaseInput/btn_release/txt_releaseBtn", type = Text, },
	tf_ef = { path = "tf_ef", type = Transform, },

}
