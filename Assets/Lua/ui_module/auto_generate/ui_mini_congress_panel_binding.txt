local require = require
local typeof = typeof

local Toggle = CS.UnityEngine.UI.Toggle
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local Transform = CS.UnityEngine.Transform
local Image = CS.UnityEngine.UI.Image
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable


module("ui_mini_congress_panel_binding")

UIPath = "ui/prefabs/gw/alliancesystem/congress/uiminicongresspanel.prefab"

WidgetTable ={
	tog_office = { path = "Toggle_Lv2_Normal_Below_Group/tog_office", type = Toggle, value_changed_event = "OnTogOfficeValueChange"},
	tog_award = { path = "Toggle_Lv2_Normal_Below_Group/tog_award", type = Toggle, value_changed_event = "OnTogAwardValueChange"},
	tog_award_conqueror = { path = "Toggle_Lv2_Normal_Below_Group/tog_award_conqueror", type = Toggle, value_changed_event = "OnTogAward_conquerorValueChange"},
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	obj_ApplyOffice = { path = "panelBg/obj_ApplyOffice", type = GameObject, },
	btn_FirstLady = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/btn_FirstLady", type = Button, event_name = "OnBtnFirstLadyClickedProxy"},
	txt_FirstLadyPositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/txt_FirstLadyPositionName", type = Text, },
	obj_FirstLadyInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyInfo", type = GameObject, },
	tf_FirstLadyHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyInfo/tf_FirstLadyHead", type = Transform, },
	txt_FirstLadyName = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyInfo/txt_FirstLadyName", type = Text, },
	txt_FirstLadyTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyInfo/Time/txt_FirstLadyTimeLeft", type = Text, },
	txt_FirstLadyAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyInfo/txt_FirstLadyAllianceName", type = Text, },
	obj_FirstLadyEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyEmpty", type = GameObject, },
	img_FirstLadyEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyEmpty/img_FirstLadyEmptyIcon", type = Image, },
	obj_FirstLadyRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/FirstLady/obj_FirstLadyRedDot", type = GameObject, },
	btn_MinisterOfStategy = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/btn_MinisterOfStategy", type = Button, event_name = "OnBtnMinisterOfStategyClickedProxy"},
	txt_MinisterOfStategyPositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/txt_MinisterOfStategyPositionName", type = Text, },
	obj_MinisterOfStategyInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyInfo", type = GameObject, },
	tf_MinisterOfStategyHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyInfo/tf_MinisterOfStategyHead", type = Transform, },
	txt_MinisterOfStategyName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyInfo/txt_MinisterOfStategyName", type = Text, },
	txt_MinisterOfStategyTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyInfo/Time/txt_MinisterOfStategyTimeLeft", type = Text, },
	txt_MinisterOfStategyAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyInfo/txt_MinisterOfStategyAllianceName", type = Text, },
	obj_MinisterOfStategyEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyEmpty", type = GameObject, },
	img_MinisterOfStategyEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyEmpty/img_MinisterOfStategyEmptyIcon", type = Image, },
	obj_MinisterOfStategyRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfStategy/obj_MinisterOfStategyRedDot", type = GameObject, },
	btn_MinisterOfDefense = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/btn_MinisterOfDefense", type = Button, event_name = "OnBtnMinisterOfDefenseClickedProxy"},
	txt_MinisterOfDefensePositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/txt_MinisterOfDefensePositionName", type = Text, },
	obj_MinisterOfDefenseInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseInfo", type = GameObject, },
	tf_MinisterOfDefenseHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseInfo/tf_MinisterOfDefenseHead", type = Transform, },
	txt_MinisterOfDefenseName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseInfo/txt_MinisterOfDefenseName", type = Text, },
	txt_MinisterOfDefenseTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseInfo/Time/txt_MinisterOfDefenseTimeLeft", type = Text, },
	txt_MinisterOfDefenseAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseInfo/txt_MinisterOfDefenseAllianceName", type = Text, },
	obj_MinisterOfDefenseEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseEmpty", type = GameObject, },
	img_MinisterOfDefenseEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseEmpty/img_MinisterOfDefenseEmptyIcon", type = Image, },
	obj_MinisterOfDefenseRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfDefense/obj_MinisterOfDefenseRedDot", type = GameObject, },
	btn_MinisterOfConstruction = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/btn_MinisterOfConstruction", type = Button, event_name = "OnBtnMinisterOfConstructionClickedProxy"},
	txt_MinisterOfConstructionPositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/txt_MinisterOfConstructionPositionName", type = Text, },
	obj_MinisterOfConstructionInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionInfo", type = GameObject, },
	tf_MinisterOfConstructionHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionInfo/tf_MinisterOfConstructionHead", type = Transform, },
	txt_MinisterOfConstructionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionInfo/txt_MinisterOfConstructionName", type = Text, },
	txt_MinisterOfConstructionTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionInfo/Time/txt_MinisterOfConstructionTimeLeft", type = Text, },
	txt_MinisterOfConstructionAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionInfo/txt_MinisterOfConstructionAllianceName", type = Text, },
	obj_MinisterOfConstructionEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionEmpty", type = GameObject, },
	img_MinisterOfConstructionEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionEmpty/img_MinisterOfConstructionEmptyIcon", type = Image, },
	obj_MinisterOfConstructionRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfConstruction/obj_MinisterOfConstructionRedDot", type = GameObject, },
	btn_MinisterOfScience = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/btn_MinisterOfScience", type = Button, event_name = "OnBtnMinisterOfScienceClickedProxy"},
	txt_MinisterOfSciencePositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/txt_MinisterOfSciencePositionName", type = Text, },
	obj_MinisterOfScienceInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceInfo", type = GameObject, },
	tf_MinisterOfScienceHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceInfo/tf_MinisterOfScienceHead", type = Transform, },
	txt_MinisterOfScienceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceInfo/txt_MinisterOfScienceName", type = Text, },
	txt_MinisterOfScienceTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceInfo/Time/txt_MinisterOfScienceTimeLeft", type = Text, },
	txt_MinisterOfScienceAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceInfo/txt_MinisterOfScienceAllianceName", type = Text, },
	obj_MinisterOfScienceEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceEmpty", type = GameObject, },
	img_MinisterOfScienceEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceEmpty/img_MinisterOfScienceEmptyIcon", type = Image, },
	obj_MinisterOfScienceRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfScience/obj_MinisterOfScienceRedDot", type = GameObject, },
	btn_MinisterOfTheInterior = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/btn_MinisterOfTheInterior", type = Button, event_name = "OnBtnMinisterOfTheInteriorClickedProxy"},
	txt_MinisterOfTheInteriorPositionName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/txt_MinisterOfTheInteriorPositionName", type = Text, },
	obj_MinisterOfTheInteriorInfo = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorInfo", type = GameObject, },
	tf_MinisterOfTheInteriorHead = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorInfo/tf_MinisterOfTheInteriorHead", type = Transform, },
	txt_MinisterOfTheInteriorName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorInfo/txt_MinisterOfTheInteriorName", type = Text, },
	txt_MinisterOfTheInteriorTimeLeft = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorInfo/Time/txt_MinisterOfTheInteriorTimeLeft", type = Text, },
	txt_MinisterOfTheInteriorAllianceName = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorInfo/txt_MinisterOfTheInteriorAllianceName", type = Text, },
	obj_MinisterOfTheInteriorEmpty = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorEmpty", type = GameObject, },
	img_MinisterOfTheInteriorEmptyIcon = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorEmpty/img_MinisterOfTheInteriorEmptyIcon", type = Image, },
	obj_MinisterOfTheInteriorRedDot = { path = "panelBg/obj_ApplyOffice/PositionGrid/MinisterOfTheInterior/obj_MinisterOfTheInteriorRedDot", type = GameObject, },
	obj_Award = { path = "panelBg/obj_Award", type = GameObject, },
	srt_RewardContent = { path = "panelBg/obj_Award/rewardArea/RewardList/StateScrollList/Viewport/srt_RewardContent", type = ScrollRectTable, },
	srt_content = { path = "panelBg/obj_Award/rewardArea/RewardList/StateScrollList/Viewport/srt_RewardContent/ListItem/AllReward/ScrollView/Viewport/srt_content", type = ScrollRectTable, },

}
