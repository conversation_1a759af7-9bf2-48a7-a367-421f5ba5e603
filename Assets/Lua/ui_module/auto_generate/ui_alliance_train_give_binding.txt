
local Button = CS.UnityEngine.UI.Button
local RectTransform = CS.UnityEngine.RectTransform
local Slider = CS.UnityEngine.UI.Slider
local InputField = CS.UnityEngine.UI.InputField
local Text = CS.UnityEngine.UI.Text


module("ui_alliance_train_give_binding")

UIPath = "ui/prefabs/gw/gw_alliancetrain/uialliancetraingive.prefab"

WidgetTable ={
	btn_closeBtn = { path = "closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	rtf_IconBg = { path = "Background/InformationBg/rtf_IconBg", type = RectTransform, },
	btn_reduceButton = { path = "Background/selectRoot/btn_reduceButton", type = Button, event_name = "OnBtnReduceButtonClickedProxy"},
	sld_Select = { path = "Background/selectRoot/sld_Select", type = Slider, value_changed_event = "OnSliderSelectValueChange"},
	btn_addButton = { path = "Background/selectRoot/btn_addButton", type = Button, event_name = "OnBtnAddButtonClickedProxy"},
	inp_InputField = { path = "Background/selectRoot/inp_InputField", type = InputField, value_changed_event = "OnInputInputFieldValueChange" , end_edit_event = "OnInputInputFieldEndEdit"},
	txt_input = { path = "Background/selectRoot/inp_InputField/txt_input", type = Text, },
	btn_thank = { path = "Background/buttonRoot/btn_thank", type = Button, event_name = "OnBtnThankClickedProxy"},
	txt_Title = { path = "Background/buttonRoot/btn_thank/txt_Title", type = Text, },
	btn_give = { path = "Background/buttonRoot/btn_give", type = Button, event_name = "OnBtnGiveClickedProxy"},
	txt_Title = { path = "Background/buttonRoot/btn_give/txt_Title", type = Text, },
	txt_number = { path = "Background/BottomBar/txt_number", type = Text, },

}
