local require = require
local typeof = typeof

local RectTransform = CS.UnityEngine.RectTransform
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local GameObject = CS.UnityEngine.GameObject


module("ui_storm_build_tips_binding")

UIPath = "ui/prefabs/storm/uistormbuildtips.prefab"

WidgetTable ={
	rtf_Pos = { path = "rtf_Pos", type = RectTransform, },
	btn_Info = { path = "rtf_Pos/BackGroud/Top/btn_Info", type = Button, event_name = "OnBtnInfoClickedProxy"},
	txt_Name = { path = "rtf_Pos/BackGroud/Top/txt_Name", type = Text, },
	btn_Share = { path = "rtf_Pos/BackGroud/Top/btnGroup/btn_Share", type = Button, event_name = "OnBtnShareClickedProxy"},
	img_Build = { path = "rtf_Pos/BackGroud/Bottom/Bg/img_Build", type = Image, },
	txt_Speed = { path = "rtf_Pos/BackGroud/Bottom/Bg/txt_Speed", type = Text, },
	txt_SpeedNum = { path = "rtf_Pos/BackGroud/Bottom/Bg/txt_SpeedNum", type = Text, },
	txt_Have = { path = "rtf_Pos/BackGroud/Bottom/Bg/txt_Have", type = Text, },
	txt_HaveNum = { path = "rtf_Pos/BackGroud/Bottom/Bg/txt_HaveNum", type = Text, },
	btn_Speed = { path = "rtf_Pos/BackGroud/Bottom/Bg/btn_Speed", type = Button, event_name = "OnBtnSpeedClickedProxy"},
	btn_Have = { path = "rtf_Pos/BackGroud/Bottom/Bg/btn_Have", type = Button, event_name = "OnBtnHaveClickedProxy"},
	obj_Role = { path = "rtf_Pos/BackGroud/obj_Role", type = GameObject, },
	obj_Owner = { path = "rtf_Pos/BackGroud/obj_Role/obj_Owner", type = GameObject, },
	img_Alli = { path = "rtf_Pos/BackGroud/obj_Role/obj_Owner/img_Alli", type = Image, },
	txt_OwnerDes = { path = "rtf_Pos/BackGroud/obj_Role/obj_Owner/txt_OwnerDes", type = Text, },
	txt_OwnerName = { path = "rtf_Pos/BackGroud/obj_Role/obj_Owner/txt_OwnerDes/txt_OwnerName", type = Text, },
	obj_Null = { path = "rtf_Pos/BackGroud/obj_Role/obj_Null", type = GameObject, },
	txt_Null = { path = "rtf_Pos/BackGroud/obj_Role/obj_Null/txt_Null", type = Text, },
	obj_Lock = { path = "rtf_Pos/BackGroud/obj_Lock", type = GameObject, },
	txt_LockDes = { path = "rtf_Pos/BackGroud/obj_Lock/txt_LockDes", type = Text, },
	txt_LockTime = { path = "rtf_Pos/BackGroud/obj_Lock/txt_LockTime", type = Text, },

}
