local require = require
local typeof = typeof

local Transform = CS.UnityEngine.Transform
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local GameObject = CS.UnityEngine.GameObject
local ScrollRect = CS.UnityEngine.UI.ScrollRect
local Toggle = CS.UnityEngine.UI.Toggle


module("ui_mail_detail_detect_binding")

UIPath = "ui/prefabs/gw/gw_mailnew/report/uimaildetail_detect.prefab"

WidgetTable ={
	tf_MainPanel = { path = "contentBg/tf_MainPanel", type = Transform, },
	tmp_BattlePos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_TimeStamp/tmp_BattlePos", type = TextMeshProUGUI, },
	tmp_TimeStamp = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_TimeStamp/tmp_TimeStamp", type = TextMeshProUGUI, },
	btn_Go2BattlePos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_TimeStamp/btn_Go2BattlePos", type = Button, event_name = "OnBtnGo2BattlePosClickedProxy"},
	txt_DetectTipInfo = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/txt_DetectTipInfo", type = Text, },
	tf_LeftFaceIconRoot = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/LeftLeaderInfo/tf_LeftFaceIconRoot", type = Transform, },
	txt_LeftPlayerInfo = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/LeftLeaderInfo/txt_LeftPlayerInfo", type = Text, },
	tmp_LeftPlayerPos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/LeftLeaderInfo/tmp_LeftPlayerPos", type = TextMeshProUGUI, },
	btn_LeftGoToPos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/LeftLeaderInfo/btn_LeftGoToPos", type = Button, event_name = "OnBtnLeftGoToPosClickedProxy"},
	tf_RightFaceIconRoot = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/RightLeaderInfo/tf_RightFaceIconRoot", type = Transform, },
	txt_RightPlayerInfo = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/RightLeaderInfo/txt_RightPlayerInfo", type = Text, },
	tmp_RightPlayerPos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/RightLeaderInfo/tmp_RightPlayerPos", type = TextMeshProUGUI, },
	btn_RightGoToPos = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/Point_LeadersInfo/RightLeaderInfo/btn_RightGoToPos", type = Button, event_name = "OnBtnRightGoToPosClickedProxy"},
	obj_Point_LootItem = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Point_LootItem", type = GameObject, },
	txt_LootTip = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Point_LootItem/Tip/txt_LootTip", type = Text, },
	sr_Loot = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Point_LootItem/sr_Loot", type = ScrollRect, },
	tf_LootsItemContent = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Point_LootItem/sr_Loot/Viewport/tf_LootsItemContent", type = Transform, },
	obj_Pages = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Pages", type = GameObject, },
	tog_Hero = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Pages/ToogleGroup/tog_Hero", type = Toggle, value_changed_event = "OnTogHeroValueChange"},
	obj_HeroTeam = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Pages/obj_HeroTeam", type = GameObject, },
	obj_HeroTeamContent = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/obj_Pages/obj_HeroTeam/obj_HeroTeamContent", type = GameObject, },
	txt_BeDetectedTip = { path = "contentBg/tf_MainPanel/Viewport/Rect_Content/txt_BeDetectedTip", type = Text, },
	btn_closeBtn = { path = "bottomRoot/closeBtn", type = Button, event_name = "OnBtnCloseBtnClickedProxy", backEvent = true},
	btn_share = { path = "bottomRoot/btn_share", type = Button, event_name = "OnBtnShareClickedProxy"},
	obj_HeroTeamItem = { path = "objItems/obj_HeroTeamItem", type = GameObject, },

}
