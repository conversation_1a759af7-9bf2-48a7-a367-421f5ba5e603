local require = require
local typeof = typeof

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local Transform = CS.UnityEngine.Transform
local Button = CS.UnityEngine.UI.Button
local GameObject = CS.UnityEngine.GameObject
local Text = CS.UnityEngine.UI.Text
local Toggle = CS.UnityEngine.UI.Toggle
local RectTransform = CS.UnityEngine.RectTransform
local Image = CS.UnityEngine.UI.Image


module("ui_desert_storm_activity_main_panel_binding")

UIPath = "ui/prefabs/gw/activity/desertstorm/uidesertstormactivitymainpanel.prefab"

WidgetTable ={
	ss_CityBg = { path = "Bg/ss_CityBg", type = SpriteSwitcher, },
	tf_BtnGroup = { path = "Bg/Panel/tf_BtnGroup", type = Transform, },
	btn_Join = { path = "Bg/Panel/tf_BtnGroup/btn_Join", type = Button, event_name = "OnBtnJoinClickedProxy"},
	btn_SelectTime = { path = "Bg/Panel/tf_BtnGroup/btn_SelectTime", type = Button, event_name = "OnBtnSelectTimeClickedProxy"},
	btn_SelectPlayer = { path = "Bg/Panel/tf_BtnGroup/btn_SelectPlayer", type = Button, event_name = "OnBtnSelectPlayerClickedProxy"},
	btn_EnterBattleField = { path = "Bg/Panel/tf_BtnGroup/btn_EnterBattleField", type = Button, event_name = "OnBtnEnterBattleFieldClickedProxy"},
	obj_enterBattleFieldTxt = { path = "Bg/Panel/tf_BtnGroup/btn_EnterBattleField/obj_enterBattleFieldTxt", type = GameObject, },
	obj_enterBattleFieldTxtGray = { path = "Bg/Panel/tf_BtnGroup/btn_EnterBattleField/obj_enterBattleFieldTxtGray", type = GameObject, },
	btn_Spectate = { path = "Bg/Panel/tf_BtnGroup/btn_Spectate", type = Button, event_name = "OnBtnSpectateClickedProxy"},
	obj_SpectateTxt = { path = "Bg/Panel/tf_BtnGroup/btn_Spectate/obj_SpectateTxt", type = GameObject, },
	obj_SpectateTxtGray = { path = "Bg/Panel/tf_BtnGroup/btn_Spectate/obj_SpectateTxtGray", type = GameObject, },
	btn_Rule = { path = "Bg/Panel/btn_Rule", type = Button, event_name = "OnBtnRuleClickedProxy"},
	btn_Shop = { path = "Bg/Panel/btn_Shop", type = Button, event_name = "OnBtnShopClickedProxy"},
	btn_History = { path = "Bg/Panel/btn_History", type = Button, event_name = "OnBtnHistoryClickedProxy"},
	txt_TimeLeftTips = { path = "Bg/Panel/txt_TimeLeftTips", type = Text, },
	txt_TimesLeft = { path = "Bg/Panel/txt_TimeLeftTips/Bg/txt_TimesLeft", type = Text, },
	obj_BeforeBattle = { path = "Bg/obj_BeforeBattle", type = GameObject, },
	btn_Log = { path = "Bg/obj_BeforeBattle/btn_Log", type = Button, event_name = "OnBtnLogClickedProxy"},
	btn_RewardBtn = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/btn_RewardBtn", type = Button, event_name = "OnBtnRewardBtnClickedProxy"},
	tog_GroupA = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupA", type = Toggle, value_changed_event = "OnTogGroupAValueChange"},
	obj_youAreInTeamA = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupA/obj_youAreInTeamA", type = GameObject, },
	obj_AbandonMark_A = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupA/obj_AbandonMark_A", type = GameObject, },
	tog_GroupB = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupB", type = Toggle, value_changed_event = "OnTogGroupBValueChange"},
	obj_youAreInTeamB = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupB/obj_youAreInTeamB", type = GameObject, },
	obj_AbandonMark_B = { path = "Bg/obj_BeforeBattle/LayoutObj/TimeAndReward/TeamGroup/tog_GroupB/obj_AbandonMark_B", type = GameObject, },
	rtf_content = { path = "Bg/obj_BeforeBattle/LayoutObj/Reward/ScrollView/Viewport/rtf_content", type = RectTransform, },
	txt_Tips = { path = "Bg/obj_BeforeBattle/LayoutObj/txt_Tips", type = Text, },
	obj_ChangeTime = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime", type = GameObject, },
	txt_TimeType = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/ChangeTime/txt_TimeType", type = Text, },
	tog_changeTime = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/ChangeTime/tog_changeTime", type = Toggle, value_changed_event = "OnTogChangeTimeValueChange"},
	txt_BattleDay = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/txt_BattleDay", type = Text, },
	txt_BattleDayValue = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/txt_BattleDay/txt_BattleDayValue", type = Text, },
	txt_BattleTime = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/txt_BattleTime", type = Text, },
	txt_BattleCount = { path = "Bg/obj_BeforeBattle/LayoutObj/obj_ChangeTime/txt_BattleCount", type = Text, },
	obj_InBattle = { path = "Bg/obj_InBattle", type = GameObject, },
	txt_TimeLeftTipsInBattle = { path = "Bg/obj_InBattle/LayoutObj/TimeAndReward/txt_TimeLeftTipsInBattle", type = Text, },
	txt_TimesLeftInBattle = { path = "Bg/obj_InBattle/LayoutObj/TimeAndReward/txt_TimeLeftTipsInBattle/txt_TimesLeftInBattle", type = Text, },
	obj_TeamGroup = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup", type = GameObject, },
	tog_GroupAInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupAInBattle", type = Toggle, value_changed_event = "OnTogGroupAInBattleValueChange"},
	obj_youAreInTeamAInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupAInBattle/obj_youAreInTeamAInBattle", type = GameObject, },
	obj_AbandonMark_AInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupAInBattle/obj_AbandonMark_AInBattle", type = GameObject, },
	tog_GroupBInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupBInBattle", type = Toggle, value_changed_event = "OnTogGroupBInBattleValueChange"},
	obj_youAreInTeamBInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupBInBattle/obj_youAreInTeamBInBattle", type = GameObject, },
	obj_AbandonMark_BInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_TeamGroup/tog_GroupBInBattle/obj_AbandonMark_BInBattle", type = GameObject, },
	obj_AllianceObj = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj", type = GameObject, },
	obj_Vs = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj/Bg/obj_Vs", type = GameObject, },
	img_LeftAlliance = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj/Bg/obj_Vs/img_LeftAlliance", type = Image, },
	obj_TargetAllianceObj = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj/Bg/obj_Vs/obj_TargetAllianceObj", type = GameObject, },
	img_RightAlliance = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj/Bg/obj_Vs/img_RightAlliance", type = Image, },
	btn_RewardBtnInBattle = { path = "Bg/obj_InBattle/LayoutObj/obj_AllianceObj/Bg/btn_RewardBtnInBattle", type = Button, event_name = "OnBtnRewardBtnInBattleClickedProxy"},
	txt_InBattleTips = { path = "Bg/obj_InBattle/LayoutObj/txt_InBattleTips", type = Text, },
	obj_allianceScore = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore", type = GameObject, },
	txt_LeftAliiancePlayerCount = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/countBg/txt_LeftAliiancePlayerCount", type = Text, },
	txt_LeftAliianceScore = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/countBg/txt_LeftAliianceScore", type = Text, },
	txt_RightAliiancePlayerCount = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/countBg/txt_RightAliiancePlayerCount", type = Text, },
	txt_RightAliianceScore = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/countBg/txt_RightAliianceScore", type = Text, },
	txt_LeftAliianceName = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/txt_LeftAliianceName", type = Text, },
	txt_RightAliianceName = { path = "Bg/obj_InBattle/LayoutObj/obj_allianceScore/txt_RightAliianceName", type = Text, },
	btn_PlayerList = { path = "Bg/obj_InBattle/btn_PlayerList", type = Button, event_name = "OnBtnPlayerListClickedProxy"},
	btn_TipsBtn = { path = "titleBg/btn_TipsBtn", type = Button, event_name = "OnBtnTipsBtnClickedProxy"},

}
