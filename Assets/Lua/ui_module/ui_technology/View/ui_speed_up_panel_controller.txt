
--- ui_speed_up_panel_controller.txt
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by nyz.
--- DateTime: 
--- desc:    
---
local require = require
local pairs = pairs
local string = string
local table = table
local newclass = newclass

local gw_const = require "gw_const"
local lingshi_data = require "lingshi_data"
local evt_sourceSupply_define = require "evt_sourceSupply_define"
local event = require "event"
local module_scroll_list = require "scroll_list"
local log = require "log"
local controller_base = require "controller_base"
local e_handler_mgr = require "e_handler_mgr"
local tostring = tostring
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local game_scheme = require "game_scheme"
local windowMgr = require "ui_window_mgr"
local lang 			        = require "lang"
local net_SpeedUp_module = require "net_speed_up_module"
local ui_speed_up_panel = require "ui_speed_up_panel"
local gw_home_common_data = require "gw_home_common_data"
local gw_home_building_queue_data = require "gw_home_building_queue_data"
local math = math
local item_source_mgr = require "item_source_mgr"
local gw_home_building_data = require"gw_home_building_data"
local gw_home_soldier_data = require "gw_home_soldier_data"
local gw_equip_define = require "gw_equip_define"
local gw_storm_mgr = require "gw_storm_mgr"

local os = os
module("ui_speed_up_panel_controller")
--TODO  类的实现方式后续需要优化，或者不用基类 现在的class的继承很耗时，
local controller = nil
local UISpeedUpController = newclass("ui_speed_up_panel_controller", controller_base)
local technology_data = require "technology_data"
local isShowBtn = false
local useItemMsg = {}
local itemNeedCount = {}

--加速类型的枚举
SpeedUpTypeEnum = {
    Technology = 1,
    Building = 2,
    TrainingSoldier = 3,
    UpgradeSoldier = 4,
    TreatmentSoldier = 5,
    EquipFactory = 6,
}

SpeedUpItemEnum =
{
    [SpeedUpTypeEnum.Technology] = 821,
    [SpeedUpTypeEnum.Building] = 811,
    [SpeedUpTypeEnum.TrainingSoldier] = 831,
    [SpeedUpTypeEnum.UpgradeSoldier] = 831,
    [SpeedUpTypeEnum.TreatmentSoldier] = 841,
    [SpeedUpTypeEnum.EquipFactory] = 801,
}

--[[窗口初始化]]
function UISpeedUpController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    self:SubscribeEvents()
    self.data = self.data or data; --这里的data的数据结构：2024/08/08新增加速类型
    if not self.data.sceneType then
        self.data.sceneType = gw_const.enSandboxType.enSandboxType_World
    end
    --region
    --[[
    
        沙漠风暴必须新增sceneType类型，若不增加sceneType类型则一律视为gw_const.enSandboxType.enSandboxType_World
    
        --科研系统的数据结构
        type = SpeedUpTypeEnum.Technology
        buildID = v.buildID,
        curTypeID = v.curTypeID,
        curScienticID = v.curScienticID,
        curResearchCompleteTime = math.floor(secondsToTime),
        cfgData = cfg01,
        
        --城建建造系统
        type = SpeedUpTypeEnum.Building
        buildID = nBuildingID --建筑的表格ID，拿来读一些显示的数据
        level = nLevel --建筑的等级
        curResearchCompleteTime = math.floor(secondsToTime),--剩下的时间
        queueIndex = nID,--建筑队列ID，传给服务器用
        cfgData = cfg01,
        
        --训练士兵
        type = SpeedUpTypeEnum.TrainingSoldier
        soldierID = soldierID --士兵的id，拿来读取士兵的对应信息
        count = count --要训练多少个士兵
        curResearchCompleteTime = math.floor(secondsToTime),--剩下的时间
        sid = nID --兵营的建筑ID
        cfgData = cfg01
        
        --士兵升级
        type = SpeedUpTypeEnum.UpgradeSoldier
        soldierID = soldierID --士兵的id，拿来读取士兵的对应信息
        count = count --要训练多少个士兵
        curResearchCompleteTime = math.floor(secondsToTime),--剩下的时间
        sid = nID --兵营的建筑ID
        cfgData = cfg01
        
        --士兵治疗
        type = SpeedUpTypeEnum.TreatmentSoldier
        soldiersList = {nSoldierID,nCount} --治疗的士兵信息
        curResearchCompleteTime --剩下的时间
        
        --装备工厂加速
        type = SpeedUpTypeEnum.EquipFactory
        equipId --装备的id，读图标用
        curResearchCompleteTime --剩下的时间
    ]]
    --endregion
    if self.data == nil then
        return;
    end
    
    self.selectIndex = -1;
    if not self.data.curResearchCompleteTime and self.data.uDoneTime then
        local curTimestamp = os.server_time();
        local curTime = self.data.uDoneTime - curTimestamp;
        self.data.curResearchCompleteTime = curTime
    end

    if self.data.curResearchCompleteTime < 1 then --小于2秒时应该打开界面过程中就被关掉了，会触发问题，所以直接return掉
        windowMgr:UnloadModule("ui_speed_up_panel")
        return;
    end
    
    
    local needDiamond = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
    self:TriggerUIEvent("UpdateQueue",self.data,needDiamond);

    local giftList = item_source_mgr.GetShowGifts(SpeedUpItemEnum[self.data.type] or 801, 2)
    self:TriggerUIEvent("SetGiftList",giftList)
    --加速完成了，如果该界面仍显示，说明是该界面进行的加速，此时挂起。
    self.finishStudyEvent = function()
        windowMgr:UnloadModule("ui_speed_up_panel")
        windowMgr:UnloadModule("ui_bs_acctip")
    end
    if self.data.type == SpeedUpTypeEnum.Technology then
        self.itemData = gw_home_common_data:GetTechnologyItem()
        event.Register(event.FINISH_TECHNOLOGY,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function()
            local queueData = technology_data:GetTechnologyQueueData()[self.data.buildID];
            --log.Error(queueData)
            self.data = queueData;
            if queueData == nil or self.data.curResearchCompleteTime <= 0 then
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end
            self.data.type = SpeedUpTypeEnum.Technology
            if windowMgr:IsModuleShown("ui_speed_up_panel") then
                local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                self.itemData = gw_home_common_data:GetTechnologyItem()
                for i,v in pairs(self.itemData) do
                    self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                    self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                end
                self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
            end

        end
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        --event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    elseif self.data.type == SpeedUpTypeEnum.Building then
        self.itemData = gw_home_common_data:GetBuildingSpeedUpItem()
        event.Register(event.FINISH_BUILDING,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function()
            local temp = self.data.queueIndex
            local queueData = gw_home_building_queue_data.GetQueueById(temp)--gw_home_common_data:GetSquadDataByIndex(self.data.buildID);
            if queueData == nil or queueData.uSid == 0 then --建筑sid为0表示当前没有在建造。
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end
            --log.Error(queueData)
            self.data = {};
            self.data.type = SpeedUpTypeEnum.Building;
            self.data.queueIndex = queueData.nID;
            
            local curTimestamp = os.server_time();
            local curTime = queueData.uDoneTime - curTimestamp;
            self.data.curResearchCompleteTime = curTime;
            local buildingData = gw_home_building_data.GetBuildingDataBySid(queueData.uSid)
            self.data.buildID = buildingData.nBuildingID;
            self.data.level = buildingData.nLevel;
            self.data.cfgData = game_scheme:Building_0(buildingData.nBuildingID,buildingData.nLevel)
            if windowMgr:IsModuleShown("ui_speed_up_panel") then
                local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                self.itemData = gw_home_common_data:GetBuildingSpeedUpItem()
                for i,v in pairs(self.itemData) do
                    self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                    self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                end
                self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
            end

        end
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    elseif self.data.type == SpeedUpTypeEnum.TrainingSoldier then
        self.itemData = gw_home_common_data:GetSoldierSpeedUpItem()
        event.Register(event.FINISH_SOLDIER_TRAINING,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function()
            local queueData = gw_home_soldier_data.GetMilitaryDataBySid(self.data.sid)
            if queueData == nil then
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end
            local sid = self.data.sid;
            --log.Error(queueData)
            self.data = {};
            self.data.type = SpeedUpTypeEnum.TrainingSoldier;
            self.data.sid = sid;
            self.data.count = queueData.nCount;
            local curTimestamp = os.server_time();
            local curTime = queueData.uDoneTime - curTimestamp;
            self.data.curResearchCompleteTime = curTime;
            if self.data.curResearchCompleteTime <= 0 then
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end
            self.data.soldierID = queueData.nSoldierID;
            self.data.cfgData = game_scheme:Soldier_0(queueData.nSoldierID)
            if windowMgr:IsModuleShown("ui_speed_up_panel") then
                local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                self.itemData = gw_home_common_data:GetSoldierSpeedUpItem()
                for i,v in pairs(self.itemData) do
                    self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                    self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                end
                self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
            end

        end
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    elseif self.data.type == SpeedUpTypeEnum.UpgradeSoldier then
        self.itemData = gw_home_common_data:GetSoldierSpeedUpItem()
        if self.data.curResearchCompleteTime <= 0 then
            windowMgr:UnloadModule("ui_speed_up_panel")
            windowMgr:UnloadModule("ui_bs_acctip")
            return;
        end
        local soldierCount = game_scheme:Soldier_nums()
        self.data.targetSoldierID = 0;
        local campusLv = 0
        local buildingLevel = gw_home_building_data.GetBuildingDataBySid(self.data.sid).nLevel
        for i=1,soldierCount do
            local soldierData = game_scheme:Soldier_1(i);
            local soldierID = soldierData.soldierID;
            local unlock = soldierData.unlock.data;
            if unlock[1] <= buildingLevel then
                if unlock[2] == nil then
                    if campusLv < soldierData.level then
                        campusLv = soldierData.level;
                        self.data.targetSoldierID = soldierID;
                    end
                else
                    if unlock[3] <= technology_data.GetScientificLevelByIndex(unlock[2]) then
                        if campusLv < soldierData.level then
                            campusLv = soldierData.level;
                            self.data.targetSoldierID = soldierID;
                        end
                    end
                end
            end
        end
        
        event.Register(event.FINISH_SOLDIER_TRAINING,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function()
            local queueData = gw_home_soldier_data.GetMilitaryDataBySid(self.data.sid)
            if queueData == nil then
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end
            --log.Error(queueData)
            --local sid = self.data.sid;
            --self.data = {};
            --self.data.type = SpeedUpTypeEnum.UpgradeSoldier;
            --self.data.sid = sid;
            --self.data.count = queueData.nCount;
            local curTimestamp = os.server_time();
            local curTime = queueData.uDoneTime - curTimestamp;
            self.data.curResearchCompleteTime = curTime;
            self.data.soldierID = queueData.nSoldierID;
            self.data.cfgData = game_scheme:Soldier_0(queueData.nSoldierID)
            if windowMgr:IsModuleShown("ui_speed_up_panel") then
                local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                self.itemData = gw_home_common_data:GetSoldierSpeedUpItem()
                for i,v in pairs(self.itemData) do
                    self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                    self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                end
                self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
            end


        end
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    elseif self.data.type == SpeedUpTypeEnum.TreatmentSoldier then
        self.itemData = gw_home_common_data:GetHospitalSpeedUpItem()
        if self.data.curResearchCompleteTime <= 0 then
            windowMgr:UnloadModule("ui_speed_up_panel")
            windowMgr:UnloadModule("ui_bs_acctip")
            return;
        end
        event.Register(event.FINISH_SOLDIER_TREATMENT,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function()
            local sceneType = self.data.sceneType or gw_const.enSandboxType.enSandboxType_World;
            local queueData = gw_home_soldier_data.GetHospitalData();
            if sceneType == gw_const.enSandboxType.enSandboxType_Desert then
                queueData = gw_storm_mgr.GetStormData().GetHospitalData();
            end
            if queueData == nil then
                windowMgr:UnloadModule("ui_speed_up_panel")
                windowMgr:UnloadModule("ui_bs_acctip")
                return;
            end

            --log.Error(queueData)
            self.data = {};
            self.data.type = SpeedUpTypeEnum.TreatmentSoldier;
            self.data.sceneType = sceneType

            local curTimestamp = os.server_time();
            local curTime = queueData.uDoneTime - curTimestamp;
            self.data.curResearchCompleteTime = curTime;
            self.data.soldiersList = queueData.soldiers;
            if self.data.curResearchCompleteTime <= 0 then
                windowMgr:UnloadModule("ui_bs_hospital_panel")
                windowMgr:UnloadModule("ui_speed_up_panel")
            else
                if windowMgr:IsModuleShown("ui_speed_up_panel") then
                    local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                    self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                    self.itemData = gw_home_common_data:GetHospitalSpeedUpItem()
                    for i,v in pairs(self.itemData) do
                        self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                        self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                    end
                    self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
                end

            end
        end
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    elseif self.data.type == SpeedUpTypeEnum.EquipFactory then
        --local curTimestamp = os.server_time();
        --local curTime = self.data.uDoneTime - curTimestamp;
        --self.data.curResearchCompleteTime = curTime
        self.itemData = gw_home_common_data:GetUniversalSpeedUpItem()
        if self.data.curResearchCompleteTime <= 0 then
            windowMgr:UnloadModule("ui_speed_up_panel")
            windowMgr:UnloadModule("ui_bs_acctip")
            return;
        end
        --event.Register(event.FINISH_BUILDING,self.finishStudyEvent)
        itemNeedCount = {}
        self.updateItemEvent = function(_,_data)
            --log.Error(queueData)
            local equipId = self.data.equipId;
            local curResearchAllTime = self.data.curResearchAllTime
            self.data = {};
            self.data.type = SpeedUpTypeEnum.EquipFactory;
            self.data.curResearchAllTime = curResearchAllTime
            local curTimestamp = os.server_time();
            local curTime = _data.uDoneTime - curTimestamp;
            self.data.curResearchCompleteTime = curTime;
            self.data.equipId = equipId;
            if self.data.curResearchCompleteTime <= 0 then
                --windowMgr:UnloadModule("ui_bs_hospital_panel")
                windowMgr:UnloadModule("ui_speed_up_panel")
            else
                if windowMgr:IsModuleShown("ui_speed_up_panel") then
                    local needDiamond1 = self:OnGetDiamondCostCount(self.data.curResearchCompleteTime)
                    self:TriggerUIEvent("UpdateQueue",self.data,needDiamond1);
                    self.itemData = gw_home_common_data:GetUniversalSpeedUpItem()
                    for i,v in pairs(self.itemData) do
                        self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
                        self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
                    end
                    self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
                end
            end
        end
        event.Register(gw_equip_define.HERO_EQUIP_BUILD_CHANGE,self.updateItemEvent)
        --event.Register(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
        --event.Register(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    end
    for i,v in pairs(self.itemData) do
        self.itemData[i].OnUseTenItemBtnClick = OnUseTenItemBtnClick;
        self.itemData[i].OnUseHundredItemBtnClick = OnUseHundredItemBtnClick;
    end
    self:TriggerUIEvent("UpdateList",self.itemData,self.selectIndex)
end

--[[界面被显示的时候调用]]
function UISpeedUpController:OnShow()
    if self.data.buildID == gw_home_building_data.GetBuildingIdByBuildingType(gw_const.enBuildingType.enBuildingType_Main)
            and (self.data.level == 3 or self.data.level == 4 or self.data.level == 5) then
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriEnterEvent(force_guide_event.tEventSpeedUpPanelClick)
    end
    self.__base.OnShow(self)
end


function UISpeedUpController:Close()
    --log.Error("Close")
    self.__base.Close(self)
    controller = nil
    event.Unregister(gw_equip_define.HERO_EQUIP_BUILD_CHANGE,self.updateItemEvent)
    event.Unregister(event.UPDATE_QUEUE,self.updateEvent)
    event.Unregister(event.SPEED_UP_SUCCESS_BY_USE_ITEM,self.updateItemEvent)
    event.Unregister(event.SPEED_UP_SUCCESS_BY_USE_DIAMOND,self.updateItemEvent)
    event.Unregister(event.FINISH_TECHNOLOGY,self.finishStudyEvent)
    event.Unregister(event.FINISH_BUILDING,self.finishStudyEvent)
    event.Unregister(event.FINISH_SOLDIER_TRAINING,self.finishStudyEvent)
    event.Unregister(event.FINISH_SOLDIER_TREATMENT,self.finishStudyEvent)
    itemNeedCount = {}
    self.selectIndex = -1;
end

function UISpeedUpController:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了  
    local UpdateRecharge = function()
        local giftList = item_source_mgr.GetShowGifts(SpeedUpItemEnum[self.data.type] or 801, 2)
        self:TriggerUIEvent("SetGiftList",giftList)
    end
    self:RegisterEvent(event.GW_REFRESH_RECHARGE_GOODS, UpdateRecharge)     --礼包数据刷新
end
---********************功能函数区**********---

--获得钻石与加速时间的换算比例。
function UISpeedUpController:OnGetDiamondCostCount(time)
    return technology_data:GetDiamondCountByTime(time);
end
--研究结束了，所以调用这个方法直接关闭界面。区别于直接关闭，还要发消息给服务器主动领取。
--注：外面的公共计时器已经收取，所以这里直接关闭，不做逻辑处理。
function UISpeedUpController:OnFinishStudy()
    windowMgr:UnloadModule("ui_speed_up_panel")
    windowMgr:UnloadModule("ui_bs_acctip")
end

function UISpeedUpController:OnCloseBtnClick()
    windowMgr:UnloadModule("ui_speed_up_panel")
    windowMgr:UnloadModule("ui_bs_acctip")
end

function UISpeedUpController:SetSelectIndex(index)
    self.selectIndex = index;
end

function  UISpeedUpController:OnBtn_Ad_1ClickedProxy()
end
function  UISpeedUpController:OnBtn_Ad_2ClickedProxy()
end

function UISpeedUpController:OnBtn_UseItem(dataItem,count)
    local itemIndex = dataItem.id;
    --请求加速使用道具
    local useItemData =
    {
        {
            itemid = itemIndex,
            nums = count,
            --dataItem = dataItem,
        }
    }
    if self.data.type == SpeedUpTypeEnum.Technology then
        local data4 =
        {
            moudleid = 2;
            arrItems = useItemData;
            extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
        }
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
    elseif self.data.type == SpeedUpTypeEnum.Building then
        local data5 =
        {
            moudleid = 1;
            arrItems = useItemData;
            extraInfo = tostring(self.data.queueIndex);
        }
        if self.data.buildID == gw_home_building_data.GetBuildingIdByBuildingType(gw_const.enBuildingType.enBuildingType_Main)
                and (self.data.level == 4 or self.data.level == 5) then
            local force_guide_system = require "force_guide_system"
            local force_guide_event = require "force_guide_event"
            force_guide_system.TriComEvent(force_guide_event.cEventSpeedUpPanelClick)
        end
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data5);
    elseif self.data.type == SpeedUpTypeEnum.TrainingSoldier then
        local data6 =
        {
            moudleid = 3;
            extraInfo = "1#"..self.data.sid.."#"..self.data.soldierID.."#0#"..self.data.count;
            arrItems = useItemData;
        }
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data6);
    elseif self.data.type == SpeedUpTypeEnum.UpgradeSoldier then
        local data7 =
        {
            moudleid = 3;
            extraInfo = "2#"..self.data.sid.."#"..self.data.soldierID.."#"..self.data.targetSoldierID.."#"..self.data.count;
            arrItems = useItemData;
        }
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data7);
    elseif self.data.type == SpeedUpTypeEnum.TreatmentSoldier then
        local str = ""
        local strList = {}
        for i,v in pairs(self.data.soldiersList) do
            table.insert(strList,v.nSoldierID.."#"..v.nCount..";")
        end
        str = table.concat(strList);
        local data8 =
        {
            moudleid = 4;
            extraInfo = str;
            arrItems = useItemData;
        }
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data8,self.data.sceneType);
    elseif self.data.type == SpeedUpTypeEnum.EquipFactory then
        local data9 =
        {
            moudleid = 5;
            extraInfo = "";
            arrItems = useItemData;
        }
        --请求加速使用道具
        net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data9);
    end
end

function OnUseTenItemBtnClick(index,dataItem)
    if itemNeedCount[dataItem.id] == nil or itemNeedCount[dataItem.id] < 0 then
        return;
    end
    local count = 10;
    if dataItem.count < count then
        count = dataItem.count;
        if itemNeedCount[dataItem.id] < dataItem.count then
            count = itemNeedCount[dataItem.id];
        end
    elseif itemNeedCount[dataItem.id] < count then
        count = itemNeedCount[dataItem.id];
    end
    local needTime = ui_speed_up_panel.GetCurTime();
    local count01 = math.floor(needTime/dataItem.time);
    if count < count01 then
        count01 = count;
    end
    e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","OnBtn_UseItem",dataItem,count01)
end

function OnUseHundredItemBtnClick(index,dataItem)
    if itemNeedCount[dataItem.id] == nil or itemNeedCount[dataItem.id] < 0 then
        return;
    end
    local count = 100;
    if dataItem.count < count then
        count = dataItem.count;
        if itemNeedCount[dataItem.id] < dataItem.count then
            count = itemNeedCount[dataItem.id];
        end
    elseif itemNeedCount[dataItem.id] < count then
        count = itemNeedCount[dataItem.id];
    end
    local needTime = ui_speed_up_panel.GetCurTime();
    local count01 = math.floor(needTime/dataItem.time);
    if count < count01 then
        count01 = count;
    end
    e_handler_mgr.TriggerHandler("ui_speed_up_panel_controller","OnBtn_UseItem",dataItem,count01)
end

function UISpeedUpController:SetItemNeedCount(itemId,itemCount)
    itemNeedCount[itemId] = itemCount;
end

function UISpeedUpController:OnBtn_UseDiamond(diamondNums)
    --请求加速使用钻石
    --[[
    local data3 =
    {
        moudleid = 2;
        diamondNums = diamondNums;
        extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
    }
    --请求加速使用钻石
    net_SpeedUp_module.MSG_SPEEDUPUSEDIAMOND_REQ(data3);
    ]]
    technology_data:ShowDiamondMessageBox(diamondNums,function()
        if self.data.type == SpeedUpTypeEnum.Technology then
            --一键钻石
            local data =
            {
                moudleid = 2;
                extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
                isBegin = true;
                diamondNums = diamondNums;
            }

            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data);
        elseif self.data.type == SpeedUpTypeEnum.Building then
            --一键钻石
            local data2 =
            {
                moudleid = 1;
                extraInfo = tostring(self.data.queueIndex);
                isBegin = true;
                diamondNums = diamondNums;
            }

            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data2);
        elseif self.data.type == SpeedUpTypeEnum.TrainingSoldier then
            local data3 =
            {
                moudleid = 3;
                extraInfo = "1#"..self.data.sid.."#"..self.data.soldierID.."#0#"..self.data.count;
                isBegin = true;
                diamondNums = diamondNums;
            }

            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data3);
        elseif self.data.type == SpeedUpTypeEnum.UpgradeSoldier then
            local data4 =
            {
                moudleid = 3;
                extraInfo = "2#"..self.data.sid.."#"..self.data.soldierID.."#"..self.data.targetSoldierID.."#"..self.data.count;
                isBegin = true;
                diamondNums = diamondNums;
            }

            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data4);
        elseif self.data.type == SpeedUpTypeEnum.TreatmentSoldier then
            local str = ""
            local strList = {}
            for i,v in pairs(self.data.soldiersList) do
                table.insert(strList,v.nSoldierID.."#"..v.nCount..";")
            end
            str = table.concat(strList);
            local data8 =
            {
                moudleid = 4;
                extraInfo = str;
                isBegin = true;
                diamondNums = diamondNums;
            }
            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data8,self.data.sceneType);
        elseif self.data.type == SpeedUpTypeEnum.EquipFactory then
            local data8 =
            {
                moudleid = 5;
                extraInfo = "";
                isBegin = true;
                diamondNums = diamondNums;
            }
            net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEDIAMOND_REQ(data8);
        end
    end)
end

function  UISpeedUpController:OnBtn_OneClickToSpeedUpClickedProxy()
    --log.Error("一键加速")
    if self.data.type == SpeedUpTypeEnum.Technology then
        local needTime = ui_speed_up_panel.GetCurTime();
        --local needTime = technology_data:GetTechnologyQueueData()[self.data.buildID].curResearchCompleteTime;
        if needTime ~= nil then
            local result,timeLeft  = gw_home_common_data.GetTechnologySpeedUpItemList(needTime)
            --[[
            result结构:
                local result = 
                {
                    totalTime = 0;
                    data = {};
                }
             local data =
            {
                id = tools[i].ID,
                time = tools[i].time * 60,
                cost = count,
            }
            result.data[tools[i].ID] = data;
            ]]

            --科研系统的加速模块为2
            --extraInfo格式：建筑ID#科技typeID#科技ID

            if timeLeft <= 0 then --道具可以完整加速
                local arrItems = {}
                for i,v in pairs(result.data) do
                    local useItemData =
                    {
                        itemid = v.id,
                        nums = v.cost,
                    }
                    table.insert(arrItems,useItemData);
                end
                --一键道具
                local data2 =
                {
                    moudleid = 2;
                    extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
                    isBegin = true;
                    arrItems = arrItems;
                    time = result.totalTime;
                }
                
                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    if a.type ~= b.type then
                        return a.type ~= 5 and b.type == 5
                    else
                        return a.time > b.time
                    end
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                --[[
                local arrItems = {}
                for i,v in pairs(result.data) do
                    local useItemData =
                    {
                        itemid = v.id,
                        nums = v.cost,
                    }
                    table.insert(arrItems,useItemData);
                end
                local data4 =
                {
                    moudleid = 2;
                    arrItems = arrItems;
                    extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
                }
    ]]
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            local result1,timeLeft1 = gw_home_common_data.GetTechnologySpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local data5 =
                                {
                                    moudleid = 2;
                                    extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
                                    isBegin = true;
                                    arrItems = arrItems;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5);
                            else

                                local data4 =
                                {
                                    moudleid = 2;
                                    arrItems = arrItems;
                                    extraInfo = self.data.buildID .. "#" .. self.data.curTypeID .. "#" .. self.data.curScienticID;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.itemCount = v.totalCount;
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        if a.type ~= b.type then
                            return a.type ~= 5 and b.type == 5
                        else
                            return a.time > b.time
                        end
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end

        end
    elseif self.data.type == SpeedUpTypeEnum.Building then
        local needTime = ui_speed_up_panel.GetCurTime();
        if needTime ~= nil then
            local result,timeLeft = gw_home_common_data.GetBuildingSpeedUpItemList(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)

            --建筑系统的加速模块为1
            --extraInfo格式：队列ID
            local arrItems = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
            end
            if timeLeft <= 0 then--result.totalTime >= needTime then --道具可以完整加速
                --一键道具
                local data2 =
                {
                    moudleid = 1;
                    extraInfo = tostring(self.data.queueIndex);
                    isBegin = true;
                    arrItems = arrItems;
                }
                
                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    if a.type ~= b.type then
                        return a.type ~= 5 and b.type == 5
                    else
                        return a.time > b.time
                    end
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            local result1,timeLeft1 = gw_home_common_data.GetBuildingSpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local data5 =
                                {
                                    moudleid = 1;
                                    extraInfo = tostring(self.data.queueIndex);
                                    arrItems = arrItems;
                                    isBegin = true;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5);
                            else
                                local data4 =
                                {
                                    moudleid = 1;
                                    arrItems = arrItems;
                                    extraInfo = tostring(self.data.queueIndex);
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        temp.itemCount = v.totalCount;
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        if a.type ~= b.type then
                            return a.type ~= 5 and b.type == 5
                        else
                            return a.time > b.time
                        end
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end
        end
    elseif self.data.type == SpeedUpTypeEnum.TrainingSoldier then
        local needTime = ui_speed_up_panel.GetCurTime();
        if needTime ~= nil then
            local result,timeLeft = gw_home_common_data.GetSoldierSpeedUpItemList(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)
            --士兵的加速是3
            --extraInfo格式：1#兵营sid#士兵id#0#数量
            local arrItems = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
            end
            if timeLeft <= 0 then--result.totalTime >= needTime then --道具可以完整加速
                --一键道具
                local data2 =
                {
                    moudleid = 3;
                    extraInfo = "1#"..self.data.sid.."#"..self.data.soldierID.."#0#"..self.data.count;
                    isBegin = true;
                    arrItems = arrItems;
                }

                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    if a.type ~= b.type then
                        return a.type ~= 5 and b.type == 5
                    else
                        return a.time > b.time
                    end
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            --local itemList1 = gw_home_common_data.GetSoldierSpeedUpItem(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)
                            local result1,timeLeft1 = gw_home_common_data.GetSoldierSpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local data5 =
                                {
                                    moudleid = 3;
                                    extraInfo = "1#"..self.data.sid.."#"..self.data.soldierID.."#0#"..self.data.count;
                                    arrItems = arrItems;
                                    isBegin = true;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5);
                            else
                                local data4 =
                                {
                                    moudleid = 3;
                                    arrItems = arrItems;
                                    extraInfo = "1#"..self.data.sid.."#"..self.data.soldierID.."#0#"..self.data.count;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        temp.itemCount = v.totalCount;
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        if a.type ~= b.type then
                            return a.type ~= 5 and b.type == 5
                        else
                            return a.time > b.time
                        end
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end
        end
    elseif self.data.type == SpeedUpTypeEnum.UpgradeSoldier then
        local needTime = ui_speed_up_panel.GetCurTime();
        if needTime ~= nil then
            local  result,timeLeft = gw_home_common_data.GetSoldierSpeedUpItemList(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)

            --士兵的加速是3
            --extraInfo格式：2#兵营sid#训练士兵id#目标士兵id#数量
            local arrItems = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
            end
            if timeLeft <= 0 then--result.totalTime >= needTime then --道具可以完整加速
                --一键道具
                local data2 =
                {
                    moudleid = 3;
                    extraInfo = "2#"..self.data.sid.."#"..self.data.soldierID.."#"..self.data.targetSoldierID.."#"..self.data.count;
                    isBegin = true;
                    arrItems = arrItems;
                }

                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    if a.type ~= b.type then
                        return a.type ~= 5 and b.type == 5
                    else
                        return a.time > b.time
                    end
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            local result1,timeLeft1 = gw_home_common_data.GetSoldierSpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local data5 =
                                {
                                    moudleid = 3;
                                    extraInfo = "2#"..self.data.sid.."#"..self.data.soldierID.."#"..self.data.targetSoldierID.."#"..self.data.count;
                                    arrItems = arrItems;
                                    isBegin = true;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5);
                            else
                                local data4 =
                                {
                                    moudleid = 3;
                                    arrItems = arrItems;
                                    extraInfo = "2#"..self.data.sid.."#"..self.data.soldierID.."#"..self.data.targetSoldierID.."#"..self.data.count;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        temp.itemCount = v.totalCount;
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        if a.type ~= b.type then
                            return a.type ~= 5 and b.type == 5
                        else
                            return a.time > b.time
                        end
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end
        end
    elseif self.data.type == SpeedUpTypeEnum.TreatmentSoldier then
        local needTime = ui_speed_up_panel.GetCurTime();
        if needTime ~= nil then
            local result,timeLeft = gw_home_common_data.GetHospitalSpeedUpItemList(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)

            --士兵治疗的加速模块为4
            --extraInfo格式：士兵1ID#士兵1数量;士兵2ID#士兵2数量..etc..
            local arrItems = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
            end
            if timeLeft <= 0 then--result.totalTime >= needTime then --道具可以完整加速
                --一键道具
                local str = ""
                local strList = {}
                for i,v in pairs(self.data.soldiersList) do
                    table.insert(strList,v.nSoldierID.."#"..v.nCount..";")
                end
                str = table.concat(strList);
                local data2 =
                {
                    moudleid = 4;
                    extraInfo = str;
                    isBegin = true;
                    arrItems = arrItems;
                }

                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2,self.data.sceneType);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    if a.type ~= b.type then
                        return a.type ~= 5 and b.type == 5
                    else
                        return a.time > b.time
                    end
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            local result1,timeLeft1 = gw_home_common_data.GetHospitalSpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local str = ""
                                local strList = {}
                                for i,v in pairs(self.data.soldiersList) do
                                    table.insert(strList,v.nSoldierID.."#"..v.nCount..";")
                                end
                                str = table.concat(strList);
                                local data5 =
                                {
                                    moudleid = 4;
                                    extraInfo = str;
                                    isBegin = true;
                                    arrItems = arrItems;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5,self.data.sceneType);
                            else
                                local str = ""
                                local strList = {}
                                for i,v in pairs(self.data.soldiersList) do
                                    table.insert(strList,v.nSoldierID.."#"..v.nCount..";")
                                end
                                str = table.concat(strList);
                                local data4 =
                                {
                                    moudleid = 4;
                                    arrItems = arrItems;
                                    extraInfo = str;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4,self.data.sceneType);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        temp.itemCount = v.totalCount;
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        if a.type ~= b.type then
                            return a.type ~= 5 and b.type == 5
                        else
                            return a.time > b.time
                        end
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end
        end
    elseif self.data.type == SpeedUpTypeEnum.EquipFactory then
        local needTime = ui_speed_up_panel.GetCurTime();
        if needTime ~= nil then
            local result,timeLeft = gw_home_common_data.GetUniversalSpeedUpItemList(needTime)--technology_data.GetTechnologySpeedUpItemList(needTime)

            --士兵治疗的加速模块为4
            --extraInfo格式：士兵1ID#士兵1数量;士兵2ID#士兵2数量..etc..
            local arrItems = {}
            for i,v in pairs(result.data) do
                local useItemData =
                {
                    itemid = v.id,
                    nums = v.cost,
                }
                table.insert(arrItems,useItemData);
            end
            if timeLeft <= 0 then--result.totalTime >= needTime then --道具可以完整加速
                --一键道具
                local data2 =
                {
                    moudleid = 5;
                    extraInfo = "";
                    isBegin = true;
                    arrItems = arrItems;
                }

                local accTipData =
                {
                    InfoData = {
                        title = 15512;
                        time = result.totalTime;--totalTime;
                        color = "#319F38";
                    },
                    ListData = {
                    },
                    confirmCallback = function()
                        net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data2);
                        windowMgr:UnloadModule("ui_speed_up_panel");
                    end
                }
                for i,v in pairs(result.data) do
                    local temp = {}
                    temp.goodId = v.id;
                    temp.goodCount = v.cost;
                    temp.cfgData = game_scheme:Item_0(v.id);
                    temp.itemCount = v.totalCount;
                    temp.type = v.type;--排序用
                    temp.time = v.time;--排序用
                    table.insert(accTipData.ListData,temp);
                end
                table.sort(accTipData.ListData,function(a, b)
                    return a.time > b.time
                end)
                windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
            else
                if result.totalTime <= 0 then
                    flow_text.Add(lang.Get(603010))
                else
                    local accTipData =
                    {
                        InfoData = {
                            title = 15512;
                            time = result.totalTime;--totalTime;
                            color = "#ef5542";
                        },
                        ListData = {
                        },
                        confirmCallback = function()
                            --请求加速使用道具
                            --再次做一次拦截
                            local result1,timeLeft1 = gw_home_common_data.GetHospitalSpeedUpItemList(needTime)
                            local arrItems = {}
                            for i,v in pairs(result1.data) do
                                local useItemData =
                                {
                                    itemid = v.id,
                                    nums = v.cost,
                                }
                                table.insert(arrItems,useItemData);
                            end
                            if timeLeft1 <= 0 then--result1.totalTime >= needTime then
                                --一键道具
                                local data5 =
                                {
                                    moudleid = 5;
                                    extraInfo = "";
                                    isBegin = true;
                                    arrItems = arrItems;
                                }
                                net_SpeedUp_module.MSG_SPEEDUPONECLICKUSEITEM_REQ(data5);
                            else
                                local data4 =
                                {
                                    moudleid = 5;
                                    arrItems = arrItems;
                                    extraInfo = "";
                                }
                                net_SpeedUp_module.MSG_SPEEDUPUSEITEM_REQ(data4);
                            end
                            windowMgr:UnloadModule("ui_speed_up_panel");
                        end
                    }
                    for i,v in pairs(result.data) do
                        local temp = {}
                        temp.goodId = v.id;
                        temp.goodCount = v.cost;
                        temp.cfgData = game_scheme:Item_0(v.id);
                        temp.type = v.type;--排序用
                        temp.time = v.time;--排序用
                        temp.itemCount = v.totalCount;
                        table.insert(accTipData.ListData,temp);
                    end
                    table.sort(accTipData.ListData,function(a, b)
                        return a.time > b.time
                    end)
                    windowMgr:ShowModule("ui_bs_acctip",nil,nil,accTipData)
                end
            end
        end
        
    end
    
end
--[[
function UISpeedUpController:ShowDiamondMessageBox(diamondCount,callback)
    local value = player_mgr.GetDiamondTips();
    if value then
        local message_box = require "message_box"
        message_box.Open(string.format2(lang.Get(601260),diamondCount), message_box.STYLE_YESNO, function(d, r)
            if r == message_box.RESULT_YES then
                log.Error("Yes")
                callback()
            else
                log.Error("No")
            end
        end, nil, lang.Get(30708), lang.Get(30707), lang.KEY_SYSTEM_TIPS, nil, true, nil, nil, nil, nil, nil, nil, lang.Get(182147), 
                function(isOn)
            player_mgr.SetDiamondTips(not isOn)
        end)

    end
end
]]
---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UISpeedUpController.new()
    end
    return controller
end
--endregion
