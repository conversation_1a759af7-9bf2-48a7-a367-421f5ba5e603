
--- ui_mini_technology_queue_panel_controller.txt
--- Generated by EmmyLua(https://github.com/EmmyLua)
--- Created by .
--- DateTime: 
--- desc:    
---
local require = require
local ipairs = ipairs
local newclass = newclass

local event = require "event"
local module_scroll_list = require "scroll_list"
local log = require "log"
local controller_base = require "controller_base"
local windowMgr = require "ui_window_mgr"
local technology_data = require "technology_data"
local net_ScientificResearch_module = require "net_scientific_research_module"
local ui_speed_up_panel_controller = require "ui_speed_up_panel_controller"
local AllianceMgr  = require "alliance_mgr"
local event_alliance_define = require "event_alliance_define"
local alliance_const = require "alliance_const"
local GWConst = GWConst
local GWG = GWG
local tostring = tostring

module("ui_mini_technology_queue_panel_controller")
--TODO  类的实现方式后续需要优化，或者不用基类 现在的class的继承很耗时，
local controller = nil
local UIMiniTechnologyQueuePanelController = newclass("ui_mini_technology_queue_panel_controller", controller_base)

--[[窗口初始化]]
function UIMiniTechnologyQueuePanelController:Init(view_name, controller_name, data)
    self.__base.Init(self,view_name,controller_name)
    self:SubscribeEvents()
    local unlock = technology_data:GetSecondQueueUnlock();
    local queueData = technology_data:GetTechnologyQueueData()
    self:TriggerUIEvent("UpdateQueue",queueData,unlock);

    self:ShowAllianceBtn()
    
end

--[[界面被显示的时候调用]]
function UIMiniTechnologyQueuePanelController:OnShow()
    self.__base.OnShow(self)
end

function UIMiniTechnologyQueuePanelController:Close()
    self.__base.Close(self)
    controller = nil
    --event.Unregister(event.UPDATE_QUEUE,self.updateEvent)
    --event.Unregister(event.GET_TECHNOLOGY,self.updateEvent)
    --event.Unregister(event.QUEUE01_FINISH,self.updateEvent)
    --event.Unregister(event.QUEUE02_FINISH,self.updateEvent)
end

function UIMiniTechnologyQueuePanelController:SubscribeEvents()
    -- 注意 事件建议使用self:RegisterEvent(event_name,func)来注册，不需要手动维护注销了   
    self.updateEvent = function()
        local unlock1 = technology_data:GetSecondQueueUnlock();
        local queueData1 = technology_data:GetTechnologyQueueData()
        self:TriggerUIEvent("UpdateQueue",queueData1,unlock1);
    end
    self:RegisterEvent(event.UPDATE_QUEUE,self.updateEvent)
    self.finishEvent01 = function(fuName,data)
        local queueData = technology_data:GetTechnologyQueueData()
        for i,v in ipairs(data) do
            if queueData[GWConst.ScientificBuildingId.Building_1] ~= nil and queueData[GWConst.ScientificBuildingId.Building_1].curScienticID == v.scientificID then
                self:TriggerUIEvent("FinishCallback",1);
            end
            if queueData[GWConst.ScientificBuildingId.Building_2] ~= nil and queueData[GWConst.ScientificBuildingId.Building_2].curScienticID == v.scientificID then
                self:TriggerUIEvent("FinishCallback",2);
            end
        end
    end
    --self:RegisterEvent(event.GET_TECHNOLOGY,self.updateEvent)
    self.allianceToModule = function(eventName, funcName, ...)
        if funcName and UIMiniTechnologyQueuePanelController[funcName] then
            UIMiniTechnologyQueuePanelController[funcName](self, ...)
        end
    end
    self:RegisterEvent(event_alliance_define.ALLIANCE_TO_MODULE, self.allianceToModule)
end
---********************功能函数区**********---
function UIMiniTechnologyQueuePanelController:OnShowTechnologyMainPanel()
    windowMgr:ShowModule("ui_technology_main_panel")
    windowMgr:UnloadModule("ui_mini_technology_queue_panel")
end

function UIMiniTechnologyQueuePanelController:OnCloseBtnClick()
    windowMgr:UnloadModule("ui_mini_technology_queue_panel")
end

function UIMiniTechnologyQueuePanelController:OnQueue01SpeedUp()
    local queueData = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_1];
    queueData.type = ui_speed_up_panel_controller.SpeedUpTypeEnum.Technology;
    --log.Error(queueData)
    windowMgr:ShowModule("ui_speed_up_panel",nil,nil,queueData)
    --windowMgr:UnloadModule("ui_mini_technology_queue_panel")
end

function UIMiniTechnologyQueuePanelController:OnQueue02SpeedUp()
    local queueData = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_2];
    queueData.type = ui_speed_up_panel_controller.SpeedUpTypeEnum.Technology;
    --log.Error(queueData)
    windowMgr:ShowModule("ui_speed_up_panel",nil,nil,queueData)
    --windowMgr:UnloadModule("ui_mini_technology_queue_panel")
end

function UIMiniTechnologyQueuePanelController:FinishStudy01()
    --net_ScientificResearch_module.MSG_SCIENTIFICRESEARCH_GETRESEARCH_REQ(1)
    technology_data:FinishTechnology(GWConst.ScientificBuildingId.Building_1)
    --self:TriggerUIEvent("FinishCallback",1);
end

function UIMiniTechnologyQueuePanelController:FinishStudy02()
    --net_ScientificResearch_module.MSG_SCIENTIFICRESEARCH_GETRESEARCH_REQ(2)
    technology_data:FinishTechnology(GWConst.ScientificBuildingId.Building_2)
    --self:TriggerUIEvent("FinishCallback",2);
end

function UIMiniTechnologyQueuePanelController:HelpStudy01()
    local queueData = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_1];
    if queueData and (queueData.curResearchCompleteTime > 0 and queueData.curResearchAllTime > 0) then
        technology_data.ReportHelp(GWConst.ScientificBuildingId.Building_1)
        AllianceMgr.ModuleToAllianceHelpREQ(alliance_const.EAllianceHelpType.EAllianceHelpType_Study, GWConst.ScientificBuildingId.Building_1)
    end
end

function UIMiniTechnologyQueuePanelController:HelpStudy02()
    local queueData = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_2];
    if queueData and (queueData.curResearchCompleteTime > 0 and queueData.curResearchAllTime > 0) then
        technology_data.ReportHelp(GWConst.ScientificBuildingId.Building_2)
        AllianceMgr.ModuleToAllianceHelpREQ(alliance_const.EAllianceHelpType.EAllianceHelpType_Study, GWConst.ScientificBuildingId.Building_2)
    end
end

---@public 是否显示联盟按钮
function UIMiniTechnologyQueuePanelController:ShowAllianceBtn()
    local result = {}
    local queueData1 = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_1];
    local queueData2 = technology_data:GetTechnologyQueueData()[GWConst.ScientificBuildingId.Building_2];
    if queueData1 and (queueData1.curResearchCompleteTime > 0 and queueData1.curResearchAllTime > 0) then
        result[GWConst.ScientificBuildingId.Building_1] = AllianceMgr.GetCheckHelpData(alliance_const.EAllianceHelpType.EAllianceHelpType_Study, GWConst.ScientificBuildingId.Building_1)
    else
        result[GWConst.ScientificBuildingId.Building_1] = false
    end
    if queueData2 and (queueData2.curResearchCompleteTime > 0 and queueData2.curResearchAllTime > 0) then
        result[GWConst.ScientificBuildingId.Building_2] = AllianceMgr.GetCheckHelpData(alliance_const.EAllianceHelpType.EAllianceHelpType_Study, GWConst.ScientificBuildingId.Building_2)
    else
        result[GWConst.ScientificBuildingId.Building_2] = false
    end
    self:TriggerUIEvent("RefreshShowAllianceBtn",result)
end
---@public 联盟帮助网络返回
function UIMiniTechnologyQueuePanelController:AllianceHelpStartRsp(keyTable,valueTable,msg)
    self:ShowAllianceBtn()
end

function UIMiniTechnologyQueuePanelController:UnlockSecondQueue()
    windowMgr:UnloadModule("ui_mini_technology_queue_panel")
    local buildingData = GWG.GWHomeMgr.buildingData.GetMaxLevelBuildingDataByBuildingID(GWConst.ScientificBuildingId.Building_2)
    if buildingData then
        local flag = GWG.GWAdmin.HomeCommonUtil.GetCityFlagState(buildingData.uFlag, GWConst.enCityFlag.enCityFlag_BuildingOpenGift)
        if flag then --未剪彩，视为未解锁
            local gw_jump_util = require "gw_jump_util"
            local buildType = tostring(GWConst.enBuildingType.enBuildingType_Research2)
            gw_jump_util.JumpToBuild(buildType)
        else
            windowMgr:ShowModule("ui_unlock_second_technology_panel")
        end
    end
end

---********************end功能函数区**********---
--region ModuleFunction 
function Show()
    if controller == nil then
        controller = UIMiniTechnologyQueuePanelController.new()
    end
    return controller
end
--endregion
