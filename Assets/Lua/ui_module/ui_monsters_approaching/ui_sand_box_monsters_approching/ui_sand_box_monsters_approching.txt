local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local tostring = tostring
local tonumber = tonumber
local type = type
local os = os
local Common_Util = CS.Common_Util.UIUtil
local val = require "val"

local VerticalLayoutGroup = CS.UnityEngine.UI.VerticalLayoutGroup
local Image = CS.UnityEngine.UI.Image

local enum_define = require "enum_define"
local log = require "log"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_sand_box_monsters_approching_binding"
local time_util = require "time_util"
local event = require "event"
local red_const = require "red_const"
local effect_item =require "effect_item"
local main_slg_mgr = require "main_slg_mgr"
local windowMgr = require "ui_window_mgr"
local redPerf = val.IsTrue("sw_red_system_perf", 0)
local GWMgr = require "gw_mgr"
local GWConst = require "gw_const"
local typeof = typeof

--region View Life
module("ui_sand_box_monsters_approching")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self.recycle_ui = true
    self.VData = {}
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self.TweenToShow()
end

function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close()   
    if self.recycle_ui then
        self.__base:Close()
    else
        if self.VData then
            for i, v in pairs(self.VData) do
                if type(v) == "table" and v.Dispose then
                    v:Dispose()
                end
            end
        end
        self.MonsterAttackTarTime = 0
        if self.MonsterAttackTime then
            self:RemoveTimer(self.MonsterAttackTime)
            self.MonsterAttackTime = nil
        end
        self.VData = nil    
        self.__base:Close()
    end
    
end
--endregion

--region View Logic

--endregion

--更新怪物攻城状态
function UIView:UpdateMonsterAttack(data)
    local monsters_approaching_define = require "monsters_approaching_define" 
    if not data or not data.dataStage then
        Common_Util.SetActive(self.rtf_MonsterApproaching, false)
        -- EndGuide()
        return
    end
    if data.dataStage == monsters_approaching_define.DataStage.Doing then
        --打开界面判断 是否触发弱引导
        -- local gw_bomberman_mgr = require "gw_bomberman_mgr"
        -- gw_bomberman_mgr.TriggerBombermanWeak()
        self:UpdateMonsterAttackCountdown(data.tarTime)
        self.txt_MonsterApproDes.text = lang.Get(data.ActivityName)
        local effect_res = data.MainSlgEffect
        if effect_res ~= self.MonsterMainSlgEffect then
            if self.MonsterEffect then 
                self.MonsterEffect:Dispose()
                self.MonsterEffect = nil
            end
            if not util.IsNullOrEmpty(effect_res) then
                self.MonsterEffect = effect_item.CEffectItem():Init(effect_res, self.rtf_MonsterEffect, window.curOrder + 1, nil, 1)
            end
        end
        self.MonsterMainSlgEffect = effect_res
        if data.iconIndex then 
            self.ss_Monster_Appro:Switch(data.iconIndex)
            self.ss_Monster_Appro.transform:GetComponent(typeof(Image)):SetNativeSize()
        end
        --设置 在沙盘中的入口显示 还是在主城中的入口显示
        local verticalObj = self.rtf_InfoPanel.transform:GetComponent(typeof(VerticalLayoutGroup)) --调整布局参数
        local PosX = self.rtf_MonsterApproaching.anchoredPosition.x
        local PosY = self.rtf_MonsterApproaching.anchoredPosition.y



        if GWMgr.curScene == GWConst.ESceneType.Home then 
            Common_Util.SetActive(self.sld_MonsterAppro,true) --在主城则显示进度条
            if verticalObj then 
                verticalObj.spacing = monsters_approaching_define.SceneEntranceOffset.Main.Spacing
                verticalObj.padding.bottom = monsters_approaching_define.SceneEntranceOffset.Main.Bottom
            end
            --调整入口所在的位置
            PosY = monsters_approaching_define.SceneEntranceOffset.Main.locY
            --调整红点位置
            local redX = monsters_approaching_define.SceneEntranceOffset.Main.redData.x
            local redY = monsters_approaching_define.SceneEntranceOffset.Main.redData.y
            self.rtf_redPos.transform.anchoredPosition = {x = redX,y = redY}
            --调整气泡位置
            local BubblePosX = self.img_BubbleBg.transform.anchoredPosition.x
            local BubblePoxY = monsters_approaching_define.SceneEntranceOffset.Main.BubblelocY
            self.img_BubbleBg.transform.anchoredPosition = {x=BubblePosX,y=BubblePoxY}
        elseif GWMgr.curScene == GWConst.ESceneType.Sand then
            Common_Util.SetActive(self.sld_MonsterAppro,false)--在沙盘则不显示进度条
            if verticalObj then 
                verticalObj.spacing = monsters_approaching_define.SceneEntranceOffset.Sand.Spacing
                verticalObj.padding.bottom = monsters_approaching_define.SceneEntranceOffset.Sand.Bottom
            end
            --调整入口所在的位置
            PosY = monsters_approaching_define.SceneEntranceOffset.Sand.locY
            --调整红点位置
            local redX = monsters_approaching_define.SceneEntranceOffset.Sand.redData.x
            local redY = monsters_approaching_define.SceneEntranceOffset.Sand.redData.y
            self.rtf_redPos.transform.anchoredPosition = {x = redX,y = redY}
            --调整气泡位置
            local BubblePosX = self.img_BubbleBg.transform.anchoredPosition.x
            local BubblePoxY = monsters_approaching_define.SceneEntranceOffset.Sand.BubblelocY
            self.img_BubbleBg.transform.anchoredPosition = {x=BubblePosX,y=BubblePoxY}
        end
        self:BindUIRed(self.rtf_redPos.transform, red_const.Enum.MonsterAttackCity, nil, {})
        self.rtf_MonsterApproaching.anchoredPosition = {x = PosX,y=PosY}
        Common_Util.SetActive(self.rtf_MonsterApproaching, true)
    else
        -- EndGuide()
        Common_Util.SetActive(self.rtf_MonsterApproaching, false)
    end
end

--更新怪物攻城进度条
function UIView:UpdateMonsterAttackSlider(data)
    if not data or not data.curNum or not data.allNum then 
        return
    end
    if data.allNum and data.allNum == 0 then 
        return 
    end
    self.sld_MonsterAppro.value = data.curNum / data.allNum
    self.txt_BomberSlider.text = string.format2(lang.Get(602017), data.curNum, data.allNum)
end

--更新怪物攻城的说话
function UIView:SetMonsterAttackTalk(data)
    if not data or not data.stage then
        Common_Util.SetActive(self.img_BubbleBg, false)
        return
    end
    local stage = data.stage
    local desId = data.desId
    local monsters_approaching_define = require "monsters_approaching_define"
    if stage == monsters_approaching_define.PopStage.Hide then
        Common_Util.SetActive(self.img_BubbleBg, false)
    else
        Common_Util.SetActive(self.img_BubbleBg, true)
        if desId then
            self.txt_MonsterApproPop.text = lang.Get(desId)
            Common_Util.SetActive(self.img_BubbleBg, true)
        else
            Common_Util.SetActive(self.img_BubbleBg, false)
        end
    end
end

--怪物攻城倒计时更新
function UIView:UpdateMonsterAttackCountdown(tarTime)
    if not tarTime then 
        return 
    end
    self:StopMonsterAttackTimer()
    if tarTime and tarTime > 0 then
        self.MonsterAttackTarTime = tarTime
        local isEnd = self:SetMonsterAttackTimeText()
        if not isEnd then
            self.MonsterAttackTime = self:CreateTimer(0.9, function()
                if not self.MonsterAttackTime then 
                    return
                end
                local isEnd = self:SetMonsterAttackTimeText()
                if isEnd then
                    self:StopMonsterAttackTimer()
                    local monsters_approaching_define = require "monsters_approaching_define"
                    event.Trigger(monsters_approaching_define.UNLOCK_MONSTER_ATTACK)
                end
            end)
        end
    end
end

--关闭怪物攻城倒计时
function UIView:StopMonsterAttackTimer()
    if self and self:IsValid() then
        self.txt_MonsterApproTime.text = ""
    end
    if self.MonsterAttackTime then
        self:RemoveTimer(self.MonsterAttackTime)
        self.MonsterAttackTime = nil
    end
end
--设置怪物攻城倒计时
function UIView:SetMonsterAttackTimeText()
    if self.MonsterAttackTarTime then
        local serverTime = os.server_time()
        local offTime = self.MonsterAttackTarTime - serverTime
        if offTime >= 0 then
            self.txt_MonsterApproTime.text = time_util.FormatTimeXMan(offTime)
            return false
        end
    end
    return true
end
--修改怪物攻城UI尺寸
function UIView:OnRefreshMonsterUI(isSmall)
    if not self:IsValid() then
        return
    end
    local scale = isSmall and 0.85 or 1
    local height = isSmall and 100 or 180
    -- local layout = isSmall and -5 or 5
    self.rtf_MonsterApproaching.localScale = {x = scale, y = scale}
    local size = self.rtf_MonsterApproaching.sizeDelta
    self.rtf_MonsterApproaching.sizeDelta = {x = size.x, y = height}
    -- self.Layout_LeftBottom_Content.spacing = layout
    if GWMgr.curScene == GWConst.ESceneType.Home then 
        local monsters_approaching_define = require "monsters_approaching_define"
        local locX = self.rtf_MonsterApproaching.anchoredPosition.x
        local locY = isSmall and monsters_approaching_define.SpecialLocY or monsters_approaching_define.SceneEntranceOffset.Main.locY
        self.rtf_MonsterApproaching.anchoredPosition = {x = locX,y = locY}
    end
    
end

-- function UIView:SetTeamActive(isActive)
--     if self:IsValid() then
--         self.UIRoot.gameObject:SetActive(isActive)    
--     end
-- end

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME
        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, true)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, true)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        if not window.recycle_ui then
            window = nil
        end
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function UIView.TweenToShow()

    if not window or not window:IsValid() then
        return
    end
    local self = window
    if self.isShowFinish then
        return
    end
    main_slg_mgr.MoveTweenXAuto(self.rtf_move, true, true,function()
        self.isShowFinish = true
    end)
end

function UIView.TweenToClose()
    if not window or not window:IsValid() then
        return
    end

    local self = window
    self.isShowFinish = false
    main_slg_mgr.MoveTweenXAuto(self.rtf_move,true,false,function()
        windowMgr:UnloadModule(_NAME)
    end)

end
--endregion
