local print = print
local require = require
local pairs = pairs     
local ipairs = ipairs
local string = string
local table = table
local newClass = newclass
local type = type
local os = os
local controller_base = require "controller_base"
local ui_window_mgr = require "ui_window_mgr"
local lang = require "lang"
local event = require "event"
local util = require "util"
local doomsday_data = require "doomsday_data"
local doomsday_define = require "doomsday_define"
local ToggleType = doomsday_define.ToggleType

--region Controller Life
module("ui_doomsday_activity_controller")
local controller = nil
local UIController = newClass("ui_doomsday_activity_controller", controller_base)

function UIController:Init(view_name, controller_name, data)
    self.CData = {}
    self.__base.Init(self, view_name, controller_name)    
end

function UIController:StartTime()
    self:RemoveExpireTime()
    local activityData = doomsday_data.GetActivityData()
    if activityData then
        self.cdTimer = util.IntervalCall(1,
        function()
            local diff = activityData.endTimeStamp - os.server_time()
            if os.server_time() > activityData.endTimeStamp then
                self:RemoveExpireTime()
                event.Trigger(event.ACTIVITY_TIME_UP)
            else
                self:TriggerUIEvent("UpdateTime",diff)
            end
        end)
    end
end

function UIController:OnShow()
    self.__base.OnShow(self)
    local net_doomsday_module = require "net_doomsday_module"
    net_doomsday_module.TMSG_SKY_FALL_THREAT_MONSTER_REQ()
    self:UpdateNormalUI()
    self:StartTime()
end

function UIController:UpdateNormalUI()
    local normalServerData = doomsday_data.GetNormalServerData()
    self.curToggleType = self.curToggleType or ToggleType.ALLIANCE
    self:TriggerUIEvent("StarNextWaveCountDownTime",normalServerData.refreshtime)
    self:TriggerUIEvent("UpdateNormalUI",self.curToggleType,normalServerData.allianceThreat,normalServerData.battleAreaThreat)
    self:TriggerUIEvent("UpdateFirstPlayerUI",normalServerData.firstRank)
end

function UIController:Close(data)   
    if self.CData then
        for i, v in pairs(self.CData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end

    self:RemoveExpireTime()
    -- if ui_window_mgr:IsModuleShown("ui_zombies_attack_activity_shop") then
    --     ui_window_mgr:UnloadModule("ui_zombies_attack_activity_shop")
    -- end

    self.CData = nil
    if not data or not data.isRecycleUI then
        self.__base.Close(self)
        controller = nil
    end
end

function UIController:RemoveExpireTime()
    if self.cdTimer then
        util.RemoveDelayCall(self.cdTimer)
        self.cdTimer = nil
    end
end

function UIController:AutoSubscribeEvents() 
    self.updateNormalServerData = function ()
        self:UpdateNormalUI()
    end
    self:RegisterEvent(doomsday_define.TMSG_SKY_FALL_THREAT_MONSTER_RSP,self.updateNormalServerData)

    local sand_ui_event_define = require "sand_ui_event_define"
    self.SANDBOX_BATTLE_NTF = function ()
        local net_doomsday_module = require "net_doomsday_module"
        net_doomsday_module.TMSG_SKY_FALL_THREAT_MONSTER_REQ()
    end
    self:RegisterEvent(sand_ui_event_define.SANDBOX_BATTLE_NTF, self.SANDBOX_BATTLE_NTF)
end

function UIController:AutoUnsubscribeEvents() 
end
--endregion

--region Controller Logic
function  UIController:OnBtnTipsClickedProxy()
    local ui_help = require "ui_help"
    ui_help.ShowWithDate(10071) 
end

function  UIController:OnBtnAwardClickedProxy()
    ui_window_mgr:ShowModule("ui_doomsday_activity_pop",nil,nil,{curIndex = 1})
end
function  UIController:OnBtnRankClickedProxy()
    ui_window_mgr:ShowModule("ui_doomsday_activity_pop",nil,nil,{curIndex = 2})
end

function  UIController:OnTogAllicanceValueChange(state)
    if state then
        self.curToggleType = ToggleType.ALLIANCE
        local normalServerData = doomsday_data.GetNormalServerData()
        self:TriggerUIEvent("UpdateToggleUI",self.curToggleType,normalServerData.allianceThreat)
    end
end
function  UIController:OnTogAreaValueChange(state)
    if state then
        self.curToggleType = ToggleType.AREA
        local normalServerData = doomsday_data.GetNormalServerData()
        self:TriggerUIEvent("UpdateToggleUI",self.curToggleType,normalServerData.battleAreaThreat)
    end
end

function  UIController:OnBtnCloseBtnClickedProxy()
	ui_window_mgr:UnloadModule(self.view_name)
end

--endregion

--region Controller Static 
function Show()
    if controller == nil then
        controller = UIController.new()
    end
    return controller
end
--endregion
