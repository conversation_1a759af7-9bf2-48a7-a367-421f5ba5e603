local require = require
local pairs = pairs     
local string = string
local table = table
local type = type
local UIUtil = CS.Common_Util.UIUtil
local os = os
local enum_define = require "enum_define"
local ui_base = require "ui_base"
local lang = require "lang"
local util = require "util"
local class = require "class"
local binding = require "ui_doomsday_activity_binding"
local doomsday_define = require "doomsday_define"
local time_util = require "time_util"
local ToggleType = doomsday_define.ToggleType
local monster_item = require "monster_item"
local alliance_mgr = require "alliance_mgr"
local red_const = require "red_const"
local face_item_new = require "face_item_new"
local string_util = require "string_util"
--region View Life
module("ui_doomsday_activity")
local ui_path = binding.UIPath
local window = nil
local UIView = {}

UIView.widget_table = binding.WidgetTable

function UIView:SetVCTypeUI()
    return self.__base.SetVCTypeUI(self, enum_define.enum_ui_vc_type.vc)
end

function UIView:Init()
    self:SetVCTypeUI()
    self.__base.Init(self)
    self:InitScrollRectTable()
    self.VData = {}
    --TMSG_ZOMBIE_COMING_ACT_INFO_REQ上次请求时间戳
    self.lastReqTime = 0
    self.isStopReq = false
end

function UIView:OnShow()
    self.__base.OnShow(self)
    self:BindUIRed(self.btn_award.transform, red_const.Enum.DoomsdayRewardRed,nil, {redPath = red_const.Type.Default })

end

function UIView:UpdateUI()
    self.canvas_panel = self.curOrder + 1
end


function UIView:UpdateNormalUI(toggleType,allianceThreat,battleAreaThreat)
    local leaderDataList = ToggleType.ALLIANCE == toggleType and allianceThreat or battleAreaThreat or {}
    self:UpdateToggleUI(toggleType,leaderDataList)
    self.txt_allicanceTogText.text = string.format("%s(%s)",lang.Get(1004265),allianceThreat and #allianceThreat or 0)
    self.txt_areaTogText.text = string.format("%s(%s)",lang.Get(1004266),battleAreaThreat and #battleAreaThreat or 0)
    self.timeTextComponent = self.txt_countDownTimeText
    self.timeFormat = "{%s1}"
end

function UIView:UpdateFirstPlayerUI(firstRank)
    if firstRank then
        local faceItem = self.faceItem or face_item_new.CFaceItem():Init(self.rtf_faceRoot, nil, 1)
        faceItem:SetFaceInfo(firstRank.faceID,function()
            local mgr_personalInfo = require "mgr_personalInfo"
            mgr_personalInfo.ShowRoleInfoView(firstRank.dbid)
        end)
        faceItem:SetFrameID(firstRank.frameID, true)
        self.faceItem = faceItem
        self.txt_name.text = string.format2("[{%s1}] {%s2}",firstRank.leagueShortName,firstRank.name)
        self.txt_hurt.text = lang.Get(2104)..string_util.ToScientificNotation(firstRank.totalDamage)
    end
    UIUtil.SetActive(self.rtf_player, firstRank~=nil)
    UIUtil.SetActive(self.rtf_none, firstRank==nil)

end

--下一波丧尸来袭时间
function UIView:StarNextWaveCountDownTime(timeStamp)
    if self.nextWaveTimer then
        self:RemoveTimer(self.nextWaveTimer)
        self.nextWaveTimer = nil
    end
    if timeStamp == 0 then
        return
    end
    self.nextWaveTimer = self.nextWaveTimer or self:CreateTimer(1, function ()
        if not self:IsValid() then
            return
        end
        local endTime = timeStamp- os.server_time()
        if endTime > 0 then
            self.txt_nextZombiesRefrashTime.text = string.format2(lang.Get(1004273),string.format("<color=#87F425>%s</color>",time_util.FormatTime5(endTime)))
        else
            self.txt_nextZombiesRefrashTime.text = string.format2(lang.Get(1004273),"<color=#87F425>00:00:00</color>")
            self:RemoveTimer(self.nextWaveTimer)
            local net_doomsday_module = require "net_doomsday_module"
            if os.server_time() - self.lastReqTime < 3 then
                self.isStopReq = true
            end
            if not self.isStopReq then
                net_doomsday_module.TMSG_SKY_FALL_THREAT_MONSTER_REQ()
                self.lastReqTime = os.server_time()
            end
        end
    end )
end
--刷新Toggle
function UIView:UpdateToggleUI(toggleIndex,listData)
    UIUtil.SetActive(self.txt_normalTips1, #listData == 0)
    
    -- 设置Toggle状态
    self:SetToggleState(toggleIndex == ToggleType.ALLIANCE)
    
    -- 设置提示文本
    self.txt_normalTips1.text = toggleIndex == ToggleType.ALLIANCE and 
        (alliance_mgr.GetIsJoinAlliance() and lang.Get(1004267) or lang.Get(1715)) or 
        lang.Get(1004267)
    
    -- 设置怪物内容
    self.srt_monsterContent:SetData(listData, #listData)
    self.srt_monsterContent:Refresh(0, -1)
end

function UIView:SetToggleState(isAlliance)
    local onColor = doomsday_define.ToggleTextColor.ON
    local offColor = doomsday_define.ToggleTextColor.OFF
    self.txt_allicanceTogText.color = isAlliance and onColor or offColor
    self.txt_areaTogText.color = isAlliance and offColor or onColor

end

function UIView:InitScrollRectTable()
    self.srt_monsterContent.onItemRender = OnItemRender
    self.srt_monsterContent.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data then
            if scroll_rect_item.data["itemUI"] then
                scroll_rect_item.data["itemUI"]:Dispose()
            end

            if scroll_rect_item.data["cdTimer"] then
                util.RemoveDelayCall(scroll_rect_item.data["cdTimer"])
                scroll_rect_item.data["cdTimer"] = nil
            end
        end
    end
end

function OnItemRender(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or { index, dataItem }
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    local mosterItem = scroll_rect_item:Get("mosterItem")
    local time = scroll_rect_item:Get("time")
    local goBtn = scroll_rect_item:Get("goBtn")

    --怪物图标
    local itemUI = scroll_rect_item.data["itemUI"] or monster_item.CMonsterItem():Init(mosterItem,nil,1.8)
    itemUI:SetMonsterInfo(dataItem.monsterId,function ()
        dataItem["goBtn"]()
    end)
    itemUI:ShowMonsterName(true)
    scroll_rect_item.data["itemUI"] = itemUI
    UIUtil.SetActive(goBtn, dataItem.endTime > os.server_time())

    --倒计时
    ItemCdTimer(scroll_rect_item,time,goBtn)

    scroll_rect_item.InvokeFunc = function(funcname)
        if dataItem[funcname] then
            dataItem[funcname]()
        end
    end
end

--滑动列表倒计时
function ItemCdTimer(scroll_rect_item,timeText,btn_x_bl)
    local cdTimer = scroll_rect_item.data["cdTimer"]
    local dataItem = scroll_rect_item.data[2]
    if cdTimer then
        util.RemoveDelayCall(cdTimer)
    end
    cdTimer = util.IntervalCall(1,
        function()
            local diff = dataItem.endTime - os.server_time()
            if diff > 0 then
                timeText.text = time_util.FormatTime5(diff)
            else
                timeText.text = "00:00:00"
                UIUtil.SetActive(btn_x_bl, false)
                util.RemoveDelayCall(cdTimer)
                scroll_rect_item.data["cdTimer"] = nil
            end
        end)
    scroll_rect_item.data["cdTimer"] = cdTimer
end

-- 刷新倒计时展示
function UIView:UpdateTime(timeStamp)
    if not util.IsObjNull(self.timeTextComponent) then
        self.timeTextComponent.text = string.format2(self.timeFormat,string.format("<color=#87F425>%s</color>",time_util.FormatTime5(timeStamp)))
    end
end



function UIView:OnHide()
    self.__base.OnHide(self)
end

function UIView:Close(data)   
    if self.VData then
        for i, v in pairs(self.VData) do
            if type(v) == "table" and v.Dispose then
                v:Dispose()
            end
        end
    end
    if self.nextWaveTimer then
        self:RemoveTimer(self.nextWaveTimer)
        self.nextWaveTimer = nil
    end

    if self.faceItem then
        self.faceItem:Dispose()
    end
    
    if self.moduleItem then
        self.moduleItem:Dispose()
    end
    
    self.VData = nil    
    self.__base.Close(self)
    window = nil
end
--endregion

--region View Logic

--endregion

--region View Static 
local CUIView = class(ui_base, nil, UIView)

function Show(data)
    if window == nil then
        window = CUIView()
        window._NAME = _NAME

        if data and type(data) == "table" then
            local tempPath = data.uiPath or ui_path
            local tempParent = data.uiParent or nil
			window:LoadUIResource(tempPath, nil, tempParent, nil, nil, false)
        else
			window:LoadUIResource(ui_path, nil, nil, nil,nil, false)
        end
    end
    window:Show()
    return window
end

function Close(data)
    if window ~= nil then
        window:Close(data)
        window = nil
    end
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end
--endregion
