local require = require
local tostring = tostring
local table = table

local Time = CS.UnityEngine.Time
local log = require "log"

-- 开始模块流程
function start_flow()
    -- 初始化重置模块
    require "init_lua_reset"
    require "init_clear"
    -- Lua库扩展extern
    require "extern"
    -- 全局函数C#调用
    require "cs_call_function"
    ---------------启动计时器-------------
    local timer_mgr = require "timer_mgr"
    timer_mgr:StartUp()

    ---------------创建初始化模块流-----------
    local util = require "util"
    local wmflow = require "wmflow"
    local config = require "init_enter_config"
    local flow = wmflow.create_flow(config.flow_cfgs, config.event_cfgs)
    flow.start(flow)
    local timer = util.IntervalCall(0, function()
        flow.time = Time.realtimeSinceStartup
        local result = flow:tickfunc()
        if result then
            log.Warning("result:", tostring(result))
            local init_progress_ui = require "init_progress_ui"
            init_progress_ui:endProgressTicker()
        end
        return result
    end)

end

local eventTable = {}
local upMsg = {}
local start_time = 0
-- 上报事件
function enterTrackEvent(eventName, node_name, node_tostr)
    local util = require "util"
    local msgStr = eventName .. tostring(node_tostr)
    if util.IsInTable(eventTable, msgStr) then
        return
    end
    table.insert(eventTable, msgStr)

    -- log.Warning("--enterTrackEvent:" .. eventName .. " node_name:" .. node_name .. " node_tostr:" .. node_tostr)

    local end_time = 0
    local try_time = 0
    if eventName == "tickfunc_start" then
        start_time = Time.realtimeSinceStartup
    elseif eventName == "tickfunc_true" then
        end_time = Time.realtimeSinceStartup - start_time
        try_time = 0
    elseif eventName == "tickfunc_false" then
        try_time = Time.realtimeSinceStartup - start_time
    else
    end

    upMsg = {
        n_name = node_name,
        e_name = eventName,
        node = node_tostr,
        end_time = end_time,
        try_time = try_time
    }
    -- local dump = dump
    -- dump(upMsg)
    util.AssetBundleManagerTrackEvent("m_flow", upMsg)
end
-- 开始模块流程
start_flow()
