local print = print
local type = type
local string = string
local tostring = tostring
local pairs = pairs
local require = require
local table = table
local tonumber = tonumber
local typeof = typeof

local _G=_G
local AssetBundleManager = CS.War.Base.AssetBundleManager
local hashRemote = CS.War.Base.AssetBundleManager.hashRemote

local Utility = CS.War.Script.Utility

local Debug = CS.UnityEngine.Debug
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local AssetsUpdator = CS.AssetsUpdator
local File = CS.System.IO.File
local Path = CS.System.IO.Path
local BgDownloadFileMgr = CS.War.Base.BgDownloadFileMgr
local FSMgr = CS.War.Base.FSMgr
local Application = CS.UnityEngine.Application
local DirectoryInfo = CS.System.IO.DirectoryInfo
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local CreateInstance = CS.System.Array.CreateInstance
local String = CS.System.String
local util = require "util"
local log = require "log"
local event = require "event"
local assetloader 	  = require "asset_loader"
local files_version_mgr = require "files_version_mgr"
local json = require "dkjson"

local init_scr = require "init_scr"
local InitLuaScr = init_scr.InitLuaScr
local OldResVersionManager = CS.War.Base.OldResVersionManager

local	time_util = require "time_util"
module("preload_resources_download")

local isWholeList
downloadType = nil
local tryGetExtParamsTime = 0
local assetLoaderList = {}

local downloadABNameList = nil

local LOAD_PATCH_TIMEOUT = 60
local DOWNLOAD_COUNT_MAX = 3 --下载重试最大次数
local downloadCountMax = DOWNLOAD_COUNT_MAX --下载重试最大次数
temp_difflist = nil --当前更新列表
local background_load = false --后台下载,异步解压, 以避免卡主线程
local onZipFinsh --当zip下载完成回调
function SetOnZipFinsh(cb) onZipFinsh = cb end
local function OnZipFinsh()
    preload_resources_main.StopUpdateDownloadPatchTimer()
    if onZipFinsh then
        onZipFinsh()
        onZipFinsh = nil
    else
        OnPatchPackageFinish()
    end
end

DownloadPatchType =
{
    package = 0, -- 整包下载
    divided_files = 1, -- files.txt 差异文件单个文件下载
    none = 3, -- 无需下载
    divided_firstres = 4, -- 首包资源差异文件单个文件下载
}

local preload_resources_main = require "preload_resources_main"

function GetDownloadPatchType(noDiffList)
    local save_time_stamp = util.GetUTC0TimeStampPlayerPrefs()
    local now_time_stamp =  time_util.GetUTC0TimeStamp()
    local loadedWhole = PlayerPrefs.GetInt(LOAD_WHOLE_MARK, 0)
    log.Warning("download_patch_type","save_time_stamp",save_time_stamp,"now_time_stamp",now_time_stamp,"loadedWhole",loadedWhole)

    local isWholePackage = files_version_mgr.IsWholePackage()
    local game_config = require "game_config"
    -- update.json 配置 zip包下载 开或关
    --local enable_patch_full_load = files_version_mgr.IsDownloadPackage()
    local enable_patch_full_load = (require "files_extend_helper").GetZipEnable()
    log.Warning("preload_resources:", ",whole package:", isWholePackage, "enable_patch_full_load:",enable_patch_full_load)

    if (now_time_stamp - save_time_stamp > 3600*24*3)  or (game_config.ENABLE_FORCE_PATCH_DOWNLOAD) then
        isWholeList = true

        local difflist = AssetBundleManager.GetDiffList(true)
        if difflist then
            log.Warning("difflist before:", difflist.Length)
        end
        if not difflist or difflist.Length == 0 then
            -- 不需要下载
            return DownloadPatchType.none,difflist
        else
            --登录下载需要剔除合集小游戏需要下载的小游戏资源
            if difflist.Length > 0 then
                print("zzd____#difflist.Length=",difflist.Length)
                difflist = ExcludeCollectionDownloadRes(difflist)
                print("zzd____after____#difflist.Length=",difflist.Length)
            end

            -- 使用整包下载且随机分类为使用整包下载。或强制使用整包下载时，启用整包下载逻辑
            -- 2021/9/17 增加 Utility.SetSharpZipDefaultCodePage 修改 SharpZip 默认编码接口，若不支持修改编码，则可能在解码时失败，所以不开启整包下载
            -- 2023/11/22 update.json开关PackageDownload强制使用整包下载

            --去掉灰度测试
            if Utility.SetSharpZipDefaultCodePage and enable_patch_full_load then
                -- 此类玩家走版本完整包更新流程
                return DownloadPatchType.package,difflist
            elseif not isWholePackage and util.IsCSharpClass(OldResVersionManager) and OldResVersionManager.IS_OLD_RES_VERSION then
                log.Warning("UseOldResVersion preload_resources use divided_firstres by not WholePackage")
                --中小包登陆前下载特殊处理：由于中小包内只有首包资源，其余资源在服务上；
                --在启用了老版本模式时；为了避免触发登录前下载，在登陆前只检查首包资源列表（不包含是否3天内）
                --此类玩家走首包资源流程
                return DownloadPatchType.divided_firstres,nil
            else
                -- 此类玩家走单文件更新流程
                return DownloadPatchType.divided_files,difflist
            end
        end
    else
        -- 对比首包资源列表，下载差异文件
        return DownloadPatchType.divided_firstres,nil
    end
end

function ExcludeCollectionDownloadRes(resDiffList)
    return resDiffList
    -- print("zzd____ downloadlist remove download games in collection games")
    -- --登录下载需要剔除合集小游戏需要下载的小游戏资源
    -- local files_version_mgr = require"files_version_mgr"
    -- if files_version_mgr.GetCollectionResKey() ~= nil then
    --     if hashRemote and hashRemote.key2mark and hashRemote.mark2list then
    --         local tiny_collection_mgr = require "tiny_collection_mgr"
    --         if #tiny_collection_mgr.GetResKeyInDownload() > 0 then
    --             local difflistTable = {}
    --             for listIndex = 0, resDiffList.Length -1 do
    --                 table.insert( difflistTable, listIndex +1, resDiffList[listIndex])
    --             end
    --             local mark2list = hashRemote.mark2list
    --             for resKeyIndex,resKeyInDownload in pairs(tiny_collection_mgr.GetResKeyInDownload()) do
    --                 print("zzd____resKeyInDownload  resKeyIndex",resKeyIndex," reskeyValue",resKeyInDownload)
    --                 local notNil,paths = mark2list:TryGetValue(resKeyInDownload)
    --                 if notNil then
    --                     for k = 0, paths.Length-1 do
    --                         for index,value in pairs(difflistTable) do
    --                             if paths[k] == value then
    --                                 --print(" zzd____paths[",k,"] = ",paths[k])
    --                                 --print(" zzd____difflistTable[",index,"] = ",value)
    --                                 table.remove( difflistTable, index )
    --                                 break
    --                             end
    --                         end
    --                     end
    --                 end
    --             end
    --             print("zzd____#difflistTable=",#difflistTable)
    --             local array=CreateInstance(typeof(String),#difflistTable)
    --             for k = 0, array.Length - 1 do
    --                 array[k] = difflistTable[k+1]
    --                 -- print("zzd____array[k]____",array[k])
    --             end
    --             print("zzd____array length____",array.Length)
    --             print("zzd____copy length_____",resDiffList.Length)
    --             return array
    --         else
    --             print("zzd____ExcludeCollectionDownloadRes____not tiny_collection_mgr.GetResKeyInDownload()____return resDiffList")
    --             return resDiffList
    --         end
    --     else
    --         print("zzd____ExcludeCollectionDownloadRes____not hashRemote____return resDiffList")
    --         return resDiffList
    --     end
    -- else
    --     print("zzd____ExcludeCollectionDownloadRes____no GetCollectionResKey____return resDiffList")
    --     return resDiffList
    -- end
end

--设置运营配置
function SetOperationConfig(str)
    local OperationConfigGroup = {}
    local OperationConfigCount = 0
    local jsonValue = json.decode(str)
    for c, d in pairs(jsonValue) do
        --local uKey = "$" .. tostring(c) -- 加上$前缀
        OperationConfigGroup[c] = d
        OperationConfigCount = OperationConfigCount + 1
    end

    if OperationConfigGroup and OperationConfigCount > 0 then -- 添加domainNew配置，减少后台配置量
        _G['_OperationConfig'] = OperationConfigGroup

    else
        log.Warning("未设置OperationConfig")
    end
end

---@param str string @ domainNew信息，简化后台配置
function SetDomainNew(str)
    local domainNewGroup = {}
    local domainNewCount = 0
    local jsonValue = json.decode(str)
    for c,d in pairs(jsonValue)do
        local uKey = "$" .. tostring(c) -- 加上$前缀
        domainNewGroup[uKey] = d
        domainNewCount = domainNewCount + 1
    end

    if domainNewGroup and domainNewCount > 0 then -- 添加domainNew配置，减少后台配置量
        _G['_DOMAIN_NEW'] = domainNewGroup
        log.Warning("domainNew mainDomain:", _G['_DOMAIN_NEW']["$mainDomain"], "apiDomain:", _G['_DOMAIN_NEW']["$apiDomain"], "gameID:", _G['_DOMAIN_NEW']["$gameID"])
        if not domainNewGroup["$mainDomain"] or domainNewGroup["$mainDomain"] == "" then
            log.Error("domainNew mainDomain未配置")
        end

        if not domainNewGroup["$apiDomain"] or domainNewGroup["$apiDomain"] == "" then
            log.Error("domainNew $apiDomain未配置")
        end

        if not domainNewGroup["$gameID"] or domainNewGroup["$gameID"] == "" then
            log.Error("domainNew gameID未配置")
        end
    else
        log.Warning("未设置domainNew")
    end
end

---@param str string @ 渠道标识信息
function SetChannelMark(str)
    local channelMark
    local jsonValue = json.decode(str)
    for _,d in pairs(jsonValue)do
        channelMark = d
    end

    if channelMark then
        _G['_GAME_CHANNEL_MARK'] = channelMark
        log.Warning("driver channelMark不为空 :"..tostring(channelMark))
    else
        log.Warning("driver channelMark为空")
    end
end

function DownloadUrl(callback)
    local q1sdk = require "q1sdk"
    log.Warning(
            "DownloadUrl"
    ,q1sdk.getExtParams()
    ,"AssetsUpdator.ExtParams"
    ,AssetsUpdator.ExtParams
    )

    local json = require "dkjson"
    local cb = function (str)
        local url_mgr = require "url_mgr"
        if url_mgr.GetPrintEnable() then
            log.Warning("Domain_data",str)
        end
        local jsonData = json.decode(str) or {}
        local resultStr = jsonData.result
        if not resultStr then
            --InvokeCallBack(callback)
            return
        end
        local urlGroup = {}
        local updateGroup = {}
        local resultData = json.decode(resultStr) or {}
        if resultData then
            local extConfig = resultData.extConfig
            local urlCount = 0
            local updateCount = 0
            if extConfig then
                for k,v in pairs(extConfig)do
                    -- 多组key value
                    -- 有两套数据 domain 和 update json
                    local key = ""
                    for m,n in pairs(v)do
                        if m == "Key" then
                            key = n
                        end
                        if key == "domain" and m == 'Value' then
                            local jsonValue = json.decode(n)
                            for c,d in pairs(jsonValue)do
                                local uKey = tostring(c)
                                urlGroup[uKey] = d
                                urlCount = urlCount + 1
                            end
                        elseif key == "update" and m == 'Value' then
                            local jsonValue = json.decode(n)
                            for c,d in pairs(jsonValue)do
                                local uKey = tostring(c)
                                updateGroup[uKey] = d
                                updateCount = updateCount + 1
                            end
                        elseif key == "domainNew" and m == 'Value' then
                            SetDomainNew(n)
                        elseif key == "channelMark" and m == 'Value' then
                            SetChannelMark(n)
                        elseif key == "operationConfig" and m == 'Value' then
                            SetOperationConfig(n)
                        end
                    end
                end
            end
            if urlGroup and urlCount > 0 then
                _G['_DOMAIN_DATA'] = urlGroup
                local url_mgr = require "url_mgr"
                url_mgr.SetUrlTable(urlGroup)
            end

            if updateCount and updateCount > 0 then
                if not _G['_EXT_PARAMS'] then
                    _G['_EXT_PARAMS'] = {}
                end
                _G['_EXT_PARAMS']['update'] = updateGroup
            end

            local chatAddress = resultData.chatAddress
            if chatAddress then
                local chatData = {}
                for k,v in pairs(chatAddress)do
                    local keyi= tostring(k)
                    if keyi == "VipIsOpen" then
                        print("VipIsOpen",v)
                        _G["_CHAT_EXTPARAMS_DATA_VIPISOPEN"] = v
                    end
                    chatData[keyi]=v
                end
                _G["_CHAT_EXTPARAMS_DATA"] = chatData
            end
            url_mgr.UpdateUrl()
        end
    end


    local log= require "log"
    local isHadSDKFunc = q1sdk.getExtParams()
    if isHadSDKFunc then
        local urlGroup = _G['_DOMAIN_DATA']
        if not urlGroup then
            if AssetsUpdator.ExtParams then
                local jsonContain,jsonStr = AssetsUpdator.ExtParams:TryGetValue("jsonStr")
                local json = require "dkjson"
                if jsonContain then
                    local jsonData = json.decode(jsonStr) or {}
                    local extConfigStr = jsonData.extConfig
                    if extConfigStr then
                        local urlCount = 0
                        local urlGroup = {}
                        local updateCount = 0
                        local updateGroup = {}
                        local extConfig
                        if type(extConfigStr) == 'table' then
                            extConfig =  extConfigStr
                        else
                            extConfig =  json.decode(extConfigStr) or {}
                        end
                        for k,v in pairs(extConfig)do
                            -- 多组key value
                            -- 有两套数据 domain 和 update json
                            local key = ""
                            for m,n in pairs(v)do
                                if m == "Key" then
                                    key = n
                                end
                                if key == "domain" and m == 'Value' then
                                    local jsonValue = json.decode(n)
                                    for c,d in pairs(jsonValue)do
                                        local uKey = tostring(c)
                                        urlGroup[uKey] = d
                                        urlCount = urlCount + 1
                                    end
                                elseif key == "update" and m == 'Value' then
                                    local jsonValue = json.decode(n)
                                    for c,d in pairs(jsonValue)do
                                        local uKey = tostring(c)
                                        updateGroup[uKey] = d
                                        updateCount = updateCount + 1
                                    end
                                elseif key == "domainNew" and m == 'Value' then
                                    SetDomainNew(n)
                                elseif key == "channelMark" and m == 'Value' then
                                    SetChannelMark(n)
                                elseif key == "operationConfig" and m == 'Value' then
                                    SetOperationConfig(n)
                                end
                            end
                        end

                        if urlGroup and urlCount > 0 then
                            _G['_DOMAIN_DATA'] = urlGroup
                            local url_mgr = require "url_mgr"
                            url_mgr.SetUrlTable(urlGroup)
                        end

                        if updateCount and updateCount > 0 then
                            if not _G['_EXT_PARAMS'] then
                                _G['_EXT_PARAMS'] = {}
                            end
                            _G['_EXT_PARAMS']['update'] = updateGroup
                        end
                    end

                    local chatAddressStr = jsonData.chatAddress
                    if chatAddressStr then
                        local chatAddress
                        if type(chatAddressStr) == 'table' then
                            chatAddress =  chatAddressStr
                        else
                            chatAddress =  json.decode(chatAddressStr) or {}
                        end
                        if chatAddress then
                            local chatData = {}
                            for k,v in pairs(chatAddress)do
                                local key = tostring(k)
                                chatData[k]=v
                            end
                            _G["_CHAT_EXTPARAMS_DATA"] = chatData
                        end
                    end
                    InvokeCallBack(callback)
                else
                    q1sdk.getExtParams(function (str)
                        if str and str ~= "" then
                            cb(str)
                        end
                        InvokeCallBack(callback)
                    end)
                end

            else
                q1sdk.getExtParams(function (str)
                    if str and str ~= "" then
                        cb(str)
                    end
                    InvokeCallBack(callback)
                end)
            end
        else
            -- 已初始化url 完毕
            InvokeCallBack(callback)
        end
    else
    end
end

function MiniGameEndCb()
    local casualgame_global = require "casualgame_global"
    casualgame_global.DownLoadMiniGameSupportStandAlongGame(function ()
        preload_resources_main.miniGamePreload = true
    end)
end

function StartPreLoad(downloadPatchType)
    log.Warning("preload_resources,downloadPatchType->",downloadPatchType)
    if downloadPatchType == DownloadPatchType.none then
        preload_resources_main.Finish()
    elseif downloadPatchType == DownloadPatchType.package then
        -- 此类玩家走版本完整包更新流程
        DownloadPatchPackage()
    elseif downloadPatchType == DownloadPatchType.divided_files then
        -- 此类玩家走单文件更新流程
        DownloadDividedFiles(temp_difflist)
        temp_difflist = nil
    elseif downloadPatchType == DownloadPatchType.divided_firstres then
        -- local txtPath = "editorconfig/restag/firstres.txt"
        local txtPath = "editorconfig/restag/firstres.bytes"
        if not assetLoaderList[txtPath] then
            assetLoaderList[txtPath] = assetloader(txtPath,"preload_resources")
        end
        assetLoaderList[txtPath]:load(function (obj)
            util.PeekWatch("preload_resources", "登录前预加载资源列表加载完成")
            util.AssetBundleManagerTrackEvent("preload_resources", {
                log_begin = 0
            })
            local txt = obj.asset
            if(obj ~= nil and txt ~= nil and txt.text ~= nil) then
                local tmp = string.split(txt.text,"\n")
                local txtLst={}
                local pos = 0
                local fileName
                for i = 1,#tmp do
                    fileName = tmp[i]
                    if fileName ~= nil then
                        table.insert(txtLst, pos, fileName)
                        pos = pos + 1
                    end
                end
                log.Warning("Number of resources preloaded before login:",pos)

                --添加休闲游戏相关资源
                local length = 0
                if hashRemote and hashRemote.key2mark and hashRemote.mark2list then
                    local res_key = files_version_mgr.GetResKey()
                    if res_key then
                        log.Warning("preload_resources,casual games preloaded key:",res_key)
                        local notNil,folderKeys = hashRemote.key2mark:TryGetValue(res_key)
                        if notNil then
                            local paths
                            local mark2list = hashRemote.mark2list
                            for i = 0, folderKeys.Length-1 do
                                local floderKey = folderKeys[i]
                                notNil,paths = mark2list:TryGetValue(floderKey)
                                if notNil then
                                    for k = 0, paths.Length-1 do
                                        if(preload_resources_main.isEnableDebugInfo) then
                                            log.Warning("preload_resources,casual games ab name->",paths[k])
                                        end
                                        table.insert(txtLst, length+pos,paths[k])
                                        length = length + 1
                                    end
                                end
                            end
                        end
                    end
                end
                log.Warning("preload_resources,casual games preloaded assest count:",length)

                --添加海外资源差异相关资源
                if hashRemote and hashRemote.langmark2list then
                    local ch_res_key = files_version_mgr.GetChannelResKey()
                    local need_res_keys = string.split(ch_res_key, "_")
                    local ui_setting_data = require "ui_setting_data"
                    local ui_setting_cfg = require "ui_setting_cfg"
                    local use_lang = tonumber(ui_setting_data.LoadConfig())
                    local curLangRes = ""
                    if  ui_setting_cfg.LangMap[use_lang] then
                        curLangRes = ui_setting_cfg.LangMap[use_lang].Lang
                        log.Warning("preload_resources,casual channel curLangRes:", curLangRes)
                        local notNil, paths = hashRemote.langmark2list:TryGetValue(curLangRes)
                        if notNil then
                            for k = 0, paths.Length - 1 do
                                if(preload_resources_main.isEnableDebugInfo) then
                                    log.Warning("preload_resources,casual channel ab name->",paths[k])
                                end
                                table.insert(txtLst, paths[k])
                            end
                        end
                    end
                    for i = 1, #need_res_keys do
                        local curResKey = need_res_keys[i]
                        if curResKey and curResKey ~= "" and curResKey ~= curLangRes then
                            log.Warning("preload_resources,casual channel preloaded key:", curResKey)
                            local notNil, paths = hashRemote.langmark2list:TryGetValue(curResKey)
                            if notNil then
                                for k = 0, paths.Length - 1 do
                                    if(preload_resources_main.isEnableDebugInfo) then
                                        log.Warning("preload_resources,casual channel ab name->",paths[k])
                                    end
                                    table.insert(txtLst, paths[k])
                                end
                            end
                        end
                    end
                end

                local download_mgr = require "download_mgr"
                Debug.LogWarning("登陆预加载开始.............")
                util.PeekWatch("preload_resources", "生成登录前预加载资源检测列表")
                log.Warning("preload_resources,difflist count->",#txtLst)
                downloadABNameList = txtLst
                download_mgr.InitDownloadMgr(txtLst,nil,OnDownloadProgressChange,OnFail)
            else
                -- 仅下载完整包，为防止三天后再次下载重复数据，仍保存整包更新版本数据，不做重复下载
                -- 即下载整包更新可能存在从首包资源更新到整包资源的情况，由于 AseetBundle 会自动比较版本从网上下载，不会出现资源不全的情况
                preload_resources_main.UpdateDividedPatchRecord()
                preload_resources_main.Finish()
            end
        end)
    end
end

local invokeDic ={}
function InvokeCallBack(cb)
    if cb and not invokeDic[cb]then
        cb()
        invokeDic[cb] = true
    end
end

-- 整包资源下载，仅在新包上生效，之前的外网包不受影响
function DownloadPatchPackage(background)
    background_load = background
    log.Warning('DownloadPatchPackage', background_load)
    if not Utility.DecompressFile then
        -- 2021/3/4 添加 Utility.DecompressFile 接口,接口不支持
        OnZipFinsh()
        return
    end

    local local_resource_version = -1
    --[[ 获取本地资源版本号
    local patchSaveRootPath = preload_resources_main.GetPatchSaveRootPath()
    local patchRecordFilePath = Path.Combine(patchSaveRootPath, "resource_record.bytes")
    if File.Exists(patchRecordFilePath) then
        preload_resources_main.recordJson = util.ReadJson(patchRecordFilePath)
    else
        -- 获得 APK 内置资源版本号, Application.version = "x.x.x",不是资源版本号
        local apkVersion = files_version_mgr.GetApkResourceVersion()
        preload_resources_main.recordJson =
        {
            cur_version = apkVersion,
            downloading_version = -1,
            subpatch_set = {}
        }
    end
]]
    preload_resources_main.recordJson = preload_resources_main.GetPatchRecordJson()
    if preload_resources_main.recordJson.cur_version then
        local_resource_version = preload_resources_main.recordJson.cur_version
    end

    if (not local_resource_version) or local_resource_version == -1 then
        -- 如果不能获取本地最新资源版本，仍采用独立文件下载方式
        log.Warning("nothing local_resource_version=", local_resource_version)
        OnZipFinsh()
        return
    end

    -- 获取外网最新资源版号，如果配置错误仍采用独立文件下载方式
    if not preload_resources_main.curRemoteResourceVersion or preload_resources_main.curRemoteResourceVersion < 0 then
        log.Error("can not find remote resource version")
        OnZipFinsh()
        return
    end

    if local_resource_version >= preload_resources_main.curRemoteResourceVersion then
        -- 不需要更新
        log.Warning("same ver",local_resource_version, curRemoteResourceVersion)
        OnZipFinsh()
        return
    end
--[[
    local remoteValidList = files_version_mgr.GetRemoteValidPatchList()
    local remoteValidCount = remoteValidList and  remoteValidList.Count or nil
    log.Warning("local version:", local_resource_version, ",remote count:", remoteValidCount, ",remote version:", preload_resources_main.curRemoteResourceVersion)
    if not remoteValidCount or local_resource_version + remoteValidCount < preload_resources_main.curRemoteResourceVersion then
        -- 服务器上没有从当前客户端版本到最新版本的资源包，可能为客户端长期未更新，版本太老，采用独立文件下载方式更新
        JumpToDownloadDividedFiles(difflist)
        return
    end
    log.Warning("down ver:", preload_resources_main.recordJson.downloading_version, ",cur ver:", preload_resources_main.recordJson.cur_version, ",remote ver:", preload_resources_main.curRemoteResourceVersion)
]]
    --local patchInfo = files_version_mgr.GetRemoteValidPatchList()
    local patchInfo = (require "files_extend_helper").GetZipInfo()
    if not patchInfo or not (require "files_extend_helper").GetZipEnable(patchInfo) then
        log.Warning("patchInfo nil")
        OnZipFinsh()
        return
    end
    --local ok,patch_version = patchInfo:TryGetValue("patch_version")
    --local ok,patch_zip_dict = patchInfo:TryGetValue("patch_zip_dict")
    local patch_version = patchInfo["patch_version"]
    local patch_zip_dict = patchInfo["patch_zip_dict"]
    if not patch_version or not patch_zip_dict then
        --开启下载zip包,但没有 zip包版本 下载
        log.Warning("no patch_version=", patch_version, "patch_zip_dict=", patch_zip_dict)
        OnZipFinsh()
        return
    end
    patch_version = tonumber(patch_version)
    local finded, totalSize
    local patch_near_version
    if local_resource_version < patch_version then
        -- 找到客户端对应的最近zip版本下载
        for i = local_resource_version,patch_version do
            --finded, totalSize = patch_zip_dict:TryGetValue(tostring(i))
            totalSize = patch_zip_dict[tostring(i)]
            finded = totalSize ~= nil
            --print(i, finded, totalSize)
            if finded and totalSize then
                patch_near_version = i
                break
            end
        end
    end
    if not finded or not totalSize then
        -- 本地已有最新的, 没有需要的zip包版本下载
        log.Warning("nothing load, local_resource_version=", local_resource_version, "patch_version=",patch_version)
        OnZipFinsh()
        return
    end
    -- 开始下载客户端对应的 zip包版本
    log.Warning("patch_version=", patch_version,"patch_near_version=", patch_near_version, "totalSize=", totalSize)

    -- downloading_version ~= preload_resources.curRemoteResourceVersion : 外网再次发生资源更新, 删除旧版本资源
    -- downloading_version == preload_resources.curRemoteResourceVersion and recordJson.cur_version ~= preload_resources.curRemoteResourceVersion: 最新版本下载中
    if preload_resources_main.recordJson.downloading_version ~= preload_resources_main.curRemoteResourceVersion then
        preload_resources_main.recordJson.downloading_version = preload_resources_main.curRemoteResourceVersion
        local subPatchInfo
        local patchSaveRootPath = preload_resources_main.GetPatchSaveRootPath()
        for key,subpatchInfo in pairs(preload_resources_main.recordJson.subpatch_set) do
            local subPatchFilePath = Path.Combine(patchSaveRootPath, subpatchInfo.subPatchName)
            File.Delete(subPatchFilePath)

            local zipFileName = Path.GetFileNameWithoutExtension(subPatchFilePath)
            local subPathcUnzipPath = Path.Combine(patchSaveRootPath, zipFileName)
            if File.Exists(subPathcUnzipPath) then
                local driectoryInfo = DirectoryInfo(subPathcUnzipPath)
                driectoryInfo:Delete(true)
            end
        end
        preload_resources_main.recordJson.subpatch_set = {}
    end

    -- remoteValidList 下标从 0 开始，数据取自 C# 层
    -- local patchIdx = preload_resources_main.curRemoteResourceVersion - local_resource_version - 1
    -- preload_resources_main.patchZipInfo.totalSize = remoteValidList[patchIdx]
    preload_resources_main.patchZipInfo.totalSize = totalSize
    preload_resources_main.patchZipInfo.subPatchList = {}
    preload_resources_main.patchZipInfo.patchCount = 0 -- 妥协结果，只有第一个包下载完成后，才能知道多少子包需要下载
    -- preload_resources_main.patchZipInfo.patchName = local_resource_version .. "-" .. preload_resources_main.curRemoteResourceVersion
    preload_resources_main.patchZipInfo.patchName = patch_near_version .. "-" .. patch_version
    preload_resources_main.patchZipInfo.downloadedSize = 0

    local curResourceDownloadUrl = files_version_mgr.GetResourceDownloadUrl()

    -- 判断哪些包下载完成，初始化 patchZipInfo.subPatchList
    local patch_x_url,remoteResourceVersionUrl
    -- local remoteResourceVersionUrl = curResourceDownloadUrl  .. preload_resources_main.curRemoteResourceVersion .. "/"
    local remoteResourceVersionUrl = curResourceDownloadUrl  .. patch_version .. "/"
    for key,recordPatchInfo in pairs(preload_resources_main.recordJson.subpatch_set) do
        patch_x_url = remoteResourceVersionUrl .. recordPatchInfo.subPatchName
        if recordPatchInfo.isComplete then
            preload_resources_main.patchZipInfo.downloadedSize = preload_resources_main.patchZipInfo.downloadedSize + recordPatchInfo.downloadedSize
        end
        preload_resources_main.AddSubPatch(recordPatchInfo.subIdx, patch_x_url, nil, recordPatchInfo.isComplete, recordPatchInfo.subPatchName, true)
    end

    -- preload_resources_main.patchZipInfo.patchRootUrl = curResourceDownloadUrl  .. preload_resources_main.curRemoteResourceVersion .. "/" .. preload_resources_main.patchZipInfo.patchName
    -- local patchRootUrl = preload_resources_main.patchZipInfo.patchRootUrl
    local patchRootUrl = remoteResourceVersionUrl .. preload_resources_main.patchZipInfo.patchName
    preload_resources_main.patchZipInfo.patchRootUrl = patchRootUrl

    local subPatchCount = util.TableCount(preload_resources_main.recordJson.subpatch_set)
    if subPatchCount == 0 then
        -- 本地没有子包体数据，添加第一个包下载
        local patch_1_url = patchRootUrl .. "-1.zip"
        subPatchName = preload_resources_main.patchZipInfo.patchName .. "-1.zip"
        preload_resources_main.AddSubPatch(1, patch_1_url, nil, false, subPatchName, true)
    end

    local patchUrl,subPatchInfo = preload_resources_main.GetDownloadPatchUrl()
    print("download url:", patchUrl)
    preload_resources_main.recordJson.downloading_version = preload_resources_main.curRemoteResourceVersion
    local json_str = json.encode({
        patchUrl = patchUrl,--当前第一个zip包下载路径
        difflistLength = temp_difflist and temp_difflist.Length or -1,--AssetBundleManager.GetDiffList()，zip下载不需要用到
        downloading_version = preload_resources_main.recordJson.downloading_version,--下载资源版本
        cur_version = preload_resources_main.recordJson.cur_version,--当前资源版本
        subPatchCount = subPatchCount,--下载zip包个数
        patchRootUrl = patchRootUrl
    })
    event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_start", json_str)
    util.AssetBundleManagerTrackEvent("preload_resources", {
        log_begin = 3
    })
    preload_resources_main.SavePatchRecord()
    DownloadPatchUrl(patchUrl, subPatchInfo)
end

-- AssetBundleManager.GetDiffList 接口相对耗时，单独传参
function DownloadDividedFiles(difflist)
    log.Warning('DownloadDividedFiles', downloadType)
    util.AssetBundleManagerTrackEvent("preload_resources", {
        log_begin = 2
    })

    if difflist and difflist.Length > 0 then
        downloadType = DownloadPatchType.divided_files
        --Debug.LogWarning("whole登陆预加载开始............. "..difflist.Length)
        log.Warning("preload_resources,difflist count->",difflist.Length)
        local download_mgr = require "download_mgr"
        downloadABNameList = difflist
        download_mgr.InitDownloadMgr(difflist,nil,OnDownloadProgressChange,OnFail)
    else
        preload_resources_main.Finish()
    end
end

function JumpToDownloadDividedFiles(difflist)
    DownloadDividedFiles(difflist)
    InitLuaScr(function (  )
        preload_resources_main.markLuaLoad = true
    end)
end

function OnPatchPackageFinish()
    JumpToDownloadDividedFiles(temp_difflist)
    temp_difflist = nil
end

function ParseSubPatchConfig(configRootPath, subset)
    local zip_patch_path = Path.Combine(configRootPath, "zip_patch.txt")
    local zip_patch_json = util.ReadJson(zip_patch_path)
    if zip_patch_json == nil then
        return false
    end

    local patchRootUrl = preload_resources_main.patchZipInfo.patchRootUrl
    local subPatchCount = zip_patch_json.zip_count

    local patch_x_url,subPatchName,patch_suffix
    for i=2,subPatchCount do
        patch_suffix = '-' .. i .. '.zip'
        patch_x_url = patchRootUrl .. patch_suffix
        subPatchName = preload_resources_main.patchZipInfo.patchName .. patch_suffix
        local curSubPatchInfo = preload_resources_main.AddSubPatch(i, patch_x_url, nil, false, subPatchName,true)
        local recordPatchInfo = preload_resources_main.GetRecordSubpatchInfo(curSubPatchInfo, subset)
        recordPatchInfo.subIdx = i
        recordPatchInfo.subPatchName = subPatchName
        recordPatchInfo.isComplete = false
        recordPatchInfo.downloadedSize = 0
    end
end
local function OnDownloadErr(url,curSubPatchInfo, errMsg)
    log.Warning("errMsg:limitRetryTime=", downloadCountMax, errMsg)
    downloadCountMax = downloadCountMax - 1
    local json_str = json.encode({
        url = url,--下载路径
        size = tonumber(curSubPatchInfo.totalSize),--当前zip包的大小
        index = idx,--当前zip在本地下载中的index
        subPatchName = curSubPatchInfo.subPatchName,--当前zip包名称
        limitRetryTime = downloadCountMax,
        msg = "下载失败，重新下载",
        error = errMsg
    })
    event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_error", json_str)
    if downloadCountMax <= 0 then return OnZipFinsh() end
    -- 下载失败，重新下载
    DownloadPatchUrl(url, curSubPatchInfo)
end
local function OnDecompressErr(url, subPatchFolderPath, curSubPatchInfo)
    -- 解压失败，文件可能不完整/损坏,重新下载
    log.Warning(url, "文件解压损坏limitRetryTime=", downloadCountMax)
    downloadCountMax = downloadCountMax - 1
    local json_str = json.encode({
        url = url,--下载路径
        subPatchFolderPath = subPatchFolderPath,--下载zip之后的保存路径
        subPatchSaveFilePath = subPatchSaveFilePath,--zip解压路径
        size = tonumber(curSubPatchInfo.totalSize),--当前zip包的大小--userdata 类型转换
        limitRetryTime = downloadCountMax,
        index = idx,--当前zip在本地下载中的index
        subPatchName = curSubPatchInfo.subPatchName,--当前zip包名称
        msg = "解压失败，文件可能不完整/损坏,重新下载"
    })
    event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_error", json_str)
    if downloadCountMax <= 0 then return OnZipFinsh() end
    DownloadPatchUrl(url, curSubPatchInfo)
end
function DownloadPatchUrl( url, subPatchInfo )
    preload_resources_main.downloadingSubPatchInfo.requestAsync = nil
    preload_resources_main.downloadingSubPatchInfo.subPatchInfo = nil

    if url == nil then
        -- 全部下载完成
        preload_resources_main.recordJson.cur_version = preload_resources_main.curRemoteResourceVersion
        preload_resources_main.SavePatchRecord()
        log.Warning("DownloadPatchUrl 下载完成!!!!")
        -- 用于验证热更文件数据是否修改正确
        -- local difflist = AssetBundleManager.GetDiffList()
        -- log.Warning("difflist after count:", difflist.Length)
        event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_end", {})
        OnZipFinsh()
        return
    end
    local recordPatchInfo = preload_resources_main.GetRecordSubpatchInfo(subPatchInfo)
    if recordPatchInfo.isComplete ~= false then
        recordPatchInfo.isComplete = false
        preload_resources_main.SavePatchRecord()
    end

    if preload_resources_main.updateDownloadPatchTimer == nil then
        preload_resources_main.updateDownloadPatchTimer = util.IntervalCall(0, preload_resources_main.OnUpdateDownloadPatch)
    end

    local http_inst = require "http_inst"
    --[[ When a timeout occurs error returns "Request timeout" . No timeout is applied when timeout is set to 0 and this property defaults to 0.
    local requestAsync = http_inst.Request_Timeout(url, LOAD_PATCH_TIMEOUT, function(text, errMsg, bytesData, downloadedBytes, request)
        preload_resources_main.downloadingSubPatchInfo.requestAsync = nil
        preload_resources_main.downloadingSubPatchInfo.subPatchInfo = nil

        local curSubPatchInfo,idx = preload_resources_main.GetSubPatchByUrl(url)
        if string.empty(errMsg) then
            -- 下载成功
            if curSubPatchInfo then
                curSubPatchInfo.isDone = true
                curSubPatchInfo.totalSize = downloadedBytes
                curSubPatchInfo.downloadedSize = curSubPatchInfo.totalSize
            end
            local saveRootPath = preload_resources_main.GetPatchSaveRootPath()
            local subPatchSaveFilePath = saveRootPath .. curSubPatchInfo.subPatchName
            File.WriteAllBytes(subPatchSaveFilePath, bytesData)

            local zipFileName = Path.GetFileNameWithoutExtension(subPatchSaveFilePath)
            local subPatchFolderPath = Path.Combine(saveRootPath, zipFileName)
            -- 解压压缩包
            local zip = require "zip"
            local decompressSuccess = zip.DecompressFile(subPatchSaveFilePath, subPatchFolderPath)
            if not decompressSuccess then
                -- 解压失败，文件可能不完整/损坏,重新下载
                log.Warning(url, "文件解压损坏limitRetryTime=", downloadCountMax)
                downloadCountMax = downloadCountMax - 1
                local json_str = json.encode({
                    url = url,--下载路径
                    subPatchFolderPath = subPatchFolderPath,--下载zip之后的保存路径
                    subPatchSaveFilePath = subPatchSaveFilePath,--zip解压路径
                    size = tonumber(curSubPatchInfo.totalSize),--当前zip包的大小--userdata 类型转换
                    index = idx,--当前zip在本地下载中的index
                    subPatchName = curSubPatchInfo.subPatchName,--当前zip包名称
                    limitRetryTime = downloadCountMax,
                    msg = "解压失败，文件可能不完整/损坏,重新下载"
                })
                event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_error", json_str)
                if downloadCountMax <= 0 then return OnPatchPackageFinish() end
                DownloadPatchUrl(url, curSubPatchInfo)
                return
            end

            -- 更新已下载完成总大小
            preload_resources_main.patchZipInfo.downloadedSize = preload_resources_main.patchZipInfo.downloadedSize + curSubPatchInfo.totalSize
            -- 下载完成一个包体后，主动更新一次显示进度，避免显示延时
            preload_resources_main.OnUpdateDownloadPatch()
            local recordPatchInfo = preload_resources_main.GetRecordSubpatchInfo(curSubPatchInfo)
            recordPatchInfo.subIdx = idx
            recordPatchInfo.subPatchName = curSubPatchInfo.subPatchName
            recordPatchInfo.isComplete = true
            recordPatchInfo.downloadedSize = util.Long2Int(curSubPatchInfo.totalSize)

            if idx == 1 then
                -- 第一个 zip 包，需要解压以获取后续子包体数据
                ParseSubPatchConfig(subPatchFolderPath, preload_resources_main.recordJson.subpatch_set)
            end

            util.StartWatch("update NewCaching files"..idx, "检验 NewCaching 文件")
            UpdateNewCachingFiles(subPatchFolderPath)
            util.StopWatch("update NewCaching files"..idx)

            -- 必须在文件完整覆盖 NewCaching 文件后，才能写入当前子补丁文件下载完成
            preload_resources_main.SavePatchRecord()

            local nextUrl,nextSubPatchInfo = preload_resources_main.GetDownloadPatchUrl()
            DownloadPatchUrl(nextUrl, nextSubPatchInfo)
        else
            log.Warning("errMsg:limitRetryTime=", downloadCountMax, errMsg)
            downloadCountMax = downloadCountMax - 1
            local json_str = json.encode({
                url = url,--下载路径
                size = tonumber(curSubPatchInfo.totalSize),--当前zip包的大小
                index = idx,--当前zip在本地下载中的index
                subPatchName = curSubPatchInfo.subPatchName,--当前zip包名称
                msg = "下载失败，重新下载",
                limitRetryTime = downloadCountMax,
                error = errMsg
            })
            event.Trigger(event.GAME_EVENT_REPORT, "DownloadPatchPackage_error", json_str)
            if downloadCountMax <= 0 then return OnPatchPackageFinish() end
            -- 下载失败，重新下载
            DownloadPatchUrl(url, curSubPatchInfo)
        end
    end)
]]
    local curSubPatchInfo, idx = preload_resources_main.GetSubPatchByUrl(url)
    local saveRootPath = preload_resources_main.GetPatchSaveRootPath()
    local subPatchSaveFilePath = saveRootPath .. curSubPatchInfo.subPatchName
    local requestAsync,localExist
    requestAsync,localExist = http_inst.Request_DownloadFile(url, LOAD_PATCH_TIMEOUT, function(subPatchSaveFilePath, errMsg, downloadedBytes)
        preload_resources_main.downloadingSubPatchInfo.requestAsync = nil
        preload_resources_main.downloadingSubPatchInfo.subPatchInfo = nil

        if string.empty(errMsg) then
            -- 下载成功
            if curSubPatchInfo then
                curSubPatchInfo.isDone = true
                curSubPatchInfo.totalSize = downloadedBytes
                curSubPatchInfo.downloadedSize = curSubPatchInfo.totalSize
            end

            local zipFileName = Path.GetFileNameWithoutExtension(subPatchSaveFilePath)
            local subPatchFolderPath = Path.Combine(saveRootPath, zipFileName)
            -- 解压压缩包
            local zip = require "zip"
            log.LoginWarning("zipFileName",zipFileName,subPatchFolderPath)
            --local decompressSuccess = zip.DecompressFile(subPatchSaveFilePath, subPatchFolderPath)
            local onloaded = function(decompressSuccess)
                if not decompressSuccess or decompressSuccess=="false" then
                    --本地有文件但解压失败,删除本地文件
                    if localExist then http_inst.deleteFileIfExists(subPatchSaveFilePath) end
                    OnDecompressErr(url, subPatchFolderPath, curSubPatchInfo)
                    return
                end

                -- 更新已下载完成总大小
                preload_resources_main.patchZipInfo.downloadedSize = preload_resources_main.patchZipInfo.downloadedSize + curSubPatchInfo.totalSize
                -- 下载完成一个包体后，主动更新一次显示进度，避免显示延时
                preload_resources_main.OnUpdateDownloadPatch()
                local recordPatchInfo = preload_resources_main.GetRecordSubpatchInfo(curSubPatchInfo)
                recordPatchInfo.subIdx = idx
                recordPatchInfo.subPatchName = curSubPatchInfo.subPatchName
                recordPatchInfo.isComplete = true
                recordPatchInfo.downloadedSize = util.Long2Int(curSubPatchInfo.totalSize)

                if idx == 1 then
                    -- 第一个 zip 包，需要解压以获取后续子包体数据
                    ParseSubPatchConfig(subPatchFolderPath, preload_resources_main.recordJson.subpatch_set)
                end

                util.StartWatch("update NewCaching files"..idx, "检验 NewCaching 文件")
                UpdateNewCachingFiles(subPatchFolderPath)
                util.StopWatch("update NewCaching files"..idx)

                -- 必须在文件完整覆盖 NewCaching 文件后，才能写入当前子补丁文件下载完成
                preload_resources_main.SavePatchRecord()

                local nextUrl,nextSubPatchInfo = preload_resources_main.GetDownloadPatchUrl()
                downloadCountMax = DOWNLOAD_COUNT_MAX
                DownloadPatchUrl(nextUrl, nextSubPatchInfo)
            end
            if background_load then
                zip.DecompressFile(subPatchSaveFilePath, subPatchFolderPath, nil, onloaded)
            else
                local decompressSuccess = zip.DecompressFile(subPatchSaveFilePath, subPatchFolderPath)
                onloaded(decompressSuccess)
            end
        else
            OnDownloadErr(url,curSubPatchInfo, errMsg)
        end
    end, subPatchSaveFilePath, false)

    preload_resources_main.downloadingSubPatchInfo.requestAsync = requestAsync
    preload_resources_main.downloadingSubPatchInfo.subPatchInfo = subPatchInfo
end

function UpdateNewCachingFiles( subpatchFolderPath )
    local subpatchFilesPath = Path.Combine(subpatchFolderPath, "files.txt")
    local filesJson = util.ReadJson(subpatchFilesPath)
    local filesSet = filesJson.list
    local _ver,_md5,_fileSize,_crc32,_unitycrc
    local subpatchFilePath,newCachingFilePath,fileStream
    local fsMgrInstance = FSMgr.Instance
    for assetbundleName,fileInfo in pairs(filesSet) do
        newCachingFilePath = BgDownloadFileMgr.GetNewCachingDataPath(assetbundleName)
        fileStream = fsMgrInstance:Get(newCachingFilePath)
        if fileStream == nil then
            -- 有其它线程或模块在使用此文件，忽略
            log.Warning(newCachingFilePath .. " is used by other module")
        else
            subpatchFilePath = Path.Combine(subpatchFolderPath, assetbundleName)
            -- crc32 校验很耗时，20 ~ 30个文件，pc 测试 > 0.1 s,不做校验约 < 0.05s ,由于是从 zip 中解压文件，不再对解压后的文件做校验
            -- subpatchFileCrc = util.Crc32File(subpatchFilePath)

            -- 先关闭 NewCaching 打开的文件流，再拷贝文件
            FSMgr.Instance:Return(newCachingFilePath, fileStream)

            _crc32 = fileInfo[4]
            remoteCrc32 = AssetBundleManager.hashRemote:GetCheckSum(assetbundleName)
            -- 统一类型进行比较
            -- str_subpatchFileCrc = tostring(subpatchFileCrc)
            str_remoteCrc32 = tostring(remoteCrc32)
            -- if _crc32 == str_subpatchFileCrc and _crc32 == str_remoteCrc32 then
            if _crc32 == str_remoteCrc32 then
                if File.Exists(subpatchFilePath) then -- 可能有源文件不存在的情况
                    -- 可覆盖拷贝
                    File.Copy(subpatchFilePath, newCachingFilePath, true)
                    -- 修改 NewCaching hashCaching
                    AssetBundleManager.SaveCachingHash(assetbundleName)
                end
            else
                log.Warning(assetbundleName, "crc 不一致:", _crc32, str_subpatchFileCrc, str_remoteCrc32)
            end
        end
    end
end

function PrintABInfo()
    if(downloadABNameList == nil)then
        return
    end
    if(not preload_resources_main.isPrintABInfo)then
        return
    end
    local length = downloadABNameList.Length or #downloadABNameList
    local addInd = 0
    if not downloadABNameList.Length then
        addInd = 1
    end
    for i = addInd, length+addInd-1 do
        local abName = downloadABNameList[i]
        local newCachingFilePath = BgDownloadFileMgr.GetNewCachingDataPath(abName)
        local localCrc32 = "nil"
        if(File.Exists(newCachingFilePath)) then
            localCrc32 = util.Crc32File(newCachingFilePath)
        end
        local remoteCrc32 = AssetBundleManager.hashRemote:GetCheckSum(abName)
        log.Warning("preload_resources,download ab name->",abName,"localCrc32->",localCrc32,"remoteCrc32->",remoteCrc32)
    end
end

function OnFail()
    preload_resources_main.Finish()
end

function OnDownloadProgressChange(finishCount, totalCount,finishSize,totalSize)
    preload_resources_main.preload_resources_ui.RefreshProgressUI(finishCount,totalCount,finishSize,totalSize)
    if finishCount == totalCount then
        Debug.LogWarning("登陆预加载：成功回调")

        preload_resources_main.UpdateDividedPatchRecord()
        preload_resources_main.Finish()

        --登录前完整资源下载完成后，创建对应版本的本地模拟HashRemote
        if downloadType == DownloadPatchType.divided_files and totalCount > 0 then
            if util.IsCSharpClass(OldResVersionManager) then
                if OldResVersionManager and not OldResVersionManager.IS_OLD_RES_VERSION then
                    OldResVersionManager.CreateHashRemoteVirtualOfCurrentDownLoad("preload_resources")
                    log.Warning("UseOldResVersion CreateHashRemoteVirtualOfCurrentDownLoad by preload_resources")
                end
            end
        end
    end
end
