local print = print
local require = require
local table = table

local CS = CS
local GC = CS.System.GC
local LuaManager = CS.War.Script.LuaManager
local Application = CS.UnityEngine.Application

local coroutine = coroutine
local debug = debug

local const = require "const"

local M = {}

function OnPlayMode( modeState )
    if modeState == 3 then
        -- PlayModeStateChange.ExitingPlayMode = 3
        M:OnExitingPlayMode()
    end
end

function M:OnExitingPlayMode()

    -- 销毁所有窗口
    local ui_window_mgr = require "ui_window_mgr"
    local ui_login_main = require "ui_login_main"
    ui_login_main.OnCloseUI()
    ui_window_mgr:CloseAll(nil, true)
    ui_window_mgr:CheckState()
    
    

    local isHook = false
    if isHook then
        local L = LuaProfiler.mainL
        -- 调用C# LuaTable LuaFunction WeakTable的析构 来清理掉lua的 ref
        GC.Collect()
        -- 清理掉C#强ref后，顺便清理掉很多弱引用
        LuaDLL.lua_gc_unhook(L, 2, 0)
    else
        GC.Collect()
        collectgarbage("collect")
    end
    
end

--初始化函数
function init()
    if not Application.isEditor then
        return
    end

    --LuaManager.onPlayMode = LuaManager.onPlayMode and (LuaManager.onPlayMode + OnPlayMode) or OnPlayMode

    ---- 获取协程堆栈
    --local xresume = coroutine.resume
    --local xtrace = debug.traceback
    --
    ---- magic here! Take care of main thread.
    --local mainthr = coroutine.running()      -- captureing is a must.
    --debug.traceback = function(athr)
    --    if athr then return xtrace(athr) end  -- no interest in specified thread.
    --    return xtrace(mainthr)
    --end
    --
    --coroutine.resume = function(thr, ...)
    --    -- another magic.
    --    local uptrace = debug.traceback
    --    debug.traceback = function(athr)
    --        if athr then return xtrace(athr) end  -- no interest in specified thread.
    --        return xtrace(thr)     -- trace the stack of thr.
    --                .. '\n' .. uptrace() -- trace from thr's resume point.
    --    end
    --
    --    local result = { xresume(thr, ...) }
    --    debug.traceback = uptrace
    --    return table.unpack(result)
    --end
end

init()

return M
	
