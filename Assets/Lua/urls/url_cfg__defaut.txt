local require = require

local game_config = require "game_config"
local const = require "const"
local url_mgr = require "url_mgr"

module("url_cfg__defaut")
------------------------------缺省url---------------------------- 
--战斗回放
REPLAY_WEB_URL = {	-- 第一个内测，第二个发布环境
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/%s/report", "https://api-cn.q1.com/games/"..const.GAMEID.."/api/battles/%s/report"},		-- 中国
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/%s/report", "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/battles/%s/report"},		-- 美加
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/%s/report", "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/battles/%s/report"},		-- 东南亚
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/%s/report", "https://api.q1.com/games/"..const.GAMEID.."/api/battles/%s/report"},        -- 预演服
}

--阿里云战斗回放
REPLAY_OSS_WEB_URL = "https://avi.hnjianlai.com"..const.GAMEID.."/avi-wm2/%s"

--多场战斗回放
REPLAY_MULTI_WEB_URL = {	-- 第一个内测，第二个发布环境
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/reportlist?ids=", "https://api-cn.q1.com/games/"..const.GAMEID.."/api/battles/reportlist?ids="},		-- 中国
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/reportlist?ids=", "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/battles/reportlist?ids="},		-- 美加
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/reportlist?ids=", "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/battles/reportlist?ids="},		-- 东南亚
    {url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/battles/reportlist?ids=", "https://api.q1.com/games/"..const.GAMEID.."/api/battles/reportlist?ids="},        -- 预演服
}

--获取最后登录的服务器id
LAST_WORLD_URL = {
	-- 内网不能访问 api.dev.q1op.com，请求最近登录链接改为 api.dev.q1oa.com
	-- 2021-12-01lastworld域名修改为原来的userid改为三个参数
	{url_mgr.NEW_DOMAIN_NAME.."game/api/account/lastworld?", "https://ops-api.q1.com/game/api/account/lastworld?"},		-- 中国
	{url_mgr.NEW_DOMAIN_NAME.."game/api/account/lastworld?", "https://ops-api-ea.q1.com/game/api/account/lastworld?"},		-- 美加
	{url_mgr.NEW_DOMAIN_NAME.."game/api/account/lastworld?", "https://ops-api-sa.q1.com/game/api/account/lastworld?"},		-- 东南亚
}

--举报
REPORT_URL = {-- 第一个内测，第二个发布环境
	{url_mgr.NEW_DOMAIN_NAME, "http://api-cn.q1.com/"},		-- 中国
	{url_mgr.NEW_DOMAIN_NAME, "http://api-xg-ea.q1.com/"},		-- 美加
	{url_mgr.NEW_DOMAIN_NAME, "http://api-xg-sa.q1.com/"},		-- 东南亚
}

BUG_REPORT_URL = "https://res.ssl.q1.com/uploadg.axd?"

-- OSS 日志上报域名
REPORT_OSS_ROOT_URL = {
	LAN = "http://api.dev.q1op.com/oss",		-- 内网
	DOMESTIC = "http://api.dev.q1op.com/oss",	-- 国内
	EN = "https://api-ea.q1.com/oss",			-- 欧美
}

--反馈客服后台api 第一个测试服，第二个正式服
CUSTOMER_SERVICE_URL = {"http://overseas-operation-common-api.dev2.q1op.com/api/commonclient/bug/report", "https://ops-api-ea.q1.com/api/commonclient/overseas/bug/report"}

--聊天相关
channelAllotUrl = url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"                      	--请求分配频道 内测
channelChangeUrl =url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"     	--请求切换频道 内测
channelMsgUrl = url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/channelchat/channelchatData?ChannelId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"                  --请求频道聊天数据 内测
channelConfigUrl = url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/channelchat/channelchatlist?LanguageId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"              --请求求频道列表 内测
privateMsgUrl = url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/channelchat/roleChatData?RoleId=%d&ServerId=%d&UserId=%s&Timestamp=%s&Sign=%s"                                  --请求私聊消息 内测
CHANNEL_ALLOT_URL = {--请求分配频道
    {channelAllotUrl, "https://api-cn.q1.com/games/"..const.GAMEID.."/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 中国
	{channelAllotUrl, "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 美加
	{channelAllotUrl, "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 东南亚
	{channelAllotUrl, "https://api.q1.com/games/"..const.GAMEID.."/api/channelchat/allot?UserId=%s&RoleId=%d&ServerId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 预演
}
CHANNEL_CHANGE_URL = {--请求切换频道
    {channelChangeUrl, "https://api-cn.q1.com/games/"..const.GAMEID.."/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 中国
	{channelChangeUrl, "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 美加
	{channelChangeUrl, "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 东南亚
	{channelChangeUrl, "https://api.q1.com/games/"..const.GAMEID.."/api/channelchat/change?NewChannelId=%d&UserId=%s&ServerId=%d&RoleId=%d&LanguageId=%d&Timestamp=%s&Sign=%s"},		-- 预演
}
CHANNEL_MSG_URL = {--请求频道聊天数据
    {channelMsgUrl, "https://api-cn.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatData?ChannelId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 中国
	{channelMsgUrl, "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatData?ChannelId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 美加
	{channelMsgUrl, "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatData?ChannelId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 东南亚
	{channelMsgUrl, "https://api.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatData?ChannelId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 预演
}
CHANNEL_CONFIG_URL = { --请求求频道列表
    {channelConfigUrl, "https://api-cn.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatlist?LanguageId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 中国
	{channelConfigUrl, "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatlist?LanguageId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 美加
	{channelConfigUrl, "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatlist?LanguageId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 东南亚
	{channelConfigUrl, "https://api.q1.com/games/"..const.GAMEID.."/api/channelchat/channelchatlist?LanguageId=%d&UserId=%s&RoleId=%d&ServerId=%d&Timestamp=%s&Sign=%s"},		-- 预演
}
PRIVATE_MSG_URL = {--请求私聊消息
    {privateMsgUrl, "https://api-cn.q1.com/games/"..const.GAMEID.."/api/channelchat/roleChatData?RoleId=%d&ServerId=%d&UserId=%s&Timestamp=%s&Sign=%s"},		-- 中国
	{privateMsgUrl, "https://api-xg-ea.q1.com/games/"..const.GAMEID.."/api/channelchat/roleChatData?RoleId=%d&ServerId=%d&UserId=%s&Timestamp=%s&Sign=%s"},		-- 美加
	{privateMsgUrl, "https://api-xg-sa.q1.com/games/"..const.GAMEID.."/api/channelchat/roleChatData?RoleId=%d&ServerId=%d&UserId=%s&Timestamp=%s&Sign=%s"},		-- 东南亚
	{privateMsgUrl, "https://api.q1.com/games/"..const.GAMEID.."/api/channelchat/roleChatData?RoleId=%d&ServerId=%d&UserId=%s&Timestamp=%s&Sign=%s"},		-- 预演
}

--预约邮箱gameVersion
gameVersion_dev = const.GAMEID.."-LOCAL-DEV" --内网
gameVersion_cn = const.GAMEID.."-CN-ZS" -- 中国
gameVersion_ea = const.GAMEID.."-US-ZS" -- 美加
gameVersion_sa = const.GAMEID.."-AP-ZS" -- 东南亚
--预约邮箱dev域名
book_mail_dev_domain = "http://actapi.dev.q1op.com/"
-- 1.发送邮件
sendMailUrl = book_mail_dev_domain.."api/huodong/bookemail/send"
SEND_MAIL = {
	{sendMailUrl, "https://actapi.q1.com/api/huodong/bookemail/send"},		    -- 中国
	{sendMailUrl, "https://actapi-ea.q1.com/api/huodong/bookemail/send"},		-- 美加
	{sendMailUrl, "https://actapi-sa.q1.com/api/huodong/bookemail/send"},		-- 东南亚
	{sendMailUrl, "http://actapi.dev.q1op.com/api/huodong/bookemail/send"},		-- 预演
}
-- 2、查询预约的邮箱（按通行证）
queryBookMailUrl = book_mail_dev_domain.."api/huodong/bookemail/get/"..const.GAMEID.."/"..gameVersion_dev
QUERY_BOOK_MAIL = {
	{queryBookMailUrl, "https://actapi.q1.com/api/huodong/bookemail/get/"..const.GAMEID.."/"..gameVersion_cn},		    -- 中国
	{queryBookMailUrl, "https://actapi-ea.q1.com/api/huodong/bookemail/get/"..const.GAMEID.."/"..gameVersion_ea},		-- 美加
	{queryBookMailUrl, "https://actapi-sa.q1.com/api/huodong/bookemail/get/"..const.GAMEID.."/"..gameVersion_sa},		-- 东南亚
	{queryBookMailUrl, "http://actapi.dev.q1op.com/api/huodong/bookemail/get/"..const.GAMEID.."/"..gameVersion_dev},    -- 预演
}
-- 3、邮箱预约（按通行证）
bookMailUrl = book_mail_dev_domain.."api/huodong/bookemail/add/"..const.GAMEID.."/"..gameVersion_dev
BOOK_MAIL = {
	{bookMailUrl, "https://actapi.q1.com/api/huodong/bookemail/add/"..const.GAMEID.."/"..gameVersion_cn},		    -- 中国
	{bookMailUrl, "https://actapi-ea.q1.com/api/huodong/bookemail/add/"..const.GAMEID.."/"..gameVersion_ea},		-- 美加
	{bookMailUrl, "https://actapi-sa.q1.com/api/huodong/bookemail/add/"..const.GAMEID.."/"..gameVersion_sa},		-- 东南亚
	{bookMailUrl, "http://actapi.dev.q1op.com/api/huodong/bookemail/add/"..const.GAMEID.."/"..gameVersion_dev},    -- 预演
}
-- 4、解绑邮箱
unbindMailUrl = book_mail_dev_domain.."api/huodong/bookemail/un/"..const.GAMEID.."/"..gameVersion_dev
UNBIND_MAIL = {
	{unbindMailUrl, "https://actapi.q1.com/api/huodong/bookemail/un/"..const.GAMEID.."/"..gameVersion_cn},		    -- 中国
	{unbindMailUrl, "https://actapi-ea.q1.com/api/huodong/bookemail/un/"..const.GAMEID.."/"..gameVersion_ea},		-- 美加
	{unbindMailUrl, "https://actapi-sa.q1.com/api/huodong/bookemail/un/"..const.GAMEID.."/"..gameVersion_sa},		-- 东南亚
	{unbindMailUrl, "http://actapi.dev.q1op.com/api/huodong/bookemail/un/"..const.GAMEID.."/"..gameVersion_dev},    -- 预演
}

--支付
PAY_URL = "http://mpay-sa.xxk.4g.q1.com:800/?Money=%s&GameID="..const.GAMEID.."&UserID=%s&CurrencyType=%s&ServerID=%s&OrderItem=%s&OrderNO=%s&OrderSign=%s&PID=1&ActorID=%s&backurl=http://xherocz.yr.dev.q1.com/welfare.html#page2"

--跳转qq Url
QQ_URL = "https://jq.qq.com/?_wv=1027&k=NZbHMUIq"

--公告（弃用）
NOTICE_DATA_URL = "https://hero2.q1.com/newsfile/js/cache/"..const.GAMEID.."BillboardList.js"

--公告渠道配置
NOTICE_CHANNEL_CONFIG = {
	--海外
	{"https://hero2-ea.q1.com/newsfile/js/cache/"..const.GAMEID.."BillboardList.js", --内网测试
		"https://hero2-ea.q1.com/newsfile/js/cache/"..const.GAMEID.."BillboardList.js"}, --正式
	--国内
	{"https://hero2.q1.com/newsfile/js/cache/"..const.GAMEID.."BillboardList.js", --内网测试
		"https://hero2.q1.com/newsfile/js/cache/"..const.GAMEID.."BillboardList.js"} --正式
	}

--登录公告渠道配置
LOGIN_NOTICE_CHANNEL_CONFIG = {
	--海外
	{
		"https://q1-operation-bulletin.oss-cn-beijing.aliyuncs.com/%s/%s/bulletin.json", --内网测试
		"https://opsoss.q1.com/%s/%s/bulletin.json"--正式
	}, 
	--国内
	{
		"https://q1-operation-bulletin.oss-cn-beijing.aliyuncs.com/%s/%s/bulletin.json", --内网测试
		"https://opsoss.q1.com/%s/%s/bulletin.json"--正式
	}, 
}

--公告banner渠道配置
NOTICE_BANNER_CHANNEL_CONFIG = {
	--海外
	{
		"https://q1-operation-bulletin.oss-cn-beijing.aliyuncs.com/%s/%s/banner.json", --内网测试
		"https://opsoss.q1.com/%s/%s/banner.json"--正式
	}, 
	--国内
	{
		"https://q1-operation-bulletin.oss-cn-beijing.aliyuncs.com/%s/%s/banner.json", --内网测试
		"https://opsoss.q1.com/%s/%s/banner.json"--正式
	}, 
}

--公告H5界面
NOTICE_HTML = 	{"https://xheromobile.com/notice?serverid=%s&language=%s&jsurl=%s&time=%s", --海外
				"https://hero2.q1.com/notice?serverid=%s&language=%s&jsurl=%s&time=%s"} --国内


--已有角色服务器列表
ROLE_SERVERS_URL_PATH = "game/api/role/list?token=%s&pid=%s&userId=%s&userName=%s&gameId=%s&sign=%s&gameVersion=%s"
ROLE_SERVERS_URL = {-- 第一个内测，第二个发布环境
	{url_mgr.NEW_DOMAIN_NAME, "http://api-cn.q1.com/"},		-- 中国
	{url_mgr.NEW_DOMAIN_NAME, "https://ops-api-ea.q1.com/"},		-- 美加
	{url_mgr.NEW_DOMAIN_NAME, "https://ops-api-sa.q1.com/"},		-- 东南亚
}
--某个英雄的评论数据
hero_comment_api = "gameId=%s&userId=%s&areaId=%s&areaName=%s&worldId=%s&worldName=%s&actorId=%s&actorName=%s&sign=%s&heroId=%s&heroName=%s&heroAvatar=%s&lang=%s"
HERO_COMMENT_URL = {
    {"http://xhero-comment.dev.q1op.com/#/hero?"..hero_comment_api, "http://xhero-comment-cn.q1.com/#/hero?"..hero_comment_api}, -- 中国
    {"http://xhero-comment.dev.q1op.com/#/hero?"..hero_comment_api, "http://xhero-comment-ea.q1.com/#/hero?"..hero_comment_api}, -- 美加
    {"http://xhero-comment.dev.q1op.com/#/hero?"..hero_comment_api, "http://xhero-comment-sa.q1.com/#/hero?"..hero_comment_api} -- 东南亚
}

translateUrl = "http://translate.dev.q1op.com/api/livedata/translate"
-- translateUrl = "http://translate-wxg.dev.q1op.com/api/livedata/translate"
TRANSLATE_URL = {--请求私聊消息
	{translateUrl, "https://translate.q1.com/api/livedata/translate"},		-- 中国
	{translateUrl, "https://translate.q1.com/api/livedata/translate"},		-- 美加
	{translateUrl, "https://translate.q1.com/api/livedata/translate"},		-- 东南亚
	-- {translateUrl, "http://translate-wxg.dev.q1op.com/api/livedata/translate"},		-- 预演
	{translateUrl, "http://translate.dev.q1op.com/api/livedata/translate"},		-- 预演
}

--注册账号时 已阅读服务协议并接受
AGREE_USER_PROTOCOL_URL = 'http://passport.q1.com/Html/Register/UserProtocol.html'

--实名认证
real_name_api = "/mapi.asmx/MRealNameAuthentForGame?GameID=%d&UUID=%s&User=%s&trueName=%s&IDCardNo=%s&time=%d&sign=%s"
REAL_NAME_URL = {"http://passport.4g.q1.com:800"..real_name_api,"https://passport.q1.com"..real_name_api}

-- 请求绑定手机验证码
BINDINGPHONE_REQCAPTCHA = {
	{"http://sdk-api.dev2.q1op.com/api/sdk/v2/captchas/action/game/send?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s",
	"https://sdkapi.youyi-game.com/api/sdk/v2/captchas/action/game/send?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s"}
}

-- 请求绑定手机
BINDINGPHONE_REQ = {
	{"http://sdk-api.dev2.q1op.com/api/sdk/v2/profile/bind/game/mobile/?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s",
	"https://sdkapi.youyi-game.com/api/sdk/v2/profile/bind/game/mobile/?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s"}
}

-- 查询绑定信息
CHECK_BINDINGPHONE_INFO={
	{"http://sdk-api.dev2.q1op.com/api/sdk/v2/profile/bind/game/check?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s&rtype=%s&session=%s",
	"https://sdkapi.youyi-game.com/api/sdk/v2/profile/bind/game/check?timestamp=%s&pid=%s&encryptflag=%s&clienttype=%s&uuid=%s&sign=%s&appid=%s&rtype=%s&session=%s"}
}

-- 查询社区开放配置
CHECK_COMMUNITY_ENTRANCE = {
	{
		url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/data/communityconfigList?Timestamp=%s&Sign=%s",
		"https://api-cn.youyi-game.com/games/"..const.GAMEID.."/api/data/communityconfigList?Timestamp=%s&Sign=%s"
	},
	{
		url_mgr.NEW_DOMAIN_NAME.."games/"..const.GAMEID.."/api/data/communityconfigList?Timestamp=%s&Sign=%s",
		"https://api-cn.youyi-game.com/games/"..const.GAMEID.."/api/data/communityconfigList?Timestamp=%s&Sign=%s"
	},
}

-- 访问社区（自动登陆）
COMMUNITY_VISIT = {
	{
		"http://club-overseas.test.q1.com/?type=1&platform=1&env=game&pid=%s&gameId=%s&userId=%s&token=%s&gameVersion=%s&languageId=%s&actorId=",
		"https://club-en.q1.com/?type=1&platform=1&env=game&pid=%s&gameId=%s&userId=%s&token=%s&gameVersion=%s&languageId=%s&actorId="
	},
	{
		"http://club-overseas.test.q1.com/?type=1&platform=1&env=game&pid=%s&gameId=%s&userId=%s&token=%s&gameVersion=%s&languageId=%s&actorId=",
		"https://club-en.q1.com/?type=1&platform=1&env=game&pid=%s&gameId=%s&userId=%s&token=%s&gameVersion=%s&languageId=%s&actorId="
	},
}

DISCORD_URL = "https://discord.gg/6R5hD2b3Rg"
TWITTWER_URL = "https://twitter.com/heroclashbattle"
NAVERGAME_URL = "https://game.naver.com/lounge/Hero_Clash/home"
NAVERCAFE_URL = "https://cafe.naver.com/heroclashbattle"
APPLE_URL ="https://apps.apple.com/app/id6443814210"--IOS亚洲包
GOOGLE_URL = "https://play.google.com/store/apps/details?id=com.xgame.newasia.sea.gp"--谷歌亚洲包
FACEBOOK_URL = "https://www.facebook.com/XClashOfficial/"
FACEBOOK_ROOT_URL = "https://www.facebook.com/XClashOfficial/"
FACEBOOK_SHARE_URL = "https://www.facebook.com/XClashOfficial/"
JAPLEGAL_AND_URL = "https://protocol.9z-play.com/GlaciersGame/enPrivacyPolicy.html"--谷歌跳转
JAPLEGAL_IOS_URL = "https://protocol.9z-play.com/GlaciersGame/enPrivacyPolicy.html"--IOS跳转
JAPLEGAL2_URL = "https://protocol.q1.com/x-clash/jpFundsSettlementAct.html"--谷歌和IOS都跳转

--维护公告
--NOTICE_NAINTAIN = "https://xheromobile.com/maintain?serverid=%s&language=%s&jsurl=%s"

-- 跳转评分链接
ANDROID_GRADE = "https://play.google.com/store/apps/details?id=com.q1.hero&fbclid=IwAR2RAcmd6RvvzVJJVUzh5foa4Zsj2ZrBMi6gc1RKtJzwP0Q8iGdOQkGHttc"

local channel_tag = game_config.CHANNEL_TAG
if channel_tag == const.package_name_set.com_bc_avenger then
	ANDROID_GRADE = "https://play.google.com/store/apps/details?id=com.bc.avenger"
end
IOS_GRADE = "https://apps.apple.com/cn/app/id1637377185"

--问卷调查  %s介绍:第一个下面的param  第二个param参数加密
CHECK_QUESTIONNAIRE_INVESTIGATION =
{
	"https://questionnaire-web-ea.test.q1oa.com/api/questionnaire/v1/questionnaire/list?%s&sign=%s",
	"https://question-ea.q1.com/api/questionnaire/v1/Questionnaire/reallist?%s&sign=%s"
}
CHECK_QUESTIONNAIRE_INVESTIGATION_PARAM = "appid=%s&gameid=%s&gameversion=%s&timestamp=%s"

CHECK_QUESTIONNAIRE_INVESTIGATION_SECRET_PARAM = "appid=%s&body=%s&gameid=%s&gameversion=%s&timestamp=%s"

--双旦H5
SHUANGDAN_H5_URL = {
	"http://xgame-ea.whh.dev.q1.com/activity/review/index.html?platform=%s&gameVersion=%s&actorId=%s&sdkToken=%s&server=%s&language=%s",
	"https://xgame-ea.q1.com/activity/review/index.html?platform=%s&gameVersion=%s&actorId=%s&sdkToken=%s&server=%s&language=%s"
}

-- 支付链接
PAY_NOTIFY_URL = "https://gameapi-test.q1.com/v2/api/pay/tanwan/"..const.GAMEID.."/"..const.GAMEID.."-LOCAL-GJ"
if game_config.ENABLE_Q1_DEBUG_MODE then
	if const.IsVietnamChannel() then
		PAY_NOTIFY_URL = "https://gameapi-test.q1.com/v2/api/pay/tanwan/"..const.GAMEID.."/"..const.GAMEID.."-LOCAL-DEV-US"--贪玩越南包
	else
		PAY_NOTIFY_URL = "https://gameapi-test.q1.com/v2/api/pay/tanwan/"..const.GAMEID.."/"..const.GAMEID.."-LOCAL-GJ"
	end
else
	if const.IsVietnamChannel() then
		PAY_NOTIFY_URL = "https://gameapi-sgp.q1.com/v2/api/pay/tanwan/"..const.GAMEID.."/"..const.GAMEID.."-VN-ZS"--贪玩越南包
	else
		PAY_NOTIFY_URL = "https://gameapi-india.q1.com/v2/api/pay/aiwan/"..const.GAMEID..""--爱玩印度
	end
end

--年中庆典H5
NianZhong_H5_URL = {
	"http://xgame-ea.whh.dev.q1.com/activity/galas/index.html?platform=%s&gameVersion=%s&actorId=%s&sdkToken=%s&server=%s&language=%s",
	"https://xgame-ea.q1.com/activity/galas/index.html?platform=%s&gameVersion=%s&actorId=%s&sdkToken=%s&server=%s&language=%s"
}

--AI客服URL
AI_CUSTOMER_SERVICE_API = "?gameId=%s&loginType=%s&platFormType=%s&mainType=%s&sign=%s&actorId=%s&sdkToken=%s"
AI_CUSTOMER_SERVICE_URL =
{
	"http://kefu.4g.q1.com:800/aichat/index.html"..AI_CUSTOMER_SERVICE_API,
	"https://kefu-ea.q1.com/aichat/index.html"..AI_CUSTOMER_SERVICE_API
}