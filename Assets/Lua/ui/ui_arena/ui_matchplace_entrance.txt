--@region FileHead
-- ui_matchplace_entrance.txt ---------------------------------
-- author:  李志平
-- date:    11/29/2018 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------


--@region Require
local print = print
local require = require
local pairs = pairs
local ipairs = ipairs
local typeof = typeof
local string = string
local table = table
local tonumber = tonumber
local os = os

local GameObject = CS.UnityEngine.GameObject
local Button = CS.UnityEngine.UI.Button
local Text = CS.UnityEngine.UI.Text
local Image = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform

local class = require "class"
local ui_base = require "ui_base"
local weapon_data = require("weapon_data")

local module_scroll_list = require "scroll_list"

local net_arena_module = require "net_arena_module"
local log = require "log"
local event = require "event"
local common_new_pb = require "common_new_pb"
local shop_pb = require "shop_pb"
local util = require "util"
local face_item = require "face_item_new"
local game_scheme = require "game_scheme"
local lang = require "lang_util"
local ui_window_mgr = require "ui_window_mgr"
local ui_utils = require "ui_utils"
local player_mgr = require "player_mgr"
local skep_mgr = require "skep_mgr"
local arena_data = require "arena_data"
local topGame_data = require "topGame_data"
local net_topgame_module = require "net_topgame_module"
local flow_text = require "flow_text"
local ReviewingUtil = require "ReviewingUtil"
local weekend_arena_mgr = require "weekend_arena_mgr"
local Lang = require "lang"
local peakGame_data = require "peakGame_data"
local TextMeshProUGUI = CS.TMPro.TextMeshProUGUI



--@region ModuleDeclare
module("ui_matchplace_entrance")
--local interface = require "iui_matchplace_entrance"
local window = nil
local UIMatchplaceEntrance = {}
local ArenaCoin = 41009
local goodsEntity = nil
local closeCallBack = nil
local ticker = nil
local reqOnce = true
local surplusTime = 0
local isInitWeekendArenaData = false


--@region WidgetTable
UIMatchplaceEntrance.widget_table = {
    Btn_MallBtn = { path = "Auto_MallBtn", type = "Button", },
    Btn_Unit1 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button", type = "Button", },
    Btn_Unit2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button", type = "Button", },
    Btn_Unit3 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button", type = "Button", },
    Auto_Unit2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2", type = "RectTransform", },
    Auto_Unit3 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3", type = "RectTransform", },
    Btn_Unit4 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button", type = "Button", },
    Auto_Unit4 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4", type = "RectTransform", },

    Btn_CloseBtn = { path = "Auto_closeBtn", type = "Button", backEvent = true },
    Text_CoinCount = { path = "Box/Auto_CoinCount", type = "Text", },
    Text_Name1 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Auto_Name", type = "Text", },
    Text_Name2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/Auto_Name", type = "Text", },
    Text_Name3 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/Auto_Name", type = "Text", },
    Text_Name4 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/Auto_Name", type = "Text", },

    Title_Name1 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/title", type = "RectTransform" },
    Title_Name2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/title", type = "RectTransform" },
    Title_Name3 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/title", type = "RectTransform" },
    Title_Name4 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/title", type = "RectTransform" },
    Title_Name5 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/title", type = "RectTransform" },

    Text_CountDown1 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_CountDown2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_CountDown3 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_CountDown4 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_CountDown5 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_CountDown6 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/Auto_Countdown", type = TextMeshProUGUI, },
    Text_Unit1_Power = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/zhanli/Auto_Power", type = TextMeshProUGUI, },
    Text_Unit1_Rank = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Auto_Rank", type = TextMeshProUGUI, },
    Text_Unit2_Power = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/zhanli/Auto_Power", type = TextMeshProUGUI, },
    Text_Unit2_Rank = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/Auto_Rank", type = TextMeshProUGUI, },
    Btn_Unit3_Des = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/des", type = TextMeshProUGUI, fitArabic = true }, --文本较长，阿拉伯语右对齐
    Btn_Unit3_gameTip = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/gameTip", type = "RectTransform", },
    Btn_Unit3_gameTip_Reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/reddet", type = "Image" },
    Btn_Unit4_gameTip = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/gameTip", type = "RectTransform", },
    Btn_Unit4_gameTip_Reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/reddet", type = "Image" },


    Rtsf_top = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/no1", type = "RectTransform" },
    Rtsf_topIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/no1/iconRoot", type = "RectTransform" },
    Text_name = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/no1/name", type = Text },

    topgame_champion = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/Champion", type = "RectTransform" },
    topgame_championIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/Champion/iconRoot", type = "RectTransform" },
    topgame_championName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/Champion/name", type = Text },

    newtopgame_champion = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/Champion", type = "RectTransform" },
    newtopgame_championIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/Champion/iconRoot", type = "RectTransform" },
    newtopgame_championName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/Champion/name", type = Text },

    unit1_champion = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Champion", type = "RectTransform" },
    unit1_championIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Champion/iconRoot", type = "RectTransform" },
    unit1_championName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/Champion/name", type = Text },

    Auto_Unit1_Reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit1/Button/reddot", type = "Image" },
    Auto_Unit2_Reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/reddot", type = "Image" },

    rewardIconRoot1 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/reward/icon1", type = "RectTransform" },
    rewardIconRoot2 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit2/Button/reward/icon2", type = "RectTransform" },

    --新服巅峰赛、王者争霸赛界面增加排名领奖按钮
    Auto_Unit3_Reward = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/reward", type = "Button", event = "OnClickUnit3RewardBtn" },
    Auto_Unit3_RewardRed = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit3/Button/reward/reddet", type = "RectTransform" },
    Auto_Unit4_Reward = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/reward", type = "Button", event = "OnClickUnit4RewardBtn" },
    Auto_Unit4_RewardRed = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit4/Button/reward/reddet", type = "RectTransform" },

    -- 时空擂台
    Auto_Unit5 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5", type = "RectTransform", },
    Btn_Unit5 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button", type = "Button", },
    weekendArena_champion = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/Champion", type = "RectTransform" },
    weekendArena_championIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/Champion/iconRoot", type = "RectTransform" },
    weekendArena_championName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/Champion/name", type = Text },
    weekendArena_desc = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/des", type = TextMeshProUGUI, fitArabic = true },
    weekendArena_tip = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/gameTip", type = "RectTransform" },
    weekendArena_reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit5/Button/reddet", type = "RectTransform" },

    -- 位面巅峰赛
    Auto_Unit6 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6", type = "RectTransform", },
    Btn_Unit6 = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button", type = "Button", event_name = "OnBtn_Unit6ClickedProxy" },
    exChampionship_champion = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/Champion", type = "RectTransform" },
    exChampionship_championIconRoot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/Champion/iconRoot", type = "RectTransform" },
    exChampionship_championName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/Champion/Image/Text", type = "Text" },
    exChampionship_unionName = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/Champion/name", type = "Text" },
    exChampionship_desc = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/des", type = TextMeshProUGUI },
    exChampionship_tip = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/gameTip", type = "RectTransform" },
    exChampionship_reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/reddet", type = "RectTransform" },
    Auto_Unit6_Reward_reddot = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/reward/reddet", type = "RectTransform" },
    Auto_Unit6_Reward = { path = "GameObject/GameObject_1/BtnGroup/Auto_Unit6/Button/reward", type = "Button", event_name = "OnClickUnit6RewardBtn" },

    effectMaskRectTransform = { path = "GameObject/effectMask", type = "RectTransform", effect_mask = true },
}


--@region WindowCtor
function UIMatchplaceEntrance:ctor(selfType)
    self.__base:ctor(selfType)

    self.time1 = 0
    self.time2 = 0
    self.time3 = topGame_data.NextSurplusTime()
    self.time4 = 0
    self.time5 = topGame_data.NextSurplusTime()
    self.newSvrDduration = 0


end

--@region WindowInit
--[[窗口初始化]]
function UIMatchplaceEntrance:Init()
    event.Trigger(event.SCENE_MATCHPLACEREDDOT, 3, false)
    net_arena_module.Send_ARENA_GET_TIME()
    net_topgame_module.Send_TOPRACE_DATA(1)
    net_topgame_module.Send_TOPRACE_DATA(2)
    self:setNewSvrDurationTime()
    -- net_arena_module.Send_ARENA_DEFEND_LINEUP(common_new_pb.CrystalCrown)
    self.Btn_MallBtn.gameObject:SetActive(false)
    -- if net_arena_module.MineTotalCE[common_new_pb.CrystalCrown] then
    --     local usedWeapon = weapon_data.GetLocalizedWeaponData(common_new_pb.Arena,0,arenaType)
    --     local homeland_mgr = require "homeland_mgr"
    --     local weaponPower = homeland_mgr.GetWeaponPowerByID(usedWeapon)
    --print("net_arena_module.MineTotalCE[common_new_pb.CrystalCrown]:",net_arena_module.MineTotalCE[common_new_pb.CrystalCrown])
    self.Text_Unit1_Power.text = net_arena_module.MineTotalCE[common_new_pb.CrystalCrown]
    -- else
    self.Text_Unit1_Power.text = "--"
    -- end
    if net_arena_module.MineRank[common_new_pb.CrystalCrown] then
        self.Text_Unit1_Rank.text = net_arena_module.MineRank[common_new_pb.CrystalCrown]
    else
        self.Text_Unit1_Rank.text = "--"
    end
    -- if net_arena_module.MineTotalCE[common_new_pb.Advance] then
    --     self.Text_Unit2_Power.text = net_arena_module.MineTotalCE[common_new_pb.Advance]
    -- else
    --     self.Text_Unit2_Power.text = "--"
    -- end
    -- if net_arena_module.MineRank[common_new_pb.Advance] then
    --     self.Text_Unit2_Rank.text = net_arena_module.MineRank[common_new_pb.Advance]
    -- else
    --     self.Text_Unit2_Rank.text = "--"
    -- end


    --  --print("isShowTopGame:",topGame_data.isShowTopGame,"topGame_data.getTopgameType():",topGame_data.getTopgameType())
    if not topGame_data.isShowTopGame then
        self.Auto_Unit3.gameObject:SetActive(false)
        self.Auto_Unit4.gameObject:SetActive(false)
    else
        self.Auto_Unit3.gameObject:SetActive(topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame)
        self.Auto_Unit4.gameObject:SetActive(topGame_data.getTopgameType() == topGame_data.TopGameType.NewAera and not ReviewingUtil.IsReviewing())
    end
    if topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame then
        local areaChampionshipSvrCfg = game_scheme:AreaChampionshipSvrCfg_0(topGame_data.getAreaID())
        -- 2021.12.29 找不到配置情况下，日常争霸赛排名前80的玩家将被邀请参赛
        self.Btn_Unit3_Des.text = string.format(lang.Get(9703), areaChampionshipSvrCfg and areaChampionshipSvrCfg.PullRoleNum or 80)
    end

    self.Auto_Unit3_Reward.gameObject:SetActive(#topGame_data.getrolerank() > 0)
    self.Auto_Unit4_Reward.gameObject:SetActive(#topGame_data.getrolerank() > 0)
    self.Auto_Unit6_Reward.gameObject:SetActive(peakGame_data.IsGameOver())
    local peakReward = peakGame_data.GetRankReward()
    self.Auto_Unit6_Reward_reddot.gameObject:SetActive(peakReward and #peakReward > 0)

    self:UpdateTopGameRewardReddot()

    -- self.Btn_Unit3_gameTip.gameObject:SetActive(topGame_data.gameState ~= topGame_data.gameStateType.None)
    --local unforced_guide_mgr = require "unforced_guide_mgr"
    --unforced_guide_mgr.Unlock(4)

    --event.Trigger(event.ENTER_ARENA)
    self:SubscribeEvents()

    local net_legend_championships_module = require "net_legend_championships_module"
    net_legend_championships_module.Send_LEGEND_RANK_DATA()

    local ui_pop_mgr = require "ui_pop_mgr"
    -- self.advance_unlock = ui_pop_mgr.CheckIsOpen(266, false)
    -- if self.advance_unlock  then
    --     net_arena_module.InitalQueryDefence = false
    --     net_arena_module.Send_ARENA_DEFEND_LINEUP(common_new_pb.Advance)
    --     net_arena_module.Send_ARENA_GET_RANKINGLIST(common_new_pb.Advance)
    -- end
    net_arena_module.InitNormalRankInfo = true
    net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)
    self.canClick1 = false
    self.canClick2 = false
    self.canClick3 = false
    self.canClick4 = false

    self.time1 = net_arena_module.SyncTime.crownTime or 0
    self.time4 = net_arena_module.SyncTime.advanceSingleTime or 0

    ticker = util.IntervalCall(1, function(pass)
        if window == nil then
            -- 窗口关闭也去掉
            ticker = nil
            return true
        end

        if not self.UIRoot then
            return false
        end

        if self.time1 > 0 then
            self.canClick1 = self.time1 - pass > 0
            self.Text_CountDown1.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(self.time1 - pass))
            if self.time1 - pass == 0 then
                net_arena_module.Send_ARENA_GET_TIME()
            end
        else
            self.canClick1 = false
            -- self.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7105)..'%s</color>', '03ed68', ui_utils.TimeCountdown(-self.time4-pass))
            -- if -self.time1-pass == 0 then
            --     net_arena_module.Send_ARENA_GET_TIME()
            -- end
        end

        if self.time4 > 0 then
            self.canClick4 = self.time4 - pass > 0
            -- self.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7104)..'%s</color>', 'fff153', ui_utils.TimeCountdown(self.time4-pass))
            -- if self.time4-pass == 0 then
            --     net_arena_module.Send_ARENA_GET_TIME()
            -- end
        else
            self.canClick4 = false
            -- self.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7105)..'%s</color>', '03ed68', ui_utils.TimeCountdown(-self.time4-pass))
            -- if -self.time4-pass == 0 then
            --     net_arena_module.Send_ARENA_GET_TIME()
            -- end
        end

        local legend_championships_mgr = require "legend_championships_mgr"
        local sec = legend_championships_mgr.GetSeasonEndTime() - os.server_time()
        if sec > 0 then
            self.Text_CountDown2.text = string.format(lang.Get(8003) .. '%s', ui_utils.TimeCountdown(sec))
        end

        if topGame_data.isShowTopGame then
            if topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame then
                if self.time3 - pass < 24 * 3600 * 7 then
                    --剩余时间低于7天说明活动已开启，显示活动结束倒计时
                    self.Text_CountDown3.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(self.time3 - pass))
                    if self.Btn_Unit3_gameTip.gameObject.activeSelf ~= true then
                        self.Btn_Unit3_gameTip.gameObject:SetActive(true)
                    end
                else
                    --剩余时间大于7天说明活动还未开启当前是预告时间，显示距离活动开启时间
                    self.Text_CountDown3.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(self.time3 - (24 * 3600 * 7) - pass))
                    if self.Btn_Unit3_gameTip.gameObject.activeSelf ~= false then
                        self.Btn_Unit3_gameTip.gameObject:SetActive(false)
                    end
                end
                --log.Warning("self.time3",self.time3,"pass",pass,"self.Text_CountDown3.text",self.Text_CountDown3.text)
            else
                -- if self.time5-pass > self.newSvrDduration  then
                --     self.Text_CountDown3.text = string.format('<color=#%s>'..lang.Get(7104)..'%s</color>', 'fff153', ui_utils.TimeCountdown(self.time5-(self.newSvrDduration)-pass))
                --     if self.Btn_Unit4_gameTip.gameObject.activeSelf ~= true then
                --         self.Btn_Unit4_gameTip.gameObject:SetActive(true)
                --     end
                -- else
                --     self.Text_CountDown3.text = string.format('<color=#%s>'..lang.Get(7105)..'%s</color>', '03ed68', ui_utils.TimeCountdown(self.time5-pass))
                --     if self.Btn_Unit4_gameTip.gameObject.activeSelf ~= false then
                --         self.Btn_Unit4_gameTip.gameObject:SetActive(false)
                --     end
                -- end
                if window.time5 - pass == window.newSvrDduration then
                    window.time5 = topGame_data.NextSurplusTime()
                    window:setNewSvrDurationTime()
                end
                if window.isshowCountDown then
                    if window.time5 - pass > window.newSvrDduration then
                        -- print(window.time5)
                        window.Text_CountDown4.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time5 - (window.newSvrDduration) - pass))
                        if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= true then
                            window.Btn_Unit4_gameTip.gameObject:SetActive(true)
                        end
                    else
                        --  --print("1:",window.time5)
                        window.Text_CountDown4.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(window.time5 - pass))
                        if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= false then
                            window.Btn_Unit4_gameTip.gameObject:SetActive(false)
                        end
                    end
                else
                    if window.time5 - pass > 0 then
                        window.Text_CountDown4.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time5 - pass))
                        if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= true then
                            window.Btn_Unit4_gameTip.gameObject:SetActive(true)
                        end
                    else
                        window.Text_CountDown4.text = ""
                        if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= false then
                            window.Btn_Unit4_gameTip.gameObject:SetActive(false)
                        end
                    end
                end
            end
        end
        local weekendIsOpen = weekend_arena_mgr.IsOpen()
        if weekendIsOpen then
            local endTime = weekend_arena_mgr.GetEndTime()
            if endTime then
                local curTime = os.server_time()
                local diff = endTime - curTime
                local str_diff = ui_utils.TimeCountdown(diff)
                self.Text_CountDown5.text = string.format(lang.Get(7104) .. '%s', str_diff)
            end
        else
            self.Text_CountDown5.text = lang.Get(8802)
        end

        local peakGameIsOpen = peakGame_data.IsOpen()
        if peakGameIsOpen then
            local curTime = os.server_time()
            local overTime = peakGame_data.GetOverTime()
            local diff = overTime - curTime
            if diff > 0 then
                self.Text_CountDown6.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(diff))
            else
                local startTime = peakGame_data.GetStartTime()
                diff = startTime - curTime
                self.Text_CountDown6.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(diff))
            end
        end
    end)
    -- surplusTime = topGame_data.GetSurplusTime()
    -- self:UpdateTime()
    -- net_topgame_module.Send_TOPRACE_GETTOPLIST(1,topGame_data.getAreaID())
    self:RefreshGoodsNum()
    self.createGoodsEvent = function(event, goodsID, sid)
        if goodsID == ArenaCoin then
            self:RefreshGoodsNum()
        end
    end
    event.Register(event.CREATE_SPECIALGOODS_ENTITY, self.createGoodsEvent)

    self:UpdateReddot()
    self:UpdateTopGameReddot()
    --新手强制引导事件
    local force_guide_system = require "force_guide_system"
    local force_guide_event = require "force_guide_event"
    force_guide_system.TriEnterEvent(force_guide_event.tEventEnterArena)

    --判断是否打开虚空擂台
    local void_ring_mgr = require "void_ring_mgr"
    void_ring_mgr.CheckFightVoidRingTip()

    -- ui_window_mgr:UnloadModule("ui_lobby")
    local menu_bot_data = require "menu_bot_data"
    menu_bot_data.CloseAllPage()
end

function UIMatchplaceEntrance:OnShow()
    self.__base:OnShow()

    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.IsGuiding(56, 430) or unforced_guide_mgr.IsGuiding(4, 71) then
        event.Trigger(event.ENTER_ARENA)
    end
    self:InitLegend()
    self:UpdateWeekendArenaEntrance()
    self:UpdatePeakGameEntrance()
end

function UIMatchplaceEntrance:UpdateWeekendArenaEntrance()
    local ui_pop_mgr = require "ui_pop_mgr"
    local player_mgr = require "player_mgr"
    local openSerTime = player_mgr.GetRoleOpenSvrTime()
    local curTime = os.server_time()
    local diffTime = util.DiffDay(openSerTime, curTime)
    local isUnlock = ui_pop_mgr.CheckIsOpen(941, false)
    self.Auto_Unit5.gameObject:SetActive(false)
    local openCfg = game_scheme:InitBattleProp_0(940)
    local openDay = tonumber(openCfg.szParam.data[0])

    if isUnlock and diffTime >= openDay and not ReviewingUtil.IsReviewing() then
        local serId, isAutoAttend = weekend_arena_mgr.GetMyWeekendCfgID()
        if serId then
            local cfg = game_scheme:WeekendArenaSvrCfg_0(serId)
            local weekendIsOpen = weekend_arena_mgr.IsOpen()
            self.weekendArena_tip.gameObject:SetActive(weekendIsOpen)
            if cfg then
                self.weekendArena_desc.text = string.format(lang.Get(8801), cfg.PullRoleNum)
            end
            self.Auto_Unit5.gameObject:SetActive(true)
        else
            self.Auto_Unit5.gameObject:SetActive(isAutoAttend)
        end
        local weekendReddot = weekend_arena_mgr.HadReddot()
        self.weekendArena_reddot.gameObject:SetActive(weekendReddot)
        if isInitWeekendArenaData then
            -- 解锁了时空擂台
            -- 发送请求  界面设置在回调处理
            self.Text_CountDown5.text = lang.Get(8802)
            local weekend_arena_mgr = require "weekend_arena_mgr"
            local weekendIsOpen = weekend_arena_mgr.IsOpen()
            -- weekend_arena_mgr.MSG_WEEKEND_ARENA_RANK_INFO_REQ() -- 更改为登陆请求一次 界面请求一次  入口不再请求
            if serId then
                weekend_arena_mgr.MSG_WEEKEND_ARENA_NO1_REQ()
            else
                if isAutoAttend then
                    weekend_arena_mgr.MSG_WEEKEND_ARENA_NO1_REQ()
                end
            end
            self.isInitWeekendArenaData = true

            if weekendIsOpen then
                local endTime = weekend_arena_mgr.GetEndTime()
                if endTime then
                    local curTime = os.server_time()
                    local diff = endTime - curTime
                    local str_diff = ui_utils.TimeCountdown(diff)
                    self.Text_CountDown5.text = string.format(lang.Get(7104) .. '%s', str_diff)
                end
            end

            isInitWeekendArenaData = true
        end

        local infoData = weekend_arena_mgr.GetRankInfoByIndex(1)-- 获得第一名数据
        if infoData and infoData.baseinfo and infoData.baseinfo.roleID ~= 0 then
            if not util.IsObjNull(self.weekendArena_championIconRoot) then
                self.weekendArenaFaceItem = self.weekendArenaFaceItem or face_item.CFaceItem():Init(self.weekendArena_championIconRoot, nil, 1)
                self.weekendArenaFaceItem:SetNewBg(true)
                self.weekendArenaFaceItem:SetFaceInfo(infoData.baseinfo.faceID, self.OnBtn_Unit5Clicked)
                self.weekendArenaFaceItem:SetActorLvText(true, infoData.baseinfo.level)
                self.weekendArenaFaceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
                self.weekendArenaFaceItem:SetFrameID(infoData.baseinfo.frameID, true)
                if self.weekendArena_champion.gameObject.activeSelf == false then
                    self.weekendArena_champion.gameObject:SetActive(true)
                end
                self.weekendArena_championName.text = infoData.baseinfo.name
            end
        end
    end
end

function UIMatchplaceEntrance:UpdatePeakGameEntrance()
    local status = peakGame_data.GetPlayerAreaStatus()

    local isOpen = status ~= peakGame_data.TopRaceStatus.WMTopRaceStatus_UnOpen
    self.Auto_Unit6.gameObject:SetActive(isOpen)

    local isProgress = status >= peakGame_data.TopRaceStatus.WMTopRaceStatus_Start and status < peakGame_data.TopRaceStatus.WMTopRaceStatus_1End
    self.exChampionship_tip.gameObject:SetActive(isProgress)

    self.exChampionship_championIconRoot.gameObject:SetActive(false)
    if isOpen then
        local champion = peakGame_data.GetLastChampion()
        self.exChampionship_champion.gameObject:SetActive(champion ~= nil)
        if champion then
            self.exChampionship_championIconRoot.gameObject:SetActive(true)
            self.peakGameFaceItem = self.peakGameFaceItem or face_item.CFaceItem():Init(self.exChampionship_championIconRoot, nil, 1)
            self.peakGameFaceItem:SetNewBg(true)
            self.peakGameFaceItem:SetFaceInfo(champion.faceID, function()
                -- 进入界面
                --ui_window_mgr:ShowModule("ui_match_peak_base")
                -- local net_peakGame_module = require "net_peakGame_module"
                -- net_peakGame_module.Send_TMSG_WMTOPRACE_TOTAL_REQ(peakGame_data.GetMyAreaID() or 1)
            end)
            self.peakGameFaceItem:SetActorLvText(true, champion.level)
            self.peakGameFaceItem:FrameEffectEnable(true, window.curOrder + 1, 1)
            self.peakGameFaceItem:SetFrameID(champion.frameID, true)

            self.exChampionship_championName.text = champion.name
            self.exChampionship_unionName.text = champion.name

        end

        local myAreaID = peakGame_data.GetMyAreaID()
        if myAreaID then
            local peakCfg = game_scheme:ExChampionshipSvrCfg_0(myAreaID)
            self.exChampionship_desc.gameObject:SetActive(peakCfg ~= nil)
            if peakCfg then
                self.exChampionship_desc.text = string.format(lang.Get(280006), peakCfg.iselectNum)
            end
        end

        local isShowReddot = peakGame_data.GetPeakGameReddot()
        self.exChampionship_reddot.gameObject:SetActive(isShowReddot)
    else
        local hadRecivedNtfData = peakGame_data.GetRecivedNtf()
        if not hadRecivedNtfData then
            -- 丢包，补发数据
            peakGame_data.SetRecivedNtf(true)
            -- local net_peakGame_module = require "net_peakGame_module"
            -- net_peakGame_module.MSG_WMTOPRACE_BRIEF_REQ()
        end
    end
end

function UIMatchplaceEntrance:InitLegend()
    local laymain_data = require "laymain_data"
    local passLv = laymain_data.GetPassLevel()
    local cfg = nil
    --展示已解锁的最高级的组的奖励，不显示体验组
    for i = 0, game_scheme:LegendsTournament_nums() - 2 do
        local _cfg = game_scheme:LegendsTournament(i)
        if _cfg and passLv >= _cfg.iUnlockStage then
            if not cfg or _cfg.iUnlockStage >= cfg.iUnlockStage then
                cfg = _cfg
            end
        end
    end
    local legend_championships_mgr = require "legend_championships_mgr"
    cfg = cfg or legend_championships_mgr.GetLegendConfig(1)
    local exhibitRewardData = {}
    local reward_mgr = require "reward_mgr"
    local rewardID1 = cfg.iExhibitReward.data[0]
    if rewardID1 then
        exhibitRewardData[1] = reward_mgr.GetRewardGoods(rewardID1)
    end
    local rewardID2 = cfg.iExhibitReward.data[1]
    if rewardID2 then
        exhibitRewardData[2] = reward_mgr.GetRewardGoods(rewardID2)
    end
    self.legendRewardIcon = self.legendRewardIcon or {}
    for k, v in ipairs(exhibitRewardData) do
        local goods_item = require "goods_item_new"
        local iconUI = self.legendRewardIcon[k] or goods_item.CGoodsItem():Init(self["rewardIconRoot" .. k], function(p)
            if not p then
                return
            end
            p:DisplayInfo()
        end, 0.5)
        self.legendRewardIcon[k] = iconUI

        iconUI:SetGoods(nil, v.id, v.num, function()
            self:OnBtn_Unit2Clicked()
        end)
    end
    --阿拉伯本地化:日常竞技场本服超框
    if Lang.USE_LANG == Lang.AR then
        local Title_Name1_TMP = self.Title_Name1:Find("Text"):GetComponent(typeof(TextMeshProUGUI))
        Title_Name1_TMP.enableAutoSizing = true
        Title_Name1_TMP.fontSizeMin = 6
        Title_Name1_TMP.fontSizeMax = 10

        local Title_Name2_TMP = self.Title_Name2:Find("Text"):GetComponent(typeof(TextMeshProUGUI))
        Title_Name2_TMP.enableAutoSizing = true
        Title_Name2_TMP.fontSizeMin = 6
        Title_Name2_TMP.fontSizeMax = 10

        local Title_Name3_TMP = self.Title_Name3:Find("Text"):GetComponent(typeof(TextMeshProUGUI))
        Title_Name3_TMP.enableAutoSizing = true
        Title_Name3_TMP.fontSizeMin = 6
        Title_Name3_TMP.fontSizeMax = 10

        local Title_Name4_TMP = self.Title_Name4:Find("Text"):GetComponent(typeof(TextMeshProUGUI))
        Title_Name4_TMP.enableAutoSizing = true
        Title_Name4_TMP.fontSizeMin = 6
        Title_Name4_TMP.fontSizeMax = 10

        local Title_Name5_TMP = self.Title_Name5:Find("Text"):GetComponent(typeof(TextMeshProUGUI))
        Title_Name5_TMP.enableAutoSizing = true
        Title_Name5_TMP.fontSizeMin = 6
        Title_Name5_TMP.fontSizeMax = 10
    end

    self:UpdateLegend()
end

function UIMatchplaceEntrance:UpdateLegend()
    local legend_championships_mgr = require "legend_championships_mgr"
    local lcRank = legend_championships_mgr.GetSelfInfo()
    if lcRank then
        self.Text_Unit2_Power.text = lcRank.totalCE
        if lcRank.rank > 0 then
            self.Text_Unit2_Rank.text = lcRank.rank
        else
            self.Text_Unit2_Rank.text = "--"
        end
    else
        self.Text_Unit2_Rank.text = "--"
        self.Text_Unit2_Power.text = "--"
    end

    -- self.Auto_Unit2:SetActive(legend_championships_mgr.IsOpening() and not ReviewingUtil.IsReviewing())
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIMatchplaceEntrance:SubscribeEvents()
    self.OnBtn_MallBtnClickedProxy = function()
        self:OnBtn_MallBtnClicked()
    end

    self.OnBtn_Unit1ClickedProxy = function()
        self:OnBtn_Unit1Clicked()
    end

    self.OnBtn_Unit2ClickedProxy = function()
        self:OnBtn_Unit2Clicked()
    end

    self.OnBtn_HistoryBtn2ClickedProxy = function()
        self:OnBtn_HistoryBtn2Clicked()
    end

    self.OnBtn_Unit3ClickedProxy = function()
        self:OnBtn_Unit3Clicked()
    end

    self.OnBtn_Unit4ClickedProxy = function()
        self:OnBtn_Unit4Clicked()
    end

    self.OnBtn_Unit5ClickedProxy = function()
        self:OnBtn_Unit5Clicked()
    end

    self.OnBtn_Unit6ClickedProxy = function()
        self:OnBtn_Unit6Clicked()
    end

    self.OnBtn_HistoryBtn3ClickedProxy = function()
        self:OnBtn_HistoryBtn3Clicked()
    end

    self.OnBtn_CloseBtnClickedProxy = function()
        self:OnBtn_CloseBtnClicked()
    end

    ----///<<< Button Proxy Line >>>///-----

    if self.Btn_MallBtn then
        self.Btn_MallBtn.onClick:AddListener(self.OnBtn_MallBtnClickedProxy)
    end

    if self.Btn_Unit1 then
        self.Btn_Unit1.onClick:AddListener(self.OnBtn_Unit1ClickedProxy)
    end

    if self.Btn_Unit2 then
        self.Btn_Unit2.onClick:AddListener(self.OnBtn_Unit2ClickedProxy)
    end

    if self.Btn_HistoryBtn2 then
        self.Btn_HistoryBtn2.onClick:AddIntervalListener(self.OnBtn_HistoryBtn2ClickedProxy)
    end

    if self.Btn_Unit3 then
        self.Btn_Unit3.onClick:AddListener(self.OnBtn_Unit3ClickedProxy)
    end

    if self.Btn_Unit4 then
        self.Btn_Unit4.onClick:AddListener(self.OnBtn_Unit4ClickedProxy)
    end

    if self.Btn_Unit5 then
        self.Btn_Unit5.onClick:AddListener(self.OnBtn_Unit5ClickedProxy)
    end

    if self.Btn_HistoryBtn3 then
        self.Btn_HistoryBtn3.onClick:AddIntervalListener(self.OnBtn_HistoryBtn3ClickedProxy)
    end

    if self.Btn_CloseBtn then
        self.Btn_CloseBtn.onClick:AddListener(self.OnBtn_CloseBtnClickedProxy)
    end

    self.OnSeasonStart = function(_, type)
        if type == common_new_pb.CrystalCrown then
            net_arena_module.MineDefence[1] = {}
        end
    end
    event.Register(event.ARENA_SEASON_START, self.OnSeasonStart)

    self.OnUpdateWeekendInfo = function()
        if window and window:IsValid() then
            window:UpdateWeekendArenaEntrance()
        end
    end
    event.Register(event.UPDATE_WEEKEND_ARENA_INFO, self.OnUpdateWeekendInfo)

    self.OnARENA_RANKINGLIST_RECV = function(eName, rankType, list)
        if rankType == common_new_pb.Advance then
            local topRankData = nil
            for i, v in ipairs(list) do
                if v.ranking == 1 then
                    topRankData = v
                    break
                end
            end
            if topRankData and topRankData.playerInfo[1] then
                local infoData = topRankData.playerInfo[1]
                if not util.IsObjNull(self.Rtsf_topIconRoot) then
                    self.faceItem = self.faceItem or face_item.CFaceItem():Init(self.Rtsf_topIconRoot, nil, 1)
                    self.faceItem:SetNewBg(true)
                    self.faceItem:SetFaceInfo(infoData.faceID, self.OnBtn_Unit2ClickedProxy)
                    self.faceItem:SetActorLvText(true, infoData.level)
                    self.faceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
                    self.faceItem:SetFrameID(game_scheme:InitBattleProp_0(357).szParam.data[0], true)
                    if self.advance_unlock and self.Rtsf_top.gameObject.activeSelf == false then
                        self.Rtsf_top.gameObject:SetActive(true)
                    end
                    self.Text_name.text = infoData.name
                end
            end
        end
    end

    self.OnUPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI = function(eName, selfRankInfo, firstRankInfo)
        if firstRankInfo then
            local infoData = firstRankInfo
            if not util.IsObjNull(self.Rtsf_topIconRoot) then
                self.faceItem = self.faceItem or face_item.CFaceItem():Init(self.Rtsf_topIconRoot, nil, 1)
                self.faceItem:SetNewBg(true)
                self.faceItem:SetFaceInfo(firstRankInfo.faceID, self.OnBtn_Unit2ClickedProxy)
                self.faceItem:SetActorLvText(true, firstRankInfo.level)
                self.faceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
                self.faceItem:SetFrameID(firstRankInfo.frameID, true)
                self.Rtsf_top:SetActive(true)
                self.Text_name.text = firstRankInfo.name
            end
        end
    end
    self:RegisterEvent(event.UPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI, self.OnUPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI)
    -- self.OnTimeTicker = function ( )
    --     if self:IsValid() then
    --         self:UpdateTime()
    --     end
    -- end

    -- event.Register(event.ACTIVITY_PER_SECOND, self.OnTimeTicker)
    --event.Register(event.ARENA_RANKINGLIST_RECV, self.OnARENA_RANKINGLIST_RECV)

    self.OnTOPRACE_GETTOPLIST = function(eName, data)
        if self:IsValid() and data and data.ranktype == 1 and data.areaid == topGame_data.getAreaID() then
            if data.rolerank and #data.rolerank > 0 then
                local infoData = data.rolerank[1]
                -- print(infoData)
                if topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame then
                    if not util.IsObjNull(self.topgame_championIconRoot) then
                        self.championFaceItem = self.championFaceItem or face_item.CFaceItem():Init(self.topgame_championIconRoot, nil, 1)
                        self.championFaceItem:SetNewBg(true)
                        self.championFaceItem:SetFaceInfo(infoData.baseinfo.faceID, self.OnBtn_Unit3ClickedProxy)
                        self.championFaceItem:SetActorLvText(true, infoData.baseinfo.roleLv)
                        self.championFaceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
                        self.championFaceItem:SetFrameID(931, true)
                        if self.topgame_champion.gameObject.activeSelf == false then
                            self.topgame_champion.gameObject:SetActive(true)
                        end
                        self.topgame_championName.text = infoData.baseinfo.name
                    end
                else
                    if not util.IsObjNull(self.newtopgame_championIconRoot) then
                        self.newchampionFaceItem = self.newchampionFaceItem or face_item.CFaceItem():Init(self.newtopgame_championIconRoot, nil, 1)
                        self.newchampionFaceItem:SetNewBg(true)
                        self.newchampionFaceItem:SetFaceInfo(infoData.baseinfo.faceID, self.OnBtn_Unit4ClickedProxy)
                        self.newchampionFaceItem:SetActorLvText(true, infoData.baseinfo.roleLv)
                        self.newchampionFaceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
                        self.newchampionFaceItem:SetFrameID(931, true, 1)
                        if self.newtopgame_champion.gameObject.activeSelf == false then
                            self.newtopgame_champion.gameObject:SetActive(true)
                        end
                        self.newtopgame_championName.text = infoData.baseinfo.name
                    end
                end
            end
        end
    end
    event.Register(event.TOPRACE_GETTOPLIST, self.OnTOPRACE_GETTOPLIST)

    self.OnWeekendArenaTopPlayerInfoUpdate = function(eName, data)
        local ui_pop_mgr = require "ui_pop_mgr"
        local isOpen = ui_pop_mgr.CheckIsOpen(941, false)
        if self:IsValid() and data and isOpen then
            local infoData = data
            if data and data.baseinfo and infoData.baseinfo.roleID ~= 0 then
                if not util.IsObjNull(self.weekendArena_championIconRoot) then
                    self.weekendArenaFaceItem = self.weekendArenaFaceItem or face_item.CFaceItem():Init(self.weekendArena_championIconRoot, nil, 1)
                    self.weekendArenaFaceItem:SetNewBg(true)
                    self.weekendArenaFaceItem:SetFaceInfo(infoData.baseinfo.faceID, self.OnBtn_Unit5Clicked)
                    self.weekendArenaFaceItem:SetActorLvText(true, infoData.baseinfo.level)
                    self.weekendArenaFaceItem:SetFrameID(infoData.baseinfo.frameID, true)
                    if self.weekendArena_champion.gameObject.activeSelf == false then
                        self.weekendArena_champion.gameObject:SetActive(true)
                    end
                    self.weekendArena_championName.text = infoData.baseinfo.name
                end
            end
        end
    end
    event.Register(event.WEEKEND_ARENA_TOP_PLAYER_INFO, self.OnWeekendArenaTopPlayerInfoUpdate)

    self.OnUPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI = function()
        self:UpdateLegend()
    end
    self:RegisterEvent(event.UPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI, self.OnUPDATE_LEGEND_CHAMPIONSHIPS_RANK_LIST_UI)

    self.OnLEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE = function()
        self:UpdateLegend()
    end
    self:RegisterEvent(event.LEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE, self.OnLEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE)

    self.OnRecvArenaRank = function(_, arenaType)
        if not self:IsValid() then
            return
        end
        local list = net_arena_module.RankingList[arenaType]
        if not list then
            return
        end
        local firstRole = list[1]
        if not firstRole then
            return
        end

        if not util.IsObjNull(self.unit1_championIconRoot) then
            self.unit1championFaceItem = self.unit1championFaceItem or face_item.CFaceItem():Init(self.unit1_championIconRoot, nil, 1)
            self.unit1championFaceItem:SetNewBg(true)
            local face_data = require "actor_face_data"

            local frameID = firstRole.playerInfo[1].roleID == player_mgr.GetPlayerRoleID() and face_data.GetRoleFrameID() or firstRole.playerInfo[1].frameID
            local faceID = firstRole.playerInfo[1].roleID == player_mgr.GetPlayerRoleID() and face_data.GetRoleFaceID() or firstRole.playerInfo[1].faceID

            self.unit1championFaceItem:SetFaceInfo(faceID, self.OnBtn_Unit1ClickedProxy)
            self.unit1championFaceItem:SetActorLvText(true, firstRole.playerInfo[1].level)
            self.unit1championFaceItem:FrameEffectEnable(true, self.curOrder + 1, 1)
            self.unit1championFaceItem:SetFrameID(frameID, true)
            if self.unit1_champion.gameObject.activeSelf == false then
                self.unit1_champion.gameObject:SetActive(true)
            end
            self.unit1_championName.text = firstRole.name
        end
    end
    event.Register(event.ARENA_READY_FOR_BATTLE, self.OnRecvArenaRank)

    self.OnClickUnit3RewardBtn = function()
        ui_window_mgr:ShowModule("ui_top_game_rank")
    end
    if self.Auto_Unit3_Reward then
        self.Auto_Unit3_Reward.onClick:AddListener(self.OnClickUnit3RewardBtn)
    end

    self.OnClickUnit4RewardBtn = function()
        ui_window_mgr:ShowModule("ui_top_game_rank")
    end
    if self.Auto_Unit4_Reward then
        self.Auto_Unit4_Reward.onClick:AddListener(self.OnClickUnit4RewardBtn)
    end

    self.UpdateWeekendArenaInfo = function()
        if self and self:IsValid() then
            -- 时空擂台设置(回调)
            self:UpdateWeekendArenaEntrance()
        end
    end
    event.Register(event.UPDATE_WEEKEND_ARENA_ALL_RANKINFO, self.UpdateWeekendArenaInfo)

    self.OnClickUnit6RewardBtn = function()
        ui_window_mgr:ShowModule("ui_peak_game_rank")
    end

    self.OnUpdatePeakgameReward = function()
        self.Auto_Unit6_Reward.gameObject:SetActive(false)
    end
    self:RegisterEvent(event.RECEIVE_PEAKGAME_RANK_REWARD, self.OnUpdatePeakgameReward)

    self.OnMakeUpPeakGameNtfData = function()
        if window and window:IsValid() then
            window:UpdatePeakGameEntrance()
        end
    end
    event.Register(event.PEAKGAME_MAKE_UP_NTF_DATA, self.OnMakeUpPeakGameNtfData)
end



--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIMatchplaceEntrance:UnsubscribeEvents()
    -- event.Unregister(event.ACTIVITY_PER_SECOND, self.OnTimeTicker)
    event.Unregister(event.ARENA_SEASON_START, self.OnSeasonStart)
    event.Unregister(event.UPDATE_WEEKEND_ARENA_INFO, self.OnUpdateWeekendInfo)
    event.Unregister(event.ARENA_RANKINGLIST_RECV, self.OnARENA_RANKINGLIST_RECV)
    event.Unregister(event.TOPRACE_GETTOPLIST, self.OnTOPRACE_GETTOPLIST)
    event.Unregister(event.WEEKEND_ARENA_TOP_PLAYER_INFO, self.OnWeekendArenaTopPlayerInfoUpdate)
    event.Unregister(event.ARENA_READY_FOR_BATTLE, self.OnRecvArenaRank)
    event.Unregister(event.UPDATE_WEEKEND_ARENA_ALL_RANKINFO, self.UpdateWeekendArenaInfo)
    if self.Btn_MallBtn then
        self.Btn_MallBtn.onClick:RemoveListener(self.OnBtn_MallBtnClickedProxy)
    end

    if self.Btn_Unit1 then
        self.Btn_Unit1.onClick:RemoveListener(self.OnBtn_Unit1ClickedProxy)
    end

    if self.Btn_Unit2 then
        self.Btn_Unit2.onClick:RemoveListener(self.OnBtn_Unit2ClickedProxy)
    end

    if self.Btn_HistoryBtn2 then
        self.Btn_HistoryBtn2.onClick:RemoveIntervalListener(self.OnBtn_HistoryBtn2ClickedProxy)
    end

    if self.Btn_Unit3 then
        self.Btn_Unit3.onClick:RemoveListener(self.OnBtn_Unit3ClickedProxy)
    end

    if self.Btn_Unit4 then
        self.Btn_Unit4.onClick:RemoveListener(self.OnBtn_Unit4ClickedProxy)
    end

    if self.Btn_Unit5 then
        self.Btn_Unit5.onClick:RemoveListener(self.OnBtn_Unit5ClickedProxy)
    end

    if self.Btn_HistoryBtn3 then
        self.Btn_HistoryBtn3.onClick:RemoveIntervalListener(self.OnBtn_HistoryBtn3ClickedProxy)
    end

    if self.Btn_CloseBtn then
        self.Btn_CloseBtn.onClick:RemoveListener(self.OnBtn_CloseBtnClickedProxy)
    end

    if self.Auto_Unit3_Reward then
        self.Auto_Unit3_Reward.onClick:RemoveListener(self.OnClickUnit3RewardBtn)
    end

    if self.Auto_Unit4_Reward then
        self.Auto_Unit4_Reward.onClick:RemoveListener(self.OnClickUnit4RewardBtn)
    end

    event.Unregister(event.CREATE_SPECIALGOODS_ENTITY, self.createGoodsEvent)
    if goodsEntity and self.goodsNumChange then
        goodsEntity.numProp:RemoveListener("goodsNum", self.goodsNumChange)
        self.goodsNumChange = nil
        goodsEntity = nil
    end

    --@endregion
end



--@region WindowBtnFunctions

function UIMatchplaceEntrance:RefreshGoodsNum()
    -- 刷新物品数量显示

    local goodsSid = skep_mgr.GetConstGoodsSidByID(ArenaCoin)

    self.goodsNumChange = function(num)
        self.Text_CoinCount.text = util.PriceConvert(num)
    end
    if goodsSid then
        local gEntity = player_mgr.GetPacketPartDataBySid(goodsSid)
        if gEntity then
            goodsEntity = gEntity
            goodsEntity.numProp:AddListener("goodsNum", self.goodsNumChange)
        end
    end

    self.goodsNumChange((goodsEntity or { numProp = { goodsNum = 0 } }).numProp.goodsNum)
end

function UIMatchplaceEntrance:UpdateReddot()
    self.Auto_Unit1_Reddot.gameObject:SetActive(arena_data.GetReddot(common_new_pb.CrystalCrown))
    local legend_championships_mgr = require "legend_championships_mgr"
    if not legend_championships_mgr.GetChallengeTips() then
        self.surplusTime = legend_championships_mgr.GetShowBtnStartTime()
        local net_login_module = require 'net_login_module'
        local serverTime = net_login_module.GetServerTime()
        self.isRedVisible = (self.surplusTime - serverTime) <= 0
        if self.ticker then
            util.RemoveDelayCall(self.ticker)
            self.ticker = nil
        end
        window.ticker = util.IntervalCall(1, function()
            if window and window:IsValid() then
                local serverTime = net_login_module.GetServerTime()
                if serverTime and window.surplusTime then
                    local diffTime = window.surplusTime - serverTime
                    if diffTime > 0 then
                        window.isRedVisible = false
                        self.Auto_Unit2_Reddot.gameObject:SetActive(false)
                    else
                        window.isRedVisible = true
                        self.Auto_Unit2_Reddot.gameObject:SetActive(legend_championships_mgr.GetRewardTips())
                        if self.ticker then
                            util.RemoveDelayCall(self.ticker)
                            self.ticker = nil
                        end
                    end
                end
            end
        end)
        self.Auto_Unit2_Reddot.gameObject:SetActive(self.isRedVisible and legend_championships_mgr.GetRewardTips())
    else
        self.Auto_Unit2_Reddot.gameObject:SetActive(legend_championships_mgr.GetEntranceTips())
    end
end

function UIMatchplaceEntrance:UpdateTopGameReddot()
    self.Btn_Unit3_gameTip_Reddot:SetActive(topGame_data.IsShowReddot() and topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame)
    self.Btn_Unit4_gameTip_Reddot:SetActive(topGame_data.IsShowReddot() and topGame_data.getTopgameType() == topGame_data.TopGameType.NewAera)
end

function UIMatchplaceEntrance:UpdateTopGameRewardReddot()
    self.Auto_Unit3_RewardRed.gameObject:SetActive(not topGame_data.isGetReward and topGame_data.rank > 0)
    self.Auto_Unit4_RewardRed.gameObject:SetActive(not topGame_data.isGetReward and topGame_data.rank > 0)
end

function UIMatchplaceEntrance:OnBtn_MallBtnClicked()
    local ui_market_new = require "ui_market_new"
    local shop_pb = require "shop_pb"
    ui_market_new.SetShopData(shop_pb.enArena, 1, function()
    end)
    --ui_window_mgr:ShowModule("ui_bag_bg")
    ui_window_mgr:ShowModule("ui_market_new")
end

function UIMatchplaceEntrance:OnBtn_Unit1Clicked()
    if not self.canClick1 then
        return
    end
    local ui_pop_mgr = require "ui_pop_mgr"
    local isOpen = ui_pop_mgr.CheckIsOpen(197)
    if isOpen then
        -- net_arena_module.InitalQueryDefence = true
        local unforced_guide_mgr = require "unforced_guide_mgr"
        if unforced_guide_mgr.IsGuiding(4, 71) then
            event.Trigger(event.CLICK_ARENA_BATTLE)
            if ui_window_mgr:IsModuleShown("ui_pointing_target") then
                ui_window_mgr:UnloadModuleImmediate("ui_pointing_target")
            end
        end
        net_arena_module.Send_ARENA_GET_TIME()
        -- if not net_arena_module.isFirstInSingleRank then
        if net_arena_module.GetArenaScores(common_new_pb.CrystalCrown) ~= 0 then
            --没有数据的时候无法进入竞技场
            local scene_mgr = require "scene_mgr"
            local BanClick = scene_mgr.BanClick
            BanClick(true, "ui_match_single_rank")
            ui_window_mgr:ShowModule("ui_match_single_rank", function()
                -- ui_window_mgr:UnloadModule("ui_lobby")
                local menu_bot_data = require "menu_bot_data"
                menu_bot_data.CloseAllPage()
                BanClick(false)
            end)
            ui_window_mgr:UnloadModule("ui_matchplace_entrance")
        end
        -- else
        -- if not net_arena_module.InitNormalRankInfo then
        --     net_arena_module.InitNormalRankInfo = false
        --     net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)
        -- end
        -- end

        --新手强制引导事件
        local force_guide_system = require "force_guide_system"
        local force_guide_event = require "force_guide_event"
        force_guide_system.TriComEvent(force_guide_event.cEventClickArenaDaily)
    end
end

function UIMatchplaceEntrance:OnBtn_Unit2Clicked()
    --传奇锦标赛入口
    -- if not self.canClick4 then return end
    -- local ui_pop_mgr = require "ui_pop_mgr"
    -- local isOpen = ui_pop_mgr.CheckIsOpen(266)
    -- if isOpen then
    -- 	net_arena_module.InitalQueryDefence = true
    --     event.Trigger(event.CLICK_ARENA_BATTLE)
    --     if ui_window_mgr:IsModuleShown("ui_pointing_target") then
    --         ui_window_mgr:UnloadModuleImmediate("ui_pointing_target")
    --     end
    --     net_arena_module.Send_ARENA_GET_TIME()
    --     net_arena_module.Recv_ARENA_DEFEND_LINEUP({arenaType = common_new_pb.Advance, lineupList = net_arena_module.MineDefence[common_new_pb.Advance]})
    -- end
    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.IsGuiding(56, 430) then
        event.Trigger(event.CLICK_LEGEND)
    end

    local legend_championships_mgr = require "legend_championships_mgr"
    if legend_championships_mgr.CanEnter(true) then
        if legend_championships_mgr.GetHaveGetLegendData() then
            local state = legend_championships_mgr.GetState()
            if state == 2 and legend_championships_mgr.GetApplyType() == 0 then
                state = 1
            end
            local win = ui_window_mgr:ShowModule("ui_legend_championships_base")
            win:SetInputParam(state, true)
            ui_window_mgr:UnloadModule("ui_matchplace_entrance")
            local menu_bot_data = require "menu_bot_data"
            menu_bot_data.CloseAllPage() --
        else
            flow_text.Add(lang.Get(8096))
            --先屏蔽重新获取信息的消息请求 先查为啥没有数据的问题
            -- local net_legend_championships_module = require "net_legend_championships_module"
            -- net_legend_championships_module.Request_LegendBasicInfo()
            --没有数据
        end
    end
end

function UIMatchplaceEntrance:OnBtn_HistoryBtn2Clicked()
    ui_window_mgr:ShowModule('ui_match_rank')
    net_arena_module.Send_ARENA_LAST_RANKING_LIST(common_new_pb.Champion)
end

function UIMatchplaceEntrance:OnBtn_Unit3Clicked()
    local json_str = {
        AreaChampionship_Stage = topGame_data.GetGameState(),
        type = topGame_data.getTopgameType()
    }
    event.Trigger(event.GAME_EVENT_REPORT, "AreaChampionship_enter", json_str)
    ui_window_mgr:ShowModule('ui_match_top_game')
end

function UIMatchplaceEntrance:OnBtn_Unit4Clicked()
    local json_str = {
        AreaChampionship_Stage = topGame_data.GetGameState(),
        type = topGame_data.getTopgameType()
    }
    event.Trigger(event.GAME_EVENT_REPORT, "AreaChampionship_enter", json_str)
    ui_window_mgr:ShowModule('ui_new_area_top_game')
    if topGame_data.gethasdata() then
        -- else
        --     flow_text.Add(lang.Get(21520))
    end
end

function UIMatchplaceEntrance:OnBtn_Unit5Clicked()
    local weekend_arena_mgr = require "weekend_arena_mgr"
    weekend_arena_mgr.RequestMyselfLineup()
    ui_window_mgr:ShowModule("ui_weekend_arena")
end

function UIMatchplaceEntrance:OnBtn_Unit6Clicked()
    -- local net_peakGame_module = require "net_peakGame_module"
    -- net_peakGame_module.Send_TMSG_WMTOPRACE_TOTAL_REQ(peakGame_data.GetMyAreaID() or 1)
    -- ui_window_mgr:ShowModule("ui_match_peak_base")
end

function UIMatchplaceEntrance:OnBtn_HistoryBtn3Clicked()
    ui_window_mgr:ShowModule('ui_match_rank')
    net_arena_module.Send_ARENA_LAST_RANKING_LIST(common_new_pb.Team)
end

function SetCloseCallBack(callback)
    closeCallBack = callback
end

function UIMatchplaceEntrance:OnBtn_CloseBtnClicked()
    if closeCallBack then
        closeCallBack()
        closeCallBack = nil
    else
        --2021.1.1 曾琳琳 提示：新增的切页限制是为了解决从别的模块跳转进来后退出时界面异常问题，如联盟活跃度任务跳转
        local laymain_mgr = require "laymain_mgr"
        local ui_lobby = require "ui_lobby"
        local new_scene_mgr = require "new_scene_mgr"
        new_scene_mgr.SetCurSceneIndex(new_scene_mgr.RISK_SCENE)--设置laymain_mgr.Show打开冒险切页时需要这个参数
        ui_lobby.SetPageIndex(laymain_mgr.RISK_SCENE)--设置laymain_mgr.Show打开的切页
        ui_window_mgr:ShowModule("ui_lobby")--打开uilobby时会执行laymain_mgr.Show操作
        -- local music_contorller = require "music_contorller"
        -- music_contorller.ChangeSceneMusic(music_contorller.ID_LOBBY)
    end
    local const = require "const"
    if const.USE_MAIN_SLG then
        local sand_ui_event_define = require "sand_ui_event_define"
        local main_slg_const = require "main_slg_const"
        event.Trigger(sand_ui_event_define.GW_EXIT_EXPERIENCE, main_slg_const.MainExperimentType.Arena_New)
    end
    ui_window_mgr:UnloadModule("ui_matchplace_entrance")
end



--@region WindowClose
function UIMatchplaceEntrance:Close()
    if self:IsValid() then
        self:UnsubscribeEvents()
    end

    local unforced_guide_mgr = require "unforced_guide_mgr"
    if unforced_guide_mgr.IsGuiding(4, 71) or unforced_guide_mgr.IsGuiding(56, 430) then
        ui_window_mgr:UnloadModule("ui_pointing_target")
    end

    if self.faceItem then
        self.faceItem:Dispose()
        self.faceItem = nil
    end
    if self.championFaceItem then
        self.championFaceItem:Dispose()
        self.championFaceItem = nil
    end
    if self.newchampionFaceItem then
        self.newchampionFaceItem:Dispose()
        self.newchampionFaceItem = nil
    end

    if self.unit1championFaceItem then
        self.unit1championFaceItem:Dispose()
        self.unit1championFaceItem = nil
    end

    if self.weekendArenaFaceItem then
        self.weekendArenaFaceItem:Dispose()
        self.weekendArenaFaceItem = nil
    end

    if self.peakGameFaceItem then
        self.peakGameFaceItem:Dispose()
        self.peakGameFaceItem = nil
    end

    if self.legendRewardIcon then
        for k, v in pairs(self.legendRewardIcon) do
            self.legendRewardIcon[k]:Dispose()
            self.legendRewardIcon[k] = nil
        end
        self.legendRewardIcon = nil
    end
    if self.ticker then
        util.RemoveDelayCall(self.ticker)
        self.ticker = nil
    end

    self.newSvrDduration = 0
    self.__base:Close()
    surplusTime = 0
    reqOnce = true
    window = nil
    closeCallBack = nil

    --@endregion
end



--@region ScrollItem



--@region WindowInherited
local CUIMatchplaceEntrance = class(ui_base, nil, UIMatchplaceEntrance)


--@region ModuleFunction
function Show()
    -- 请求时间
    --net_arena_module.Send_ARENA_GET_TIME()
    if window == nil then
        window = CUIMatchplaceEntrance()
        window._NAME = _NAME;
        window:LoadUIResource("ui/prefabs/uimatchplaceentrance.prefab", nil, --[[GameObject.Find("UIRoot/CanvasScreenEffect").transform]]nil, nil, nil, true)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        window:Close()
        window = nil
    end
end



--@region RegisterMsg
function RankInfoUpdate(_, arenaType, ranking, scores, totalCE)
    if window and window:IsValid() then
        if arenaType == common_new_pb.CrystalCrown then
            if totalCE ~= nil then
                if not window.Text_Unit1_Power:IsNull() then
                    -- local usedWeapon = weapon_data.GetLocalizedWeaponData(common_new_pb.Arena,0,arenaType)
                    -- local homeland_mgr = require "homeland_mgr"
                    -- local weaponPower = homeland_mgr.GetWeaponPowerByID(usedWeapon)
                    window.Text_Unit1_Power.text = totalCE
                end
            end
            if ranking ~= nil then
                if not window.Text_Unit1_Rank:IsNull() then
                    window.Text_Unit1_Rank.text = ranking
                end
            end
            if scores ~= nil then
                if not window.Auto_Unit1_Reddot:IsNull() then
                    window:UpdateReddot()
                end
            end
            -- elseif arenaType == common_new_pb.Advance then
            --     if totalCE ~= nil then
            --         if not window.Text_Unit2_Power:IsNull() then
            --             window.Text_Unit2_Power.text = totalCE
            --         end
            --     end
            --     if ranking ~= nil then
            --         if not window.Text_Unit2_Rank:IsNull() then
            --             window.Text_Unit2_Rank.text = ranking
            --         end
            --     end
            --     if scores ~= nil then
            --         if not window.Auto_Unit2_Reddot:IsNull() then
            --             window:UpdateReddot() 
            --         end
            --     end
        end
    end
end
event.Register(event.ARENA_READY_FOR_BATTLE, RankInfoUpdate)

function TimeUpdate(_, time1, time2, time3, time4)
    if window ~= nil then
        window.time1 = time1
        window.time2 = time2
        window.time3 = topGame_data.NextSurplusTime()
        window.time4 = time4
        window.time5 = topGame_data.NextSurplusTime()
        if ticker ~= nil then
            util.RemoveDelayCall(ticker)
            ticker = nil
        end
        ticker = util.IntervalCall(1, function(pass)
            if window == nil then
                -- 窗口关闭也去掉
                ticker = nil
                return true
            end

            if not window.UIRoot then
                return false
            end

            if window.time1 > 0 then
                window.canClick1 = window.time1 - pass > 0
                window.Text_CountDown1.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time1 - pass))
                if window.time1 - pass == 0 then
                    net_arena_module.Send_ARENA_GET_TIME()
                end
            else
                window.canClick1 = false
                -- window.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7105)..'%s</color>', '03ed68', ui_utils.TimeCountdown(-window.time4-pass))
                -- if -window.time1-pass == 0 then
                --     net_arena_module.Send_ARENA_GET_TIME()
                -- end
            end

            if window.time4 > 0 then
                window.canClick4 = window.time4 - pass > 0
                -- window.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7104)..'%s</color>', 'fff153', ui_utils.TimeCountdown(window.time4-pass))
                -- if window.time4-pass == 0 then
                --     net_arena_module.Send_ARENA_GET_TIME()
                -- end
            else
                window.canClick4 = false
                -- window.Text_CountDown2.text = string.format('<color=#%s>'..lang.Get(7105)..'%s</color>', '03ed68', ui_utils.TimeCountdown(window.time4-pass))
                -- if -window.time4-pass == 0 then
                --     net_arena_module.Send_ARENA_GET_TIME()
                -- end
            end
            if topGame_data.isShowTopGame then
                if topGame_data.getTopgameType() == topGame_data.TopGameType.TopGame then
                    if window.time3 - pass < 24 * 3600 * 7 then
                        -- print(window.time3)
                        window.Text_CountDown3.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time3 - pass))
                        if window.time3 - pass < 0 and not topGame_data.GetGameOver() then
                            topGame_data.SetGameOver(true)
                        end
                        if window.Btn_Unit3_gameTip.gameObject.activeSelf ~= true then
                            window.Btn_Unit3_gameTip.gameObject:SetActive(true)
                        end
                    else
                        --  --print("1:",window.time3)
                        window.Text_CountDown3.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(window.time3 - (24 * 3600 * 7) - pass))
                        if window.Btn_Unit3_gameTip.gameObject.activeSelf ~= false then
                            window.Btn_Unit3_gameTip.gameObject:SetActive(false)
                        end
                        if window.Btn_Unit3_gameTip_Reddot.gameObject.activeSelf ~= false then
                            window.Btn_Unit3_gameTip_Reddot.gameObject:SetActive(false)
                        end
                    end
                    --log.Warning("window.time3",window.time3,"pass",pass,"window.Text_CountDown3.text",window.Text_CountDown3.text)
                else
                    -- --print("window.time5 - pass:",window.time5 - pass,"window.newSvrDduration:",window.newSvrDduration)
                    if window.time5 - pass <= 10 and window.time5 - pass >= -50 then
                        --开服可能有30秒的误差，倒计时结束可能不会立即开启巅峰赛
                        window.time5 = topGame_data.NextSurplusTime()
                        window:setNewSvrDurationTime()
                    end
                    if window.isshowCountDown then
                        if window.time5 - pass > window.newSvrDduration then
                            -- print(window.time5)
                            window.Text_CountDown4.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time5 - (window.newSvrDduration) - pass))
                            if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= true then
                                window.Btn_Unit4_gameTip.gameObject:SetActive(true)
                            end
                        else
                            --  --print("1:",window.time5)
                            window.Text_CountDown4.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(window.time5 - pass))
                            if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= false then
                                window.Btn_Unit4_gameTip.gameObject:SetActive(false)
                            end
                            if window.Btn_Unit4_gameTip_Reddot.gameObject.activeSelf ~= false then
                                window.Btn_Unit4_gameTip_Reddot.gameObject:SetActive(false)
                            end
                        end
                    else
                        if window.time5 - pass > 0 then
                            window.Text_CountDown4.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(window.time5 - pass))
                            if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= true then
                                window.Btn_Unit4_gameTip.gameObject:SetActive(true)
                            end
                        else
                            window.Text_CountDown4.text = ""
                            if window.Btn_Unit4_gameTip.gameObject.activeSelf ~= false then
                                window.Btn_Unit4_gameTip.gameObject:SetActive(false)
                            end
                            if window.Btn_Unit4_gameTip_Reddot.gameObject.activeSelf ~= false then
                                window.Btn_Unit4_gameTip_Reddot.gameObject:SetActive(false)
                            end
                        end
                    end
                end
            end

            local peakGameIsOpen = peakGame_data.IsOpen()
            if peakGameIsOpen then
                local curTime = os.server_time()
                local overTime = peakGame_data.GetOverTime()
                local diff = overTime - curTime
                if diff > 0 then
                    window.Text_CountDown6.text = string.format(lang.Get(7104) .. '%s', ui_utils.TimeCountdown(diff))
                else
                    local startTime = peakGame_data.GetStartTime()
                    diff = startTime - curTime
                    window.Text_CountDown6.text = string.format(lang.Get(7105) .. '%s', ui_utils.TimeCountdown(diff))
                end
            end
        end)
    end
end
event.Register(event.ARENA_TIME_UPDATE, TimeUpdate)

function UIMatchplaceEntrance:setNewSvrDurationTime()
    self.newSvrDduration = 0
    self.isshowCountDown = false
    local setting_server_data = require "setting_server_data"
    local worldId = tonumber(setting_server_data.GetLoginWorldID())
    local cfg = game_scheme:NewSvrAreaChampionshipSvrCfg_0(worldId)
    local opentime = topGame_data.getOpentime()
    local timedata = 0
    if cfg then
        local time = cfg.firstOpenTime.data
        timedata = os.time({ year = time[0], month = time[1], day = time[2], hour = time[3], min = time[4], sec = time[5] }) + util.GetTimeArea() * 3600
    end
    local net_login_module = require "net_login_module"
    local serverTime = net_login_module.GetServerTime()
    --print("opentime:",opentime,"serverTime:",serverTime)
    if cfg and cfg.openType == 1 then
        if opentime <= timedata then
            self.isshowCountDown = true
            self.newSvrDduration = cfg.timeInv * 24 * 3600
        else
            self.newSvrDduration = 7 * 24 * 3600
        end
    else
        self.newSvrDduration = 7 * 24 * 3600
        local day = game_scheme:InitBattleProp_0(833).szParam.data[0]
        if player_mgr.GetRoleOpenSvrTime() + (day - 1) * 24 * 3600 == opentime and serverTime < opentime then
            self.isshowCountDown = true
        end
    end
    --print("self.newSvrDduration:",self.newSvrDduration,"self.isshowCountDown:",self.isshowCountDown)
end

-- 阵容设置成功
function ArenaReady(_, arenaType)
    --if window == nil then return end
    local wnds = { "ui_match_single_rank", "ui_single_match", "ui_matchplace_team", "ui_match_senior_single_rank", "ui_match_senior_single_rank" }
    if ui_window_mgr:IsModuleShown(wnds[arenaType]) then
        return
    end
    if arenaType == common_new_pb.Team and ui_window_mgr:IsModuleShown("ui_multi_match") then
        return
    end
    if arenaType == common_new_pb.Team then
        -- 请求我的队伍信息。根据自己组队情况
        net_arena_module.Send_ARENA_GET_TEAMINFO()
        return
    end
    net_arena_module.Send_ARENA_ENTER(arenaType)
end
event.Register(event.ARENA_LINEUP_SUCCESS, ArenaReady)

--防守阵容返回
function RecvDefendLineup(_, arenaType, mineLineup)
    if window and window:IsValid() then
        if reqOnce then
            if net_arena_module.MineDefence[common_new_pb.CrystalCrown] ~= nil and table.getn(net_arena_module.MineDefence[common_new_pb.CrystalCrown]) > 0 then
                if not net_arena_module.InitNormalRankInfo then
                    net_arena_module.InitNormalRankInfo = true
                    net_arena_module.Send_ARENA_ENTER(common_new_pb.CrystalCrown)
                end
            end
            -- if net_arena_module.MineDefence[common_new_pb.Advance] ~= nil and table.getn(net_arena_module.MineDefence[common_new_pb.Advance]) > 0 then
            --     if not net_arena_module.getInitSeniorRankInfo() then
            --         net_arena_module.setInitSeniorRankInfo(true)
            --         net_arena_module.Send_ARENA_ENTER(common_new_pb.Advance)
            --     end
            -- end
            reqOnce = false
        end
    end

    --只请求防守阵容
    if not net_arena_module.InitalQueryDefence then
        return
    end
    net_arena_module.InitalQueryDefence = false

    -- 有没有防守阵容
    if mineLineup == nil or table.getn(mineLineup) <= 0 then
        if arenaType == common_new_pb.Champion then
            local ui_multiplayer_hero_select = require "ui_multiplayer_hero_select"
            ui_multiplayer_hero_select.Set4Defence(true)

            local hero_select = ui_window_mgr:ShowModule("ui_multiplayer_hero_select")
            hero_select.enableArrange = true
            hero_select.startHandle = function(_window)
                local lineupList = {}
                for i, v in ipairs(_window.heroData) do
                    local team = {}
                    for _k, _v in pairs(v.heroes) do
                        local palPart = player_mgr.GetPalPartDataBySid(_v)
                        if palPart then
                            table.insert(team, { palId = --[[_v]]palPart.heroSid, row = _k <= 3 and 0 or 1, col = _k <= 3 and _k - 1 or (_k - 4) })
                        end
                    end
                    table.insert(lineupList, { palList = team })
                end

                local ok = true
                for i, v in ipairs(lineupList) do
                    if #v.palList <= 0 then
                        ok = false
                    end
                end

                if ok then
                    net_arena_module.Send_ARENA_SET_DEFENCE_LINEUP(arenaType, lineupList)
                    net_arena_module.MineDefence[arenaType] = lineupList
                    _window:Close()
                else
                    event.Trigger(event.SHOW_FLOW_MESSAGE, 7129)
                end
            end
            -- elseif arenaType == common_new_pb.Advance then
            -- 	-- 高阶竞技场多队伍
            -- 	SetAdvanceDefenceLineups(arenaType)
        else
            SaveDefenceLineups(arenaType, false)
        end
    else
        -- 入场
        event.Trigger(event.ARENA_LINEUP_SUCCESS, arenaType)
    end
end
event.Register(event.ARENA_DEFEND_LINEUP_RECV, RecvDefendLineup)

function SetAdvanceDefenceLineups(arenaType)
    local ui_multi_hero_select = require "ui_multi_hero_select"
    ui_multi_hero_select.SetSaveHeroData({}, nil, ui_multi_hero_select.DEFENCE)
    local window = ui_window_mgr:ShowModule("ui_multi_hero_select")

    if window ~= nil then
        window.onCloseEvent = nil
        window.onFightEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                local selectedHero = ui_multi_hero_select.GetSelectedHero()
                local lineupList = {}
                local temp = {}
                for i, v in pairs(selectedHero) do
                    local temp = {}
                    for pos, data in pairs(v) do
                        -- pos 0-5
                        table.insert(temp, { palId = data.heroSid, row = pos < 3 and 0 or 1, col = pos < 3 and pos or pos - 3 })
                    end
                    table.insert(lineupList, { palList = temp })
                end
                net_arena_module.MineDefence[arenaType] = lineupList
                net_arena_module.Send_ARENA_SET_DEFENCE_LINEUP(arenaType, net_arena_module.MineDefence[arenaType])
                ui_window_mgr:UnloadModule("ui_multi_hero_select")
            end
        end
    end
end

function SaveDefenceLineups(_arenaType, setPre)
    local arenaType = _arenaType
    if setPre then
        local array = net_arena_module.MineDefence[arenaType] or { {} }
        local hData = {}
        if array[1] then
            array = array[1].palList or {}
            for _, v in ipairs(array) do
                local heroEntity = player_mgr.GetPalPartDataBySid(v.palId)
                if heroEntity then
                    hData[v.row * 3 + v.col] = heroEntity
                end
            end
        end

        local ui_select_hero = require "ui_select_hero"
        -- local wID = weapon_data.GetLocalizedWeaponData(common_new_pb.Arena,0,arenaType)
        -- local weaponData = {
        --     stage = common_new_pb.Arena,
        --     weaponId = wID,
        --     lineupIdx = 1,
        --     arenaType1 = arenaType,
        -- }
        -- ui_select_hero.SetSaveWeaponData(weaponData)--传送武器数据

        ui_select_hero.SetSaveHeroData(hData)
    end

    local wID = weapon_data.GetLocalizedWeaponData(common_new_pb.Arena, 0, arenaType)
    local ui_select_model_node = require "ui_select_model_node"
    ui_select_model_node.SetBattleType(common_new_pb.Arena)
    local moduleName = "ui_select_hero"
    local weaponData = {
        stage = common_new_pb.Arena,
        weaponId = wID,
        lineupIdx = 0,
        arenaType1 = arenaType,
    }

    local _module = require(moduleName)
    _module.SetSaveWeaponData(weaponData)

    local unforced_guide_mgr = require "unforced_guide_mgr"
    unforced_guide_mgr.SetBattleType(unforced_guide_mgr.BATTLE_TYPE_NUM.AREAN_SINGLE)

    local scene_mgr = require "scene_mgr"
    local BanClick = scene_mgr.BanClick
    BanClick(true, moduleName)

    local window = ui_window_mgr:ShowModule(moduleName, function()
        BanClick(false)
    end)

    local isShowTask = ui_window_mgr:IsModuleShown("ui_task_achievement")
    if isShowTask then
        ui_window_mgr:UnloadModule("ui_task_achievement")
    end
    if window ~= nil then
        window.onCloseEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:UnloadModule(moduleName)
                if isShowTask then
                end
                if not setPre then
                end
            end
        end

        window.onFightEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                local selectedHero = window:GetSelectedHero()
                local weapon = window:GetSelectedWeapon()
                local temp = {}
                for pos = 0, 5 do
                    if selectedHero and selectedHero[pos] ~= nil then
                        local data = selectedHero[pos]
                        if data then
                            table.insert(temp, { palId = data.heroSid, row = pos < 3 and 0 or 1, col = pos < 3 and pos or pos - 3 })
                        end
                    end
                end
                --下面写法插入阵容数据是从1-5最后是0，服务器存储数据是按顺序存储，导致阵容位置实际对应不上
                -- for pos, data in pairs(selectedHero) do
                --     -- pos 0-5
                --      --print("pos",pos,"data.heroSid",data.heroSid,"row",pos < 3 and 0 or 1,"")
                --     table.insert(temp, {palId = data.heroSid, row = pos < 3 and 0 or 1, col = pos < 3 and pos or pos - 3})
                -- end
                net_arena_module.MineDefence[arenaType] = { { palList = temp } }
                net_arena_module.Send_ARENA_SET_DEFENCE_LINEUP(arenaType, net_arena_module.MineDefence[arenaType])

                util.DelayCall(0.3, function()
                    ui_window_mgr:UnloadModule(moduleName)
                end)
            end
        end
    end
end

function updataReddot()
    if window and window:IsValid() then
        if not window.Auto_Unit1_Reddot:IsNull() and not window.Auto_Unit2_Reddot:IsNull() then
            window:UpdateReddot()
        end
    end
end
event.Register(event.ARENA_GET_LEVEL_REWARD_INFO, updataReddot)
event.Register(event.ARENA_GET_CURRENT_RIVAL, updataReddot)
event.Register(event.ARENA_GET_VICTORY_REWARD, updataReddot)
event.Register(event.LEGEND_CHAMPIONSHIPS_SEASON_DATA_UPDATE, updataReddot)

function update_TopGameReddot()
    if window and window:IsValid() then
        window:UpdateTopGameReddot()
        window:UpdateTopGameRewardReddot()
    end
end
event.Register(event.TOPRACE_DATA, update_TopGameReddot)
event.Register(event.TOPRACE_LIKE_UPDATE, update_TopGameReddot)
event.Register(event.TOPRACE_GETTOPLIST, update_TopGameReddot)
event.Register(event.TOPRACE_SETDEFENSELINEUP, update_TopGameReddot)

function update_TopGameRewardReddot()
    if window and window:IsValid() then
        window:UpdateTopGameRewardReddot()
    end
end
event.Register(event.TOPRACE_RANK_REWARD, update_TopGameRewardReddot)

function OnSceneDestroy()
    if window ~= nil then
        window:Close()
        window = nil
    end
    isInitWeekendArenaData = false
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)
event.Register(event.LOADING_BATTLE_TIME_OUT, OnSceneDestroy)