--@region FileHead
-- ui_sigil_equip.txt ---------------------------------
-- author:  罗华冠
-- date:    3/3/2021 3:32:34 PM
-- ver:     1.0
-- desc:    印记穿搭界面
-------------------------------------------------


--@region Require
local print     = print
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local typeof    = typeof
local string    = string
local table     = table
local tonumber  = tonumber
local coroutine = coroutine
local math      = math

local Button                = CS.UnityEngine.UI.Button
local Text                  = CS.UnityEngine.UI.Text
local Image                 = CS.UnityEngine.UI.Image
local ScrollRectTable       = CS.UI.UGUIExtend.ScrollRectTable
local ScrollRectItem        = CS.UI.UGUIExtend.ScrollRectItem
local ImageGray 	        = CS.War.UI.ImageGray
local LayoutRebuilder       = CS.UnityEngine.UI.LayoutRebuilder
local SpriteSwitcher        = CS.War.UI.SpriteSwitcher
local Input                 = CS.UnityEngine.Input
local ClickIsOverUI         = CS.War.UI.ClickIsOverUI
local Canvas                = CS.UnityEngine.Canvas
local ParticleSystem        = CS.UnityEngine.ParticleSystemRenderer

--local Text = UnityEngine.UI.Text


local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local log                   = require "log"
local RectTransform         = CS.UnityEngine.RectTransform
local sigil_mgr             = require "sigil_mgr"
local player_mgr            = require "player_mgr"
local game_scheme           = require "game_scheme"
local ui_window_mgr         = require "ui_window_mgr"
local prop_pb               = require "prop_pb"
local lang                  = require "lang"
local goods_item            = require "goods_item_new"
-- local goods_item            = require "goods_item"
local util                  = require "util"
local simple_hero_item             = require "simple_hero_item"
local base_object           = require "base_object"
local skep_mgr              = require "skep_mgr"
local item_data             = require "item_data"
local laymain_data          = require "laymain_data"
local equipment_mgr         = require "equipment_mgr"
local hero_mgr              = require "hero_mgr"

--@region ModuleDeclare
module("ui_sigil_equip")
--local interface = require "iui_sigil_equip"
local window = nil
local UISigilEquip = {}
local sigilData = {} -- 当前各个部位的穿戴情况
local parent = nil
local heroEntity = nil -- 英雄实体数据
local showAllPos = true -- 是否展示所有部位
local curSelectSuit = nil -- 当前筛选的套装类型
local curSelectSuitID = nil -- 当前筛选得套装id
local curSelectPos = nil -- 当前选中的部位
local curSelectSid = nil -- 当前选中的印记sid
local curSelectSuitItem = nil -- 用于记录上一个选中得套装item
local curSelectitem = nil -- 用于记录上一个选中得印记item
local replaceEntity = nil -- 当前需要替换得目标道具实体数据
local activedSuitData = nil -- 当前已激活套装数据
local allSuitData = nil -- 当前装备所有套装数据（包含未激活）
local selectedIndex = nil -- 当前英雄索引
local heroCount = nil -- 当前英雄总数
local heroShareData = nil -- 查看他人英雄的数据
local isTrialHero = nil -- 是否试用英雄


PageType = {
    SigilPanel = 1,
    PropPanel = 2,
    }
local curPage = nil
SigilPageType = {
    Sigil = 1,
    Suit = 2,
    }
local curSigilPage = nil


--@region WidgetTable
UISigilEquip.widget_table = {
    -- top
	-- 槽位
    heroIcon = {path ="top/heroIcon/mask/icon" , type = "Image"},

	sigilRect = {path ="top/sigil" , type = RectTransform},
	stoneItem1 = {path ="top/sigil/stone_1" , type = "Button"},
	stoneItem2 = {path ="top/sigil/stone_2" , type = "Button"},
	stoneItem3 = {path ="top/sigil/stone_3" , type = "Button"},
	stoneItem4 = {path ="top/sigil/stone_4" , type = "Button"},
	stoneItem5 = {path ="top/sigil/stone_5" , type = "Button"},
	stoneItem6 = {path ="top/sigil/stone_6" , type = "Button"},

    
	sigilItem1 = {path ="top/sigil/stone_1" , type = ScrollRectItem},
	sigilItem2 = {path ="top/sigil/stone_2" , type = ScrollRectItem},
	sigilItem3 = {path ="top/sigil/stone_3" , type = ScrollRectItem},
	sigilItem4 = {path ="top/sigil/stone_4" , type = ScrollRectItem},
	sigilItem5 = {path ="top/sigil/stone_5" , type = ScrollRectItem},
	sigilItem6 = {path ="top/sigil/stone_6" , type = ScrollRectItem},

	stone_red_1 = {path ="top/sigil/stone_1/red" , type = "Image"},
	stone_red_2 = {path ="top/sigil/stone_2/red" , type = "Image"},
	stone_red_3 = {path ="top/sigil/stone_3/red" , type = "Image"},
	stone_red_4 = {path ="top/sigil/stone_4/red" , type = "Image"},
	stone_red_5 = {path ="top/sigil/stone_5/red" , type = "Image"},
	stone_red_6 = {path ="top/sigil/stone_6/red" , type = "Image"},

	undressBtn = {path = "bottom/undressBtn", type = "Button"},
	recommandBtn = {path = "bottom/recommandBtn", type = "Button"},
	helpBtn = {path = "top/question", type = "Button"},
	lineupBtn = {path = "top/LineupBtn", type = "Button"},
    
    -- bottom
    -- 套装效果
    suitRect = {path ="top/suit/items" , type = RectTransform},
    suitItem1 = {path ="top/suit/items/item1" , type = "Button"},
    suitItem2 = {path ="top/suit/items/item2" , type = "Button"},
    suitItem3 = {path ="top/suit/items/item3" , type = "Button"},

    suitItemGray1 = {path ="top/suit/items/item1/icon" , type = ImageGray},
    suitItemGray2 = {path ="top/suit/items/item2/icon" , type = ImageGray},
    suitItemGray3 = {path ="top/suit/items/item3/icon" , type = ImageGray},

    suit_img_bg_1 = {path ="top/suit/items/item1" , type = "Image"},
    suit_img_bg_2 = {path ="top/suit/items/item2" , type = "Image"},
    suit_img_bg_3 = {path ="top/suit/items/item3" , type = "Image"},

    suit_img_1 = {path ="top/suit/items/item1/icon" , type = "Image"},
    suit_img_2 = {path ="top/suit/items/item2/icon" , type = "Image"},
    suit_img_3 = {path ="top/suit/items/item3/icon" , type = "Image"},

    suit_lvtext_1 = {path ="top/suit/items/item1/level" , type = "Text"},
    suit_lvtext_2 = {path ="top/suit/items/item2/level" , type = "Text"},
    suit_lvtext_3 = {path ="top/suit/items/item3/level" , type = "Text"},

    -- 属性
    propRect = {path ="bottom/totalProp/srollview/Viewport/prop" , type = RectTransform},
    prop1 = {path ="bottom/totalProp/srollview/Viewport/prop/prop1" , type = RectTransform},
    prop2 = {path ="bottom/totalProp/srollview/Viewport/prop/prop2" , type = RectTransform},
    prop3 = {path ="bottom/totalProp/srollview/Viewport/prop/prop3" , type = RectTransform},
    prop4 = {path ="bottom/totalProp/srollview/Viewport/prop/prop4" , type = RectTransform},
    prop5 = {path ="bottom/totalProp/srollview/Viewport/prop/prop5" , type = RectTransform},
    prop6 = {path ="bottom/totalProp/srollview/Viewport/prop/prop6" , type = RectTransform},

    propText1 = {path ="bottom/totalProp/srollview/Viewport/prop/prop1/propName" , type = "Text"},
    propText2 = {path ="bottom/totalProp/srollview/Viewport/prop/prop2/propName" , type = "Text"},
    propText3 = {path ="bottom/totalProp/srollview/Viewport/prop/prop3/propName" , type = "Text"},
    propText4 = {path ="bottom/totalProp/srollview/Viewport/prop/prop4/propName" , type = "Text"},
    propText5 = {path ="bottom/totalProp/srollview/Viewport/prop/prop5/propName" , type = "Text"},
    propText6 = {path ="bottom/totalProp/srollview/Viewport/prop/prop6/propName" , type = "Text"},

    propvalueText1 = {path ="bottom/totalProp/srollview/Viewport/prop/prop1/value" , type = "Text"},
    propvalueText2 = {path ="bottom/totalProp/srollview/Viewport/prop/prop2/value" , type = "Text"},
    propvalueText3 = {path ="bottom/totalProp/srollview/Viewport/prop/prop3/value" , type = "Text"},
    propvalueText4 = {path ="bottom/totalProp/srollview/Viewport/prop/prop4/value" , type = "Text"},
    propvalueText5 = {path ="bottom/totalProp/srollview/Viewport/prop/prop5/value" , type = "Text"},
    propvalueText6 = {path ="bottom/totalProp/srollview/Viewport/prop/prop6/value" , type = "Text"},

	--propBg1 = {path ="bottom/totalProp/prop/propBg1" , type = "Image"},
	--propBg2 = {path ="bottom/totalProp/prop/propBg2" , type = "Image"},

    nonePropTip = {path ="bottom/totalProp/noneTip" , type = RectTransform}, 


    -- 套装效果提示
    suitTipRect = {path ="bottom/suitTip" , type = RectTransform},
    suitTipItem = {path ="bottom/suitTip" , type = ScrollRectItem},
    suitTipCloseBtn = {path ="bottom/suitTip/closeBtn" , type = 'Button'},
    suitTipBg = {path ="bottom/suitTip/bg" , type = RectTransform},

    -- 属性面板
    propPanel = {path ="bottom/totalProp" , type = ScrollRectItem},

    -- 印记面板
    sigilPanel = {path ="bottom/sigilPanel" , type = RectTransform},
    btnGroupLayout = {path = "bottom/sigilPanel/btnGroup", type = RectTransform},
    showAllPosBtn = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn", type = 'Button'},
    suitBtn = {path = "bottom/sigilPanel/btnGroup/suitBtn", type = 'Button'},
    suitBtnRect = {path = "bottom/sigilPanel/btnGroup/suitBtn", type = RectTransform},
    showAllBtnSwitcher = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn", type = SpriteSwitcher},
    showAllBtnImage = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn", type = 'Image'},
    showAllBtnSelectedImageNoSelectedBgBG={path = "bottom/sigilPanel/btnGroup/showAllPosBtn/selectedBg/noSelectedBg", type = 'Image'},
    showAllBtnSelectedImage = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn/selectedBg/selected", type = 'Image'},
    showAllBtnText = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn/text", type = 'Text'},
    showAllBtnSelText = {path = "bottom/sigilPanel/btnGroup/showAllPosBtn/selText", type = 'Text'},

    suitBtnSwitcher = {path = "bottom/sigilPanel/btnGroup/suitBtn", type = SpriteSwitcher},
    suitBtnImage = {path = "bottom/sigilPanel/btnGroup/suitBtn", type = 'Image'},
    suitBtnIcon = {path = "bottom/sigilPanel/btnGroup/suitBtn/suitImage/icon", type = 'Image'},
    suitBtnImageRect = {path = "bottom/sigilPanel/btnGroup/suitBtn/suitImage", type = RectTransform},
    suitBtnSelectBgImage = {path = "bottom/sigilPanel/btnGroup/suitBtn/selectedBg", type = 'Image'},
    suitBtnSelectImage = {path = "bottom/sigilPanel/btnGroup/suitBtn/selectedBg/selected", type = 'Image'},
    suitBtnSelectImageNoSelectedBgBG={path = "bottom/sigilPanel/btnGroup/suitBtn/selectedBg/noSelectedBg", type = 'Image'},

    suitBtnName = {path = "bottom/sigilPanel/btnGroup/suitBtn/text", type = 'Text'},
    suitBtnSelName = {path = "bottom/sigilPanel/btnGroup/suitBtn/selText", type = 'Text'},

    selectSuitPanel = {path ="bottom/sigilPanel/selectSuitPanel" , type = RectTransform},
    suitList = {path = "bottom/sigilPanel/selectSuitPanel/suitList/Viewport/Content", type = ScrollRectTable},

	sigilList = {path = "bottom/sigilPanel/sigilList/Viewport/Content", type = ScrollRectTable},
    sigilListPanel = {path ="bottom/sigilPanel/sigilList" , type = RectTransform},
    sigilNoneTip = {path ="bottom/sigilPanel/sigilList/noneTip" , type = RectTransform},

    sigilLockPanel = {path ="bottom/sigilPanel/lockTip" , type = RectTransform},
    sigilLockText = {path ="bottom/sigilPanel/lockTip/text" , type = 'Text'},
    -- 物品详情面板
    wearPanel = {path ="bottom/wear" , type = RectTransform},
    replacePanel = {path ="bottom/replace" , type = RectTransform},

    wearItem = {path="bottom/wear/item",type = ScrollRectItem},
    replaceItem1 = {path="bottom/replace/item1",type = ScrollRectItem},
    replaceItem2 = {path="bottom/replace/item2",type = ScrollRectItem},

    wearItemCanvas = {path="bottom/wear/item",type = Canvas},
    replaceItemCanvas1 = {path="bottom/replace/item1",type = Canvas},
    replaceItemCanvas2 = {path="bottom/replace/item2",type = Canvas},

    itemCloseBtn = {path ="bottom/itemCloseBtn" , type = 'Button'},

    -- Toggle
    toggleGroup = {path="bottom/ToggleGroup",type=RectTransform},
    sigilToggle = {path="bottom/ToggleGroup/Viewport/Content/toggle_1",type='Toggle'},
    propToggle = {path="bottom/ToggleGroup/Viewport/Content/toggle_2",type='Toggle'},

    sigilSelText = {path="bottom/ToggleGroup/Viewport/Content/toggle_1/Text",type='Text'},
    propSelText = {path="bottom/ToggleGroup/Viewport/Content/toggle_2/Text",type='Text'},

    sigilNotSelText = {path="bottom/ToggleGroup/Viewport/Content/toggle_1/Text_black",type='Text'},
    propNotSelText = {path="bottom/ToggleGroup/Viewport/Content/toggle_2/Text_black",type='Text'},

    -- arrow
    leftArrow = {path ="top/leftArrow" , type = 'Button'},
    rightArrow = {path ="top/rightArrow" , type = 'Button'},
    
    unloadallBtn = {path ="top/UnloadAll" , type = 'Button'},

    suitDescInactivedText ={path="bottom/wear/item/suitDescInactived/Text",type="Text",fitArabic=true},

    listItemDesc ={path="bottom/sigilPanel/selectSuitPanel/suitList/Viewport/Content/ListItem/desc",type="Text",fitArabic=true},
}


--@region WindowCtorsigi
function UISigilEquip:ctor(selfType)
	self.__base:ctor(selfType)
end

--@region WindowInit
--[[窗口初始化]]
function UISigilEquip:Init()
    self:SubscribeEvents()
    net_route.RegisterMsgHandlers(MessageTable)
    self:CreateIconAsset()

    self.sigilList.onItemRender = onItemRenderBottom
    self.sigilList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.itemUI ~= nil then
            scroll_rect_item.data["itemUI"]:Dispose()
            scroll_rect_item.data["itemUI"] = nil
        end
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.heroIcon ~= nil then
            scroll_rect_item.data["heroIcon"]:Dispose()
            scroll_rect_item.data["heroIcon"] = nil
        end
    end
    self.sigilList.renderPerFrames = 3

    --self.sigilList:SetPageSize()

    self.suitList.onItemRender = onSuitItemRenderBottom
    self.suitList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.itemUI ~= nil then
        end
    end
end

-- 印记item
function onItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local root = scroll_rect_item:Get('root')
    local selected = scroll_rect_item:Get('selected')
	local count = scroll_rect_item:Get("count")
	local countBg = scroll_rect_item:Get("countBg")
	local progress = scroll_rect_item:Get("progress")
	local progressfull = scroll_rect_item:Get("progressfull")
	local rectTransform = scroll_rect_item:Get("rectTransform")
	local lock = scroll_rect_item:Get("lock")
	local lockBg = scroll_rect_item:Get("lockBg")
	local red = scroll_rect_item:Get("red")
	local heroIconRoot = scroll_rect_item:Get("heroIcon")

    local entity = dataItem.entity
    local itemID = entity:GetGoodsID()
    local itemSid = entity:GetGoodsSid()
    if curSelectSid then
        selected.gameObject:SetActive(curSelectSid == itemSid)
    else
        selected.gameObject:SetActive(false)
    end

    local condition = dataItem.cfg.Herostar
    local isUnlock = true
    if heroEntity then
        isUnlock = heroEntity:GetStarLv() >= condition
    end

    lock.gameObject:SetActive(not isUnlock)
    lockBg.gameObject:SetActive(not isUnlock)

    local itemUI = scroll_rect_item.data["itemUI"] and scroll_rect_item.data["itemUI"] or goods_item.CGoodsItem():Init(root.gameObject.transform,function (p)
        if not p then return end
        p:DisplayInfo()
    end,0.66)
    scroll_rect_item.data["itemUI"] = itemUI
	itemUI:SetFrameBg(3)
	-- itemUI:SetCountEnable(false)
    itemUI:SetGoods(entity, itemID, 0, function()
        if not isUnlock then
            local sigilCfg = game_scheme:Sigil_0(itemID)
            local flow_text = require "flow_text"
            local heroStarStr = lang.Get(hero_mgr.Hero_Star_Desc[sigilCfg.Herostar])
            flow_text.Add(string.format( lang.Get(230025),heroStarStr))
            return
        end
        if curSelectSid ~= itemSid then
            if curSelectitem then
                curSelectitem:SetActive(false)
            end
            curSelectSid = itemSid
            replaceEntity = entity
            curSelectitem = selected
            curSelectitem:SetActive(true)
            if window and window:IsValid() then
                window.targetHeroEntity = dataItem.heroEntity
                window:SetItemUI(dataItem.entity,dataItem.heroEntity,nil,true)
            end
        else
            if curSelectitem then
                curSelectitem:SetActive(false)
            end
            curSelectSid = nil
            replaceEntity = nil
            curSelectitem = nil
            if window and window:IsValid() then
                window:SetItemUI(dataItem.entity,dataItem.heroEntity,nil,false)
            end
        end
    end)
    
	if dataItem.heroEntity then
		local hero = scroll_rect_item.data["heroIcon"] or simple_hero_item.CSimpleHeroItem():Init(heroIconRoot,function (p)
            if not p then return end
                p:DisplayInfo()
        end,0.3)
        scroll_rect_item.data["heroIcon"] = hero
        scroll_rect_item.data["heroIcon"]:SetHero(dataItem.heroEntity, nil, true)
        if hero:IsLoaded() then
            hero:DisplayInfo()
        end
    end
	heroIconRoot.gameObject:SetActive(dataItem.heroEntity ~= nil)
end


-- 套装item
function onSuitItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem

    local totalText = scroll_rect_item:Get('totalText')
    local suitName = scroll_rect_item:Get('suitName')
    local icon = scroll_rect_item:Get('icon')
    local iconBg = scroll_rect_item:Get('iconBg')
    local desc = scroll_rect_item:Get('desc')
    local selected = scroll_rect_item:Get('selected')
    

    local isAll = dataItem.isAll

    totalText.gameObject:SetActive(isAll)
    suitName.gameObject:SetActive(not isAll)
    icon.gameObject:SetActive(not isAll)
    iconBg.gameObject:SetActive(not isAll)
    desc.gameObject:SetActive(not isAll)
    
    
	if not curSelectSuit then
		-- 选中所有套装
        selected.gameObject:SetActive(isAll)
		if isAll then
            curSelectSuitItem = selected
		end
	else
		selected.gameObject:SetActive(dataItem.suitType == curSelectSuit)
		if dataItem.suitType == curSelectSuit then
            curSelectSuitItem = selected
		end
	end

    if dataItem.suitID then
        local suitCfg = game_scheme:EquipmentSet_0(dataItem.suitID)
        if suitCfg then
            suitName.text = lang.Get(suitCfg.nameID)
            desc.text = lang.Get(suitCfg.descID2)
            if window and window.sigilSpriteAsset then
                window.sigilSpriteAsset:GetSprite(suitCfg.iconID, function(sprite)
                    if window and window:IsValid() then
                        icon.enabled = true
                        icon.sprite = sprite
                    end
                end)
                -- window.sigilSpriteAsset:GetSprite(suitCfg.iconID2, function(sprite)
                --     if window and window:IsValid() then
                --         iconBg.enabled = true
                --         iconBg.sprite = sprite
                --     end
                -- end)
            end
        end
    end

    local func = {}
    func['clickFun'] = function ()
		if not isAll then
			curSelectSuit = dataItem.suitType
            curSelectSuitID = dataItem.suitID
		else
			curSelectSuit = nil
            curSelectSuitID = nil
		end
        curSelectPos = nil
        curSelectSid = nil
        curSelectitem = nil
        replaceEntity = nil
        showAllPos = true
        selected.gameObject:SetActive(true)
        local old = curSelectSuitItem
        if old then
            old.gameObject:SetActive(false)
        end
        curSelectSuitItem = selected
        if window and window:IsValid() then
            window:UpdateBtnGroupState()
			window:ShowSuitPanel(false)
        end
    end
    
    scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end


--图片资源管理=======================
function UISigilEquip:CreateIconAsset()
	local card_sprite_asset=require"card_sprite_asset"
	self.sigilSpriteAsset=card_sprite_asset.CreateSpriteAsset()
end


--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UISigilEquip:OnShow()
	self.__base:OnShow()
    self:SetUIState()
end


function UISigilEquip:UpdateUI()
    for i=1,6 do
        local data = sigilData[i]
        local sigilItem = self['sigilItem'..i]
        if sigilItem and data then
            local starCanvas = sigilItem:Get("starCanvas")
            starCanvas.sortingOrder = window.curOrder + 1

            SetChildOrder(sigilItem:Get("blue"))
            SetChildOrder(sigilItem:Get("yellow"))
            SetChildOrder(sigilItem:Get("white"))
        end
    end
end

function SetChildOrder(obj,order)
    if obj and not util.IsObjNull(obj) then
        local childs = obj.gameObject.transform:GetComponentsInChildren(typeof(ParticleSystem))
        for i = 0, childs.Length - 1 do
            childs[i].sortingOrder = order
        end
    end
end

function UISigilEquip:SetUIState()
    if heroShareData then
        -- 查看他人英雄 仅展示属性
        self.toggleGroup.gameObject:SetActive(false)
        self:UpdateShareHeroIcon()
        self:UpdateShareSuitData()
        self:UpdateSigilSlotUI()
        self:UpdateShareSigilUI()
        self:UpdatePanelState(PageType.PropPanel)
    else
        curSigilPage = SigilPageType.Sigil
        self:UpdateUIPage()
        self:UpdateHeroIcon()
        self:UpdateSuitData()
        self:UpdateSigilUI()
        self:UpdatePanelState(PageType.SigilPanel)
        -- self:UpdateSigilSlotUI()
        self:UpdateBtnGroupState()
    end
end

function UISigilEquip:UpdateHeroIcon()
    self.heroSid = sigil_mgr.GetCurHeroSid()
    heroEntity = player_mgr.GetPalPartDataBySid(self.heroSid)
    local hero_trial_mgr = require "hero_trial_mgr"
    isTrialHero = hero_trial_mgr.isTrialHeroBySid(self.heroSid)
    -- 绕过客户端判断试用英雄 test
    local lingshi_data = require "lingshi_data"
    local value = lingshi_data.GetSkipClientMsgLimit()
    if value then
        isTrialHero = nil
    end
    if heroEntity then
        local heroCfg = game_scheme:Hero_0(heroEntity.heroID)
        if heroCfg then
            local skinID = heroEntity.skinID
            local faceID = nil
            if not skinID or skinID == 0 then
                faceID = heroCfg.faceID.data[0]
            else
                local skin_mgr = require "skin_mgr"
                skinID = skin_mgr.GetCurSkinID(heroEntity.heroID)
                faceID = heroCfg.faceID.data[0]
            end
            if skinID and skinID ~= 0 then
                local starsLv = heroEntity:GetStarLv()
                local _faceID = hero_mgr.ChangeHeroSkinIcon(skinID, starsLv)
                faceID = _faceID or faceID
            end
            if faceID then
                self.sigilSpriteAsset:GetSprite(faceID, function(sprite)
                    if self:IsValid() then
                        self.heroIcon.enabled = true
                        self.heroIcon.sprite = sprite
                    end
                end)
            end
        end
    end
end

function UISigilEquip:UpdateShareHeroIcon()
    if heroShareData then
        local heroCfg = game_scheme:Hero_0(heroShareData.palID)
        if heroCfg then
            self.sigilSpriteAsset:GetSprite(heroCfg.faceID.data[0], function(sprite)
                if self:IsValid() then
                    self.heroIcon.enabled = true
                    self.heroIcon.sprite = sprite
                end
            end)
        end
    end
end

-- 更新面板状态
function UISigilEquip:UpdatePanelState(value)
    if isTrialHero then --试用英雄不显示印记选择列表
        value = PageType.PropPanel
        self.propToggle.isOn = true
        self.sigilToggle.isOn = false
        self.sigilToggle.gameObject:SetActive(false)
    else
        self.sigilToggle.gameObject:SetActive(true)
        if value == PageType.SigilPanel then
            self.sigilToggle.isOn = true
            self.propToggle.isOn = false
        end
    end
    if value then
        if not isTrialHero and curPage == value then
            return
        end
        curPage = value
    end
    self.sigilPanel.gameObject:SetActive(curPage==PageType.SigilPanel)
    self.propPanel.gameObject:SetActive(curPage==PageType.PropPanel)

    self.sigilSelText.gameObject:SetActive(curPage==PageType.SigilPanel)
    self.propSelText.gameObject:SetActive(curPage==PageType.PropPanel)

    self.sigilNotSelText.gameObject:SetActive(curPage~=PageType.SigilPanel)
    self.propNotSelText.gameObject:SetActive(curPage~=PageType.PropPanel)

    if curPage==PageType.PropPanel then
        -- 更新属性面板
        if heroShareData then
            self:UpdateSharePropPanelUI()
        else
            self:UpdatePropPanelUI()
        end
    elseif curPage==PageType.SigilPanel then
        -- 更新印记面板
        self:UpdateSigilPanelUI()
    end
end

-- 更新按钮状态
function UISigilEquip:UpdateBtnGroupState()
    if curSigilPage == SigilPageType.Sigil and showAllPos then
        -- 展示所有部位
        self.showAllBtnSwitcher:Switch(1)
        self.showAllBtnImage:SetNativeSize()
        self.showAllBtnSelectedImage.gameObject:SetActive(true)
        self.showAllBtnSelectedImageNoSelectedBgBG.enabled=false
        self.showAllBtnText.gameObject:SetActive(false)
        self.showAllBtnSelText.gameObject:SetActive(true)
    else
        self.showAllBtnSwitcher:Switch(0)
        self.showAllBtnImage:SetNativeSize()
        self.showAllBtnSelectedImage.gameObject:SetActive(false)
        self.showAllBtnSelectedImageNoSelectedBgBG.enabled=true

        self.showAllBtnText.gameObject:SetActive(true)
        self.showAllBtnSelText.gameObject:SetActive(false)
    end
    if curSelectSuit then
        -- 已筛选套装
        if curSigilPage == SigilPageType.Suit then
            self.suitBtnSwitcher:Switch(1)
            self.suitBtnImage:SetNativeSize()
        else
            self.suitBtnSwitcher:Switch(0)
            self.suitBtnImage:SetNativeSize()
        end
        local suitCfg = game_scheme:EquipmentSet_0(curSelectSuitID)
        if suitCfg then
            self.suitBtnImageRect.gameObject:SetActive(true)
            self.suitBtnSelectBgImage.gameObject:SetActive(false)
            self.sigilSpriteAsset:GetSprite(suitCfg.iconID, function(sprite)
                if self:IsValid() then
                    self.suitBtnIcon.enabled = true
                    self.suitBtnIcon.sprite = sprite
                end
            end)
            self.suitBtnName.text = lang.Get(suitCfg.nameID)
            self.suitBtnSelName.text = lang.Get(suitCfg.nameID)
        end
    else
        if curSigilPage == SigilPageType.Suit then
            -- 在套装页但未筛选套装
            self.suitBtnSwitcher:Switch(1)
            self.suitBtnSelectImage.gameObject:SetActive(true)
            self.suitBtnSelectImageNoSelectedBgBG.enabled=false
        else
            self.suitBtnSwitcher:Switch(0)
            self.suitBtnSelectImage.gameObject:SetActive(false)
            self.suitBtnSelectImageNoSelectedBgBG.enabled=true

        end
        self.suitBtnImageRect.gameObject:SetActive(false)
        self.suitBtnSelectBgImage.gameObject:SetActive(true)
        
        self.suitBtnName.text = lang.Get(230082)
        self.suitBtnSelName.text = lang.Get(230082)
    end
    self.suitBtnName.gameObject:SetActive(curSigilPage~=SigilPageType.Suit)
    self.suitBtnSelName.gameObject:SetActive(curSigilPage==SigilPageType.Suit)
    
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.suitBtnRect)
    LayoutRebuilder.ForceRebuildLayoutImmediate(self.btnGroupLayout)
end

-- 套装筛选面板显隐控制
function UISigilEquip:ShowSuitPanel(value)
    self.selectSuitPanel.gameObject:SetActive(value)
    if value then
        curSigilPage = SigilPageType.Suit
    else
        curSigilPage = SigilPageType.Sigil
    end
    self:UpdateSigilSlotUI()
    self:UpdateBtnGroupState()
end

-- 更新印记槽
function UISigilEquip:UpdateSigilUI()
    if not self.heroSid then
        return
    end
    if not heroEntity then
        return
    end
    if not self.itemUI then
        self.itemUI = {}
    end
	local skepID = heroEntity:GetSigilSkepID()
	if skepID then
		local skep_mgr = require "skep_mgr"
		local skepList,length = skep_mgr.GetGoodsSidOfSkep(skepID)
        sigilData = {}
        if length > 0 then
            for k,v in pairs(skepList)do
                local entity = player_mgr.GetPacketPartDataBySid(v)
                if entity then
                    local pos = entity:GetSkepPlace()
                    sigilData[pos]=entity
                    local sigilItem = self['sigilItem'..pos]
                    -- sigilItem.gameObject:SetActive(sigilItem)
                    if sigilItem then
                        local add = sigilItem:Get("Add")
                        local red = sigilItem:Get("red")
                        local icon = sigilItem:Get("icon")
                        local starGroup = sigilItem:Get("starGroup")
                        local level = sigilItem:Get("level")
                        local mask = sigilItem:Get("mask")
                        local activedBg = sigilItem:Get("activedBg")
                        local starHGroup = sigilItem:Get("starHGroup")
                        local reddot = sigilItem:Get("reddot")
                        local activeBgRect = sigilItem:Get("activeBgRect")
                        local blue = sigilItem:Get("blue")
                        local yellow = sigilItem:Get("yellow")
                        local white = sigilItem:Get("white")
						local starCanvas = sigilItem:Get("starCanvas")

                        blue:SetActive(false)
                        yellow:SetActive(false)
                        white:SetActive(false)
                        
                        self:UpdateEnhanceState(sigilItem,entity)
                        reddot.gameObject:SetActive(false)
                        
                        local itemID = entity:GetGoodsID()
                        local itemCfg = game_scheme:Item_0(itemID)
                        if itemCfg then
                            -- 有装备印记
                            level.gameObject:SetActive(true)
                            starGroup.gameObject:SetActive(true)
                            mask.gameObject:SetActive(true)
                            local sigilCfg = game_scheme:Sigil_0(itemID)
                            if sigilCfg then
                                local grade = sigilCfg.grade-50
                                for i=0,starGroup.gameObject.transform.childCount-1 do
                                    local star = starGroup.gameObject.transform:GetChild(i)
                                    if star then
                                        star.gameObject:SetActive(grade >= i+1)
                                    end
                                end
                            end
                     
                            --starHGroup.spacing = -140+(grade*15)
							starCanvas.sortingOrder = window.curOrder + 1
                            level.text = '+'..entity.numProp.equipStrLv
                            self.sigilSpriteAsset:GetSprite(itemCfg.icon, function(sprite)
                                if self:IsValid() then
                                    icon.enabled = true
                                    icon.sprite = sprite
                                    icon:SetNativeSize()
                                end
                            end)
                            self.sigilSpriteAsset:GetSprite(itemCfg.icon2, function(sprite)
                                if self:IsValid() then
                                    activedBg.enabled = true
                                    activedBg.sprite = sprite
                                    activedBg.gameObject:SetActive(true)
                                    -- activedBg:SetNativeSize()
                                end
                            end)

							if itemCfg.icon2para and not string.empty(itemCfg.icon2para) then
								local arrData = string.split(itemCfg.icon2para, '#', tonumber)
								-- activeBgRect.anchoredPosition = {x=arrData[1],y=arrData[2]}
								activeBgRect.gameObject.transform.localEulerAngles = {x=0,y=0,z=arrData[3]}
                                if #arrData>=4 and tonumber(arrData[4]) == 1 then --第四个参数使用x轴的镜像翻转,1是翻转
                                    activeBgRect.gameObject.transform.localScale = {x = -1,y = 1,z = 1}
                                else
                                    activeBgRect.gameObject.transform.localScale = {x = 1,y = 1,z = 1}
                                end
							end

                            if activedSuitData then
                                local suitCfg = game_scheme:EquipmentSet_0(sigilCfg.suitID)
                                local curSuitLv = nil
                                if suitCfg then
                                    for k,v in pairs(activedSuitData)do
                                        -- 判断有无同类型的已激活套装
                                        if sigilCfg.suitType == v.sigilCfg.suitType then
                                            if sigilCfg.suitID >= v.sigilCfg.suitID then
                                                curSuitLv = util.get_len(v.suitCfg.preSet.data)
                                            end
                                        end
                                    end
                                    if curSuitLv then
                                        blue:SetActive(curSuitLv == 0)
                                        yellow:SetActive(curSuitLv == 1)
                                        white:SetActive(curSuitLv == 2)
                                    end
                                end
                            end
                        end
                    end
                else
                    
                end
            end
        end
	end
    
    for pos=1,6 do
        local sigilItem = self['sigilItem'..pos]
        local entity = sigilData[pos]
        if sigilItem and not entity then
            -- 空槽
            -- local add = sigilItem:Get("Add")
            local red = sigilItem:Get("red")
            local icon = sigilItem:Get("icon")
            local starGroup = sigilItem:Get("starGroup")
            local level = sigilItem:Get("level")
            local activedBg = sigilItem:Get("activedBg")
            local mask = sigilItem:Get("mask")
            local reddot = sigilItem:Get("reddot")
            local arrow = sigilItem:Get('arrow')
            local blue = sigilItem:Get("blue")
            local yellow = sigilItem:Get("yellow")
            local white = sigilItem:Get("white")

            blue:SetActive(false)
            yellow:SetActive(false)
            white:SetActive(false)
            
            arrow:SetActive(false)
            level.gameObject:SetActive(false)
            mask.gameObject:SetActive(false)
            starGroup.gameObject:SetActive(false)
            activedBg.gameObject:SetActive(false)
            red.gameObject:SetActive(false)
            reddot.gameObject:SetActive(sigil_mgr.IsHadValidSigil(pos,heroEntity) and not isTrialHero)
        end
    end
    self:UpdatePropUI()
end

-- 检测已装备印记是否有可升级
function UISigilEquip:UpdateEnhanceState(item,entity)
    if item and entity then
        local arrow = item:Get('arrow')
        arrow:SetActive(false)

        local sigilID = entity:GetGoodsID()
        local prop = entity:GetSigilProps()
        local sigilCfg = game_scheme:Sigil_0(sigilID)
        if sigilCfg and prop then
            local itemCfg = game_scheme:Item_0(sigilID)
            local curCfg = game_scheme:EquipEnhance_0(sigilCfg.grade,prop.enhanceLv,equipment_mgr.ENHANCE_TYPE.sigil)
            if not curCfg then
                return
            end
            local hanceCfg = game_scheme:EquipEnhance_0(sigilCfg.grade,math.min(prop.enhanceLv+1,curCfg.maxLevel),equipment_mgr.ENHANCE_TYPE.sigil)
            if hanceCfg then
                local goodsid = skep_mgr.GetConstGoodsSidByID(hanceCfg.costItem)
                local costEntity = player_mgr.GetPacketPartDataBySid(goodsid)
                local hasNum = costEntity and costEntity.numProp.goodsNum or 0
                local costEnough = hasNum>=hanceCfg.costNum
                local goldEnough = player_mgr.GetPlayerCoin()>=hanceCfg.costGold 
                local maxEnhanceLv = hanceCfg.maxLevel
                local nextHanceCfg = game_scheme:EquipEnhance_0(sigilCfg.grade, math.min(prop.enhanceLv+1,maxEnhanceLv),equipment_mgr.ENHANCE_TYPE.sigil)
                local isMax = maxEnhanceLv == prop.enhanceLv
                if not isMax and costEnough and goldEnough and not isTrialHero then
                    arrow:SetActive(true)
                end
            end
        end
    end
end

-- 更新属性总览
function UISigilEquip:UpdatePropUI()
    local propList = {}
    for k,v in pairs(sigilData)do
        local entity = v
        if entity then
            local sigilProp = entity:GetSigilProps()
            self:GetProp(sigilProp.baseProp1,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp2,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp3,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp4,sigilProp.enhanceLv,propList)
        end
    end
    for index=1,6 do
        local rect = self['prop'..index]
        if rect then
            rect.gameObject:SetActive(false)
        end
    end
    local index = 1
    for k,v in pairs(propList)do
        if v and v ~= 0 then
            local propValue = v            
            local proCfg = game_scheme:ProToLang_0(k)
            local propName = ""
            if proCfg then
                propName = lang.Get(proCfg.iLangId)
            end
            local isPercentage = self:IsPercentageProp(k)
            local percentage = isPercentage and "%" or ""
            local rect = self['prop'..index]
            local name = self['propText'..index]
            local value = self['propvalueText'..index]
            if rect then
                rect.gameObject:SetActive(true)
            end
            if name then
                name.text = propName
            end
            if value then
                value.text = "+"..((isPercentage and (propValue / 100)) or propValue)..percentage
            end
            index = index + 1
        end
    end
    --self.propBg1.gameObject:SetActive(util.get_len(propList)>0)
    --self.propBg2.gameObject:SetActive(util.get_len(propList)>4)
    self:UpdateSuitUI()
end

function UISigilEquip:GetProp(propID,enhanceLv,propList)
    if not self.eqProCfg then
        self.eqProCfg = {}
    end
    local cfg_equip = game_scheme:EquipmentPro_0(propID)
    if cfg_equip then
        if cfg_equip and cfg_equip.lvPro.data[enhanceLv-1] ~= nil then
            propList[cfg_equip.proType] = cfg_equip.lvPro.data[enhanceLv-1] + (propList[cfg_equip.proType]  or 0)
        end
        self.eqProCfg[propID] = cfg_equip
    end
end

-- 更新套装效果
function UISigilEquip:UpdateSuitUI()
    if sigilData and heroEntity then
        local suitData = hero_mgr.GetActiveSigilSuitDataByEntity(heroEntity)
        self.suitData = {}
        local index = 1
        -- self.noneSuitTip.gameObject:SetActive(util.get_len(suitData) <= 0)
        for index=1,3 do
            local item = self["suitItem"..index]
            if item then
                item.gameObject:SetActive(false)
            end
        end

        self.suitRect.gameObject:SetActive(util.get_len(suitData) > 0)
        if util.get_len(suitData) > 0 then
            for k,v in ipairs(suitData)do
                if v then
                    local item = self["suitItem"..index]
                    if item then
                        item.gameObject:SetActive(true)
                        local icon = self["suit_img_"..index]
                        local iconBg = self["suit_img_bg_"..index]
                        local lvText = self["suit_lvtext_"..index]

                        if lvText then
                            lvText.gameObject:SetActive(true)
                            lvText.text = lang.Get(v.suitCfg.suitlevel)
                        end
                        if icon then
                            self.sigilSpriteAsset:GetSprite(v.suitCfg.iconID, function(sprite)
                                if self:IsValid() then
                                    icon.enabled = true
                                    icon.sprite = sprite
                                    -- icon:SetNativeSize()
                                end
                            end)
                            
                            -- self.sigilSpriteAsset:GetSprite(v.suitCfg.iconID2, function(sprite)
                            --     if self:IsValid() then
                            --         iconBg.enabled = true
                            --         iconBg.sprite = sprite
                            --         iconBg:SetNativeSize()
                            --     end
                            -- end)
                            local grayItem = self["suitItemGray"..index]
                            table.insert(self.suitData, v)
                        end
                    end
                    index = index + 1
                end
            end
        end
    end
end

-----------------------------------选中面板-----------------------------------
-- @param entity:对应印记是否有被穿戴 
-- @param btnState:按钮显示状态 
-- 用于区分是穿戴还是替换
function UISigilEquip:SetItemUI(entity,hero,btn,isShow)
    -- self.itemCloseBtn.gameObject:SetActive(isShow)
    if entity then
        local position = game_scheme:Sigil_0(entity.goodsID).colourType
        local curEntity = sigilData[position] -- 当前部位穿戴得印记数据
        local curCanvas = nil
        local curCanvas2 = nil
        if curEntity then
            -- 替换印记
            local isSelf = curEntity and curEntity.goodsSid == entity.goodsSid
            if isSelf then
                -- 查看自身穿戴印记
                self.wearPanel.gameObject:SetActive(isShow)
                self.replacePanel.gameObject:SetActive(false)
                curCanvas = self.wearItemCanvas
            else
                self.wearPanel.gameObject:SetActive(false)
                self.replacePanel.gameObject:SetActive(isShow)
                curCanvas = self.replaceItemCanvas1
                curCanvas2 = self.replaceItemCanvas2
            end
        else
            -- 穿戴印记
            self.wearPanel.gameObject:SetActive(isShow)
            self.replacePanel.gameObject:SetActive(false)
            curCanvas = self.wearItemCanvas
        end
        if isShow then
            if curEntity and curEntity.goodsSid ~= entity.goodsSid then
                -- 替换
                local curSigilCfg = game_scheme:Sigil_0(curEntity.goodsID)
                local tarSigilCfg = game_scheme:Sigil_0(entity.goodsID)
                local needAdd = true
                if curSigilCfg and tarSigilCfg and curSigilCfg.suitID == tarSigilCfg.suitID then
                    needAdd = false
                end
                self:UpdateItem(self.replaceItem1,entity,hero,2,needAdd,curSigilCfg)
                self:UpdateItem(self.replaceItem2,curEntity,heroEntity,0,false)
                LayoutRebuilder.ForceRebuildLayoutImmediate(self.replacePanel)
            else
                -- 穿戴
                local needAdd = true
                if curEntity and curEntity.goodsSid == entity.goodsSid then
                    -- 查看本身 不需额外添加
                    needAdd = false
                end
                btn = isTrialHero and 0 or btn
                local btnState = btn and btn or 2
                self:UpdateItem(self.wearItem,entity,hero,btnState,needAdd)
            end

            if curCanvas then
                curCanvas.sortingOrder = self.curOrder + 2
            end
            if curCanvas2 then
                curCanvas2.sortingOrder = self.curOrder + 2
            end

            self.itemTicker = self.itemTicker or base_object()
            
            self.itemTicker:CreateTimeTicker(0, function()
                while true do
                    if Input.anyKeyDown or Input.anyKey then
                        if not curCanvas or (curCanvas and not ClickIsOverUI.Instance:IsPointerOverUIObject(curCanvas, {x = Input.mousePosition.x, y = Input.mousePosition.y})) then
                            if not curCanvas2 or (curCanvas2 and not ClickIsOverUI.Instance:IsPointerOverUIObject(curCanvas2, {x = Input.mousePosition.x, y = Input.mousePosition.y})) then
                                break
                            end
                        end
                    end
                    coroutine.yield(0.01)
                end
                if self and self:IsValid() then
                    self:SetItemUI()
                end
            end)
        end
        return
    end
    if not isShow then
        self.wearPanel.gameObject:SetActive(false)
        self.replacePanel.gameObject:SetActive(false)
        self.itemCloseBtn.gameObject:SetActive(false)
        if self.itemTicker then
            self.itemTicker:Dispose()
            self.itemTicker = nil
        end
    end
end

-- 更新对应item
-- @param btnState: 0:隐藏按钮 1：强化/卸下 2：穿戴
-- @param needAdd:true:需要加上本身 false:不需要加上本身（查看未穿戴需要预览效果加上本身，已穿戴的就不需要在加了）
-- @param curSigilCfg: 当前部位穿戴的印记配置（有可能为nil）
function UISigilEquip:UpdateItem(item,entity,hero,btnState,needAdd,curSigilCfg)
    if item and entity then
        local iconBg = item:Get('iconBg')
        local icon = item:Get('icon')
        local name = item:Get('name')
        local starGroup = item:Get('starGroup')
        local equiped = item:Get('equiped')
        local prop = item:Get('prop')
        local buttonRoot = item:Get('buttonRoot')
        local banUpgradeButton = item:Get('banUpgradeButton')
        local upgradeButton = item:Get('upgradeButton')
        local wearButton = item:Get('wearButton')
        local takeoffButton = item:Get('takeoffButton')

        local suitName = item:Get('suitName')
        local suitDesc = item:Get('suitDesc') --
        local suitNameInactived = item:Get('suitNameInactived')
        local suitDescInactived = item:Get('suitDescInactived')--
        local shareBtn = item:Get('shareBtn')
        local rectText_1=item:Get('text_1')
        local rectText_2=item:Get('text_2')

        suitName.gameObject:SetActive(false)
        suitDesc.transform.parent.gameObject:SetActive(false)
        suitNameInactived.gameObject:SetActive(false)
        suitDescInactived.transform.parent.gameObject:SetActive(false)

        buttonRoot.gameObject:SetActive(btnState~=0)
        shareBtn.gameObject:SetActive(not isTrialHero)

        shareBtn.onClick:RemoveAllListeners()
        shareBtn.onClick:AddListener(function()
            if entity then
                local ui_share = require "ui_share"
                local mq_common_pb = require "mq_common_pb"
                ui_share.Open(mq_common_pb.enSpeak_ShareEquip, entity.goodsSid)
            end
        end)


        local itemID = entity:GetGoodsID()
        local sigilCfg = game_scheme:Sigil_0(itemID)
        local position = sigilCfg.colourType
        equiped.gameObject:SetActive(hero)

        local extraCount = needAdd and 1 or 0
        if btnState == 1 then
            wearButton.gameObject:SetActive(false)
            upgradeButton.gameObject:SetActive(entity.numProp.equipStrLv < sigil_mgr.GetSigilMaxEnhanceLv())
            banUpgradeButton.gameObject:SetActive(entity.numProp.equipStrLv >= sigil_mgr.GetSigilMaxEnhanceLv())
            takeoffButton.gameObject:SetActive(true)
        elseif btnState == 2 then
            wearButton.gameObject:SetActive(true)
            upgradeButton.gameObject:SetActive(false)
            banUpgradeButton.gameObject:SetActive(false)
            takeoffButton.gameObject:SetActive(false)
        end

        local itemCfg = game_scheme:Item_0(itemID)
        if itemCfg then
            -- 印记词条展示
            local propGroup = sigil_mgr.GetEquipPropDesc(entity,entity.goodsID,entity.numProp.equipStrLv)
            local propText = ""
            for i=0,#propGroup do
                local data = propGroup[i]
                if data then
                    if i ~= #propGroup then
                        propText = propText..data.desc..'  <color=#28B66CFF>+'..data.num..'</color>'..'\n'
                    else
                        propText = propText..data.desc..'  <color=#28B66CFF>+'..data.num..'</color>'
                    end
                end
            end
            prop.text = propText
            name.text = lang.Get(itemCfg.nameKey)..' Lv.'..entity.numProp.equipStrLv
            local grade = sigilCfg.grade-50
            
            for i=1,6 do
                local star = starGroup.transform:GetChild(i-1)
                if star then
                    local isShow = grade >= i
                    star.gameObject:SetActive(isShow)
                end
            end
            --starGroup.spacing = -140+(grade*15)
            self.sigilSpriteAsset:GetSprite(itemCfg.icon, function(sprite)
                if self:IsValid() then
                    icon.enabled = true
                    icon.sprite = sprite
                end
            end)
            
            self.sigilSpriteAsset:GetSprite(itemCfg.icon2, function(sprite)
                if self:IsValid() then
                    iconBg.enabled = true
                    iconBg.sprite = sprite
                end
            end)

            local activedSuitID = nil -- 激活的取最高一套
            local inActivedSuitID = {} -- 非激活的取最低一套
            local suitID = sigilCfg.suitID
            if suitID and suitID ~= 0 then
                local suitCfg = game_scheme:EquipmentSet_0(suitID)
                if suitCfg then
                    for k,v in pairs(activedSuitData)do
                        -- 判断有无同类型的已激活套装
                        if sigilCfg.suitType == v.sigilCfg.suitType then
                            if sigilCfg.suitID <= v.sigilCfg.suitID then
                                activedSuitID = v.sigilCfg.suitID
                            end
                        end
                    end
                    if activedSuitID then
                        local data = allSuitData[activedSuitID]
                        suitName.gameObject:SetActive(true)
                        suitDesc.transform.parent.gameObject:SetActive(true)
                        suitName.text = lang.Get(suitCfg.condition)..'('..data.target..'/'..data.target..')'
                        suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                        --LayoutRebuilder.ForceRebuildLayoutImmediate(contentrect)
                       

                    else
                        local preSet = suitCfg.preSet.data
                        if allSuitData and util.get_len(allSuitData) > 0 then
                            if util.get_len(preSet) <= 0 then
                                -- 无前置套装 仅需展示自身套装情况
                                if allSuitData[suitID] then
                                    -- 已有同套装的已装备
                                    local data = allSuitData[suitID]
                                    if curSigilCfg and curSigilCfg.suitType == data.sigilCfg.suitType then
                                        -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1 譬如：4星换3星 3星部分之前是已激活 无需再+1
                                        if curSigilCfg.suitID >= data.sigilCfg.suitID then
                                            extraCount = 0
                                        end
                                    end
                                    local count = data.count + data.extraCount + extraCount -- +1是因为策划要求展示时加上自身 这件印记的数量
    
                                    suitName.gameObject:SetActive(count >= data.target)
                                    suitDesc.transform.parent.gameObject:SetActive(count >= data.target)
                                    suitNameInactived.gameObject:SetActive(count < data.target)
                                    suitDescInactived.transform.parent.gameObject:SetActive(count < data.target)
    
                                    if count >= data.target then
                                        -- 穿上这件就已激活
                                        suitName.text = lang.Get(suitCfg.condition)..'('..data.target..'/'..data.target..')'
                                        suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                                    else
                                        -- 仍然不能激活
                                        suitNameInactived.text = lang.Get(suitCfg.condition)..'('..count..'/'..data.target..')'
                                        suitDescInactived.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                                    end
                                else
                                    suitName.gameObject:SetActive(false)
                                    suitDesc.transform.parent.gameObject:SetActive(false)
                                    suitNameInactived.gameObject:SetActive(true)
                                    suitDescInactived.transform.parent.gameObject:SetActive(true)
                                    -- 仅自身一件
                                    -- 仅判断同套装会有漏洞 同类型的没判断
                                    if suitCfg.twoPiecePro ~= 0 then
                                        suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                        suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                    elseif suitCfg.fourPiecePro ~= 0 then
                                        suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                        suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                    elseif suitCfg.sixPiecePro ~= 0 then
                                        suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                        suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                    end
                                end
                            else
                                -- 无同套装的印记穿戴
                                -- 判断是否激活低档次套装

                                -- 判断自身是否满足 譬如：3件6星 目前穿3星
                                for k,v in pairs(allSuitData)do
                                    if v.sigilCfg.suitType == sigilCfg.suitType then
                                        if v.sigilCfg.suitID >= sigilCfg.suitID then
                                            local count = v.count+v.extraCount+extraCount
                                            local target = 0
                                            if v.suitCfg.twoPiecePro ~= 0 then
                                                target = 2
                                            elseif v.suitCfg.fourPiecePro ~= 0 then
                                                target = 4
                                            elseif v.suitCfg.sixPiecePro ~= 0 then
                                                target = 6
                                            end
                                            if count >= target then
                                                activedSuitID = sigilCfg.suitID
                                            end
                                        end
                                    end
                                end
                                if activedSuitID then
                                    -- 自身已激活
                                    suitName.gameObject:SetActive(true)
                                    suitDesc.transform.parent.gameObject:SetActive(true)
                                    local target = 0
                                    if suitCfg.twoPiecePro ~= 0 then
                                        target = 2
                                    elseif suitCfg.fourPiecePro ~= 0 then
                                        target = 4
                                    elseif suitCfg.sixPiecePro ~= 0 then
                                        target = 6
                                    end
                                    suitName.text = lang.Get(suitCfg.condition)..'('..target..'/'..target..')'
                                    suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                                else
                                    -- 需要判断前置套装情况
                                    for i=0,util.get_len(preSet) do
                                        local preSuitID = preSet[i]
                                        if preSuitID and preSuitID ~= 0 then
                                            local preSuitData = allSuitData[preSuitID]
                                            if preSuitData then
                                                -- 已穿戴低档次套装印记
                                                if curSigilCfg and curSigilCfg.suitType == preSuitData.sigilCfg.suitType then
                                                    -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                    if curSigilCfg.suitID >= preSuitData.sigilCfg.suitID then
                                                        extraCount = 0
                                                    else
                                                        extraCount = 1
                                                    end
                                                end
                                                local count = preSuitData.count+preSuitData.extraCount+extraCount
                                                if count >= preSuitData.target then
                                                    -- 满足激活条件
                                                    activedSuitID = preSuitID
                                                else
                                                    table.insert(inActivedSuitID, preSuitID)
                                                end
                                            else
                                                -- 仅自身一件
                                                table.insert(inActivedSuitID, preSuitID)
                                            end
                                        end
                                    end
                                    if activedSuitID then
                                        local activedSuitCfg = game_scheme:EquipmentSet_0(activedSuitID)
                                        if activedSuitCfg then
                                            suitName.gameObject:SetActive(true)
                                            suitDesc.transform.parent.gameObject:SetActive(true)
                                            local nameText = ''
                                            if activedSuitCfg.twoPiecePro ~= 0 then
                                                nameText = lang.Get(activedSuitCfg.condition)..'(2/2)'
                                            elseif activedSuitCfg.fourPiecePro ~= 0 then
                                                nameText = lang.Get(activedSuitCfg.condition)..'(4/4)'
                                            elseif activedSuitCfg.sixPiecePro ~= 0 then
                                                nameText = lang.Get(activedSuitCfg.condition)..'(6/6)'
                                            end
                                            suitName.text = nameText
                                            suitDesc.text = lang.Get(activedSuitCfg.suitlevel)..' '..lang.Get(activedSuitCfg.descID)
                                        end
                                    end
                                    if #inActivedSuitID > 0 then
                                        -- 展示最低等级的一套
                                        local minSuitID = suitID
                                        for i=1,#inActivedSuitID do
                                            local id = inActivedSuitID[i]
                                            if id then
                                                if allSuitData[id] ~= nil then
                                                    -- 若当前印记没有激活任何等级的套装效果
                                                    -- 则展示当前穿戴的同类套装印记中则展示当前穿戴的同类套装印记中
                                                    -- 最低星级那个印记可激活的最高等级套装最低星级那个印记可激活的最高等级套装
                                                    minSuitID = id
                                                    break
                                                end
                                            end
                                        end
                                        local minSuitData = allSuitData[minSuitID]
                                        if minSuitData then
                                            -- 身上有穿本套装类似的印记
                                            -- 若全员同星，只能满足自身套装
                                            if curSigilCfg and curSigilCfg.suitType == minSuitData.sigilCfg.suitType then
                                                -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                if curSigilCfg.suitID >= minSuitData.sigilCfg.suitID then
                                                    extraCount = 0
                                                else
                                                    extraCount = 1
                                                end
                                            end
                                            local count = minSuitData.count + minSuitData.extraCount + extraCount
                                            if count >= minSuitData.target then
                                                suitName.gameObject:SetActive(true)
                                                suitDesc.transform.parent.gameObject:SetActive(true)
        
                                                suitName.text = lang.Get(minSuitData.suitCfg.condition)..'('..minSuitData.target..'/'..minSuitData.target..')'
                                                suitDesc.text = lang.Get(minSuitData.suitCfg.suitlevel)..' '..lang.Get(minSuitData.suitCfg.descID)
                                            else
                                                suitNameInactived.gameObject:SetActive(true)
                                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                                                suitNameInactived.text = lang.Get(minSuitData.suitCfg.condition)..'('..count..'/'..minSuitData.target..')'
                                                suitDescInactived.text = lang.Get(minSuitData.suitCfg.suitlevel)..' '..lang.Get(minSuitData.suitCfg.descID)
                                            end
                                        else
                                            
                                            local selfSuitData = allSuitData[suitCfg.suitID]
                                            if selfSuitData then
                                                -- 有同类型的其他印记
                                                if curSigilCfg and curSigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                                    -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                    if curSigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                                        extraCount = 0
                                                    else
                                                        extraCount = 1
                                                    end
                                                end
                                                local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                                                if count >= selfSuitData.target then
                                                    suitName.gameObject:SetActive(true)
                                                    suitDesc.transform.parent.gameObject:SetActive(true)
                    
                                                    suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                                    suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                                else
                                                    suitNameInactived.gameObject:SetActive(true)
                                                    suitDescInactived.transform.parent.gameObject:SetActive(true)
                    
                                                    suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                                    suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                                end
                                            else
                                                -- 为第一件
                                                suitNameInactived.gameObject:SetActive(true)
                                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                                                if suitCfg.twoPiecePro ~= 0 then
                                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                                elseif suitCfg.fourPiecePro ~= 0 then
                                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                                elseif suitCfg.sixPiecePro ~= 0 then
                                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                                end
                                            end
                                        end
                                    else
                                        local selfSuitData = allSuitData[suitCfg.suitID]
                                        if selfSuitData then
                                            -- 有同类型的其他印记
                                                if curSigilCfg and curSigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                                    -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                    if curSigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                                        extraCount = 0
                                                    else
                                                        extraCount = 1
                                                    end
                                                end
                                            local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                                            if count >= selfSuitData.target then
                                                suitName.gameObject:SetActive(true)
                                                suitDesc.transform.parent.gameObject:SetActive(true)
                
                                                suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                                suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                            else
                                                suitNameInactived.gameObject:SetActive(true)
                                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                
                                                suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                                suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                            end
                                        else
                                            -- 展示自身
                                            suitNameInactived.gameObject:SetActive(true)
                                            suitDescInactived.transform.parent.gameObject:SetActive(true)
                                        
                                            -- 仅自身一件
                                            if suitCfg.twoPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            elseif suitCfg.fourPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            elseif suitCfg.sixPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            end
                                        end
                                    end
                                end
                            end
                        else
                            -- 展示自身
                            local selfSuitData = allSuitData[suitCfg.suitID]
                            if selfSuitData then
                                -- 有同类型的其他印记
                                if curSigilCfg and curSigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                    -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                    if curSigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                        extraCount = 0
                                    else
                                        extraCount = 1
                                    end
                                end
                                local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                                if count >= selfSuitData.target then
                                    suitName.gameObject:SetActive(true)
                                    suitDesc.transform.parent.gameObject:SetActive(true)
    
                                    suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                    suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                else
                                    suitNameInactived.gameObject:SetActive(true)
                                    suitDescInactived.transform.parent.gameObject:SetActive(true)
    
                                    suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                    suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                end
                            else
                                -- 仅自身一件
                                suitNameInactived.gameObject:SetActive(true)
                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                                if suitCfg.twoPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                elseif suitCfg.fourPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                elseif suitCfg.sixPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                end
                            end
                        end
                    end
                end
            end
            if itemCfg.icon2para and not string.empty(itemCfg.icon2para) then
                local arrData = string.split(itemCfg.icon2para, '#', tonumber)
                -- self.iconImg2Rect.anchoredPosition = {x=arrData[1],y=arrData[2]}
                iconBg.gameObject.transform.localEulerAngles = {x=0,y=0,z=arrData[3]}
                if #arrData>=4 and tonumber(arrData[4]) == 1 then --第四个参数使用x轴的镜像翻转,1是翻转
                    iconBg.gameObject.transform.localScale = {x = -1,y = 1,z = 1}
                end
            end
        end
        local suitCfg = game_scheme:EquipmentSet_0(itemID)
        if suitCfg then
            suitDesc.text = lang.Get(suitCfg.descID)
        end

        local itemRect = item.transform:GetComponent(typeof(RectTransform))
       

        if rectText_1 then
           -- print("suitDesc.preferre", suitDesc.preferredHeight)
            rectText_1:SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, suitDesc.preferredHeight)
        end
        if rectText_2 then
           -- print("suitDescInactived.preferre", suitDescInactived.preferredHeight)
            rectText_2:SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, suitDescInactived.preferredHeight)
        end

        LayoutRebuilder.ForceRebuildLayoutImmediate(itemRect)
    end
end
-------------------------------------end-------------------------------------


function UISigilEquip:IsPercentageProp(type)
    return not (type == prop_pb.enPro_HP or type == prop_pb.enPro_Attack or type == prop_pb.enPro_Defend or type == prop_pb.enPro_Speed or type == prop_pb.enPro_Extra_Skill_Points_Rate or type == prop_pb.enPro_Energy)
end

--@region WindowOnHide
--[[界面隐藏时调用]]
function UISigilEquip:OnHide()

end



--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UISigilEquip:SetInputParam(p)
	self.inputParam = p
    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end



--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UISigilEquip:BuildUpdateData()


end



--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UISigilEquip:UpdateUIPage()
	self:BuildUpdateData()


end



--@region WindowClose
function UISigilEquip:Close()
    net_route.UnregisterMsgHandlers(MessageTable)

    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    
	if self.sigilSpriteAsset then
		self.sigilSpriteAsset:Dispose()
		self.sigilSpriteAsset = nil
	end
	if self.cardSpriteAsset then
		self.cardSpriteAsset:Dispose()
		self.cardSpriteAsset = nil
	end
    if self.itemUI then
        for k,v in pairs(self.itemUI)do
            v:Dispose()
        end
    end
    if self.sigilList then
        self.sigilList:ItemsDispose()
    end
    
    if self.itemTicker then
        self.itemTicker:Dispose()
        self.itemTicker = nil
    end
    heroEntity = nil
    curPage = nil
    curSelectPos = nil
    curSelectSid = nil
    curSelectSuit = nil
    curSelectSuitID = nil
    showAllPos = true
    curSigilPage = nil
    curSelectSuitItem = nil
    curSelectitem=nil
    activedSuitData = nil -- 当前已激活套装数据
    allSuitData = nil -- 当前装备所有套装数据（包含未激活）
    curSelectSid = nil
    heroShareData = nil
    isTrialHero = nil
    replaceEntity= nil
    activedSuitData= nil
    selectedIndex= nil
    heroCount= nil
    sigilData = {}
	self.__base:Close()
    window = nil
end

function UISigilEquip:SigilDetail(data,pos)
    if curSelectPos and curSelectPos == pos then
        if data then
            -- 查看详情
            self:SetItemUI(data,heroEntity,1,true)
        else
            if heroShareData then
                self:ShowShareItemInfo(pos)
            end
        end
    else
        curSelectPos = pos
        curSelectSid = nil
        showAllPos = false
        if data then
            -- 查看详情
            self:SetItemUI(data,heroEntity,1,true)
        else
            if heroShareData then
                self:ShowShareItemInfo(pos)
            end
        end
        self:UpdateBtnGroupState()
        self:UpdateSigilSlotUI()
    end
end

function UISigilEquip:ShowPropPopup(value)
    self.propPanel.gameObject:SetActive(value)
    if value then
        self:UpdatePropPanelUI()
    end
end

--------------------------------构造数据---------------------------------------
-- 构造套装数据
function BuildSuitData()
    local data = {}
    local allSuitCfg = sigil_mgr.GetAllSuitTypeCfg()
	if allSuitCfg then
		for k,value in pairs(allSuitCfg)do
			local v = {}
            v.suitID = value.suitID
			v.suitType = value.suitType
			table.insert(data, v)
		end
		table.sort(data,function (x,y)
			return x.suitID < y.suitID
		end)
	
		local first = {isAll = true}
		table.insert( data, 1, first)
	end
    return data
end

-- 构造印记数据
function UISigilEquip:BuildListData()
    local dataList = {}
    local sourceEntity = nil
    if showAllPos then
        sourceEntity = {}
        for k,v in pairs(sigilData)do
            if v then
                sourceEntity[v.goodsSid] = v
            end
        end
    else
        if curSelectPos then
            sourceEntity = sigilData[curSelectPos]
        end
    end
	local allSigil,packSigil,packSigilOfSid = player_mgr.GetAllSigil()
    local cacheSigilCfg = {}
    local cacheEqCfg = {}
	for k,entity in pairs(allSigil)do
		if entity then
            local isExcept = false
            if showAllPos then
                -- 需要过滤所有当前穿戴
                if sourceEntity and sourceEntity[entity.goodsSid] then
                    isExcept = true
                end
            else
                -- 只需过滤对应部位当前穿戴
                if sourceEntity and sourceEntity.goodsSid == entity.goodsSid then
                    isExcept = true
                end
            end
            if not cacheSigilCfg[entity:GetGoodsID()] then
                cacheSigilCfg[entity:GetGoodsID()] = game_scheme:Sigil_0(entity:GetGoodsID())
            end
            local sigilCfg = cacheSigilCfg[entity:GetGoodsID()]
            if not isExcept and (not curSelectSuit or curSelectSuit == sigilCfg.suitType) then
                local data = {}
                data.entity = entity
                data.state = 0
                if sigilCfg then
                    data.grade = sigilCfg.grade-50
                    data.pos = sigilCfg.colourType
                    data.suitID = sigilCfg.suitID
                    data.suitType = sigilCfg.suitType
                    
                    if not cacheEqCfg[sigilCfg.grade] then
                        cacheEqCfg[sigilCfg.grade] = {}
                    end
                    
                    local equipStrLv = entity:GetEquipStrLv()
                    if not cacheEqCfg[sigilCfg.grade][equipStrLv] then
                        cacheEqCfg[sigilCfg.grade][equipStrLv] = game_scheme:EquipEnhance_0(sigilCfg.grade,equipStrLv,equipment_mgr.ENHANCE_TYPE.sigil)
                    end
                    data.enhanceCfg = cacheEqCfg[sigilCfg.grade][equipStrLv]
                    data.cfg = sigilCfg
                    if curSelectPos and curSelectPos ~= 0 then
                        if curSelectPos == sigilCfg.colourType then
                            local itemSid = entity:GetGoodsSid()
                            if not packSigilOfSid[itemSid] then
                                -- 已装备
                                data.state = 2
                                data.heroSID = player_mgr.GetHeroOfSigilSkep(entity:GetSkepID())
                                data.heroEntity = player_mgr.GetPartDataCacheBySid(data.heroSID)
                            end
                            data.attachData = {
                                selected = false,
                            }
                            dataList[#dataList+1] = data
                        end
                    else
                        local itemSid = entity:GetGoodsSid()
                        if not packSigilOfSid[itemSid] then
                            -- 已装备
                            data.state = 2
                            data.heroSID = player_mgr.GetHeroOfSigilSkep(entity:GetSkepID())
                            data.heroEntity = player_mgr.GetPartDataCacheBySid(data.heroSID)
                        end
                        data.attachData = {
                            selected = false,
                        }
                        dataList[#dataList+1] = data
                    end
                else
                    log.Error("sigil ",entity:GetGoodsID()," no sigil config")
                end
            end
		end
	end
    table.sort( dataList, sigil_mgr.SortHigher2Lower)
    return dataList
end
--------------------------------end-------------------------------------------
-- 更新部位的穿戴情况
function UISigilEquip:UpdateSigilSlotUI(skipUpdate)
    for i=1,6 do
        local item = self['sigilItem'..i]
        if item then
            if curSelectPos and curSelectPos == i then
                item:Get('selected'):SetActive(true)
            else
                item:Get('selected'):SetActive(false)
            end
        end
    end

    if not skipUpdate then
        self:UpdateSigilPanelUI()
    end
end

-- 更新印记面板
function UISigilEquip:UpdateSigilPanelUI()
    if not heroEntity or not heroEntity:GetStarLv() then
        return
    end
    if not heroShareData and curSigilPage then
        self.sigilListPanel.gameObject:SetActive(curSigilPage == SigilPageType.Sigil)
        self.selectSuitPanel.gameObject:SetActive(curSigilPage == SigilPageType.Suit)
        local unlockCondition = game_scheme:InitBattleProp_0(709).szParam.data[0]
        unlockCondition = tonumber(unlockCondition)
        
        local levelLimit = game_scheme:InitBattleProp_0(709).szParam.data[1]
        local curMapLv = laymain_data.GetPassLevel()
        
        self.sigilLockPanel.gameObject:SetActive(heroEntity:GetStarLv() < unlockCondition or curMapLv < levelLimit)
        if self.sigilLockPanel.gameObject.activeSelf then
            self.sigilNoneTip.gameObject:SetActive(false)
        end

        if heroEntity:GetStarLv() < unlockCondition or curMapLv < levelLimit then
            local heroStarStr = lang.Get(hero_mgr.Hero_Star_Desc[unlockCondition])
            self.sigilLockText.text = string.format( lang.Get(230025),heroStarStr)
        end

        if curSigilPage == SigilPageType.Sigil then
            -- 印记列表
            if heroEntity:GetStarLv() >= unlockCondition then
                local listData = self:BuildListData()
                self.sigilList.data = listData
                -- self.sigilList.pageSize = #listData > 36 and 36 or #listData
                self.sigilList:Refresh(0,-1)
                self.sigilNoneTip.gameObject:SetActive(#listData<=0)
            end
        else
            -- 套装面板
            local listData = BuildSuitData()
            self.suitList.data = listData
            self.suitList:Refresh(0,-1)
        end
    end
end

-- 更新属性面板
function UISigilEquip:UpdatePropPanelUI()
    local propList = {}
    for k,v in pairs(sigilData)do
        local entity = v
        if entity then
            local sigilProp = entity:GetSigilProps()
            self:GetProp(sigilProp.baseProp1,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp2,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp3,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.baseProp4,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.extendProp1,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.extendProp2,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.extendProp3,sigilProp.enhanceLv,propList)
            self:GetProp(sigilProp.extendProp4,sigilProp.enhanceLv,propList)
        end
    end
    for index=1,22 do
        local rect = self.propPanel:Get('prop'..index)
        if rect then
            rect.gameObject:SetActive(false)
        end
    end
    local index = 1
    for k,v in pairs(propList)do
        if v and v ~= 0 then
            local propValue = v
            local proCfg = game_scheme:ProToLang_0(k)
            local propName = ""
            if proCfg then
                propName = lang.Get(proCfg.iLangId)
            end
            local isPercentage = self:IsPercentageProp(k)
            local percentage = isPercentage and "%" or ""
            local rect = self.propPanel:Get('prop'..index)
            local name = rect.transform:Find("propName")
            local value = rect.transform:Find("value")
            if rect then
                rect.gameObject:SetActive(true)
            end
            if name then
                name = name.transform:GetComponent(typeof(Text))
                name.text = propName
            end
            if value then
                value = value.transform:GetComponent(typeof(Text))
                value.text = "+"..((isPercentage and (propValue / 100)) or propValue)..percentage
            end
            index = index + 1
        end
    end
    local propList_len = util.get_len(propList)
    self.nonePropTip.gameObject:SetActive(propList_len<=0)
    --self.propPanel:Get('propBg1').gameObject:SetActive(propList_len>2)
    --self.propPanel:Get('propBg2').gameObject:SetActive(propList_len>6)
    --self.propPanel:Get('propBg3').gameObject:SetActive(propList_len>10)
    --self.propPanel:Get('propBg4').gameObject:SetActive(propList_len>14)
    --self.propPanel:Get('propBg5').gameObject:SetActive(propList_len>18)
end

function UISigilEquip:ShowSuitTipItem(value)
    self.suitTipRect.gameObject:SetActive(value)
end

function UISigilEquip:GetSuitLvCountByType( type )
    local suitLv = {}
    for k,v in pairs(sigilData)do
        local entity = v
        if entity then
            local sigilID = entity:GetGoodsID()
            local prop = entity:GetSigilProps()
            local sigilCfg = game_scheme:Sigil_0(sigilID)
            local suitCfg = game_scheme:EquipmentSet_0(sigilCfg.suitID)
            if type == suitCfg.suitType then
                suitLv[sigilCfg.suitID] = suitLv[sigilCfg.suitID] and suitLv[sigilCfg.suitID] + 1 or 1
            end
        end
    end
    return suitLv
end

function UISigilEquip:SuitDetail(data)
    self:ShowSuitTipItem(true)
    local icon = self.suitTipItem:Get('icon')
    local name = self.suitTipItem:Get('name')
    local count1 = self.suitTipItem:Get('count1')
    local desc1 = self.suitTipItem:Get('desc1')
    local count2 = self.suitTipItem:Get('count2')
    local desc2 = self.suitTipItem:Get('desc2')
    local count3 = self.suitTipItem:Get('count3')
    local desc3 = self.suitTipItem:Get('desc3')
    local iconBg = self.suitTipItem:Get('iconBg')

    iconBg.gameObject:SetActive(false)
    name.text = lang.Get(data.suitCfg.nameID)
    local suitLv = self:GetSuitLvCountByType(data.suitCfg.suitType)
    local allData = sigil_mgr.GetAllSigilCfg()
    local setDesCount = function (desc,count,suitdata,index)
        local suitcount = 0
        for k,v in pairs(suitLv) do
            if suitdata.suitID <= k then
                suitcount  = suitcount + v
            end
        end
        --  --print("suitcount:",suitcount)
        local color = suitdata.suitlevel == data.suitCfg.suitlevel and "%s" or '<color=#B7B7B7>%s</color>'
        local curCount = 0
        if suitdata.suitlevel == data.suitCfg.suitlevel then
            curCount = data.count + data.extraCount
        else
            curCount = suitcount or 0
        end
        local suitData = allData[suitdata.suitID]
        local minStar = suitData.minStar
        --  suitdata.suitlevel == data.suitCfg.suitlevel and data.count + data.extraCount or 0
        desc.text = string.format(color,lang.Get(suitdata.descID)) 
        count.text = string.format(color,lang.Get(suitdata.suitlevel).." "..string.format(lang.Get(230090),minStar)..'('..(curCount)..'/'..data.target..')')
        local oversea_res = require "oversea_res"
        oversea_res.SetTextParam(desc, {lang.DE, lang.FR}, {fontSize = 20})
        oversea_res.SetTextParam(count, {lang.DE, lang.FR}, {fontSize = 20})
    end

    for i,v in ipairs(data.suitIdList) do
        if i == 1 then
            setDesCount(desc1,count1,v,i)
        elseif i == 2 then
            setDesCount(desc2,count2,v,i)
        elseif i == 3 then
            setDesCount(desc3,count3,v,i)
        end
    end

    self.sigilSpriteAsset:GetSprite(data.suitCfg.iconID, function(sprite)
        if self:IsValid() then
            icon.enabled = true
            icon.sprite = sprite
            icon:SetNativeSize()
        end
    end)
    -- self.sigilSpriteAsset:GetSprite(data.suitCfg.iconID2, function(sprite)
    --     iconBg.enabled = true
    --     iconBg.sprite = sprite
    --     iconBg:SetNativeSize()
    -- end)
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UISigilEquip:SubscribeEvents()
    self.sigilBtnEvent_1 = function()
        self:SigilDetail(sigilData[1],1)
    end
    self.sigilBtnEvent_2 = function()
        self:SigilDetail(sigilData[2],2)
    end
    self.sigilBtnEvent_3 = function()
        self:SigilDetail(sigilData[3],3)
    end
    self.sigilBtnEvent_4 = function()
        self:SigilDetail(sigilData[4],4)
    end
    self.sigilBtnEvent_5 = function()
        self:SigilDetail(sigilData[5],5)
    end
    self.sigilBtnEvent_6 = function()
        self:SigilDetail(sigilData[6],6)
    end
    for i=1,6 do
        local btn = self["stoneItem"..i]
        local callback = self["sigilBtnEvent_"..i]
        if btn and callback then
            btn.onClick:AddListener(callback)
        end
    end
    
  
    
    self.unloadallSigilEvent = function()
        if sigilData  then  --卸载所有位置的印记
            local emptycount = 0
            for i = 1, 6 do
                -- 脱下
                local sourceEntity = sigilData[i]
                if sourceEntity then
                    local prop = sourceEntity
                    local data = {}
                    data.bwear = 0
                    data.newSigilSid = 0
                    data.oldSigilSid = sourceEntity:GetGoodsSid()
                    data.sigilpos = sourceEntity:GetSkepPlace()
                    local heroSid = sigil_mgr.GetCurHeroSid()
                    sigil_mgr.Request_ReplaceSigil(heroSid,{data})
                    self:SetItemUI(sourceEntity,nil,nil,false)
                else
                    emptycount = emptycount+1
                    -- 异常
                     --log.Error(" ------------印记穿戴异常------------")
                    
                end
            end
            if emptycount==6 then
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(230099))
            end
            
        end
    end
    self.unloadallBtn.onClick:AddListener( self.unloadallSigilEvent ) 

    local getEquipmentSetBysuitType = function (suitType)
        local suitIdList = {}
        for i=0,game_scheme:EquipmentSet_nums()-1 do
            local cfg = game_scheme:EquipmentSet(i)
            if suitType and suitType > 0 and suitType == cfg.suitType then
                table.insert(suitIdList,cfg)
            end
        end
        table.sort( suitIdList, function (x,y)
			return x.suitID < y.suitID
		end)
        return suitIdList
    end 

    self.suitBtnEvent_1 = function()
        local suitType = game_scheme:EquipmentSet_0(self.suitData[1].suitID).suitType
        self.suitData[1]["suitIdList"] = getEquipmentSetBysuitType(suitType)
        self:SuitDetail(self.suitData[1])
        LayoutRebuilder.ForceRebuildLayoutImmediate(self.suitTipBg)
    end
    self.suitBtnEvent_2 = function()
        local suitType = game_scheme:EquipmentSet_0(self.suitData[2].suitID).suitType
        self.suitData[2]["suitIdList"] = getEquipmentSetBysuitType(suitType)
        self:SuitDetail(self.suitData[2])
        LayoutRebuilder.ForceRebuildLayoutImmediate(self.suitTipBg)
    end
    self.suitBtnEvent_3 = function()
        local suitType = game_scheme:EquipmentSet_0(self.suitData[3].suitID).suitType
        self.suitData[3]["suitIdList"] = getEquipmentSetBysuitType(suitType)
        self:SuitDetail(self.suitData[3])
        LayoutRebuilder.ForceRebuildLayoutImmediate(self.suitTipBg)
    end
    for i=1,3 do
        local btn = self["suitItem"..i]
        local callback = self["suitBtnEvent_"..i]
        if btn and callback then
            btn.onClick:AddListener(callback)
        end
    end

    self.undressEvent = function ()
        if util.get_len(sigilData) <= 0 then
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(230010))
            return
        end
        local sigilGroup = {}
        for k,v in pairs(sigilData)do
            if v then
                local data = {}
                data.bwear = 0
                data.newSigilSid = 0
                data.oldSigilSid = v.goodsSid
                data.sigilpos = v:GetSkepPlace()
                table.insert(sigilGroup,data)
            end
        end
        local heroSid = sigil_mgr.GetCurHeroSid()
        sigil_mgr.Request_ReplaceSigil(heroSid,sigilGroup)
    end
    self.widget_table["undressBtn"].event_name = "undressEvent"

    self.itemCloseEvent = function ()
        curSelectSid = nil
        if curSelectSuitItem then
            -- curSelectSuitItem:SetActive(false)
            -- curSelectSuitItem = nil
        end
        self:UpdateSigilPanelUI()
        self:SetItemUI()
    end
    self.widget_table["itemCloseBtn"].event_name = "itemCloseEvent"


    -- TODO 收到英雄印记数据变化 更新界面
    self.updateSigilData = function (name)
        if window and window:IsValid() then
            window:UpdateSuitData()
            window:UpdateSigilUI()
            window:UpdateSigilSlotUI(true)
            window:UpdatePanelState()
        end
    end
    self:RegisterEvent(event.REPLACE_SIGIL_RESPONSE, self.updateSigilData)
    self:RegisterEvent(event.ENHANCE_SIGIL_RESPONSE, self.updateSigilData)
    self:RegisterEvent(event.DEVOUR_SIGIL_RESPONSE, self.updateSigilData)
    -- self:RegisterEvent(event.UPDATE_HERO_NUM_PROP, self.updateSigilData)

    self.changeHeroEntity = function ()
        if window and window:IsValid() then
            curSelectSid = nil
            curSelectPos = nil
            showAllPos = true
            curSigilPage = SigilPageType.Sigil
            window:UpdateHeroIcon()
            window:UpdateSuitData()
            window:UpdateSigilUI()
            window:UpdateSigilSlotUI(true)
            window:UpdatePanelState()
        end
    end
    self:RegisterEvent(event.UPDAGE_CURRENT_HEROSID, self.changeHeroEntity)

    self.hideSuitTipEvent = function ()
        self:ShowSuitTipItem(false)
    end
    self.widget_table["suitTipCloseBtn"].event_name = "hideSuitTipEvent"
    

	self.helpBtnEven = function()
		local ui_help = require "ui_help"
		ui_help.ShowWithDate(131)
	end
    self.widget_table["helpBtn"].event_name = "helpBtnEven"

    self.lineupBtnEven = function ()
        local ui_lineup_gift = require "ui_lineup_gift"
        ui_lineup_gift.SetGiftType(ui_lineup_gift.GIFT_TYPE.sigil)
        ui_window_mgr:ShowModule("ui_lineup_gift")
    end
    self.widget_table["lineupBtn"].event_name = "lineupBtnEven"

    self.sigilToggleEvent = function ()
		if self.sigilToggle.isOn then
            self:UpdatePanelState(PageType.SigilPanel)
        end
    end
	self.sigilToggle.onValueChanged:AddListener(self.sigilToggleEvent) 

    self.propToggleEvent = function ()
		if self.propToggle.isOn then
            self:UpdatePanelState(PageType.PropPanel)
        end
    end
	self.propToggle.onValueChanged:AddListener(self.propToggleEvent) 

    self.showAllPosEvent = function ()
        curSelectSid = nil
        curSelectPos = nil
        showAllPos = true
        curSigilPage = SigilPageType.Sigil
        self:UpdateBtnGroupState()
        self:UpdateSigilSlotUI()
        self:SetItemUI()
    end
    self.widget_table["showAllPosBtn"].event_name = "showAllPosEvent"
    
    self.showSuitEvent = function ()
        self:ShowSuitPanel(true)
        self:SetItemUI()
    end
    self.widget_table["suitBtn"].event_name = "showSuitEvent"

    self.replaceEvent = function ()
        local data = {}
        local newSigilSid = replaceEntity and replaceEntity:GetGoodsSid() or 0
        if not newSigilSid then
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(230023))
            return
        end
        local pos = game_scheme:Sigil_0(replaceEntity.goodsID).colourType
        local sourceEntity = sigilData[pos]
        local oldSigilSid = 0
        if sourceEntity then
            oldSigilSid = sourceEntity:GetGoodsSid()
        end
        if self.targetHeroEntity then
            -- 替换其他英雄的印记
            local req = {tarherosid = sigil_mgr.GetCurHeroSid(),
            tarsigilsid = oldSigilSid,
            tarsigilpos = pos,
            srcherosid = self.targetHeroEntity.heroSid,
            srcsigilsid = newSigilSid,
            srcsigilpos = pos
            }
            sigil_mgr.Request_ReplaceSigilBetweenPal(req)
        else
            table.insert( data,{bwear=1,newSigilSid = newSigilSid,oldSigilSid=oldSigilSid,sigilpos=pos})
            sigil_mgr.Request_ReplaceSigil(sigil_mgr.GetCurHeroSid(),data)
        end
        
        self:SetItemUI(replaceEntity, nil, nil, false)

        local _heroEntity = player_mgr.GetPalPartDataBySid(sigil_mgr.GetCurHeroSid())
        local json = require "dkjson"
        local json_str = json.encode({
            Sigil_id = replaceEntity:GetGoodsID(),
            hero_id = _heroEntity:GetHeroID()
                        })
        event.Trigger(event.GAME_EVENT_REPORT, "Sigil_dress", json_str) --神器获取
        ui_window_mgr:UnloadModule("ui_sigil_equip_select")
    end
    self.wearItem:Get('wearButton').onClick:AddListener(self.replaceEvent)
    self.replaceItem1:Get('wearButton').onClick:AddListener(self.replaceEvent)
    self.replaceItem2:Get('wearButton').onClick:AddListener(self.replaceEvent)

    self.upgradeEvent = function ()
        -- 升级
        local sourceEntity = sigilData[curSelectPos]
        local ui_sigil_equip_upgrade = require "ui_sigil_equip_upgrade"
        ui_sigil_equip_upgrade.SetSigilData(sourceEntity)
        ui_window_mgr:ShowModule("ui_sigil_equip_upgrade")
        self:SetItemUI(sourceEntity,nil,nil,false)
    end
    self.wearItem:Get('upgradeButton').onClick:AddListener(self.upgradeEvent)
    self.replaceItem1:Get('upgradeButton').onClick:AddListener(self.upgradeEvent)
    self.replaceItem2:Get('upgradeButton').onClick:AddListener(self.upgradeEvent)

    self.takeoffEvent = function ()
        -- 脱下
        local sourceEntity = sigilData[curSelectPos]
        if sourceEntity then
            local prop = sourceEntity
            local data = {}
            data.bwear = 0
            data.newSigilSid = 0
            data.oldSigilSid = sourceEntity:GetGoodsSid()
            data.sigilpos = sourceEntity:GetSkepPlace()
            local heroSid = sigil_mgr.GetCurHeroSid()
            sigil_mgr.Request_ReplaceSigil(heroSid,{data})
            self:SetItemUI(sourceEntity,nil,nil,false)
        else
            -- 异常
            -- log.Error(" ------------印记穿戴异常------------")
        end
    end
    self.wearItem:Get('takeoffButton').onClick:AddListener(self.takeoffEvent)
    self.replaceItem1:Get('takeoffButton').onClick:AddListener(self.takeoffEvent)
    self.replaceItem2:Get('takeoffButton').onClick:AddListener(self.takeoffEvent)

    self.leftArrowClickEvent = function ()
        self:LeftArrowEvent()
    end
    self.widget_table["leftArrow"].event_name = "leftArrowClickEvent"

    self.rightArrowClickEvent = function ()
        self:RightArrowEvent()
    end
    self.widget_table["rightArrow"].event_name = "rightArrowClickEvent"
end



--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UISigilEquip:UnsubscribeEvents()
    for i=1,6 do
        local btn = self["stoneItem"..i]
        local callback = self["sigilBtnEvent_"..i]
        if btn and callback then
            btn.onClick:RemoveListener(callback)
        end
    end

    for i=1,3 do
        local btn = self["suitItem"..i]
        local callback = self["suitBtnEvent_"..i]
        if btn and callback then
            btn.onClick:RemoveListener(callback)
        end
    end

    self.wearItem:Get('wearButton').onClick:RemoveListener(self.replaceEvent)
    self.replaceItem1:Get('wearButton').onClick:RemoveListener(self.replaceEvent)
    self.replaceItem2:Get('wearButton').onClick:RemoveListener(self.replaceEvent)

    self.wearItem:Get('upgradeButton').onClick:RemoveListener(self.upgradeEvent)
    self.replaceItem1:Get('upgradeButton').onClick:RemoveListener(self.upgradeEvent)
    self.replaceItem2:Get('upgradeButton').onClick:RemoveListener(self.upgradeEvent)

    self.wearItem:Get('takeoffButton').onClick:RemoveListener(self.takeoffEvent)
    self.replaceItem1:Get('takeoffButton').onClick:RemoveListener(self.takeoffEvent)
    self.replaceItem2:Get('takeoffButton').onClick:RemoveListener(self.takeoffEvent)

    self.unloadallBtn.onClick:RemoveListener( self.unloadallSigilEvent )
end

-- 更新当前套装数据
function UISigilEquip:UpdateSuitData()
    activedSuitData,allSuitData = hero_mgr.GetActiveSigilSuitDataByEntity(heroEntity)
    selectedIndex,heroCount = sigil_mgr.GetCurHeroIndex()
    self:UpdateArrowState()
end


-----------------------------------------分享相关-------------------------------------
-- 【查看他人】更新套装数据
function UISigilEquip:UpdateShareSuitData()
    activedSuitData,allSuitData = sigil_mgr.GetShareSuitData(heroShareData)
    self:UpdateArrowState()
end

function UISigilEquip:UpdateShareSigilUI()
    if not heroShareData then
        return
    end

    self.shareSigilData = {}
    local equipInfo = nil
    if heroShareData.equipinfo then
        equipInfo = heroShareData.equipinfo
    elseif heroShareData.equip then
        equipInfo = heroShareData.equip
    end
    if equipInfo then
        for k,v in ipairs(equipInfo)do
            local itemID = v.equipid or v.goodsId
            local _sigilData = game_scheme:Item_0(itemID)
            if _sigilData then
                if _sigilData.type == item_data.Item_Type_Enum.Sigil then
                    local sigilCfg = game_scheme:Sigil_0(itemID)
                    if sigilCfg then
                        local pos = sigilCfg.colourType
                        self.shareSigilData[pos] = {info = v,sigilCfg = sigilCfg,itemCfg = _sigilData}
                    end
                end
            end
        end
    end


    for i=1,6 do
        local data = self.shareSigilData[i]
        local sigilItem = self['sigilItem'..i]
        if sigilItem then
            if data then
                local icon = sigilItem:Get("icon")
                local starGroup = sigilItem:Get("starGroup")
                local level = sigilItem:Get("level")
                local mask = sigilItem:Get("mask")
                local activedBg = sigilItem:Get("activedBg")
                local reddot = sigilItem:Get("reddot")
                local activeBgRect = sigilItem:Get("activeBgRect")
                local blue = sigilItem:Get("blue")
                local yellow = sigilItem:Get("yellow")
                local white = sigilItem:Get("white")
                local starCanvas = sigilItem:Get("starCanvas")
        
                blue:SetActive(false)
                yellow:SetActive(false)
                white:SetActive(false)

                reddot.gameObject:SetActive(false)
                local itemCfg = data.itemCfg

                level.gameObject:SetActive(true)
                starGroup.gameObject:SetActive(true)
                mask.gameObject:SetActive(true)
                local sigilCfg = data.sigilCfg
                local grade = sigilCfg.grade-50
                for i=0,starGroup.gameObject.transform.childCount-1 do
                    local star = starGroup.gameObject.transform:GetChild(i)
                    if star then
                        star.gameObject:SetActive(grade >= i+1)
                    end
                end
                --starHGroup.spacing = -140+(grade*15)
                starCanvas.sortingOrder = window.curOrder + 1
                level.text = '+'..data.info.lv

                self.sigilSpriteAsset:GetSprite(itemCfg.icon, function(sprite)
                    if self:IsValid() then
                        icon.enabled = true
                        icon.sprite = sprite
                        icon:SetNativeSize()
                    end
                end)
                self.sigilSpriteAsset:GetSprite(itemCfg.icon2, function(sprite)
                    if self:IsValid() then
                        activedBg.enabled = true
                        activedBg.sprite = sprite
                        activedBg.gameObject:SetActive(true)
                        -- activedBg:SetNativeSize()
                    end
                end)

                if itemCfg.icon2para and not string.empty(itemCfg.icon2para) then
                    local arrData = string.split(itemCfg.icon2para, '#', tonumber)
                    activeBgRect.gameObject.transform.localEulerAngles = {x=0,y=0,z=arrData[3]}
                    if #arrData>=4 and tonumber(arrData[4]) == 1 then --第四个参数使用x轴的镜像翻转,1是翻转
                        activeBgRect.gameObject.transform.localScale = {x = -1,y = 1,z = 1}
                    else
                        activeBgRect.gameObject.transform.localScale = {x = 1,y = 1,z = 1}
                    end
                end

                if activedSuitData then
                    local suitCfg = game_scheme:EquipmentSet_0(sigilCfg.suitID)
                    local curSuitLv = nil
                    if suitCfg then
                        for k,v in pairs(activedSuitData)do
                            -- 判断有无同类型的已激活套装
                            if sigilCfg.suitType == v.sigilCfg.suitType then
                                if sigilCfg.suitID >= v.sigilCfg.suitID then
                                    curSuitLv = util.get_len(v.suitCfg.preSet.data)
                                end
                            end
                        end
                        blue:SetActive(curSuitLv == 0)
                        yellow:SetActive(curSuitLv == 1)
                        white:SetActive(curSuitLv == 2)
                    end
                end
            else
                local red = sigilItem:Get("red")
                local starGroup = sigilItem:Get("starGroup")
                local level = sigilItem:Get("level")
                local activedBg = sigilItem:Get("activedBg")
                local mask = sigilItem:Get("mask")
                local reddot = sigilItem:Get("reddot")
                local arrow = sigilItem:Get('arrow')
                
                arrow:SetActive(false)
                level.gameObject:SetActive(false)
                mask.gameObject:SetActive(false)
                starGroup.gameObject:SetActive(false)
                activedBg.gameObject:SetActive(false)
                red.gameObject:SetActive(false)
                reddot.gameObject:SetActive(false)
            end
        end
    end
end

function UISigilEquip:UpdateSharePropPanelUI()
    local propList = {}
    for k,v in pairs(self.shareSigilData)do
        local props = v.info.proId
        local basePropID = v.sigilCfg.proPool.data[0]
        local enhanceLv = v.info.lv
        self:GetProp(basePropID,v.info.lv,propList)
        if props then
            for m,n in ipairs(props)do
                if n and n ~= 0 then
                    self:GetProp(n,enhanceLv,propList)
                end
            end
        end
    end
    for index=1,22 do
        local rect = self.propPanel:Get('prop'..index)
        if rect then
            rect.gameObject:SetActive(false)
        end
    end
    local index = 1
    for k,v in pairs(propList)do
        if v and v ~= 0 then
            local propValue = v
            local proCfg = game_scheme:ProToLang_0(k)
            local propName = ""
            if proCfg then
                propName = lang.Get(proCfg.iLangId)
            end
            local isPercentage = self:IsPercentageProp(k)
            local percentage = isPercentage and "%" or ""
            --self:GetChildComponent("Auto_ScoreText", "Text").text = --[["<color=#220300FF>".. lang.Get(15989)..":</color>"..]]tostring(data.scores)

            local rect = self.propPanel:Get('prop'..index)
            local name = rect.transform:Find("propName")
            local value = rect.transform:Find("value")
            if rect then
                rect.gameObject:SetActive(true)
            end
            if name then
                name = name.transform:GetComponent(typeof(Text))
                name.text = propName
            end
            if value then
                value = value.transform:GetComponent(typeof(Text))
                value.text = "+"..((isPercentage and (propValue / 100)) or propValue)..percentage
            end
            index = index + 1
        end
    end
    local propList_len = util.get_len(propList)
    -- self.nonePropTip.gameObject:SetActive(propList_len<=0)
    --self.propPanel:Get('propBg1').gameObject:SetActive(propList_len>2)
    --self.propPanel:Get('propBg2').gameObject:SetActive(propList_len>6)
    --self.propPanel:Get('propBg3').gameObject:SetActive(propList_len>10)
    --self.propPanel:Get('propBg4').gameObject:SetActive(propList_len>14)
    --self.propPanel:Get('propBg5').gameObject:SetActive(propList_len>18)
    self:UpdateShareSuitUI()
end

-- 查看印记详情
function UISigilEquip:ShowShareItemInfo(pos)
    if self.shareSigilData then
        local data = self.shareSigilData[pos]
        local sigilItem = self['sigilItem'..pos]
        if sigilItem then
            if data then
                self.wearPanel.gameObject:SetActive(true)
                self:UpdateShareItem(self.wearItem,data,self.wearItemCanvas)
                self.wearItemCanvas.sortingOrder = self.curOrder + 2
            end
        end
    end
end

function UISigilEquip:UpdateShareItem(item,data,canvas)
    if item and data then
        local iconBg = item:Get('iconBg')
        local icon = item:Get('icon')
        local name = item:Get('name')
        local starGroup = item:Get('starGroup')
        local equiped = item:Get('equiped')
        local prop = item:Get('prop')
        local buttonRoot = item:Get('buttonRoot')
        local banUpgradeButton = item:Get('banUpgradeButton')
        local upgradeButton = item:Get('upgradeButton')
        local wearButton = item:Get('wearButton')
        local takeoffButton = item:Get('takeoffButton')

        local suitName = item:Get('suitName')
        local suitDesc = item:Get('suitDesc')
        local suitNameInactived = item:Get('suitNameInactived')
        local suitDescInactived = item:Get('suitDescInactived')
        local shareBtn = item:Get('shareBtn')

        local rectText_1=item:Get('text_1')
        local rectText_2=item:Get('text_2')

       

        shareBtn.gameObject:SetActive(false)
        suitName.gameObject:SetActive(false)
        suitDesc.transform.parent.gameObject:SetActive(false)
        suitNameInactived.gameObject:SetActive(false)
        suitDescInactived.transform.parent.gameObject:SetActive(false)
        equiped.gameObject:SetActive(true)

        buttonRoot.gameObject:SetActive(false)
        local itemCfg = data.itemCfg
        local sigilCfg = data.sigilCfg
        local position = sigilCfg.colourType

        local extraProps = {baseProp1=data.sigilCfg.proPool.data[0],
        baseProp2=0,
        baseProp3=0,
        baseProp4=0,
        extendProp1=0,
        extendProp2=0,
        extendProp3=0,
        extendProp4=0,
        }
        local index = 1
        for k,v in ipairs(data.info.proId)do
            if v and v~=0 then
                extraProps['extendProp'..index]=v
                index = index + 1
            end
        end
        local itemID = data.info.equipid or data.info.goodsId
        local propGroup = sigil_mgr.GetEquipPropDesc(nil,itemID,data.info.lv,extraProps)
        local propText = ""
        for i=0,#propGroup do
            local data = propGroup[i]
            if data then
                if i ~= #propGroup then
                    propText = propText..data.desc..'  <color=#28B66CFF>+'..data.num..'</color>'..'\n'
                else
                    propText = propText..data.desc..'  <color=#28B66CFF>+'..data.num..'</color>'
                end
            end
        end
        prop.text = propText
        name.text = lang.Get(itemCfg.nameKey)..' Lv.'..data.info.lv
        local grade = sigilCfg.grade-50
        for i=1,6 do
            local star = starGroup.transform:GetChild(i-1)
            if star then
                local isShow = grade >= i
                star.gameObject:SetActive(isShow)
            end
        end
        --starGroup.spacing = -140+(grade*15)
        self.sigilSpriteAsset:GetSprite(itemCfg.icon, function(sprite)
            if self:IsValid() then
                icon.enabled = true
                icon.sprite = sprite
            end
        end)
        
        self.sigilSpriteAsset:GetSprite(itemCfg.icon2, function(sprite)
            if self:IsValid() then
                iconBg.enabled = true
                iconBg.sprite = sprite
            end
        end)
        if itemCfg.icon2para and not string.empty(itemCfg.icon2para) then
            local arrData = string.split(itemCfg.icon2para, '#', tonumber)
            iconBg.gameObject.transform.localEulerAngles = {x=0,y=0,z=arrData[3]}
            if #arrData>=4 and tonumber(arrData[4]) == 1 then --第四个参数使用x轴的镜像翻转,1是翻转
                iconBg.gameObject.transform.localScale = {x = -1,y = 1,z = 1}
            end
        end

        local extraCount = 0

        local activedSuitID = nil -- 激活的取最高一套
        local inActivedSuitID = {} -- 非激活的取最低一套
        local suitID = sigilCfg.suitID
        if suitID and suitID ~= 0 then
            local suitCfg = game_scheme:EquipmentSet_0(suitID)
            if suitCfg then
                for k,v in pairs(activedSuitData)do
                    -- 判断有无同类型的已激活套装
                    if sigilCfg.suitType == v.sigilCfg.suitType then
                        if sigilCfg.suitID <= v.sigilCfg.suitID then
                            activedSuitID = v.sigilCfg.suitID
                        end
                    end
                end
                if activedSuitID then
                    local _data = allSuitData[activedSuitID]
                    suitName.gameObject:SetActive(true)
                    suitDesc.transform.parent.gameObject:SetActive(true)
                    suitName.text = lang.Get(suitCfg.condition)..'('.._data.target..'/'.._data.target..')'
                    suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                else
                    local preSet = suitCfg.preSet.data
                    if allSuitData and util.get_len(allSuitData) > 0 then
                        if util.get_len(preSet) <= 0 then
                            -- 无前置套装 仅需展示自身套装情况
                            if allSuitData[suitID] then
                                -- 已有同套装的已装备
                                local _data = allSuitData[suitID]
                                if sigilCfg and sigilCfg.suitType == _data.sigilCfg.suitType then
                                    -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1 譬如：4星换3星 3星部分之前是已激活 无需再+1
                                    if sigilCfg.suitID >= _data.sigilCfg.suitID then
                                        extraCount = 0
                                    end
                                end
                                local count = _data.count + _data.extraCount -- +1是因为策划要求展示时加上自身 这件印记的数量

                                suitName.gameObject:SetActive(count >= _data.target)
                                suitDesc.transform.parent.gameObject:SetActive(count >= _data.target)
                                suitNameInactived.gameObject:SetActive(count < _data.target)
                                suitDescInactived.transform.parent.gameObject:SetActive(count < _data.target)

                                if count >= _data.target then
                                    -- 穿上这件就已激活
                                    suitName.text = lang.Get(suitCfg.condition)..'('.._data.target..'/'.._data.target..')'
                                    suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                                else
                                    -- 仍然不能激活
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'('..count..'/'.._data.target..')'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                                end
                            else
                                suitName.gameObject:SetActive(false)
                                suitDesc.transform.parent.gameObject:SetActive(false)
                                suitNameInactived.gameObject:SetActive(true)
                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                                -- 仅自身一件
                                -- 仅判断同套装会有漏洞 同类型的没判断
                                if suitCfg.twoPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                elseif suitCfg.fourPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                elseif suitCfg.sixPiecePro ~= 0 then
                                    suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                    suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                end
                            end
                        else
                            -- 无同套装的印记穿戴
                            -- 判断是否激活低档次套装

                            -- 判断自身是否满足 譬如：3件6星 目前穿3星
                            for k,v in pairs(allSuitData)do
                                if v.sigilCfg.suitType == sigilCfg.suitType then
                                    if v.sigilCfg.suitID >= sigilCfg.suitID then
                                        local count = v.count+v.extraCount+extraCount
                                        local target = 0
                                        if v.suitCfg.twoPiecePro ~= 0 then
                                            target = 2
                                        elseif v.suitCfg.fourPiecePro ~= 0 then
                                            target = 4
                                        elseif v.suitCfg.sixPiecePro ~= 0 then
                                            target = 6
                                        end
                                        if count >= target then
                                            activedSuitID = sigilCfg.suitID
                                        end
                                    end
                                end
                            end
                            if activedSuitID then
                                -- 自身已激活
                                suitName.gameObject:SetActive(true)
                                suitDesc.transform.parent.gameObject:SetActive(true)
                                local target = 0
                                if suitCfg.twoPiecePro ~= 0 then
                                    target = 2
                                elseif suitCfg.fourPiecePro ~= 0 then
                                    target = 4
                                elseif suitCfg.sixPiecePro ~= 0 then
                                    target = 6
                                end
                                suitName.text = lang.Get(suitCfg.condition)..'('..target..'/'..target..')'
                                suitDesc.text = lang.Get(suitCfg.suitlevel)..' '..lang.Get(suitCfg.descID)
                            else
                                -- 需要判断前置套装情况
                                for i=0,util.get_len(preSet) do
                                    local preSuitID = preSet[i]
                                    if preSuitID and preSuitID ~= 0 then
                                        local preSuitData = allSuitData[preSuitID]
                                        if preSuitData then
                                            -- 已穿戴低档次套装印记
                                            if sigilCfg and sigilCfg.suitType == preSuitData.sigilCfg.suitType then
                                                -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                if sigilCfg.suitID >= preSuitData.sigilCfg.suitID then
                                                    extraCount = 0
                                                else
                                                    extraCount = 1
                                                end
                                            end
                                            local count = preSuitData.count+preSuitData.extraCount+extraCount
                                            if count >= preSuitData.target then
                                                -- 满足激活条件
                                                activedSuitID = preSuitID
                                            else
                                                table.insert(inActivedSuitID, preSuitID)
                                            end
                                        else
                                            -- 仅自身一件
                                            table.insert(inActivedSuitID, preSuitID)
                                        end
                                    end
                                end
                                if activedSuitID then
                                    local activedSuitCfg = game_scheme:EquipmentSet_0(activedSuitID)
                                    if activedSuitCfg then
                                        suitName.gameObject:SetActive(true)
                                        suitDesc.transform.parent.gameObject:SetActive(true)
                                        local nameText = ''
                                        if activedSuitCfg.twoPiecePro ~= 0 then
                                            nameText = lang.Get(activedSuitCfg.condition)..'(2/2)'
                                        elseif activedSuitCfg.fourPiecePro ~= 0 then
                                            nameText = lang.Get(activedSuitCfg.condition)..'(4/4)'
                                        elseif activedSuitCfg.sixPiecePro ~= 0 then
                                            nameText = lang.Get(activedSuitCfg.condition)..'(6/6)'
                                        end
                                        suitName.text = nameText
                                        suitDesc.text = lang.Get(activedSuitCfg.suitlevel)..' '..lang.Get(activedSuitCfg.descID)
                                    end
                                end
                                if #inActivedSuitID > 0 then
                                    -- 展示最低等级的一套
                                    local minSuitID = suitID
                                    for i=1,#inActivedSuitID do
                                        local id = inActivedSuitID[i]
                                        if id then
                                            if allSuitData[id] ~= nil then
                                                -- 若当前印记没有激活任何等级的套装效果
                                                -- 则展示当前穿戴的同类套装印记中则展示当前穿戴的同类套装印记中
                                                -- 最低星级那个印记可激活的最高等级套装最低星级那个印记可激活的最高等级套装
                                                minSuitID = id
                                                break
                                            end
                                        end
                                    end
                                    local minSuitData = allSuitData[minSuitID]
                                    if minSuitData then
                                        -- 身上有穿本套装类似的印记
                                        -- 若全员同星，只能满足自身套装
                                        if sigilCfg and sigilCfg.suitType == minSuitData.sigilCfg.suitType then
                                            -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                            if sigilCfg.suitID >= minSuitData.sigilCfg.suitID then
                                                extraCount = 0
                                            else
                                                extraCount = 1
                                            end
                                        end
                                        local count = minSuitData.count + minSuitData.extraCount + extraCount
                                        if count >= minSuitData.target then
                                            suitName.gameObject:SetActive(true)
                                            suitDesc.transform.parent.gameObject:SetActive(true)
    
                                            suitName.text = lang.Get(minSuitData.suitCfg.condition)..'('..minSuitData.target..'/'..minSuitData.target..')'
                                            suitDesc.text = lang.Get(minSuitData.suitCfg.suitlevel)..' '..lang.Get(minSuitData.suitCfg.descID)
                                        else
                                            suitNameInactived.gameObject:SetActive(true)
                                            suitDescInactived.transform.parent.gameObject:SetActive(true)
                                            suitNameInactived.text = lang.Get(minSuitData.suitCfg.condition)..'('..count..'/'..minSuitData.target..')'
                                            suitDescInactived.text = lang.Get(minSuitData.suitCfg.suitlevel)..' '..lang.Get(minSuitData.suitCfg.descID)
                                        end
                                    else
                                        
                                        local selfSuitData = allSuitData[suitCfg.suitID]
                                        if selfSuitData then
                                            -- 有同类型的其他印记
                                            if sigilCfg and sigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                                -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                if sigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                                    extraCount = 0
                                                else
                                                    extraCount = 1
                                                end
                                            end
                                            local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                                            if count >= selfSuitData.target then
                                                suitName.gameObject:SetActive(true)
                                                suitDesc.transform.parent.gameObject:SetActive(true)
                
                                                suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                                suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                            else
                                                suitNameInactived.gameObject:SetActive(true)
                                                suitDescInactived.transform.parent.gameObject:SetActive(true)
                
                                                suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                                suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                            end
                                        else
                                            -- 为第一件
                                            suitNameInactived.gameObject:SetActive(true)
                                            suitDescInactived.transform.parent.gameObject:SetActive(true)
                                            if suitCfg.twoPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            elseif suitCfg.fourPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            elseif suitCfg.sixPiecePro ~= 0 then
                                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                            end
                                        end
                                    end
                                else
                                    local selfSuitData = allSuitData[suitCfg.suitID]
                                    if selfSuitData then
                                        -- 有同类型的其他印记
                                            if sigilCfg and sigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                                -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                                if sigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                                    extraCount = 0
                                                else
                                                    extraCount = 1
                                                end
                                            end
                                        local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                                        if count >= selfSuitData.target then
                                            suitName.gameObject:SetActive(true)
                                            suitDesc.transform.parent.gameObject:SetActive(true)
            
                                            suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                            suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                        else
                                            suitNameInactived.gameObject:SetActive(true)
                                            suitDescInactived.transform.parent.gameObject:SetActive(true)
            
                                            suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                            suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                                        end
                                    else
                                        -- 展示自身
                                        suitNameInactived.gameObject:SetActive(true)
                                        suitDescInactived.transform.parent.gameObject:SetActive(true)
                                    
                                        -- 仅自身一件
                                        if suitCfg.twoPiecePro ~= 0 then
                                            suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                            suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                        elseif suitCfg.fourPiecePro ~= 0 then
                                            suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                            suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                        elseif suitCfg.sixPiecePro ~= 0 then
                                            suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                            suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                                        end
                                    end
                                end
                            end
                        end
                    else
                        -- 展示自身
                        local selfSuitData = allSuitData[suitCfg.suitID]
                        if selfSuitData then
                            -- 有同类型的其他印记
                            if sigilCfg and sigilCfg.suitType == selfSuitData.sigilCfg.suitType then
                                -- 替换同部位的印记 仅大于自身套装等级的才需要额外+1
                                if sigilCfg.suitID >= selfSuitData.sigilCfg.suitID then
                                    extraCount = 0
                                else
                                    extraCount = 1
                                end
                            end
                            local count = selfSuitData.count + selfSuitData.extraCount + extraCount
                            if count >= selfSuitData.target then
                                suitName.gameObject:SetActive(true)
                                suitDesc.transform.parent.gameObject:SetActive(true)

                                suitName.text = lang.Get(selfSuitData.suitCfg.condition)..'('..selfSuitData.target..'/'..selfSuitData.target..')'
                                suitDesc.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                            else
                                suitNameInactived.gameObject:SetActive(true)
                                suitDescInactived.transform.parent.gameObject:SetActive(true)

                                suitNameInactived.text = lang.Get(selfSuitData.suitCfg.condition)..'('..count..'/'..selfSuitData.target..')'
                                suitDescInactived.text = lang.Get(selfSuitData.suitCfg.suitlevel)..' '..lang.Get(selfSuitData.suitCfg.descID)
                            end
                        else
                            -- 仅自身一件
                            suitNameInactived.gameObject:SetActive(true)
                            suitDescInactived.transform.parent.gameObject:SetActive(true)
                            if suitCfg.twoPiecePro ~= 0 then
                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/2)'
                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                            elseif suitCfg.fourPiecePro ~= 0 then
                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/4)'
                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                            elseif suitCfg.sixPiecePro ~= 0 then
                                suitNameInactived.text = lang.Get(suitCfg.condition)..'(1/6)'
                                suitDescInactived.text = lang.Get(suitCfg.suitlevel)..'  '..lang.Get(suitCfg.descID)
                            end
                        end
                    end
                end
            end
        end


        self.itemTicker = self.itemTicker or base_object()
            
        self.itemTicker:CreateTimeTicker(0, function()
            while true do
                if Input.anyKeyDown or Input.anyKey then
                    if not canvas or (canvas and not ClickIsOverUI.Instance:IsPointerOverUIObject(canvas, {x = Input.mousePosition.x, y = Input.mousePosition.y})) then
                        break
                    end
                end
                coroutine.yield(0.01)
            end
            if self and self:IsValid() then
                self.wearPanel.gameObject:SetActive(false)
            end
        end)

    end
    local itemRect = item.transform:GetComponent(typeof(RectTransform))

    if rectText_1 then
        -- print("suitDesc.preferre", suitDesc.preferredHeight)
        rectText_1:SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, suitDesc.preferredHeight)
    end
    if rectText_2 then
        -- print("suitDescInactived.preferre", suitDescInactived.preferredHeight)
        rectText_2:SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, suitDescInactived.preferredHeight)
    end

    LayoutRebuilder.ForceRebuildLayoutImmediate(itemRect)
end

function UISigilEquip:UpdateShareSuitUI()
    if heroShareData then
        local suitData = sigil_mgr.GetShareSuitData(heroShareData)
        self.suitData = {}
        local index = 1
        -- self.noneSuitTip.gameObject:SetActive(util.get_len(suitData) <= 0)
        for index=1,3 do
            local item = self["suitItem"..index]
            if item then
                item.gameObject:SetActive(false)
            end
        end

        self.suitRect.gameObject:SetActive(util.get_len(suitData) > 0)
        if util.get_len(suitData) > 0 then
            for k,v in ipairs(suitData)do
                if v then
                    local item = self["suitItem"..index]
                    if item then
                        item.gameObject:SetActive(true)
                        local icon = self["suit_img_"..index]
                        local iconBg = self["suit_img_bg_"..index]
                        local lvText = self["suit_lvtext_"..index]

                        if lvText then
                            lvText.gameObject:SetActive(true)
                            lvText.text = lang.Get(v.suitCfg.suitlevel)
                        end
                        if icon then
                            self.sigilSpriteAsset:GetSprite(v.suitCfg.iconID, function(sprite)
                                if self:IsValid() then
                                    icon.enabled = true
                                    icon.sprite = sprite
                                end
                            end)
                            local grayItem = self["suitItemGray"..index]
                            table.insert(self.suitData, v)
                        end
                    end
                    index = index + 1
                end
            end
        end

    end
end

------------------------------------------end----------------------------------------


----------------------------------箭头点击事件------------------------------------------

function UISigilEquip:LeftArrowEvent()
    local ui_hero_base = require "ui_hero_base"
    ui_hero_base.LeftArrowClick()
    self:UpdateArrowState()
end

function UISigilEquip:RightArrowEvent()
    local ui_hero_base = require "ui_hero_base"
    ui_hero_base.RightArrowClick()
    self:UpdateArrowState()
end

function UISigilEquip:UpdateArrowState()
    if selectedIndex then
        self.leftArrow.interactable = heroCount > 1 and selectedIndex ~= 1
        self.rightArrow.interactable = heroCount > 1 and  selectedIndex ~= heroCount
    else
        self.leftArrow.interactable = false
        self.rightArrow.interactable = false
    end
end
-------------------------------------end-----------------------------------------------


function SetShareData()
    local index = sigil_mgr.GetCurHeroSid()
    local hero_share_data = require "hero_share_data"
    heroShareData = hero_share_data.GetHeroDataByIndex(index)
end

function SetShareDataByIndex()
    local index,_ = sigil_mgr.GetCurHeroIndex()
    local hero_share_data = require "hero_share_data"
    heroShareData = hero_share_data.GetHeroDataByIndex(index)
end

--@region WindowBtnFunctions


--@region ScrollItem

function SetParent(_parent)
    parent = _parent
end
--@region WindowInherited
local CUISigilEquip = class(ui_base, nil, UISigilEquip)


--@region ModuleFunction
function Show()
    if window == nil then
        window = CUISigilEquip()
        window._NAME = _NAME;
        window:LoadUIResource("ui/prefabs/uisigilequip.prefab", nil, parent, nil, nil, true, nil, nil, true)
    else
        window:Show()
    end
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end



--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd


