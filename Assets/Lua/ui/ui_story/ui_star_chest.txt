--------------------------------------
--------------星星宝箱界面----------------
--------------------------------------
local require = require
local math = math
local table = table
local print = print
local ipairs = ipairs
local pairs = pairs

local lang = require "lang"
local event = require "event"
local class = require "class"
local ui_base = require "ui_base"
local game_scheme = require "game_scheme"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local goods_item = require "goods_item_new"
local hero_item = require "hero_item_new"
local window_mgr = require "ui_window_mgr"
local flow_text = require "flow_text"
local force_guide_system = require"force_guide_system"
local force_guide_event = require"force_guide_event"
local laymain_data = require("laymain_data")
local reward_mgr = require("reward_mgr")
local model_item = require "model_item"
local util = require "util"
local hero_mgr = require"hero_mgr"
local sort_order = require "sort_order"

local Button            = CS.UnityEngine.UI.Button
local Image             = CS.UnityEngine.UI.Image
local Text              = CS.UnityEngine.UI.Text
local RectTransform     = CS.UnityEngine.RectTransform
local ScrollRectTable   = CS.UI.UGUIExtend.ScrollRectTable
local ImageGray         = CS.War.UI.ImageGray
local LeanTween         = CS.LeanTween
local Transform 	    = CS.UnityEngine.Transform
local SpriteSwitcher    = CS.War.UI.SpriteSwitcher
local LayoutElement   = CS.UnityEngine.UI.LayoutElement

module("ui_star_chest")

local window = nil
local maxStarChestID = 0
local StarChest = {}
local groupModel = nil
local assetBundleName = "art/battleplayer/herocardview.prefab"
local curModelId = nil
local ntTimeTicker = nil
local offset4Hero =    --英雄显示偏移
{
	[3] = {x=0,y=370,z=0},
	[12] = {x=0,y=370,z=0},
	[19] = {x=0,y=370,z=0},
	[25] = {x=0,y=370,z=0},
	[39] = {x=0,y=370,z=0},
	[59] = {x=0,y=370,z=0},
	[65] = {x=0,y=370,z=0},
	[73] = {x=0,y=370,z=0},
	[78] = {x=0,y=370,z=0},
	[88] = {x=0,y=370,z=0},
    [101] = {x=0,y=370,z=0},
    [21] = {x=0,y=370,z=0},
}

StarChest.widget_table = 
{
	closedBtn = {path = "closeBtn", type = "Button", backEvent = true},
	getBtn = {path = "getBtn", type = "Button"},
	getTxt = {path = "getBtn/Text", type = "Text"},
	getRed = {path = "getBtn/red", type = "Image"},
	payBtn = {path = "payBtn", type = "Button"},
	payTxt = {path = "payBtn/Text", type = "Text"},
	payRed = {path = "payBtn/red", type = "Image"},
	tip = {path = "tip", type = "Text"}, 
    scrollTable = {path = "Scroll View/Viewport/Content", type = ScrollRectTable},
	getGray = {path = "getBtn", type = ImageGray},
	payGray = {path = "payBtn", type = ImageGray},
    legends_title_en = {path = "star/legends_EN", type = RectTransform},--传说英雄标题
    legends_title_zh = {path = "star/legends_ZH", type = RectTransform},
    scrollRect = {path = "Scroll View/Viewport/Content", type = RectTransform},
	rawImage = {path = "Bg/heroImage", type = "RawImage"},--背景
    rawImageRect = {path = "Bg/heroImage", type = "RectTransform"},--英雄模型
    heroRoot = {path = "heroRoot", type = RectTransform},--英雄头像节点
	progress = { path = "progressbg/progress", type = "Image", },
    progressTxt = { path = "progressbg/count", type = "Text", },--进度
    langID = {path = "langID/Text", type = "Text"},--英雄定位描述
    myths_title_en = {path = "star/myths_EN", type = RectTransform},--神话英雄标题
    myths_title_zh = {path = "star/myths_ZH", type = RectTransform},
    
	rarityType = {path ="rarityType" , type = SpriteSwitcher},-- 奖励英雄的稀有度
	rarityEff = {path ="rarityType/eEffect_S+" , type = Transform},
    noviceTournamentBtn = {path ="novice_tournament" , type = "Button"},

    ntHoldPaceRect = {path ="novice_tournament/node_rank/holdPaceNode" , type = LayoutElement},
    ntRankUpImg = { path = "novice_tournament/node_rank/img_up", type = Image},
    ntRankDownImg = { path = "novice_tournament/node_rank/img_down", type = Image},
    ntRankText = { path = "novice_tournament/node_rank/text_rank", type = "Text", },
    ntActivityTimeText = { path = "novice_tournament/text_activity_time", type = "Text", },
    ntRedRect = { path = "novice_tournament/red", type = RectTransform, },
}

function GetCurReward(curChestID)
    local rewardData = {}
    local needStarNum = 0
    curChestID = curChestID or laymain_data.GetCurStarChestID()
    local data = window:BuildListData()
    for i,v in pairs(data) do
        if v.chestID == curChestID then
            needStarNum = v.ConditionValue
        end
    end
    return needStarNum
end

function GetCurProcess(curChestID)
    local curNum = laymain_data.GetStarNum()
    local needNum = GetCurReward(curChestID)
    local pro = (curNum)/(needNum)
    if pro > 1 then
        pro = 1
    end
    local txt = (curNum).."/"..(needNum)
    if curChestID == maxStarChestID and curChestID == laymain_data.GetStarReward() then
        return 1
    end
    return pro,txt
end

function onItemRenderBottom(scroll_rect_item, index, dataItem)
    dataItem = window.scrollTable.data[#window.scrollTable.data - index + 1]
	scroll_rect_item.data = scroll_rect_item.data or {index,dataItem}
	scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    if not scroll_rect_item.data then
        scroll_rect_item.gameObject:SetActive(false)
    end
	local commonReward = scroll_rect_item:Get("commonReward")
	local payReward = scroll_rect_item:Get("payReward")
	local curRewardTxt = scroll_rect_item:Get("Text")
	local progressbg = scroll_rect_item:Get("progressbg")
	local processImg = scroll_rect_item:Get("progress")
    local processTxt = scroll_rect_item:Get("count")
    local lock = scroll_rect_item:Get("lock")
    local bg = scroll_rect_item:Get("bg")
    local dot = scroll_rect_item:Get("dot")
    local xingxing = scroll_rect_item:Get("xingxing")
    local itemUI = {}
    
    local payCount = laymain_data.GetGiftStarPayCount(laymain_data.StarActivityID)
    lock.gameObject:SetActive(dataItem.isFree~=1 and payCount~=1)
    local showPro = dataItem.chestID == laymain_data.GetCurStarChestID() and laymain_data.GetStarReward() < maxStarChestID
    progressbg.gameObject:SetActive(showPro)
    bg.gameObject:SetActive(showPro)
    dot.gameObject:SetActive(not showPro)
    xingxing.gameObject:SetActive(showPro)
    if showPro then
        --星星宝箱进度=（当前星星总数-上一关宝箱所需星星数量）/（当前宝箱所需星星数量-上一个宝箱所需星星数量）
        local curNum = laymain_data.GetStarNum() or 0--当前星星总数
        local lastNeedNum = 0--上一关宝箱所需星星数量
        local cfg = laymain_data.GetHookChestByID(laymain_data.GetStarReward(),2)
        if cfg then
            lastNeedNum = cfg.ConditionValue
        end
        local needNum = dataItem.ConditionValue
        local pro = (curNum-lastNeedNum)/(needNum-lastNeedNum)
        if pro > 1 then
            pro = 1
        end
        ------ --print("当前星星数量curNum",curNum,"所需星星数量needNum",needNum)
        processImg.fillAmount = pro
        processTxt.text = pro>= 1 and (needNum-lastNeedNum).."/"..(needNum-lastNeedNum) or (curNum-lastNeedNum).."/"..(needNum-lastNeedNum)
    end

	if not scroll_rect_item.data["itemUI"] then scroll_rect_item.data["itemUI"] = {} end
	if not scroll_rect_item.data["HeroUI"] then scroll_rect_item.data["HeroUI"] = {} end
    for i, v in ipairs(dataItem.goodData) do
        local rewardType = v.rewardType	
        local RewardID = v.RewardID
        local root = (v.rewardType == 1) and commonReward.transform or payReward.transform
        v = v.reward
		if v.nType == item_data.Reward_Type_Enum.Hero then
             if scroll_rect_item.data["HeroUI"][i] == nil then
				    scroll_rect_item.data["HeroUI"][i] = hero_item.CHeroItem()
                scroll_rect_item.data["HeroUI"][i]:Init(root, function() 
                    scroll_rect_item.data["HeroUI"][i]:DisplayInfo()
                    scroll_rect_item.data["HeroUI"][i]:SetHeroMaskAlpha(1)
				end,0.7)
			end
			local hero = {
				heroSid = v.sid,
				heroID = v.id,
				numProp = {
					starLv = v.starLevel,
					lv = v.lv,
				}
			}
			local OnClickHero = function (item)
				--TODO:打开英雄信息面板
                local heroData = {}
                table.insert(heroData, {heroSid=v.sid,heroID=v.id,starLv=v.starLevel})
                local ui_show_hero_card = require "ui_show_hero_card"
                ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroList(heroData),nil,nil,nil,true)
            end
            if scroll_rect_item.data["HeroUI"][i] then
                scroll_rect_item.data["HeroUI"][i]:HeroEffectEnable(true, window.curOrder, 1)
                scroll_rect_item.data["HeroUI"][i]:SetHero(hero,OnClickHero) 
                if rewardType == 1 then
                    --已领取最后一个
                    local getLast = dataItem.chestID==laymain_data.GetStarReward() and  dataItem.chestID==maxStarChestID
                    scroll_rect_item.data["HeroUI"][i]:SelectHero(dataItem.chestID < laymain_data.GetCurStarChestID() or getLast)
                    scroll_rect_item.data["HeroUI"][i]:MaskHero(dataItem.chestID < laymain_data.GetCurStarChestID() or getLast, true)
                elseif rewardType == 2 then
                    local getLast = dataItem.chestID==laymain_data.GetGiftStarReward() and  dataItem.chestID==maxStarChestID
                    scroll_rect_item.data["HeroUI"][i]:SelectHero(dataItem.chestID < laymain_data.GetCurGiftStarChestID() or getLast)
                    scroll_rect_item.data["HeroUI"][i]:MaskHero(dataItem.chestID < laymain_data.GetCurGiftStarChestID() or getLast, true)
                end
                if scroll_rect_item.data["itemUI"][i] then
                    scroll_rect_item.data["itemUI"][i]:Dispose()
                    scroll_rect_item.data["itemUI"][i] = nil
                end
            end
		else
			--设置奖励（物品）
            if scroll_rect_item.data["itemUI"][i] == nil then
                scroll_rect_item.data["itemUI"][i] = goods_item.CGoodsItem()
                scroll_rect_item.data["itemUI"][i]:Init(root,nil,0.7,nil,40)	
			end
            -- 加载物品图标
            if scroll_rect_item.data["itemUI"][i] then
                scroll_rect_item.data["itemUI"][i]:SetGoods(nil, v.id, v.num, function()		
                    local iui_item_detail = require "iui_item_detail"
                    iui_item_detail.Show(v.id, nil, item_data.Item_Show_Type_Enum.Reward_Interface, v.num, nil, nil,RewardID)
                end)	
                scroll_rect_item.data["itemUI"][i]:SetCountEnable(true)
                scroll_rect_item.data["itemUI"][i]:SetFrameBg(3)
                if rewardType == 1 then
                    --已领取最后一个
                    local getLast = dataItem.chestID==laymain_data.GetStarReward() and  dataItem.chestID==maxStarChestID
                    scroll_rect_item.data["itemUI"][i]:SetBattleMaskEnable(dataItem.chestID < laymain_data.GetCurStarChestID() or getLast)
                    scroll_rect_item.data["itemUI"][i]:SetMaskEnable(dataItem.chestID < laymain_data.GetCurStarChestID() or getLast)
                elseif rewardType == 2 then
                    local getLast = dataItem.chestID==laymain_data.GetGiftStarReward() and  dataItem.chestID==maxStarChestID
                    scroll_rect_item.data["itemUI"][i]:SetBattleMaskEnable(dataItem.chestID < laymain_data.GetCurGiftStarChestID() or getLast)
                    scroll_rect_item.data["itemUI"][i]:SetMaskEnable(dataItem.chestID < laymain_data.GetCurGiftStarChestID() or getLast)
                end
                if scroll_rect_item.data["HeroUI"][i] then
                    scroll_rect_item.data["HeroUI"][i]:Dispose()
                    scroll_rect_item.data["HeroUI"][i] = nil
                end
            end
		end
    end
	scroll_rect_item.name="ListItem"..index
end

--[[构建列表数据]]
function StarChest:BuildListData()	
    local allData = laymain_data.GetAllHookChestCfg(2)
    self.scrollToIndex =0
    local data = {}
    for i,v in pairs(allData) do
        local temp = {}
        temp.chestID = v.chestID
        temp.ConditionValue = v.ConditionValue
        temp.goodData = {}
        temp.isFree = v.isFree
        temp.chestSequence = v.chestSequence
        if not maxStarChestID or v.chestID > maxStarChestID then
            maxStarChestID = v.chestID
        end
        for j=0,#v.RewardID.data do
            local reward = reward_mgr.GetRewardGoods(v.RewardID.data[j])
            local t = {}
            t.RewardID = v.RewardID.data[j]
            t.reward = reward
            t.rewardType = 1--奖励类型1是普通奖励
            table.insert(temp.goodData,t)
        end
        if v.giftRewardID.data and #v.giftRewardID.data > 0 then
            for j=0,#v.giftRewardID.data do
                local reward = reward_mgr.GetRewardGoods(v.giftRewardID.data[j])
                local t = {}
                t.RewardID = v.giftRewardID.data[j]
                t.reward = reward
                t.rewardType = 2--奖励类型2是豪华奖励
                table.insert(temp.goodData,t)
            end
        end
        table.insert( data,temp)
    end
    table.sort( data, function(v1,v2)
        return v1.chestSequence < v2.chestSequence
    end)

    for i,v in pairs(data) do
        ------ --print("chestID",v.chestID)
        for j, k in ipairs(v.goodData) do
            local rewardType = k.rewardType
            k = k.reward
            ------ --print("rewardType",rewardType,"奖励ID",v.RewardID,"id",k.id,"num",k.num,"nType",k.nType)
        end
        if v.chestID == laymain_data.GetCurStarChestID() then
            self.scrollToIndex = #data - v.chestSequence + 1
        end
    end
    return data
end

--[[更新列表]]
function StarChest:UpdateList()
	if self.scrollTable == nil then
		return
    end
    local data = self:BuildListData()	
	self.scrollTable.data = data
    self.scrollTable:Refresh(0, -1)
    -- self.scrollTable.moveContainer.localPosition = {x=0,y=0}
    -- local curPosition = self.scrollTable.moveContainer.localPosition
    -- local t = (self.scrollToIndex-2) * (-120)
    -- if curPosition.y > t then
    --     curPosition.y = t
    -- end
    -- if #data > 3 then
    --     self.scrollTable:ScrollTo(self.scrollToIndex)
    --     self.scrollTable.moveContainer.localPosition = curPosition
    -- end
    ------ --print("更新列表",#data,self.scrollToIndex)
end

function StarChest:InitList()
	self.scrollTable.onItemRender = onItemRenderBottom
	self.scrollTable.onItemDispose = function(scroll_rect_item, index)
		if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.itemUI ~= nil then
			for i,v in pairs(scroll_rect_item.data.itemUI) do
                v:Dispose()
                v = nil	
			end
		end
		if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.HeroUI ~= nil then
			for i,v in pairs(scroll_rect_item.data.HeroUI) do 
                v:Dispose()
                v = nil	
			end
		end
	end
	self:UpdateList()
    self.scrollTable:ScrollTo(self.scrollToIndex+1)
end

--获取某个宝箱ID的普通宝箱和付费宝箱的领取状态
function StarChest:ChestGainState()
    local canScroll = nil
    local normalStarReward = laymain_data.GetStarReward()
    if normalStarReward == laymain_data.GetGiftStarReward() then
        canScroll = true
    elseif normalStarReward > laymain_data.GetGiftStarReward() then
        local payCount = laymain_data.GetGiftStarPayCount(laymain_data.StarActivityID)
        local cfg = laymain_data.GetHookChestByID(laymain_data.GetCurGiftStarChestID(),2)
        --免费；付费且已支付
        canScroll = cfg.isFree ~= 1 and payCount ~= 1
        --付费宝箱可领取时，不可滚动
    end
    return canScroll
end

--领取奖励后移动列表效果
function StarChest:ShowMoveAni(oldIndex,newIndex)
    local t = oldIndex and newIndex and math.abs(oldIndex-newIndex)
    if t and t > 0 and t < 12 then
        --需要移动动画条件：1、两侧同一条都可领取且领取成功时；2、只有一侧可领取且领取成功时
        if self:ChestGainState() then
            local dis = t == 1 and t*120 or (t-1)*120--目标位置移到当前位置的距离
            local t = self.scrollRect.anchoredPosition.y - dis
            LeanTween.moveY(self.scrollRect.transform, t, 1):setOnComplete(function ()
                self.oldIndex = newIndex
            end)
        end

    elseif t and t >= 12 then
        self.scrollTable:ScrollTo(self.scrollToIndex+1)
    end
end

function StarChest:Init()
    self.curOrder = sort_order.ApplyBaseIndexs(self,nil, 1)
    self:SubscribeEvent()
    self.UIs = {}
    self.UIs2 = {}
    self.initHero = nil
end

--[[初始化顶部英雄信息]]
function StarChest:SetInitHero()
    local data,curCfg = self:GetInitReward()
    if not data then return end
    if self.initHero == nil then
        self.initHero = hero_item.CHeroItem()
        self.initHero:Init(self.heroRoot, function() 
            self.initHero:DisplayInfo()
        end,1)
    end
    local hero = {
        heroSid = 0,
        heroID = data.id,
        numProp = {
            starLv = data.starLevel,
            lv = data.lv,
        }
    }
    local OnClickHero = function (item)
        --TODO:打开英雄信息面板
        local heroData = {}
        table.insert(heroData, {heroSid=0,heroID=data.id,starLv=data.starLevel})
        local ui_show_hero_card = require "ui_show_hero_card"
        ui_show_hero_card.ShowWithData(ui_show_hero_card.ParseArrHeroList(heroData),nil,nil,nil,true)
    end
    if self.initHero then
        self.initHero:SetHero(hero,OnClickHero) 
        self.initHero:HeroEffectEnable(true, window.curOrder)
    end

    if not curCfg then return end
    --标题类型（1传说英雄 2神话英雄）
    self.legends_title_zh.gameObject:SetActive(lang.USE_LANG==lang.ZH and curCfg.showTitletype == 1)
    self.legends_title_en.gameObject:SetActive(lang.USE_LANG~=lang.ZH and curCfg.showTitletype == 1)
    self.myths_title_zh.gameObject:SetActive(lang.USE_LANG==lang.ZH and curCfg.showTitletype == 2)
    self.myths_title_en.gameObject:SetActive(lang.USE_LANG~=lang.ZH and curCfg.showTitletype == 2)

    --进度（当前累计星星-上一阶段所需星星）/（本阶段所需星星-上一阶段所需星星）
    local curNum = laymain_data.GetStarNum()--当前累计星星
    local curShowID = curCfg.showID--当前阶段
    local curStarNum = curCfg.ConditionValue--本阶段所需星星
    local lastShowID = curCfg.showID-1--上一阶段
    local lastStarNum = lastShowID > 0 and (self:GetSameStateLastCfg(lastShowID)) or 0--上一阶段所需星星
    local pro = (curNum-lastStarNum)/(curStarNum-lastStarNum)
    if pro > 1 then
        pro = 1
    end
    ------ --print("curNum",curNum,"curStarNum",curStarNum,"lastStarNum",lastStarNum,"curShowID",curShowID,"lastShowID",lastShowID)
    self.progress.fillAmount = pro
    self.progressTxt.text = pro>= 1 and (curStarNum-lastStarNum).."/"..(curStarNum-lastStarNum) or (curNum-lastStarNum).."/"..(curStarNum-lastStarNum)
    ------ --print("curCfg.showLangID",curCfg.showLangID)
    --英雄定位
    self.langID.text = lang.Get(curCfg.showLangID)
end

function StarChest:GetSameStateLastCfg(showID)
    local data = laymain_data.GetAllHookChestCfg(2)
    local cfg = nil
    for i,v in pairs(data) do
        if v.showID == showID then
            if not cfg or cfg.ConditionValue < v.ConditionValue then
                cfg = v
            end
        end
    end
    if cfg then
        ------ --print("showID",showID,"chestID",cfg.chestID,"ConditionValue",cfg.ConditionValue)
        return cfg.ConditionValue
    else
        ------ --print("showID",showID)
        return 0
    end
end

function StarChest:GetInitReward()
    local curChestID = laymain_data.GetCurStarChestID()
    local curCfg = game_scheme:HookLevelChest_0(curChestID)
    curChestID = curCfg.showChestID
    local data = nil
    curCfg = game_scheme:HookLevelChest_0(curChestID)--待展示英雄奖励宝箱
    for i,v in pairs(window:BuildListData()) do
        if v.chestID == curChestID then
            for j, k in ipairs(v.goodData) do
                if k.rewardType == 1 then
                    data = k.reward
                    break
                end
            end
        end
    end
    return data,curCfg
end

--[[设置英雄模型]]
function StarChest:LoadHeroModel()
    local data = self:GetInitReward()
    if not data then return end
    -- 设置英雄稀有度
    local cfg_hero = game_scheme:Hero_0(data.id)
    if cfg_hero then
        local heroData = game_scheme:Hero_0(cfg_hero.HerohandBookID)
        if heroData.rarityType and heroData.rarityType > 0 then
            self.rarityType:Switch(heroData.rarityType - 1)
            self.rarityEff.gameObject:SetActive(heroData.rarityType == 4)
        else
----             --print("请配置"..heroEntity.heroID.."rarityType数据")
        end
    end
    ------  --print("heroEntity.heroID:",heroEntity.heroID,"heroData.rarityType:",heroData.rarityType)
    
    local modelId = hero_mgr.ChangeHeroModel(data.id, data.starLevel)
    -- --print("groupModel",groupModel,"curModelId",curModelId,"modelId",modelId)
    if self.model and curModelId == modelId then
        return
    end
    --     ------ --print("heroID",data.id)
	if offset4Hero[data.id] then
		local pos = self.rawImageRect.transform.localPosition
		self.rawImageRect.transform.localPosition = {x=pos.x+offset4Hero[data.id].x,y=offset4Hero[data.id].y,z=pos.z+offset4Hero[data.id].z}
	end
	local cfg_model = game_scheme:Modul_0(modelId)
       
    if not self.model or curModelId ~= modelId then
        curModelId = modelId
        self.model = model_item.CModelItem():Init(assetBundleName,{cfg_model.modelPath},function(_rt)
            if self:IsValid() then
                self.rawImage.texture = _rt
            end
        end)
        if cfg_model.showChildModel and cfg_model.showChildModel == 1 then
            self.model:SetShowOtherModel()
        end
    end
end

function StarChest:InitBtnState()
    local config = game_scheme:welfareActivity_0(laymain_data.StarActivityID)
    local contentId=config.contentId.data[0]
    local conCsv= game_scheme:ActivityContent_0(contentId)
    local rechargeCfg = game_scheme:Recharge_0(conCsv.rechargeID.data[0])
    local iPrice = rechargeCfg.iPrice
    local payCount = laymain_data.GetGiftStarPayCount(laymain_data.StarActivityID)
    local cfg = laymain_data.GetHookChestByID(laymain_data.GetCurGiftStarChestID(),2)
    local isGray = false
    if payCount == 1 then
        self.payTxt.text = lang.Get(2200)
        isGray = (laymain_data.GetCurGiftStarChestID() == maxStarChestID and laymain_data.GetGiftStarReward() == maxStarChestID) or GetCurProcess(laymain_data.GetCurGiftStarChestID())<1
    else
        --如果还没领取免费奖励可以显示领取
        if cfg.isFree == 1 then
            self.payTxt.text = lang.Get(2200)
        else
            local net_recharge_module = require "net_recharge_module"
            self.payTxt.text = net_recharge_module.GetMoneyStrByGoodsID(rechargeCfg.iGoodsID)

        end
        isGray = cfg.isFree == 1 and GetCurProcess(laymain_data.GetCurGiftStarChestID())<1
    end
    self.tip.gameObject:SetActive(payCount ~= 1)
    self.payGray:SetEnable(isGray)--没有可领取时
    local pro = GetCurProcess()
    self.getGray:SetEnable(pro<1 or laymain_data.GetStarReward() == maxStarChestID)
    self.getRed.gameObject:SetActive(pro>=1 and laymain_data.GetStarReward() < maxStarChestID)
    self.getTxt.color = (pro<1 or laymain_data.GetStarReward() == maxStarChestID) and {r = 90/255, g = 90/255, b = 90/255, a = 1} or {r = 182/255, g = 109/255, b = 1/255, a = 1}
    self.payTxt.color = isGray and {r = 90/255, g = 90/255, b = 90/255, a = 1} or {r = 182/255, g = 109/255, b = 1/255, a = 1}
    local canGet = GetCurProcess(laymain_data.GetCurGiftStarChestID())>=1 and laymain_data.GetCurGiftStarChestID() < maxStarChestID and (payCount == 1 or cfg.isFree == 1)
    self.payRed.gameObject:SetActive(canGet)
end

function StarChest:OnShow()
	self.__base:OnShow()
    local util = require "util"
    util.DelayCall(0,function ( ... )
        if not self:IsValid() then return end
        self:InitList()
        self:InitBtnState()
        self:SetInitHero()
        self:LoadHeroModel()
        self.oldIndex = self.scrollToIndex
        force_guide_system.TriEnterEvent(force_guide_event.tEnterStarChest)
    end)
end

function StarChest:SubscribeEvent()
	self.closeBtnHandler = function ()
        window_mgr:UnloadModule("ui_star_chest")
        force_guide_system.TriEnterEvent(force_guide_event.cStarChestClose)

    end
    if self.closedBtn then
        self.closedBtn.onClick:AddListener(self.closeBtnHandler)
    end

    self.getBtnEvent = function()
        --TODO:获取奖励请求
        local pro = GetCurProcess()
        if pro >= 1 and laymain_data.GetStarReward() < maxStarChestID then
            local payCount = laymain_data.GetGiftStarPayCount(laymain_data.StarActivityID)
            if payCount == 1 then
                local sData = {type = 2,gift = 0}
                laymain_data.Request_ReceiveIdleStarOrStageAward( sData )
            else
                window_mgr:ShowModule("ui_star_chest_single_reward")
            end
            force_guide_system.TriComEvent(force_guide_event.cClickStarGet)
        else
            local flow_text = require "flow_text"
            flow_text.Add(lang.Get(7066))
        end
    end
    if self.getBtn then
        self.getBtn.onClick:AddListener(self.getBtnEvent)
    end

    self.payBtnEvent = function()
        local cfg = laymain_data.GetHookChestByID(laymain_data.GetCurGiftStarChestID(),2)
        if cfg.isFree == 1 then
            if GetCurProcess(laymain_data.GetCurGiftStarChestID()) >= 1 then
                local sData = {type = 2,gift = 1}
                laymain_data.Request_ReceiveIdleStarOrStageAward( sData )
            else
                local flow_text = require "flow_text"
                flow_text.Add(lang.Get(7066))
            end
        else
            local payCount = laymain_data.GetGiftStarPayCount(laymain_data.StarActivityID)
            if payCount == 1 then
                --已充值
                if GetCurProcess(laymain_data.GetCurGiftStarChestID()) >= 1 then
                    local sData = {type = 2,gift = 1}
                    laymain_data.Request_ReceiveIdleStarOrStageAward( sData )
                else
                    local flow_text = require "flow_text"
                    flow_text.Add(lang.Get(7066))
                end
            elseif payCount ~= 1 then
                --未充值时
                if laymain_data.GetGiftRewardData(2) > 0 then
                    local ui_gift_chest = require("ui_gift_chest")
                    ui_gift_chest.SetGiftChestType(2)
                    window_mgr:ShowModule("ui_gift_chest")
                else
                    --直接发起购买请求
                    local activityID = laymain_data.StarActivityID
                    local config = game_scheme:welfareActivity_0(activityID)
                    local contentId = config and config.contentId.data[0]
                    local conCsv = contentId and game_scheme:ActivityContent_0(contentId)
                    local RmbCostID = conCsv and conCsv.rechargeID.data[0]
                    local rechargeCfg = conCsv and game_scheme:Recharge_0(conCsv.rechargeID.data[0])
                    local iPrice = rechargeCfg and rechargeCfg.iPrice
                    local  ui_gift_chest = require "ui_gift_chest"
                    ui_gift_chest.C2SContentRmbBuyREQ(RmbCostID,iPrice)
                end
            end
        end
        force_guide_system.TriComEvent(force_guide_event.cClickStarGet)

    end
    if self.payBtn then
        self.payBtn.onClick:AddListener(self.payBtnEvent)
    end

    self.updateEvent = function()
        self:UpdateList()
        local newIndex = self.scrollToIndex
        self:InitBtnState()    
        self:SetInitHero()
        self:LoadHeroModel()
        self:ShowMoveAni(self.oldIndex,newIndex)
    end
    event.Register(event.UPDATE_STAR_CHESTID,self.updateEvent) 
    
end

function StarChest:UnsubscribeEvent()
    if self.closedBtn then
        self.closedBtn.onClick:RemoveListener(self.closeBtnHandler)
    end
    if self.getBtn then
        self.getBtn.onClick:RemoveListener(self.getBtnEvent)
    end
    if self.payBtn then
        self.payBtn.onClick:RemoveListener(self.payBtnEvent)
    end
    event.Unregister(event.UPDATE_STAR_CHESTID,self.updateEvent)
    
end

function StarChest:Close()   
	if self.UIRoot and self:IsValid() then
        self:UnsubscribeEvent()
        if self.UIs ~= nil then
            for i, v in pairs(self.UIs) do
                v:Dispose()
                v = nil
            end
            self.UIs = {}
        end
        if self.UIs2 then
            for k,v in pairs(self.UIs2) do
                if v then
                    v:Dispose()
                    v = nil
                end
            end
            self.UIs2 = {}
        end
        if self.scrollTable then
            self.scrollTable:ItemsDispose()
        end
        --检测弱引导
        local button_tips_trigger = require "button_tips_trigger"
        button_tips_trigger.CheckTask()
        if self.initHero then
            self.initHero:Dispose()
            self.initHero = nil
        end
	end
    if self.model then
        self.model:Dispose()
        self.model = nil
    end
    groupModel = nil
    curModelId = nil
	self.__base:Close()
	window = nil
    if ntTimeTicker then
        util.RemoveDelayCall(ntTimeTicker)
        ntTimeTicker = nil
    end
end

local CStarChest = class(ui_base, nil, StarChest)

function Show()
	if window == nil then
		window = CStarChest()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uistarchest.prefab", nil, nil, nil, true)
	end
	
	window:Show()
	return window
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
end

function OnSceneDestroy()
    window_mgr:UnloadModule("ui_hook_boss")
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)