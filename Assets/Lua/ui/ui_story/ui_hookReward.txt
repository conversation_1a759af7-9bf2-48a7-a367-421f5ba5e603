--------------------------------------
--------------掉落奖励----------------
--------------------------------------
local string = string
local require = require
local print = print
local pairs = pairs
local ipairs = ipairs
local math = math
local debug = debug
local table = table
local os = os

local class = require "class"
local ui_base = require "ui_base"
local com_class = require "com_class"
local event = require "event"
local prop_pb = require "prop_pb"
local game_scheme = require "game_scheme"
local iui_item_detail = require "iui_item_detail"
local item_data = require "item_data"
local player_mgr = require "player_mgr"
local goods_item = require "goods_item_new"
local util = require "util"
local force_guide_system=require"force_guide_system"
local force_guide_event=require"force_guide_event"
local lang = require "lang"
local log = require "log"
local ui_window_mgr 	= require "ui_window_mgr"
local source_data = require "source_data"
local color_palette         = require "color_palette"
local sprite_asset          = require "sprite_asset"
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI
local message_box=require"message_box"
local Button = CS.UnityEngine.UI.Button
local Image = CS.UnityEngine.UI.Image
local Text = CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform

local mudule_scroll_list = require "scroll_list"
local CScrollList = mudule_scroll_list.CScrollList
local CScrollListItemBase = mudule_scroll_list.CScrollListItemBase

module("ui_hookReward")

local window = nil
local HookReward = {}
local goodsData = {}
local playerCoin = 0
local playerHeroExp = 0
local playerExp = 0
local playerIdleTime = 0
local hasEquip = false  -- 当前奖励是否有装备
local timer = nil
local vipExtrText = ""

local titleStr = nil
HookReward.widget_table =
{
	listSkin = {path = "bg/box/zhanlipin/list", type = RectTransform},	--掉落列表
	getBtn = {path = "bg/getBtn", type = "Button"},			--领取按钮
	emptyClickBtn={path = "bg/box/getBtn", type = "Button"},	--背景领取按钮
	CloseBtn = {path = "bg/closeBtn", type = "Button", backEvent = true},			--关闭按钮
	
	--好友助力
	--friendHelpBtn = {path = "bg/friendHelpBtn", type = "Button"},			--好友助力按钮
	--friendHelpBtnRect = {path = "bg/friendHelpBtn", type = "RectTransform"},
	--friendHelpText = {path = "bg/friendHelpBtn/Text", type = TextMeshProUGUI},
	--friendHelpRed = {path = "bg/friendHelpBtn/red", type = "RectTransform"},
	
	--城主苦力
	coolieBtn = {path = "bg/coolieBtn", type = "Button"},			--城主苦力按钮
	masterRoot = {path = "bg/masterRoot", type = "RectTransform"},	--城主头像
    masterHead = {path = "bg/masterRoot/head", type = "RectTransform"},	--城主父节点
	nationalFlag = {path = "bg/masterRoot/nationalFlag", type = "Image"},--城主国旗
	
	-- 挂机收益
	Rtsf_guajishouyi = { path = "bg/box/guajishouyi", type = "RectTransform", },
	Text_coin = { path = "bg/box/guajishouyi/bg1/coinText", type = "Text", },
	Text_hun = { path = "bg/box/guajishouyi/bg2/heroText", type = "Text", },
	Rtsf_exp = { path = "bg/box/guajishouyi/bg3", type = "RectTransform", },
	Text_exp = { path = "bg/box/guajishouyi/bg3/expText", type = "Text", },

	--Coolie_Text_coin = { path = "bg/box/guajishouyi/bg4/coinText", type = "Text", },
	--Coolie_Text_tips = { path = "bg/box/guajishouyi/bg4/tipText", type = "Text", },
	--titleText = { path = "bg/titleText", type = "Text", },

	HookTime = { path = "bg/box/guajishouyi/HookTime", type = "Text", },

	vip = { path = "bg/box/guajishouyi/VIP", type = "Text", },

	coinVIPText = { path = "bg/box/guajishouyi/bg1/VIPText", type = "Text", },
	hunVIPText = { path = "bg/box/guajishouyi/bg2/VIPText", type = "Text", },
	expVIPText = { path = "bg/box/guajishouyi/bg3/VIPText", type = "Text", },
	Img_VIP = { path = "bg/box/guajishouyi/viprukouzi", type = "Image", },

	targetTip = { path = "targetTip", type = "Text", },
}

local CRewardScollListItem = com_class.CreateClass(CScrollListItemBase)
function CRewardScollListItem:Create(goSkin)
	CScrollListItemBase.Create(self, goSkin)
	self.ui = goSkin
	self.iconUI = goods_item.CGoodsItem()
	self.iconUI:Init(self.ui.transform, nil, 0.72)
end

function CRewardScollListItem:Draw(data, i)
--	data.id			data.qty	data.sid 	--物品id		
	self.iconUI:SetGoods(nil, data.id or data.propid, data.qty or data.propvalue, function()
		iui_item_detail.Show(data.id or data.propid, data.sid, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, data.rewardID)
	end)
	self.iconUI:SetCountEnable(true)
end

function CRewardScollListItem:Destroy(reself)
	if self.iconUI then
		self.iconUI:Dispose()
		self.iconUI = nil
	end
end

function HookReward:ctor()
	--self._noBg = true
	self.submiteRestTime = 0
end

function HookReward:Init()
    event.Trigger(event.ASH_HIDE_EFFECT, true)
    self.assets = sprite_asset.CSpriteAsset("ui/recharge/vip.asset")
    --获取奖励函数
	local function getReward()
		if hasEquip == true then
			local equipment_mgr = require "equipment_mgr"
			if equipment_mgr.CheckEquipIsFull() == true then
				local message_box = require "message_box"		
				function okCall(callbackData, nRet) 
					if message_box.RESULT_YES == nRet then
						ui_window_mgr:UnloadModule("ui_hookReward")
					end
				end
				equipment_mgr.ShowEquipFullTip()
				return
			end
		end
		event.Trigger(event.CONFIRM_GET_REWARD)
		local music_contorller = require "music_contorller"
		music_contorller.PlayFxAudio(300030)
	end
	self.onGetBtnHandler = function ()			--这个要判断包裹是否满了
		if self.onConfirmClick then
			self.onConfirmClick()
		end
		if playerIdleTime>3600 or force_guide_system.GetState()==2 then --超过一小时或者处于新手引导
               --获取礼物事件,显示获取动画
				playerCoin = nil
				playerHeroExp = nil
				playerExp = nil
				hasEquip = false
				--local tableclone={table.unpack(goodsData)}
				event.Trigger(event.GOT_HOOK_REWARD,goodsData)
				goodsData = {}
			getReward()
			return
		end
		--message_box.Open(lang.Get(280003), message_box.STYLE_YESNO,
		--function(callbackData, nResult)
			--if message_box.RESULT_YES == nResult then
                --获取礼物事件,显示获取动画
				playerCoin = nil
				playerHeroExp = nil
				playerExp = nil
				hasEquip = false
				--local tableclone={table.unpack(goodsData)}
				event.Trigger(event.GOT_HOOK_REWARD,goodsData)
				goodsData = {}
				getReward()
			--else
				ui_window_mgr:UnloadModule("ui_hookReward")
			--end
		--end,
		--0, lang.KEY_OK,lang.KEY_CANCEL)
	end

	self.onCloseBtnHandler = function ()			--close
		ui_window_mgr:UnloadModule("ui_hookReward")
	end
	self.getBtn.onClick:AddListener(self.onGetBtnHandler)
	self.emptyClickBtn.onClick:AddListener(self.onGetBtnHandler)
	self.CloseBtn.onClick:AddListener(self.onCloseBtnHandler)
	self.vip.text = table.concat( {"VIP",player_mgr.GetPlayerVipLevel()})
	local vipCfg = game_scheme:VipPrivilege_0(player_mgr.GetPlayerVipLevel())
	local hour = vipCfg and vipCfg.hangUpAddTime/3600
	if hour >= 0 then
		if hour >= 24 then
			--显示天数
			local day = hour/24
			vipExtrText =string.format( lang.Get(333),string.format( day > 1 and lang.Get(334) or lang.Get(336),day))
		elseif hour < 24 then
			--显示小时
			vipExtrText = string.format( lang.Get(333),string.format( lang.Get(335),hour))
		end
	end

	self:RefreshTimer()
	
	
	
	local extraStr = ""
	local value = vipCfg and string.format( "%0.2f", vipCfg.fIdleResRatio)
	local ratio = (value-1)*100
	if ratio >= 0 then
		extraStr = string.format( lang.Get(332),(ratio))
	end

	self:Update_SubmitGiftData()
	if self.submiteRestTime  and self.submiteRestTime > 0 then
		local submiteCfg = game_scheme:SubmitGift_0(7)
		local extraValue = submiteCfg.count/submiteCfg.countOffset
		extraStr = string.format("%s(%s%d%%)",extraStr,lang.Get(81294),extraValue)
		print(396,"订阅礼包 >>>>>>",self.submiteRestTime,extraStr)
	end
	self.coinVIPText.text = extraStr
	self.hunVIPText.text = extraStr
	self.expVIPText.text = extraStr

	-- self.RefreshIdleTime = function(eventName,idleTime)
	-- 	if window and self:IsValid() then
	-- 		RefreshIdleTime()
	-- 		self:InitData()
	-- 		self:RefreshTimer(idleTime)
	-- 	end
	-- end
	-- event.Register(event.UPDATE_IDLE_TIME, self.RefreshIdleTime)

	self.vipLevelHandle = function(num)								--等级变化
		if window and self:IsValid() and num then
			local max = game_scheme:VipPrivilege_nums()-1
            if num >= max then
                num = max
            end
            local privilege = game_scheme:VipPrivilege_0(num)
            if privilege and self.assets and self.Img_VIP then
				self.assets:GetSprite(privilege.vipLobbyImg, function(sprite)
					if self and self:IsValid() and self.Img_VIP and sprite then
						self.Img_VIP.sprite = sprite
					end
				end)
				if self.vip then
					self.vip.text = table.concat( {"VIP",num})
					self.vip.color = color_palette.HexToColor(privilege.textColor)
				end
            end
		end
	end
	--界面未等待登录消息走完，角色数据可能未下发
	local playerProp = player_mgr.GetPlayerProp()
	if playerProp then
		--AddListener就会调用一次，但又不适合交playerProp.vipLevel作为参数传入，否则会被作为参数每次派发
		if self.vipLevelHandle then
			playerProp:RemoveListener("vipLevel", self.vipLevelHandle)
			playerProp:AddListener("vipLevel", self.vipLevelHandle)
			self.vipLevelHandle(playerProp.vipLevel)
		end
	end


	if self.coolieBtn then
		self.onClickCoolie = function()
			local ui_friend_main = require "ui_friend_main"
			local windowMgr = require "ui_window_mgr"
			ui_friend_main.SetInputParam({defaultWindow = 3})
			windowMgr:ShowModule("ui_friend_main")
		end
		self.coolieBtn.onClick:AddListener(self.onClickCoolie)
	end
end

function HookReward:InitData()
	self.ListInstance:SetListData(goodsData)
	--self.titleText.text = titleStr or lang.Get(7048)

	self.Text_coin.text = ""
	self.Text_hun.text = ""
	self.Text_exp.text = ""
	--self.Coolie_Text_coin.text = ""
	--self.Coolie_Text_tips.text = ""

	local laymain_data = require "laymain_data"
	local nextLevel_cfg = laymain_data.GetHangLevelCfg()
	if nextLevel_cfg then
		local hangUpId = nextLevel_cfg.money
		local hangUp_cfg = game_scheme:HangUp_0(hangUpId)
		if hangUp_cfg then
			local strCoins,strExp,strSouls = laymain_data.GetAutoYieldDesByHangInfo(hangUp_cfg)
			self.Text_coin.text = strCoins
			self.Text_hun.text = strSouls
			self.Text_exp.text = strExp

			if hangUp_cfg.exp == 0 then
				self.Rtsf_exp.gameObject:SetActive(false)
			end
		end
		
		--local coolie_mgr = require "coolie_mgr"
		--local my_dbid = player_mgr.GetPlayerRoleID()
		--local master_data = coolie_mgr.GetMasterData(my_dbid, my_dbid)
		--if master_data then
			--local strCoins,strExp,strSouls = laymain_data.GetAutoYieldDesByCoolie(hangUp_cfg)
			--local cfg = game_scheme:InitBattleProp_0(1715)
			--local ratio = 0
			--if cfg then
			--	ratio = cfg.szParam.data[0]
			--end
			--self.Text_coin.text = ""
			--self.Coolie_Text_coin.text = strCoins
			--self.Coolie_Text_tips.text = string.format(lang.Get(402225), ratio)
		--end

		--202.3.13梁骐显 最大时间改为读表
		local vipCfg = game_scheme:VipPrivilege_0(player_mgr.GetPlayerVipLevel())
		local hangUpAddTime = vipCfg and vipCfg.hangUpAddTime--VIP新增挂机时长
		if playerIdleTime and hangUp_cfg and playerIdleTime > hangUp_cfg.LimitTime + hangUpAddTime then
			playerIdleTime = hangUp_cfg.LimitTime + hangUpAddTime
		end
		local timeStr =  util.FormatTime2(playerIdleTime, "#H:#M:#S")
		self:SetHookTimeText(timeStr)
	end
	self.Rtsf_guajishouyi.gameObject:SetActive(playerCoin~=nil)
	
	local laymain_data = require "laymain_data"
	local passLv = laymain_data.GetPassLevel()
	local hookCfg = game_scheme:HookLevel_0(passLv)
	if hookCfg then
		local hangupCfg = game_scheme:HangUp_0(hookCfg.checkNumber)
		self.targetTip.text = lang.Get(hangupCfg.unLockReward)
	else
		self.targetTip.text = ''
	end
end

--[[资源加载完成，被显示的时候调用]]
function HookReward:OnShow()
	self.__base:OnShow()
	util.DelayCall(0.2,function ( ... )
		if not self:IsValid() then return end
		self.ListInstance = CScrollList:CreateInstance({})
		self.ListInstance:Create(self.listSkin, CRewardScollListItem)
		self:InitData()
		-- local dayNum, timeStr = string.countdown(playerIdleTime)
		-- local hour, min, sec = string.match(timeStr, '(%d+):(%d+):(%d+)')
		-- if (dayNum*24 + hour) >= 12 then
		-- 	hour = 12
		-- 	min = 0
		-- 	sec = 0
		-- end
		force_guide_system.TriEnterEvent(force_guide_event.tEnterHookReward)
	end)


	-- if self.coolieBtn then
	-- 	local face_item = require "face_item"
	-- 	local coolie_mgr = require "coolie_mgr"
	-- 	local my_dbid = player_mgr.GetPlayerRoleID()
	-- 	local master_data = coolie_mgr.GetMasterData(my_dbid, my_dbid)
	-- 	local hasMaster = master_data ~= nil
	-- 	self.coolieBtn.gameObject:SetActive(coolie_mgr.IsUnlockEntrance() and not hasMaster)
	-- 	self.masterRoot.gameObject:SetActive(hasMaster)

	-- 	if hasMaster then
	-- 		local iconUI = self.master_iconUI or face_item.CFaceItem():Init(self.masterHead)
	-- 		iconUI:SetFaceInfo(master_data.faceID, function(faceID)
	-- 			local ui_coolie_other = require "ui_coolie_other"
	-- 			ui_coolie_other.OpenDetail(master_data.dbid, master_data.worldid, master_data.stage, master_data.name,
	-- 					master_data.lv, master_data.faceID, master_data.faceFrameID, master_data.power,
	-- 					master_data.NationalFlag)
	-- 		end)
	-- 		iconUI:SetNewBg(true)
	-- 		iconUI:SetFrameID(master_data.faceFrameID, true)
	-- 		self.master_iconUI = iconUI

	-- 		local const = require "const"
	-- 		if const.IsHKChannel() then --- 港澳台包屏蔽国旗
	-- 			self.nationalFlag.gameObject:SetActive(false)
	-- 		else
	-- 			local flagID = master_data.NationalFlag
	-- 			if flagID and flagID > 0 then
	-- 				self.nationalFlag.gameObject:SetActive(true)
	-- 				coolie_mgr.SetNationalFlag(flagID, self.nationalFlag)
	-- 			else
	-- 				self.nationalFlag.gameObject:SetActive(false)
	-- 			end
	-- 		end
			
	-- 	else
	-- 		self.nationalFlag.gameObject:SetActive(false)
	-- 	end
	-- end
end 
function HookReward:Update_SubmitGiftData()
	--订阅礼包
	local gift_data = require "gift_data"
	local endtime = gift_data.GetBookGiftData()
	self.submiteRestTime = endtime - os.server_time()
end

function HookReward:Close()
    event.Trigger(event.ASH_HIDE_EFFECT, false)
	if self.UIRoot then
		self.getBtn.onClick:RemoveListener(self.onGetBtnHandler)
		self.emptyClickBtn.onClick:RemoveListener(self.onGetBtnHandler)
		if self.coolieBtn then
			self.coolieBtn.onClick:RemoveListener(self.onClickCoolie)
		end
		if self.master_iconUI then
			self.master_iconUI:Dispose()
			self.master_iconUI = nil
		end
		--event.Unregister(event.UPDATE_IDLE_TIME, self.RefreshIdleTime)
		if playerProp then
			playerProp:RemoveListener("vipLevel", self.vipLevelHandle)
		end
		if timer then
			util.RemoveDelayCall(timer)
			timer = nil
		end
	end
	
	if self.onConfirmClick then
		self.onConfirmClick = nil
	end
	
	if self.ListInstance then
        self.ListInstance:ReleaseInstance()
        self.ListInstance = nil
    end
	
	self.__base:Close()
	window = nil

	--新手强制引导事件 ver3
	force_guide_system.TriComEvent(force_guide_event.cEventCloseHookReward)
end

local CHookReward = class(ui_base, nil, HookReward)
function Show()
	if window == nil then
		window = CHookReward()
		window._NAME = _NAME
		window.isBlurBg = true
		window:LoadUIResource("ui/prefabs/uihookreward.prefab", nil,nil, nil, true)
	end
    window:Show()
	return window
end

function IsFullUI()
	return util.CanUseBlurBg()
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	if window ~= nil then
		window:Close()
		window = nil
	end
	titleStr = nil
end

function RefreshIdleTime()
	local rewardIDs = source_data.GetGoodsRewardIDs()
	local hookProp = source_data.GetHookData()
	local hookCoin = math.min(hookProp.maxCoin, hookProp.hookCoin + hookProp.oldhookCoin)
	local hookHeroExp = math.min(hookProp.maxHeroExp,hookProp.hookHeroExp+hookProp.oldhookHeroExp)
	local hookExp = math.min(hookProp.maxExp,hookProp.hookExp+hookProp.oldhookExp)
	local total = hookCoin + hookHeroExp + hookExp
	if source_data.GetRewardIDsLength() > 0 or total > 0 then
		SetRewardData(rewardIDs, hookCoin, hookHeroExp, hookExp, hookProp.idleTime)
	end
end

function HookReward:SetHookTimeText(hookTime)
	self.HookTime.text = string.format("%s<color=#87F425>%s</color><size=22>%s</size>", lang.Get(7049),hookTime,vipExtrText)
end

function HookReward:RefreshTimer(time)
	if timer then
		util.RemoveDelayCall(timer)
		timer = nil
	end
	if time and playerIdleTime and time ~= playerIdleTime then
		playerIdleTime = time
	end
	local laymain_data = require "laymain_data"
	local nextLevel_cfg = laymain_data.GetHangLevelCfg()
	if playerIdleTime then
		timer = util.IntervalCall(1, function(pass)
			if window == nil then   -- 窗口关闭也去掉
				return true
			end
			playerIdleTime = playerIdleTime + 1
			if playerIdleTime and not self.HookTime:IsNull() then
				local hangUpId = nextLevel_cfg and nextLevel_cfg.money
				local hangUp_cfg = hangUpId and game_scheme:HangUp_0(hangUpId)
				local vipCfg = game_scheme:VipPrivilege_0(player_mgr.GetPlayerVipLevel())
				local hangUpAddTime = vipCfg and vipCfg.hangUpAddTime--VIP新增挂机时长
				if playerIdleTime and hangUp_cfg and playerIdleTime > hangUp_cfg.LimitTime + hangUpAddTime then
					playerIdleTime = hangUp_cfg.LimitTime + hangUpAddTime
					self:SetHookTimeText(util.FormatTime2(playerIdleTime, "#H:#M:#S"))
					return true
				else
					self:SetHookTimeText(util.FormatTime2(playerIdleTime, "#H:#M:#S"))
				end
			end
		end)
	end

end
--掉落奖励数据sid、金币数量、英魂数量、经验
function SetRewardData(dataArray, coin, heroExp, hookExp,idleTime)
	------ --print("掉落奖励数据sid、金币数量、英魂数量、经验<<<<<<<<<<<<",coin,heroExp,hookExp,idleTime)
	playerCoin = coin
	playerHeroExp = heroExp
	playerExp = hookExp
	playerIdleTime = idleTime or 0
	-- 把金币和绿魂作为物品显示在滑动列表中
	local count = 0
	goodsData = {}
	
	local coinAndHeroExp = {{id = 1, qty = coin}, {id = 3, qty = heroExp}, {id = 4, qty = hookExp}}
	for i, good in ipairs(coinAndHeroExp) do
		if good.qty > 0 then
			count = count + 1
			goodsData[count] = {}
			goodsData[count].id = good.id
			goodsData[count].qty = good.qty
			goodsData[count].sid = 0
		end
	end

	local reward_mgr = require "reward_mgr"
	if dataArray then
		for _,v in pairs(dataArray) do 
			if v and v.id and v.id > 0 then
				local rewardData = reward_mgr.GetRewardGoods(v.id)	
				if rewardData then
					local hasItem = false
					for key, value in pairs(goodsData) do
						if value.id == rewardData.id then
							------ --print("有重复项，相加，ID=",value.id)
							value["qty"] = value.qty + v.num * rewardData.num
							hasItem = true
						end
					end
					if not hasItem then
						count = count + 1
						goodsData[count] = {}
						goodsData[count].id = rewardData.id
						goodsData[count].qty = v.num * rewardData.num
						goodsData[count].sid = 0
						goodsData[count].rewardID = v.id
						local goodscfg = game_scheme:Item_0(rewardData.id)
						if goodscfg and goodscfg.type == prop_pb.GOODS_TYPE_EQUIP then
							hasEquip = true
						end
					end
				end
			else
				log.Error("找不到掉落奖励id:"..v.id..",num:"..v.num..debug.traceback())
			end
		end
	end
end

function SetGoodsData(dataArray)
	goodsData = dataArray
end
function SetDungeonData(dataArray,title)
	titleStr = title
	goodsData = {}
	playerCoin = nil
	local i = 1
	for k,v in pairs(dataArray) do
		if v>0 then
			goodsData[i] = {id=k,qty=v}
			i = i + 1
		end
	end
end
-- function OnGetRewardRsp(msg)
-- 	if msg.code == error_code_pb.enErr_NoError then
-- 		--清空战利品数据
-- 		goodsData = {}
-- 	end
-- end

function OnSceneDestroy()
	Close()
end
event.Register(event.SCENE_DESTROY, OnSceneDestroy)