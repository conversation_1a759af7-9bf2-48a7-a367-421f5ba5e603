-- ui_hero_biography_data.txt ------------------------------------------
-- author:  熊旋
-- date:    2019.3.9
-- ver:     1.0
-- desc:    英雄剧情数据
--------------------------------------------------------------

local print 		= print
local require 		= require
local table 		= table
local pairs 		= pairs
local dump          = dump

local player_mgr 	= require "player_mgr"


module("ui_hero_biography_data")
local heroData = nil

--[[获取服务器下发的数据]]
function GetNetData()
	local msgData = player_mgr.GetBiographyData()
	return msgData
end
--[[登陆时直接将数据发过来存在这里]]
function SetData(data)
    heroData = data
end

function UpdataHeroState(data)
	local newData = data
	if heroData == nil then
	    heroData = GetNetData()
	end
	for k,v in pairs(heroData) do
		if v.heroID == newData.heroID then
			if v.nLvStage == newData.nLvStage then
				v.type = newData.type    --更新解锁状态
				------ --print("解锁状态更新",newData.type)
			end
		end
	end
	-- dump(heroData)
end

function GetBiographyData(heroID)
	local someonHero = {}
	if heroData and #heroData > 0 then
		for k,v in pairs(heroData) do
			if v.heroID == heroID then
				local tempdata = {}
				tempdata.nLvStage = v.nLvStage
				tempdata.lockType = v.type
				table.insert(someonHero, tempdata)
			end
			--dump(v)
		end
	end
	return someonHero
end