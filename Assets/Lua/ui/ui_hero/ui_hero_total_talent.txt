--@region FileHead
-- ui_hero_total_talent.txt ---------------------------------
-- author:  罗华冠
-- date:    8/26/2020 5:16:36 PM
-- ver:     1.0
-- desc:    英雄全天赋预览
-------------------------------------------------
 

--@region Require
local require   = require
local pairs     = pairs
local string    = string
local table     = table
local dump      = dump

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local ScrollRectTable = CS.UI.UGUIExtend.ScrollRectTable

local lang					= require "lang"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local game_scheme           = require "game_scheme"
local windowMgr             = require "ui_window_mgr"
local hero_mgr = require "hero_mgr"

--@region ModuleDeclare
module("ui_hero_total_talent")
--local interface = require "iui_hero_total_talent"
local window = nil
local UIHeroTotalTalent = {}

local heroEntity = nil
local talentSkillInfo = nil

TalentState = {
    activate = 1,
    unlock = 2,
    lock = 3,
}

--@region WidgetTable
UIHeroTotalTalent.widget_table = {
    talentList = {path = 'bg/List/Viewport/Content', type = ScrollRectTable},
    closeBtn = {path = "closeBtn", type = "Button",backEvent = true},
    unlock ={path="bg/List/Viewport/Content/ListItem/unlock",type="Text",fitArabic=true},
    lock ={path="bg/List/Viewport/Content/ListItem/lock",type="Text",fitArabic=true},
    text ={path="bg/List/Viewport/Content/ListItem/desc/text",type="Text",fitArabic=true},
}
 

--@region WindowCtor
function UIHeroTotalTalent:ctor(selfType)
    self.__base:ctor(selfType)
end  

-- 英雄实体的入口
function SetHeroEntity(entity)
    heroEntity = entity
    InitTalentSkillInfo()
end

-- 虚拟实体数据 仅做展示用
-- @params talentSkills :技能id的表结构
function SetFakeHeroEntity(_heroID,_starLv,_talentSkills)
    local entity = {}
    entity.heroID = _heroID
    entity.numProp = {starLv=_starLv}
    entity.talentSkills = {}
    -- 规避表结构非连续
    local index = 1
    --dump(_talentSkills)
    for k,v in pairs(_talentSkills)do
        local data = {}
        data.talentID = v
        data.lv = 1
        entity.talentSkills[index] = data
        index=index+1
    end
    heroEntity = entity
    --dump(heroEntity)
    InitTalentSkillInfo()
end


function InitTalentSkillInfo()
    local count = 0
    if heroEntity then
        talentSkillInfo = {}
        local index = 1 -- 槽位
        for i=1,15 do
            local cfg = game_scheme:HeroPro_0(heroEntity.heroID,i)
            if cfg and cfg.talentSkill then
                local data = {}
                if cfg.talentSkill == 0 then
                    data.unlock = false -- 0代表此星级不能解锁天赋
                    data.count = count
                else
                    count = count + 1
                    data.index = index
                    data.unlock = true
                    data.talent = cfg.talentSkill -- 解锁哪个槽位
                    data.starLv = cfg.starLv -- 解锁哪个槽位
                    data.count = count
                    index = index + 1
                end
                talentSkillInfo[cfg.starLv] = data
            end
        end
    end
end

--@region WindowInit
--[[窗口初始化]]
function UIHeroTotalTalent:Init()
    self:SubscribeEvents()
    self:CreateIconAsset()
    self:InitList()
end  


function UIHeroTotalTalent:CreateIconAsset()
	local card_sprite_asset=require"card_sprite_asset"
	self.talentIconAsset=card_sprite_asset.CreateSpriteAsset()
end

function UIHeroTotalTalent:DisposeIconAsset()
	if self.talentIconAsset then
		self.talentIconAsset:Dispose()
		self.talentIconAsset=nil
	end
end

function UIHeroTotalTalent:GetProjIconSprite(name,callback)
	self.talentIconAsset:GetSprite(name,callback)
end


function UIHeroTotalTalent:InitList()
    self.talentList.onItemRender = onItemRenderBottom
    
    self.talentList.onItemDispose = function(scroll_rect_item, index)
        if scroll_rect_item and scroll_rect_item.data and scroll_rect_item.data.iconArr ~= nil then
            for k,item in pairs(scroll_rect_item.data["iconArr"]) do
                item:Dispose()
            end
        end
    end
end


function onItemRenderBottom(scroll_rect_item, index, dataItem)
    scroll_rect_item.data = scroll_rect_item.data or {index, dataItem}
    scroll_rect_item.data[1] = index
    scroll_rect_item.data[2] = dataItem
    
    local descText = scroll_rect_item:Get('desc')
    local nameText = scroll_rect_item:Get('name')
    local icon = scroll_rect_item:Get('icon')
    local indexText = scroll_rect_item:Get("index")
    local lockText = scroll_rect_item:Get("lock")
    local unlockText = scroll_rect_item:Get("unlock")
    local add = scroll_rect_item:Get("add")
    local lockIcon = scroll_rect_item:Get("lockIcon")
    local indexTrans = scroll_rect_item:Get("indexTrans")
    local desc = scroll_rect_item:Get("desc")
    local infoBtn = scroll_rect_item:Get("infoBtn")

    indexTrans.gameObject:SetActive(false)
    desc.gameObject:SetActive(false)
    add.gameObject:SetActive(false)
    lockText.gameObject:SetActive(false)
    unlockText.gameObject:SetActive(false)
    nameText.gameObject:SetActive(false)
    descText.gameObject:SetActive(false)
    icon.gameObject:SetActive(false)
    lockIcon.gameObject:SetActive(false)
    infoBtn.gameObject:SetActive(false)

    if dataItem.state == TalentState.activate then
        -- 已激活
        desc.gameObject:SetActive(true)
        nameText.gameObject:SetActive(true)
        descText.gameObject:SetActive(true)
        icon.gameObject:SetActive(true)
        infoBtn.gameObject:SetActive(true)

        local cfg = game_scheme:HeroSkill_0(dataItem.talentID,1)
        if cfg then
            descText.text = lang.Get(cfg.descID)
            nameText.text = lang.Get(cfg.nameID)
            
            indexTrans.gameObject:SetActive(true)
            indexText.text = dataItem.index
            window:GetProjIconSprite(cfg.icon,function (sprite)
                icon.sprite=sprite
            end)
        end
    elseif dataItem.state == TalentState.unlock then
        -- 已解锁
        add.gameObject:SetActive(true)
        unlockText.gameObject:SetActive(true)
        unlockText.text = lang.Get(5912)
    elseif dataItem.state == TalentState.lock then
        -- 未解锁
        lockIcon.gameObject:SetActive(true)
        lockText.gameObject:SetActive(true)
        lockText.text = string.format(lang.Get(5913),lang.Get(hero_mgr.Hero_Star_Desc[dataItem.starLv]))
    end

    -- local yellowplus = scroll_rect_item:Get('yellowplus')
    -- local redplus = scroll_rect_item:Get('redplus')
    -- local blueplus = scroll_rect_item:Get('blueplus')
    -- local purpleplus = scroll_rect_item:Get('purpleplus')
    -- local whiteplus = scroll_rect_item:Get('whiteplus')
    -- local gradebg = scroll_rect_item:Get('gradebg')
    -- local zuanshi = scroll_rect_item:Get('zuanshi')
    -- local img_zuanshi = scroll_rect_item:Get('img_zuanshi')
    local heroQuality = scroll_rect_item:Get('heroQualityItem')
    local heroStarLv = dataItem.starLv
    hero_mgr.SetHeroStarDisplay(heroQuality,heroStarLv)
    -- if heroStarLv >= hero_mgr.Hero_Star.White then
    --     if heroStarLv >= hero_mgr.Hero_Star.White and heroStarLv <= hero_mgr.Hero_Star.White_1 then
    --         zuanshi:Switch(5)
    --     elseif heroStarLv >= hero_mgr.Hero_Star.White_2 and heroStarLv <= hero_mgr.Hero_Star.White_4 then
    --         zuanshi:Switch(6)
    --     elseif heroStarLv == hero_mgr.Hero_Star.White_5 then
    --         zuanshi:Switch(7)
    --     end
	-- elseif heroStarLv >= hero_mgr.Hero_Star.Red and heroStarLv <= hero_mgr.Hero_Star.RedPlus then
	-- 	zuanshi:Switch(4)
	-- elseif heroStarLv >= hero_mgr.Hero_Star.Yellow and heroStarLv <= hero_mgr.Hero_Star.YellowPlus then
	-- 	zuanshi:Switch(3)
	-- elseif heroStarLv >= hero_mgr.Hero_Star.Purple and heroStarLv <= hero_mgr.Hero_Star.PurplePlus then
	-- 	zuanshi:Switch(2)
	-- elseif heroStarLv == hero_mgr.Hero_Star.BluePlus or heroStarLv == hero_mgr.Hero_Star.Blue then
	-- 	zuanshi:Switch(1)
	-- elseif heroStarLv == hero_mgr.Hero_Star.Green then
	-- 	zuanshi:Switch(0)
    -- end
    -- --img_zuanshi:SetNativeSize()
    
	-- yellowplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.YellowPlus)
	-- redplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.RedPlus or heroStarLv == hero_mgr.Hero_Star.White_1)
    -- blueplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.BluePlus)
    -- purpleplus.gameObject:SetActive(heroStarLv == hero_mgr.Hero_Star.PurplePlus)
    -- whiteplus.gameObject:SetActive(heroStarLv >= hero_mgr.Hero_Star.White_2 and heroStarLv <= hero_mgr.Hero_Star.White_5)
    
    -- gradebg:Switch(heroStarLv <= hero_mgr.Hero_Star.White_1 and 0 or 1)

    local func = {}
    func["infoEvent"] = function ()
        -- 查看技能详情
        local ui_skill_info = require "ui_skill_info"
        local cfg = game_scheme:HeroSkill_0(dataItem.talentID,dataItem.lv)
        ui_skill_info.SetTalentSkillInfo(cfg.nameID,cfg.descID)
        windowMgr:ShowModule("ui_skill_info")
    end

    scroll_rect_item.InvokeFunc = function(funcname)
		func[funcname]()
    end
end

function UIHeroTotalTalent:RefreshUI()
    self:RefreshTalentList()
end

function UIHeroTotalTalent:RefreshTalentList()
    self.talentList.data = self:GetTalentInfo()
    self.talentList:Refresh(-1, -1)
end

-- index 槽位
function CheckTalentIndex(index,talentID)
    local talentCfg = game_scheme:TalentSkill_0(index)
    local lv = 0
    if talentCfg then
        for i=1,5 do
            local talentCfgID = talentCfg["talentSkill"..i].data[0]
            if talentCfgID == talentID then
                lv = i
                break
            end
        end
    end
    return lv
end

function UIHeroTotalTalent:GetTalentInfo()
    local info = {}
    if heroEntity then
        local talents = heroEntity.talentSkills
        for i=1,4 do -- 客户端写死了 4个槽位
            local v = talents[i]
            if v then
                local data = {}
                if v.talentID and v.talentID ~= 0 then
                    data.state = TalentState.activate
                    -- 有激活的天赋
                    data.talentID = v.talentID
                    data.lv = v.lv
                    data.index = CheckTalentIndex(i,v.talentID) -- 天赋序号
                    for m,n in pairs(talentSkillInfo)do
                        if n.unlock then
                            -- 有槽位技能的
                            if i == n.index then
                                data.starLv = n.starLv
                                break
                            end
                        end
                    end
                else
                    -- 判断是否解锁
                    for m,n in pairs(talentSkillInfo)do
                        if n.unlock then
                            -- 有槽位技能的
                            if i == n.index then
                                data.starLv = n.starLv
                                if heroEntity.numProp.starLv >= n.starLv then
                                    -- 已解锁
                                    data.state = TalentState.unlock
                                else
                                    -- 未解锁
                                    data.state = TalentState.lock
                                end
                                break
                            end
                        end
                    end
                end
                table.insert( info, data)
            end
        end
    end
    return info
end

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIHeroTotalTalent:OnShow()
    self:RefreshUI()
end  

 

--@region WindowOnHide
--[[界面隐藏时调用]]
function UIHeroTotalTalent:OnHide()
 
 
end  

 

--@region WindowSetInputParam
--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
function UIHeroTotalTalent:SetInputParam(p)
	self.inputParam = p

    --如果正在显示，则更新一次窗口
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
	
 
 
end  

 

--@region WindowBuildUpdateData
--[[构建UI更新数据]]
function UIHeroTotalTalent:BuildUpdateData()
 
 
end  

 

--@region WindowUpdateUI
--[[资源加载完成，被显示的时候调用]]
function UIHeroTotalTalent:UpdateUIPage()
	self:BuildUpdateData()
 
 
end  

 

--@region WindowClose
function UIHeroTotalTalent:Close()
    net_route.UnregisterMsgHandlers(MessageTable)
    if self:IsValid() then
        self:UnsubscribeEvents()
    end
    self.__base:Close()
    heroEntity = nil
    window = nil
end  

 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIHeroTotalTalent:SubscribeEvents()
    self.closeEvent = function ()
        windowMgr:UnloadModule("ui_hero_total_talent")
    end
    self.closeBtn.onClick:AddListener(self.closeEvent)
end  

 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIHeroTotalTalent:UnsubscribeEvents()
    self.closeBtn.onClick:RemoveListener(self.closeEvent)
end  

local CUIHeroTotalTalent = class(ui_base, nil, UIHeroTotalTalent)
 

function Show()
    if window == nil then
        window = CUIHeroTotalTalent()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiherototaltalent.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

 

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
 

