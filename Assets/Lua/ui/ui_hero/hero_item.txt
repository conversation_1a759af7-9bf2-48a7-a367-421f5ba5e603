-- hero_item.txt ------------------------------------------
-- author:  梁骐显
-- date:    2018.11.07
-- ver:     1.0
-- desc:    英雄头像item，可以通用
--------------------------------------------------------------

local require  = require
local print    = print
local type     = type
local pairs    = pairs
local tostring = tostring
local ipairs   = ipairs
local string   = string
local typeof   = typeof

local Vector3                = CS.UnityEngine.Vector3
local RectTransform          = CS.UnityEngine.RectTransform
local GrayImageType          = typeof(CS.War.UI.ImageGray)
local GameObject             = CS.UnityEngine.GameObject
local SpriteSwitcher         = CS.War.UI.SpriteSwitcher
local ParticleSystemRenderer = CS.UnityEngine.ParticleSystemRenderer
local Common_Util            = CS.Common_Util.UIUtil

local screen_util = require "screen_util"
local Animation              = CS.UnityEngine.Animation
local CanvasGroup            = CS.UnityEngine.CanvasGroup
local SpriteRenderer         = CS.UnityEngine.SpriteRenderer
local RawImage               = CS.UnityEngine.UI.RawImage
local Image                  = CS.UnityEngine.UI.Image
local TextMeshProUGUI         = CS.TMPro.TextMeshProUGUI

local log               = require "log"
local card_sprite_asset = require "card_sprite_asset"
local game_scheme       = require "game_scheme"
local hero_mgr          = require "hero_mgr"
local lang              = require "lang"
local CModelViewer      = require "modelviewer"
local sort_order        = require "sort_order"
local util              = require "util"
local idle              = require "idle"
local render_tool       = require "render_tool"
local const             = require "const"
local ui_soul_link_mgr = require "ui_soul_link_mgr"
local event = require "event"


module("hero_item")
local entityResPath = "ui/prefabs/heroitem.prefab"
local animEntityResPath = "ui/prefabs/heroanimitem.prefab"
local nullIconArr = {
    4011, 4011, 4011, 4011, 
    4012, 
    4013, 4013, 
    4014, 4014,4014, 4014, 4014, 4014}
local HeroItem = {}
-- local canvasRoot = GameObject.Find("/UIRoot/CanvasWithMesh")
-- canvasRoot = canvasRoot.transform
local QUALITY_MASK_ENUM ={0,0,0,0,0,0,1,0,1,2,2,2,2,2,2}
local effectScales = {}

HeroItem.widget_table = {
    button = {path = "", type = "Button"},
    -- image = {path = "Active/icon", type = "Image"},
    -- frame = {path = "Active/Frame", type = "Image"},
    frameAnim = {path = "Active", type = Animation},
    longImage = {path = "Active/longIcon", type = "Image"},
    longImageGray = {path = "Active/longIcon", type = GrayImageType},
    longFrameBg = {path = "Active/bg", type = SpriteSwitcher},
    longFrameBgGray = {path = "Active/bg", type = GrayImageType},
    longFrame = {path = "Active/longFrame", type = SpriteSwitcher},
    longFrameGray = {path = "Active/longFrame", type = GrayImageType},
    longFrameRtsf = {path = "Active/longFrame", type = "RectTransform"},
    -- longStarBg = {path = "Active/longStarBg", type = SpriteSwitcher},
    levelBg = {path = "Active/levelBg", type = "Image"},
    levelText = {path = "Active/level", type = TextMeshProUGUI},
    typeIcon = {path = "Active/type", type = SpriteSwitcher},
    typeIconGray = {path = "Active/type", type = GrayImageType},
    typeFrame = {path = "Active/typeFrame", type = "Image"},
    typeFrameGray = {path = "Active/typeFrame", type = GrayImageType},
    profTypeIcon = {path = "Active/profType", type = "Image"},
    profTypeIconGray = {path = "Active/profType", type = GrayImageType},
    superStar = {path = "superStar/Text", type = "Text"},
    superStarImage = {path = "superStar", type = "Image"},
    -- stars = {path = "Active/stars", type = RectTransform},
    heroStars = {path = "Active/heroStar",type = RectTransform},
    lockImage = {path = "state/lock", type = "Image"},
    selectedImage = {path = "state/selected", type = "Image"},
    -- addImage = {path = "state/add", type = "Image"},
    -- mask = {path = "state/mask", type = "Image"},
    maskBig = {path = "state/maskBig", type = "Image"},
    maskNew = {path = "state/maskNew", type = SpriteSwitcher},
    highlight = {path = "highlight", type = "Image"},
    hpSlider = {path = "hpSlider", type = "RectTransform"},
    hp = {path = "hpSlider/hp", type = "RectTransform"},
    mp = {path = "hpSlider/mp", type = "RectTransform"},
    rect_hpSlider = {path = "hpSlider", type = "RectTransform"},
    newSign={path = "newSign", type = "Text"},
    liuguangEffect = {path = "effect", type = RectTransform},
    selectEffect = {path = "selectEffect", type = RectTransform},
    alphaCanva = {path = "", type = CanvasGroup},
    bossIcon = {path = "bossIcon", type = "Image"},
    newCHImg={path = "NewSignCH", type = SpriteRenderer},
    newENImg={path = "NewSignEN", type = SpriteRenderer},
    maxLevelTips = {path = "maxLevelTips", type = TextMeshProUGUI},
    maxLevelTips2 = {path = "maxLevelTips2", type = TextMeshProUGUI},
    trialSignRoot = {path = "trialSign", type = RectTransform},--试用标志
    trialTimesLimit = {path = "trialSign/Text", type = "Text"},--试用次数限制
    
    decorateRoot = {path = "Active/decorate", type = "RectTransform"},
    decorateImg1 = {path = "Active/decorate/Image1", type = "Image"},
    decorateImg2 = {path = "Active/decorate/Image2", type = "Image"},
    decorateImg3 = {path = "Active/decorate/Image3", type = "Image"},
    --newSS={path = "NewSign", type = SpriteSwitcher},
    
    forceWeaponRect = {path = "forceWeapon" , type = RectTransform},
    forceWeaponIconImg = {path = "forceWeapon" , type = Image},
    forceWeaponIcon = {path = "forceWeapon" , type = SpriteSwitcher},

    --Stars=========================
    -- stars_1 = {path = "Active/stars/star_1", type = RectTransform},
    -- stars_2 = {path = "Active/stars/star_2", type = RectTransform},
    -- stars_3 = {path = "Active/stars/star_3", type = RectTransform},
    -- stars_4 = {path = "Active/stars/star_4", type = RectTransform},
    -- stars_5 = {path = "Active/stars/star_5", type = RectTransform},
    -- stars_6 = {path = "Active/stars/star_6", type = RectTransform},

    sigilRoot = {path = "sigilRoot" , type = RectTransform},
    sigilIcon = {path = "sigilRoot/Image" , type = "Image"},
    scoreSp = {path = "scoreIcon" , type = SpriteSwitcher},
    scoreTfm = {path = "scoreIcon" , type = RectTransform},
    godEquipRoot = {path = "artifactRoot" , type = RectTransform},

    headPortraitTfm = { path = "pifubiaoqianDI", type = RectTransform,luaObjList={"gameObject"} }, --头像
    headPortraitTxt = { path = "pifubiaoqianDI/PiFu", type = "Text" },
    
    teamObj = {path = "Active/TeamBg",type = RectTransform},
    teamText = {path = "Active/TeamBg/Text",type = "Text"},

    jobIcon = {path = "Active/job", type = SpriteSwitcher},
}

shortHPSliderPos = {x=0,y=-84}
longHPSliderPos = {x=0,y=-94}

local forceEquipPos = {
    [-1] = 31.3,
    [0] = 30.7,
    [1] = 30.7,
    [2] = 27.9,
    [3] = 27.9,
    [4] = 20.5,
    [5] = 18.5,
}

function HeroItem:ctor(selfType, ownerModuleName)
    self.__base:ctor(selfType,"hero_item")
    self.selected = util.DirtyValue(false) --英雄item的状态
    self.hero = nil
    self.callback = nil
    spriteAsset = spriteAsset or card_sprite_asset.CreateHeroAsset()
    typeIconAsset = typeIconAsset or card_sprite_asset.CreateSpriteAsset()

    self.ownerModuleName = ownerModuleName

    --用于状态标记falg
    self.maskFlag = util.DirtyValue(false)
    self.highlightFlag = util.DirtyValue(false)
    self.lockFlag = util.DirtyValue(false)
    self.selectedFlag = util.DirtyValue(false)
    self.grayFlag = util.DirtyValue(false,true)
    self.halfAlpha = util.DirtyValue(false,true)
    self.interactableFlag = util.DirtyValue(true)
    self.CareerFlag = util.DirtyValue(false)
    self.typeFlag = util.DirtyValue(true,true)
    self.hpFlag = util.DirtyValue(false,true)
    self.hpValue = util.DirtyValue(1,true)
    self.mpValue = util.DirtyValue(1,true)
    self.enableNewFlag = util.DirtyValue(false)
    
    self.showTeamIcon = util.DirtyValue(false)
    self.teamValue = util.DirtyValue("",false)
    
    -- self.enableTrialLimit = false--试用次数限制显示
    self.battleType = nil--战斗类型
    self.trialMask = nil--试用次数为0遮罩
    self.trialFlag = nil--试用标志显示（仅显示标志，不显示次数）

 	self.showEffect = false  -- 是否显示五星流光特效
    self.bossFlag = util.DirtyValue(false)
    self.maxLvFlag = util.DirtyValue(false)
    self.maxLvFlag2 = util.DirtyValue(false)
    self.showLvFlag = util.DirtyValue(false,true)
    self.isSoulLinkingFlag = util.DirtyValue(false,true)
    self.lockFunc = nil
	self.isHeroanimitem = false
	self.scale = 1
    self.effectOrder = 0     -- 五星流光层级
	self.maskType = 0        -- 五星流光特效的masking类型
    self.newOrder = 0
    self.useIncrementOrder = false
    self.overrideEffectRes = nil		    -- 覆盖流光特效资源
    self.overrideEffectScale = 1            -- 覆盖流光特效缩放
    --锁定回复

    self.renderTxInfo = nil
    self.decorateData= nil

    self.forceWeaponOn = false
    self.forceWeaponIndex = -1

    -- 考虑使用 displayFx 配置，替换掉 liuguangEffect 效果的加载，出于稳定性考虑，先保留 liuguangEffect 加载逻辑
    self.displayFx = 
    {
        select = 
        {
            valid = false,
            effectImageName = "selectEffect",
        },
    }
    
    self.sigilFlag = false --显示印记标识
    self.scoreFlag = false --显示评分标识
    self.godEquipFlag = false --显示神器标识
end
---isSetTip 是否开启标签
function HeroItem:Init(parentTrans, callback, scale, itemType, initCompleteCallback,updateState,isSetTip)
	self.scale = scale or 1
    local path = nil
    if itemType then
		self.isHeroanimitem = true
        path = animEntityResPath
        -- local orderStart, orderEnd = sort_order.ApplyIndexs(canvasRoot, 2) --重新获得层级
        -- self.orderStart = orderStart
        -- self.orderEnd = orderEnd
    else
        path = entityResPath
    end
    self.itemType = itemType
    self.isSetTip = isSetTip
    self:LoadResource(
        path,
        "",
        function()
            self:RegistEvents()
            if not self.isHeroanimitem then
                self.transform.sizeDelta = {x=115, y=115}
            end
            if callback then
                callback(self)
            end
            if scale then
                self.transform.localScale = {x=scale, y= scale, z= scale}
            else
                self.transform.localScale = Vector3.one
            end

            self.transform.anchoredPosition3D = {x=0, y= 0, z= 0}
			self.transform.anchorMin = {x=0.5, y= 0.5} 
            self.transform.anchorMax = {x=0.5, y= 0.5} 
            if updateState == nil or updateState then
            self:SetState()
            end
            
			local lang_util = require "lang_util"
			lang_util.SetFont(self.transform.gameObject)

            self.loaded = true

            --所有初始化处理完成后调用，请不要在下面加初始化逻辑
            if initCompleteCallback then
                initCompleteCallback(self)
            end
        end,
        idle.ITEM_IO,
        parentTrans.transform
    )

    self.initLiuGuangEffect = false
    self.targetEffectModelViewerResPath = nil
    return self
end

function HeroItem:ShowOrHideTeamIcon(value,index)
    self.showTeamIcon.value = value or false;
    self.teamValue.value = index or "";
    --Common_Util.SetActive(self.teamObj,value)
    --if value then
    --    self.teamText.text = index;
    --end
end

function HeroItem:OnResLoad()
end

function HeroItem:IsSelected()
    return self.selected.value
end

function HeroItem:SetEffectResPath(flag, resPath)
    if resPath == nil or resPath == "" then
        log.Warning("SetEffectResPath 不能为空")
        return
    end

    local fxInfo = self.displayFx[flag]
    if fxInfo == nil then
        fxInfo = {}
        self.displayFx[flag] = fxInfo
    end
    fxInfo.resPath = resPath

    return fxInfo
end

--初始化英雄信息
function HeroItem:InitHeroInfo(hero)
    self.hero = hero

    -- 需要初始化 self.hero 后才能获得资源路径
    -- 更换英雄时关闭所有加载特效
    local k,v
    for k,v in pairs(self.displayFx) do
        self:SetEffectValid(k, false)
    end
end

-- 英雄可选择合成时特效果
function HeroItem:GetSelectResPath()
    if self.hero == nil then
        log.Warning("GetSelectResPath 未设置英雄数据")
        return nil
    end

    local cfg = game_scheme:Hero_0(self.hero.heroID)
    local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv or (cfg and cfg.starLv)

    local resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_lan/prefabs/effect_ui_juexinyanjiusuo_shangzhen_lan.prefab"
    if starsLv == hero_mgr.Hero_Star.BluePlus then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_lan_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_lan_02.prefab"
    elseif starsLv == hero_mgr.Hero_Star.Purple then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi.prefab"
    elseif starsLv == hero_mgr.Hero_Star.PurplePlus then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi_02.prefab"
    elseif starsLv == hero_mgr.Hero_Star.Yellow then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang.prefab"
    elseif starsLv == hero_mgr.Hero_Star.YellowPlus then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang_02.prefab"
    elseif starsLv == hero_mgr.Hero_Star.Red then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong.prefab"
    elseif starsLv == hero_mgr.Hero_Star.RedPlus then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong_02.prefab"
    elseif starsLv >= hero_mgr.Hero_Star.White then
        resPath = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_bai/prefabs/effect_ui_juexinyanjiusuo_shangzhen_bai.prefab"
    end

    return resPath
end

function HeroItem:PlayFrameAnim()
    if self.isHeroanimitem then
        self.frameAnim:Play()
    end
end

--英雄不可按
function HeroItem:Interactable(flag)
    self.interactableFlag.value = flag
    self:SetState()
end

--蒙黑英雄
function HeroItem:MaskHero(flag, interactable)
    self:Interactable(interactable and interactable or not flag)
    self.maskFlag.value = flag
    self:SetState()
end

--锁住英雄
function HeroItem:LockHero(flag, interactable)
    self:Interactable(interactable and interactable or not flag)
    self.maskFlag.value = flag
    self.lockFlag.value = flag
    self:SetState()
end

function HeroItem:SetHeroMaskAlpha(alpha)
    local maskType = QUALITY_MASK_ENUM[self.hero.numProp.starLv - 2]
    if maskType == 0 then
        if self.maskBig then
            local c = self.maskBig.color
            c.a = alpha
            self.maskBig.color = c
        end
    -- else
    --     local newImage = self.maskNew.gameObject:GetComponent(typeof(Image))
    --     local c = newImage.color
    --     c.a = 1
    --     newImage.color = c
    end
end

--选择英雄
function HeroItem:SelectHero(flag)
    self.maskFlag.value = flag
    self.selectedFlag.value = flag
    self.selected = flag
    self:SetState()
end

--是否显示新英雄标记
function HeroItem:NewSignEnable(bool,order)
    self.enableNewFlag.value=bool
    self.newOrder = order
    self:SetState()
end

--判断是否显示试用次数限制，通过战斗类型
function HeroItem:TrialLimitEnable(_type)
    -- print("qsy_yxsy:[hero_item]TrialLimitEnable>>>>",_type)
    -- self.enableTrialLimit = bool
    self.battleType = _type
    self:SetState()
end

--是否显示试用标志
function HeroItem:TrialFlagEnable(bool)
    self.trialFlag = bool
    self:SetState()
end

--是否显示Boss标记
function HeroItem:BossFlagEnable(bool)
    self.bossFlag.value = bool
    self:SetState()
end

--是否显示最高等级标记
function HeroItem:MaxLvFlagEnable(bool, text, bool2)
    self.maxLvFlag.value = bool
    self.maxLvText = text
    self.maxLvFlag2.value = bool2
    self:SetState()
end

--原力武器图标
function HeroItem:ForceWeaponFlag(stage)
    self.forceWeaponOn = stage > 0
    self.forceWeaponIndex =stage - 1
    self:SetState()
end

--[[是否显示精英特效
@param bool 是否显示
@param maskType render的显示类型，主要处理滑块光效穿透问题
]]
function HeroItem:HeroEffectEnable(bool, order, maskType, useIncrement)
	if self.isHeroanimitem == true then
		return
	end
	self.showEffect = bool
	self.maskType = maskType or 0
    self.effectOrder = order
    self.useIncrementOrder = useIncrement or false
end

--[[设置覆盖流光特效
@param number 流光特效类型
]]
function HeroItem:SetOverrideEffect(res_path, scale)
    self.overrideEffectRes = res_path
    self.overrideEffectScale = scale or 1
end

function HeroItem:DisplayImage(transform, renderTxInfo, enabled)
    local rawimage = transform:GetComponent(typeof(RawImage))
    if rawimage == nil then
        log.Warning("HeroItem 使用粒子系统转RT时需要绑定 RawImage组件")
        -- 不能使用指定纹理，等待 UnLoadHeroLiuGuangEffect 进行释放
        return false
    end
    -- log.Error("DisplayImage, instance id:",rawimage.gameObject:GetInstanceID())
    return renderTxInfo:SetImage(transform, rawimage, enabled)
end

-- fxInfo.loadingResSet: 加载中的资源集
-- fxInfo.displayResPath: 当前显示的资源
-- fxInfo.resPath: 当前需要显示的资源
-- return: 是否需要加载 resPath。即当前需要显示的资源既不在加载中，也不在显示
function HeroItem:UpdateEffectLoadState( fxInfo )
    if fxInfo.loadingResSet == nil then
        fxInfo.loadingResSet = {}
    end
    local bLoading = fxInfo.loadingResSet[fxInfo.resPath]
    if bLoading then
        return false
    end

    if fxInfo.displayResPath == fxInfo.resPath then
        return false
    end

    return true
end

function HeroItem:AddEffectLoadingRes(fxInfo, resPath )
    if fxInfo.loadingResSet == nil then
        fxInfo.loadingResSet = {}
    end
    fxInfo.loadingResSet[resPath] = true
end

function HeroItem:RemoveEffectLoadingRes(fxInfo, resPath )
    if fxInfo.loadingResSet == nil then
        return
    end
    fxInfo.loadingResSet[resPath] = nil
end

function HeroItem:DisplayEffect(fxInfo)
    fxInfo.renderTxInfo = render_tool.GetRenderTextureByTag(fxInfo.resPath, self.ownerModuleName)
    if fxInfo.renderTxInfo then
        -- 当前显示的资源路径
        fxInfo.displayResPath = fxInfo.resPath
        local effectTrans = self[fxInfo.effectImageName]
        if effectTrans == nil then
            log.Error("需设置用于显示粒子效的图片:"..tostring(fxInfo.effectImageName))
        else
            self:DisplayImage(effectTrans, fxInfo.renderTxInfo, true)
        end
    else
        if self:IsDisposed() then
            -- 界面已经销毁,不再加载资源
            return
        end

        -- loadingResPath： 加载中资源。displayResPath：当前显示资源。resPath：新显示资源
        -- 防止同一个 item 对同一资源在资源加载过程重复释放和加载，释放时根据资源路径释放，次数会变少
        -- 即加载过程中，先卸载，再加载，若不作处理，调用两次 render_tool.LoadResource，但卸载时，是按 ab name 卸载，会减少卸载次数
        local needLoadRes = self:UpdateEffectLoadState(fxInfo)
        if not needLoadRes then
            return
        end
        self:AddEffectLoadingRes(fxInfo, fxInfo.resPath)

        render_tool.LoadResource(fxInfo.resPath, function(assetbundleName, renderGameObject)
            local effectTrans = self[fxInfo.effectImageName]
            if effectTrans == nil then
                renderGameObject.Dispose()
                return
            end

            if self:IsDisposed() then
                -- 界面已经销毁,不再加载资源
                renderGameObject.Dispose()
                return
            end

            if fxInfo.resPath ~= assetbundleName or effectTrans == nil or util.IsObjNull(effectTrans) then
                --界面不再需要加载完成的资源，或已销毁
                renderGameObject.Dispose()
                return
            end

            local resPath = assetbundleName
            self:RemoveEffectLoadingRes(fxInfo, resPath)

            -- 加载特效资源完成时，可能当前帧已有其它 item 已完成资源加载，此处先从缓存中查询一次
            fxInfo.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
            if fxInfo.renderTxInfo then
                fxInfo.displayResPath = resPath
                self:DisplayImage(effectTrans, fxInfo.renderTxInfo, true)
                return
            end

            -- 缓存中不存在，创建新的 RT 显示图片
            renderGameObject.SetMaxParticleSize(100)
            renderGameObject.SetSize(Vector3.one * 0.01)

            local rawimage = effectTrans:GetComponent(typeof(RawImage))
            if rawimage then
                fxInfo.displayResPath = resPath
                fxInfo.renderTxInfo = renderGameObject.RenderGameObject(resPath,132,170,render_tool.RenderMode.Single,0,-0.02,10, nil, self.ownerModuleName)
                if fxInfo.renderTxInfo then
                    fxInfo.renderTxInfo:SetImage(effectTrans, rawimage, true)
                end
            else
                log.Error(effectTrans.."使用粒子系统转RT时需要绑定 RawImage 组件")
                renderGameObject.Dispose()
            end
        end)
    end
    fxInfo.displaying = true
    return true
end

function HeroItem:DisplayParticleWithRT(resPath)
    self.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
    if self.renderTxInfo then        
        self.effectModelViewerResPath = resPath
        self:DisplayImage(self.liuguangEffect, self.renderTxInfo, true)
    else        
        if self.targetEffectModelViewerResPath == resPath or self.effectModelViewerResPath == resPath then
            -- 防止同一个 item 对同一资源在资源加载过程重复释放和加载，释放时根据资源路径释放，次数会变少            
            return true
        end
        if self:IsDisposed() then
            -- 界面已经销毁,不再加载资源
            return
        end

        self.targetEffectModelViewerResPath = resPath
        render_tool.LoadResource(resPath, function(assetbundleName, renderGameObject)
            if self:IsDisposed() then
                -- 界面已经销毁,不再加载资源
                renderGameObject.Dispose()
                return
            end            
            if self.targetEffectModelViewerResPath ~= assetbundleName or self.liuguangEffect == nil or util.IsObjNull(self.liuguangEffect) then
                --界面不再需要加载完成的资源，或已销毁
                renderGameObject.Dispose()
                return
            end
            self.targetEffectModelViewerResPath = nil            
            self.renderTxInfo = render_tool.GetRenderTextureByTag(resPath, self.ownerModuleName)
            if self.renderTxInfo then
                self.effectModelViewerResPath = resPath
                self:DisplayImage(self.liuguangEffect, self.renderTxInfo, true)
                return
            end            
            renderGameObject.SetMaxParticleSize(100)
            renderGameObject.SetSize(Vector3.one * 0.01)
            local rawimage = self.liuguangEffect:GetComponent(typeof(RawImage))
            if rawimage then
                self.effectModelViewerResPath = resPath

                self.renderTxInfo = renderGameObject.RenderGameObject(resPath,132,170,render_tool.RenderMode.Single,0,-0.02,10, nil, self.ownerModuleName)
                if self.renderTxInfo then
                    self.renderTxInfo:SetImage(self.liuguangEffect, rawimage, true)
                end
            else
                log.Warning(self.liuguangEffect.."使用粒子系统转RT时需要绑定 RawImage 组件")
                renderGameObject.Dispose()
            end
        end)
    end
    self.initLiuGuangEffect = true
    return true
end

-- 不再独立于界面单独卸载特效，与界面一致保持缓存，逻辑上保持一致，使用 util.SetActiveParticleSystemRT 替代此接口
-- function HeroItem:DisposeItemEffect()
--     self:UnLoadHeroLiuGuangEffect()
-- end

local effectForSPlus = 
{
    [hero_mgr.Hero_Star.Purple] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi.prefab",
    [hero_mgr.Hero_Star.PurplePlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_zi_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_zi_02.prefab",
    [hero_mgr.Hero_Star.Yellow] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang.prefab",
    [hero_mgr.Hero_Star.YellowPlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_huang_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_huang_02.prefab",
    [hero_mgr.Hero_Star.Red] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong.prefab",
    [hero_mgr.Hero_Star.RedPlus] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_hong_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_hong_02.prefab",
    [hero_mgr.Hero_Star.White] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_bai/prefabs/effect_ui_juexinyanjiusuo_shangzhen_bai.prefab",
    [hero_mgr.Hero_Star.White_1] = "art/effects/effects/effect_ui_juexinyanjiusuo_shangzhen_bai_02/prefabs/effect_ui_juexinyanjiusuo_shangzhen_bai_02.prefab",
    [hero_mgr.Hero_Star.White_2] = "art/effects/effects/effect_ui_common_herokuang_glowsao03/prefabs/effect_ui_common_herokuang_glowsao03.prefab",
    [hero_mgr.Hero_Star.White_3] = "art/effects/effects/effect_ui_common_herokuang_glowsao02/prefabs/effect_ui_common_herokuang_glowsao02.prefab",
    [hero_mgr.Hero_Star.White_4] = "art/effects/effects/effect_ui_common_herokuang_glowsao01/prefabs/effect_ui_common_herokuang_glowsao01.prefab",
    [hero_mgr.Hero_Star.White_5] = "art/effects/effects/effect_ui_common_herokuang_saoguang/prefabs/effect_ui_common_herokuang_saoguang.prefab"
}

function HeroItem:LoadHeroLiuGuangEffect()
	--local resPath = "art/effects/effects/effect_ui_wuxingyingxiongliuguan/prefabs/effect_ui_wuxingyingxiongliuguan.prefab"
    --if self.isLongIcon then
    local resPath = ""
    --end
    local cfg = game_scheme:Hero_0(self.hero.heroID)
    local handBook_cfg = game_scheme:Hero_0(cfg.HerohandBookID)
    local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv or (cfg and cfg.starLv)

    -- 英雄稀有度类型（1B 2A 3S 4S+），S+ 英雄特效与其它英雄不一致
    if handBook_cfg and handBook_cfg.rarityType == 4 then
        -- resPath ="art/effects/prefabs/ui/ui_yingxiongliuguang_s.prefab"
        -- if starsLv == hero_mgr.Hero_Star.BluePlus or starsLv == hero_mgr.Hero_Star.YellowPlus or starsLv == hero_mgr.Hero_Star.RedPlus then
        --     resPath = "art/effects/prefabs/ui/ui_yingxiongliuguang_s1.prefab"
        -- elseif starsLv >= hero_mgr.Hero_Star.White and starsLv <= hero_mgr.Hero_Star.White_4 then
        --     resPath = "art/effects/prefabs/ui/ui_yingxiongliuguang_ss.prefab"
        -- elseif starsLv == hero_mgr.Hero_Star.White_5 then
        --     resPath = "art/effects/prefabs/ui/ui_common_herokuang_glow.prefab"
        -- end

        --只保留S+的特效，其他都不需要
        resPath = effectForSPlus[starsLv]

    -- else
    --     if handBook_cfg and handBook_cfg.rarityType ~= 3 then
    --         resPath ="art/effects/prefabs/ui/ui_wuxingyingxiongliuguan_big_world.prefab"
    --         if starsLv == hero_mgr.Hero_Star.BluePlus or starsLv == hero_mgr.Hero_Star.YellowPlus or starsLv == hero_mgr.Hero_Star.RedPlus then
    --             resPath = "art/effects/prefabs/ui/ui_wuxingyingxiongliuguan_big1_world.prefab"
    --         elseif starsLv >= hero_mgr.Hero_Star.White then
    --             resPath = "art/effects/prefabs/ui/ui_wuxingyingxiongliuguan_big2_world.prefab"
    --         end
    --     end
    end

    --使用覆盖流光
    if not string.empty(self.overrideEffectRes) then
        resPath = self.overrideEffectRes
    end
    
    if not resPath or resPath == "" then
        return
    end

    if const.CanUseParticlSystemRT(self.ownerModuleName) then        
        if self.initLiuGuangEffect then            
            --所有逻辑中必须先卸载，再加载新特效，否则判定为逻辑问题，不更新特效，防止效果重复加载
            log.Warning("HeroItem 重复加载特效，请检查逻辑，先卸载原有特效")
            return
        end        
        --使用render_tool的情况下，后续逻辑不执行
        if self:DisplayParticleWithRT(resPath) then            
            return
        end
    end    
	-- 根据屏幕分辨率适配
	-- local scaleFactor = (screen_util.width / screen_util.height) / (720/1280) * self.scale
	if self.effectModelViewer == nil then
		self.effectModelViewer = CModelViewer()
                    
		self.effectModelViewer:Init(self.liuguangEffect, function(obj)
            self.effectModelViewer:ShowGameObject(resPath, function(goEffect)
                self.goEffect = goEffect
                local scale = Vector3.one
                local childs = goEffect:GetComponentsInChildren(typeof(ParticleSystemRenderer))
                local child = nil
                --覆盖流光特效自定义控制缩放
                if resPath == self.overrideEffectRes then
                    scale = {x=self.overrideEffectScale,y=self.overrideEffectScale,z=self.overrideEffectScale}
                end
                for i = 0, childs.Length - 1 do
                    -- childs[i].gameObject.transform.localScale = {x = scaleFactor, y = scaleFactor, z = scaleFactor}
                    child = childs[i]
                    -- effectScales[child.name] =  child.gameObject.transform.localScale
                    -- util.SetTransformScale(child.gameObject.transform, self.scale)
                    --child.transform.localScale = scale
                    child.maskInteraction = self.maskType or 0
                end
                -- util.SetTransformScale(goEffect.transform, self.scale)
                --goEffect.transform.localScale = self.scale * Vector3.one
                -- HeroItem 被设置缩放后，在子节点内的此特效也已缩放，再乘算缩放导致特效效果小于英雄头像
                goEffect.transform.localScale = scale
                goEffect.transform.localPosition = {x=0,y=0,z=0}
            end)
		end)
	end
	--设置光效的渲染顺序
    self.effectModelViewer:SetRenderOrder(self.effectOrder or 100, self.useIncrementOrder)
end

function HeroItem:UnLoadHeroLiuGuangEffect()
    if self.effectModelViewer then
        self.effectModelViewer:ResetScale()--遗落之境开始战斗上阵时的英雄特效缩放问题修复
        self.effectModelViewer:Dispose()
        self.effectModelViewer = nil
    end

    -- 增加 self.targetEffectModelViewerResPath 记录当前最新的加载资源，防止资源加载过程中，先释放，再加载相同资源造成的多次调用 render_tool 绘制
    if self.effectModelViewerResPath then
        render_tool.ReleaseRenderGameObject(self.effectModelViewerResPath, self.ownerModuleName, self.renderTxInfo)
        self:DisplayImage(self.liuguangEffect, self.renderTxInfo, false)
        self.effectModelViewerResPath = nil
        self.renderTxInfo = nil
    end

    self.initLiuGuangEffect = false
end

function HeroItem:SetSelectEffectValid(bValid)
    local flag = "select"

    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        log.Warning("需要开启的选择效果不支持:"..flag)
        return
    end

    local resPath = self:GetSelectResPath()
    if fxInfo.resPath == resPath and fxInfo.valid == bValid then
        return
    end

    if fxInfo.resPath ~= resPath then
        fxInfo = self:SetEffectResPath(flag, resPath)
    end

    self:SetEffectValid(flag, bValid)
end

function HeroItem:SetEffectValid( flag, bValid )
    local fxInfo = self.displayFx[flag]
    if bValid then
        if fxInfo and fxInfo.valid ~= bValid then
            fxInfo.valid = true
            self:LoadHeroEffect(flag)
        else
            log.Warning("需要开启的效果类型不支持:"..flag)
            return
        end
    else
        if fxInfo and fxInfo.valid ~= bValid then
            fxInfo.valid = false
            self:UnLoadHeroEffect(flag)
        end
    end
end

function HeroItem:LoadHeroEffectSet()
    local k,v
    for k,v in pairs(self.displayFx) do
        if v.valid then
            self:LoadHeroEffect(k)
        end
    end
end

function HeroItem:UnLoadHeroEffectSet()
    local k,v
    for k,v in pairs(self.displayFx) do
        self:UnLoadHeroEffect(k)
    end
end

function HeroItem:LoadHeroEffect( flag )
    if not const.CanUseParticlSystemRT(self.ownerModuleName) then
        log.Warning("不支持加载真实粒子系统实现:"..tostring(flag))
        return
    end

    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        log.Error("特效数据未初始化:"..tostring(flag))
        return
    end

    if fxInfo.displaying then
        --所有逻辑中必须先卸载，再加载新特效，否则判定为逻辑问题，不更新特效，防止效果重复加载
        log.Error("HeroItem 重复加载卡牌可选择特效，请检查逻辑，先卸载原有特效")
        return
    end
    if fxInfo.resPath == nil or fxInfo.resPath == "" then
        log.Error("特效数据未设置需要显示的资源 AssetBundleName："..tostring(flag))
        return
    end

    self:DisplayEffect(fxInfo)
end

function HeroItem:UnLoadHeroEffect( flag )
    local fxInfo = self.displayFx[flag]
    if not fxInfo then
        return
    end

    -- 增加 targetEffectModelViewerResPath 记录当前最新的加载资源，防止资源加载过程中，先释放，再加载相同资源造成的多次调用 render_tool 绘制
    if fxInfo.displayResPath and fxInfo.renderTxInfo then
        render_tool.ReleaseRenderGameObject(fxInfo.displayResPath, self.ownerModuleName, fxInfo.renderTxInfo)

        local effectTrans = self[fxInfo.effectImageName]
        if effectTrans then
            self:DisplayImage(effectTrans, fxInfo.renderTxInfo, false)
        end
        fxInfo.displayResPath = nil
        fxInfo.renderTxInfo = nil
    end

    fxInfo.displaying = false
end

--英雄头像灰色
function HeroItem:GrayHeroIcon(flag)
    self.grayFlag.value = flag
    self:SetState()
end

--英雄半透
function HeroItem:HalfAlphaHeroIcon(flag)
    self.halfAlpha.value = flag
    self:SetState()
end

--显示阵营标识
function HeroItem:OnShowCareer(flag)
    self.CareerFlag.value = flag
    self:SetState()
end
--显示星级
function HeroItem:OnShowStars(flag)
    --self.StarsFlag.value = flag
    --self:SetState()
end
--显示类型
function HeroItem:OnShowType(flag)
    self.typeFlag.value = flag
    --self:SetState()
end
--显示血量
function HeroItem:OnShowHP(flag, value, value2)
    self.hpFlag.value = flag
    self.hpValue.value = value
    self.mpValue.value = value2
    self:SetState()
end

--用于列表选中高亮英雄
function HeroItem:HighLightHero(flag)
    self.highlightFlag.value = flag
    self:SetState()
end

--自动判断是否锁定，是的话锁定并飘字说明原因
function HeroItem:EnableAutoLock(_hook, _lock, _arena)
    if self.hero.heroSid then
        self.lockFunc = nil
         --锁定回复
        local _isLock = false
        local LockResonString = "Hero Locked"

        if CheckHook(self.hero.heroSid) and _hook then
            LockResonString = lang.Get(100635)
            _isLock = true
        elseif CheckLock(self.hero.heroSid) and _lock then
            LockResonString = lang.Get(100632)
            _isLock = true
        elseif CheckArena(self.hero.heroSid) and _arena then
            LockResonString = lang.Get(100631)
            _isLock = true
        end
        if _isLock then
            self.lockFunc = function()
                local flow_text = require "flow_text"
                flow_text.Add(LockResonString)
            end
        end
        self:LockHero(_isLock, true)
    end
end

--根据英雄品质设置遮罩
function HeroItem:SetMask()
    if self.hero == nil then
        return
    end
    local cfg = game_scheme:Hero_0(self.hero.heroID)
    local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv and self.hero.numProp.starLv or (cfg and cfg.starLv)
    local starsType = (starsLv and starsLv - hero_mgr.Hero_Star.Green > 0 and starsLv - hero_mgr.Hero_Star.Green) or 1 -- 规避 1 2星级英雄 遮罩异常问题
    local maskType = QUALITY_MASK_ENUM[starsType]
    local maskNewImage = self.maskNew.gameObject:GetComponent(typeof(Image))

    Common_Util.SetActive(self.teamObj,self.showTeamIcon.value)
    self.teamText.text = self.teamValue.value
    
    local show = self.lockFlag.value or self.maskFlag.value or self.trialMask or false

    if self.itemType then
        --大的头像框
        self.maskBig:SetActive(show)
        self.maskNew.gameObject:SetActive(false)
    else
        --小的头像框
        if maskType == 0 then
            --旧版遮罩
            self.maskBig:SetActive(show)
            self.maskNew.gameObject:SetActive(false)
        elseif maskType == 1 then
            --史诗+遮罩
            self.maskNew:Switch(0)
            self.maskNew:SetActive(show)
            maskNewImage:SetNativeSize()
            self.maskBig:SetActive(false)
        elseif maskType == 2 then
            --神话遮罩
            self.maskNew:Switch(1)
            self.maskNew:SetActive(show)
            maskNewImage:SetNativeSize()
            self.maskBig:SetActive(false)
        end
    end

    if not show then
        self.maskNew:SetActive(false)
        self.maskBig:SetActive(false)
    end
end

function HeroItem:SetState()
    if self.UIRoot ~= nil then
        self:SetMask()
        if self.highlightFlag.dirty then
            self.highlightFlag.dirty = false
            self.highlight:SetActive(self.highlightFlag.value)
        end
        if self.CareerFlag.dirty then
            self.CareerFlag.dirty = false
            self.profTypeIcon:SetActive(self.CareerFlag.value)
        end
        if self.selectedFlag.dirty then
            self.selectedFlag.dirty = false
            self.selectedImage:SetActive(self.selectedFlag.value)
        end
        if self.hpFlag.dirty then
            self.hpFlag.dirty = false
            self.rect_hpSlider:SetActive(self.hpFlag.value or false )
        end
        --限制值范围在0-1之间
        if self.hpValue.dirty then
            self.hpValue.dirty = false
            local hpValue = self.hpValue.value
            hpValue = (hpValue and hpValue>=1) and 1 or hpValue
            self.hp.sizeDelta = {x=(hpValue or 0)*112,y=self.hp.sizeDelta.y}
        end
        if self.mpValue.dirty then
            self.mpValue.dirty = false
            local mpValue = self.mpValue.value
            mpValue = (mpValue and mpValue>=1) and 1 or mpValue
            self.mp.sizeDelta = {x=(mpValue or 0)*112,y=self.mp.sizeDelta.y}
        end
        if self.isHide then
            self.gameObject:SetActive(false)
        end

        if self.halfAlpha.dirty then
            self.halfAlpha.dirty = false
            self.alphaCanva.alpha = self.halfAlpha.value and 0.6 or 1
        end

        if self.interactableFlag.dirty then
            self.interactableFlag.dirty = false
            self.button.interactable = self.interactableFlag.value
        end
        if self.grayFlag.dirty then
            self.grayFlag.dirty = false
            local flag = self.grayFlag.value
            self.typeIconGray:SetEnable(flag)
            self.typeFrameGray:SetEnable(flag)
            self.profTypeIconGray:SetEnable(flag)
            self.longFrameGray:SetEnable(flag)
            self.longFrameBgGray:SetEnable(flag)
            self.longImageGray:SetEnable(flag)
        end
        if self.enableNewFlag.dirty then
            self.enableNewFlag.dirty = false
            self.newCHImg:SetActive(self.enableNewFlag.value and lang.USE_LANG==lang.ZH)
            self.newENImg:SetActive(self.enableNewFlag.value and lang.USE_LANG~=lang.ZH)
        end
        self.newCHImg.sortingOrder = self.newOrder
        self.newENImg.sortingOrder = self.newOrder
        if self.maxLvFlag.dirty then
            self.maxLvFlag.dirty = false
            self.maxLevelTips.gameObject:SetActive(self.maxLvFlag.value)
        end
        if self.lockFlag.dirty then
            self.lockImage:SetActive(self.lockFlag.value)
        end
        if self.lockFlag.dirty or self.maxLvFlag2.dirty then
            self.lockFlag.dirty = false
            self.maxLvFlag2.dirty = false
            self.maxLevelTips2.gameObject:SetActive(self.lockFlag.value and self.maxLvFlag2.value)
        end
        if self.maxLvText then
            self.maxLevelTips.text = self.maxLvText
            self.maxLevelTips2.text = self.maxLvText
        else
            self.maxLevelTips.text = lang.Get(21168)
            self.maxLevelTips2.text = lang.Get(21168)
        end

        if self.bossFlag.dirty then
            self.bossFlag.dirty = false
            self.bossIcon:SetActive(self.bossFlag.value)
        end
        
        self.showLvFlag.value = self.hero and self.hero.numProp ~= nil and self.hero.numProp.lv ~= 0 --and self.hero.numProp.lv

        if self.showLvFlag.dirty then
            self.showLvFlag.dirty = false
            self:ShowHeroLv(self.showLvFlag.value)
        end
        self:SetHeroStar()
        self:SetHeroJob()
        self:SetTip()
        self:ShowDecorate(self.decorateData)
        self:CheckTrialCondition()

        self.forceWeaponIcon.gameObject:SetActive(self.forceWeaponOn)
        if self.forceWeaponOn then
            self.forceWeaponIcon:Switch(self.forceWeaponIndex)
            self.forceWeaponIconImg:SetNativeSize()
        end
    end
end

function HeroItem:SetHeroJob()
    if self.hero then
        local cfg = game_scheme:Hero_0(self.hero.heroID)
        if cfg then
            self.jobIcon:Switch(cfg.profession - 1)
        end
    end
end

function HeroItem:SetHeroStar()
    if self.hero and self.hero.numProp and self.hero.numProp.starLv then
        local ui_hero_star_item = require "ui_hero_star_item"
        self.heroStarObj = self.heroStarObj or ui_hero_star_item.new()
        self.heroStarObj:Init(self.heroStars, self.hero.numProp.starLv)
    end
end


function  HeroItem:SetTip()
    if not util.IsObjNull( self.headPortraitTfm) then
        self.headPortraitTfm.gameObject:SetActive(self.isSetTip)
        self.headPortraitTxt.text = lang.Get(1804)
    end
    
end

function HeroItem:RegistEvents()
    self.onClickHero = function(...)
        -- body
        if self.lockFunc then
            self.lockFunc()
        elseif self.trialLimitFunc then
            self.trialLimitFunc()
        else
            if self.callback then
                self.callback(self)
            end

            --取消新英雄标记
            if self.enableNewFlag then
                self.newSign.text=""
                self.newCHImg:SetActive(false)
                self.newENImg:SetActive(false)
                hero_mgr.CancelMarkNewHero(self.hero.heroSid)
            end
        end
    end
    self.button.onClick:AddListener(self.onClickHero)

    --屏幕尺寸变化
    self.OnScreenSizeChanged = function(eventName, newSize, oldSize)
        if self.effectModelViewer ~= nil and self.goEffect ~= nil and self.overrideEffectRes == nil then
            screen_util.SetParticleAllScale(self.goEffect, self.scale)
        end
    end
    event.Register(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
end

--设置数据
function HeroItem:SetHero(hero, callback, isLongIcon)
    --[[
        heor参数使用方法：
        1、传入heroEntity实体
        2、支持以下结构表：
        {
            heroID = ?,
            skinID = ?,
            numProp = {
                starLv = ?,
                lv = ?,
                Career = ?
                Type = ?
            }
        }
    ]]

    self.callback = callback

    self.isLongIcon=isLongIcon

    if hero ~= nil then
        self:InitHeroInfo(hero)
    end

    if self.gameObject ~= nil then
        self:DisplayInfo(self.itemType)
    -- else
    --     log.Error("英雄头像模块未初始化")
    end
end

function HeroItem:SetActive(isVisible)
    if self.gameObject then
        self.gameObject:SetActive(isVisible)
    else
        self.isHide = not isVisible
    end
end
--刷新显示
function HeroItem:DisplayInfo(itemType)
    if self.hero == nil or util.IsObjNull(self.gameObject) then
        return
    end
    
    --如果state不是null，则正常加载英雄，否怎特殊处理
    local cfg = game_scheme:Hero_0(self.hero.heroID)
    local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv and self.hero.numProp.starLv or 1
    if cfg then
        starsLv = starsLv ~= 1 and starsLv or cfg.starLv
    end
    self.superStarImage:SetActive(false)

    --临时英雄头像
    local heroType = 0
    local heroCareer = 0
    if cfg == nil then
        --往后用于不同星级头像设置
        -- self.image:SetActive(false)
        local nullIcon = nullIconArr[starsLv]
        if itemType then
            --加载出战的大英雄图标
            -- self.image:SetActive(false)
            --self.frame:SetActive(false)
        else
            typeIconAsset:GetSprite(
                nullIcon,
                function(sprite)
                    -- self.image.sprite = sprite
                    if self:IsDisposed() then return end
                    self.longImage.sprite = sprite
                end
            )
        end
        self.longFrame:Switch(hero_mgr.Star2Frame[starsLv])
        self.longFrameBg:Switch(hero_mgr.Star2Frame[starsLv])

        heroType = self.hero.numProp ~= nil and self.hero.numProp.type ~= nil and self.hero.numProp.type or 0
        heroCareer = self.hero.numProp and self.hero.numProp.heroCareer and self.hero.numProp.heroCareer or 0
    else
		-- 刷新五星精英特效的显示
        self:UnLoadHeroLiuGuangEffect()
        self:UnLoadHeroEffectSet()        
		if cfg.isElite ~= 1 then
			self.showEffect = false			
        end	        
        if self.showEffect then
			self:LoadHeroLiuGuangEffect()
            self:LoadHeroEffectSet()
		end
        heroType = cfg.type ~= nil and cfg.type or 0
        heroCareer = cfg.profession
        --根据星级显示对应头像
        -- local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv and self.hero.numProp.starLv or cfg.starLv
        local currentFace = hero_mgr.ChangeHeroIcon(self.hero.heroID, starsLv)
        if not self.hero.rawIcon then
            local skinID = self.hero.skinID
            if self.hero.battleProp and self.hero.battleProp.power and ((not skinID) or (skinID == 0)) then
                --如果有battleProp字段证明传入的是自己的英雄，外部没传入则自动检测是否穿戴皮肤
                local notMaze = true
                if self.hero.heroSid then
                    local player_mgr = require "player_mgr"
                    if player_mgr.GetMazePalPartDataBySid(self.hero.heroSid) or player_mgr.GetPeakPalPartDataBySid(self.hero.heroSid) then
                        notMaze = false
                    end
                end
                if notMaze then
                    local skin_mgr = require "skin_mgr"
                    skinID = skin_mgr.GetCurSkinID(self.hero.heroID)
                end
            end
            if skinID and skinID ~= 0 then
                local faceID = hero_mgr.ChangeHeroSkinIcon(skinID, starsLv)
                currentFace = faceID or currentFace
            end
        end
        -- if itemType then
            ----加载出战的大英雄图标
            -- self.image:SetActive(false)
            --self.frame:SetActive(false)
        -- else
            local tFun=nil
            ------  --print("heroType>>>",starsLv)
           --if self.isLongIcon then
                -- self.image:SetActive(false)
                -- self.frame:SetActive(false)
                self.longImage:SetActive(true)
                --self.longFrame:SetActive(heroType >=1)
                self.longFrame:Switch(cfg.rarityType-2)--hero_mgr.Star2Frame[starsLv])
                --self.longFrameBg:SetActive(heroType >=1)
                self.longFrameBg:Switch(cfg.rarityType-2)--hero_mgr.Star2Frame[starsLv])
				-- self.longStarBg:SetActive(heroType >=1)
                -- self.longStarBg:Switch(heroType-1)
                tFun=function(sprite)--长条形头像
                    if self:IsDisposed() then return end
                    self.longImage.sprite = sprite
                end
                self.rect_hpSlider.anchoredPosition = longHPSliderPos
			--[[
            else
                tFun=function(sprite) 
                    self.image.sprite = sprite
                end
                self.rect_hpSlider.anchoredPosition = shortHPSliderPos
            end
			]]--
            spriteAsset:GetSprite(currentFace,tFun)
        -- end
    end

    if self.typeFlag.dirty then
        self.typeFlag.dirty = false
        self.typeIcon:SetActive(heroType ~= 0 and heroType ~= nil and self.typeFlag.value)
    end
    self.typeIcon:Switch((heroType - 1) or 0)

    --阵营框 根据专属等级变化
    self:SetTypeFrame()

    if self.forceWeaponOn then
        local pos = self.forceWeaponRect.anchoredPosition
        local y = forceEquipPos[loadIndex] 
        if y then
            pos.y = y
        end
        self.forceWeaponRect.anchoredPosition = pos
    end

    -- assetName = "type_big_" .. (heroType or 0)
    -- typeIconAsset:GetSprite(
    --     assetName,
    --     function(sprite)
    --         self.typeIcon.sprite = sprite
    --     end
    -- )

    self.profTypeIcon:SetActive(heroCareer ~= 0 and heroCareer ~= nil and self.CareerFlag.value)
    typeIconAsset:GetSprite(
        "prof_" .. heroCareer,
        function(sprite)
            if self:IsDisposed() then return end
            self.profTypeIcon.sprite = sprite
        end
    )

    --等级（右上角）
            
    self.showLvFlag.value = self.hero and self.hero.numProp ~= nil and self.hero.numProp.lv ~= 0 --and self.hero.numProp.lv

    if self.showLvFlag.dirty then
        self.showLvFlag.dirty = false
        self:ShowHeroLv(self.showLvFlag.value)
    end
    self:ShowDecorate(self.decorateData)
    self:CheckTrialCondition()
    self:SetHeroStar()
    --进阶等级（左上角）
    -- self.advLevelText.text = self.hero.numProp.advLevel ~= 0 and self.hero.numProp.advLevel or ""

    -- local hpRate = self.hero.numProp and self.hero.numProp.hpRate
    -- self.hpSlider:SetActive(hpRate)
    -- self.hpSlider.value = (hpRate or 0)

    --新英雄标记
    self.newSign.text = hero_mgr.IsNewHero(self.hero.heroSid) and lang.Get(9338) or ""
    if hero_mgr.IsNewHero(self.hero.heroSid) then
        self.newCHImg:SetActive(self.enableNewFlag and lang.USE_LANG==lang.ZH)
        self.newENImg:SetActive(self.enableNewFlag and lang.USE_LANG~=lang.ZH)
        self.newCHImg.sortingOrder = self.newOrder
        self.newENImg.sortingOrder = self.newOrder
    else
        self.newCHImg:SetActive(false)
        self.newENImg:SetActive(false)
    end
    --英雄评分
    self.scoreFlag = self.scoreFlag or false
    if self.scoreTfm.gameObject.activeSelf ~= self.scoreFlag then
        self.scoreTfm.gameObject:SetActive(self.scoreFlag)
    end
    if self.scoreFlag and self.scoreIndex then
        self.scoreSp:Switch(self.scoreIndex)
    end
    --印记
    self.sigilFlag = self.sigilFlag or false
    if self.sigilRoot.gameObject.activeSelf ~=self.sigilFlag  then
        self.sigilRoot.gameObject:SetActive(self.sigilFlag)
    end
    if self.sigilFlag and self.sigilIconId then
        typeIconAsset:GetSprite(self.sigilIconId,function(sprite)
            if self:IsDisposed() then return end
            self.sigilIcon.sprite = sprite
        end)
    end
    --神器
    self.godEquipFlag = self.godEquipFlag or false
    if self.godEquipRoot.gameObject.activeSelf ~= self.godEquipFlag then
        self.godEquipRoot.gameObject:SetActive(self.godEquipFlag)
    end
    if self.godEquipFlag and self.godEquipEntity then
        local goods_item = require "goods_item_new"
        self.godEqipItem = self.godEqipItem or goods_item.CGoodsItem()
        self.godEqipItem:Init(self.godEquipRoot,function()
        end,0.25)
        self.godEqipItem:SetGoods(self.godEquipEntity)
    end
end

--阵营框 根据专属等级变化
function HeroItem:SetTypeFrame()
    local cfg = game_scheme:Hero_0(self.hero.heroID)
    if not cfg then
        log.Error("Hero表获取不到数据，ID=",self.hero.heroID)
        return
    end
    local assetName=nil
    local equipment_mgr = require "equipment_mgr"
    if self.hero.numProp and cfg then
        --专属装备等级：如果实体里有exclusiveEquipLv，则取实体的该变量，如果没有则从背包里找出该装备
        local equipLv = self.hero.exclusiveEquipLv
        if equipLv==nil then
            local exclusiveEquipEntity = equipment_mgr.GetExclusiveEquip(self.hero)
            equipLv = exclusiveEquipEntity and exclusiveEquipEntity.numProp.equipStrLv or 0
        end
        local equipLvMap = equipment_mgr.GetEquipLvMap(cfg.equipID or 0)
        for i=1,#equipLvMap do
            if equipLv >= equipLvMap[i].lv then
                assetName = "zhenyingLv_"..equipLvMap[i].heroMark
            else
                break
            end
        end
    end
    if not assetName then
        self.typeFrame.gameObject:SetActive(false)
    else
        self.typeFrame.gameObject:SetActive(true)
        typeIconAsset:GetSprite(
            assetName,
            function(sprite)
                if self:IsDisposed() then return end
                self.typeFrame.sprite = sprite
            end
        )
    end        
end
--[[检查其他玩家的灵魂链接]]
function HeroItem:CheckOtherPlayerSoulLink(isSoulLinking)
    self.isIgnoreSoulLlinkCheck = true
    if self.UIRoot ~= nil then
        if isSoulLinking and isSoulLinking~=0 then
            self.levelText.color = ui_soul_link_mgr.GetHeroSoulLinkTxtColor()
        else
            self.levelText.color = {r = 1, g = 1, b = 1, a = 1}
        end
    end
end

--[[检查试用情况]]
function HeroItem:CheckTrialCondition()
    -- print("qsy_yxsy:[hero_item]CheckTrialCondition0000>>>>",self.hero,const.ONOFF_HEROTRIAL)
    if self.UIRoot ~= nil then
        if self.hero and const.ONOFF_HEROTRIAL then
            self.trialLimitFunc = nil
            self.trialMask = false
            local hero_trial_mgr = require "hero_trial_mgr"
            local isTrial = hero_trial_mgr.isTrialHeroBySid(self.hero.heroSid) or self.trialFlag--非战报判断 or 战报判断
            self.trialSignRoot.gameObject:SetActive(isTrial)

            --试用次数限制显示
            self.trialTimesLimit.gameObject:SetActive(false)
            -- print("qsy_yxsy:[hero_item]CheckTrialCondition>>>>",self.battleType)
            if isTrial and self.battleType then
                local canTimes = nil
                local limitTimes = hero_trial_mgr.GetTrialHeroBattleTimes_Limit(self.battleType,self.hero.heroSid)
                --TODO 次数限制刷新
                -- print("qsy_yxsy:[hero_item]CheckTrialCondition>>>>",limitTimes,canTimes,self.battleType)
                if limitTimes and limitTimes ~= -1 then--有次数限制
                    canTimes = hero_trial_mgr.GetTrialHeroBattleTimes_Can(self.battleType,self.hero.heroSid)
                    if canTimes then
                        self.trialTimesLimit.text = "("..canTimes.."/"..limitTimes..")"
                        if canTimes == 0 then
                            self.trialTimesLimit.color = {r=255/255, g= 48/255, b= 42/255, a= 1}
                            self.trialMask = true
                            self.trialLimitFunc = function ()
                                local flow_text = require "flow_text"
                                flow_text.Add(lang.Get(393011))
                            end
                        else
                            self.trialTimesLimit.color = {r=58/255, g= 106/255, b= 169/255, a= 1}
                        end
                        if self.hpFlag.value then--有血条的情况,改变位置
                            self.trialTimesLimit.transform.localPosition = {x = 0,y = 88,z = 0}
                        else
                            self.trialTimesLimit.transform.localPosition = {x = 0,y = -83,z = 0}
                        end
                        self.trialTimesLimit.gameObject:SetActive(true)
                    else
                        log.ErrorReport(log.ENUM_LOGTYPE.ERRINFO,"canTimes is nil.请检查服务器数据处理")
                    end
                end
            end
        end
    end
end

--展示英雄饰品吊坠
function HeroItem:ShowDecorate(decorateData)
    if self.UIRoot ~= nil then
        self.decorateData = decorateData
        local decoration_mgr = require "decoration_mgr"
        local count = self.hero and decoration_mgr.GetDecorationSkillActiveCount(self.hero.heroSid,self.hero.heroID,self.decorateData) or 0
        for i=1,3 do
            self["decorateImg"..i].gameObject:SetActive(i==count)
        end
    end
end

--检查英雄是否在竞技场防守阵容中
function CheckArena(sid)
    local net_arena_module = require "net_arena_module"
    for k, v in pairs(net_arena_module.MineDefence) do
        for __i, __v in ipairs(v) do
            for _i, _v in ipairs(__v.palList) do
                if _v.palId == sid then
                    return true
                end
            end
        end
    end
    return false
end

--检查英雄是否在挂机阵容中
function CheckHook(sid)
    local hook_hero_data = require "hook_hero_data"
    local data = hook_hero_data.GetSaveHeroData()
    for k, v in pairs(data) do
        if sid == v.heroSid then
            return true
        end
    end
    return false
end

--检查英雄是否锁定
function CheckLock(Sid)
    return false
end

function HeroItem:ClearDisplayFx()
    local k,v
    for k,v in pairs(self.displayFx) do
        self:UnLoadHeroEffect(k)

        v.valid = false
        v.displaying = false
        v.loadingResSet = {}
        v.displayResPath = nil
        v.resPath = nil
    end
end

function HeroItem:Dispose()
    -- if spriteAsset then
    --     spriteAsset:Dispose()
    --     spriteAsset = nil
    -- end
    self:Reset()
    self.isIgnoreSoulLlinkCheck = nil

    self:UnLoadHeroLiuGuangEffect()

    self:ClearDisplayFx()
    -- Dispose 时由 ClearDisplayFx 完成更完整的数据清理
    -- self:UnLoadHeroEffectSet()

    if self.onClickHero and self.button and not util.IsObjNull(self.button) then
        self.button.onClick:RemoveListener(self.onClickHero)
        self.onClickHero = nil
    end

    self.lockFunc = nil
     --锁定回复
    self.updateFunctionTable = nil

    event.Unregister(event.SCREEN_SIZE_CHANGED, self.OnScreenSizeChanged)
    
    self.__base:Dispose()
end

-----------------------重构------------------------------
-- 重构期望：调用者主动确认自己所需的模块，所有方法都只做其方法所描述的事，即调用者需要主动更新hero_item
--从对象池拿出来的预制体可能有组件未关闭，且新模块中未设置对应的组件，导致某些组件错误的显示
--白名单模式，永远认为HeroItem预制体内默认显示的组件会被设置到正确状态，其他组件设置为false
function HeroItem:Reset()
    if self.UIRoot ~= nil then
        self.profTypeIcon:SetActive(false)
        self.decorateImg1:SetActive(false)
        self.decorateImg2:SetActive(false)
        self.decorateImg3:SetActive(false)
        self.maxLevelTips2:SetActive(false)
        self.maskNew:SetActive(false)
        self.maskBig:SetActive(false)
        self.lockImage:SetActive(false)
        self.selectedImage:SetActive(false)
        self.highlight:SetActive(false)
        self.newSign:SetActive(false)
        self.bossIcon:SetActive(false)
        self.newCHImg:SetActive(false)
        self.newENImg:SetActive(false)
        self.maxLevelTips:SetActive(false)
        self.typeIconGray:SetEnable(false)
        self.typeFrameGray:SetEnable(false)
        self.profTypeIconGray:SetEnable(false)
        self.longFrameGray:SetEnable(false)
        self.longFrameBgGray:SetEnable(false)
        self.longImageGray:SetEnable(false)
        self.levelText.color = {r = 1,g = 1,b = 1,a = 1}
        self.alphaCanva.alpha = 1
        if self.godEqipItem then
            self.godEqipItem:Dispose()
            self.godEqipItem = nil
        end
        self.godEquipFlag = false
        self.sigilFlag = false
        self.scoreFlag = false
        self.godEquipEntity = nil
        self.sigilIconId = nil
        self.scoreIndex = nil
        if self.sigilRoot and (not util.IsObjNull(self.sigilRoot)) and self.sigilRoot.gameObject and self.sigilRoot.gameObject.activeSelf then
            self.sigilRoot.gameObject:SetActive(false)
        end
        if self.scoreTfm and (not util.IsObjNull(self.sigilRoot)) and self.scoreTfm.gameObject and self.scoreTfm.gameObject.activeSelf  then
            self.scoreTfm.gameObject:SetActive(false)
        end
        self.forceWeaponIcon.gameObject:SetActive(false)
        self.forceWeaponOn = false
    end
end
function HeroItem:SetHero_(hero)
    self.hero = hero
end
function HeroItem:ShowHeroIcon()
    local cfg = game_scheme:Hero_0(self.hero.heroID)
    local starsLv = self.hero.numProp ~= nil and self.hero.numProp.starLv and self.hero.numProp.starLv or 1
    if cfg then
        starsLv = starsLv ~= 1 and starsLv or cfg.starLv
    end
    heroType = cfg.type ~= nil and cfg.type or 0
    self.longImage:SetActive(true)
    --self.longFrame:SetActive(heroType >=1)
    self.longFrame:Switch(cfg.rarityType-2)--hero_mgr.Star2Frame[starsLv])
    --self.longFrameBg:SetActive(heroType >=1)
    self.longFrameBg:Switch(cfg.rarityType-2)--hero_mgr.Star2Frame[starsLv])
    self.typeIcon:SetActive(heroType ~= 0 and heroType ~= nil)
    self.typeIcon:Switch((heroType - 1) or 0)
    
    self.jobIcon:Switch(cfg.profession - 1)
    
    local currentFace = hero_mgr.ChangeHeroIcon(self.hero.heroID, starsLv)
    if not self.hero.rawIcon then
        local skinID = self.hero.skinID
        if self.hero.battleProp and self.hero.battleProp.power and ((not skinID) or (skinID == 0)) then
            --如果有battleProp字段证明传入的是自己的英雄，外部没传入则自动检测是否穿戴皮肤
            local notMaze = true
            if self.hero.heroSid then
                local player_mgr = require "player_mgr"
                if player_mgr.GetMazePalPartDataBySid(self.hero.heroSid) or player_mgr.GetPeakPalPartDataBySid(self.hero.heroSid) then
                    notMaze = false
                end
            end
            if notMaze then
                local skin_mgr = require "skin_mgr"
                skinID = skin_mgr.GetCurSkinID(self.hero.heroID)
            end
        end
        if skinID and skinID ~= 0 then
            local faceID = hero_mgr.ChangeHeroSkinIcon(skinID, starsLv)
            currentFace = faceID or currentFace
        end
    end
    spriteAsset:GetSprite(currentFace, function(sprite)
        if self:IsDisposed() then
            return
        end
        self.longImage.sprite = sprite
    end)
end
function HeroItem:ShowIconFrame(flag)
    self.longFrame:SetActive(flag)
end

function HeroItem:SetIconGray(flag)
    if(self.longImage ~= nil)then
        self.longImage:SetGray(flag)
    end
    self.longFrameGray:SetEnable(flag)
end

function HeroItem:SetClickCallback(callback)
    self.callback = callback
end
function HeroItem:EnableEffect(order,maskType)
    self.maskType = maskType or 0
    self.effectOrder = order
    self:LoadHeroLiuGuangEffect()
end
function HeroItem:ShowMaxLv(flag,text)
    self.maxLevelTips.gameObject:SetActive(flag)
    self.maxLevelTips2.gameObject:SetActive(flag)
    if flag and self.maxLvText then
        self.maxLevelTips.text = text
        self.maxLevelTips2.text = text
    else
        self.maxLevelTips.text = lang.Get(21168)
        self.maxLevelTips2.text = lang.Get(21168)
    end
end
function HeroItem:SetSelect(select)
    self.selectedImage:SetActive(select)
end
function HeroItem:SetMask_(maskFlag)
    self.maskFlag.value = maskFlag
    self:SetMask()
end
function HeroItem:SetInteractable(flag)
    self.button.interactable = flag
end
function HeroItem:ShowLockIcon(flag)
    if self.lockFlag ~= flag then
        self.lockFlag.value = flag
        self.lockImage:SetActive(flag)
        self:SetMask()
    end
end
function HeroItem:ShowHeroLv(flag)
    --self.levelBg.gameObject:SetActive(flag)
    if not util.IsObjNull(self.levelText) then
        Common_Util.SetActive(self.levelText,flag) --self.levelText.gameObject:SetActive(flag)
        if flag then
            local lv = 0
            if self.hero.numProp and self.hero.numProp.lv then
                lv = self.hero.numProp.lv
            end
            self.levelText.text = string.format("Lv.%d   ",lv) or ""
            if not isShowLv or self.hero.numProp.starLv == hero_mgr.Hero_Star.BluePlus or self.hero.numProp.starLv == hero_mgr.Hero_Star.YellowPlus or self.hero.numProp.starLv == hero_mgr.Hero_Star.RedPlus then
                self.levelText.text = self.levelText.text.."   "
            end
            self.isSoulLinkingFlag.value = ui_soul_link_mgr.IsHeroSoulLinking(self.hero.heroSid)
            if self.isSoulLinkingFlag.dirty then
                self.isSoulLinkingFlag.dirty = false
                if self.isSoulLinkingFlag.value then
                    self.levelText.color = ui_soul_link_mgr.GetHeroSoulLinkTxtColor()
                else
                    self.levelText.color = {r = 1,g = 1,b = 1,a = 1}
                end
            end
        end
    end

end
function HeroItem:EnableTransparent(flag)
    if self.transparent ~= flag then
        self.transparent = flag
        self.alphaCanva.alpha = self.transparent and 0.6 or 1
    end
end
function HeroItem:ShowHP(flag)
    self.rect_hpSlider:SetActive(flag)
end
function HeroItem:IsLoaded()
    if self then
    return self.loaded
    end
    return false
end
function HeroItem:SetSigilState(state,iconID)
    self.sigilFlag = state
    self.sigilIconId = iconID
end
function HeroItem:SetGodEquipState(state,entity)
    self.godEquipFlag = state
    self.godEquipEntity = entity
end
function HeroItem:SetScoreState(state,index)
    self.scoreFlag = state
    self.scoreIndex = index or 0
end
local class = require "class"
local base_game_object = require "base_game_object"
CHeroItem = class(base_game_object, nil, HeroItem)
