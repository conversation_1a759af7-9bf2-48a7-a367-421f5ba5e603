local require 		= require
local string 		= string
local typeof 		= typeof

local ui_base 		= require "ui_base"
local class 		= require "class"
local lang 			= require "lang"
local windowMgr 	= require "ui_window_mgr"
local game_scheme   = require "game_scheme"
local ui_tips_mgr   = require "ui_tips_mgr"

local Button 		= CS.UnityEngine.UI.Button
local Text 			= CS.UnityEngine.UI.Text
local RectTransform = CS.UnityEngine.RectTransform
local TextAnchor	= CS.UnityEngine.TextAnchor

module("ui_hero_type_tip")
local window = nil
local HeroID = 0	  -- 英雄id
local ShowType = 0
local UIHeroType ={}
local OccupationPos
local GroupPos
local Location1
local Location2
local txt = ""

local parentTrans

UIHeroType.widget_table = 
{
	tipMaskBtn = {path ="mask" , type = "Button"},
	bg = {path ="bg" , type = RectTransform},
	tipTxt = {path ="bg/Text" , type = "Text"},
	rect_Tips = {path = "" , type = RectTransform},
}

function UIHeroType:Init() 
	ui_tips_mgr.ShowUITip("ui_hero_type_tip")
	self:SubscribeEvent()
	--self:SetShowTips()
end

--[[资源加载完成，被显示的时候调用]]
function UIHeroType:OnShow()
	self.__base:OnShow()
	self:SetShowTips()
end

function SetHeroType(showType,heroid)
	ShowType = showType
	HeroID = heroid
	txt = ""
end



function UIHeroType:SetTextAlignLeft()
	self.alignmentLeft = true
end

function UIHeroType:SetTipsAnchorsY(y)
	self.anchorsY = y
end

function UIHeroType:SetTipsPos(pos)
	self.TipsPos = pos
end

function UIHeroType:SetShowTips()
	if not HeroID or not ShowType then return end
	if not GroupPos then 
		GroupPos = {x=80,y=self.bg.localPosition.y}
	end
	if self.alignmentLeft then
		self.tipTxt.alignment = TextAnchor.LowerLeft
	end
	local cfg_hero = game_scheme:Hero_0(HeroID)
	if self.size then
		self.bg.sizeDelta = self.size
		self.tipTxt:GetComponent(typeof(RectTransform)).sizeDelta = {x=self.size.x-20,y=self.size.y}
	end
	if self.anchorsY then
		self.rect_Tips.anchorMin = {x = self.rect_Tips.anchorMin.x,y = self.anchorsY} 
		self.rect_Tips.anchorMax = {x = self.rect_Tips.anchorMax.x,y = self.anchorsY} 
	end
	if self.TipsPos then
		self.rect_Tips.anchoredPosition = self.TipsPos
	end
	if self.bgPivot then
		self.bg.pivot = self.bgPivot
	end
	if cfg_hero then
		--local templet = "<color=%s>%s</color>"
		--local color = profColorConfig[cfg_hero.profession]
		if ShowType == 1 then
			--设置职业名
			if not OccupationPos then 
				OccupationPos = {x=-100,y=self.bg.localPosition.y}
			end
			self.bg.localPosition = OccupationPos
			self.tipTxt.text = txt..lang.Get(4100 + cfg_hero.profession)--string.format(templet ,color , lang.Get(4100 + cfg_hero.profession))
		elseif ShowType == 2 then
			--设置阵营
			self.bg.localPosition = GroupPos
			self.tipTxt.text = txt..lang.Get(5211 + cfg_hero.type)--string.format(templet ,color , lang.Get(4105 + cfg_hero.type))
		elseif ShowType == 3 then
			--定位1
			self.bg.localPosition = Location1
			self.tipTxt.text = txt..lang.Get(cfg_hero.MainDuty)--string.format(templet ,color , lang.Get(4105 + cfg_hero.type))
		elseif ShowType == 4 then
			--定位2
			self.bg.localPosition = Location2
			self.tipTxt.text = txt..lang.Get(cfg_hero.SubDuty)--string.format(templet ,color , lang.Get(4105 + cfg_hero.type))
		end
	end
end

--注册事件
function UIHeroType:SubscribeEvent()
	self.returnBtnEvent = function()
		windowMgr:UnloadModule("ui_hero_type_tip")
    end
	self.tipMaskBtn.onClick:AddListener(self.returnBtnEvent)
end

 
--销毁事件
function UIHeroType:UnsubscribeEvent()
    self.tipMaskBtn.onClick:RemoveListener(self.returnBtnEvent)
end

function UIHeroType:SetSize(size)
	self.size = size
end

--[[文字向上扩展(默认居中)]]
function UIHeroType:SetPivot2Top()
	self.bgPivot = {x = 0.5,y = 0}
end

--[[文字向下扩展(默认居中)]]
function UIHeroType:SetPivot2Bottom()
	self.bgPivot = {x = 0.5,y = 1}
end

function UIHeroType:SetText(text)
	txt = text
end

function UIHeroType:Close()
	if self:IsValid() then
		self:UnsubscribeEvent()
		parentTrans = nil
	end
	ui_tips_mgr.CloseUITip()
	self.__base:Close()
end

function Show()
	if window == nil then
		window = CHeroType()
		window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiherotypetip.prefab", nil, parentTrans, nil)
	end
	
	window:Show()
	return window
end

function SetOccupationTipsPos(pos)
	OccupationPos = pos
end

function SetParent(trans)
	parentTrans = trans
end

function SetGroupTipsPos(pos)
	GroupPos = pos
end

function SetLocation1TipsPos(pos)
	Location1 = pos
end

function SetLocation2TipsPos(pos)
	Location2 = pos
end

function Hide()
	if window ~= nil then
		window:Hide()
	end
end

function Close()
	txt = nil
	OccupationPos = nil
	GroupPos = nil
	Location1 = nil
	Location2 = nil
	if window ~= nil then
		window:Close()
		window = nil
	end
end

CHeroType = class(ui_base, nil, UIHeroType)
