local require = require
local print = print
local ipairs = ipairs
local table = table
local dump = dump
local tostring = tostring
local pairs = pairs

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
module("net_hero_module")
local event = require "event"
local package_pb = require "package_pb"
local msg_pb = require "msg_pb"
local hero_pb = require "hero_pb"
local herodecompose_pb = require("herodecompose_pb")
local net = require "net"
local net_route = require "net_route"
local player_mgr = require "player_mgr"
local flow_text = require "flow_text"
local lang = require "lang"
local lang_key = require "lang_res_key"
local windowMgr = require "ui_window_mgr"
local util = require "util"
local log = require("log")
local game_scheme = require "game_scheme"
local gw_equip_define = require "gw_equip_define"
local isReqState = nil--英雄技能更改请求状态
local isReqRewardState = nil--英雄剧情奖励领取请求状态
--[[
英雄升级请求
@Param HeroSid		英雄实体序列号
]]
function C2SHeroUpgradeREQ(HeroSid, bupgrade5)
    local msg = hero_pb.TMSG_HERO_UPGRADE_REQ()
    ------print("英雄实体序列号",HeroSid)
    msg.heroSid = HeroSid
    msg.bupgrade5 = bupgrade5
    ------ print("英雄升级请求",bupgrade5,msg.heroSid)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_UPGRADE_REQ, msg)
end

--[[英雄升级请求回复]]
function S2CHeroUpgradeRSP(msg)
    ------ print("英雄升级回复>>>>>>>>>>")
    if msg.err ~= 0 then
        --失败
        ----        print("英雄升级失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
        -- flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.HERO_UPGRAD_RSP, msg.upgradenum)
    end
    event.Trigger(event.HERO_UPDATE)
end

--[[
英雄技能更改请求
@Param HeroSid		英雄实体序列号
@Param SkillData	技能数据
	@Param bTakeOff     是否脱下
	@Param newSkillSid  新技能Sid(没有数据给0,下同)
	@Param oldSkillSid  旧技能Sid
	@Param skillPos     技能槽位置(1-3位，槽位从0计起,0天生技能，不可更换)
]]
function C2SHeroSkillUpdateREQ(HeroSid, SkillData)
    if isReqState or not HeroSid or not SkillData then
        util.DelayCall(3, function()
            isReqState = nil
        end)
        return
    end
    isReqState = true
    local msg = hero_pb.TMSG_HERO_SKILL_UPDATE_REQ()
    msg.heroSid = HeroSid
    msg.skill.bTakeOff = SkillData.bTakeOff
    msg.skill.newSkillSid = SkillData.newSkillSid
    msg.skill.oldSkillSid = SkillData.oldSkillSid
    msg.skill.skillPos = SkillData.skillPos
    ----    print("英雄技能更改请求",msg.heroSid,msg.skill.bTakeOff,msg.skill.newSkillSid,msg.skill.oldSkillSid,msg.skill.skillPos)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_SKILL_UPDATE_REQ, msg)
end

function C2SheroEquipmentUpdateRSP(msg)
    if msg.errorcode ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.errorcode + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("装备更新失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
    else
        ----        print("C2SheroEquipmentUpdateRSP>>>>>>>>>>>>>>>>>>")
        event.Trigger(gw_equip_define.HERO_EQUIP_WEAR)
        event.Trigger(gw_equip_define.HERO_EQUIP_UPDATE)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.EuqipHeroItemWear)
        red_system.TriggerRed(red_const.Enum.EuqipHeroItemLevel)
    end

end

--[[英雄进阶回复]]
function C2SHeroAdvanceRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("进阶失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
    else
        ------ print("C2SHeroAdvanceRSP>>>>>>>>>")
        event.Trigger(event.HERO_ADVANCE_RSP_EVENT)
        event.Trigger(event.HERO_UPDATE)
    end

end

--[[英雄觉醒回复]]
function C2SHeoAwakenRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("觉醒失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
    else
        event.Trigger(event.CREATE_ARR_UPDATE, msg.reSource)
        event.Trigger(event.HERO_AWAKEN_RSP, msg.reSource, msg.heroSid)
        event.Trigger(event.HERO_UPDATE)
        event.Trigger(event.CHECK_CHONGMU_POPUP, msg.heroSid)
    end

end

--[[英雄合成回复]]
function C2SHeoComposeRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("合成失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
    else
        -- local util = require "util"
        -- util.DelayCall(2,function()
        event.Trigger(event.HERO_UPDATE)
        event.Trigger(event.CREATE_HERO_AWARD, msg.heroSid, msg.reSource)
        event.Trigger(event.CREATE_ARR_UPDATE)
        -- end)

    end

end

---该方法暂放此类中
function GetAutoCompose()
    return PlayerPrefs.GetString(player_mgr.GetPlayerRoleID() .. "AutoComposeHero") == "true" and true or false
end

---该方法暂放此类中
function SetAutoCompose(bool)
    local str = tostring(bool)
    PlayerPrefs.SetString(player_mgr.GetPlayerRoleID() .. "AutoComposeHero", str)
end

--[[获取英雄时判断是否需要自动分解]]
function CheckAutoCompose()
    if PlayerPrefs.GetString(player_mgr.GetPlayerRoleID() .. "AutoComposeHero") == "true" then
        local arrHeroData = player_mgr.GetPalPartData()
        local selectedHero = {}
        for k, heroEntity in pairs(arrHeroData) do
            local hero_mgr = require "hero_mgr"
            if heroEntity.numProp.starLv <= hero_mgr.Hero_Star.Green then
                table.insert(selectedHero, heroEntity)
            end
        end
        if #selectedHero > 0 then
            local net_hero_module = require "net_hero_module"
            C2SHeoDecomposeREQ(selectedHero)
        end
    end
end

local decomposeHeroID = {}
local resetHeroSid = nil
--[[英雄分解请求]]
function C2SHeoDecomposeREQ(selectedHero, pType)
    local msg = herodecompose_pb.TMSG_ALTAR_HERO_DECOMPOSE_REQ()
    local sidStr = "发送分解请求:"
    decomposeHeroID = {}
    for k, hero in pairs(selectedHero) do
        msg.heroSid:append(hero.heroSid)
        sidStr = sidStr .. "," .. hero.heroSid
        table.insert(decomposeHeroID, hero)
    end
    msg.opType = pType or herodecompose_pb.enAlterOp_Decompose

    resetHeroSid = nil
    if pType == herodecompose_pb.enAlterOp_GoBack then
        for k, hero in pairs(selectedHero) do
            resetHeroSid = hero.heroSid
            break
        end
    end

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_ALTAR_HERO_DECOMPOSE_REQ, msg)
end

--[[英雄分解回复]]
function S2CHeoDecomposeRSP(msg)
    if msg.enErrorCode ~= 0 then
        --失败
        local errorCode = msg.enErrorCode
        if msg.enErrorCode == 4020 then
            --lang文本替换，lang表和错误码都不好修改
            errorCode = 4027
        end
        flow_text.Add(lang.Get(errorCode + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("分解失败",msg.enErrorCode + lang_key.KEY_ERROR_CODE_SERVER_BASE)
        if windowMgr:IsModuleShown("ui_hero_decompose") then
            local ui_hero_decompose = require("ui_hero_decompose")
            ui_hero_decompose.SetPanel(true)
            ui_hero_decompose.SetSelectHero()
        end
    else
        local returnArr = {}
        if msg.EquipSid then
            for i = 1, #msg.EquipSid do
                table.insert(returnArr, msg.EquipSid[i])
            end
        end
        if msg.SkillSid then
            for i = 1, #msg.SkillSid do
                table.insert(returnArr, msg.SkillSid[i])
            end
        end
        if msg.sigilSid then
            for i = 1, #msg.sigilSid do
                table.insert(returnArr, msg.sigilSid[i])
            end
        end
        --归还的饰品
        if msg.decoSid then
            for i = 1, #msg.decoSid do
                table.insert(returnArr, msg.decoSid[i])
            end
        end
        --归还的宝石
        if msg.starDiamondSid then
            for i = 1, #msg.starDiamondSid do
                table.insert(returnArr, msg.starDiamondSid[i])
            end
        end
        -- 其他地方也可能自动分解英雄
        handleRewardData(msg.rewardArr, returnArr, msg.opType, msg.palData)
        --local ui_hero_decompose = require "ui_hero_decompose"
        --ui_hero_decompose.ShowReward(event.DECOMPOSE_HERO_AWARD,msg.rewardArr,returnArr,msg.opType)

        event.Trigger(event.HERO_UPDATE)
        event.Trigger(event.CREATE_ARR_UPDATE)

        if msg.opType == herodecompose_pb.enAlterOp_GoBack and resetHeroSid then
            event.Trigger(event.RESET_HERO_ENTITY, resetHeroSid)
            resetHeroSid = nil
        end

        for i, hero in ipairs(decomposeHeroID) do
            event.Trigger(event.GAME_EVENT_REPORT, "hero_decompose", "{\"hero_decompose\":" .. hero.heroID .. "}")
            --英雄解构打点
            local essenceItem = nil
            if msg.opType == herodecompose_pb.enAlterOp_Destory then
                local reward_mgr = require "reward_mgr"
                local game_scheme = require "game_scheme"
                local heroDecompose_cfg = game_scheme:heroDecompose_0(hero.numProp.starLv, hero.numProp.stepLV)

                local heroCfg = game_scheme:Hero_0(hero.heroID)
                if heroCfg then
                    local heroType = heroCfg.type

                    if hero.rarityType == 3 then
                        --彩粹
                        essenceItem = reward_mgr.GetRewardGoods(heroDecompose_cfg.iReward4)
                    elseif hero.rarityType == 4 then
                        --精粹
                        if heroType == 1 or heroType == 2 or heroType == 3 or heroType == 4 then
                            essenceItem = reward_mgr.GetRewardGoods(heroDecompose_cfg.iReward2)
                        elseif heroType == 5 or heroType == 6 then
                            essenceItem = reward_mgr.GetRewardGoods(heroDecompose_cfg.iReward3)
                        end
                    else
                        --灵粹 
                        essenceItem = reward_mgr.GetRewardGoods(heroDecompose_cfg.iReward5)
                    end
                else
                    log.Error("can't find Config(Hero) by id = ", hero.heroID)
                end

                local json_str = {
                    hero_id = hero.heroID, --英雄ID
                    hero_lv = hero.numProp.lv, --英雄等级  ..hero.numProp.starLv..",\"hero_level\":"..hero.numProp.lv.."\"}"
                    hero_star = hero.numProp.starLv, --英雄星级
                    reward_num = essenceItem.num, --获得的物品数量
                    reward_id = essenceItem.id, --获得的物品数量
                }
                event.Trigger(event.GAME_EVENT_REPORT, "hero_redecompose", json_str)
            end
        end
    end
end

function handleRewardData(rewardArr, returnArr, opType, palData)
    local datalist = {}
    --插入归还的英雄
    if palData and #palData > 0 then
        for i = 1, #palData do
            local skin_mgr = require "skin_mgr"
            local skinid = skin_mgr.GetCurSkinID(palData[i].nHeroID)
            local hero = {
                sid = nil,
                id = palData[i].nHeroID,
                num = palData[i].nHeroNum, --回退本体数量
                nType = 2,
                starLevel = palData[i].nHeroStar, --回退本体星级
                skinID = skinid
            }
            table.insert(datalist, hero)
        end
    end
    for i = 1, #rewardArr do
        local hasItem = nil
        --合并
        for k, _item in pairs(datalist) do
            if _item.id == rewardArr[i].itemID and _item.nType == 1 then
                hasItem = _item
                break
            end
        end
        if hasItem ~= nil then
            hasItem["num"] = hasItem["num"] + rewardArr[i].itemNum
        else
            local goods = {
                id = rewardArr[i].itemID,
                num = rewardArr[i].itemNum,
                nType = 1,
                rewardid = rewardArr[i].rewardID
            }
            if goods.num ~= 0 then
                table.insert(datalist, goods)
            end

        end
    end
    --插入返还的装备技能物品
    for i = 1, #returnArr do
        local entity = player_mgr.GetPacketPartDataBySid(returnArr[i])

        local goods = {
            sid = returnArr[i],
            nType = 1,
            num = entity:GetGoodsNum() or 1,
            lv = entity:GetEquipStrLv()
        }
        if goods.num == 0 then
            goods.num = 1
        end
        table.insert(datalist, goods)
    end
    local content = (opType == herodecompose_pb.enAlterOp_Decompose) and (GetAutoCompose() and lang.Get(21035) or lang.Get(21036)) or lang.Get(21037)
    local title = (opType == herodecompose_pb.enAlterOp_Decompose) and lang.Get(21031) or lang.Get(21027)
    if opType == herodecompose_pb.enAlterOp_GoBack then
        content = lang.Get(21043)
        title = lang.Get(21041)
    elseif opType == herodecompose_pb.enAlterOp_Destory then
        title = lang.Get(23014)
        content = lang.Get(23014)
    end

    local resultData = { title = content, dataList = datalist }

    event.Trigger(event.DECOMPOSE_HERO_AWARD, resultData, title)
end


--[[英雄技能更改回复]]
function S2CHeroSkillUpdateRSP(msg)
    if msg.err ~= 0 then
        --失败
        ----        print("技能更改失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.SKILL_UPDATE)
        event.Trigger(event.HERO_UPDATE)
    end
    isReqState = nil
end

--[[
英雄技能升级请求
@Param SkillId		英雄技能id
]]
function C2SHeroSkillUpgradeREQ(SkillSId)
    local msg = hero_pb.TMSG_HERO_SKILL_UPGRADE_REQ()
    msg.skillSid = SkillSId
    ----    print("英雄技能升级请求",SkillSId)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_SKILL_UPGRADE_REQ, msg)
end

--[[英雄技能升级回复]]
function S2CHeroSkillUpgradeRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.SKILL_UPGRADE_SUCCESSFULLY)
    end
end

--[[购买英雄列表请求]]
function C2SBuyHeroListREQ()
    local msg = hero_pb.TMSG_BUY_HERO_LIST_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_BUY_HERO_LIST_REQ, msg)
end

--[[购买英雄列表回复]]
function S2CBuyHeroListRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    end
end

function S2CPHeroGoodsRevertNTF(msg)
    ----    print("S2CPHeroGoodsRevertNTF>>>>>",msg)
    if msg.bRevert then
        flow_text.Add(lang.Get(lang.KEY_PUT_EQUIPMENT_BACK))
    end
end

function S2CHeroReplaceRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.CREATE_ARR_UPDATE)
        event.Trigger(event.HERO_REPLACE_RSP, msg.heroSid)
        -- event.Trigger(event.HERO_UPDATE)

    end
end

function S2CHeroRecoverRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.CREATE_ARR_UPDATE)
        event.Trigger(event.HERO_RECOVER_RSP, msg.heroSid)
        -- event.Trigger(event.HERO_UPDATE)

    end
end

-- 英雄图鉴升级请求
function C2SHeroBiographyLevelUpREQ(heroid)
    local msg = hero_pb.TMSG_HERO_HANDBOOK_UPGRADE_REQ()
    ------print("英雄剧情奖励领取请求",heroId,heroLv)
    msg.heroid = heroid

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_HANDBOOK_UPGRADE_REQ, msg)
end

-- 英雄图鉴升级响应
function C2SHeroBiographyLevelUpRSP(msg)
    if msg.err ~= 0 then
        --失败
        ------print("英雄剧情奖励领取失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        isReqRewardState = nil
        return
    end
    if msg.data then
        event.Trigger(event.HERO_BIODATA_DATA_UPDATE, msg.data)
    end
end

-- 英雄图鉴等级通知(登录发送)
function C2SHeroBiographyListNTF(msg)
    event.Trigger(event.HERO_BIODATA_LIST_UPDATE, msg.data)
end

-- 英雄图鉴等级通知(获得英雄/升星时通知)
function C2SHeroBiographySingleUpdateNTF(msg)
    event.Trigger(event.HERO_BIODATA_SINGLE_UPDATE, msg.data)
end

--[[英雄剧情奖励领取请求]]
function C2SHeroBiographyREQ(heroId, heroLv)
    if isReqRewardState or not heroId or not heroLv then
        util.DelayCall(3, function()
            isReqRewardState = nil
        end)
        return
    end
    isReqRewardState = true
    local msg = hero_pb.TMSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ()
    ------print("英雄剧情奖励领取请求",heroId,heroLv)
    msg.heroID = heroId
    msg.heroLv = heroLv
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ, msg)
end
--[[英雄剧情奖励领取回复]]
function S2CHeroBiographyRSP(msg)
    if msg.err ~= 0 then
        --失败
        ------print("英雄剧情奖励领取失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        isReqRewardState = nil
        return
    end
    ------print("领取成功,奖励id==",#msg.rewardID)
    local iui_reward = require "iui_reward"
    local reward_mgr = require "reward_mgr"
    local rewardData = {}
    for i, v in ipairs(msg.rewardID) do
        table.insert(rewardData, reward_mgr.GetRewardGoods(v))
    end
    ------print("领取成功弹奖励面板",#rewardData)
    iui_reward.Show(rewardData, true, nil, lang.Get(lang.KEY_ACHIEVEMENT_GETREWARD), lang.Get(1))
    event.Trigger(event.HERO_BIODATA_UPDATE)
    isReqRewardState = nil
end

function S2C_RolePlotAdd_NTF(msg)
    ------print("==角色剧情数据更新==S2C_RolePlotAdd_NTF>>>>>>>>>>")
    --dump(msg.palRolePlotData)
    local biographyData = require("ui_hero_biography_data")
    local uiHeroBase = require("ui_hero_base")
    for k, v in ipairs(msg.palRolePlotData) do
        ------print("vvv>>>>>",v)
        biographyData.UpdataHeroState(v)
    end
    event.Trigger(event.HERO_BIODATA_UPDATE)
end

--穿装
function C2SChangeEquipment(heroSid, data)
    -- body
    if #data ~= 0 then
        local str = "请求改变装备："
        local msg = hero_pb.TMSG_HERO_EQUIPMENT_UPDATE_REQ()
        msg.heroSid = heroSid
        for k, value in pairs(data) do
            local equipments = msg.equipments:add()
            equipments.bTakeOff = value.bTakeOff
            equipments.newEquipmentSid = value.newEquipmentSid
            equipments.oldEquipmentSid = value.oldEquipmentSid
            equipments.pos = value.pos
            --打印==================================
            --str = str .. "\n脱下:" .. ((value.takeOff and "是") or "否") .. "\t旧:" .. value.oldEqSid .. "\t新:" .. value.newEqSid .. "\t位置:" .. value.pos
        end
        ------print(str)
        net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_EQUIPMENT_UPDATE_REQ, msg)
    end
end

--交换(在穿的装备一装在别的英雄的情况)
function C2SExchangeEquip(heroSid1, itemsid1, heroSid2, itemsid2, pos)
    ------print("heroSid1",heroSid1,"itemsid1",itemsid1,"heroSid2",heroSid2,"itemsi2",itemsid2)
    local package_pb = require "package_pb"
    local msg = package_pb.TMSG_HERO_UPDATE_GEMSTONE_REQ()
    msg.hg1.heroSid = heroSid1
    msg.hg1.itemsid = itemsid1
    msg.hg1.pos = pos
    msg.hg2.heroSid = heroSid2
    msg.hg2.itemsid = itemsid2
    msg.hg2.pos = pos

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_UPDATE_GEMSTONE_REQ, msg)
end

--交换装备回复
function S2C_MSG_HERO_UPDATE_GEMSTONE_RSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        ----        print("装备更新失败",msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE)
    else
        --event.Trigger(gw_equip_define.HERO_EQUIP_UPDATE, msg)
        event.Trigger(gw_equip_define.HERO_EQUIP_WEAR)
        event.Trigger(gw_equip_define.HERO_EQUIP_UPDATE)
    end
end


--[[英雄天赋设置请求]]
function C2SHeroSetTalentSkillREQ(heroSid, heroTalentIdx, talentSkillIdx)
    local msg = hero_pb.TMSG_HERO_TALENT_SKILL_ACTIVE_REQ()
    msg.heroSid = heroSid
    msg.heroTalentIdx = heroTalentIdx
    msg.talentSkillIdx = talentSkillIdx
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_TALENT_SKILL_ACTIVE_REQ, msg)
end

--[[英雄天赋设置回复]]
function C2SHeroSetTalentSkillRSP(msg)
    if msg.err ~= 0 then
        --失败
        flow_text.Add(lang.Get(msg.err + lang_key.KEY_ERROR_CODE_SERVER_BASE))
        return
    end
    -- 改为通知英雄天赋界面刷新
    event.Trigger(event.SET_HERO_TALENT_SKILL)
end

local Active_ID = 0
local Expend = 0
local HeroID = 0
function Send_HERO_ACTIVE_AWAKENSKILL(awakeSid, arrCostSid, arrCostFeedSid, arrCostItem)
    local msg = hero_pb.TMSG_HERO_ACTIVE_AWAKENSKILL_REQ()
    msg.dwTargetSid = awakeSid
    for k, v in ipairs(arrCostSid) do
        msg.dwCostSid:append(v)
    end
    for k, v in ipairs(arrCostFeedSid) do
        local a = player_mgr.GetPalPartDataBySid(v)
        Expend = a.heroID
        msg.dwAddtionCostSid:append(v)
    end
    for k, v in pairs(arrCostItem) do
        local t = msg.costItem:add()
        t.itemSid = k
        t.itemNum = v
    end
    local heroEntity = player_mgr.GetPalPartDataBySid(awakeSid)
    local heroCfg = game_scheme:Hero_0(heroEntity.heroID)
    Active_ID = heroCfg and heroCfg.awakeSkill
    HeroID = heroEntity.heroID
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_ACTIVE_AWAKENSKILL_REQ, msg)
end

function Recv_HERO_ACTIVE_AWAKENSKILL(msg)
    if msg.errCode ~= 0 then
        flow_text.Add(lang.Get(msg.errCode + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        local data = {
            Active_ID = Active_ID or 0, --玩家激活觉醒技的ID
            Expend = Expend or 0, --消耗激活觉醒技的材料
            HeroID = HeroID or 0, --激活觉醒技时对应的英雄ID
        }
        event.Trigger(event.GAME_EVENT_REPORT, "HeroAwakeSkill", data)
        event.Trigger(event.HERO_AWAKEN_SKILL_AWAKE_SUCCESSFULLY, msg.returnSouce)
    end
end

function Send_HERO_FAST_UPGRADE(heroSid, level)
    local msg = hero_pb.TMSG_HERO_FAST_UPGRADE_REQ()
    msg.heroSid = heroSid--英雄SID
    msg.level = level--要升到的等级
    --print("英雄SID",heroSid,"要升到的等级",level)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_HERO_FAST_UPGRADE_REQ, msg)
end

function Recv_HERO_FAST_UPGRADE(msg)
    --print("msg.errCode",msg.errCode)
    if msg.errCode ~= 0 then
        flow_text.Add(lang.Get(msg.errCode + lang_key.KEY_ERROR_CODE_SERVER_BASE))
    else
        event.Trigger(event.HERO_FAST_UPGRADE_RSP)
    end
end
--获取试用英雄请求
function Send_TRIALHERO_GET_REQ(data)
    print("获取试用英雄请求>>>>>")
    local msg = hero_pb.TMSG_TRIALHERO_GET_REQ()
    msg.activityID = data.activityID
    msg.heroID = data.heroID
    --print("英雄SID",heroSid,"要升到的等级",level)
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, msg_pb.MSG_TRIALHERO_GET_REQ, msg)
end

--获取试用英雄回复
function Recv_TRIALHERO_GET(msg)
    -- print("qsy_yxsy:[net_hero_module]获取试用英雄回复>>>>>",msg)
    if msg.errCode ~= 0 then
        --失败
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        local hero_trial_mgr = require "hero_trial_mgr"
        --qsy_TODO
        --1、弹试用英雄获取弹窗2、刷新领取界面时间倒计时显示
        if msg.activityID then
            hero_trial_mgr.ShowGetHeroPopup(msg.activityID)
        end
        -- print("qsy_yxsy:[net_hero_module]UPDATE_TRIAL_HERO_GET>>>>>")
        event.Trigger(event.UPDATE_TRIAL_HERO_GET)

        --打点
        -- hero_trial_mgr.GameEventReport_Receive()
    end
end

-------------------------------------------------------------------
-- /// 注册消息
-------------------------------------------------------------------
local MessageTable = {


    { msg_pb.MSG_HERO_UPGRADE_RSP, S2CHeroUpgradeRSP, hero_pb.TMSG_HERO_UPGRADE_RSP, }, --英雄升级回复
    { msg_pb.MSG_HERO_SKILL_UPDATE_RSP, S2CHeroSkillUpdateRSP, hero_pb.TMSG_HERO_SKILL_UPDATE_RSP, }, --英雄技能更改回复
    { msg_pb.MSG_HERO_EQUIPMENT_UPDATE_RSP, C2SheroEquipmentUpdateRSP, hero_pb.TMSG_HERO_EQUIPMENT_UPDATE_RSP, }, --英雄装备更新回复
    { msg_pb.MSG_HERO_SKILL_UPGRADE_RSP, S2CHeroSkillUpgradeRSP, hero_pb.TMSG_HERO_SKILL_UPGRADE_RSP, }, --英雄技能升级回复
    { msg_pb.MSG_HERO_ADVANCE_RSP, C2SHeroAdvanceRSP, hero_pb.TMSG_HERO_ADVANCE_RSP, }, --英雄进阶回复
    { msg_pb.MSG_HERO_AWAKEN_RSP, C2SHeoAwakenRSP, hero_pb.TMSG_HERO_AWAKEN_RSP, }, --英雄觉醒回复
    { msg_pb.MSG_BUY_HERO_LIST_RSP, S2CBuyHeroListRSP, hero_pb.TMSG_BUY_HERO_LIST_RSP, }, --购买英雄列表回复
    { msg_pb.MSG_HERO_COMPOSE_RSP, C2SHeoComposeRSP, hero_pb.TMSG_HERO_COMPOSE_RSP, }, --英雄觉醒回复
    { msg_pb.MSG_ALTAR_HERO_DECOMPOSE_RSP, S2CHeoDecomposeRSP, herodecompose_pb.TMSG_ALTAR_HERO_DECOMPOSE_RSP, }, --英雄分解回复
    { msg_pb.MSG_HERO_GOODS_REVERT_NTF, S2CPHeroGoodsRevertNTF, hero_pb.TMSG_HERO_GOODS_REVERT_NTF, }, --装备放回背包回复
    { msg_pb.MSG_HERO_CHANGE_RSP, S2CHeroReplaceRSP, hero_pb.TMSG_HERO_CHANGE_RSP, }, --英雄置换回复
    { msg_pb.MSG_HERO_CHANGE_RESTORE_RSP, S2CHeroRecoverRSP, hero_pb.TMSG_HERO_CHANGE_RESTORE_RSP, }, --英雄置换回退回复
    { msg_pb.MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP, S2CHeroBiographyRSP, hero_pb.TMSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP, }, --英雄剧情奖励回复
    { msg_pb.MSG_HERO_ROLEPLOT_MOD_INFO_NTF, S2C_RolePlotAdd_NTF, hero_pb.TMSG_HERO_ROLEPLOT_MOD_INFO_NTF }, --角色剧情数据更新回复
    { msg_pb.MSG_HERO_UPDATE_GEMSTONE_RSP, S2C_MSG_HERO_UPDATE_GEMSTONE_RSP, package_pb.TMSG_HERO_UPDATE_GEMSTONE_RSP }, --交换装备
    { msg_pb.MSG_HERO_TALENT_SKILL_ACTIVE_RSP, C2SHeroSetTalentSkillRSP, hero_pb.TMSG_HERO_TALENT_SKILL_ACTIVE_RSP }, --天赋设置
    { msg_pb.MSG_HERO_HANDBOOK_UPGRADE_RSP, C2SHeroBiographyLevelUpRSP, hero_pb.TMSG_HERO_HANDBOOK_UPGRADE_RSP }, --图鉴升级
    { msg_pb.MSG_HERO_HANDBOOK_LV_LIST_NTF, C2SHeroBiographyListNTF, hero_pb.TMSG_HERO_HANDBOOK_LV_LIST_NTF }, --英雄图鉴等级列表通知(登录发送)
    { msg_pb.MSG_HERO_HANDBOOK_LV_NTF, C2SHeroBiographySingleUpdateNTF, hero_pb.TMSG_HERO_HANDBOOK_LV_NTF }, --英雄图鉴等级通知(获得英雄/升星时通知)
    { msg_pb.MSG_HERO_ACTIVE_AWAKENSKILL_RSP, Recv_HERO_ACTIVE_AWAKENSKILL, hero_pb.TMSG_HERO_ACTIVE_AWAKENSKILL_RSP }, --英雄觉醒技激活
    { msg_pb.MSG_HERO_FAST_UPGRADE_RSP, Recv_HERO_FAST_UPGRADE, hero_pb.TMSG_HERO_FAST_UPGRADE_RSP }, --英雄快速升级
    { msg_pb.MSG_TRIALHERO_GET_RSP, Recv_TRIALHERO_GET, hero_pb.TMSG_TRIALHERO_GET_RSP }, --获取试用英雄回复



}

net_route.RegisterMsgHandlers(MessageTable)

--断线时将请求状态清空
function Over()
    isReqState = nil
    isReqRewardState = nil
end
event.Register(event.NET_DISCONNECT_EVENT, Over)

--region 新英雄系统

local xManMsg_pb = require "xManMsg_pb"
local gw_hero_define = require("gw_hero_define")


--module("net_hero_module")

function Error_Code(msg)
    if not msg.err then
        log.Error("#### errorCode==nil ####")
        return false;
    end
    if msg.err ~= 0 then
        -- 搜索失败
        --log.Error("gw_hero: ", msg.err)
        flow_text.Add(lang.Get(100000 + msg.err))
        return false
    end
    return true
end


-- 英雄升级请求(回复发TMSG_PROP_UPDATE_NTF)
function MSG_HERO_UPGRADE_NEW_REQ(data)
    -- heroSid
    local msg = hero_pb.TMSG_HERO_UPGRADE_NEW_REQ()
    msg.heroSid = data.heroSid
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_UPGRADE_NEW_REQ, msg)
end


-- 英雄升级请求回复
function MSG_HERO_UPGRADE_NEW_RSP(msg)
    -- err
    if Error_Code(msg) then
        -- 英雄升级成功
        event.Trigger(gw_hero_define.UPGRADE_HERO_NEW, msg)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.HeroItemLevel)
    else
        --log.Error("gw_hero: 英雄升级失败", msg.err)
    end
end

-- 英雄碎片合成请求
function MSG_HERO_DEBRIS_COMPOSE_NEW_REQ(data)
    -- heroSid
    local msg = hero_pb.TMSG_HERO_DEBRIS_COMPOSE_NEW_REQ()
    msg.heroItemId = data.heroItemId

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_DEBRIS_COMPOSE_NEW_REQ, msg)

end

-- 英雄碎片合成回复
function MSG_HERO_DEBRIS_COMPOSE_NEW_RSP(msg)
    -- err
    -- heroSid
    if Error_Code(msg) then
        -- 英雄升级成功
        event.Trigger(gw_hero_define.HERO_DEBRIS_COMPOSE_NEW, msg)
    else
        log.Error("gw_hero: 英雄碎片合成失败")
    end
end

-- 英雄觉醒请求
function MSG_HERO_AWAKEN_NEW_REQ(data)
    -- heroSid
    -- goods1
    -- goods2
    local msg = hero_pb.TMSG_HERO_AWAKEN_NEW_REQ()
    msg.heroSid = data.heroSid

    msg.goods1.itemSid = data.goods1.itemSid
    msg.goods1.itemNum = data.goods1.itemNum

    msg.goods2.itemSid = data.goods2.itemSid
    msg.goods2.itemNum = data.goods2.itemNum

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_AWAKEN_NEW_REQ, msg)

end

-- 英雄觉醒回复(属性更新发TMSG_PROP_UPDATE_NTF)
function MSG_HERO_AWAKEN_NEW_RSP(msg)
    -- err
    -- heroSid
    if Error_Code(msg) then
        -- 英雄升级成功
        event.Trigger(gw_hero_define.HERO_AWAKEN_NEW, msg)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.HeroItemStar)
        red_system.TriggerRed(red_const.Enum.HeroItemSkill)
        
        red_system.TriggerRed(red_const.Enum.HeroStoryToggle1)
        red_system.TriggerRed(red_const.Enum.HeroStoryToggle2)
        red_system.TriggerRed(red_const.Enum.HeroStoryToggle3)
    else
        log.Error("gw_hero: 英雄觉醒失败")
    end
end

-- 英雄技能升级请求
function MSG_HERO_SKILL_UPGRADE_NEW_REQ(data)
    -- skillSid
    local msg = hero_pb.TMSG_HERO_SKILL_UPGRADE_NEW_REQ()

    msg.heroSid = data.heroSid
    msg.skillIndex = data.skillIndex

    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_SKILL_UPGRADE_NEW_REQ, msg)

end

-- 英雄技能升级回复
function MSG_HERO_SKILL_UPGRADE_NEW_RSP(msg)
    -- err
    if Error_Code(msg) then
        -- 英雄升级成功
        event.Trigger(gw_hero_define.HERO_SKILL_UPGRADE_NEW, msg)
        local red_system = require "red_system"
        local red_const = require "red_const"
        red_system.TriggerRed(red_const.Enum.HeroItemSkillIndex)
    else
        log.Error("gw_hero: 英雄技能升级")
    end
end

--region 废弃的 

-- 英雄升级请求回复 s-c
function MSG_HERO_UPGRADE_RSP(msg)
    -- err
    -- upgradenum

end

-- 英雄碎片合成请求
function MSG_HERO_DEBRIS_COMPOSE_REQ(data)
    -- heroSid
    -- debrisSid
    -- debrisNum
    local msg = hero_pb.TMSG_HERO_DEBRIS_COMPOSE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_DEBRIS_COMPOSE_REQ, msg)

end

-- 英雄碎片合成回复
function MSG_HERO_DEBRIS_COMPOSE_RSP(msg)
    -- err
    -- heroSid
    -- debrisSid
    -- debrisNum

end

-- 英雄装备更新请求 C->S
function MSG_HERO_EQUIPMENT_UPDATE_REQ(data)
    -- heroSid
    -- equipments
    local msg = hero_pb.TMSG_HERO_EQUIPMENT_UPDATE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_EQUIPMENT_UPDATE_REQ, msg)

end

-- 英雄装备更新回复 S->C
function MSG_HERO_EQUIPMENT_UPDATE_RSP(msg)
    -- err

end

-- 英雄进阶请求 C->S
function MSG_HERO_ADVANCE_REQ(data)
    -- heroSid
    local msg = hero_pb.TMSG_HERO_ADVANCE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_ADVANCE_REQ, msg)

end

-- 英雄进阶回复(属性更新发TMSG_PROP_UPDATE_NTF) S->C
function MSG_HERO_ADVANCE_RSP(msg)
    -- err

end

-- 英雄技能更改请求 C->S
function MSG_HERO_SKILL_UPDATE_REQ(data)
    -- heroSid
    -- skill
    local msg = hero_pb.TMSG_HERO_SKILL_UPDATE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_SKILL_UPDATE_REQ, msg)

end

-- 英雄技能更改回复 S->C
function MSG_HERO_SKILL_UPDATE_RSP(msg)
    -- err

end

-- 英雄技能升级请求 C->S
function MSG_HERO_SKILL_UPGRADE_REQ(data)
    -- skillSid
    local msg = hero_pb.TMSG_HERO_SKILL_UPGRADE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_SKILL_UPGRADE_REQ, msg)

end

-- 英雄技能升级回复 S->C
function MSG_HERO_SKILL_UPGRADE_RSP(msg)
    -- err

end

-- 英雄觉醒请求 C->S
function MSG_HERO_AWAKEN_REQ(data)
    -- data
    local msg = hero_pb.TMSG_HERO_AWAKEN_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_AWAKEN_REQ, msg)

end

-- 英雄觉醒回复(属性更新发TMSG_PROP_UPDATE_NTF) S->C
function MSG_HERO_AWAKEN_RSP(msg)
    -- err
    -- reSource
    -- heroSid

end

-- 英雄合成请求 C->S
function MSG_HERO_COMPOSE_REQ(data)
    -- composeSid
    -- starLv
    -- heroSid
    -- NewHeroId
    local msg = hero_pb.TMSG_HERO_COMPOSE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_COMPOSE_REQ, msg)

end

-- 英雄合成响应 S->C
function MSG_HERO_COMPOSE_RSP(msg)
    -- err
    -- heroSid
    -- reSource

end

-- 购买英雄列表请求 C->S
function MSG_BUY_HERO_LIST_REQ(data)
    local msg = hero_pb.TMSG_BUY_HERO_LIST_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_BUY_HERO_LIST_REQ, msg)

end

-- 购买英雄列表回复 S->C
function MSG_BUY_HERO_LIST_RSP(msg)
    -- err
    -- buyNum

end

-- 删除英雄时装备技能等退回通知 S->C
function MSG_HERO_GOODS_REVERT_NTF(msg)
    -- bRevert

end

-- 英雄置换请求 c->s
function MSG_HERO_CHANGE_REQ(data)
    -- heroSid
    local msg = hero_pb.TMSG_HERO_CHANGE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_CHANGE_REQ, msg)

end

-- 英雄置换回复 s->c
function MSG_HERO_CHANGE_RSP(msg)
    -- err
    -- heroSid

end

-- 英雄置换回退请求 c->s
function MSG_HERO_CHANGE_RESTORE_REQ(data)
    -- heroSid
    local msg = hero_pb.TMSG_HERO_CHANGE_RESTORE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_CHANGE_RESTORE_REQ, msg)

end

-- 英雄置换回退回复 s->c
function MSG_HERO_CHANGE_RESTORE_RSP(msg)
    -- err
    -- heroSid

end

-- 英雄快速升级
function MSG_HERO_FAST_UPGRADE_REQ(data)
    -- heroSid
    -- level
    local msg = hero_pb.TMSG_HERO_FAST_UPGRADE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_FAST_UPGRADE_REQ, msg)

end

-- 英雄快速升级
function MSG_HERO_FAST_UPGRADE_RSP(msg)
    -- errCode
    if msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        -- Success Handling
    end
end

-- 装备升级请求 c->s
function MSG_EQUIP_DEVOUR_REQ(data)
    -- equipSid
    -- cost
    -- costsource
    local msg = hero_pb.TMSG_EQUIP_DEVOUR_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_EQUIP_DEVOUR_REQ, msg)

end

-- 装备升级回复 s->c
function MSG_EQUIP_DEVOUR_RSP(msg)
    -- err

end

-- 英雄图鉴增加英雄的通知  MSG_HERO_HANDBOOK_ADD_HERO_NTF
function MSG_HERO_HANDBOOK_ADD_HERO_NTF(msg)
    -- palHBookData

end

-- 英雄图鉴英雄阶段信息修改的通知  MSG_HERO_ROLEPLOT_MOD_INFO_NTF
function MSG_HERO_ROLEPLOT_MOD_INFO_NTF(msg)
    -- palRolePlotData

end

-- 请求领取英雄角色阶段奖励 -- 请求   MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ
function MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ(data)
    -- heroID
    -- heroLv
    local msg = hero_pb.TMSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_REQ, msg)

end

-- 请求领取英雄角色阶段奖励 -- 应答 MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP
function MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP(msg)
    -- err
    -- rewardID

end

-- 天赋技能激活请求
function MSG_HERO_TALENT_SKILL_ACTIVE_REQ(data)
    -- heroSid
    -- heroTalentIdx
    -- talentSkillIdx
    local msg = hero_pb.TMSG_HERO_TALENT_SKILL_ACTIVE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_TALENT_SKILL_ACTIVE_REQ, msg)

end

-- 天赋技能激活回复
function MSG_HERO_TALENT_SKILL_ACTIVE_RSP(msg)
    -- err

end

-- 英雄图鉴等级列表通知(登录发送)
function MSG_HERO_HANDBOOK_LV_LIST_NTF(msg)
    -- data

end

-- 英雄图鉴等级通知(获得英雄/升星时通知)
function MSG_HERO_HANDBOOK_LV_NTF(msg)
    -- data

end

-- 英雄图鉴激活/升级请求
function MSG_HERO_HANDBOOK_UPGRADE_REQ(data)
    -- heroid
    local msg = hero_pb.TMSG_HERO_HANDBOOK_UPGRADE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_HANDBOOK_UPGRADE_REQ, msg)

end

-- 英雄图鉴激活/升级回复
function MSG_HERO_HANDBOOK_UPGRADE_RSP(msg)
    -- err
    -- data

end

-- 英雄觉醒技能
function MSG_HERO_ACTIVE_AWAKENSKILL_REQ(data)
    -- dwTargetSid
    -- dwCostSid
    -- dwAddtionCostSid
    -- costItem
    local msg = hero_pb.TMSG_HERO_ACTIVE_AWAKENSKILL_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_ACTIVE_AWAKENSKILL_REQ, msg)

end

function MSG_HERO_ACTIVE_AWAKENSKILL_RSP(msg)
    -- errCode
    -- returnSouce
    if msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        -- Success Handling
    end
end

-- 英雄饰品更新请求 C->S
function MSG_HERO_DECO_UPDATE_REQ(data)
    -- heroSid
    -- decorates
    local msg = hero_pb.TMSG_HERO_DECO_UPDATE_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_HERO_DECO_UPDATE_REQ, msg)

end

-- 英雄饰品更新回复 S->C
function MSG_HERO_DECO_UPDATE_RSP(msg)
    -- err

end

-- 获取试用英雄请求
function MSG_TRIALHERO_GET_REQ(data)
    -- activityID
    -- heroID
    local msg = hero_pb.TMSG_TRIALHERO_GET_REQ()
    net.SendMessage(net.Endpoint_Client, net.Endpoint_Zone, 0, xManMsg_pb.MSG_TRIALHERO_GET_REQ, msg)

end

-- 获取试用英雄回复
function MSG_TRIALHERO_GET_RSP(msg)
    -- errCode
    -- activityID
    if msg.errCode ~= 0 then
        flow_text.Add(lang.Get(100000 + msg.errCode))
    else
        -- Success Handling
    end
end

--endregion

local MessageTable2 = {
    { xManMsg_pb.MSG_HERO_UPGRADE_NEW_RSP, MSG_HERO_UPGRADE_NEW_RSP, hero_pb.TMSG_HERO_UPGRADE_NEW_RSP },
    { xManMsg_pb.MSG_HERO_DEBRIS_COMPOSE_NEW_RSP, MSG_HERO_DEBRIS_COMPOSE_NEW_RSP, hero_pb.TMSG_HERO_DEBRIS_COMPOSE_NEW_RSP },
    { xManMsg_pb.MSG_HERO_AWAKEN_NEW_RSP, MSG_HERO_AWAKEN_NEW_RSP, hero_pb.TMSG_HERO_AWAKEN_NEW_RSP },
    { xManMsg_pb.MSG_HERO_SKILL_UPGRADE_NEW_RSP, MSG_HERO_SKILL_UPGRADE_NEW_RSP, hero_pb.TMSG_HERO_SKILL_UPGRADE_NEW_RSP },
    --{ xManMsg_pb.MSG_HERO_UPGRADE_RSP, MSG_HERO_UPGRADE_RSP, hero_pb.TMSG_HERO_UPGRADE_RSP },
    --{ xManMsg_pb.MSG_HERO_DEBRIS_COMPOSE_RSP, MSG_HERO_DEBRIS_COMPOSE_RSP, hero_pb.TMSG_HERO_DEBRIS_COMPOSE_RSP },
    --{ xManMsg_pb.MSG_HERO_EQUIPMENT_UPDATE_RSP, MSG_HERO_EQUIPMENT_UPDATE_RSP, hero_pb.TMSG_HERO_EQUIPMENT_UPDATE_RSP },
    --{ xManMsg_pb.MSG_HERO_ADVANCE_RSP, MSG_HERO_ADVANCE_RSP, hero_pb.TMSG_HERO_ADVANCE_RSP },
    --{ xManMsg_pb.MSG_HERO_SKILL_UPDATE_RSP, MSG_HERO_SKILL_UPDATE_RSP, hero_pb.TMSG_HERO_SKILL_UPDATE_RSP },
    --{ xManMsg_pb.MSG_HERO_SKILL_UPGRADE_RSP, MSG_HERO_SKILL_UPGRADE_RSP, hero_pb.TMSG_HERO_SKILL_UPGRADE_RSP },
    --{ xManMsg_pb.MSG_HERO_AWAKEN_RSP, MSG_HERO_AWAKEN_RSP, hero_pb.TMSG_HERO_AWAKEN_RSP },
    --{ xManMsg_pb.MSG_HERO_COMPOSE_RSP, MSG_HERO_COMPOSE_RSP, hero_pb.TMSG_HERO_COMPOSE_RSP },
    --{ xManMsg_pb.MSG_BUY_HERO_LIST_RSP, MSG_BUY_HERO_LIST_RSP, hero_pb.TMSG_BUY_HERO_LIST_RSP },
    --{ xManMsg_pb.MSG_HERO_GOODS_REVERT_NTF, MSG_HERO_GOODS_REVERT_NTF, hero_pb.TMSG_HERO_GOODS_REVERT_NTF },
    --{ xManMsg_pb.MSG_HERO_CHANGE_RSP, MSG_HERO_CHANGE_RSP, hero_pb.TMSG_HERO_CHANGE_RSP },
    --{ xManMsg_pb.MSG_HERO_CHANGE_RESTORE_RSP, MSG_HERO_CHANGE_RESTORE_RSP, hero_pb.TMSG_HERO_CHANGE_RESTORE_RSP },
    --{ xManMsg_pb.MSG_HERO_FAST_UPGRADE_RSP, MSG_HERO_FAST_UPGRADE_RSP, hero_pb.TMSG_HERO_FAST_UPGRADE_RSP },
    --{ xManMsg_pb.MSG_EQUIP_DEVOUR_RSP, MSG_EQUIP_DEVOUR_RSP, hero_pb.TMSG_EQUIP_DEVOUR_RSP },
    --{ xManMsg_pb.MSG_HERO_HANDBOOK_ADD_HERO_NTF, MSG_HERO_HANDBOOK_ADD_HERO_NTF, hero_pb.TMSG_HERO_HANDBOOK_ADD_HERO_NTF },
    --{ xManMsg_pb.MSG_HERO_ROLEPLOT_MOD_INFO_NTF, MSG_HERO_ROLEPLOT_MOD_INFO_NTF, hero_pb.TMSG_HERO_ROLEPLOT_MOD_INFO_NTF },
    --{ xManMsg_pb.MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP, MSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP, hero_pb.TMSG_HERO_ROLEPLOT_GET_STAGE_REWARD_RSP },
    --{ xManMsg_pb.MSG_HERO_TALENT_SKILL_ACTIVE_RSP, MSG_HERO_TALENT_SKILL_ACTIVE_RSP, hero_pb.TMSG_HERO_TALENT_SKILL_ACTIVE_RSP },
    --{ xManMsg_pb.MSG_HERO_HANDBOOK_LV_LIST_NTF, MSG_HERO_HANDBOOK_LV_LIST_NTF, hero_pb.TMSG_HERO_HANDBOOK_LV_LIST_NTF },
    --{ xManMsg_pb.MSG_HERO_HANDBOOK_LV_NTF, MSG_HERO_HANDBOOK_LV_NTF, hero_pb.TMSG_HERO_HANDBOOK_LV_NTF },
    --{ xManMsg_pb.MSG_HERO_HANDBOOK_UPGRADE_RSP, MSG_HERO_HANDBOOK_UPGRADE_RSP, hero_pb.TMSG_HERO_HANDBOOK_UPGRADE_RSP },
    --{ xManMsg_pb.MSG_HERO_ACTIVE_AWAKENSKILL_RSP, MSG_HERO_ACTIVE_AWAKENSKILL_RSP, hero_pb.TMSG_HERO_ACTIVE_AWAKENSKILL_RSP },
    --{ xManMsg_pb.MSG_HERO_DECO_UPDATE_RSP, MSG_HERO_DECO_UPDATE_RSP, hero_pb.TMSG_HERO_DECO_UPDATE_RSP },
    --{ xManMsg_pb.MSG_TRIALHERO_GET_RSP, MSG_TRIALHERO_GET_RSP, hero_pb.TMSG_TRIALHERO_GET_RSP },
}
net_route.RegisterMsgHandlers(MessageTable2)
--endregion
