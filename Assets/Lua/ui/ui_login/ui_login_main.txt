local print = print
local require = require
local os = os
local tonumber = tonumber
local typeof = typeof
local pairs = pairs
local string = string
local tostring = tostring
local dump = dump
local math = math
local coroutine = coroutine
local type = type
local unpack = unpack
local table = table

local log = require "login_log"
local game_scheme = require "game_scheme"
local class = require "class"
local ui_base = require "ui_base"
local game = require "game"
--local facebook = require "facebook"
local net_gateway_module = require "net_gateway_module"
local net_login_module = require "net_login_module"
local login_pb = require "login_pb"
local flow_text = require "flow_text"
local event = require "event"
local lang = require "lang"
--local GooglePlayProxy = require "google"
local net_route = require "net_route"
local msg_pb = require "msg_pb"
local AutoLogin = require "ui_iauto_login"
local login_preload_mgr = require "login_preload_mgr"
local ui_setting_data = require "ui_setting_data"
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local account_data = require "account_data"
local game_config = require "game_config"
local q1sdk = require "q1sdk"
local setting_server_data = require "setting_server_data"
local util = require "util"
local windowMgr = require "ui_window_mgr"
local ui_login_main_view = require "ui_login_main_view"
local ui_login_main_auto_view = require "ui_login_main_auto_login_view"
local ui_login_main_mgr = require "ui_login_main_mgr"
local const = require "const"
local http_inst = require "http_inst"
local ReviewingUtil = require "ReviewingUtil"
local base_object = require "base_object"
local files_version_mgr = require "files_version_mgr"

local SpriteSwitcher = CS.War.UI.SpriteSwitcher
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local GameObject = CS.UnityEngine.GameObject
local Q1_LoginErrorCode = CS.Q1.LoginErrorCode
local EnSubModel = ui_setting_attr_enum.EnSubModel
local EnBaseAttrKey = ui_setting_attr_enum.EnBaseAttrKey
local RectTransform = CS.UnityEngine.RectTransform
local screen_util = require "screen_util"
local AspectRatioFitter = CS.UnityEngine.UI.AspectRatioFitter
local Button = CS.UnityEngine.UI.Button

local Utility = CS.War.Script.Utility
local IsInEditor = CS.War.Script.Utility.IsInEditor
local Time = CS.UnityEngine.Time
local Application = CS.UnityEngine.Application
local RuntimePlatform = CS.UnityEngine.RuntimePlatform
local Debug = CS.UnityEngine.Debug
local HTTPReq = CS.War.Script.HTTPReq
local Resources = CS.UnityEngine.Resources
local Image = CS.UnityEngine.UI.Image
local Texture2D = CS.UnityEngine.Texture2D
local Camera = CS.UnityEngine.Camera
--require "iosystem_load"
local PingCheck = CS.War.Script.PingCheck
local url_mgr = require "url_mgr"

local GameScheme = GameScheme
local Config = CS.War.Base.Config

module("ui_login_main")

local tNoticeData = nil
local noticePassed = false
local bHasOpenNotice = false
local hasInitDataLuaInterface = false
local initingDataLuaInterface = false
local hasInitPreloadAsset = false
local hasInitPreloadResource = false
local hasInitAllLuaDatabase = false
local save_sdk_callback_args = nil

local eNOTICE_TIP_TIME = {
    EVERY_TIME = "1", --每次登录
    EVERY_DAY = "2", --每天第一次登录
    ONLY_ONCE = "3" --仅第一次登录
}

local eNOTICE_TIP_STYLE = {
    SHOW_WINDOW = "1", --显示窗口
    SHOW_RED_DOT = "2" --显示红点
}

local SDKInited = false
local LoginMain = {}
local window = nil
local bAutoLoading = true

local AccountNameSave = ""
local StrTokenSave = ""

local ResetTitleClickLimit = 5
local ClickTitleRepeatCount = 10
local LastClickTick = 0
local ClickCount = 0
local isShowBIndPrompt = false
local isCallLogin = false --用于判断是否是通过调用登陆接口而触发的回调（因为游戏内注册绑定也是走同样的回调）
local isLogout = false
local isInMaintain = false -- 是否在维护

local errCode_temp = nil --登陆数据临时保存一下
local errMsg_temp = nil
local token_temp = nil
local userID_temp = nil
local loginType_temp = nil
local openInfo_temp = nil
local serverInfo = nil
local worldid = nil
local newWorldid = nil

local hasLobbyShow = false

--用于耗时统计
local consumeStart = 0
local loadedFlag = false

local loginPending = false
--local connectingServer = false

local pingCheck = nil

local loginView = nil
local requestRecordInfo = {
    requestCount = 0,
    intervalTime = {0.5, 1, 2}
}

local preloadResStatus = {
    resource = false,
    login = false,
    loginmain = false
}

local SessionLoginHelp = {
    sessionLoginWays = {
        [login_pb.enLoginPartnerID_Google] = 1,
        [login_pb.enLoginPartnerID_Facebook] = 1,
        [login_pb.enLoginPartnerID_GameCenter] = 1,
        [login_pb.enLoginPartnerID_Apple] = 1,
        [login_pb.enLoginPartnerID_HuaWei] = 1,
        [login_pb.enLoginPartnerID_Email] = 1,
        [login_pb.enLoginPartnerID_Visitor] = 1,
    }
}
function SessionLoginHelp.CanSessionLogin(loginType)
    if SessionLoginHelp.sessionLoginWays[loginType] then
        return true
    else
        return false
    end
end

local preloadResTotalCount = 0
local preloadResLoadedCount = 0
local preloadResPercent = 0
local preloadResRecord = {}

local loginProgressTicker = nil
-- 是否正在切换账号，弹了sdk框
local isSwitching = false

-- 登录失败重试次数
local loginFailedTimes = 0
local loginTimer = nil
local hasLoginCallback = false

-- local LastWorldUrl = {
-- 	{"http://api.dev.q1op.com/games/2118/api/users/%s/last-world", "https://api-cn.q1.com/games/2118/api/users/%s/last-world"},		-- 中国
-- 	{"http://api.dev.q1op.com/games/2118/api/users/%s/last-world", "https://api-ea.q1.com/games/2118/api/users/%s/last-world"},		-- 美加
-- 	{"http://api.dev.q1op.com/games/2118/api/users/%s/last-world", "https://api-sa.q1.com/games/2118/api/users/%s/last-world"},		-- 东南亚
-- }

LoginMain.widget_table = {
    req = {path = "", type = HTTPReq},
    BtnVisitorEditor = {path = "LoginBg/List/BtnVisitorEditor", type = "Button"},
    BtnAccountEditor = {path = "LoginBg/List/BtnAccountEditor", type = "Button"},
    BtnVisitor = {path = "LoginBg/List/BtnVisitor", type = "Button"},
    BtnAccount = {path = "LoginBg/List/BtnAccount", type = "Button"},
    BtnWechat = {path = "LoginBg/List/BtnWechat", type = "Button"}, --微信按钮
    BtnFacebook = {path = "LoginBg/List/BtnFacebook", type = "Button"},
    BtnGgPlay = {path = "LoginBg/List/BtnGgPlay", type = "Button"},
    BtnNotice = {path = "ButtonNotice", type = "Button"},
    BtnSelectLanguage = {path = "ButtonLanguage", type = "Button"},
    BtnUserTerms = {path = "ButtonUserTerms", type = "Button"},
    --VisitorLoginBg = {path = "VisitorLoginBg", type = "Image"},
    --VisitorLoginMsg = {path = "VisitorLoginBg/VisitorLoginMsg", type = "Text"},
    noticeFlag = {path = "ButtonNotice/noticeFlag", type = "Image"},
    ButtonNoticeText = {path = "ButtonNotice/Text", type = "Text"},
    SelectLanguageText = {path = "ButtonLanguage/Text", type = "Text"},
    ButtonUserTermsText = {path = "ButtonUserTerms/Text", type = "Text"},
    BtnTitle = {path = "BtnTitle", type = "Button"},
    VisitorText = {path = "LoginBg/List/BtnVisitor/Text", type = "Text"},
    AccountText = {path = "LoginBg/List/BtnAccount/Text", type = "Text"},
    FacebookText = {path = "LoginBg/List/BtnFacebook/Text", type = "Text"},
    GgPlayText = {path = "LoginBg/List/BtnGgPlay/Text", type = "Text"},
    LoginErrorBg = {path = "LoginErrorBg", type = RectTransform},
    succeedText = {path = "LoginErrorBg/succeedText", type = "Text"},
    fitterImageBg = {path = "bg", type = AspectRatioFitter},
    bgLogin = {path = "selectServer", type = "Button"}, --背景登陆按钮点击任意空白处登陆
    DroServers = {path = "selectServer/DroServers", type = "Button"},
    ServersText = {path = "selectServer/DroServers/Label", type = "Text"},
    ClickImg = {path = "selectServer/Click", type = SpriteSwitcher},
    gameLogo = {path = "bg/gameLogo", type = "RawImage"},
    bgRawImage = {path = "bg", type = "RawImage"},
    heroImage = {path = "bg/heroImage", type = "RawImage"},
    BtnList = {path = "LoginBg", type = RectTransform},
    Rstf_loading = {path = "loading", type = RectTransform},
    ButtonAccount = {path = "ButtonAccount", type = "Button"}, --自动登陆情况下在选区时出现
    -- pingCheck = {path = "",type = PingCheck},
    facebookBtn = {path = "LoginErrorBg/Button", type = "Button"},
    closeErrBtn = {path = "LoginErrorBg/Mask", type = "Button"},
    LoginNetError = {path = "LoginNetError", type = RectTransform},
    netFacebookBtn = {path = "LoginNetError/Button", type = "Button"},
    netQQBtn = {path = "LoginNetError/QQButton", type = "Button"},
    netCloseErrBtn = {path = "LoginNetError/Mask", type = "Button"},
    netReconnectBtn = {path = "LoginNetError/btnReconnect", type = "Button"},
    AgeBtn = {path = "ButtonAge", type = "Button"},
    -- 登录失败重试按钮
    ButtonRelogin = {path = "ButtonRelogin", type = "Button"},
    TextRelogin = {path = "ButtonRelogin/Text", type = "Text"},
    loginingTip = {path = "loginingTip", type = RectTransform}
}

function GetShowBuindPronmpt()
    return isShowBIndPrompt
end

function SetShowBuindPronmpt(isShow)
    isShowBIndPrompt = isShow
end

function LoginByType(loginType)
    log.LoginWarning("开始登录，登录方式：", loginType)
    event.RecodeTrigger("start_login_sdk", {loginType = loginType})
    if loginType == login_pb.enLoginPartnerID_Google then
        GgPlayProxy_Login()
    elseif loginType == login_pb.enLoginPartnerID_Facebook then
        Facebook_Login()
    elseif loginType == login_pb.enLoginPartnerID_GameCenter then
        GameCenter_Login()
    elseif loginType == login_pb.enLoginPartnerID_Apple then
        Apple_Login()
    elseif loginType == login_pb.enLoginPartnerID_HuaWei then
        HuaWei_Login()
    elseif loginType == login_pb.enLoginPartnerID_Twitter then
        Twitter_Login()
    else
        Q1Visitor_Login()
    end
end

function SetIsSwitching(value)
    isSwitching = value
end

function LoginMain:Init()
    self.ButtonRelogin.gameObject:SetActive(false)
    self.loginingTip.gameObject:SetActive(false)

    self.ButtonAccount.gameObject:SetActive(not ReviewingUtil.IsReviewing())

    util.RegisterConsole(
        "Port:801",
        0,
        function(s)
        end
    )
    util.RegisterConsole(
        "Port:802",
        0,
        function(s)
        end
    )

    local ui_window_mgr = require("ui_window_mgr")
    ui_window_mgr:EnableCheckNoWindow()

    net_gateway_module.SetPort801()
    net_gateway_module.SetPort802()
    net_gateway_module.SetTestIP()
    --是否使用最新区服？（e.g.本地未找到区服信息）
    local localWorldid = PlayerPrefs.GetInt("worldid")
    if userID_temp then
        local localRegionid, id = GetWorldidOfUser(userID_temp)
        localWorldid = id
    end

    log.LoginWarning("读取本地 worldid:", localWorldid)
    PropertySetWorldId(localWorldid)

    -- 这个提前下
    if q1sdk.IsEnable() and not SDKInited then
        SDKInited = true
        if game_config.ENABLE_Q1_DEBUG_MODE then
            q1sdk.SetLogcat(true)
        end
    --q1sdk.GetUUID()
    end
    if not pingCheck then
        pingCheck = GameObject.Find("/Engine"):GetComponent(typeof(PingCheck))
    else
        pingCheck:Dispose()
    end
    if util.IsWebSocket() and pingCheck then
        pingCheck:Dispose()
    end

    --国服去掉协议
    self.BtnUserTerms.gameObject:SetActive(util.ShouldUseCustomSDKUI())

    util.RegisterConsole(
        "TestCG",
        0,
        function(s)
            local userID = PlayerPrefs.GetString("userID", "")
            local key = "CutsceneShown" .. userID
            PlayerPrefs.SetInt(key, 0)
        end
    )

    --选择登录打点
    --local Adjust = require "adjust"
    --Adjust.TrackEvent(Adjust.Event_SelectLogin)

    --国服包修改内容
    if game_config.Q1SDK_DOMESTIC then
        self.succeedText.text = lang.Get(1680)
    end

    isShowBIndPrompt = true

    local isAndroid = Application.platform == RuntimePlatform.Android
    local isIsInEditor = IsInEditor()
    ----     --print(" >>>isAndroid, IsInEditor", isAndroid, isIsInEditor)

    --界面显示内容初始化
    loginView:Init(isIsInEditor)

    self.ServersText.text = ""
    if worldid == 0 then
        local recommendWorldid, recommendServerInfo, recommendServerCount = setting_server_data.GetNewestWorldInfo()
        if recommendServerCount == 0 then
            event.RecodeTrigger("empty_recommend_server", {})
        end
        local recommendServerName = ""
        if recommendServerInfo ~= nil then
            recommendServerName = recommendServerInfo.name
        end
        if recommendWorldid ~= 0 then
            self.ServersText.text = recommendServerName
            log.LoginWarning("设置推荐服务器 worldid:", recommendWorldid)
            PropertySetWorldId(recommendWorldid)
        else
            log.LoginError("本地未配置 worldid 情况下，未能从推荐服务器列表找到满足条件的服务器")
            event.RecodeTrigger("can_not_find_recommend_server", {})
        end
    end

    -- 新增，用于设置worldid，目前使用到的无法满足当前需求
    if newWorldid then
        log.LoginWarning("切换服务器 worldid:", newWorldid)
        PropertySetWorldId(newWorldid)
    end

    local serverInfo = GetServerInfo(worldid)
    if serverInfo then
        self.ServersText.text = serverInfo.name --上次登陆小区名称或者新区名(一区二区 或 5566)
        log.LoginWarning("根据选择的服务器切换配置 worldid:", serverInfo.id)
        PropertySetWorldId(serverInfo.id)
        -- IsWebSocket下不开启ping
        if not util.IsWebSocket() and pingCheck then
            pingCheck:Active(serverInfo.ipList[1])
        end
        isInMaintain = serverInfo.isrepaired

        log.LoginWarning("LoginMain:Init() serverinfo_isrepaired ", serverInfo.isrepaired)






    end

    local selLang = tonumber(ui_setting_data.GetAttrData(EnSubModel.En_Model_Base, EnBaseAttrKey.En_AttrKey_Lang))

    if selLang == 1 then
        --self.gameLogo:Switch(0)
        self.ClickImg:Switch(0)
    else
        --self.gameLogo:Switch(1)
        self.ClickImg:Switch(1)
    end

    --@region 登陆相关按钮事件
    self.onBtnVisitorEditorClicked = function()
        account_data.SetFristVisitor(0)
        local lastLoginType = account_data.GetLoginType()
        if lastLoginType ~= -1 then
            -- 上报游客登陆
            --local ev = AdjustEvent("eba1lg")
            --Adjust.trackEvent(ev)
            -- 进入preLoding
            --window:EnterLoginLoading()
            --game.EnterState(game.STATE_VISITOR_LOADING)
            --game.EnterState(game.STATE_ACCOUNT_LOGIN)

            local tips = lang.Get(lang.KEY_VISITOR_LOGIN_TIPS) or "Creating account, please hold on..."
            --self.VisitorLoginMsg.text = tips
            --self.VisitorLoginBg.gameObject:SetActive(true)
            --activity_indicator.Show("Login")
            -- --print("记录的账号："..PlayerPrefs.GetString("NewAccount"))
            if PlayerPrefs.GetString("NewAccount") ~= nil then
                AutoLogin.Show()
            else
                AutoLogin.ShowBg()
            end

            net_gateway_module.ConnectServer(
                function(errCode)
                    if errCode == 0 then
                        local visitorAccount = account_data.GetVisitorAccount()
                        --print("VisitorAccount: ", visitorAccount)
                        net_login_module.SetLoginContext(visitorAccount, "", 5, "")

                        local net_prop_module = require "net_prop_module"
                        net_login_module.SetLoginWorldID(net_prop_module.WorldID)

                        account_data.SetLoginType(5)
                        if PlayerPrefs.GetString("NewAccount") ~= nil then
                            AutoLogin.ShowSucceed()
                        end

                        --游客登录打点
                        --local Adjust = require "adjust"
                        --Adjust.TrackEvent(Adjust.Event_Login)

                        PlayerPrefs.SetString("NewAccount", "1") --设置空的账号 用来判断该游客是否创建角色
                    else
                        --activity_indicator.Hide("Login")
                        --上报登录失败原因
                        --local loginFailedEvent = AdjustEvent("hxamib")
                        --loginFailedEvent:addCallbackParameter("Visitor","Visitor error code"..errCode)
                        --Adjust.trackEvent(loginFailedEvent)
                        --window.VisitorLoginBg.gameObject:SetActive(false)
                    end
                end
            )
        else
            --print("无任何账号")
            --self.CreateVisitor.gameObject:SetActive(true)
        end
    end
    self.BtnVisitorEditor.onClick:AddListener(self.onBtnVisitorEditorClicked)

    self.onBtnAccountEditorClicked = function()
        game.EnterState(game.STATE_ACCOUNT_LOGIN)
        account_data.SetLoginType(-1)
    end
    self.BtnAccountEditor.onClick:AddListener(self.onBtnAccountEditorClicked)

    self.onBtnLogin = function()
        -- event.RecodeTrigger("before_login", {login_mark = "onBtnLogin231013"})			--登陆前数数打点
        -- if not hasInitDataLuaInterface then
        --     --print("hasInitDataLuaInterface=",hasInitDataLuaInterface)
        -- 	return
        -- end
        event.RecodeTrigger("before_login", {login_mark = "hasInitDataLuaInterface231013"}) --登陆前数数打点
        if loginPending then
            --print("loginPending=",loginPending)
            return
        end
        event.RecodeTrigger("before_login", {login_mark = "loginPending231013"}) --登陆前数数打点
        LoginPending(true)
        --connectingServer = false
        log.LoginWarning("开始登录游戏")
        -- util.StartWatch("Login", "点击登录开始登录到大厅显示")
        -- util.EnableCanvasTouch(false, self.UIRoot, "uiloginmain", "onLoginEvent")
        -- util.DelayCall(
        --     20,
        --     function()
        --         if window ~= nil then
        --             util.EnableCanvasTouch(true, window.UIRoot, "uiloginmain", "onLoginEvent")
        --         end
        --     end
        -- )

        event.RecodeTrigger("on_click_login_sdk", {})

        if util.ShouldUseCustomSDKUI() then
            AutoLogin.Show()
        else
            if Application.platform == RuntimePlatform.Android then
                --国内安卓登录界面隐藏转圈和loading条
                local ui_login_progress = require "ui_login_progress"
                ui_login_progress:HideProgress()
                AutoLogin.Hide()
            end
        end

        --"版本类型 0国内版本 1国外版本"
        if game_config.VERSION_TYPE == 0 then
            if PlayerPrefs.GetInt("Q1Login") == 1 then
                if util.ShouldUseCustomSDKUI() then
                    local ui_account_login = require "ui_account_login"
                    local username = PlayerPrefs.GetString(ui_account_login.PrefKey, "")
                    local password = ""
                    local prePwd = PlayerPrefs.GetString(ui_account_login.PrefPwd, "")
                    if string.len(prePwd) > 0 then
                        password = Utility.Decryptor(prePwd)
                    end

                    -- q1sdk.login(username, password, function(errCode, errMsg, token, userID, openInfo)
                    --     PlayerPrefs.SetString(ui_account_login.PrefKey, username)
                    --     PlayerPrefs.SetString(ui_account_login.PrefPwd, Utility.Encryptor(password))
                    --     q1sdk.SetRoleBindType(openInfo)
                    --     Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_BingChuan, openInfo)
                    --     PlayerPrefs.SetInt("Q1Login", 1)
                    -- end)

                    q1sdk.Login(
                        function(errCode, errMsg, token, userID, openInfo)
                            PlayerPrefs.SetString(ui_account_login.PrefKey, username)
                            PlayerPrefs.SetString(ui_account_login.PrefPwd, Utility.Encryptor(password))
                            q1sdk.SetRoleBindType(openInfo)
                            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_BingChuan, openInfo)
                            PlayerPrefs.SetInt("Q1Login", 1)
                        end,
                        "login"
                    )
                else
                    Q1SDK_Login()
                end
            else
                Q1Visitor_Login()
            end
        else
            local adjust = require "adjust"
            local adjustAtt = adjust.GetAttribution()
            local open_test_mgr = require "open_test_mgr"
            open_test_mgr.SetConvertFlag(adjustAtt.clickLabel)
            open_test_mgr.SetSharerID(adjustAtt.clickLabel)

            log.DirectWarning("登录前，取adjust标签: ", adjustAtt.clickLabel or "nil")

            local lastLoginType = PlayerPrefs.GetInt("last_login_type", login_pb.enLoginPartnerID_Visitor)
            local lastUserID = PlayerPrefs.GetString("last_login_userID", "")
            log.LoginWarning("上次登录用户id:", lastUserID, "上次登录类型：", lastLoginType)
            -- 2020/06/30 之后的 Android 或 iOS 版本提供了 getLatestLoginPlatform 或 获取 UserInfo，然后从 UserInfo 获取session，loginType 等数据的方式
            -- 为兼容外网运行中客户端，SessionValid 检测方式需要保留
            -- 联运的包不走session缓存，需要登录
            local build_diff_setting = require "build_diff_setting"
            log.DirectWarning("build_diff_setting.NeedSessionLogin():", build_diff_setting.NeedSessionLogin(), "game_config.CHANNEL_TYPE", game_config.CHANNEL_TYPE)
            if SessionLoginHelp.CanSessionLogin(lastLoginType) and lastUserID ~= "" and build_diff_setting.NeedSessionLogin() then
                ReportSetRegionEvent()
                q1sdk.SessionValid(
                    function(bSuccess, id, msg)
                        event.RecodeTrigger(
                            "login_session_callback",
                            {
                                success = (not (not bSuccess)),
                                session_result = tostring(msg)
                            }
                        )
                        log.LoginWarning("SessionValid:", bSuccess, id, msg)
                        if bSuccess then
                            -- local lastOpenInfo = PlayerPrefs.GetString('last_login_openInfo', '')
                            -- if not string.empty(msg) then
                            --     session = msg
                            --     local currentUser = q1sdk.CurrentUser()
                            --     if currentUser then
                            --         lastUserID = currentUser:CallStatic("getUserId")
                            --         lastOpenInfo = currentUser:CallStatic("getOpenInfo")
                            --         log.LoginWarning("更新用户id:", lastUserID, "更新open：", lastOpenInfo)
                            --     end
                            -- end
                            -- Login_Callback(0, '', session, lastUserID, lastLoginType, lastOpenInfo)
                            local session = PlayerPrefs.GetString("last_login_token", "")
                            if not string.empty(msg) then
                                session = msg
                            end
                            event.RecodeTrigger("start_login_session", {loginType = lastLoginType})

                            Login_Callback(0, nil, session, lastUserID, lastLoginType, PlayerPrefs.GetString("last_login_openInfo", ""))
                        else
                            -- iOS 2020/6/30版本提供了新的 loginWithLatestLoginPlatform 接口，保持逻辑统一，暂不为 iOS 单独实现新逻辑
                            -- 使用本地记录上次登录方式
                            LoginByType(lastLoginType)
                        end
                    end
                )
            else
                --判断是不是从老包转过来的, 是老包转过来的自动转为google登录
                local open_test_mgr = require "open_test_mgr"
                if open_test_mgr.GetConvertFlag() then
                    lastLoginType = login_pb.enLoginPartnerID_Google
                else
                    local adjust = require "adjust"
                    local adjustAtt = adjust.GetAttribution()
                    local open_test_mgr = require "open_test_mgr"
                    open_test_mgr.SetConvertFlag(adjustAtt.clickLabel)
                    log.DirectWarning("open_test_mgr 第二次获取登录标记失败")
                    if open_test_mgr.GetConvertFlag() then
                        log.DirectWarning("open_test_mgr 第二次获取登录标记成功", open_test_mgr.GetConvertFlag())
                        lastLoginType = login_pb.enLoginPartnerID_Google
                    end
                end
                LoginByType(lastLoginType)
            end
        end
    end
    self.bgLogin.onClick:AddIntervalListener(self.onBtnLogin, 0.9)

    self.onAccountEvent = function()
        --选区时可以返回重新输账号密码
        self.ButtonAccount.gameObject:SetActive(false)
        self.BtnList.gameObject:SetActive(true)
        self.bgLogin.gameObject:SetActive(false)
    end
    self.ButtonAccount.onClick:AddListener(self.onAccountEvent)

    self.onOpenServerSelect = function()
        --打开选区界面
        windowMgr:ShowModule("ui_select_servers")
    end
    self.DroServers.onClick:AddListener(self.onOpenServerSelect)

    self.onBtnVisitorClicked = function()
        Q1Visitor_Login()
    end
    self.BtnVisitor.onClick:AddListener(self.onBtnVisitorClicked)

    self.onBtnAccountClicked = function()
        isCallLogin = true
        Q1SDK_Login()
    end
    self.BtnAccount.onClick:AddListener(self.onBtnAccountClicked)

    self.onBtnFacebookClicked = function()
        Facebook_Login()
    end
    self.BtnFacebook.onClick:AddListener(self.onBtnFacebookClicked)

    self.onBtnGgPlay = function()
        GgPlayProxy_Login()
    end
    self.BtnGgPlay.onClick:AddListener(self.onBtnGgPlay)

    self.onBtnTitleClick = function()
        local curTime = Time.time

        --时间到了，但是次数没到，则清除记录
        if curTime - LastClickTick >= ResetTitleClickLimit then
            if ClickCount < ClickTitleRepeatCount then
                LastClickTick = curTime
                ClickCount = 0
            end
        end

        --次数够了，则弹出授权窗口
        ClickCount = ClickCount + 1
        if ClickCount > ClickTitleRepeatCount then
            local windowMgr = require "ui_window_mgr"
            windowMgr:ShowModule("ui_login_code")
            LastClickTick = curTime
            ClickCount = 0
        end
    end
    self.BtnTitle.onClick:AddListener(self.onBtnTitleClick)

    --没有维护公告则不显示按钮
    -- self.BtnNotice.gameObject:SetActive(false)
    -- local notice_data = require "notice_data"
    -- notice_data.GetMaintainData(function (maintainData)
    --     if window and self:IsValid() and maintainData~=nil then
    --         self.BtnNotice.gameObject:SetActive(true)
    --     end
    -- end)

    --self.onBtnNotice = function()
    --     local ui_setting_cfg = require "ui_setting_cfg"
    --     if ui_setting_cfg.EVERSION_TYPE.DOMESTIC ~= game_config.VERSION_TYPE then
    --          --print("Utility.GetSystemLanguage_Android()>>>>>>>",Utility.GetSystemLanguage_Android())
    --     end
    -- local windowMgr = require "ui_window_mgr"
    -- windowMgr:ShowModule("ui_inotice")
    --window.noticeFlag.gameObject:SetActive(false)

    --     local serverID = 0 --到时候可以用服务器id
    --     serverID = serverID or 0
    --     if nil ~= tNoticeData then
    --         local xmlVersion = tostring(tNoticeData["version"])
    --         --时间戳
    --         local timeStamp = os.time()
    --         local szCurTime = os.date("%Y%m%d", timeStamp)
    --         PlayerPrefs.SetString("NoticeVersion", xmlVersion)
    --         PlayerPrefs.SetString("NoticeTime", szCurTime)
    --         local szPrefsVersionKey = string.format("NoticeVersion_%d", serverID)
    --         local szPrefsNoticeTimeKey = string.format("NoticeTime_%d", serverID)
    --         PlayerPrefs.SetString(szPrefsVersionKey, xmlVersion)
    --         PlayerPrefs.SetString(szPrefsNoticeTimeKey, szCurTime)
    --         bHasOpenNotice = true
    --     end
    --end
    --self.BtnNotice.onClick:AddListener(self.onBtnNotice)

    self.onBtnSelectLanguage = function()
        local windowMgr = require "ui_window_mgr"
        windowMgr:ShowModule("ui_iselect_language")
    end
    self.BtnSelectLanguage.onClick:AddListener(self.onBtnSelectLanguage)

    self.onBtnUserTerms = function()
        local windowMgr = require "ui_window_mgr"
        windowMgr:ShowModule("ui_iuser_terms")
    end
    self.BtnUserTerms.onClick:AddListener(self.onBtnUserTerms)

    self.fitterImageBg.aspectRatio = screen_util.width / screen_util.height

    -- 上报进入登陆界面
    --local ev = AdjustEvent("dmwm6x")
    --Adjust.trackEvent(ev)

    --account_data.SetShowBind(true)
    if not isIsInEditor then
        --local ui_iuser_terms = require "ui_iuser_terms"
        --local bIsExit = ui_iuser_terms.IsConfirmUserTerm()
        bAutoLoading = false
        if bAutoLoading then --and  bIsExit~=0 then
            bAutoLoading = false

            local lastLoginType = account_data.GetLoginType()
            ------ --print("lastloginType"..lastLoginType)

            if lastLoginType == login_pb.enLoginPartnerID_Visitor then
                self.onBtnVisitorClicked()
            elseif lastLoginType == login_pb.enLoginPartnerID_BingChuan then
                self.onBtnAccountClicked()
            elseif lastLoginType == login_pb.enLoginPartnerID_Facebook then
                self.onBtnFacebookClicked()
            elseif lastLoginType == login_pb.enLoginPartnerID_Google then
                self.onBtnGgPlayClicked()
            end
        end
    end

    self.languageSettingChanged = function()
        -- 国际化
        self:Loacalization()
    end
    event.Register(event.LANGUAGE_SETTING_CHANGED, self.languageSettingChanged)

    self.loginErrorHandle = function(_, errCode, timeStamp)
        if errCode ~= 0 then
            local error_code_pb = require "error_code_pb"
            if errCode == error_code_pb.enErr_Login_ServerMaintenance then
                --self.timeStamp = timeStamp
                --log.LoginWarning("收到服务器维护通知")
                event.RecodeTrigger("login_server_maintenance", {})

                isInMaintain = true
                self:ShowServerMaintenance(true)
                windowMgr:UnloadModule("ui_auto_login_impl")
            end
        else
            isInMaintain = false
            self:ShowServerMaintenance(false)
            if game_config.Q1SDK_DOMESTIC then
                q1sdk.trackUserLogin(userID_temp)
            end
        end
    end
    event.Register(event.LOGIN_RESPONSE_ERROR, self.loginErrorHandle)
    self.autoLoginCloseHandle = function(_)
        loginView:AutoLoginCloseHandle()
    end
    event.Register(event.AUTOLOGIN_CLOSED, self.autoLoginCloseHandle)

    -- 国际化
    self:Loacalization()

    --播放登陆音乐
    self:PlayLoginMusic()

    self.FacebookBtnEvent = function()
        local url_mgr = require "url_mgr"
        local url = url_mgr.FACEBOOK_ROOT_URL
        q1sdk.ApplicationOpenURL(url)
    end
    self.facebookBtn.onClick:AddListener(self.FacebookBtnEvent)

    self.AgeBtnEvent = function()
        local ui_help = require "ui_help"
        ui_help.ShowWithDate(144)
    end
    self.AgeBtn.onClick:AddListener(self.AgeBtnEvent)

    -- self.CloseErrEvent= function()
    --     AutoLogin.Close()
    --     self:ShowServerMaintenance(false)
    --     LoginPending(false)
    ------      --print("=======关闭LoginErrorBg==========",loginPending)
    --     if ui_login_main_mgr.IsAutoLogin() --[[ and (not connectingServer) ]] then
    --         loginView:AutoLoginWithDefaultAccount()
    --     end
    -- end
    -- self.closeErrBtn.onClick:AddListener(self.CloseErrEvent)
end

function LoginMain:OnShow()
    self.__base:OnShow()

    --预实例化主界面和主场景界面
    event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT, {mark = "start_ui_lobby"})

    local ui_window_mgr = require("ui_window_mgr")

    local battlePlayer = require "battle_player"
    battlePlayer.LoadMainPlayer()

    event.RecodeTrigger("login_main_show", {})

    util.EnableCanvasTouch(true, self.UIRoot, "uiloginmain", nil)

    local uiUpdateNodeParent = windowMgr.canvasMeshT or GameObject.Find("/UIRoot/CanvasWithMesh")
    if uiUpdateNodeParent then
        local uiUpdateNode = uiUpdateNodeParent.transform:Find("driver_UIUpdateNode/UIUpdate(Clone)")
        if uiUpdateNode then
            uiUpdateNode.gameObject:SetActive(true)
        end
    end
    hasLobbyShow = false

    event.Register(event.BATTLE_START, OnCloseUI)

    local channel_tag = util.GetChannelTag()
    if channel_tag == const.package_name_set.com_q1_hero then
        self.gameLogo.texture = Resources.Load("Logo/LOGOx-hero", typeof(Texture2D))
        self.gameLogo.gameObject:SetActive(true)
    else
        self.gameLogo.gameObject:SetActive(false)
    end

    -- 渠道包 隐藏logo
    if game_config.ENABLE_Q1SDK_CHANNEL then
        self.gameLogo.gameObject:SetActive(false)
    end

    --使用初始化流则不需要关闭
    local update_config_mgr = require "update_config_mgr"
    local init_flow = update_config_mgr.IsUseInitFlow()
    if not init_flow then
        --ClearStartupCanvas()
    end

    util.StopWatch("init")
    util.DelayCall(0.1, CheckStartUpCanvas)

    loginView:OnShow()
    -- SetPreloadResourceStatus("loginmain", true)

    require "Hero"

    local controller_cutscene = require "controller_cutscene"
    controller_cutscene.PrepareMenuCG()

    self.ButtonReloginEvent = function()
        if game_config.ENABLE_Q1SDK_CHANNEL then
            q1sdk.Logout()
        end

        self:onBtnLogin()
        self.ButtonRelogin.interactable = false
        self.loginingTip.gameObject:SetActive(true)
    end
    self.ButtonRelogin.onClick:AddListener(self.ButtonReloginEvent)

    --OnPropressChange(1)
end

function LoginMain:LoadBackgroundScene()
    if game_config.CHANNEL_ID ~= 2118002 and game_config.CHANNEL_ID ~= 2118003 then
        --3d背景
        -- local seed=tostring(os.time()):reverse():sub(1, math.random(3,6))
        -- math.randomseed(seed)
        -- local randomNum=math.random(0, game_scheme:InitBattleProp_0(164).szParam.count-1 )
        -- local modelId = game_scheme:InitBattleProp_0(164).szParam.data[randomNum]
        -- local cfg_model = game_scheme:Modul_0(modelId)
        ------ --print("LoginMain:OnShow path:"..cfg_model.modelPath)
        --local starttime = os.clock();

        util.PeekWatch("init", "开始加载英雄")
        if not self.model then
            local assetBundleName = "art/battleplayer/newuilayscene.prefab"
            local res = "animations/characters/72_guang/edit_72_guang.prefab"
            local model_item = require "model_item"
            self.model =
                model_item.CModelItem():Init(
                assetBundleName,
                {res},
                function(_rt)
                    if self and self:IsValid() then
                        self.heroImage.texture = _rt
                        self.model:SetMaskScale(1, {x = 1, y = 1.45})
                        self.model:SetMaskActive(1, 0)

                        if loadedFlag then
                            event.Trigger(event.GAME_EVENT_REPORT, "login_load_end", '{"since_start_time":' .. Time.time .. ',"consume":' .. (Time.time - consumeStart) .. "}")
                            loadedFlag = false
                        else
                            loadedFlag = true
                        end
                    end
                end
            )
        end
    end
end

function LoginMain:PlayLoginMusic()
    local music_contorller = require "music_contorller"
    music_contorller.ChangeSceneMusic(music_contorller.ID_LOGIN)
end

function LoginMain:Loacalization()
    loginView:Loacalization()
end

-- 直接请求公告，有就显示
function LoginMain:TryShowNotice()
    local notice_data = require "notice_data"
    local serverData = GetServerInfo(worldid)
    if not serverData then
        log.DirectWarning("notice---找不到服务器信息：", worldid)
        return
    end

    local areaID = serverData.areaID
    log.DirectWarning("notice---大区ID：", areaID)
    notice_data.TryShowNotice(
        areaID,
        function()
            AutoLogin.Close()
            LoginPending(false)
            if ui_login_main_mgr.IsAutoLogin() then
                print("停服过程，返回到登录界面时，重新拉下serverinfo")
                local server_data = require "server_data"
                server_data.ReqData()
                loginView:DelayAutoLogin(0.25)
            end
        end,
        isInMaintain
    )
end

function LoginMain:ShowNotice()
    --///登录界面不再显示新闻公告，此方法作废
    --loginView:ShowNotice()
end

function LoginMain:HasInitDataLuaInterface()
    return true -- hasInitDataLuaInterface
end

--重置状态
function LoginMain:ResetState()
    --隐藏游客登录提示
    --[[if self.CreateVisitor and self.CreateVisitor.gameObject then
        self.CreateVisitor.gameObject:SetActive(false)
    end]]
    --隐藏创建游客登录提示
    --[[if self.VisitorLoginBg and self.VisitorLoginBg.gameObject then
        self.VisitorLoginBg.gameObject:SetActive(false)
    end]]
    AutoLogin.Close()
end

function GetAccountName()
    return AccountNameSave
end
function SetAccountName(name)
    AccountNameSave = name
end

function LoginMain:EnterLoginLoading()
    -- 屏蔽Loading界面
    --local windowMgr = require "ui_window_mgr"
    --windowMgr:ShowModule("ui_login_loading")
end

function LoginMain:ShowServerMaintenance(bShow)
    log.DirectWarning("notice---ShowServerMaintenance", bShow)
    if bShow then
        self:TryShowNotice()
    end
end

function LoginMain:Close()
    if self:IsValid() then
        event.RecodeTrigger("login_main_close", {since_start_time = Time.realtimeSinceStartup})

        if self.onBtnVisitorEditorClicked ~= nil then
            self.BtnVisitorEditor.onClick:RemoveListener(self.onBtnVisitorEditorClicked)
        end
        if self.onBtnAccountEditorClicked ~= nil then
            self.BtnAccountEditor.onClick:RemoveListener(self.onBtnAccountEditorClicked)
        end
        if self.onBtnVisitorClicked ~= nil then
            self.BtnVisitor.onClick:RemoveListener(self.onBtnVisitorClicked)
        end
        if self.onBtnAccountClicked ~= nil then
            self.BtnAccount.onClick:RemoveListener(self.onBtnAccountClicked)
        end
        if self.onBtnFacebookClicked ~= nil then
            self.BtnFacebook.onClick:RemoveListener(self.onBtnFacebookClicked)
        end
        if self.onBtnGgPlay ~= nil then
            self.BtnGgPlay.onClick:RemoveListener(self.onBtnGgPlay)
        end
        -- if self.onBtnNotice ~= nil then
        --     self.BtnNotice.onClick:RemoveListener(self.onBtnNotice)
        -- end
        if self.onBtnSelectLanguage ~= nil then
            self.BtnSelectLanguage.onClick:RemoveListener(self.onBtnSelectLanguage)
        end
        if self.onBtnUserTerms ~= nil then
            self.BtnUserTerms.onClick:RemoveListener(self.onBtnUserTerms)
        end

        --[[if self.onBtnQuickGameClicked ~= nil then
            self.BtnQuickGame.onClick:RemoveListener(self.onBtnQuickGameClicked)
        end

        if self.onBtnAccountLoginClicked ~= nil then
            self.BtnAccountLogin.onClick:RemoveListener(self.onBtnAccountLoginClicked)
        end]]
        if self.onBtnTitleClick then
            self.BtnTitle.onClick:RemoveListener(self.onBtnTitleClick)
            self.onBtnTitleClick = nil
        end

        if self.onBtnLogin ~= nil then
            self.bgLogin.onClick:RemoveIntervalListener(self.onBtnLogin)
            self.onBtnLogin = nil
        end

        if self.onAccountEvent ~= nil then
            self.ButtonAccount.onClick:RemoveListener(self.onAccountEvent)
            self.onAccountEvent = nil
        end

        if self.onOpenServerSelect ~= nil then
            self.DroServers.onClick:RemoveListener(self.onOpenServerSelect)
            self.onOpenServerSelect = nil
        end

        if self.languageSettingChanged then
            event.Unregister(event.LANGUAGE_SETTING_CHANGED, self.languageSettingChanged)
        end

        if self.autoLoginCloseHandle then
            event.Unregister(event.AUTOLOGIN_CLOSED, self.autoLoginCloseHandle)
        end

        if self.loginErrorHandle then
            event.Unregister(event.LOGIN_RESPONSE_ERROR, self.loginErrorHandle)
            self.loginErrorHandle = nil
        end

        if game_config.CHANNEL_ID ~= 2118002 and game_config.CHANNEL_ID ~= 2118003 then
            if self.model then
                self.model:Dispose()
                self.model = nil
            end
        end

        if self.FacebookBtnEvent ~= nil then
            self.facebookBtn.onClick:RemoveListener(self.FacebookBtnEvent)
            self.FacebookBtnEvent = nil
        end

        if self.AgeBtnEvent ~= nil then
            self.AgeBtn.onClick:RemoveListener(self.AgeBtnEvent)
            self.AgeBtnEvent = nil
        end

        -- if self.CloseErrEvent ~= nil then
        -- 	self.closeErrBtn.onClick:RemoveListener(self.CloseErrEvent)
        -- 	--self.CloseErrEvent = nil
        -- end

        if self.ButtonRelogin then
            self.ButtonRelogin.onClick:RemoveListener(self.ButtonReloginEvent)
            self.ButtonRelogin = nil
        end
    end

    errCode_temp = nil
    errMsg_temp = nil
    -- 不清空 换服会用缓存
    --token_temp = nil
    --不清空userID_temp，用于数据本地保存
    --userID_temp = nil
    --loginType_temp = nil
    openInfo_temp = nil

    newWorldid = nil
    isInMaintain = false
    loginFailedTimes = 0
    if loginTimer then
        loginTimer:Dispose()
        loginTimer = nil
    end

    if self.checkCloseTicker then
        util.RemoveDelayCall(self.checkCloseTicker)
        self.checkCloseTicker = nil
    end

    loginView:Close()

    self.__base:Close()
    CheckStartUpCanvas()
    --util.DelayCall(0.2,CheckStartUpCanvas)

    windowMgr:UnloadModule("ui_inotice")
end

function LoginPending(bPending)
    loginPending = bPending
    if loginPending == false and window and window.UIRoot and (not window.UIRoot:IsNull()) then
        util.EnableCanvasTouch(true, window.UIRoot, "uiloginmain", "onLoginEvent")
    end
end

local function OnLogout(eventname, bAutoLogin)
    local lastLoginType = account_data.GetLoginType()
    bAutoLoading = bAutoLogin
    if lastLoginType < 0 then
        return
    end

    --此函数由event.ACCOUNT_LOGOUT触发，此时只有net_login_module.ReturnLogin触发，且已经调用过Logout/LogoutLogic
    --此类状态变更由触发源net_login_module负责管理更合适
    --q1sdk.LogoutLogic()
    --q1sdk.Logout()

    if window ~= nil then
        window:ResetState()
    end

    local isIsInEditor = IsInEditor()
    if not isIsInEditor then
        isCallLogin = true
        if game_config.VERSION_TYPE == 0 then
            Q1SDK_Login()
        else
            isLogout = true
        end
    end
end

--上报区ID---------------------
local curRegionID = nil -- 当前上报的区ID
-- jenkins,sdk 采用如下配置，下标从 0 开始
local regionTable = {
    [1] = 0, -- 0 中国中心（默认）
    [2] = 1, -- 1 欧美     ea
    [3] = 2 -- 2 东南亚    sa
    -- 3 预演服 review
}

--[[
ios
typedef enum{
    region_cn = 0,        //国内
    region_ea = 1,        //欧美
    region_sa = 2,        //东南亚
    region_review = 3,    //预演
    region_debug = 4,     //debug,内网
} SDK_Region;
]]
function ReportSetRegionEvent()
    -- local game_config = require "game_config"
    -- -- 这个特别处理下
    --如果渠道id为位面2国内版，则不设置sdk的Environment，使用渠道包设置的Environment
    if game_config.CHANNEL_ID == 2124001 then
        return
    end

    local loginRegionID = setting_server_data.GetRegionID(worldid)
    -- loginRegionID(游戏中的区id)，需要映射到上报事件定义的区id
    -- 游戏中大区，如服务器列表中的大区配置，下标从 1 开始，需要映射到 sdk 使用的从 0 开始的配置上
    local convertID = regionTable[loginRegionID] or 0
    if game_config.GAME_GATEWAY_GROUP == 1 then
        -- 预演服
        convertID = 3
    end
    if not curRegionID or curRegionID ~= loginRegionID then
        -- 上报区id
        curRegionID = convertID
        q1sdk.SetRegion(curRegionID)
    end
end

-- 只有联运包才会计算登录超时
function CheckLoginTimeout()
    if loginTimer then
        loginTimer:Dispose()
        loginTimer = nil
    end
    loginTimer = base_object()
    local timeout = game_scheme:InitBattleProp_0(968).szParam.data[0]
    loginTimer:CreateTimeTicker(
        0,
        function()
            local last = os.time()
            while true do
                local now = os.time()
                local pass = now - last
                if hasLoginCallback == true then
                    break
                end

                if pass >= timeout then
                    -- 超时 需要重新登录
                    OnChannelLoginError()
                    break
                end
                coroutine.yield(1)
            end
            if loginTimer then
                loginTimer:Dispose()
                loginTimer = nil
            end
        end
    )
end

--登陆接口相关---------------------
function Q1Visitor_Login()
    log.LoginWarning("开始游客登录")
    ReportSetRegionEvent()
    isCallLogin = true
    --q1sdk.Logout()
    -- 切换账号回到登录界面时不要自动登录，走sdk登录框,不要主动登录
    if isSwitching then
        isSwitching = false
        event.Trigger(event.GAME_EVENT_REPORT, "sdk_is_switching")
        log.LoginWarning("sdk正在切换账号")
        return
    end

    --"国内还是国外版冰川sdk"
    if game_config.Q1SDK_DOMESTIC then
        --国内登录调用
        --2021-11-29 修改 这里如果是换区的话，不需要sdk登录，换区消息中会直接服务器连接
        local isSwitchServer = net_login_module.GetSwitchServer()
        if isSwitchServer and userID_temp and token_temp then
            net_login_module.SetSwitchServer(false)
            q1sdk.SetRoleBindType(openInfo_temp)
            log.DirectWarning("Q1Visitor_Login:token_temp:", token_temp)
            Login_Callback(errCode_temp, errMsg_temp, token_temp, userID_temp, login_pb.enLoginPartnerID_BingChuan, openInfo_temp)
            if q1sdk.Visitor() then
                PlayerPrefs.SetInt("Q1Login", 0)
            end
        else
            hasLoginCallback = false
            if game_config.ENABLE_Q1SDK_CHANNEL then
                CheckLoginTimeout()
            end
            q1sdk.Login(
                function(errCode, errMsg, token, userID, openInfo)
                    hasLoginCallback = true
                    q1sdk.SetRoleBindType(openInfo)
                    if q1sdk.Visitor() then
                        PlayerPrefs.SetInt("Q1Login", 0)
                    end
                    -- 异常情况处理，联运包存在小概率sdk登录框带入游戏内的处理
                    if game_config.ENABLE_Q1SDK_CHANNEL then
                        if not windowMgr:IsModuleShown("ui_login_main") then
                            Q1SDK_SwitchAccount()
                            return
                        end
                    end
                    Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_BingChuan, openInfo)
                end,
                "login"
            )
        end
    else
        q1sdk.Login(
            function(errCode, errMsg, token, userID, openInfo)
                q1sdk.SetRoleBindType(openInfo)
                local partnerID = login_pb.enLoginPartnerID_Visitor
                Login_Callback(errCode, errMsg, token, userID, partnerID, openInfo)

                if q1sdk.Visitor() then
                    util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Visitor)
                    PlayerPrefs.SetString('last_login_token', token)
                    PlayerPrefs.SetString('last_login_userID', userID)
                    PlayerPrefs.SetString('last_login_openInfo', openInfo)
                end
            end,
            "guestLogin"
        )
    end
end

-- (success, errorCode, msg, jsonStr)
function Q1SDK_LoginBind(callback)
    net_login_module.ACCOUNT_ISBIND = 1

    if game_config.Q1SDK_DOMESTIC then
        local ui_create_phone_account = require "ui_create_phone_account"
        local ui_account_login = require "ui_account_login"

        ui_create_phone_account.Set(
            function(op, account, pwd, needBind)
                -- 这样SDK那边才会判断不是游客了
                q1sdk.Logout()

                q1sdk.Login(
                    function(errCode, errMsg, token, userID, openInfo)
                        q1sdk.SetRoleBindType(openInfo)
                        callback(true, 0, "", string.format('{"user":%d}', userID_temp))
                    end,
                    "login"
                )

                PlayerPrefs.SetInt("Q1Login", 1)
            end
        )

        windowMgr:ShowModule("ui_create_phone_account")
    end
end

function Q1SDK_Login()
    ReportSetRegionEvent()
    --isCallLogin = true
    --q1sdk.Logout()
    net_login_module.ACCOUNT_ISBIND = 0
    if game_config.Q1SDK_DOMESTIC and (not util.ShouldUseCustomSDKUI()) then
        local login_pb = require "login_pb"
        q1sdk.Login(
            function(errCode, errMsg, token, userID, openInfo)
                q1sdk.SetRoleBindType(openInfo)
                Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_BingChuan, openInfo)
            end,
            "login"
        )
        return
    end

    if game_config.Q1SDK_DOMESTIC then
        local ui_account_login = require "ui_account_login"
        ui_account_login.Set(
            function(op, account, pwd, needBind)
                local net_login_module = require "net_login_module"
                local login_pb = require "login_pb"

                q1sdk.Login(
                    function(errCode, errMsg, token, userID, openInfo)
                        q1sdk.SetRoleBindType(openInfo)
                        PlayerPrefs.SetString(ui_account_login.PrefKey, account)
                        PlayerPrefs.SetString(ui_account_login.PrefPwd, Utility.Encryptor(pwd))

                        Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_BingChuan, openInfo)
                        PlayerPrefs.SetInt("Q1Login", 1)
                        if errCode == 0 then
                            ui_account_login.Close()
                        elseif errCode == 2 then
                            flow_text.Add(lang.Get(1632))
                        end
                    end,
                    "login"
                )
            end
        )

        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_account_login")
    else
        Debug.Log("Q1SDK_Login foreign")
    end
end

function IsVisible()
    local ui_window_mgr = require "ui_window_mgr"
    local bActive = (window ~= nil and window:IsVisible()) or ui_window_mgr:IsModuleShown("ui_login")
    return bActive
end

---返回登录界面
function ReturnLogin()
    local ui_window_mgr = require "ui_window_mgr"
    local bActive = IsVisible()
    if bActive then
        return
    end

    event.Trigger(event.RETURN_LOGIN)
    event.Trigger(event.SCENE_DESTROY)
    local login_module = require "net_login_module"
    login_module.ReturnLogin(false)
end

function Facebook_SwitchAccount()
    ----     --print("Facebook_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch FaceBook Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_Facebook)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Facebook)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "facebookLogin"
    )
end

function GooglePlay_SwitchAccount()
    ----     --print("GgPlay_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch GgPlay Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_Google)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Google)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "googleLogin"
    )
end

function Email_SwitchAccount(errCode, errMsg, token, userID, loginType, openInfo)
    local email_subscribe_mgr = require "email_subscribe_mgr"
    email_subscribe_mgr.Log("ui_loginMain Email_SwitchAccount",errCode, errMsg, token, userID, loginType, openInfo)
    isCallLogin = false
    local emailCallBack = function(errCode, errMsg, token, userID, openInfo)
        if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
            log.Error("switch GgPlay Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
            return
        end
        q1sdk.SetRoleBindType(openInfo)

        event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, loginType)

        util.SaveLocalIntData("last_login_type", loginType)
        PlayerPrefs.SetString("last_login_token", token)
        PlayerPrefs.SetString("last_login_userID", userID)
        PlayerPrefs.SetString("last_login_openInfo", openInfo)
        ReturnLogin()
    end
    emailCallBack(errCode, errMsg, token, userID, openInfo)
end

function Twitter_SwitchAccount()
    ----    print("GgPlay_SwitchAccount >>>")

    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch Twitter Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_Twitter)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Twitter)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "twitterLogin",
        true
    )
end

function Amazon_SwitchAccount()
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch GgPlay Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_Amazon)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Amazon)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "amazonLogin"
    )
end

function GameCenter_SwitchAccount()
    ----     --print("GameCenter_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch GameCenter Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_GameCenter)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_GameCenter)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "gamecenterLogin"
    )
end

function Apple_SwitchAccount()
    ----     --print("Apple_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch Apple Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_Apple)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Apple)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "appleLogin"
    )
end

function HuaWei_SwitchAccount()
    --print("HuaWei_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch HuaWei Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, login_pb.enLoginPartnerID_HuaWei)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_HuaWei)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "hwLogin"
    )
end

function Visitor_SwitchAccount()
    ----     --print("Visitor_SwitchAccount >>>")
    isCallLogin = false
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                log.Error("switch Visitor Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                --local msg = string.format(fmt, errMsg)
                --flow_text.Add(msg)
                return
            end
            q1sdk.SetRoleBindType(openInfo)

            local loginType = login_pb.enLoginPartnerID_Visitor
            if not q1sdk.Visitor() then
                loginType = login_pb.enLoginPartnerID_BingChuan
            end
            event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, loginType)

            util.SaveLocalIntData("last_login_type", loginType)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)

            ReturnLogin()
        end,
        "guestLogin"
    )
end

-- sdk等三方渠道 切换账号通用接口
function Channel_SwitchAccount()
    isCallLogin = false
    log.DirectWarning("use Channel_SwitchAccount:")
    local callback = nil
    if const.IsVietnamIosChannel() then
        q1sdk.ChannelBindAccount()
    else
        callback = function(errCode, errMsg, token, userID, openInfo, isNew)
            log.DirectWarning("三方渠道切换账号成功回调", errCode, errMsg, token, userID, openInfo)
            q1sdk.SetRoleBindType(openInfo)
            local partnerID = login_pb.enLoginPartnerID_Visitor
            Login_Callback(errCode, errMsg, token, userID, partnerID, openInfo)

            if q1sdk.Visitor() then
                util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Visitor)
            end
        end
        q1sdk.ChannelSwitchAccount(callback)
    end
end

function Channel_GuestLogin()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo, isNew)
            isNewUser = isNew
            q1sdk.SetRoleBindType(openInfo)
            local partnerID = login_pb.enLoginPartnerID_Visitor
            Login_Callback(errCode, errMsg, token, userID, partnerID, openInfo)

            if q1sdk.Visitor() then
                util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Visitor)
            end
        end,
        "guestLogin"
    )
end
-- 国内版切换账号
function Q1SDK_SwitchAccount()
    if not game_config.Q1SDK_DOMESTIC or util.ShouldUseCustomSDKUI() then
        return
    end

    isCallLogin = false
    -- 如果是渠道包，直接登出，等到登录界面再弹登录
    if game_config.ENABLE_Q1SDK_CHANNEL then
        ReturnLogin()
        q1sdk.Logout()
    else
        q1sdk.SwitchAccount(
            function(errCode, errMsg, token, userID, openInfo)
                if errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
                    log.Error("switch DOMESTIC Failed,errCode:" .. tostring(errCode) .. ",errMsg:" .. tostring(errMsg) .. ",token:" .. tostring(token) .. ",userID:" .. tostring(userID))
                    --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
                    --local msg = string.format(fmt, errMsg)
                    --flow_text.Add(msg)
                    return
                end
                q1sdk.SetRoleBindType(openInfo)

                local loginType = login_pb.enLoginPartnerID_BingChuan
                event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, loginType)

                util.SaveLocalIntData("last_login_type", loginType)
                PlayerPrefs.SetString("last_login_token", token)
                PlayerPrefs.SetString("last_login_userID", userID)
                PlayerPrefs.SetString("last_login_openInfo", openInfo)

                ReturnLogin()
            end,
            "switchAccount"
        )
    end
end

function Facebook_Login()
    ReportSetRegionEvent()
    ----     --print("Facebook_Login >>>")
    isCallLogin = true
    --[[if game_config.ENABLE_Q1_DEBUG_MODE then
        ------ --print("开启冰川内网验证模式")
        q1sdk.SetLogcat(true)
	end]]
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_Facebook, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Facebook)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "facebookLogin"
    )
end

function GgPlayProxy_Login()
    ReportSetRegionEvent()
    ----     --print("GgPlayProxy_Login >>>")
    isCallLogin = true
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_Google, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Google)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "googleLogin"
    )
end

function GameCenter_Login()
    ReportSetRegionEvent()
    ----     --print("GameCenter_Login >>>")
    isCallLogin = true
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_GameCenter, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_GameCenter)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "gamecenterLogin"
    )
end

function Apple_Login()
    ReportSetRegionEvent()
    ----     --print("Apple_Login >>>")
    isCallLogin = true
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_Apple, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Apple)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "appleLogin"
    )
end
function Twitter_Login()
    ReportSetRegionEvent()
    ----    print("Twitter_Login >>>")
    isCallLogin = true
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_Twitter, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_Twitter)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "twitterLogin"
    )
end

function HuaWei_Login()
    ReportSetRegionEvent()
    ----     --print("HuaWei_Login >>>")
    isCallLogin = true
    --q1sdk.Logout()
    q1sdk.Login(
        function(errCode, errMsg, token, userID, openInfo)
            Login_Callback(errCode, errMsg, token, userID, login_pb.enLoginPartnerID_HuaWei, openInfo)
            q1sdk.SetRoleBindType(openInfo)

            util.SaveLocalIntData("last_login_type", login_pb.enLoginPartnerID_HuaWei)
            PlayerPrefs.SetString("last_login_token", token)
            PlayerPrefs.SetString("last_login_userID", userID)
            PlayerPrefs.SetString("last_login_openInfo", openInfo)
        end,
        "hwLogin"
    )
end

function View_Login_Callback(errCode)
    if window and window.UIRoot and not window.UIRoot:IsNull() then
        loginView:Login_Callback(errCode)
    end
end

function OnLoginError(msg)
    LoginPending(false)
end

function OnChannelLoginError()
    LoginPending(false)
    log.DirectWarning("登录超时：")
    if window and window.ButtonRelogin then
        if window.ButtonRelogin.gameObject.activeSelf == false then
            window.ButtonRelogin.gameObject:SetActive(true)
        end
        window.ButtonRelogin.interactable = true
        window.loginingTip.gameObject:SetActive(false)
    end
end

--[[显示选区按钮,弹出公告]]
function Login_Callback(errCode, errMsg, token, userID, loginType, openInfo)
    util.SetUseBlockAll(true)
    --login_preload_mgr.PreloadAssetAfterLogin()

    util.PeekWatch("Login", "SDK登录成功")
    log.LoginWarning("SDK登录回调 Login_Callback", "errCode:", errCode, "errMsg:", errMsg, "uid:", userID, "loginType:", loginType)
    PlayerPrefs.SetString("current_token", token)
    event.RecodeTrigger(
        "login_callback",
        {
            loginType = tonumber(loginType),
            errMsg = tostring(errMsg)
        }
    )

    --判断登录是否成功
    if errCode == Q1_LoginErrorCode.Cancel or errCode == 1 then
        LoginPending(false)
        View_Login_Callback(errCode)
        log.LoginWarning("SDK登录取消")
        return
    elseif errCode == Q1_LoginErrorCode.Error or errCode == 2 then
        log.LoginError("SDK登录失败, error=Q1SdkLoginError", errCode)

        --账号或密码错误
        --local fmt = lang.Get(lang.KEY_LOGIN_FAILED) or "Login Failed:%s"
        --local msg = string.format(fmt, errMsg)
        --flow_text.Add(msg)
        -- 客户端打点上报
        q1sdk.UserEvent(0, 0, "", 0, "", "userLoginError", "error=Q1SDKLoginError")
        setting_server_data.ReportSelectServerReport("user_loginerror")

        if game_config.ENABLE_Q1SDK_CHANNEL then
            OnChannelLoginError()
        else
            if util.ShouldUseCustomSDKUI() then
                if q1sdk.Q1SDK_DOMESTIC then
                    local ui_window_mgr = require "ui_window_mgr"
                    ui_window_mgr:ShowModule("ui_account_login")
                end
            end

            LoginPending(false)
            View_Login_Callback(errCode)
        end
        return
    end

    if window and window.ButtonRelogin then
        window.ButtonRelogin.gameObject:SetActive(false)
        window.loginingTip.gameObject:SetActive(false)
    end

    View_Login_Callback(errCode)

    errCode_temp = errCode
    errMsg_temp = errMsg
    token_temp = token
    userID_temp = userID
    loginType_temp = loginType
    openInfo_temp = openInfo

    q1sdk.SetRoleBindType(openInfo)

    Continue_Login()
end

function Continue_Login()
    --选完区后登陆
    isCallLogin = true
    Login_Event(errCode_temp, errMsg_temp or "", token_temp, userID_temp, loginType_temp, openInfo_temp or "")
end

function GetLastWorldUrl()
    local regionid = setting_server_data.GetRegionID(worldid)
    if regionid == 0 then
        local worldId = GetNewestWorldId()
        PropertySetWorldId(worldId)
        regionid = setting_server_data.GetRegionID(worldId)
        log.LoginError(
                "GetLastWorldUrl can not find region, maybe overwrite install by different channel app,或者服务器列表为空导致的找不到区服和大区, use newest worldid:",
                worldid, "regionid:", regionid)
    end
    local url = url_mgr.LAST_WORLD_URL[regionid]
    if url ~= nil then
        return game_config.ENABLE_Q1_DEBUG_MODE and url[1] or url[2]
    else
        return ""
    end
end

--获取最终链接
function GetNewLastUrl(url,userID)
    local key = game_config.ENABLE_Q1_DEBUG_MODE and "abcdabcd" or "6AtmjFpMs4C0uQg80Vn8cvNJbX8rt2AL"
    local gameID = const.GAMEID
    local gameVersion = const.GetGameVersion()
    local channelID = game_config.CHANNEL_ID or 1
    local channelUserID = game_config.ENABLE_Q1SDK_CHANNEL and net_login_module.GetU8SDKUserID() or userID
    local sdkUserName =  game_config.ENABLE_Q1SDK_CHANNEL and net_login_module.GetU8UserName() or userID
    local orgSign = string.format("%s%s%s%s%s%s", gameID, gameVersion, channelID, channelUserID, sdkUserName, key)
    local sign = Utility.Md5(orgSign)
    local params =
    {
        "gameId="..gameID,
        "gameVersion="..gameVersion,
        "channelId="..channelID,
        "channelUserId="..channelUserID,
        "sdkUserName="..sdkUserName,
        "sign="..string.lower(sign),
    }
    local newUrl = url..table.concat( params, "&")
    return newUrl
end

function GetNewestWorldId()
    return setting_server_data.GetNewestWorldInfo()
end

--[[与服务器沟通]]
function Login_Event(errCode, errMsg, token, userID, loginType, openInfo)
    ----     --print("与服务器沟通Login_Callback >>>errCode=", errCode, "errMsg=",errMsg,"token=", token,"userID=", userID,"loginType=", loginType,"openInfo=", openInfo)
    if window == nil then
        LoginPending(false)
        return
    end

    if nil ~= openInfo then
    ----         --print("Login_Event openInfo:"..openInfo)
    end

    local json = require "dkjson"
    local jsonData = json.decode(openInfo) or {}
    print("根据usertypelist 判断是否是游客")
    if jsonData["usertypelist"] == "" or jsonData["usertypelist"] == "0" then
        print("usertypelist   游客")
        q1sdk.SetVisitorState(true)
    else
        q1sdk.SetVisitorState(false)
    end

    --按发行需求，游客账号登录后，不论绑定何方第三方账号，都不修改登录类型，总通过游客方式进行登录
    if not q1sdk.Visitor() and loginType == login_pb.enLoginPartnerID_Visitor and string.len(openInfo) > 0 then
        local json = require "dkjson"
        local openInfoData = json.decode(openInfo)
        local usertype = openInfoData["usertype"]
        if usertype == 0 then --游客
            --loginType = q1sdk.Visitor() and login_pb.enLoginPartnerID_Visitor or login_pb.enLoginPartnerID_BingChuan
            log.DirectWarning("bindType: BingChuan")
        elseif usertype == 1 then --1 FaceBook
            --loginType = login_pb.enLoginPartnerID_Facebook
            log.DirectWarning("bindType: Facebook")
        elseif usertype == 2 then --2 Google
            --loginType = login_pb.enLoginPartnerID_Google
            log.DirectWarning("bindType: Google")
        elseif usertype == 3 then --MobGame,安卓平台
            log.DirectWarning("unsupported bindType: MobGame")
        elseif usertype == 4 then --4 GameCenter
            --loginType = login_pb.enLoginPartnerID_GameCenter
            log.DirectWarning("bindType: GameCenter")
        elseif usertype == 7 then --7 Apple
            log.DirectWarning("bindType: Apple")
        elseif usertype == 8 then -- 华为
            log.DirectWarning("bindType: HuaWei")
        else
            log.Error("unknown loginType:" .. tostring(usertype))
        end
        q1sdk.SetRoleBindType(openInfo)
    end
    log.DirectWarning("is guest >>" .. tostring(q1sdk.Visitor()) .. ",loginType:" .. loginType)

    event.Trigger(event.ON_LOGIN_CALLBACK, isCallLogin, token, userID, loginType)
    --如果不是通过登陆而进入的回调，则不需要走登陆流程
    if not isCallLogin then
        LoginPending(false)
        return
    end
    --window.VisitorLoginBg.gameObject:SetActive(true)
    isCallLogin = false

    AccountNameSave = userID
    StrTokenSave = token

    local url = GetLastWorldUrl()
    requestRecordInfo.requestCount = 1

    local newUrl = GetNewLastUrl(url,userID)
    RequestRecord_Timeout(newUrl, 10, token, userID, loginType, RecordResponseHandler)
end

function RequestRecord_Timeout(url, timeout, token, userID, loginType, responseHandler)
    -- 如果本地有缓存，直接登录上次的区服，如果没有再去请求区服id
    local localRegionid, localWorldid = GetWorldidOfUser(userID)
    if localWorldid and localWorldid ~= 0 then
        log.LoginWarning("使用本地缓存的worldid登录:", localWorldid, ",localRegionid:", localRegionid, ",userID:", userID)
        -- 如果是切换账号，取消重新设置worldid
        local localUserId = PlayerPrefs.GetString("userID")
        if localUserId ~= userID then
            PropertySetWorldId(localWorldid)
        end

        responseHandler(url, "", token, userID, loginType)
        return
    end
    local requestRecordFailed = function(requestUrl, error)
        local maxIntervalTimes = #requestRecordInfo.intervalTime
        if requestRecordInfo.requestCount >= maxIntervalTimes then
            --不做提示
            --flow_text.Add("Error:"..error)
            log.LoginWarning("最近登录记录无效，超过最大次数:", maxIntervalTimes, ",显示连续异常 url:", url, ",error:", error)
            event.RecodeTrigger("request_record_timeout", {url = requestUrl})

            LoginPending(false)
            loginView:ShowNetError()
            requestRecordInfo.requestCount = 0
        else
            log.LoginWarning("最近登录记录无效，重新请求 url:", requestUrl, ",error:", error)
            local intervalTime = requestRecordInfo.intervalTime[requestRecordInfo.requestCount + 1]
            util.DelayCall(
                intervalTime,
                function()
                    requestRecordInfo.requestCount = requestRecordInfo.requestCount + 1
                    RequestRecord_Timeout(requestUrl, timeout, token, userID, loginType, responseHandler)
                end
            )
        end
    end
    local requestHandle = function(record, error)
        util.PeekWatch("Login", "获取登录记录成功")
        log.LoginWarning("获取登录记录结果 >>>>> ", "record:", record, "error:", error)
        if (record ~= nil and #record == 0 or record == nil) and error ~= nil and #error > 0 then
            requestRecordFailed(url, error)
        else
            if record == "" and ui_login_main_mgr.IsAutoLogin() then
                requestRecordFailed(url, error)
            else
                responseHandler(url, record, token, userID, loginType)
            end
        end
    end
    log.LoginWarning("开始获取最近登录记录", "url:", url)

    if string.empty(url) then
        log.LoginError("最近登录记录 url 为空")
        util.DelayCall(
            2,
            function()
                local newUrl = ""
                if game_config.CHANNEL_TAG == "com.wmzz.q1" then
                    -- 位面1暂时还原
                    newUrl = string.format(url, userID)
                else
                    newUrl = string.format(url, game_config.CHANNEL_ID or 1, userID, userID)
                    if game_config.ENABLE_Q1SDK_CHANNEL then
                        newUrl = string.format(url, game_config.CHANNEL_ID, net_login_module.GetU8SDKUserID(), net_login_module.GetU8UserName())
                        log.DirectWarning("u8 last world:", net_login_module.GetU8SDKUserID(), net_login_module.GetU8UserName(), game_config.CHANNEL_ID)
                    end
                end

                requestRecordFailed(newUrl)
            end
        )
    else
        http_inst.Req_Timeout(url, timeout, requestHandle)
    end
end

function CG_BACK_LOGIN_FLOW()
    if save_sdk_callback_args then
        RecordResponseHandler(unpack(save_sdk_callback_args))
    else
        Continue_Login()
    end
end

function RecordResponseHandler(url, record, token, userID, loginType)
    save_sdk_callback_args = nil
    local json = require "dkjson"
    local data = json.decode(record) or {}
    log.LoginWarning("最近登录记录获取成功")
    event.RecodeTrigger("request_record_success", {})

    AutoLogin.Show()
    if window and window.ButtonAccount then
        window.ButtonAccount.gameObject:SetActive(false)
    end

    if worldid == nil or worldid == 0 then
        local localRegionid, localWorldid = GetWorldidOfUser(userID)
        PropertySetWorldId(localWorldid) --防止世界id为空情况出现
    end
    if ui_login_main_mgr.IsAutoLogin() then
        local localUserId = PlayerPrefs.GetString("userID")
        local localWorldRegion = PlayerPrefs.GetInt("worldRegion")
        local worldRegion = setting_server_data.GetRegionID(worldid)
        log.LoginWarning("local userId:" .. localUserId .. ",local worldRegion:" .. localWorldRegion .. ",userID:" .. tostring(userID) .. ",worldRegion:" .. worldRegion)
        -- 测试默认开启
        if PlayerPrefs.GetInt("TestCG", 0) == 1 then
            local controller_cutscene = require "controller_cutscene"
            controller_cutscene.CheckEnterNewPlayerCutscene()
        end
        if (localUserId ~= userID or localWorldRegion ~= worldRegion) and data.message and string.find( data.message, "Specified user is not found!", 1) 
            and newWorldid == nil then
            if not files_version_mgr.IsUseWebUrlLogin() then
                --新账号，服务器未保存最近一次区服
                local newestWorldId = GetNewestWorldId()
                PropertySetWorldId(newestWorldId)
                log.LoginWarning("用户/大区变更，为新用户且未指定切换区服，使用最新区 worldid：", newestWorldId, "Specified user is not found!")
            end
            
            local controller_cutscene = require "controller_cutscene"
            controller_cutscene.CheckEnterNewPlayerCutscene()

        elseif (localUserId ~= userID or localWorldRegion ~= worldRegion) and data.worldId and newWorldid == nil then
            --如果自动登录，且获得上次登录区服，且不是同账号换区服
            --加上只有账号不一致时才使用服务器数据，是防止服务器时间异常更新，导致区域异常切换（内网容易出现，经常修改服务器时间
            if not files_version_mgr.IsUseWebUrlLogin() then
                local serverData = GetServerInfo(data.worldId)
                if serverData ~= nil then
                    PropertySetWorldId(data.worldId)
                    log.LoginWarning("本地与后台配置不一致，使用后台记录", "use last worldid:", worldid, ",localUserId：", tostring(localUserId), 
                        ",userID:",tostring(userID))
                else
                    log.LoginError("未能找到服务器配置", " worldid:", data.worldId, ",use worldid:", tostring(worldid), ",local worldRegion:", localWorldRegion,
                        ",worldRegion:", worldRegion, ",server list has been changed")
                end
            else
                PropertySetWorldId(data.worldId)
                log.LoginWarning("本地与后台配置不一致，使用后台记录", "use last worldid:", worldid, ",localUserId：", tostring(localUserId), 
                    ",userID:",tostring(userID))
            end
        end
        local serverData = GetServerInfo(worldid)
        --同大区安装包覆盖安装或服务器列表，区服worldid被修改等情况导致找不到服务器。使用最近登录区服
        if nil == serverData and data.worldId then
            if not files_version_mgr.IsUseWebUrlLogin() then
                log.LoginWarning("未能找到服务器配置，优先设定后台记录区服")
                serverData = GetServerInfo(data.worldId)
                if serverData then
                    PropertySetWorldId(data.worldId)
                    log.LoginWarning("使用后台记录区服， use last worldid:"..worldid)
                end
            else
                log.LoginWarning("IsUseWebUrlLogin is true.")
            end
        end
        --如果本地记录与最后登录区服都找不到服务器信息，使用当前服务器列表中最新服
        if nil == serverData then
            if not files_version_mgr.IsUseWebUrlLogin() then
                local newestWorldId = GetNewestWorldId()
                PropertySetWorldId(newestWorldId)
                log.LoginWarning("未能找到后台记录区服，使用最新区服。 use newest worldid:" .. worldid)
                serverData = GetServerInfo(worldid)
            end
        end
        if serverData then
            -- Todo 维护状态使用webUrl登录会有问题
            isInMaintain = serverData.isrepaired
            --打印这个serverData
            for k, v in pairs(serverData) do
                log.LoginWarning("RecordResponseHandler serverData." .. k .. ":" .. tostring(v))
            end
            log.LoginWarning("RecordResponseHandler serverinfo_isrepaired:", serverData.isrepaired)
        end
    end

    log.LoginWarning("RecordResponseHandler url:" .. tostring(url) , ",userID:" .. tostring(userID) .. ",worldid:" .. worldid .. ",maintain:" .. tostring(isInMaintain))
    ------ --print("window.req:RequestRecord_Timeout>>>>>>>>>>>>>url=",url,"userID=", userID,"worldid=",worldid,"isInMaintain=",isInMaintain)
    local isIsInEditor = IsInEditor()
    if window then
        window:ShowServerMaintenance(isInMaintain == true and not isIsInEditor)
    end
    if isInMaintain == true then
        LoginPending(false)
    --登录与资源预加载并发，未收到资源加载完成消息前，不能隐藏进度条，屏蔽如下代码
    --windowMgr:UnloadModule("ui_auto_login_impl")

    --自动登录允许进入游戏，屏蔽以下代码
    --if ui_login_main_mgr.IsAutoLogin() then
    --    return
    --end
    end

    --connectingServer = true
    util.PeekWatch("Login", "开始连接服务器")
    net_gateway_module.ConnectServer(function(errCode)
            -- 这里直接使用worldid可能会有问题，webUrl登录检验异常要以服务器下发的wordlid为准
            if files_version_mgr.IsUseWebUrlLogin() then
                local regionID = 0
                regionID, worldid = GetWorldidOfUser(userID)
            end
            
            log.LoginWarning("连接服务返回", "net_gateway_module.ConnectServer isInMaintain:", isInMaintain, ",errCode:", errCode, ",worldid:", worldid)
            --connectingServer = false,握手成功才能回来，但服务器真实维护中，根本不会收到握手消息
            --window.VisitorLoginBg.gameObject:SetActive(false)
            if errCode == 0 then
                util.PeekWatch("Login", "连接服务器成功")
                log.LoginWarning("连接服务器成功")
                event.Trigger(event.RECORD_LOGIN_COST_TIME_POINT,"CostTime_连接服务器成功")
                event.RecodeTrigger("connect_server_sucess", {})

                net_login_module.SetLoginContext(userID, "g", loginType, token)
                net_login_module.SetLoginWorldID(worldid)
                --account_data.SetLoginToken(token)
                account_data.SetLoginType(loginType)
                util.SaveLocalIntData("last_login_type", loginType)
                local idAuthRes = tonumber(q1sdk.GetIdAuth())
                account_data.SetIdAuth(idAuthRes > 0)
                ------ --print("SetLoginType >>>",loginType)
                --记录所有登录信息
                setting_server_data.SetLoginWorldID(worldid)
                setting_server_data.SetConnectType(setting_server_data.GetConnectType())
                SaveWorldId(worldid)
                if isInMaintain == false then
                    AutoLogin.ShowSucceed()
                end
            else
                LoginPending(false)
                AutoLogin.Close()

                --local ui_login_progress = require "ui_login_progress"
                --ui_login_progress:Close()

                if ui_login_main_mgr.IsAutoLogin() then
                    log.LoginError("连接服务器失败，延时尝试")
                    local server_data = require "server_data"
                    server_data.ReqData()
                    log.LoginError("刷新一次server info")

                    --已有服务器维护中弹窗提示，不做自动重连
                    if windowMgr.IsModuleShown("ui_notice_impl") then
                        log.LoginWarning("已有ui_notice_impl 10秒重连")
                        loginView:DelayAutoLogin(10)
                    else
                        loginView:DelayAutoLogin(0.5)
                    end
                end
            end
        end,
        worldid
    )

    --Facebook登录打点
    --local Adjust = require "adjust"
    --Adjust.TrackEvent(Adjust.Event_Login)

    -- q1sdk.UserEvent(0, 0, "", 0, userID or "", "SDKLogin", "ver=Q1SDKLogin")
    local json_str =
        json.encode(
        {
            pid = game_config.CHANNEL_ID,
            uuid = q1sdk.GetUUID(),
            imei_idfa = q1sdk.GetImeiMD5(),
            radid = q1sdk.GetRadid(),
            rsid = q1sdk.GetRsid()
        }
    )
    event.Trigger(event.GAME_EVENT_REPORT, "sdk_login", json_str)
end

--登陆接口相关end---------------------

local CLoginMain = class(ui_base, nil, LoginMain)

function Show()
    ----     --print("ui_login_main:show")
    if window ~= nil and window.isShow then
        return window
    end
    util.PeekWatch("init", "开始加载登录界面")
    if window == nil then
        consumeStart = Time.time

        -- LoginMain.LoadBackgroundScene()

        window = CLoginMain()

        InitLoginView(window)

        event.Register(event.ACCOUNT_LOGOUT, OnLogout)

        local ui_iuser_terms = require "ui_iuser_terms"
        -- ui_iuser_terms.Show_First_Login_Terms(function()
        --     -- 只提示一次
        --     if not noticePassed then
        --         noticePassed = true
        --         window:ShowNotice()
        --     end
        -- end)
        -- PlayerPrefs.SetInt("UserTerms",0) --测试用
        window._NAME = _NAME
        window:LoadUIResource(
            "ui/prefabs/uiloginmain.prefab",
            nil,
            GameObject.Find("/UIRoot/Canvas").transform,
            function()
                ui_iuser_terms.Show_First_Login_Terms(function()      
                    -- 只提示一次
                    if not noticePassed then
                        noticePassed = true
                        window:ShowNotice()
                    end
                end)      

                --耗时打点
                local reportMsg = {
                    sinceStartTime = Time.time,
                    consume = Time.time - consumeStart
                    -- system_memory_size = util.GetSystemMemorySize(),
                }
                event.EventReport("login_ui_loaded", reportMsg)
                event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT, {mark = "login_ui_loaded"})
                -- if loadedFlag then
                --     loadedFlag = false
                -- else
                --     loadedFlag = true
                -- end
            end,
            nil,
            true,
            false
        )
    else
        window:Show()
    end
    --event.Trigger(event.RESET_WAIT_SUB_UI)

    PlayerPrefs.SetInt("LastSelectMatchMode", -1)

    util.PeekWatch("init", "登录界面显示方法调用完成")
    return window
end

function InitLoginView(mainModule)
    local orgView = loginView
    if not ui_login_main_mgr.IsAutoLogin() then
        --国内版本使用手动登录流程
        loginView = ui_login_main_view
        ClearStartupCanvas()
    else
        --国外版本使用全自动登录流程
        loginView = ui_login_main_auto_view
    end
    --如果未初始化或切换了登录方式，则重新初始化进度条表现
    if orgView ~= loginView then
        loginView:InitTips()
    end

    if mainModule then
        loginView:SetModule(mainModule)
        --变化量为0，仅作刷新进度条
        --OnPropressChange(0)
    end

    if Config.IsTrue then -- 添加p_CheckFakeBundle开关
        -- log.Warning("===before set p_CheckFakeBundle:", Config.IsTrue("p_CheckFakeBundle"))
        if Config.SetConfig then
            Config.SetConfig("p_CheckFakeBundle", "true")
        -- log.Warning("===after set p_CheckFakeBundle:", Config.IsTrue("p_CheckFakeBundle"))
        end
    else
        log.DirectWarning("===p_CheckFakeBundle: false")
    end
end

-- function SetPreloadResourceStatus(resName, status)
--     if preloadResStatus[resName] == nil then
--         return
--     end
--     preloadResStatus[resName] = status
--     if status == false then
--         return
--     end

--     local k, v
--     for k, v in pairs(preloadResStatus) do
--         if v == false then
--             return
--         end
--     end
--     --资源准备完成
--     loginView:SetLoadPercentActive(false)
-- end

--函数内只能做资源预加载，不可以执行打开界面等实际操作
-- function PreloadAsset(registResFnc)
--     login_preload_mgr.PreloadAssetBeforeLogin(registResFnc)
-- end

--与逻辑直接相关资源，需要依赖数据，语言，界面等资源加载完成才能进行
-- function PreloadInitResource(registResFnc)

--     require "net_domination_module"

--     local lobbyScript =
--     {
--         'ui_lobby',
--         'ui_iselect_language',
--         'lobbymatch_pb',
--         'hero_entity',
--         'calpro_mgr',
--         'tech_data_Mgr',
--         'tech_pb',
--         'ui_hero_biography_data',
--         'target_task_data',
--         'day7_challenge_data',
--         'signIn_data',
--         'seven_signin_data',
--         'ui_menu_bot',
--         'block_cache',
--         'ui_menu_side',
--         'block_cache',
--         'ashdungeon_pb',
--         'dungeon_mgr',
--         'ui_dungeon',
--         'ui_dungeon_levels',
--         'long_click',
--         'goods_entity',
--         'ui_time_loading',
--         'ui_scene_loading',
--         'ui_menu_top',
--         'ui_explore',
--         'ui_player_detail_info_ex',
--         'ui_boss_battle',
--         'ui_battle_sweep',
--         'ui_task_achievement',
--         'ui_button_tips',
--         'ui_multiplayer_hero_select',
--         'ui_weapon_select',
--         'battle_preview_manager',
--         'activity_tips_mgr',
--         'scene_pointer',
--         'ui_pointing_target',
--         'unforced_guide_mgr',
--         'ui_unlock_guide',
--     }
--     if registResFnc then
--         --registResFnc("preload_script", #lobbyScript)
--         return
--     end

--     if hasInitPreloadResource then
--         return
--     end
--     hasInitPreloadResource = true

--     -- util.DelayCall(0, function ()
--     --     -- local register_net_module = require 'register_net_module'
--     --     -- register_net_module:RequireModule()
--     --     -- local register_init_module = require 'register_init_module'
--     --     -- register_init_module:RequireModule()
--     --     util.StartWatch("preload init resource", "显示资源初始化预加载")
--     --     util.PeekWatch("preload init resource", "注册网络模块完成")

--     --     --创建玩家数据，灵魂链接等消息延迟下发
--     --     -- local msg_pb = require "msg_pb"
--     --     -- net_route.RegisterGlobalPreEvent(msg_pb.MSG_ROGUE_ENTITY_CREATE_NTF, event.UI_LOBBY_SHOW)
--     --     -- net_route.RegisterGlobalPreEvent(msg_pb.MSG_PROP_CREATEENTITY_NTF, event.UI_LOBBY_SHOW)
--     --     -- net_route.RegisterGlobalPreEvent(msg_pb.MSG_UPDATE_SOUL_LINK_MOUDLE_NTF, event.UI_LOBBY_SHOW)

--     --     --在登录之后启动屏幕touch反馈光效
--     --     local screen_touch_effect =require "screen_touch_effect"
--     --     screen_touch_effect.Start()
--     --     util.PeekWatch("preload init resource", "创建点击光效完成")

--     --     -- local scene_mgr = require "scene_mgr"
--     --     -- scene_mgr.InitSprite()
--     --     util.PeekWatch("preload init resource", "初始化公用Asset资源完成")
--     --     -- require "controller_cutscene"

--     --     util.PeekWatch("preload init resource", "开始预加载lua脚本")
--     --     -- local totalResourceCount = #lobbyScript
--     --     -- local loadspeed = 0
--     --     -- for i=1,totalResourceCount do
--     --     --     loadspeed = (loadspeed + 1) % 3
--     --     --     require (lobbyScript[i])
--     --     --     util.PeekWatch("preload asset", lobbyScript[i],"lua脚本预加载完成")
--     --     --     OnPropressChange(1)
--     --     --     if loadspeed == 0 then
--     --     --         coroutine.yield(0)
--     --     --     end
--     --     -- end
--     --     util.PeekWatch("preload init resource", "预加载lua脚本完成")

--     --     -- local net_leagueComp_module = require "net_leagueComp_module"

--     --     util.StopWatch("preload init resource")

--     -- end)
-- end

-- function PreloadAllLuaTable()
--     if hasInitAllLuaDatabase then
--         return
--     end
--     hasInitAllLuaDatabase = true

--     -- util.StartWatch("all lua table", "后台分批加载表数据")
--     -- util.DelayCall(0, function ()
--     --     while true do
--     --        local hasFinished,percent = game_scheme:initializeDataLuaInterface(1)
--     --        if hasFinished then
--     --            break
--     --        end
--     --        coroutine.yield(0)
--     --     end
--     --     util.PeekWatch("all lua table", "后台表数据加载完成")
--     -- end)
-- end

-- function PreloadLuaTable(registResFnc)
--     PreloadAllLuaTable()

--     local preloadTables =
--     {
--         --耗时较多表格
--         GameScheme.FILEID_Hero,
--         GameScheme.FILEID_HeroPro,
--         GameScheme.FILEID_monsterTeam,
--         GameScheme.FILEID_Reward,
--     }
--     if registResFnc then
--         --registResFnc("preload_luatable", #preloadTables)
--         return
--     end

--     if hasInitDataLuaInterface or initingDataLuaInterface then
--         return
--     end

--     initingDataLuaInterface = true

--     local rootObj = windowMgr.canvasMeshT or GameObject.Find("/UIRoot/CanvasWithMesh")
--     util.EnableCanvasTouch(false, rootObj, "uiloginmain", "dataluainterace")

--     util.DelayCall(0,function ()
--         util.StartWatch("export lua table", "导出数据表")
--         -- 由DataCenter完成使用时加载数据表，此处不做统一导出
--         --while true do
--         --    local hasFinished,percent = game_scheme:initializeDataLuaInterface(20)
--         --    if hasFinished then
--         --        break
--         --    end
--         --    -- 导出接口分配[0 - 20]进度
--         --    loginView:SetLoadPercent(0.2 * percent)
--         --    coroutine.yield(0)
--         --end

--         --提前加载耗时表格
--         -- local i = 1
--         -- local dataTableCount = #preloadTables
--         -- for i=1,dataTableCount do
--         --     game_scheme:ExportGameSchemeToLua(preloadTables[i])
--         --     OnPropressChange(1)
--         --     coroutine.yield(0)
--         -- end

--         hasInitDataLuaInterface = true
--         initingDataLuaInterface = false

--         --自动登录模块收到此消息后，判断资源准备完成后，会发起登录流程
--         event.Trigger(event.GAME_DATA_LUA_INTERFACE)

--         event.Trigger(event.MARK_PROGRESS_LOGIN_EVENT, {mark = "lua_table"})
--         coroutine.yield(0)
--         local rootObj = windowMgr.canvasMeshT or GameObject.Find("/UIRoot/CanvasWithMesh")

--         -- local rootObj = GameObject.Find("/UIRoot/CanvasWithMesh")
--         util.EnableCanvasTouch(true, rootObj, "uiloginmain", "dataluainterace")

--         util.PeekWatch("preload asset", "加载耗时数据表完成")
--     end)
-- end

-- function ResetPreloadResource()
--     preloadResTotalCount = 0
--     preloadResLoadedCount = 0
--     preloadResPercent = 0
--     preloadResRecord = {}
-- end

-- function RegisterRreloadResource(resFlag, resCount)
--     if preloadResRecord[resFlag] then
--         return
--     end

--     preloadResRecord[resFlag] = true
--     preloadResTotalCount = preloadResTotalCount + resCount
-- end

--等待登录界面等资源加载，动画
--function OnPropressAnimate()
--    if loginProgressTicker then
--        util.RemoveDelayCall(loginProgressTicker)
--        loginProgressTicker = nil
--    end
--    loginProgressTicker =
--        util.DelayCall(
--        0,
--        function()
--            while preloadResPercent < 0.99 do
--                if loginView then
--                    --进度只到80% ~ 99%
--                    preloadResPercent = preloadResPercent + 0.1
--                    loginView:SetLoadPercent(preloadResPercent)
--                end
--                coroutine.yield(0)
--            end
--        end
--    )
--end
--
----真实资源加载进度
--function OnPropressChange(changedCount)
--    if changedCount > 0 then
--        if preloadResLoadedCount >= preloadResTotalCount then
--            --已加载完成
--            log.Warning("已加载完成，超出限制")
--            return
--        end
--
--        preloadResLoadedCount = preloadResLoadedCount + changedCount
--        if preloadResLoadedCount >= preloadResTotalCount then
--            preloadResLoadedCount = preloadResTotalCount
--
--            --资源加载完成
--            -- SetPreloadResourceStatus("resource", true)
--            OnPropressAnimate()
--            util.StopWatch("preload asset", "预加载登录资源完成")
--        end
--        --[56% ~ 80%],0.56为首包资源检测占用
--        preloadResPercent = 0.56 + 0.24 * preloadResLoadedCount / preloadResTotalCount
--    end
--
--    ------  --print("SetLoadPercent OnPropressChange:"..changedCount..",percent:"..preloadResPercent)
--    if loginView then
--        loginView:SetLoadPercent(preloadResPercent)
--    end
--end

-- function RegisterRreloadAllResource(...)
--     local fncSet = {...}
--     local k, v
--     --注册所有需要导入的资源
--     for k, v in pairs(fncSet) do
--         v(RegisterRreloadResource)
--     end
-- end

-- function StartPreloadAllResource()
--     if hasInitPreloadResource then
--         return
--     end

--     RegisterRreloadAllResource(PreloadAsset, PreloadLuaTable, PreloadInitResource)
--     PreloadAsset()
--     PreloadLuaTable()
--     PreloadInitResource()
-- end

function Hide()
    if not hasLobbyShow then
        return --大厅界面未显示前不关闭
    end
    if window ~= nil and window.isShow then
        event.Unregister(event.ACCOUNT_LOGOUT, OnLogout)
        window:Hide()
        local windowMgr = require "ui_window_mgr"
        windowMgr:HideModule("ui_iselect_language")
        windowMgr:HideModule("ui_iuser_terms")
        --fixed bug,登录成功，ui_login界面关闭，新手引导battle界面出来前再次点击ui_login_main中的登录按钮打开ui_login,ui_login_main被battle关闭，ui_login残留未被关闭
        windowMgr:HideModule("ui_login")
        util.PeekWatch("Login", "隐藏登录界面")
        util.EnableCanvasTouch(true, window.UIRoot, "uiloginmain", nil)
    end
end

function Close()
    util.PeekWatch("Login", "关闭登录界面")
    print("close loginmain",hasLobbyShow)
    if not hasLobbyShow then
        return --大厅界面未显示前不关闭
    end
    LoginPending(false)
    AutoLogin.Close()
    if loginProgressTicker then
        util.RemoveDelayCall(loginProgressTicker)
        loginProgressTicker = nil
    end
    local ui_login_progress = require "ui_login_progress"
    ui_login_progress:Close()

    local files_version_mgr = require "files_version_mgr"
    files_version_mgr.RequestUpdateJson()

    local preload_resources = require "preload_resources"
    preload_resources.DisposeVersionTip()

    if window ~= nil then
        Hide()
        local windowMgr = require "ui_window_mgr"
        windowMgr:UnloadModule("ui_iselect_language")
        windowMgr:UnloadModule("ui_iuser_terms")
        windowMgr:UnloadModule("ui_login")
        window:Close()

        local uiUpdateNode = GameObject.Find("driver_UIUpdateNode/UIUpdate(Clone)")
        if uiUpdateNode then
            uiUpdateNode:SetActive(false)
        end
        -- util.EnableCameraHDR("/UIRoot/CanvasBelowHUD/HUDCamera", false)
        util.EnableCameraHDR("/UIRoot/UICamera", false)
        -- util.EnableCamera("/UIRoot/Canvas/Camera", false)
        window = nil
    end
end

function OnCloseUI()
    print("OnCloseUI")
    hasLobbyShow = true
    if window and window:IsValid() then
        loginView:SetLoadPercent(1)
    end

    local ui_login_main_mgr = require "ui_login_main_mgr"
    ui_login_main_mgr.SetOnceLogined()

    event.Unregister(event.UI_LOBBY_SHOW, OnCloseUI)
    event.Unregister(event.BATTLE_START, OnCloseUI)
    Close()

    local windowMgr = require "ui_window_mgr"
    windowMgr:UnloadModule("ui_login_main")

    --ClearStartupCanvas()

    -- local controller_cutscene = require "controller_cutscene"
    -- controller_cutscene.SaveCGPlayedStatus()
end

function ClearStartupCanvas()
    --清理启动背景
    local obj = GameObject.Find("StartupCanvas")
    if obj then
        obj.transform:Find("Tips"):SetActive(false)
    end
end
local StartupCanvasGO = nil
function CheckStartUpCanvas()
    ------ --print("CheckStartUpCanvas")
    StartupCanvasGO = StartupCanvasGO or GameObject.Find("/StartupCanvas")

    if StartupCanvasGO then
        local ui_window_mgr = require "ui_window_mgr"
        local bActive = IsVisible()
        log.DirectWarning("CheckStartUpCanvas ", bActive)
        ------ print(bActive)
        StartupCanvasGO:SetActive(bActive)
    end
end
function OnNewAccountNTF(msg)
    ------ --print("游客新的账号")
    --print("OnNewAccountNTF(msg) ", msg.accountName)
    account_data.SetVisitorAccount(msg.accountName)
    PlayerPrefs.SetString("NewAccount", "") --设置空的账号 用来判断该游客是否创建角色
    local lastLoginType = account_data.GetLoginType()
    if lastLoginType == login_pb.enLoginPartnerID_Visitor then
        net_login_module.SetLoginContext(msg.accountName, "", login_pb.enLoginPartnerID_Visitor, "")
    end

    --[[if window and window.VisitorLoginBg then
        window.VisitorLoginBg.gameObject:SetActive(false)
        if window.VisitorLoginMsg then
            local tips = string.format(lang.Get(1591), msg.accountName)
            window.VisitorLoginMsg.text = tips
        end
    end]]
end

local MessageTable = {
    {msg_pb.MSG_LOGIN_NEW_ACCOUNT_NTF, OnNewAccountNTF, login_pb.TMSG_LOGIN_NEW_ACCOUNT_NTF}
}

net_route.RegisterMsgHandlers(MessageTable)

function OnStateEnter(curState)
    ------ --print("当前的状态："..curState)
    if curState == game.STATE_LOGIN_TYPE then
        -- Show()

        local ui_window_mgr = require "ui_window_mgr"
        ui_window_mgr:ShowModule("ui_login_main")
    elseif curState == game.STATE_CREATE_ACTOR or curState == game.STATE_MAKE_MATCH_V or curState == game.STATE_NEW_PLAYER_CUTSCENE then
        if not util.IsWebSocket() and pingCheck then
            local pingData = pingCheck:GetPingData()
            local param = '{"min_ping":' .. pingData.minPing .. ',"max_ping":' .. pingData.maxPing .. ',"avg_ping":' .. pingData.avgPing .. "}"
            event.Trigger(event.GAME_EVENT_REPORT, "befor_login_ping", param)
        end
        Close()
    end
end

function GetUserID()
    return AccountNameSave
    --userID_temp
end

function GetBCUserID()
    return userID_temp or ""
end

function PropertySetWorldId(id)
    if id == 0 and worldid ~= nil and worldid ~= 0 then
        log.DirectWarning("PropertySetWorldId id:0,保持原区服不做修改:" .. tostring(worldid))
        return
    else
        log.DirectWarning("PropertySetWorldId id:" .. id)
    end
    worldid = id
    net_gateway_module.SetWebLoginWorldid(worldid)
end
--- 根据worldId，获取服务器列表中对应的服务器信息
function GetServerInfo(worldId)
    local server_data = setting_server_data.GetServerInfo(worldId)
    if server_data == nil then
        log.LoginError("服务器列表为空")
        event.RecodeTrigger("empty_server_list", {})
        return nil
    end
    return server_data
end
--永久保存区服数据
function SaveWorldId(worldId)
    log.Warning("---SaveWorldId id:" .. worldId)
    setting_server_data.SetLoginWorldID(worldId)
    if userID_temp ~= nil then
        PlayerPrefs.SetString("userID", userID_temp)
        local regionid = setting_server_data.GetRegionID()
        PlayerPrefs.SetInt("worldRegion", regionid)
        SaveWorldidOfUser(regionid, userID_temp, worldId)
    end
end
-- 以regionid-userID为唯一标识服，存放对应的账号保存的worldId
function SaveWorldidOfUser(regionid, userID, worldId)
    local str = regionid .. "-" .. worldId
    PlayerPrefs.SetString(userID, str)
end

function GetWorldidOfUser(userID)
    local str = PlayerPrefs.GetString(userID)
    local regionID = 0
    local worldID = 0
    if str and str ~= "" then
        local params = string.split(str, "-")
        regionID = tonumber(params[1])
        worldID = tonumber(params[2])
    end
    return regionID, worldID
end

function SetWorldID(id)
    if window and window:IsValid() then
        PropertySetWorldId(id)
        local serverData = GetServerInfo(id)
        if serverData then
            window.ServersText.text = serverData.name --上次登陆小区名称或者新区名(一区二区 或 5566)
            PropertySetWorldId(serverData.id)
            isInMaintain = serverData.isrepaired
            log.LoginWarning("SetWorldID: serverinfo_isrepaired ", serverData.isrepaired)
            window:ShowServerMaintenance(isInMaintain == true and not isIsInEditor)
        ----             --print("SetWorldID==============",serverData.id,serverData.name,serverData.isrepaired)
        end
    end
end

function GetCurWorldID()
    return worldid
end

--同账号换区服，需要使用指定的区服号
function SetNewWorldID(id)
    log.DirectWarning("SetWorldID:", id)
    newWorldid = id
end

function IsRegionOumei()
    local regionID = setting_server_data.GetRegionID(worldid) or 0
    return regionID == 2
end
