local print = print
local require = require
local os = os
local ipairs = ipairs
local tostring = tostring
local math = math

local log = require "login_log"
local game = require "game"
local net_gateway_module = require "net_gateway_module"
local net_login_module = require "net_login_module"
local event = require "event"
local lang = require "lang"
local net_route = require "net_route"
local AutoLogin = require "ui_iauto_login"
local ui_setting_attr_enum = require "ui_setting_attr_enum"
local game_config = require "game_config"
local setting_server_data = require "setting_server_data"
local util = require "util"
local ui_window_mgr = require "ui_window_mgr"
local ui_login_progress = require "ui_login_progress"
local const = require "const"

local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local GameObject = CS.UnityEngine.GameObject
local Q1_LoginErrorCode = CS.Q1.LoginErrorCode
local RectTransform = CS.UnityEngine.RectTransform

local Utility = CS.War.Script.Utility
local IsInEditor = CS.War.Script.Utility.IsInEditor
local Application = CS.UnityEngine.Application

module("ui_login_main_auto_login_view")

local M = {}
local mod = nil

function M:SetModule(_module)
    mod = _module
    self.isInEditor = IsInEditor()

    event.Unregister(event.USER_TERMS_ACCEPT, OnAcceptUserTerms)
    event.Register(event.USER_TERMS_ACCEPT, OnAcceptUserTerms)
end

function M:Loacalization()
end

function M:Init(isIsInEditor)
    ----     --print("ui_login_main_auto_login_view Init")

    self.loginErrorCount = 0
    self.isInEditor = isIsInEditor
    self:InitConnectProp()
    self:InitConnectListener()
    self:InitServerList()

    --公告
    mod.BtnNotice.gameObject:SetActive(false)
    --协议
    mod.BtnUserTerms.gameObject:SetActive(false)

    mod.bgLogin.gameObject:SetActive(false) --选区列表显示

    mod.BtnVisitorEditor.gameObject:SetActive(false)
    mod.BtnAccountEditor.gameObject:SetActive(false)

    mod.BtnVisitor.gameObject:SetActive(false)
    -- iOS上可用,国内冰川账号登录
    mod.BtnAccount.gameObject:SetActive(false)
    -- 只有安卓
    mod.BtnGgPlay.gameObject:SetActive(false)
    -- iOS上可用
    mod.BtnFacebook.gameObject:SetActive(false)

    --已经自动登录成功隐藏登陆按钮显示选区按钮
    mod.BtnList.gameObject:SetActive(false)
    mod.ButtonAccount.gameObject:SetActive(false)

    self.NetFacebookBtnEvent = function()
        local url_mgr = require "url_mgr"
        local url = url_mgr.FACEBOOK_ROOT_URL
        local q1sdk = require "q1sdk"
        q1sdk.ApplicationOpenURL(url)
    end

    mod.netFacebookBtn.onClick:AddListener(self.NetFacebookBtnEvent)
    mod.netFacebookBtn.gameObject:SetActive((not game_config.Q1SDK_DOMESTIC))
    self.NetQQBtnEvent = function()
        local url_mgr = require "url_mgr"
        local url = url_mgr.QQ_URL
        local q1sdk = require "q1sdk"
        q1sdk.ApplicationOpenURL(url)
    end
    mod.netQQBtn.onClick:AddListener(self.NetQQBtnEvent)
    mod.netQQBtn.gameObject:SetActive(game_config.Q1SDK_DOMESTIC)
    self.NetCloseErrEvent = function()
        AutoLogin.Close()
        mod.LoginNetError.gameObject:SetActive(false)
        --加一帧延时等待 message_box 关闭，防止其它逻辑直接同帧返回登录失败
        util.DelayCall(
            0.5,
            function()
                M:AutoLoginWithDefaultAccount()
            end
        )
    end
    if game_config.Q1SDK_DOMESTIC then
        mod.succeedText.text = lang.Get(1680)
    end
    mod.netCloseErrBtn.onClick:AddListener(self.NetCloseErrEvent)
    mod.netReconnectBtn.onClick:AddListener(self.NetCloseErrEvent)
end

function M:OnShow()
    ----     --print("ui_login_main_auto_login_view OnShow")

    --self:InitTips()

    if mod.HasInitDataLuaInterface() then
        self:AutoLoginWithDefaultAccount()
    end

    event.Register(event.GAME_DATA_LUA_INTERFACE, OnGameDataLuaInterfaceComplete)
    event.Register(event.ACCOUNT_LOGOUT, OnLogout)
end

function M:InitTips()
    if not util.IsObjNull(self.transTips) then
        if not self.transTips.gameObject.activeSelf then
            self:SetLoadPercentActive(true)
        end
        return
    end

    self.loadPercent = -1

    local objStartupCanvas = GameObject.Find("/StartupCanvas")
    if objStartupCanvas == nil then
        return
    end
    self.transTips = objStartupCanvas.transform:Find("Tips")
    if self.transTips then
        self:SetLoadPercentActive(true)
        local txtTips = self.transTips:GetComponent("Text")
        -- 正在载入中…
        txtTips.text = const.LangInitFinish and lang.Get(1652) or "loading.."
    else
        return
    end
    local transTipsContent = self.transTips:Find("content")
    if transTipsContent then
        local progressBg = transTipsContent.transform:Find("progressBg")
        if progressBg then
            self.sliderPercent = progressBg.transform:Find("progressFill"):GetComponent("RectTransform")
        end
        self.txtPercent = transTipsContent.transform:Find("percent"):GetComponent("Text")

        --self:SetLoadPercent(0)
    end
end

function M:SetLoadPercent(percent)
    if self.sliderPercent == nil then
        return
    end

    if percent <= self.loadPercent then
        return
    end

    self.loadPercent = percent
    ui_login_progress:SetPercent(percent)
end

function M:SetLoadPercentActive(bActived)
    if self.transTips then
        self.transTips.gameObject:SetActive(bActived and util.NeedShowIOSLoading())
    end
end

function M:IsEnableAutoLogin()
    local ui_iuser_terms = require "ui_iuser_terms"
    local bConfirmTerm = ui_iuser_terms.IsConfirmUserTerm()
    if bConfirmTerm == 0 then
        --未接受用户协议，不能自动登录
        return false
    end
    return true
end

-- 自动登录方式，不弹出公告界面
function M:ShowNotice()
end

function M:Disconnect()
    ------  --print("auto login connect = false")
    self.connectSyncProp.connect = false
end

function M:Close()
    event.Unregister(event.GAME_DATA_LUA_INTERFACE, OnGameDataLuaInterfaceComplete)
    event.Unregister(event.ACCOUNT_LOGOUT, OnLogout)

    if mod.UIRoot then
        if self.NetFacebookBtnEvent ~= nil then
            mod.netFacebookBtn.onClick:RemoveListener(self.NetFacebookBtnEvent)
            self.NetFacebookBtnEvent = nil
        end
        if self.NetQQBtnEvent ~= nil then
            mod.netQQBtn.onClick:RemoveListener(self.NetQQBtnEvent)
            self.NetQQBtnEvent = nil
        end

        if self.NetCloseErrEvent ~= nil then
            mod.netCloseErrBtn.onClick:RemoveListener(self.NetCloseErrEvent)
            mod.netReconnectBtn.onClick:RemoveListener(self.NetCloseErrEvent)
            self.NetCloseErrEvent = nil
        end
    end

    --self:SetLoadPercentActive(false)
    self.transTips = nil
    self.sliderPercent = nil
    self.txtPercent = nil
    mod = nil
end

function M:AutoLoginWithDefaultAccount(loginReason)
    event.RecodeTrigger(
        "default_auto_login",
        {
            login_reason = tostring(loginReason)
        }
    )

    if mod == nil then
        --print("Auto Login Mode is nil,return")
        return
    end
    event.RecodeTrigger("before_login", {login_mark = "modnil231013"}) --登陆前数数打点
    if not self:IsEnableAutoLogin() then
        return
    end
    event.RecodeTrigger("before_login", {login_mark = "IsEnableAutoLogin231013"}) --登陆前数数打点
    if self.connectSyncProp.connect then
        return
    end
    event.RecodeTrigger("before_login", {login_mark = "connectSyncProp231013"}) --登陆前数数打点
    log.LoginWarning("开始自动登录")

    util.StartWatch("Login", "自动登录开始登录到大厅显示")
    --net_route.SetCachingAllMsg(false)

    if self.isInEditor then
        --self:Login_Callback(2003) --for test
        self:AutoLoginInEditor()
    elseif mod.onBtnLogin then
        mod:onBtnLogin()
    end
end

function M:DelayAutoLogin(delay)
    util.DelayOneCall(
        "AutoLogin",
        function()
            self:AutoLoginWithDefaultAccount()
        end,
        delay
    )
end

function M:AutoLoginCloseHandle()
    mod.ButtonAccount.gameObject:SetActive(false)
end

function M:Login_Callback(errCode)
    --print("auto login view Login_Callback errCode:"..tostring(errCode))
    if mod == nil then
        --可能在其它界面完成SDK登录过程，在游戏内（不登出到登录界面），如切换账号
        return
    end
    mod.ButtonAccount.gameObject:SetActive(false)
    mod.BtnList.gameObject:SetActive(false)

    if errCode ~= nil and errCode ~= Q1_LoginErrorCode.OK and errCode ~= 0 then
        --print("自动登录失败,errCode:"..tostring(errCode))
        self.loginErrorCount = self.loginErrorCount + 1
        --国内版本不显示登录错误提示界面
        if self.loginErrorCount > 3 and util.ShouldUseCustomSDKUI() then
            self:ShowNetError()
            self.loginErrorCount = 0
        else
            if not util.ShouldUseCustomSDKUI() then
                --取消登录后，展示转圈
                AutoLogin.Show()
            end
            util.DelayCall(
                1,
                function()
                    --打开sdk隐藏转圈
                    if not util.ShouldUseCustomSDKUI() then
                        AutoLogin.Hide()
                    end
                    M:AutoLoginWithDefaultAccount()
                end
            )
        end
    else
        if not util.ShouldUseCustomSDKUI() then
            --登录成功，展示转圈
            AutoLogin.Show()
        end
        self.loginErrorCount = 0
    end
end

function M:ShowNetError()
    mod.LoginNetError.gameObject:SetActive(true)
    AutoLogin.Close()
end

function OnGameDataLuaInterfaceComplete()
    M:AutoLoginWithDefaultAccount("lua interface complete")
end

function OnLogout()
    log.Warning("auto login OnLogout")
    --登录失败，e.g.无网络，服务器拒绝等，重新进行登录请求
    M:Disconnect()
    if mod then
        local ui_login_main = require "ui_login_main"
        ui_login_main.LoginPending(false)
        if mod.LoginNetError and not util.IsObjNull(mod.LoginNetError) and mod.LoginNetError.gameObject.activeSelf then
            --已有连续异常弹窗提示，不做自动重连
            return
        end
        if mod.LoginErrorBg and not util.IsObjNull(mod.LoginErrorBg) and mod.LoginErrorBg.gameObject.activeSelf then
            --已有服务器维护中弹窗提示，不做自动重连
            return
        end
 		--已有服务器维护中弹窗提示，不做自动重连
        if ui_window_mgr.IsModuleShown("ui_notice_impl") then
            M:DelayAutoLogin(10)
            return
        end
    end
    M:AutoLoginWithDefaultAccount()
end

function OnAcceptUserTerms()
    ----     --print("OnAcceptUserTerms")
    --接受用户协议后自动登录
    M:AutoLoginWithDefaultAccount("on accept user terms")
end

--/**************************************************************************************************************************************
--* Editor模拟功能

-- 修改游戏状态，进行连接前准备
function M:AutoLoginInEditor()
    --获取缓存的账号名字 --当前自动登录默认获取第一个服
    --local lastServerNameKey = Application.dataPath.."ui_login_lastServer"
    --local lastServerName = PlayerPrefs.GetString(lastServerNameKey, "")
    local accountKey = Application.dataPath.."ui_login_account"
    local  account =PlayerPrefs.GetString(accountKey..tostring(1))   --取第一个
    account = account or ""
    local password = "*"

    if account.Length == 0 then
        --目前版本已不能通过 onBtnVisitorEditorClicked 接口完成登录功能，不兼容处理此问题
        mod.onBtnVisitorEditorClicked()
        return
    end

    net_login_module.SetLoginContext(account, password, 0, "")
    game.EnterState(game.STATE_CONNECT)

    ------  --print("auto login connect = true")
    self.connectSyncProp.connect = true
end

function M:InitConnectProp()
    if self.isInEditor and self.connectSyncProp ~= nil then
        return
    end

    local prop = require "prop"
    self.connectSyncProp = prop.CProp({connect = false})
end

-- local isInMaintain = true
function M:InitConnectListener()
    if self.isInEditor and self.connectSyncListener ~= nil then
        return
    end

    self.curSelectServerIdx = 1

    --prop的监听函数
    self.connectSyncListener = function(connect)
        --  --print("auto login connect =:"..tostring(connect))
        if not connect then
            return
        end

        ------ --print("CONNECT!")

        --从当前区随机选择一个IP地址
        local server_info = require "server_info"
        local address = nil
        math.randomseed(os.time())
        local selectWorldID = nil
        if server_info.servers and self.curSelectServerIdx < #server_info.servers then
            local server = server_info.servers[self.curSelectServerIdx] --curSelectServerIdx + 1 = 3，目前可用于测试登录失败情况
            if #server > 0 then
                local idx = math.random(1, #server)
                address = server[idx]
            end
            selectWorldID = server.worldid
        end

        if address == nil then
            return
        end

        -- PC 端测试服务器维护中功能
        -- mod:ShowServerMaintenance(isInMaintain)
        -- if isInMaintain == true then
        --     isInMaintain = false
        --     local ui_login_main = require "ui_login_main"
        --     ui_login_main:LoginPending(false)
        --     util.DelayOneCall("DelayDisconnect", function ()
        --         -- 不能在 propItem 的消息监听函数里再次修改数据，否则逻辑上容易产生纰漏，导致设置失败
        --         M:Disconnect()
        --     end, 0)
        -- end

        ------ --print("开始连接...")
        local tcp_ip = {[1] = address.ip}
        local tcp_port = {[1] = address.port}
        local udp_ip = {[1] = address.ip}
        local udp_port = {[1] = address.udp_port}
        -- 添加容错的ip到列表中
        local tcpips, tcpPorts, udpips, udpPorts = net_gateway_module.GetConnectConfig(selectWorldID)
        net_gateway_module.UpdateServerList(tcp_ip,tcpips)
        net_gateway_module.UpdateServerList(tcp_port,tcpPorts)
        net_gateway_module.UpdateServerList(udp_ip,udpips)
        net_gateway_module.UpdateServerList(udp_port,udpPorts)
        
        net_gateway_module.ConnectGateway(
            tcp_ip,
            tcp_port,
            udp_ip,
            udp_port,
            function()
                local controller_cutscene = require "controller_cutscene"
                controller_cutscene.CheckEnterNewPlayerCutscene()

                local ui_login_main = require "ui_login_main"
                ui_login_main.LoginPending(false)
                AutoLogin.Show()
                --local ui_login_progress = require "ui_login_progress"
                --ui_login_progress:Close()

                net_login_module.SetLoginWorldID(selectWorldID)

                local setting_server_data = require "setting_server_data"
                setting_server_data.SetLoginWorldID(selectWorldID)
                ui_login_main.SaveWorldId(selectWorldID)
                -- ui_login_main.SetPreloadResourceStatus("login", true)
            end,
            nil,
            net_gateway_module.CONNECT_STATE_RETUREN_HALL
        )
    end
end

function M:InitServerList()
    --加载服务器列表
    local server_info = require "server_info"
    if server_info.servers == nil then
        return
    end

    local lastServerName = PlayerPrefs.GetString("server", "")
    local serverIndex = 0

    for i, v in ipairs(server_info.servers) do
        if lastServerName == v.name then
            self.curSelectServerIdx = i
            break
        end
    end

    --监听connect的值
    self.connectSyncProp:AddListener("connect", self.connectSyncListener)
end

-- * Editor模拟功能 结束 ******************************************************************************************************************/

return M
