local require = require

local event = require "event"
local msg_pb = require "msg_pb"
local broadcast_pb = require "broadcast_pb"
local net_route = require "net_route"
local chat_pb = require "chat_pb"
local platform_pb = require "platform_pb"

module("net_broadcast_module")

function Recv_BROADCAST_MSG(msg)
    local message = nil
    if msg.enType == broadcast_pb.enBc_System then
        message = broadcast_pb.TMSG_BROADCAST_SYS_MSG_NTF()
    elseif msg.enType == broadcast_pb.enBc_Chat then
        message = chat_pb.TMSG_CHAT_NEW_MSG_NTF()
    elseif msg.enType == broadcast_pb.enBc_Notice then
        message = platform_pb.TMSG_BROADCAST_NOTICE_NTF()
    end
    message:ParseFromString(msg.data)
    --event.Trigger(event.RECEIVE_BROADCAST, msg.enType, message)
end

local MessageTable = 
{
	{msg_pb.MSG_BROADCAST_MSG_NTF, Recv_BROADCAST_MSG, broadcast_pb.TMSG_BROADCAST_MSG_NTF},
}
--net_route.RegisterMsgHandlers(MessageTable)