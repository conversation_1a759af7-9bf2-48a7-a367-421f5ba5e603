--@region FileHead
-- ui_vip_privilege.txt ---------------------------------
-- author:  无名氏
-- date:    12/12/2019 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion 

--@region Require
local require   = require
local pairs     = pairs
local ipairs    = ipairs
local string    = string
local table     = table
local math      = math

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local window_mgr            = require "ui_window_mgr"
local lang                  = require "lang"
local game_scheme           = require "game_scheme"
local player_mgr            = require "player_mgr"
local goods_item            = require "goods_item_new"
local iui_item_detail       = require "iui_item_detail"
local item_data             = require "item_data"
--@endregion 

--@region ModuleDeclare
module("ui_vip_privilege")
--local interface = require "iui_vip_privilege"
local window = nil
local UIVipPrivilege = {}
--@endregion 

--@region WidgetTable
UIVipPrivilege.widget_table = {
	closeBtn = {path = "Auto_closeBtn", type = "Button",backEvent = true},
    Text_VipReward = { path = "Auto_Privilege/VipReward/Horizontal", type = "RectTransform", },
    Text_DailyReward = { path = "Auto_Privilege/DailyReward/Horizontal", type = "RectTransform", },
    Text_DailyRewardRt = { path = "Auto_Privilege/DailyReward", type = "RectTransform", },
	
    Btn_next = { path = "Auto_Privilege/Auto_next", type = "Button", },
    Btn_pre = { path = "Auto_Privilege/Auto_pre", type = "Button", },
    Text_VipRewardText = { path = "Auto_Privilege/VipReward/Auto_VipRewardText", type = "Text", },
    Text_DailyRewardText = { path = "Auto_Privilege/DailyReward/Auto_DailyRewardText", type = "Text", },
    Text_AllRewardText = { path = "Auto_Privilege/Auto_AllRewardText", type = "Text", },
	Text_Vip_Rewards = { path = "Auto_Privilege/rewardsBg/text", type = "Text" , },
	Text_Vip_Perks = { path = "Auto_Privilege/perksBg/text", type = "Text" , },
--@region User
--@endregion 
}
--@endregion 

--@region WindowCtor
function UIVipPrivilege:ctor(selfType)
--@region User
	self.__base:ctor(selfType)
--@endregion 
end --///<<< function

--@endregion 

--@region WindowInit
--[[窗口初始化]]
function UIVipPrivilege:Init()
	self.Text_VipRewardText.text = lang.Get(9140)
	self.Text_VipRewardText.text = lang.Get(9140)
	self.Text_DailyRewardText.text = lang.Get(9141)
	
	self.baseOffset = 1
	self.moveLevel = function(offset)
		for i,v in ipairs(self.dailyItem or {}) do
			v:Dispose()
		end
		for i,v in ipairs(self.achieveItem or {}) do
			v:Dispose()
		end
		self.dailyItem = {}
		self.achieveItem = {}
		local playerProp = player_mgr.GetPlayerProp()-- or {faceID = 1, roleName = '', lv=1}
		-- 最大13级vip
		local max = game_scheme:VipPrivilege_nums()-1

		local queryLevel = math.min(max, playerProp.vipLevel+offset)--playerProp.vipLevel >= max and max or playerProp.vipLevel+1
		queryLevel = math.max(1, queryLevel)
		self.Btn_next.gameObject:SetActive(queryLevel < max)
		self.Btn_pre.gameObject:SetActive(queryLevel > 1)
		self.baseOffset = queryLevel - playerProp.vipLevel

		local privilege = game_scheme:VipPrivilege_0(queryLevel)
		local fmt = lang.Get(9139)
		local buyFreeTimes = game_scheme:InitBattleProp_0(205).szParam.data[0]
		self.Text_AllRewardText.text = string.format(fmt, privilege.iHeroPackIncrNum, (privilege.fIdleResRatio-1)*100, privilege.iTavernTaskNum, privilege.iActivityEctypeBuyNum,buyFreeTimes + (privilege.qIdleBuyTimes or 0),privilege.iArenaBuyTimes or 0)

		for i, v in pairs(privilege.iMCItemIDs.data) do
			local aItem = goods_item.CGoodsItem()
			--aItem:SetSpecify('ui/prefabs/goodsitem_vip.prefab')
			aItem:Init(self.Text_DailyReward, nil, 0.65)
		
			aItem:SetGoods(nil, v, privilege.iMCItemCounts.data[i], function()
				iui_item_detail.Show(v, nil, item_data.Item_Show_Type_Enum.Reward_Interface)
			end)
			table.insert(self.dailyItem, aItem)
		end
		self.Text_DailyRewardRt.gameObject:SetActive(#self.dailyItem > 0)

		-- local achievement = game_scheme:Achievement_0(220-1+queryLevel)
		for i, v in pairs(privilege.VIPLvReward.data) do
			local aItem = goods_item.CGoodsItem()
			--aItem:SetSpecify('ui/prefabs/goodsitem_vip.prefab')
			aItem:Init(self.Text_VipReward, nil, 0.65)
		
			local arr = game_scheme:Reward_0(v).arrParam
			aItem:SetGoods(nil, arr[0], arr[1], function()
				iui_item_detail.Show(arr[0], nil, item_data.Item_Show_Type_Enum.Reward_Interface)
			end)
			table.insert(self.achieveItem, aItem)
		end

		self.Text_Vip_Rewards.text=string.format(lang.Get(9190),queryLevel)
		self.Text_Vip_Perks.text=string.format(lang.Get(9191),queryLevel)
	end

	self.moveLevel(self.baseOffset)
	
    self:SubscribeEvents()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnShow
--[[资源加载完成，被显示的时候调用]]
function UIVipPrivilege:OnShow()
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowOnHide
--[[资源加载完成，被显示的时候调用]]
function UIVipPrivilege:OnHide()
--@region User
--@endregion 
end --///<<< function

--@region WindowClose
function UIVipPrivilege:Close()
	if self:IsValid() then
		self:UnsubscribeEvents()
	end
    
	self.__base:Close()
    window = nil
--@region User
--@endregion 
end --///<<< function

--@endregion 

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UIVipPrivilege:SubscribeEvents()
	self.closeEvent = function()
        window_mgr:UnloadModule("ui_vip_privilege")
    end
	self.widget_table["closeBtn"].event_name = "closeEvent"
	
    self.OnBtn_nextClickedProxy = function()
        self.baseOffset = self.baseOffset + 1
		self.moveLevel(self.baseOffset)
    end

    self.OnBtn_preClickedProxy = function()
        self.baseOffset = self.baseOffset - 1
		self.moveLevel(self.baseOffset)
    end

----///<<< Button Proxy Line >>>///-----
	self.widget_table["Btn_next"].event_name = "OnBtn_nextClickedProxy"
	self.widget_table["Btn_pre"].event_name = "OnBtn_preClickedProxy"

end --///<<< function

--@endregion 

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UIVipPrivilege:UnsubscribeEvents()

--@region User
--@endregion 
end --///<<< function

--@region WindowInherited
local CUIVipPrivilege = class(ui_base, nil, UIVipPrivilege)
--@endregion 

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUIVipPrivilege()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uivipprivilege.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

--@endregion 

--@region RegisterMsg
MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
--@endregion 

