local require = require 
local typeof = typeof
local string = string

local GameObject = CS.UnityEngine.GameObject
local PlayerPrefs = CS.UnityEngine.PlayerPrefs
local Utility = CS.War.Script.Utility
local Application = CS.UnityEngine.Application
local ConnectTest = CS.War.Script.ConnectTest

local event = require "event"

module("connect_test")
local init = nil
local connectTest = nil
function ChooseDirectlyConnect()
    if true then
        --2020.9.28 赖嘉明：屏蔽直连，全部使用代理（开通了全球加速）
        return false
    end
    if Application.isEditor or Utility.VersionIsHigher('1.0.44', Application.version) then
        --return PlayerPrefs.GetInt("ConnectTest_chooseDirectly", 0) == 1
    end

    if init then
        return PlayerPrefs.GetInt("ConnectTest_chooseDirectly", 0) == 1
    end
    init = true

	if not connectTest then
		local obj = GameObject.Find("/ConnectTest")
		local util = require "util"
		if not util.IsObjNull(obj) then
            connectTest = obj:GetComponent(typeof(ConnectTest))	
        end
    end
    if not connectTest then
        return PlayerPrefs.GetInt("ConnectTest_chooseDirectly", 0) == 1
    end
	local directlyAverageTime = connectTest:GetDirectlyAverageConnectTime()
    local indirectlyAverageTime = connectTest:GetIndirectlyAverageConnectTime()
    local str = "{\"directly_average_time\":%s,\"indirectly_average_time\":%s}"
    event.Trigger(event.GAME_EVENT_REPORT, "connect_test", string.format(str, directlyAverageTime, indirectlyAverageTime))
	local directly = connectTest:GetDirectlyConnectComplete()
    if not directly then
        PlayerPrefs.SetInt("ConnectTest_chooseDirectly", 0)
		return false
	end
    PlayerPrefs.SetInt("ConnectTest_chooseDirectly", directlyAverageTime < indirectlyAverageTime and 1 or 0)
	return directlyAverageTime < indirectlyAverageTime 
end

changeFlag = nil
function ChangeNextConnect()
    if changeFlag == nil then
        changeFlag = false
    else
        changeFlag = not changeFlag
    end
end

function GetChangeFlag()
    return changeFlag
end

function ResetChangeFlag()
    --changeFlag = nil
end