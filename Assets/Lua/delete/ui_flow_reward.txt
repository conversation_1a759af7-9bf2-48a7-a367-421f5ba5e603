--@region FileHead
-- ui_flow_reward.txt ---------------------------------
-- author:  罗华冠
-- date:    7/17/2020 4:03:52 PM
-- ver:     1.0
-- desc:    奖励飞动画
-------------------------------------------------
 

--@region Require
local require   = require
local pairs     = pairs
local table     = table

local Image             = CS.UnityEngine.UI.Image
local LeanTween         = CS.LeanTween
local Canvas            = CS.UnityEngine.Canvas

local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local net_route             = require "net_route"
local goods_item            = require "goods_item_new"
local game_scheme           = require "game_scheme"
local util                  = require "util"
local windowMgr             = require "ui_window_mgr"
 

module("ui_flow_reward")
local window = nil
local UIFlowReward = {}

local targetObj1 = nil -- 终点对象1
local targetObj2 = nil -- 终点对象2
local flowObj = nil -- 飘动的奖励对象

local modelPath1 = nil -- 终点对象1的路径
local modelPath2 = nil -- 终点对象2的路径

local itemIcon = nil -- 飞的对象
local rewardIcon = nil -- 奖励对象

local progress_list = nil -- 用于排序进度条动画
local isFirstPlay = true -- 标记是否首次播放
local isClose = false -- 标记是否首次播放

UIFlowReward.widget_table = {
    targetTrans = {path = "targetTrans", type = "RectTransform"},
    targetAni = {path = "targetTrans/show_ani", type = "RectTransform"},
    startTrans = {path = "startTrans", type = "RectTransform"},
    progressTrans = {path = "targetTrans/progress", type = "RectTransform"},
    rewardTrans = {path = "targetTrans/progress/rewardIcon", type = "RectTransform"},
    progressValue = {path = "targetTrans/progress/progressBg/progressValue", type = "Image"},
    effect = {path = "targetTrans/progress/progressBg/effect", type = "RectTransform"},
    canvasComponent = {path = "", type = Canvas},
}


function UIFlowReward:ctor(selfType)

 
end  

 

--[[窗口初始化]]
function UIFlowReward:Init()
    self:SubscribeEvents()
 
end  

 

--[[资源加载完成，被显示的时候调用]]
function UIFlowReward:OnShow()
    self.canvasComponent.sortingLayerName = "Top"
    SetProgressPos(self.targetTrans.gameObject.transform.position)
end  

 

--[[界面隐藏时调用]]
function UIFlowReward:OnHide()
 

end  

function ShowTargetAni()
    if window and window:IsValid() then
        window.targetAni.gameObject:SetActive(true)
    end
end

-- 增加进度条动画队列
-- state:0未播放 1正在播放 2已播放
function SetProgressAniList(cur,num,total,rewardID)
    local data = {cur=0,num=0,total=0,state=0,rewardID=0}
    data.cur = cur
    data.num = num
    data.total = total
    data.rewardID = rewardID
    table.insert( progress_list, data)
    ShowProgress()
end

-- 获取现在要播放得进度条信息
function GetShowProgress(check)
    local cur = 0
    local num = 0
    local total = 0
    local state = 2
    local rewardID = 0
    local isShow = false
    local index = 0
    for k,v in pairs(progress_list)do
        if v.state == 1 then
            -- 正在播放中 则不打断当前得
            index = k
            state = v.state
            cur = v.cur
            num = v.num
            total = v.total
            state = v.state
            rewardID = v.rewardID
            break
        end
        if v.state == 0 then
            cur = v.cur
            num = v.num
            total = v.total
            state = v.state
            rewardID = v.rewardID
            isShow = true
            index = k
            if not check then
                progress_list[k].state = 1
            end
            break
        end
    end
    return isShow,cur,num,total,rewardID,state,index
end

-- 播放进度条动画
function ShowProgress()
    if window and window:IsValid() then
        window.progressTrans.gameObject:SetActive(true)
        local isShow,cur,num,total,rewardID,state,index = GetShowProgress()
        if isShow then -- 如果有正在播放得 就不打断
            -- 无正在播放得
            isClose = false
            SetReward(rewardID)
            LeanTween.value(cur,num,0.5):setOnUpdate(function (val)
                if window and window:IsValid() then
                    window.progressValue.fillAmount = val/total
                end
            end):setOnComplete(function ()
                progress_list[index].state = 2
                local delay = 0.2
                if num >= total then
                    delay = 1
                    window.effect.gameObject:SetActive(true)
                end
                -- 判断是否存在队列
                local _isShow,_cur,_num,_total,_rewardID,_state,_index = GetShowProgress(true)
                if _isShow then
                    -- 存在下一个动画
                    ShowProgress()
                else
                    -- 此动画为最终动画
                    isClose = true
                    util.DelayCallOnce(delay,function ()
                        if isClose then -- 规避在延时过程 又点击
                            windowMgr:UnloadModule("ui_flow_reward")
                        end
                    end)
                end
            end)
        end
    end
end

function PlaySecProgressAni()

end

function SetGoods(name,rewardID,callback)
    if window and window:IsValid() and not itemIcon then
        progress_list = {}
        itemIcon = goods_item.CGoodsItem()
        itemIcon:Init(window.startTrans,nil,0.65)
        local rewardCfg = game_scheme:Reward_0(rewardID)
        itemIcon:SetGoods(nil, rewardCfg.arrParam[0], rewardCfg.arrParam[1])
        window:PlayAni(callback)
    end
end

function SetReward(rewardID)
    if window and window:IsValid() then
        if not rewardIcon then
            rewardIcon = goods_item.CGoodsItem()
            rewardIcon:Init(window.rewardTrans,nil,0.4)
        end
        local rewardCfg = game_scheme:Reward_0(rewardID)
        if rewardIcon then
            rewardIcon:SetGoods(nil, rewardCfg.arrParam[0], rewardCfg.arrParam[1])
        end
    end
end

function UIFlowReward:PlayAni(callback)
    if self:IsValid() and itemIcon then
        LeanTween.move(self.startTrans,self.targetTrans.localPosition, 1):setOnComplete(function()
            if callback then
                callback()
            end
        end)
        LeanTween.scale(self.startTrans,{x = 0.2, y = 0.2,z = 0},1):setOnComplete(function ()
            self.startTrans.gameObject:SetActive(false)
        end)
    end
end

 

--[[设置窗口的输入参数。该参数通常是由其它模块或者外部设置进来。需要注意的是，
当调用这个函数的时候，窗口资源可能还是没有加载完成的。
@param p 参数表
]]
-- p[1] startPos p2 targetPos  
function UIFlowReward:SetInputParam(p)
    self.inputParam = p
    if p then
        if p[1] then
            self:SetStartPosition(p[1])
        end

        if p[2] then
            self:SetTargetPosition(p[2])
        end
    end
    if self.UIRoot and self.UIRoot.activeSelf == true then
        self:UpdateUIPage()
    end
end  

-- 设定终点位置
function SetTargetPosition(pos)
    window.targetTrans.gameObject.transform.position = pos
    SetProgressPos(pos)
end

function SetProgressPos(pos)
    -- local progressPos = window.progressTrans.gameObject.transform.localPosition
    -- window.progressTrans.gameObject.transform.position = {x=pos.x+100,y=pos.y,z=pos.z}
end

-- 设定起点位置
function SetStartPosition(pos)
    window.startTrans.gameObject.transform.position = pos
end

--[[构建UI更新数据]]
function UIFlowReward:BuildUpdateData()
 
 
end  

 

--[[资源加载完成，被显示的时候调用]]
function UIFlowReward:UpdateUIPage()
	self:BuildUpdateData()
 
 
end  

 

function UIFlowReward:Close()
    net_route.UnregisterMsgHandlers(MessageTable)

    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    self.__base:Close()
    if rewardIcon then
        rewardIcon:Dispose()
		rewardIcon = nil
    end
    if itemIcon then
        itemIcon:Dispose()
		itemIcon = nil
    end
    
    window = nil
 
 
end  

 

--[[订阅UI事件]]
function UIFlowReward:SubscribeEvents()

end  

 

--[[退订UI事件]]
function UIFlowReward:UnsubscribeEvents()
 
 
end  

 

local CUIFlowReward = class(ui_base, nil, UIFlowReward)
 

function Show()
    if window == nil then
        window = CUIFlowReward()
        window._NAME = _NAME
        window:LoadUIResource("ui/prefabs/uiflowreward.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        Hide()
        window:Close()
        window = nil
    end
end

 

MessageTable =
{ --///<<< tableStart
} --///<<< tableEnd
 

