--@region FileHead
-- author:  孙健
-- date:    6/19/2020 12:25:43
-- ver:     1.0
-- desc:    动画回调
--@endregion


local AnimEventListener = CS.War.Script.AnimEventListener
local Utility = CS.War.Script.Utility
local Application  = CS.UnityEngine.Application

local M = {}
local version = '1.0.66'  --版本高于version时生效

---@param gameObj 类型为GameObject必须具有Animator组件
---@param clipName 触发回调的动画片段名称
---@param time  回调方法触发时机
---@param callBack 回调方法
---@param defaultImp 未强更时的实现由调用者提供
function M.AddEvent(gameObj,clipName,time,callBack,defaultImp)
    if Utility.VersionIsHigher(Application.version, version) then
        AnimEventListener.Get(gameObj):AddEvent(clipName, time, callBack)
    else
        if type(defaultImp) == "function" then
            defaultImp()
        end
    end
end

---@param gameObj  类型为GameObject必须具有Animator组件
---@param clipName 移除回调的动画片段名称
function M.RemoveEvent(gameObj,clipName)
    if Utility.VersionIsHigher(Application.version, version) then
        AnimEventListener.Get(gameObj):RemoveEvent(clipName)
    end
end
return M
