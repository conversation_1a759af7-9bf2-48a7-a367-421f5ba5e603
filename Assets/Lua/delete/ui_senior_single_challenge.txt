--@region FileHead
-- ui_senior_single_challenge.txt ---------------------------------
-- author:  孙健
-- date:    10/9/2019 12:00:00 AM
-- ver:     1.0
-- desc:    Description
-------------------------------------------------
--@endregion

--@region Require
local print     = print
local require   = require
local pairs     = pairs
local typeof    = typeof
local string    = string
local table     = table
local tostring  = tostring
local type      = type
local tonumber  = tonumber

local Button        = CS.UnityEngine.UI.Button
local Text          = CS.UnityEngine.UI.Text
local Image         = CS.UnityEngine.UI.Image
local RectTransform = CS.UnityEngine.RectTransform
local UI			= CS.UnityEngine.UI
local PlayerPrefs   = CS.UnityEngine.PlayerPrefs
local Outline       = CS.UnityEngine.UI.Outline

local event					= require "event"
local class                 = require "class"
local ui_base               = require "ui_base"
local module_scroll_list    = require "scroll_list"
local common_new_pb         = require "common_new_pb"
local net_arena_module      = require "net_arena_module"
local ui_window_mgr         = require "ui_window_mgr"
local util                  = require "util"
local face_item             = require "face_item_new"
local skep_mgr              = require "skep_mgr"
local player_mgr            = require "player_mgr"
local lang			        = require "lang_util"
local general_shop_mgr      = require "general_shop_mgr"
local game_scheme           = require "game_scheme"
local activity_tips_mgr     = require "activity_tips_mgr"
local activity_mgr          = require "activity_mgr"
local goods_item            = require "goods_item_new"
local iui_item_detail       = require "iui_item_detail"
local item_data             = require "item_data"
--@endregion

--@region ModuleDeclare
module("ui_senior_single_challenge")
--local interface = require "iui_single_challenge"
local window = nil
local UISingleChallenge = {}
local HeroImgOffset = {x=-138, y=-22, z=0}
local arenaEntity = nil
local goodsEntity = nil
local initialOffset = 0
local arenaType = common_new_pb.Advance     -- 区分是否多阵容
local battleType = nil
local myScore = nil
local otherScore = {}
--@endregion

--@region WidgetTable
UISingleChallenge.widget_table = {
    Rect_Adaptation = { path = "yuanjiaoredBJ_R", type = RectTransform, },
    Btn_Challenge1 = { path = "content/Auto_Unit1/Auto_Challenge1", type = "Button", },
    Btn_closeBtn = { path = "yuanjiaoredBJ_R/Auto_closeBtn", type = "Button",backEvent = true  },
    Btn_BuyTicket = { path = "yuanjiaoredBJ_R/goumai/Auto_BuyTicket", type = "Button", },
    Btn_Exchange = { path = "yuanjiaoredBJ_R/Auto_Exchange", type = "Button", },
    Btn_Challenge2 = { path = "content/Auto_Unit2/Auto_Challenge2", type = "Button", },
    Btn_Challenge3 = { path = "content/Auto_Unit3/Auto_Challenge3", type = "Button", },
    Text_Title = { path = "yuanjiaoredBJ_R/ImageTitle/Auto_Title", type = "Text", },
    Text_Cost1 = { path = "content/Auto_Unit1/Auto_Cost1", type = "Text", },
    Text_PlayerName1 = { path = "content/Auto_Unit1/Auto_PlayerName1", type = "Text", },
    Text_AbilityText1 = { path = "content/Auto_Unit1/Auto_AbilityText1", type = "Text", },
    Text_TicketNum = { path = "yuanjiaoredBJ_R/goumai/Auto_TicketNum", type = "Text", },
    Text_Cost2 = { path = "content/Auto_Unit2/Auto_Cost2", type = "Text", },
    Text_PlayerName2 = { path = "content/Auto_Unit2/Auto_PlayerName2", type = "Text", },
    Text_AbilityText2 = { path = "content/Auto_Unit2/Auto_AbilityText2", type = "Text", },
    Text_Cost3 = { path = "content/Auto_Unit3/Auto_Cost3", type = "Text", },
    Text_PlayerName3 = { path = "content/Auto_Unit3/Auto_PlayerName3", type = "Text", },
    Text_AbilityText3 = { path = "content/Auto_Unit3/Auto_AbilityText3", type = "Text", },
    Img_Unit1 = { path = "content/Auto_Unit1", type = "Image", },
    Img_Unit2 = { path = "content/Auto_Unit2", type = "Image", },
    Img_Unit3 = { path = "content/Auto_Unit3", type = "Image", },

    Img_PlayerIcon1 = { path = "content/Auto_Unit1/touxiangbankuang", type = "Image", },
    Img_PlayerIcon2 = { path = "content/Auto_Unit2/touxiangbankuang", type = "Image", },
    Img_PlayerIcon3 = { path = "content/Auto_Unit3/touxiangbankuang", type = "Image", },

    --@region User
    --Text_NumText = { path = "Auto_IminRank/Auto_NumText", type = "Text", },
    Text_PlayerName = { path = "Auto_IminRank/Auto_PlayerName", type = "Text", },
    Text_ScoreText = { path = "Auto_IminRank/Auto_ScoreText", type = "Text", },
    Text_AbilityText = { path = "Auto_IminRank/Auto_AbilityText", type = "Text", },

    Img_IminRank = { path = "Auto_IminRank", type = "Image", },
    Img_touxiangbankuang = { path = "Auto_IminRank/touxiangkuang", type = "Image", },

    Text_Dan1 = { path = "content/Auto_Unit1/dan1", type = "Text", },
    Outline_Dan1 = {path = "content/Auto_Unit1/dan1", type = Outline},
    Text_Dan2 = { path = "content/Auto_Unit2/dan2", type = "Text", },
    Outline_Dan2 = {path = "content/Auto_Unit2/dan2", type = Outline},
    Text_Dan3 = { path = "content/Auto_Unit3/dan3", type = "Text", },
    Outline_Dan3 = {path = "content/Auto_Unit3/dan3", type = Outline},

    --活动提示内容
    title = {path = "yuanjiaoredBJ_R/activity/title", type = "Text",},
    content = {path = "yuanjiaoredBJ_R/activity/content", type = "Text",},
    processbg = {path = "yuanjiaoredBJ_R/activity/processbg", type = RectTransform,},
    process = {path = "yuanjiaoredBJ_R/activity/processbg/process", type = RectTransform,},
    num = {path = "yuanjiaoredBJ_R/activity/processbg/count", type = "Text",},
    items = {path = "yuanjiaoredBJ_R/activity/items", type = RectTransform,},
    item_0 = {path = "yuanjiaoredBJ_R/activity/items/item_1", type = RectTransform,},
    item_1 = {path = "yuanjiaoredBJ_R/activity/items/item_2", type = RectTransform,},
    item_2 = {path = "yuanjiaoredBJ_R/activity/items/item_3", type = RectTransform,},
    Txt_Challenge1 = { path = "content/Auto_Unit1/Auto_Challenge1/TiaoZhan", type = "Text", },
    Txt_Challenge2 = { path = "content/Auto_Unit2/Auto_Challenge2/TiaoZhan", type = "Text", },
    Txt_Challenge3 = { path = "content/Auto_Unit3/Auto_Challenge3/TiaoZhan", type = "Text", },
    --@endregion
}
--@endregion

--更新提示
function UISingleChallenge:UpdateTips()
    self.tipsData = activity_tips_mgr.GetCurrentTipsData()
    if true then
        return
    end
    local activityEntity = self.tipsData.acntivity
    if activityEntity then
        local activityConfig = activity_mgr.GetActivityCsvByTypeAndId(activityEntity.type,activityEntity.activityID)
        local content = nil
        --活动内容去除
        for k,_content in pairs(activityEntity.content) do
            local contentConfig = game_scheme:ActivityContent_0(_content.contentID)
            print("==================",k,util.get_len(activityEntity.content),contentConfig.remark)
            if _content.value/contentConfig.conditionTimes.data[0] < 1 then
                content = {
                    remark = contentConfig.remark,--活动描述
                    rewardGroup = activity_mgr.GetActivityContentRewards(contentConfig,activityEntity.nLevel),--活动奖励rewardID
                    conditions = contentConfig.conditionTimes.data[0],--活动条件
                    contentID = _content.contentID,--内容id
                    value = _content.value,--值
                }
                break
            end
        end
        if not content then
            for k,_content in pairs(activityEntity.content) do
                if k == util.get_len(activityEntity.content) - 1 then
                    local contentConfig = game_scheme:ActivityContent_0(_content.contentID)
                    content = {
                        remark = contentConfig.remark,--活动描述
                        rewardGroup = activity_mgr.GetActivityContentRewards(contentConfig),--活动奖励rewardID
                        conditions = contentConfig.conditionTimes.data[0],--活动条件
                        contentID = _content.contentID,--内容id
                        value = _content.value,--值
                    }
----                    print("contentConfig.conditionTimes.data[0]",contentConfig.conditionTimes.data[0],"_content.value",_content.value)
                    break
                end
            end
        end

----        print("activityConfig.repeatTimes = "..activityConfig.repeatTimes,"activityEntity.completeTimes = "..activityEntity.completeTimes)
        if content then
            if not self.contentID or self.contentID == content.contentID then
                self["items"].gameObject:SetActive(true)
                for i=1,content.rewardGroup.count do
                    local cfg = game_scheme:Reward_0(content.rewardGroup.data[i])
                    if cfg ~= nil then
                        self["item_"..i].gameObject:SetActive(true)
                        if not self["goods"..i] then
                            self["goods"..i] = goods_item.CGoodsItem()
                        end

                        self["goods"..i]:SetFrameBg(3)
                        self.OnShowDetail = function (entity,id,attachData)
                            iui_item_detail.Show(id,nil, item_data.Item_Show_Type_Enum.Reward_Interface, nil, nil, nil, attachData,nil,nil,nil,function( ... )
                                event.Trigger(event.SUPPER_EFFECT_ENABLE,false)
                            end,function( ... )
                                event.Trigger(event.SUPPER_EFFECT_ENABLE,true)
                            end)
                        end
                        self["goods"..i]:Init(self["item_"..i].transform,function ()
                            self["goods"..i]:SetGoods(nil, cfg.arrParam[0], cfg.arrParam[1], self.OnShowDetail, cfg.iRewardID)
                        end,0.5)
                    end
                end
                self.processbg.gameObject:SetActive(true)
                --self.num.gameObject:SetActive(true)
                self.title.text = lang.Get(7138)
                self.content.text = lang.Get(tonumber(content.remark))
                self.num.text = content.value.."/"..content.conditions

                local pro = content.value / content.conditions
                if pro > 1 then
                    pro = 1
                end
                local width = (pro ~= 0) and (337 * pro) or 0
                if window.process then
                    window.process.sizeDelta = {x = width, y = window.process.sizeDelta.y}
                end

                self.contentID = content.contentID
            end
        end
    end
end

--@region WindowInit
--[[窗口初始化]]
function UISingleChallenge:Init()
    self:SubscribeEvents()
    --@region User
    self:UpdateOnReceiveInfo(net_arena_module.MineRank[arenaType], net_arena_module.MineScore[arenaType], net_arena_module.MineTotalCE[arenaType])
    self.arenaCoinGoodsNumChange = function(num)
        self.Text_TicketNum.text = util.PriceConvert(num)
    end

    self:RefreshGoodsNum()

    local list = net_arena_module.RankingList[arenaType] or {}

    -- 自己是否在排行榜中
    local mineRank = net_arena_module.MineRank[arenaType]
    local imInRank = net_arena_module.MineRank[arenaType] ~= nil and mineRank <= #list

    --[[if mineRank and imInRank then
        self:Draw(list[mineRank], mineRank, nil)
    elseif net_arena_module.MineRank[arenaType] ~= nil then]]
    local playerProp = player_mgr.GetPlayerProp()
    local data = {
        ranking = net_arena_module.MineRank[arenaType],
        scores = net_arena_module.MineScore[arenaType],
        totalCE = net_arena_module.MineTotalCE[arenaType],
        name = playerProp.roleName,
        playerInfo = {{roleID = player_mgr.GetPlayerRoleID(), faceID = playerProp.faceID, level = playerProp.lv, name = playerProp.name}}
    }
    self:Draw(data, data.ranking, nil)
    --end

    --小秘书
    event.Trigger(event.SCENE_OPEN_MODULE,3)
    self:UpdateTips()
    --@endregion
end --///<<< functions

--@endregion

function UISingleChallenge:RefreshGoodsNum()
    local goodsid = skep_mgr.GetConstGoodsSidByID(skep_mgr.const_id.advanceArenaTicket)
    self.entity = player_mgr.GetPacketPartDataBySid(goodsid)

    if self.entity then
        self.entity.numProp:AddListener("goodsNum", self.arenaCoinGoodsNumChange)
        self.arenaCoinGoodsNumChange(self.entity.numProp.goodsNum)
        --self.Text_TicketNum.text =  util.PriceConvert(self.entity.numProp.goodsNum)
    end
end

--@region WindowSubscribeEvents
--[[订阅UI事件]]
function UISingleChallenge:SubscribeEvents()
    self.OnBtn_Challenge1ClickedProxy = function()
        self:OnBtn_ChallengeClicked(0)
    end

    self.OnBtn_closeBtnClickedProxy = function()
        self:OnBtn_closeBtnClicked()
    end

    self.OnBtn_BuyTicketClickedProxy = function()
        self:OnBtn_BuyTicketClicked()
    end

    self.OnBtn_ExchangeClickedProxy = function()
        self:OnBtn_ExchangeClicked()
    end

    self.OnBtn_Challenge2ClickedProxy = function()
        self:OnBtn_ChallengeClicked(1)
    end

    self.OnBtn_Challenge3ClickedProxy = function()
        self:OnBtn_ChallengeClicked(2)
    end

    self.OnBtn_Challenge4ClickedProxy = function()
        self:OnBtn_ChallengeClicked(3)
    end

    self.OnBtn_Challenge5ClickedProxy = function()
        self:OnBtn_ChallengeClicked(4)
    end

    ----///<<< Button Proxy Line >>>///-----

    if self.Btn_Challenge1 then
        self.Btn_Challenge1.onClick:AddListener(self.OnBtn_Challenge1ClickedProxy)
    end

    if self.Btn_closeBtn then
        self.Btn_closeBtn.onClick:AddListener(self.OnBtn_closeBtnClickedProxy)
    end

    if self.Btn_BuyTicket then
        self.Btn_BuyTicket.onClick:AddListener(self.OnBtn_BuyTicketClickedProxy)
    end

    if self.Btn_Exchange then
        self.Btn_Exchange.onClick:AddListener(self.OnBtn_ExchangeClickedProxy)
    end

    if self.Btn_Challenge2 then
        self.Btn_Challenge2.onClick:AddListener(self.OnBtn_Challenge2ClickedProxy)
    end

    if self.Btn_Challenge3 then
        self.Btn_Challenge3.onClick:AddListener(self.OnBtn_Challenge3ClickedProxy)
    end

    if self.Btn_Challenge4 then
        self.Btn_Challenge4.onClick:AddListener(self.OnBtn_Challenge4ClickedProxy)
    end

    if self.Btn_Challenge5 then
        self.Btn_Challenge5.onClick:AddListener(self.OnBtn_Challenge5ClickedProxy)
    end
end --///<<< function

--@endregion

--@region WindowUnsubscribeEvents
--[[退订UI事件]]
function UISingleChallenge:UnsubscribeEvents()
    if self.Btn_Challenge1 then
        self.Btn_Challenge1.onClick:RemoveListener(self.OnBtn_Challenge1ClickedProxy)
    end

    if self.Btn_closeBtn then
        self.Btn_closeBtn.onClick:RemoveListener(self.OnBtn_closeBtnClickedProxy)
    end

    if self.Btn_BuyTicket then
        self.Btn_BuyTicket.onClick:RemoveListener(self.OnBtn_BuyTicketClickedProxy)
    end

    if self.Btn_Exchange then
        self.Btn_Exchange.onClick:RemoveListener(self.OnBtn_ExchangeClickedProxy)
    end

    if self.Btn_Challenge2 then
        self.Btn_Challenge2.onClick:RemoveListener(self.OnBtn_Challenge2ClickedProxy)
    end

    if self.Btn_Challenge3 then
        self.Btn_Challenge3.onClick:RemoveListener(self.OnBtn_Challenge3ClickedProxy)
    end

    if self.Btn_Challenge4 then
        self.Btn_Challenge4.onClick:RemoveListener(self.OnBtn_Challenge4ClickedProxy)
    end

    if self.Btn_Challenge5 then
        self.Btn_Challenge5.onClick:RemoveListener(self.OnBtn_Challenge5ClickedProxy)
    end

    --@region User
    --@endregion
end --///<<< function

--@endregion

--@region WindowBtnFunctions
function getKey()
    return 'attack_lineups_'..player_mgr.GetPlayerRoleID()..common_new_pb.AdvanceSingle
end

function UISingleChallenge:OnBtn_ChallengeClickedNew(i)
	local goodsNum = self.entity and self.entity.numProp.goodsNum or 0
    if (net_arena_module.MineRemainFreeTimes[arenaType] <= 0 or arenaType == common_new_pb.Champion) and goodsNum < game_scheme:Arena(arenaType).ticketNum then
        self:OnBtn_BuyTicketClicked()
        return
    end

    if #self.rivalList <= 0 then
        return
    end

    local rivalIndex=nil
    if #self.rivalList%3>0 then
        rivalIndex=((initialOffset + i) % (#self.rivalList+3-(#self.rivalList%3)) + 1)
    else
        rivalIndex=((initialOffset + i) % (#self.rivalList) + 1)
    end

    local rival = self.rivalList[rivalIndex]
	
	--@region 设置武器数据，首次设置出战阵容，不使用防守阵容数据
	local ui_multi_hero_select = require "ui_multi_hero_select"
	if not PlayerPrefs.HasKey(ui_multi_hero_select.getKey()) then
		local weaponData = {0, 0, 0}
		local selectedHero = {{}, {}, {}}
		ui_multi_hero_select.SetAdvanceHeroData(selectedHero, weaponData)
	end
	--@endregion

	--@region 载入上次的设置
	local heroData, weaponData = ui_multi_hero_select.GetAdvanceHeroData()
	ui_multi_hero_select.SetSaveHeroData(heroData, nil, ui_multi_hero_select.ATTACK)
	local window = ui_window_mgr:ShowModule("ui_multi_hero_select")
	ui_window_mgr:UnloadModule("ui_senior_single_challenge")
	net_arena_module.Send_PLAYER_DETAILS(rival.rivalInfo[1].roleID, common_new_pb.emDetailType_Advance,true)  -- 请求阵容信息
    if window ~= nil then
		window.onCloseEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                ui_window_mgr:ShowModule("ui_senior_single_challenge")
            end
        end
		
        window.onFightEvent = function(window)
            if window ~= nil then
                window.onCloseEvent = nil
                window.onFightEvent = nil
                local selectedHero = ui_multi_hero_select.GetSelectedHero()
				local selectedWeapon = ui_multi_hero_select.GetSelectWeapon()
				ui_multi_hero_select.SetAdvanceHeroData(selectedHero, selectedWeapon)
                ui_window_mgr:UnloadModule("ui_multi_hero_select")
				
				local ui_multi_win_lose = require "ui_multi_win_lose"
				ui_multi_win_lose.SetRivalInfo(rival.rivalInfo)
			   
				local ui_multi_battle_result = require "ui_multi_battle_result"
				ui_multi_battle_result.SetRivalInfo(rival.rivalInfo)

				local battle_extern = require "battle_extern"
				battle_extern.SetUpArenaAdvanceBattle(common_new_pb.Advance, rival.rivalInfo[1].roleID, false)
				net_arena_module.SetLastRival(rival)
            end
        end
    end
end

function UISingleChallenge:OnBtn_ChallengeClicked(i)
	-- 2019-12-28高阶竞技场修改为多队伍竞技场 AdvanceSingle-->Advance
	self:OnBtn_ChallengeClickedNew(i)
end

function UISingleChallenge:OnBtn_closeBtnClicked()
    ui_window_mgr:UnloadModule("ui_senior_single_challenge")
end

function UISingleChallenge:OnBtn_BuyTicketClicked()
    --[[if arenaType == common_new_pb.Champion then
        event.Trigger(event.ARENA_DEFEND_LINEUP_RECV, arenaType, {})
    else
        local ui_matchplace_entrance = require "ui_matchplace_entrance"
        ui_matchplace_entrance.SaveDefenceLineups(arenaType, true)
    end]]
    general_shop_mgr.OpenShop(506)
end

function UISingleChallenge:OnBtn_ExchangeClicked()
    initialOffset = initialOffset + 3
    self:NextBatch()
end

function UISingleChallenge:UpdateOnReceiveInfo(ranking, scores, totalCE)
    if not self.UIRoot or not ranking then
        return
    end

    self.rivalList = net_arena_module.RivalList[arenaType] or {}
    --initialOffset = initialOffset
    self:NextBatch()
end

function UISingleChallenge:GetChildComponent(path, type_str)
    if self.Img_IminRank == nil then
        return nil
    end

    local child = nil
    if path == nil or path == "" then
        child = self.Img_IminRank
    else
        child = self.Img_IminRank.transform:Find(path)
    end

    if child == nil or type_str == nil then
        return child
    end

    if type(type_str) == "string" then
        type_str = typeof(UI[type_str])
    end

    local cmp = child:GetComponent(type_str)
    return cmp
end

function UISingleChallenge:Draw(data, i, list)
    self:GetChildComponent("Auto_ScoreText", "Text").text = --[["<color=#220300FF>".. lang.Get(15989)..":</color>"..]]tostring(data.scores)
    self:GetChildComponent("Auto_AbilityText", "Text").text = tostring(data.totalCE)
    self:GetChildComponent("Auto_PlayerName", "Text").text = data.name
    self:GetChildComponent("ImageLeft").gameObject:SetActive(arenaType ~= 1)

    self.profile = face_item.CFaceItem()
    self.profile:Init(self:GetChildComponent("touxiangkuang"), nil, 1)
    self.profile:SetFaceInfo(data.playerInfo[1].faceID, nil)
    self.profile:SetNewBg(true)
    self.profile:SetActorLvText(true, data.playerInfo[1].level)
    self.profile:SetFrameID(data.playerInfo[1].frameID,true)
    self.profile:FrameEffectEnable(true, window.curOrder+1)
end

--@desc换一批
function UISingleChallenge:NextBatch()
    if #self.rivalList <= 0 then
        return
    end
    local showNum = 3

    local startIndex=nil
    if #self.rivalList%3>0 then
        startIndex=(initialOffset % (#self.rivalList+3-(#self.rivalList%3)) + 1)
    else
        startIndex=(initialOffset % (#self.rivalList) + 1)
    end
    local tmp={}
    for i=0,showNum-1 do
        table.insert(tmp,self.rivalList[startIndex+i])
    end
    table.sort(tmp, function(a,b)
        if a==nil or b==nil then
            return false
        end
        if a==-1 or b==-1 then
            return false
        end
        if a.gradesID==nil or b.gradesID==nil then
            return false
        end

        return a.gradesID < b.gradesID
    end)
    for i = 0, showNum-1 do
        local index = i + 1
        local rival = tmp[index]

        self.rivalList[startIndex+i]=tmp[index]

        if(rival~=nil) then
            self["Img_Unit"..index].gameObject:SetActive(true)
            local rivalGroup = rival.rivalInfo
            self['Text_AbilityText'..index].text = --[[lang.Get(7163)..":"..]]tostring(rival.totalCE)
            self['Text_PlayerName'..index].text = rivalGroup[1].name
            ------ print("net_arena_module.RemainFreeTimes",net_arena_module.MineRemainFreeTimes[arenaType])
            self['Text_Cost'..index].text = (net_arena_module.MineRemainFreeTimes[arenaType] <= 0 or arenaType == common_new_pb.Champion) and 'x'..game_scheme:Arena(arenaType).ticketNum or ''
            self['Txt_Challenge'..index].gameObject:SetActive(net_arena_module.MineRemainFreeTimes[arenaType] > 0)
            --{heroID=, numProp={type=can be nil, lv=, starLv=}}
            otherScore[index] = tostring(rival.score)
            if not self['profile'..index] then
                local profile = face_item.CFaceItem()
                profile:Init(self['Img_PlayerIcon'..index].transform, nil, 1)
                self['profile'..index] = profile
            end

            self['profile'..index]:SetFaceInfo(rivalGroup[1].faceID, function()
                if arenaType == common_new_pb.Advance then
					-- 这里先显示最强阵容，应该要改成竞技场阵容
                    net_arena_module.Send_PLAYER_DETAILS(rivalGroup[1].roleID, common_new_pb.emDetailType_TopCE)

                    local ui_player_detail_info_ex = require "ui_player_detail_info_ex"
                    ui_player_detail_info_ex.SetHeroInfo(rivalGroup[1].faceID, rivalGroup[1].level,  rivalGroup[1].name, rivalGroup[1].roleID, rival.totalCE,nil,nil,nil,nil,rivalGroup[1].frameID)
                    ui_window_mgr:ShowModule("ui_player_detail_info_ex")
                else
                    --local rival = {rivalInfo = data.playerInfo}
                    local ui_team_confirm_battle = require "ui_team_confirm_battle"
                    ui_team_confirm_battle.Set4BattleConfirm(true, true)
                    while #rival.rivalInfo < 3 do
                        table.insert(rival.rivalInfo, rival.rivalInfo[1])
                    end
                    rival.totalCE = rival.totalCE
                    rival.teamName = rivalGroup[1].name

                    ui_team_confirm_battle.SetRivalInfo(rival)
                    ui_window_mgr:ShowModule("ui_team_confirm_battle")

                    net_arena_module.Send_PLAYER_DETAILS(rival.rivalInfo[1].roleID, common_new_pb.emDetailType_Advance)  -- 请求阵容信息
                end
            end)
            self['profile'..index]:SetNewBg(true)
            self['profile'..index]:SetActorLvText(true, rivalGroup[1].level)
            self['profile'..index]:SetFrameID(rivalGroup[1].frameID,true)
            self['profile'..index]:FrameEffectEnable(true, window.curOrder+1)

            local advArena = nil
            if rival.gradesID ~= nil then
                advArena = game_scheme:AdvArenaReward_0(rival.gradesID)
            end

            if advArena then
                if advArena ~= nil then
                    if advArena.StageType == 1 then
                        self['Text_Dan'..index].text = lang.Get(7191)
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    elseif advArena.StageType == 2 then
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7190)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    elseif advArena.StageType == 3 then
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7189)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    elseif advArena.StageType == 4 then
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7188)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    elseif advArena.StageType == 5 then
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7187)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    elseif advArena.StageType == 6 then
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7186)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    else
                        local sign = nil
                        if advArena.level == 9 then
                            sign =  "IX"
                        elseif advArena.level == 8 then
                            sign =  "VIII"
                        elseif advArena.level == 7 then
                            sign =  "VII"
                        elseif advArena.level == 6 then
                            sign = "VI"
                        elseif advArena.level == 5 then
                            sign = "V"
                        elseif advArena.level == 4 then
                            sign = "IV "
                        elseif advArena.level == 3 then
                            sign = "III"
                        elseif advArena.level == 2 then
                            sign = "II"
                        else
                            sign = "I"
                        end
                        self['Text_Dan'..index].text = lang.Get(7185)..sign
                        self['Text_Dan'..index].color = {r=1, g= 232/255, b= 75/255, a= 1}
                        self['Outline_Dan'..index].effectColor =  {r=102/255, g=79/255, b=10/255, a=1}
                    end
                end
            end
        else
            self["Img_Unit"..index].gameObject:SetActive(false)
        end
    end
end
--@endregion

--@region WindowClose
function UISingleChallenge:Close()
    if self.profile then
        self.profile:Dispose()
        self.profile = nil
    end
    local windowMgr = require "ui_window_mgr"
    windowMgr:UnloadModule("ui_activity_tips")

    if self.entity then
        self.entity.numProp:RemoveListener("goodsNum", self.arenaCoinGoodsNumChange)
        self.entity = nil
    end
    if self:IsValid() then
		self:UnsubscribeEvents()
	end
    for i = 1, 3 do
        if self["profile" .. i] then
            self["profile" .. i]:Dispose()
            self["profile" .. i] = nil
        end
    end
    for i=0,2 do
        if self["goods"..i] ~= nil then
            self["goods"..i]:Dispose()
        end
    end
    self.__base:Close()
    window = nil
    --@region User
    --@endregion
end --///<<< function

--@endregion

--@region ScrollItem

--@endregion

--@region WindowInherited
local CUISingleChallenge = class(ui_base, nil, UISingleChallenge)
--@endregion

--@region ModuleFunction
function Show()
    if window == nil then
        window = CUISingleChallenge()
        window._NAME = _NAME;window:LoadUIResource("ui/prefabs/uiseniorsinglechallenge.prefab", nil, nil, nil)
    end
    window:Show()
    return window
end

function InitialOffsetStep()
    initialOffset = initialOffset + 3
end

function SetArenaType(at)
    arenaType = at
end

function Hide()
    if window ~= nil then
        window:Hide()
    end
end

function Close()
    if window ~= nil then
        --Hide()
        window:Close()
        window = nil
    end
end

--@endregion

--@region RegisterMsg
--@endregion
function InfoUpdate(_, arenaType)
    if arenaType == common_new_pb.Advance then
        local check = function()
            if not window or window.UIRoot then
                return true
            end
        end

        util.Until(check, function()
            if window and window:IsValid() then
                window:UpdateOnReceiveInfo(--[[ranking, scores, totalCE]]net_arena_module.MineRank[arenaType], net_arena_module.MineScore[arenaType], net_arena_module.MineTotalCE[arenaType])
            end
        end)
    end


end
event.Register(event.ARENA_RIVAL_LIST, InfoUpdate)

function TicketCreated(_)
    if window and window:IsValid() then
        window:RefreshGoodsNum()
    end
end
event.Register(event.ARENA_TICKET_CREATE, TicketCreated)

function ClearDataOnChangeWorld()
    PlayerPrefs.SetString(getKey(), "")
end
event.Register(event.ACCOUNT_CHANGE_WORLD_RSP,ClearDataOnChangeWorld)