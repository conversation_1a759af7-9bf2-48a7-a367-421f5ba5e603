#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLuaBase.lua_CSFunction;
#endif

using XLua;
using XLua.CSObjectWrap;
using War.Script;
<%
require "TemplateCommon"
%>

namespace XLua.CSObjectWrap
{
    public static class XLuaWrapRegister
	{
        <%
        local split_method_perfix = 'Register'
        local split_method_count = 0
        local wrap_in_split_method = 0
        local max_wrap_in_split_method = 50
        %>
        <%ForEachCsList(wraps, function(wrap)%>
        <%if wrap_in_split_method == 0 then%>public static void <%=split_method_perfix%>()
        {
            LuaEnv luaEnv = LuaEnvironment.LuaEnv;
            UnityEngine.Debug.LogWarning($"XLua.CSObjectWrap.XLuaWrapRegister.Register(), {typeof(XLuaWrapRegister).Assembly.GetName().Name}");
        <%end%>
            <%=CSVariableName(wrap)%>Wrap.__Register(luaEnv.L);
        <%if wrap_in_split_method == max_wrap_in_split_method then
        wrap_in_split_method = 0
        split_method_count = split_method_count + 1
        %>
        }
        <%else
        wrap_in_split_method = wrap_in_split_method + 1
        end
        end)%>
        <% if generic_wraps then 
        for generic_def, instances in pairs(generic_wraps) do
        for _, args in ipairs(instances) do
        local generic_arg_list = "<"
        ForEachCsList(args, function(generic_arg, gai)
            if gai ~= 0 then generic_arg_list = generic_arg_list .. ", " end
            generic_arg_list = generic_arg_list .. CsFullTypeName(generic_arg)
        end)
        generic_arg_list = generic_arg_list .. ">"
        
        %>
        <%if wrap_in_split_method == 0 then%>public static void <%=split_method_perfix%>()
        {
            LuaEnv luaEnv = LuaEnvironment.LuaEnv;
        <%end%>
            <%=CSVariableName(generic_def)%>Wrap<%=generic_arg_list%>.__Register(luaEnv.L);
        <%if wrap_in_split_method == max_wrap_in_split_method then
        wrap_in_split_method = 0
        split_method_count = split_method_count + 1
        %>
        }
        <%else
        wrap_in_split_method = wrap_in_split_method + 1
        end
        end
        end
        end%>
        
        <%if wrap_in_split_method ~= 0 then
        split_method_count = split_method_count + 1
        %>}<%end%>
	}
}
