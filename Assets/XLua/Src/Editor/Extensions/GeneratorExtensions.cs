using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;
using FBoard;
using UnityEditor;
using UnityEngine;
using XLua;

namespace CSObjectWrapEditor
{
    public static class GeneratorExtensions
    {
        private static Dictionary<string, string> _assemblyName2Path = new Dictionary<string, string>();
        private const string DefaultAssemblyPath = "Assets/Scripts/Assembly-CSharp.asmdef";
        private const string DefaultAssembly = "Assembly-CSharp";
        public static List<Type> LuaCallCSharp = null;

        [MenuItem("XLua/Generate Assets Code", false, 1)]
        public static void GenAssetsCode()
        {
            ClearGenCode();
            GetGenConfig(Utils.GetAllTypes());
        }

        [MenuItem("XLua/Clear Assets GenCode", false, 1)]
        public static void ClearGenCode()
        {
            string[] folders = Directory.GetDirectories("Assets", "XLuaGen", SearchOption.AllDirectories);
            foreach (string folder in folders)
            {
                Debug.LogWarning($"delete dir: {folder}");
                Directory.Delete(folder, true);
                string metaFile = $"{folder}.meta";
                if (File.Exists(metaFile))
                {
                    File.Delete(metaFile);
                }
            }
            AssetDatabase.Refresh();
        }
        
        public static void ClearAssemblyCache()
        {
            _assemblyName2Path.Clear();
        }
        
        public static bool IsInAssets(Type type)
        {
            if (_assemblyName2Path.Count == 0)
            {
                _assemblyName2Path = GetAssemblyMap();
            }
            
            string name = type.Assembly.GetName().Name;
            if (!_assemblyName2Path.TryGetValue(name, out var assemblyPath) || !assemblyPath.StartsWith("Assets/"))
            {
                return false;
            }
            return true;
        }
        
        public static Dictionary<string, string> GetAssemblyMap()
        {
            string[] guids = AssetDatabase.FindAssets("t:asmdef");
            Dictionary<string, string> assemblyMap = new Dictionary<string, string>(); 
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                path = path.Replace(@"\" , "/");
                string fileName = Path.GetFileNameWithoutExtension(path);
                assemblyMap[fileName] = path;
                Debug.Log($"fileName: {fileName}, {path}");
            }
            assemblyMap[DefaultAssembly] = DefaultAssemblyPath;
            return assemblyMap;
        }
        
        public static void GetGenConfig(IEnumerable<Type> check_types)
        {
            LuaCallCSharp = new List<Type>();
            
            Generator.BlackList = new List<List<string>>();
            Generator.memberFilters = new List<Func<MemberInfo, bool>>();
            Generator.DoNotGen = new Dictionary<Type, HashSet<string>>();
            Generator.AdditionalProperties = new Dictionary<Type, List<string>>();
            ClearAssemblyCache();
            
            foreach (var t in check_types)
            {
                if (!IsInAssets(t))
                {
                    Debug.LogWarning($"({t.FullName},{t.Assembly.GetName().Name}) is not in assets directory, skip");
                    continue;
                }
                
                MergeCfg(t, null, () => t);

                if (!t.IsAbstract || !t.IsSealed) continue;

                var fields = t.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.DeclaredOnly);
                for (int i = 0; i < fields.Length; i++)
                {
                    var field = fields[i];
                    MergeCfg(field, field.FieldType, () => field.GetValue(null));
                }

                var props = t.GetProperties(BindingFlags.Static | BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.DeclaredOnly);
                for (int i = 0; i < props.Length; i++)
                {
                    var prop = props[i];
                    MergeCfg(prop, prop.PropertyType, () => prop.GetValue(null, null));
                }
            }
            
            LuaCallCSharp = LuaCallCSharp.Distinct()
                .Where(type=> Generator.IsPublic(type) && !Generator.isObsolete(type) && !type.IsGenericTypeDefinition)
                .Where(type => !typeof(Delegate).IsAssignableFrom(type))
                .Where(type => !type.Name.Contains("<"))
                .ToList();
            
            Dictionary<string, string> name2Path = GetAssemblyMap();
            Dictionary<string, List<Type>> assembly2TypeList = new Dictionary<string, List<Type>>();
            foreach (var type in LuaCallCSharp)
            {
                string assemblyName = type.Assembly.GetName().Name;
                if (!name2Path.TryGetValue(assemblyName, out string assemblyPath))
                {
                    Debug.LogError($"can not find assembly path, assemblyName: {assemblyName}, type: {type.FullName}");
                    continue;
                }
                //Packages目录的脚本生成到目录Assets/Scripts/XLuaGen，确保Packages下的程序集勾选Auto Reference, 否则会报错
                if (assemblyPath.StartsWith("Packages/")) 
                {
                    if (assemblyName == "Unity.Entities")
                    {
                        assemblyName = "Entities.Hybrid";
                    }
                    else
                    {
                        assemblyName = DefaultAssembly;   
                    }
                }
                
                if (!assembly2TypeList.TryGetValue(assemblyName, out var typeList))
                {
                    typeList = new List<Type>();
                    assembly2TypeList[assemblyName] = typeList;
                }
                typeList.Add(type);
            }

            if (assembly2TypeList.Count > 0)
                GenCode(assembly2TypeList, name2Path);
        }

        public static void MergeCfg(MemberInfo test, Type cfg_type, Func<object> get_cfg)
        {
            if (Generator.isDefined(test, typeof(LuaCallCSharpAttribute)))
            {
                object ccla = GetCustomAttribute(test, typeof(LuaCallCSharpAttribute));
                AddToList(LuaCallCSharp, get_cfg, ccla);
            }
            
            if (Generator.isDefined(test, typeof(BlackListAttribute))
                && (typeof(List<List<string>>)).IsAssignableFrom(cfg_type))
            {
                Generator.BlackList.AddRange(get_cfg() as List<List<string>>);
            }
            if (Generator.isDefined(test, typeof(BlackListAttribute)) && typeof(Func<MemberInfo, bool>).IsAssignableFrom(cfg_type))
            {
                Generator.memberFilters.Add(get_cfg() as Func<MemberInfo, bool>);
            }

            if (Generator.isDefined(test, typeof(AdditionalPropertiesAttribute))
                && (typeof(Dictionary<Type, List<string>>)).IsAssignableFrom(cfg_type))
            {
                var cfg = get_cfg() as Dictionary<Type, List<string>>;
                foreach (var kv in cfg)
                {
                    if (!Generator.AdditionalProperties.ContainsKey(kv.Key))
                    {
                        Generator.AdditionalProperties.Add(kv.Key, kv.Value);
                    }
                }
            }

            if (Generator.isDefined(test, typeof(DoNotGenAttribute))
                && (typeof(Dictionary<Type, List<string>>)).IsAssignableFrom(cfg_type))
            {
                var cfg = get_cfg() as Dictionary<Type, List<string>>;
                foreach (var kv in cfg)
                {
                    HashSet<string> set;
                    if (!Generator.DoNotGen.TryGetValue(kv.Key, out set))
                    {
                        set = new HashSet<string>();
                        Generator.DoNotGen.Add(kv.Key, set);
                    }
                    set.UnionWith(kv.Value);
                }
            }
        }
        
        private static void AddToList(List<Type> list, Func<object> get, object attr)
        {
            object obj = get();
            if (obj is Type)
            {
                list.Add(obj as Type);
            }
            else if (obj is IEnumerable<Type>)
            {
                list.AddRange(obj as IEnumerable<Type>);
            }
            else if (obj is Dictionary<Type, List<string>> dic)
            {
                list.AddRange(dic.Keys);
            }
            else
            {
                throw new InvalidOperationException("Only field/property with the type IEnumerable<Type> can be marked " + attr.GetType().Name);
            }
        }
        
        private static object GetCustomAttribute(MemberInfo test, Type type)
        {
            return test.GetCustomAttributes(type, false).FirstOrDefault();
        }
        
        //目前仅生成LuaCallC#代码，没有C#CallLua
        public static void GenCode(Dictionary<string, List<Type>> assembly2TypeList, Dictionary<string, string> name2Path)
        {
            SaveRegisterTypeToConfig(assembly2TypeList, name2Path);
            
            foreach (var pair in assembly2TypeList)
            {
                string assemblyName = pair.Key;
                if (!name2Path.TryGetValue(assemblyName, out string assemblyPath))
                {
                    Debug.LogError($"can not find assembly path, assemblyName: {assemblyName}");
                    continue;
                }

                assemblyPath = assemblyPath.Replace(@"\" , "/");
                string dir = Path.GetDirectoryName(assemblyPath);
                string genPath = $"{dir}/XLuaGen";
                Gen(pair.Key, pair.Value, genPath);
            }   
            
            Debug.Log("Gen assets xlua wrap finished!");
            AssetDatabase.Refresh();
        }

        public static void SaveRegisterTypeToConfig(Dictionary<string, List<Type>> assembly2TypeList, Dictionary<string, string> name2Path)
        {
            ConfigMethod configMethod = new ConfigMethod();
            configMethod.methodEntries = new List<ConfigMethodEntry>();
            foreach (var pair in assembly2TypeList)
            {
                string assemblyName = pair.Key;
                if (!name2Path.TryGetValue(assemblyName, out string assemblyPath))
                {
                    Debug.LogError($"can not find assembly path, assemblyName: {assemblyName}");
                    continue;
                }

                string typeNamespace = assemblyName.Replace("-", "_");
                ConfigMethodEntry entry = new ConfigMethodEntry();
                entry.assemblyName = assemblyName;
                entry.typeName = $"{typeNamespace}.XLuaWrapRegister";
                entry.id = entry.typeName;
                entry.methodName = MethodAutoRegistrar.NONE;
                configMethod.methodEntries.Add(entry);
            }
            string content = Newtonsoft.Json.JsonConvert.SerializeObject(configMethod);
            string path = $"Assets/{MethodAutoRegisterCollector.AUTO_CONFIG_PATH}";
            var dir = Path.GetDirectoryName(path);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
                AssetDatabase.Refresh();
            }
            
            File.WriteAllText(path, content);
            Debug.Log($"SaveRegisterTypeToConfig success, filePath {path}, content: {content}");
            AssetDatabase.Refresh();
        }
        
        public static void Gen(string assemblyName, List<Type> typeList, string genPath)
        {
            var start = DateTime.Now;
            if (Directory.Exists(genPath))
            {
                Directory.Delete(genPath, true);
            }
            Directory.CreateDirectory(genPath);
            AssetDatabase.Refresh();

            Generator.LuaCallCSharp = typeList;
            if (Generator.memberFilters == null)
                Generator.memberFilters = new List<Func<MemberInfo, bool>>();   

            if (Generator.BlackList == null)
                Generator.BlackList = new List<List<string>>();
            
            if (Generator.DoNotGen == null)
                Generator.DoNotGen = new Dictionary<Type, HashSet<string>>();
            
            Generator.GCOptimizeList = new List<Type>();
            Generator.CSharpCallLua = new List<Type>();
            Generator.ReflectionUse = new List<Type>();
                
            var luaenv = Generator.luaenv;
            var gcOptimizeList = Generator.GCOptimizeList;
            
            luaenv.DoString("require 'TemplateCommon'");
            var gen_push_types_setter = luaenv.Global.Get<LuaFunction>("SetGenPushAndUpdateTypes");
            gen_push_types_setter.Call(gcOptimizeList.Where(t => !t.IsPrimitive && Generator.SizeOf(t) != -1).Concat(Generator.LuaCallCSharp.Where(t => t.IsEnum)).Distinct().ToList());
            var xlua_classes_setter = luaenv.Global.Get<LuaFunction>("SetXLuaClasses");
            xlua_classes_setter.Call(Utils.GetAllTypes().Where(t => t.Namespace == "XLua").ToList());

            string path = $"{genPath}/";
            Generator.Gen(typeList, new List<Type>(), new List<Type>().Distinct(), path);
            DeleteNoUseGenScript(genPath);
            GenLuaRegister(assemblyName, genPath);
            Debug.Log($"Gen assets xlua wrap finished for assembly ({assemblyName})! use {(DateTime.Now - start).TotalMilliseconds} ms");
        }
        
        public static void DeleteNoUseGenScript(string genPath)
        {
            string wrapPusherPath = $"{genPath}/WrapPusher.cs";
            if (File.Exists(wrapPusherPath))
            {
                File.Delete(wrapPusherPath);
            }
            string packUnpackPath = $"{genPath}/PackUnpack.cs";
            if (File.Exists(packUnpackPath))
            {
                File.Delete(packUnpackPath);
            }
        }
        
        public static void GenLuaRegister(string assemblyName, string genPath)
        {
            var wraps = Generator.LuaCallCSharp;

            var itf_bridges = Generator.CSharpCallLua.Where(t => t.IsInterface);

            string filePath = $"{genPath}/XLuaWrapRegister.cs";
            StreamWriter textWriter = new StreamWriter(filePath, false, Encoding.UTF8);

            var lookup = Generator.LuaCallCSharp.Distinct().ToDictionary(t => t);

            var extension_methods_from_lcs = (from t in Generator.LuaCallCSharp
                                    where Generator.isDefined(t, typeof(ExtensionAttribute))
                                    from method in t.GetMethods(BindingFlags.Static | BindingFlags.Public)
                                    where Generator.isDefined(method, typeof(ExtensionAttribute)) && !Generator.isObsolete(method)
                                    where !method.ContainsGenericParameters || Generator.isSupportedGenericMethod(method)
                                    select Generator.makeGenericMethodIfNeeded(method))
                                    .Where(method => !lookup.ContainsKey(method.GetParameters()[0].ParameterType));

            var extension_methods = (from t in Generator.ReflectionUse
                                     where Generator.isDefined(t, typeof(ExtensionAttribute))
                                     from method in t.GetMethods(BindingFlags.Static | BindingFlags.Public)
                                     where Generator.isDefined(method, typeof(ExtensionAttribute)) && !Generator.isObsolete(method)
                                     where !method.ContainsGenericParameters || Generator.isSupportedGenericMethod(method)
                                     select Generator.makeGenericMethodIfNeeded(method)).Concat(extension_methods_from_lcs);

            TextAsset textAsset = AssetDatabase.LoadAssetAtPath<TextAsset>("Assets/XLua/Src/Editor/Extensions/XLuaWrapRegister.tpl.txt");
            XLuaTemplate xluaRegister = new XLuaTemplate();
            xluaRegister.name = textAsset.name;
            xluaRegister.text = textAsset.text;
            
            GenOne(assemblyName, typeof(DelegateBridgeBase), (type, type_info) =>
            {
#if GENERIC_SHARING
                type_info.Set("wraps", wraps.Where(t=>!t.IsGenericType).ToList());
                var genericTypeGroups = wraps.Where(t => t.IsGenericType).GroupBy(t => t.GetGenericTypeDefinition());

                var typeToArgsList = Generator.luaenv.NewTable();
                foreach (var genericTypeGroup in genericTypeGroups)
                {
                    var argsList = Generator.luaenv.NewTable();
                    int i = 1;
                    foreach(var genericType in genericTypeGroup)
                    {
                        argsList.Set(i++, genericType.GetGenericArguments());
                    }
                    typeToArgsList.Set(genericTypeGroup.Key, argsList);
                    argsList.Dispose();
                }

                type_info.Set("generic_wraps", typeToArgsList);
                typeToArgsList.Dispose();
#else
                type_info.Set("wraps", wraps.ToList());
#endif

                type_info.Set("itf_bridges", itf_bridges.ToList());
                type_info.Set("extension_methods", extension_methods.ToList());
            }, xluaRegister, textWriter);
            textWriter.Close();
        }
        
        public static void GenOne(string assemblyName, Type type, Action<Type, LuaTable> type_info_getter, XLuaTemplate templateAsset, StreamWriter textWriter)
        {
            if (Generator.isObsolete(type)) return;
            LuaFunction template;
            if (!Generator.templateCache.TryGetValue(templateAsset.name, out template))
            {
                template = XLua.TemplateEngine.LuaTemplate.Compile(Generator.luaenv, templateAsset.text);
                Generator.templateCache[templateAsset.name] = template;
            }

            LuaTable type_info = Generator.luaenv.NewTable();
            LuaTable meta = Generator.luaenv.NewTable();
            meta.Set("__index", Generator.luaenv.Global);
            type_info.SetMetaTable(meta);
            meta.Dispose();

            type_info_getter(type, type_info);

            try
            {
                string genCode = XLua.TemplateEngine.LuaTemplate.Execute(template, type_info);
                string typeNamespace = assemblyName.Replace("-", "_");
                genCode = genCode.Replace("namespace XLua.CSObjectWrap", $"namespace {typeNamespace}");
                genCode = genCode.Replace("XLua.CSObjectWrap.XLuaWrapRegister", $"{assemblyName}.XLuaWrapRegister");
                textWriter.Write(genCode);
                textWriter.Flush();
            }
            catch (Exception e)
            {
                Debug.LogError("gen wrap file fail! err=" + e.Message + ", stack=" + e.StackTrace);
            }
            finally
            {
                type_info.Dispose();
            }
        }
    }
}