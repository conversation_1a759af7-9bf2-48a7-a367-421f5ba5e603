using UnityEngine;
using UnityEditor;
using System.IO;

public class TerrainWeightmapExporter : EditorWindow
{
    private Terrain targetTerrain;
    private string savePath = "";
    private int textureWidth = 512;
    private int textureHeight = 512;

    [MenuItem("SToolCollection/Tool/Terrain/Export Weightmaps")]
    public static void ShowWindow()
    {
        GetWindow<TerrainWeightmapExporter>("Export Weightmaps");
    }

    void OnGUI()
    {
        GUILayout.Label("Terrain Weightmap Exporter", EditorStyles.boldLabel);

        targetTerrain = (Terrain)EditorGUILayout.ObjectField("Target Terrain", targetTerrain, typeof(Terrain), true);

        textureWidth = EditorGUILayout.IntField("Texture Width", textureWidth);
        textureHeight = EditorGUILayout.IntField("Texture Height", textureHeight);

        GUILayout.Space(10);

        if (GUILayout.Button("Choose Save Folder"))
        {
            savePath = EditorUtility.SaveFolderPanel("Select Save Folder", Application.dataPath, "");
        }

        GUILayout.Label("Save Path: " + savePath);

        GUILayout.Space(20);

        if (GUILayout.Button("Export Weightmaps"))
        {
            if (targetTerrain == null)
            {
                EditorUtility.DisplayDialog("Error", "Please assign a Terrain!", "OK");
                return;
            }

            if (string.IsNullOrEmpty(savePath))
            {
                EditorUtility.DisplayDialog("Error", "Please select a save folder!", "OK");
                return;
            }

            ExportWeightmaps();
        }
    }

    private void ExportWeightmaps()
    {
        TerrainData terrainData = targetTerrain.terrainData;
        int alphamapWidth = terrainData.alphamapWidth;
        int alphamapHeight = terrainData.alphamapHeight;
        int layerCount =  terrainData.alphamapLayers;

        for (int layer = 0; layer < 1; layer++)
        {
            Texture2D sourceTexture =  terrainData.GetAlphamapTexture(layer);

            // �����ߴ�
            Texture2D resizedTexture = sourceTexture;
            if (textureWidth != alphamapWidth || textureHeight != alphamapHeight)
            {
                resizedTexture = ResizeTexture(sourceTexture, textureWidth, textureHeight);
                DestroyImmediate(sourceTexture);
            }

            // �����ļ�
            byte[] pngData = resizedTexture.EncodeToPNG();
            string layerName = terrainData.terrainLayers[layer].name;
            string safeLayerName = string.Join("_", layerName.Split(Path.GetInvalidFileNameChars()));
            string fileName = $"{targetTerrain.name}.png";
            string fullPath = Path.Combine(savePath, fileName);

            File.WriteAllBytes(fullPath, pngData);
            DestroyImmediate(resizedTexture);
        }

        // ˢ��Asset���ݿ⣨���������AssetsĿ¼�£�
        if (savePath.StartsWith(Application.dataPath))
        {
            string relativePath = "Assets" + savePath.Substring(Application.dataPath.Length);
            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("Success", $"Exported {layerCount} weightmaps to:\n{relativePath}", "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Success", $"Exported {layerCount} weightmaps to:\n{savePath}", "OK");
        }
    }

    private Texture2D ResizeTexture(Texture2D source, int newWidth, int newHeight)
    {
        Texture2D result = new Texture2D(newWidth, newHeight, source.format, false);

        float ratioX = (float)source.width / newWidth;
        float ratioY = (float)source.height / newHeight;

        for (int y = 0; y < newHeight; y++)
        {
            for (int x = 0; x < newWidth; x++)
            {
                int srcX = Mathf.FloorToInt(x * ratioX);
                int srcY = Mathf.FloorToInt(y * ratioY);
                srcX = Mathf.Clamp(srcX, 0, source.width - 1);
                srcY = Mathf.Clamp(srcY, 0, source.height - 1);
                result.SetPixel(x, y, source.GetPixel(srcX, srcY));
            }
        }

        result.Apply();
        return result;
    }
}