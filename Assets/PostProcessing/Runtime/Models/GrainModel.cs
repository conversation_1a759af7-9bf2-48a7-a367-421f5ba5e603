using System;

namespace UnityEngine.PostProcessing
{
    [Serializable]
    public class GrainModel : PostProcessingModel
    {
        [Serializable]
        public struct Settings
        {
            [<PERSON><PERSON><PERSON>("Enable the use of colored grain.")]
            public bool colored;

            [Range(0f, 1f), Toolt<PERSON>("Grain strength. Higher means more visible grain.")]
            public float intensity;

            [Range(0.3f, 3f), Toolt<PERSON>("Grain particle size.")]
            public float size;

            [Range(0f, 1f), <PERSON><PERSON><PERSON>("Controls the noisiness response curve based on scene luminance. Lower values mean less noise in dark areas.")]
            public float luminanceContribution;

            public static Settings defaultSettings
            {
                get
                {
                    return new Settings
                    {
                        colored = true,
                        intensity = 0.5f,
                        size = 1f,
                        luminanceContribution = 0.8f
                    };
                }
            }
        }

        [SerializeField]
        Settings m_Settings = Settings.defaultSettings;
        public Settings settings
        {
            get { return m_Settings; }
            set { m_Settings = value; }
        }

        public override void Reset()
        {
            m_Settings = Settings.defaultSettings;
        }
    }
}
