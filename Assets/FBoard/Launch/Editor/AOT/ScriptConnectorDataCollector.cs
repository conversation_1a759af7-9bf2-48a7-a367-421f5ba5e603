using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using Newtonsoft.Json;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace FBoard
{
    public static class ScriptConnectorDataCollector
    {
        public const string TYPE_ASSEMBLY_MAP_PATH = "Assets/editorconfig/launch/tempregister.txt";

        //[MenuItem("Launch/CheckScriptConnectorData")]
        public static void CheckField()
        {
            if (File.Exists(TYPE_ASSEMBLY_MAP_PATH))
            {
                File.Delete(TYPE_ASSEMBLY_MAP_PATH);
            }

            Dictionary<string, string> typeName2Assembly = new Dictionary<string, string>();
            List<ConfigMethodEntry> list = new List<ConfigMethodEntry>();
            string[] guids = AssetDatabase.FindAssets("t:prefab");
            float index = 0;
            int count = guids.Length;

            Type connectorType = typeof(ScriptConnector);
            FieldInfo typeFullNameField =
                connectorType.GetField("typeFullName", BindingFlags.NonPublic | BindingFlags.Instance);
            FieldInfo fieldAbNamesField =
                connectorType.GetField("fieldAbNames", BindingFlags.NonPublic | BindingFlags.Instance);
            
            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                index++;
                EditorUtility.DisplayProgressBar("ScriptConnectorDataCollector", path, index / count);
                GameObject gameObject = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (gameObject == null)
                {
                    Debug.LogError("资源异常，path:{path}");
                    continue;   
                }
                ScriptConnector[] connectors = gameObject.GetComponentsInChildren<ScriptConnector>(true);
                if (connectors != null)
                {
                    foreach (var connector in connectors)
                    {
                        string scriptPath = AssetDatabase.GUIDToAssetPath(connector.TypeGuid);
                        if (!string.IsNullOrEmpty(scriptPath))
                        {
                            CheckFieldAbName(connector, gameObject, fieldAbNamesField);
                            
                            long fileID = connector.TypeFileID;
                            Type type = null;
                            if (fileID != ScriptConnectorEditor.ScriptComFileID ||
                                scriptPath.EndsWith(ScriptConnectorEditor.DllSuffix))
                            {
                                Object[] assets = AssetDatabase.LoadAllAssetsAtPath(scriptPath);
                                foreach (var asset in assets)
                                {
                                    if (asset.GetFileId() == fileID && asset is MonoScript monoScript)
                                    {
                                        type = monoScript.GetClass();
                                        break;
                                    }
                                }
                            }
                            else
                            {
                                MonoScript monoScript = AssetDatabase.LoadAssetAtPath<MonoScript>(scriptPath);
                                if (monoScript != null)
                                {
                                    type = monoScript.GetClass();
                                }
                                else
                                {
                                    Debug.LogError(
                                        $"monoScript is null from {scriptPath} load, typeGuid: {connector.TypeGuid}, prefabPath: {path}");
                                }
                            }

                            if (type != null)
                            {
                                if (type.FullName != connector.LogicTypeName)
                                {
                                    //校正类名
                                    //connector.SetTypeFullName(type.FullName);
                                    if (typeFullNameField != null)
                                    {
                                        typeFullNameField.SetValue(connector, type.FullName);
                                        EditorUtility.SetDirty(gameObject);   
                                    }
                                    else
                                    {
                                        Debug.LogError("Field typeFullName can not find in Type ScriptConnector");
                                    }
                                }

                                string typeFullName = string.IsNullOrEmpty(type.FullName) ? type.Name : type.FullName;
                                if (!typeName2Assembly.ContainsKey(typeFullName))
                                {
                                    typeName2Assembly[typeFullName] = type.Assembly.GetName().Name;
                                    ConfigMethodEntry entry = new ConfigMethodEntry();
                                    entry.id = type.Name;
                                    entry.typeName = typeFullName;
                                    entry.assemblyName = type.Assembly.GetName().Name;
                                    entry.methodName = MethodAutoRegistrar.ALL;
                                    list.Add(entry);
                                    Debug.Log(
                                        $"add assembly map for typeName : {type.Name}, typeFullName: {typeFullName}, assemblyName: {type.Assembly.GetName().Name}");
                                }
                            }
                            else
                            {
                                Debug.LogError(
                                    $"type is null from {scriptPath}, typeGuid: {connector.TypeGuid}, typeFileID: {fileID}, prefabPath: {path}");
                            }
                        }
                        else
                        {
                            Debug.LogError(
                                $"Asset is not exist in ScriptConnectorDataCollector for guid: {connector.TypeGuid}, prefabPath: {path}");
                        }
                    }
                }
            }

            EditorUtility.ClearProgressBar();
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(list, Formatting.Indented);
            File.WriteAllText(TYPE_ASSEMBLY_MAP_PATH, json);

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            Debug.Log("Collect type and assembly map finish");
        }

        private static void CheckFieldAbName(ScriptConnector connector, GameObject gameObject, FieldInfo fieldAbNamesField)
        {
            if (connector.FieldGuids != null && connector.FieldGuids.Length > 0)
            {
                int len = connector.FieldGuids.Length;
                if (connector.FieldAbNames == null || connector.FieldAbNames.Length != len)
                {
                    string[] paths = new string[len];
                    if (fieldAbNamesField != null)
                    {
                        fieldAbNamesField.SetValue(connector, paths);
                        EditorUtility.SetDirty(gameObject);   
                    }
                    else
                    {
                        Debug.LogError("Field fieldAbNames can not find in Type ScriptConnector");
                    }
                }
                
                if (connector.FieldAbNames == null)
                {
                    Debug.LogError($"FieldAbNames is null in gameObject: {gameObject}");    
                    return;
                }
                
                for (int i = 0; i < len; i++)
                {
                    string path = AssetDatabase.GUIDToAssetPath(connector.FieldGuids[i]);
                    string abName = path.Replace("Assets/","").ToLower();
                    if (connector.FieldAbNames[i] != abName)
                    {
                        //校正路径
                        //connector.SetFilePath(i, path);
                        if (fieldAbNamesField != null)
                        {
                            string[] curPaths = (string[])fieldAbNamesField.GetValue(connector);
                            curPaths[i] = abName;
                            fieldAbNamesField.SetValue(connector, curPaths);
                            EditorUtility.SetDirty(gameObject);   
                        }
                        else
                        {
                            Debug.LogError("Field fieldAbNames can not find in Type ScriptConnector");
                        }
                    }
                }   
            }
        }
    }
}
