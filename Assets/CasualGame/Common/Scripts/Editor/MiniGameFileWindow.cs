using Sirenix.Utilities.Editor;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using TMPro;
using UnityEditor;
using UnityEngine;

namespace bc.MiniGameBase
{
    public class MiniGameFileWindow : EditorWindow
    {
        private string targetFolder = string.Empty;
        private List<string> filterFolders = new List<string>();
        private string filterExtensionStr = string.Empty;
        private TMP_FontAsset tmpFontAsset;

        [MenuItem("Tools/С��Ϸ�ļ�����")]
        public static void OpenWindow()
        {
            MiniGameFileWindow window = EditorWindow.GetWindow<MiniGameFileWindow>();
            window.titleContent = new GUIContent("С��Ϸ�ļ�����");
        }

        private void OnGUI()
        {
            EditorGUILayout.BeginVertical();
            {
                var rect = EditorGUILayout.GetControlRect();
                targetFolder = SirenixEditorFields.FolderPathField(rect, new GUIContent("Ŀ���ļ���"), targetFolder, string.Empty, false, false);

                DrawLine();

                filterExtensionStr = EditorGUILayout.TextField(new GUIContent("��Դ��������(t:prefab t:texture t:audioclip)"), filterExtensionStr);

                EditorGUILayout.BeginHorizontal();
                {
                    GUILayout.Label("�����ļ���", GUILayout.ExpandWidth(false));
                    if (GUILayout.Button("���", GUILayout.Width(80)))
                    {
                        filterFolders.Add(string.Empty);
                    }
                }
                EditorGUILayout.EndHorizontal();

                GUILayout.Space(5);
                GUILayout.Button("", GUILayout.Height(2));

                EditorGUILayout.BeginVertical();
                {
                    for (int i = filterFolders.Count - 1; i >= 0; i--)
                    {
                        EditorGUILayout.BeginHorizontal();
                        {
                            rect = EditorGUILayout.GetControlRect();
                            filterFolders[i] = SirenixEditorFields.FolderPathField(rect, new GUIContent(""), filterFolders[i], string.Empty, false, false);
                            if (GUILayout.Button("X", GUILayout.Width(20)))
                            {
                                filterFolders.RemoveAt(i);
                                GUIUtility.ExitGUI();
                            }
                        }
                        EditorGUILayout.EndHorizontal();
                    }
                }
                EditorGUILayout.EndVertical();

                GUILayout.Button("", GUILayout.Height(2));
                GUILayout.Space(5);

                if (GUILayout.Button("���δʹ����Դ"))
                {
                    FindUnusedAssets();
                }
                if (GUILayout.Button("����δʹ����Դ"))
                {
                    ClearUnusedAssets();
                }
                if (GUILayout.Button("������ļ���"))
                {
                    DirectoryInfo dir = new DirectoryInfo(targetFolder);
                    DeleteEmptyDirectory(dir);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }

                DrawLine();

                if (GUILayout.Button("�滻lua�ļ���"))
                {
                    ReplaceLuaExtension();
                }

                tmpFontAsset = (TMP_FontAsset)EditorGUILayout.ObjectField("TMP����", tmpFontAsset, typeof(TMP_FontAsset), true);
                if (GUILayout.Button("�滻TMP���SDF") && tmpFontAsset != null)
                {
                    ReplaceSDF();
                }
            }
            EditorGUILayout.EndVertical();
        }

        private void DrawLine()
        {
            GUILayout.Space(2f);
            EditorGUISimpleTemplate.DrawColorLine();
            GUILayout.Space(2f);
        }

        private HashSet<string> filterExtension = new HashSet<string>() {
            ".lua", ".cs", ".txt"
        };

        private void ClearUnusedAssets()
        {
            var unusedAssetsPath = GetUnusedAssets();
            var outFailedPaths = new List<string>();
            AssetDatabase.DeleteAssets(unusedAssetsPath.ToArray(), outFailedPaths);

            foreach (var path in outFailedPaths)
            {
                var obj = AssetDatabase.LoadAssetAtPath(path, typeof(Object));
                Debug.LogError($"ɾ��ʧ�� {path}", obj);
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void DeleteEmptyDirectory(DirectoryInfo dir)
        {
            DirectoryInfo[] subdirs = dir.GetDirectories();
            foreach (DirectoryInfo subdir in subdirs)
            {
                DeleteEmptyDirectory(subdir);
            }
            FileSystemInfo[] files = dir.GetFileSystemInfos();
            if (files.Length == 0)
            {
                AssetDatabase.DeleteAsset(FileEditorUtil.GetUnityCanUsePath(dir.FullName));
            }
        }

        private void FindUnusedAssets()
        {
            var unusedAssetsPath = GetUnusedAssets();

            StringBuilder strB = new StringBuilder();
            strB.AppendLine("·��,��չ��");
            foreach (var path in unusedAssetsPath)
            {
                string extension = Path.GetExtension(path);
                strB.AppendLine($"{path},{extension}");
            }

            string outPutPath = "Assets/δʹ����Դ��ѯ���.csv";
            FileEditorUtil.SafeWriteAllText(outPutPath, strB.ToString());
            Debug.LogError($"��ѯ�������� {outPutPath}");
        }

        private HashSet<string> GetUnusedAssets()
        {
            var guids = AssetDatabase.FindAssets(filterExtensionStr, new string[] { targetFolder });
            HashSet<string> usedAssetsPath = new HashSet<string>();
            HashSet<string> maybeUnusedAssetsPath = new HashSet<string>();
            HashSet<string> unusedAssetsPath = new HashSet<string>();

            foreach (var guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                string extension = Path.GetExtension(path);
                if (string.IsNullOrEmpty(extension))
                {
                    continue;
                }
                if (filterExtension.Contains(extension))
                {
                    usedAssetsPath.Add(path);
                    continue;
                }

                var abname = AssetDatabase.GetImplicitAssetBundleName(path);
                if (!string.IsNullOrEmpty(abname))
                {
                    var depends = AssetDatabase.GetDependencies(path);
                    foreach (var depend in depends)
                    {
                        usedAssetsPath.Add(depend);
                    }
                    usedAssetsPath.Add(path);
                }
                else if (!usedAssetsPath.Contains(path))
                {
                    maybeUnusedAssetsPath.Add(path);
                }
            }

            foreach (var path in maybeUnusedAssetsPath)
            {
                if (usedAssetsPath.Contains(path))
                {
                    continue;
                }
                bool isFilter = false;
                foreach (var filterFol in filterFolders)
                {
                    if (path.StartsWith(filterFol))
                    {
                        isFilter = true;
                        break;
                    }
                }
                if (!isFilter)
                {
                    unusedAssetsPath.Add(path);
                }
            }

            return unusedAssetsPath;
        }

        private void ReplaceLuaExtension()
        {
            DirectoryInfo dir = new DirectoryInfo(targetFolder);
            var luaFiles = dir.GetFiles("*.lua", SearchOption.AllDirectories);
            foreach (var luaFile in luaFiles)
            {
                string newPath = luaFile.FullName.Substring(0, luaFile.FullName.LastIndexOf(".")) + ".txt";
                luaFile.MoveTo(newPath);
            }
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private void ReplaceSDF()
        {
            try
            {
                EditorUtility.DisplayProgressBar("TMP�����滻", "�����滻��...", 0);
                string[] asstIds = AssetDatabase.FindAssets("t:Prefab", new string[1] { targetFolder });
                if (asstIds.Length == 0)
                {
                    return;
                }

                List<GameObject> inputTextList = new List<GameObject>(5);
                List<TMP_Text> textList = new List<TMP_Text>(5);

                int prefabCount = 0;//Ԥ��������
                int tmpCount = 0;//tmp�������
                for (int i = 0; i < asstIds.Length; i++)
                {
                    string path = AssetDatabase.GUIDToAssetPath(asstIds[i]);
                    var prefab = PrefabUtility.LoadPrefabContents(path);
                    if (prefab == null)
                    {
                        Debug.LogError($"����Ԥ����ʧ��: {path}");
                        continue;
                    }
                    Debug.Log($"��ʼ�滻��{prefabCount + 1}��Ԥ����, path: {path}");

                    var inputs = prefab.GetComponentsInChildren<TMP_InputField>(true);
                    if (inputs.Length > 0)
                    {
                        foreach (var input in inputs)
                        {
                            input.fontAsset = tmpFontAsset;
                        }
                    }

                    var tmpComs = prefab.GetComponentsInChildren<TMP_Text>(true);
                    foreach (var text in tmpComs)
                    {
                        text.font = tmpFontAsset;
                    }

                    PrefabUtility.SaveAsPrefabAsset(prefab, path, out bool bSuccess);

                    if (bSuccess)
                    {
                        prefabCount++;
                        EditorUtility.DisplayProgressBar("�����滻��...", prefab.name, prefabCount / (float)asstIds.Length);
                        PrefabUtility.UnloadPrefabContents(prefab);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError("SDF�滻�쳣" + e);
            }
            finally
            {
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                EditorUtility.ClearProgressBar();
            }
        }
    }
}