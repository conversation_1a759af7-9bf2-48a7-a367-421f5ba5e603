using System;
using System.Collections.Generic;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using Sirenix.OdinInspector;
using UnityEngine;
using XLua;
using Object = UnityEngine.Object;

namespace CasualGame.lib_ChuagnYi
{
    /// <summary>
    /// 方便把一些变量展示到面板上
    /// </summary>
    [RequireComponent(typeof(LuaMono))]
    public class LuaShowData : MonoBehaviour
    {
#if UNITY_EDITOR
        // [System.Serializable]
        // public struct NumPair
        // {
        //     public string name;
        //     public float  value;
        // }

        [System.Serializable]
        public struct StringPair
        {
            public string name;
            public string value;
        }

        [System.Serializable]
        public struct ObjectPair
        {
            public string name;
            public Object value;
        }

        public LuaMono luaMono;

        // [TabGroup("num")]    public List<string> toShowNumbers;
        // [TabGroup("string")]
        public List<string> toShowStrings;
        // [TabGroup("Object")] 
        public List<string> toShowObjects;

        // [TabGroup("num")]
        // [HideIf("@this.toShowNumbers == null || this.toShowNumbers.Count == 0 || !UnityEngine.Application.isPlaying")]
        // [Searchable]
        // public List<NumPair> numberList;

        // [TabGroup("string")]
        [HideIf("@this.toShowStrings == null || this.toShowStrings.Count == 0|| !UnityEngine.Application.isPlaying")]
        [Searchable]
        public List<StringPair> stringList;

        // [TabGroup("Object")]
        [HideIf("@this.toShowObjects == null || this.toShowObjects.Count == 0|| !UnityEngine.Application.isPlaying")]
        [Searchable]
        public List<ObjectPair> objectList;

        private void OnValidate()
        {
            luaMono ??= GetComponent<LuaMono>();
        }

        private void Awake()
        {
            luaMono       ??= GetComponent<LuaMono>();
            // toShowNumbers ??= new List<string>();
            toShowStrings ??= new List<string>();
            toShowObjects ??= new List<string>();
            // numberList    ??= new List<NumPair>();
            stringList    ??= new List<StringPair>();
            objectList    ??= new List<ObjectPair>();
        }

        private void LateUpdate()
        {
            if (luaMono && luaMono.luaComp != null)
            {
                // numberList.Clear();
                // foreach (var k in toShowNumbers)
                // {
                //     if (string.IsNullOrEmpty(k)) continue;
                //     var kk = k.Replace("self.", "");
                //     try
                //     {
                //         numberList.Add(new NumPair {name = k, value = luaMono.luaComp.GetInPath<float>(kk)});
                //     }
                //     catch (Exception e)
                //     {
                //         numberList.Add(new NumPair {name = $"{k}_{e.Message}", value = 0});
                //     }
                // }

                stringList.Clear();
                foreach (var k in toShowStrings)
                {
                    if (string.IsNullOrEmpty(k)) continue;
                    var kk = k.Replace("self.", "");
                    try
                    {
                        stringList.Add(new StringPair {name = k, value = luaMono.luaComp.GetInPath<string>(kk)});
                    }
                    catch (Exception e)
                    {
                        stringList.Add(new StringPair {name = $"{k}_{e.Message}", value = e.Message});
                    }
                }

                objectList.Clear();
                foreach (var k in toShowObjects)
                {
                    if (string.IsNullOrEmpty(k)) continue;
                    var kk = k.Replace("self.", "");
                    try
                    {
                        objectList.Add(new ObjectPair {name = k, value = luaMono.luaComp.GetInPath<Object>(kk)});
                    }
                    catch (Exception e)
                    {
                        objectList.Add(new ObjectPair {name = $"{k}_{e.Message}", value = null});
                    }
                }
            }
        }
#endif
    }
}