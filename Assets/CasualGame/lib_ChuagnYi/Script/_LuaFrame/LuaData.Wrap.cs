using System;

namespace CasualGame.lib_ChuagnYi
{
    public interface IObjWrap
    {
        object GetObj();
        void   SetObj(object obj);
    }
    
    public class ObjWrap<T> : IObjWrap
    {
        public T obj;

        public object GetObj()
        {
            return obj;
        }

        public void SetObj(object obj)
        {
            this.obj = (T) obj;
        }
    }
    
    public partial class LuaData
    {
        [Serializable]
        public class SerializedObjValue
        {
            public string             key;
            public UnityEngine.Object value;
        }

        [Serializable]
        public class SerializedValue
        {
            public string key;
            public string jsonStr;
        }
    }
}