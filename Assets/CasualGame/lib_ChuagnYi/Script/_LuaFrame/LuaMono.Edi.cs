#if UNITY_EDITOR
using System.Collections.Generic;
using System.IO;
using CasualGame.lib_ChuagnYi.NeeG;
using CasualGame.lib_ChuagnYi.NeeG.Pool;
using Sirenix.OdinInspector;
using UnityEditor;
using UnityEngine;
using XLua;

namespace CasualGame.lib_ChuagnYi
{
    public partial class LuaMono
    {
        [BoxGroup("inspector")] [Sirenix.OdinInspector.LabelText("luaBehaviour的Inspector是否显示")]
        public bool showLuaBehaviorInspector;

        [BoxGroup("inspector")]
        [ShowInInspector]
        public bool isDataLuaInstanceNull => luaData == null || luaData.luaInstance == null;

        [BoxGroup("inspector")]
        [ShowInInspector]
        public bool isLuaCompNull => luaComp == null;

        [BoxGroup("inspector")] [Sirenix.OdinInspector.LabelText("luaData的Inspector是否追踪runtime数据")]
        public bool updateLuaDataInspectorRuntime = true;

        private static string[] LuaCompsDropDown
        {
            get { return LuaCompsDropDownFuncStatic(); }
        }

        public static string[] LuaCompsDropDownFuncStatic()
        {
            var list = NeeListPool<string>.New();
            var path = NeeGamePath.Instance.luaPath;
            var di   = new DirectoryInfo(path);
            var files1 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_comp_*.txt",
                    SearchOption.AllDirectories);
            foreach (var f in files1)
            {
                list.Add(f.Name.Substring(0, f.Name.Length - 4));
            }

            // list.Add("----------------");
            var files2 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_mgr_*.txt",
                    SearchOption.AllDirectories);
            foreach (var f in files2)
            {
                list.Add(f.Name.Substring(0, f.Name.Length - 4));
            }

            var arr = list.ToArray();
            NeeListPool<string>.Free(list);

            return arr;
        }

        public void OnValidateEditor()
        {
            if (luaData)
            {
                luaData.autoUpdate = updateLuaDataInspectorRuntime;
            }
        }

        public string[] LuaCompsDropDownFunc()
        {
            var list = NeeListPool<string>.New();
            var path = NeeGamePath.Instance.luaPath;
            var di   = new DirectoryInfo(path);
            var files1 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_comp_*.txt",
                    SearchOption.AllDirectories);
            foreach (var f in files1)
            {
                list.Add(f.Name.Substring(0, f.Name.Length - 4));
            }

            // list.Add("----------------");
            var files2 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_mgr_*.txt",
                    SearchOption.AllDirectories);
            foreach (var f in files2)
            {
                list.Add(f.Name.Substring(0, f.Name.Length - 4));
            }

            var arr = list.ToArray();
            NeeListPool<string>.Free(list);

            return arr;
        }

        [Button]
        public override void SyncName()
        {
            var tempName = this.luaName;
            var pre      = NeeGamePath.Instance.gameName.Length + 1;
            tempName        = tempName.Substring(pre, tempName.Length - pre);
            gameObject.name = tempName;
        }


        [Button("选择lua文件")]
        public void SelecetFile()
        {
            var list = NeeListPool<string>.New();
            var path = NeeGamePath.Instance.luaPath;
            var di   = new DirectoryInfo(path);
            var files1 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_*.txt",
                    SearchOption.AllDirectories);
            var pre = di.FullName;

            foreach (var f in files1)
            {
                if (f.Name == $"{luaName}.txt")
                {
                    var temp  = f.ToString();
                    var temp2 = temp.Substring(pre.Length, temp.Length - pre.Length);
                    temp2 = path + temp2;
                    var sel = AssetDatabase.LoadAssetAtPath<TextAsset>(temp2);
                    // Selection.activeObject = sel;
                    Debug.Log(sel);
                    if (sel)
                    {
                        EditorGUIUtility.PingObject(sel);
                    }

                    Debug.Log($"{temp2}");
                    fullPath = temp2;
                    break;
                }
            }
        }
        
        [Button("打开lua文件")]
        public void OpenFile()
        {
            var list = NeeListPool<string>.New();
            var path = NeeGamePath.Instance.luaPath;
            var di   = new DirectoryInfo(path);
            var files1 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_*.txt",
                    SearchOption.AllDirectories);
            var pre = di.FullName;

            foreach (var f in files1)
            {
                if (f.Name == $"{luaName}.txt")
                {
                    var temp  = f.ToString();
                    var temp2 = temp.Substring(pre.Length, temp.Length - pre.Length);
                    temp2 = path + temp2;
                    var sel = AssetDatabase.LoadAssetAtPath<TextAsset>(temp2);
                    // Selection.activeObject = sel;
                    Debug.Log(sel);
                    if (sel)
                    {
                        // EditorGUIUtility.PingObject(sel);
                        AssetDatabase.OpenAsset(sel, 0);
                    }

                    Debug.Log($"{temp2}");
                    fullPath = temp2;
                    break;
                }
            }
        }

        // [Button("设置LuaData")]
        public void SetLuaData()
        {
            var list = NeeListPool<string>.New();
            var path = NeeGamePath.Instance.luaPath;
            var di   = new DirectoryInfo(path);
            var files1 = di.GetFiles($"{NeeGamePath.Instance.gameName.ToLower()}_*.txt",
                    SearchOption.AllDirectories);
            var pre = di.FullName;

            foreach (var f in files1)
            {
                if (f.Name == $"{luaName}.txt")
                {
                    var temp  = f.ToString();
                    var temp2 = temp.Substring(pre.Length, temp.Length - pre.Length);
                    fullPath = path + temp2;
                    if (!luaData)
                    {
                        luaData = GetComponent<LuaData>();
                        if (!luaData)
                            luaData = gameObject.AddComponent<LuaData>();
                    }

                    luaData.LuaScriptPath = fullPath;
                    break;
                }
            }
        }
    }
}
#endif