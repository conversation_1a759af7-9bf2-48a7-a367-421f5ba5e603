// **********************************************************************
//  文件信息
// 文件名(File Name):                LanguageMgr.cs
// 作者(Author):                     huangxujie
// 创建时间(CreateTime):             2022-09-13 10:29
// 脚本描述(Module description):     多语言管理
// **********************************************************************

using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using CasualGame.lib_ChuagnYi.NeeG;
using Sirenix.OdinInspector;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    /// <summary>
    /// 本地化mgr
    /// </summary>
    [ScrptsOrder(EScriptsOrder.Init - 3)]
    public class NeeLocalizeMgr : NeeMonoSingleton<NeeLocalizeMgr>
    {
        /// <summary>
        /// 第一层key为语言str，第二层key为文本对应的key值
        /// </summary>
        private UDictionary<string, UDictionary<string, string>> languageDic;

        public TextAsset     LanguageCsv;
        public System.Action onLanguageCsvLoaded;

        /// <summary>
        /// 当前语言的字典
        /// </summary>
        [ShowInInspector] private Dictionary<string, string> currentLanguageDic;

        private void Start()
        {
            NeeGame.RegisterOnAsyncInited(() =>
            {
                if (fromLua)
                {
                    SetAysncInitBool(true);
                    return;
                }

                languageDic = new UDictionary<string, UDictionary<string, string>>();
                {
                    var dataTable = ConvertCSVtoDataTable((LanguageCsv as TextAsset).text);
                    //添加对应语言字典
                    for (var i = 1; i < dataTable.Columns.Count; i++)
                    {
                        var temp = dataTable.Columns[i].ToString();
                        ClearTex(ref temp);
                        AddLanguage(temp);
                    }

                    //添加文本内容到字典
                    for (int i = 0; i < dataTable.Rows.Count; i++)
                    {
                        List<string> texList = new List<string>();
                        var          key     = dataTable.Rows[i][0].ToString();
                        for (var j = 1; j < dataTable.Columns.Count; j++)
                        {
                            var temp = dataTable.Rows[i][j].ToString();
                            ClearTex(ref temp);
                            texList.Add(temp);
                        }

                        AddTextToDic(key, texList);
                    }
                }

                if (string.IsNullOrEmpty(NeeGame.Instance.Language))
                {
                    var temp     = UnityEngine.Application.systemLanguage.ToString();
                    var language = "";
                    if (temp == "ChineseSimplified" || temp == "Chinese")
                    {
                        language = "zh";
                    }
                    else if (temp == "English")
                    {
                        language = "en";
                    }
                    else
                    {
                        language = "en";
                    }

                    NeeGame.Instance.Language = language;
                }

                if (languageDic.ContainsKey(NeeGame.Instance.Language))
                {
                    currentLanguageDic = languageDic[NeeGame.Instance.Language];
                }

                onLanguageCsvLoaded?.Invoke();
                SetAysncInitBool(true);
            });
        }

        public bool fromLua;

        public void InitFromLua()
        {
            Debug.Log($" self.csLocalizeMgr:InitFromLua()");
            fromLua     = true;
            languageDic = new UDictionary<string, UDictionary<string, string>>();
            string lang = string.Empty;
            {
                var dataTable = ConvertCSVtoDataTable((LanguageCsv as TextAsset).text);
                //添加对应语言字典
                for (var i = 1; i < dataTable.Columns.Count; i++)
                {
                    var temp = dataTable.Columns[i].ToString();
                    ClearTex(ref temp);
                    if (string.IsNullOrEmpty(lang))
                        lang = temp;
                    AddLanguage(temp);
                }

                //添加文本内容到字典
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    List<string> texList = new List<string>();
                    var          key     = dataTable.Rows[i][0].ToString();
                    for (var j = 1; j < dataTable.Columns.Count; j++)
                    {
                        var temp = dataTable.Rows[i][j].ToString();
                        ClearTex(ref temp);
                        texList.Add(temp);
                    }

                    AddTextToDic(key, texList);
                }
            }
            // Debug.LogError(lang);
            currentLanguageDic = languageDic[lang].ToDictionary(x => x.Key, x => x.Value);
        }

        public void RewriteFromLua(string key, string text)
        {
            if (currentLanguageDic.ContainsKey(key))
            {
                currentLanguageDic[key] = text;
            }
            else
            {
                Debug.LogError($"no such key to rewrite : {key}");
            }
        }

        public void ChangeLanguage(string str)
        {
            if (string.IsNullOrEmpty(str))
            {
                if (languageDic.ContainsKey(str))
                {
                    currentLanguageDic        = languageDic[str];
                    NeeGame.Instance.Language = str;
                    onLanguageCsvLoaded?.Invoke();
                }
            }
        }

        private void ClearTex(ref string str)
        {
            var replace = str;
            if (str.Contains('\r'))
            {
                replace = replace.Replace("\r", "");
            }

            if (str.Contains('\n'))
            {
                replace = replace.Replace("\r", "");
            }

            str = replace;

            str = str.Replace('^', ',');
        }

        /// <summary>
        /// 添加对应的语言字典
        /// </summary>
        /// <param name="lan"></param>
        private void AddLanguage(string lan)
        {
            if (languageDic != null)
            {
                if (!languageDic.ContainsKey(lan))
                {
                    languageDic.Add(lan, new UDictionary<string, string>());
                }
                else
                {
                    Debug.LogError("add exist language to dic：" + lan);
                }
            }
            else
            {
                Debug.LogError("dic not exist!!");
            }
        }

        /// <summary>
        /// 添加对应文本到所有多语言字典中
        /// </summary>
        /// <param name="key">文本的key</param>
        /// <param name="tex">文本内容列表</param>
        private void AddTextToDic(string key, List<string> tex)
        {
            if (languageDic != null)
            {
                var list              = languageDic.ToList();
                var loadLanguageCount = 0;
                for (var i = 0; i < list.Count; i++)
                {
                    if (list[i].Value.ContainsKey(key))
                    {
                        if (Application.isPlaying)
                            Debug.LogWarning(list[i].Key + " language exist key :" + key);
                    }
                    else
                    {
                        // Debug.Log("add new tex:" + tex[i]);
                        list[i].Value.Add(key, tex[i]);
                        loadLanguageCount++;
                    }
                }

                if (loadLanguageCount < tex.Count) //csv文本对应语言字典缺少
                {
                    if (Application.isPlaying)
                        Debug.LogWarning("csv数据与字典不匹配,语言数据数量大于字典数量");
                }
            }
            else
            {
                Debug.LogError("dic not exist!!");
            }
        }

        private DataTable ConvertCSVtoDataTable(string text)
        {
            var dt = new DataTable();
            {
                // text.Replace("\r", "");
                var lines   = text.Split('\n');
                var headers = lines[1].Split(',');
                foreach (var header in headers) dt.Columns.Add(header);

                for (var li = 2; li < lines.Length; li++)
                {
                    // Debug.Log(lines[li]);
                    var rows = lines[li].Split(',');
                    var dr   = dt.NewRow();
                    if (rows.Length == headers.Length)
                    {
                        for (var i = 0; i < headers.Length; i++) dr[i] = rows[i];
                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;
        }

        public string GetTranslate(string key)
        {
            if (currentLanguageDic != null)
            {
                if (currentLanguageDic.ContainsKey(key))
                {
                    return currentLanguageDic[key];
                }
                else
                {
                    Debug.LogError("get not exist key :" + key);
                }
            }
            else
            {
                Debug.LogError("current dic is null :");
            }

            return "Error";
        }

        public string GetTranslateFormat(string key, params string[] obj)
        {
            if (currentLanguageDic != null)
            {
                if (currentLanguageDic.ContainsKey(key))
                {
                    return string.Format(currentLanguageDic[key], obj);
                }
                else
                {
                    Debug.LogError("get not exist key :" + key);
                }
            }
            else
            {
                Debug.LogError("current dic is null :");
            }

            return "Error";
        }

#if UNITY_EDITOR
        public string GetTranslateEditor(string key)
        {
            if (languageDic == null || currentLanguageDic == null)
            {
                languageDic = new UDictionary<string, UDictionary<string, string>>();
                {
                    var dataTable = ConvertCSVtoDataTable((LanguageCsv as TextAsset).text);
                    //添加对应语言字典
                    for (var i = 1; i < dataTable.Columns.Count; i++)
                    {
                        var temp = dataTable.Columns[i].ToString();
                        ClearTex(ref temp);
                        AddLanguage(temp);
                    }

                    //添加文本内容到字典
                    for (int i = 0; i < dataTable.Rows.Count; i++)
                    {
                        List<string> texList = new List<string>();
                        var          key2    = dataTable.Rows[i][0].ToString();
                        for (var j = 1; j < dataTable.Columns.Count; j++)
                        {
                            var temp = dataTable.Rows[i][j].ToString();
                            ClearTex(ref temp);
                            texList.Add(temp);
                        }

                        AddTextToDic(key2, texList);
                    }
                }
                NeeGame.Instance.Language = "zh";
                if (languageDic.ContainsKey(NeeGame.Instance.Language))
                {
                    currentLanguageDic = languageDic[NeeGame.Instance.Language];
                }
            }


            if (currentLanguageDic != null)
            {
                if (currentLanguageDic.ContainsKey(key))
                {
                    return currentLanguageDic[key];
                }
                else
                {
                    Debug.LogError("get not exist key :" + key);
                }
            }
            else
            {
                Debug.LogError("current dic is null :");
            }

            return "Error";
        }
#endif
    }
}