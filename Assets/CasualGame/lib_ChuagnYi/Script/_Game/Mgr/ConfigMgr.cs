using System;
using System.Collections.Generic;
using System.Data;
using CasualGame.lib_ChuagnYi.NeeG;
using Sirenix.OdinInspector;
using UnityEngine;
using XLua;
#if UNITY_EDITOR
using UnityEditor;

#endif

namespace CasualGame.lib_ChuagnYi
{
    public class ConfigMgr : NeeMonoSingleton<ConfigMgr>
    {

        public void SetLuaValue(DataTable dataTable, int index, LuaTable lua)
        {
            // var dataTable = ConvertCSVtoDataTable(txt);
            // for (var j = 0; j < dataTable.Rows.Count; j++)
            if (index == 0)
            {
                Debug.Log("index cannot be 0");
                return;
            }

            {
                for (var c = 0; c < dataTable.Columns.Count; c++)
                {
                    // Debug.Log($"{dataTable.Columns[c]} : {dataTable.Rows[j][c]}");
                    var temp = dataTable.Rows[0][c];
                    switch (temp)
                    {
                        case "int":
                            lua?.Set<string, int>($"{dataTable.Columns[c]}",
                                    int.Parse(dataTable.Rows[index][c].ToString()));
                            break;
                        case "float":
                            lua?.Set<string, float>($"{dataTable.Columns[c]}",
                                    float.Parse(dataTable.Rows[index][c].ToString()));
                            break;
                        case "bool":
                            lua?.Set<string, bool>($"{dataTable.Columns[c]}",
                                    bool.Parse(dataTable.Rows[index][c].ToString()));
                            break;
                        case "string":
                        case "string[]":
                        case "int[]":
                        case "float[]":
                        case "bool[]":
                            lua?.Set<string, string>($"{dataTable.Columns[c]}",
                                    (dataTable.Rows[index][c].ToString()));
                            break;
                        default:
                            Debug.LogError($"not supported type : {temp}");
                            break;
                    }
                }
            }
        }

        public DataTable ConvertCSVtoDataTable(string text)
        {
            var dt = new DataTable();
            {
                // text.Replace("\r", "");
                var lines = text.Split('\n');
                lines[1] = lines[1].Replace("\r\n", "").Replace("\r", "").Replace("\n", "");
                var headers = lines[1].Split(',');
                foreach (var header in headers)
                {
                    dt.Columns.Add(header);
                }

                for (var li = 2; li < lines.Length; li++)
                {
                    // Debug.Log(lines[li]);
                    var rows = lines[li].Replace("\r\n", "").Replace("\r", "").Replace("\n", "").Split(',');
                    var dr   = dt.NewRow();
                    if (rows.Length == headers.Length)
                    {
                        for (var i = 0; i < headers.Length; i++) dr[i] = rows[i];

                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;
        }
    }
}