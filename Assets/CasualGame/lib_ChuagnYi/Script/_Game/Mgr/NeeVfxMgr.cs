using System;
using System.Collections;
using CasualGame.lib_ChuagnYi.NeeG;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    public class NeeVfxMgr : NeeMonoSingleton<NeeVfxMgr>
    {
        public ParticleSystem PlayVfxForAWhile(string vfxName, float duration, Vector3 pos, Quaternion? qua=null,Vector3? scaler=null)
        {
            var ob = NeeGame.PoolObject<ParticleSystem>(vfxName, pos, qua ?? Quaternion.identity, transform);
            ob?.Play();
            // ob.GetComponent<ParticleSystem>()?.Play();
            if(scaler.HasValue)
                ob.transform.localScale = scaler.Value;

            StartCoroutine(Core());
            return ob.GetComponent<ParticleSystem>();

            IEnumerator Core()
            {
                yield return new WaitForSeconds(duration);
                NeeGame.ReturnObject(ob);
            }
        }
    }
}