using System;
using System.Collections;
using System.Linq;
using CasualGame.lib_ChuagnYi.NeeG;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi
{
    public class CameraMgr : NeeMonoSingleton<CameraMgr>
    {
        public Camera         mainCam;
        public Camera         uiCam;
        public Vector3        initPos;
        public Vector3        targetPos;

        private void Awake()
        {
            NeeGame.Instance.mainCam = mainCam;
            NeeGame.Instance.uiCam   = uiCam;
            targetPos                = initPos = mainCam.transform.position;
        }
    }
}