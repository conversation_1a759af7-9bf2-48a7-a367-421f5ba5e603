// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "CasualGame/lib_ChuagnYi/ChuagnYi_Surface_Shadow"
{
    Properties
    {
        [Toggle]_IsUseCustomLightDir("IsUseCustomLightDir", Float) = 0
		_LightDir("LightDir", Vector) = (0.5,1,0,0)
        _Color("_Color", Color) = (0,0,0,0)
        _AmbientColorMult("_AmbientColorMult", Color) = (1,1,1,1)
        _ShadowColor("_ShadowColor", Color) = (0,0,0,1)
        _ShadowIntensity("_ShadowIntensity", float) = 0.5
        _MainTex("_MainTex", 2D) = "white" {}
        _HighlightColor("_HighlightColor", Color) = (0.6603774,0.6603774,0.6603774,1)
        _HighlightColor2("_HighlightColor2", Color) = (0.6603774,0.6603774,0.6603774,1)
        _HightlightAreaSize("_HightlightAreaSize", Range( 0.5 , 1)) = 0.9
        _HightlightAreaSize2("_HightlightAreaSize2", Range( 0.5 , 1)) = 0.9
        [HideInInspector] _texcoord( "", 2D ) = "white" {}

    }

    SubShader
    {


        Tags
        {
            "RenderType"="Opaque" "RenderPipeline"="UniversalPipeline"
        }
        LOD 100

        CGINCLUDE
        #pragma target 3.0
        ENDCG
        Blend Off
        AlphaToMask Off
        Cull Back
        ColorMask RGBA
        ZWrite On
        ZTest LEqual
        Offset 0 , 0



        Pass
        {
            Name "Unlit"
            Tags
            {
                "LightMode"="UniversalForward"
            }
            CGPROGRAM
            #pragma multi_compile_fwdbase
            #ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
            #endif
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #include "UnityCG.cginc"
            #include "UnityShaderVariables.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            #define ASE_NEEDS_FRAG_WORLD_POSITION


            struct appdata
            {
                float4 vertex : POSITION;
                float4 color : COLOR;
                float4 ase_texcoord : TEXCOORD0;
                half3 ase_normal : NORMAL;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                #ifdef ASE_NEEDS_FRAG_WORLD_POSITION
                float3 worldPos : TEXCOORD0;
                #endif
                float4 ase_texcoord1 : TEXCOORD1;
                float4 ase_texcoord2 : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
                SHADOW_COORDS(3)
            };

            //This is a late directive

            uniform float _IsUseCustomLightDir;
			uniform float3 _LightDir;
            uniform float4 _AmbientColorMult;
            uniform sampler2D _MainTex;
            uniform half4 _MainTex_ST;
            uniform half4 _Color;
            uniform half4 _ShadowColor;
            uniform float _HightlightAreaSize2;
            uniform float4 _HighlightColor2;
            uniform float _HightlightAreaSize;
            uniform float4 _HighlightColor;
            uniform half _ShadowIntensity;


            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                UNITY_TRANSFER_INSTANCE_ID(v, o);

                half3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
                o.ase_texcoord2.xyz = ase_worldNormal;

                o.ase_texcoord1.xy = v.ase_texcoord.xy;

                //setting value to unused interpolator channels and avoid initialization warnings
                o.ase_texcoord1.zw = 0;
                o.ase_texcoord2.w = 0;
                float3 vertexValue = float3(0, 0, 0);
                #if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
                #endif
                vertexValue = vertexValue;
                #if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
                #else
                v.vertex.xyz += vertexValue;
                #endif
                o.pos = UnityObjectToClipPos(v.vertex);

                #ifdef ASE_NEEDS_FRAG_WORLD_POSITION
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                #endif
                TRANSFER_SHADOW(o);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 uv_MainTex = i.ase_texcoord1.xy * _MainTex_ST.xy + _MainTex_ST.zw;
				float3 ase_worldNormal = i.ase_texcoord2.xyz;
				float3 worldSpaceLightDir = UnityWorldSpaceLightDir(WorldPosition);
				float dotResult26 = dot( ase_worldNormal , (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) );
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				float3 normalizeResult97 = normalize( ( (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) + ase_worldViewDir ) );
				float dotResult99 = dot( ase_worldNormal , normalizeResult97 );
				float normal_viewDir109 = dotResult99;
				float clampResult102 = clamp( (0.0 + (normal_viewDir109 - _HightlightAreaSize2) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize2)) , 0.0 , 1.0 );
				float clampResult39 = clamp( (0.0 + (normal_viewDir109 - _HightlightAreaSize) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize)) , 0.0 , 1.0 );
				float4 temp_output_41_0 = ( clampResult39 * _HighlightColor );

                fixed shadow = SHADOW_ATTENUATION(i);
                shadow = lerp(1, shadow, _ShadowIntensity * _ShadowColor.a);
                // shadow=1;

                finalColor = ((UNITY_LIGHTMODEL_AMBIENT * _AmbientColorMult * unity_FogColor) +(
                   ((1-shadow)*_ShadowColor+ tex2D(_MainTex, uv_MainTex) * shadow)* _Color * 1.0 * ((dotResult26 * 0.3) + (1.0 -
                        0.3))) + (clampResult102 * _HighlightColor2) + temp_output_41_0);
                return finalColor;
            }
            ENDCG
        }

    }
SubShader
    {


        Tags
        {
            "RenderType"="Opaque"
        }
        LOD 100

        CGINCLUDE
        #pragma target 3.0
        ENDCG
        Blend Off
        AlphaToMask Off
        Cull Back
        ColorMask RGBA
        ZWrite On
        ZTest LEqual
        Offset 0 , 0



        Pass
        {
            Name "Unlit"
            Tags
            {
                "LightMode"="ForwardBase"
            }
            CGPROGRAM
            #pragma multi_compile_fwdbase
            #ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
            #endif
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #include "UnityCG.cginc"
            #include "UnityShaderVariables.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            #define ASE_NEEDS_FRAG_WORLD_POSITION


            struct appdata
            {
                float4 vertex : POSITION;
                float4 color : COLOR;
                float4 ase_texcoord : TEXCOORD0;
                half3 ase_normal : NORMAL;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                #ifdef ASE_NEEDS_FRAG_WORLD_POSITION
                float3 worldPos : TEXCOORD0;
                #endif
                float4 ase_texcoord1 : TEXCOORD1;
                float4 ase_texcoord2 : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
                SHADOW_COORDS(3)
            };

            //This is a late directive

            uniform float _IsUseCustomLightDir;
			uniform float3 _LightDir;
            uniform float4 _AmbientColorMult;
            uniform sampler2D _MainTex;
            uniform half4 _MainTex_ST;
            uniform half4 _Color;
            uniform half4 _ShadowColor;
            uniform float _HightlightAreaSize2;
            uniform float4 _HighlightColor2;
            uniform float _HightlightAreaSize;
            uniform float4 _HighlightColor;
            uniform half _ShadowIntensity;


            v2f vert(appdata v)
            {
                v2f o;
                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
                UNITY_TRANSFER_INSTANCE_ID(v, o);

                half3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
                o.ase_texcoord2.xyz = ase_worldNormal;

                o.ase_texcoord1.xy = v.ase_texcoord.xy;

                //setting value to unused interpolator channels and avoid initialization warnings
                o.ase_texcoord1.zw = 0;
                o.ase_texcoord2.w = 0;
                float3 vertexValue = float3(0, 0, 0);
                #if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
                #endif
                vertexValue = vertexValue;
                #if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
                #else
                v.vertex.xyz += vertexValue;
                #endif
                o.pos = UnityObjectToClipPos(v.vertex);

                #ifdef ASE_NEEDS_FRAG_WORLD_POSITION
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                #endif
                TRANSFER_SHADOW(o);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(i);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 uv_MainTex = i.ase_texcoord1.xy * _MainTex_ST.xy + _MainTex_ST.zw;
				float3 ase_worldNormal = i.ase_texcoord2.xyz;
				float3 worldSpaceLightDir = UnityWorldSpaceLightDir(WorldPosition);
				float dotResult26 = dot( ase_worldNormal , (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) );
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				float3 normalizeResult97 = normalize( ( (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) + ase_worldViewDir ) );
				float dotResult99 = dot( ase_worldNormal , normalizeResult97 );
				float normal_viewDir109 = dotResult99;
				float clampResult102 = clamp( (0.0 + (normal_viewDir109 - _HightlightAreaSize2) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize2)) , 0.0 , 1.0 );
				float clampResult39 = clamp( (0.0 + (normal_viewDir109 - _HightlightAreaSize) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize)) , 0.0 , 1.0 );
				float4 temp_output_41_0 = ( clampResult39 * _HighlightColor );

                fixed shadow = SHADOW_ATTENUATION(i);
                shadow = lerp(1, shadow, _ShadowIntensity * _ShadowColor.a);
                // shadow=1;

                finalColor = ((UNITY_LIGHTMODEL_AMBIENT * _AmbientColorMult * unity_FogColor) +(
                   ((1-shadow)*_ShadowColor+ tex2D(_MainTex, uv_MainTex) * shadow)* _Color * 1.0 * ((dotResult26 * 0.3) + (1.0 -
                        0.3))) + (clampResult102 * _HighlightColor2) + temp_output_41_0);
                return finalColor;
            }
            ENDCG
        }

    }
    //	CustomEditor "ASEMaterialInspector"

    Fallback "Specular"
}
/*A222SEBEGIN
Version=18935
 2;201;1920;1019;2056.634;1067.686;1.593455;True;False
 Node;AmplifyShaderEditor.ViewDirInputsCoordNode;44;-1802.652,-1770.535;Inherit;True;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
 Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;29;-1751.679,-1341.765;Inherit;True;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
 Node;AmplifyShaderEditor.SimpleAddOpNode;32;-1393.371,-1301.286;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
 Node;AmplifyShaderEditor.NormalizeNode;34;-1232.957,-1266.805;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
 Node;AmplifyShaderEditor.WorldNormalVector;43;-1439.847,-1670.09;Inherit;True;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
 Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;25;-1040.971,-583.6299;Inherit;False;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
 Node;AmplifyShaderEditor.WorldNormalVector;24;-1017.171,-731.7299;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
 Node;AmplifyShaderEditor.DotProductOpNode;37;-1044.299,-1478.595;Inherit;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
 Node;AmplifyShaderEditor.RangedFloatNode;48;-784.3777,-487.0539;Inherit;False;Constant;_Lambert;_Lambert;8;0;Create;True;0;0;0;False;0;False;0.3;0.5;0;1;0;1;FLOAT;0
 Node;AmplifyShaderEditor.RangedFloatNode;47;-939.5202,-1614.68;Inherit;False;Property;_HightlightAreaSize;_HightlightAreaSize;7;0;Create;True;0;0;0;False;0;False;0.9;1;0.5;1;0;1;FLOAT;0
 Node;AmplifyShaderEditor.DotProductOpNode;26;-610.1711,-632.23;Inherit;False;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
 Node;AmplifyShaderEditor.SimpleMultiplyOpNode;45;-430.7529,-628.0656;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0
 Node;AmplifyShaderEditor.OneMinusNode;49;-493.3777,-369.0539;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
 Node;AmplifyShaderEditor.TFHCRemapNode;31;-629.1073,-1676.897;Inherit;True;5;0;FLOAT;0;False;1;FLOAT;0.9;False;2;FLOAT;1;False;3;FLOAT;0;False;4;FLOAT;0.8;False;1;FLOAT;0
 Node;AmplifyShaderEditor.RangedFloatNode;52;-786.9683,-321.8611;Inherit;False;Constant;_LightIntensity;LightIntensity;9;0;Create;True;0;0;0;False;0;False;1;0;0.1;3;0;1;FLOAT;0
 Node;AmplifyShaderEditor.SimpleAddOpNode;46;-437.7529,-511.0656;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0
 Node;AmplifyShaderEditor.ClampOpNode;39;-426.8909,-1433.817;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
 Node;AmplifyShaderEditor.ColorNode;30;-540.3649,-1215.581;Inherit;False;Property;_HighlightColor;_HighlightColor;6;0;Create;True;0;0;0;False;0;False;0.6603774,0.6603774,0.6603774,1;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
 Node;AmplifyShaderEditor.SamplerNode;2;-1058.854,-97.7173;Inherit;True;Property;_MainTex;_MainTex;1;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
 Node;AmplifyShaderEditor.ColorNode;1;-1011.854,-295.7173;Inherit;False;Property;_Color;_Color;0;0;Create;True;0;0;0;False;0;False;0,0,0,0;0.04481131,0.5,0.0792321,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
 Node;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-124.1029,-1507.324;Inherit;True;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
 Node;AmplifyShaderEditor.FogAndAmbientColorsNode;23;-982.3712,154.67;Inherit;False;unity_AmbientEquator;0;1;COLOR;0
 Node;AmplifyShaderEditor.SimpleMultiplyOpNode;4;-625.8536,-167.7173;Inherit;False;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;3;FLOAT;0;False;1;COLOR;0
 Node;AmplifyShaderEditor.SamplerNode;36;-905.0497,-1084.089;Inherit;True;Property;_TextureSample0;Texture Sample 0;2;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
 Node;AmplifyShaderEditor.SimpleAddOpNode;28;-449.1711,13.3702;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
 Node;AmplifyShaderEditor.ColorNode;5;-607.0536,184.3827;Inherit;False;Property;_EmissionColor;_EmissionColor;3;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,0,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
 Node;AmplifyShaderEditor.RangedFloatNode;6;-341.8536,261.2827;Inherit;False;Property;_Metallic;_Metallic;4;0;Create;True;0;0;0;False;0;False;0;0.2;0;1;0;1;FLOAT;0
 Node;AmplifyShaderEditor.SimpleAddOpNode;42;-811.3618,-1386.173;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;-0.993;False;1;FLOAT;0
 Node;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-578.3046,-972.497;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
 Node;AmplifyShaderEditor.SimpleMultiplyOpNode;40;-576.0621,-1426.473;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;300;False;1;FLOAT;0
 Node;AmplifyShaderEditor.RangedFloatNode;7;-328.8536,353.2827;Inherit;False;Property;_Glossiness;_Glossiness;5;0;Create;True;0;0;0;False;0;False;0;0.61;0;1;0;1;FLOAT;0
 Node;AmplifyShaderEditor.SimpleAddOpNode;35;-316.3233,-1112.254;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
 Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;78;173,-21;Float;False;True;-1;2;ASEMaterialInspector;100;1;CausalGame/Aquapark Nee_Surface;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;False;True;0;1;False;-1;0;False;-1;0;1;False;-1;0;False;-1;True;0;False;-1;0;False;-1;False;False;False;False;False;False;False;False;False;True;0;False;-1;False;True;0;False;-1;False;True;True;True;True;True;0;False;-1;False;False;False;False;False;False;False;True;False;255;False;-1;255;False;-1;255;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;7;False;-1;1;False;-1;1;False;-1;1;False;-1;False;True;1;False;-1;True;3;False;-1;True;True;0;False;-1;0;False;-1;True;1;RenderType=Opaque=RenderType;True;2;False;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=ForwardBase;False;False;0;;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;0;1;True;False;;False;0
 WireConnection;32;0;29;0
 WireConnection;32;1;44;0
 WireConnection;34;0;32;0
 WireConnection;37;0;43;0
 WireConnection;37;1;34;0
 WireConnection;26;0;24;0
 WireConnection;26;1;25;0
 WireConnection;45;0;26;0
 WireConnection;45;1;48;0
 WireConnection;49;0;48;0
 WireConnection;31;0;37;0
 WireConnection;31;1;47;0
 WireConnection;46;0;45;0
 WireConnection;46;1;49;0
 WireConnection;39;0;31;0
 WireConnection;41;0;39;0
 WireConnection;41;1;30;0
 WireConnection;4;0;2;0
 WireConnection;4;1;1;0
 WireConnection;4;2;52;0
 WireConnection;4;3;46;0
 WireConnection;28;0;23;0
 WireConnection;28;1;4;0
 WireConnection;28;2;41;0
 WireConnection;42;0;37;0
 WireConnection;38;0;36;0
 WireConnection;38;1;1;0
 WireConnection;40;0;42;0
 WireConnection;35;0;41;0
 WireConnection;35;1;38;0
 WireConnection;78;0;28;0
 ASEEND*/
//CHKSM=0914FFEC4E5D6408BC3BD4D8B8512BF479EE2377