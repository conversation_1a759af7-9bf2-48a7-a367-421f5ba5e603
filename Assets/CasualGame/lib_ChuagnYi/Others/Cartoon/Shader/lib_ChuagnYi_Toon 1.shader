// Made with Amplify Shader Editor v1.9.0.2
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "CasualGame/lib_ChuagnYi/ChuagnYi_Toon"
{
	Properties
	{
		[Toggle]_IsUseCustomLightDir("IsUseCustomLightDir", Float) = 0
		_LightDir("LightDir", Vector) = (0.5,1,0,0)
		_Color("_Color", Color) = (0,0,0,0)
		_AmbientColorMult("_AmbientColorMult", Color) = (1,1,1,1)
		_MainTex("_MainTex", 2D) = "white" {}
		_HighlightColor("_HighlightColor", Color) = (0.6603774,0.6603774,0.6603774,1)
		_HighlightColor2("_HighlightColor2", Color) = (0.6603774,0.6603774,0.6603774,1)
		_HightlightAreaSize("_HightlightAreaSize", Range( 0.5 , 1)) = 0.9
		_HightlightAreaSize2("_HightlightAreaSize2", Range( 0.5 , 1)) = 0.9
		_RampTex("RampTex", 2D) = "white" {}
		_RampMult("RampMult", Range( 0 , 10)) = 2
		[HideInInspector] _texcoord( "", 2D ) = "white" {}

	}
	
	SubShader
	{
		
		
		Tags { "RenderType"="Opaque" "RenderPipeline"="UniversalPipeline" }
	LOD 100

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		AlphaToMask Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"
			Tags { "LightMode"="UniversalForward" }
			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#include "Lighting.cginc"
			#include "AutoLight.cginc"
			#define ASE_NEEDS_FRAG_WORLD_POSITION


			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float3 ase_normal : NORMAL;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			//This is a late directive
			
			uniform float4 _AmbientColorMult;
			uniform sampler2D _MainTex;
			uniform float4 _MainTex_ST;
			uniform float4 _Color;
			uniform sampler2D _RampTex;
			uniform float _IsUseCustomLightDir;
			uniform float3 _LightDir;
			uniform float _RampMult;
			uniform float _HightlightAreaSize2;
			uniform float4 _HighlightColor2;
			uniform float _HightlightAreaSize;
			uniform float4 _HighlightColor;

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
				o.ase_texcoord2.xyz = ase_worldNormal;
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				o.ase_texcoord2.w = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = vertexValue;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 uv_MainTex = i.ase_texcoord1.xy * _MainTex_ST.xy + _MainTex_ST.zw;
				float3 ase_worldNormal = i.ase_texcoord2.xyz;
				float3 worldSpaceLightDir = UnityWorldSpaceLightDir(WorldPosition);
				float dotResult26 = dot( ase_worldNormal , (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) );
				float2 temp_cast_0 = (( ( dotResult26 * 0.3 ) * _RampMult )).xx;
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				float3 normalizeResult132 = normalize( ( (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) + ase_worldViewDir ) );
				float dotResult134 = dot( ase_worldNormal , normalizeResult132 );
				float normal_viewDir137 = dotResult134;
				float clampResult102 = clamp( (0.0 + (normal_viewDir137 - _HightlightAreaSize2) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize2)) , 0.0 , 1.0 );
				float clampResult39 = clamp( (0.0 + (normal_viewDir137 - _HightlightAreaSize) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize)) , 0.0 , 1.0 );
				float4 temp_output_41_0 = ( clampResult39 * _HighlightColor );
				
				
				finalColor = ( ( UNITY_LIGHTMODEL_AMBIENT * _AmbientColorMult * unity_FogColor ) + ( tex2D( _MainTex, uv_MainTex ) * _Color * 1.0 * ( tex2D( _RampTex, temp_cast_0 ) + ( 1.0 - 0.3 ) ) ) + ( clampResult102 * _HighlightColor2 ) + temp_output_41_0 );
				return finalColor;
			}
			ENDCG
		}
	}
SubShader
	{
		
		
		Tags { "RenderType"="Opaque" }
	LOD 100

		CGINCLUDE
		#pragma target 3.0
		ENDCG
		Blend Off
		AlphaToMask Off
		Cull Back
		ColorMask RGBA
		ZWrite On
		ZTest LEqual
		Offset 0 , 0
		
		
		
		Pass
		{
			Name "Unlit"
			Tags { "LightMode"="ForwardBase" }
			CGPROGRAM

			

			#ifndef UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX
			//only defining to not throw compilation error over Unity 5.5
			#define UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input)
			#endif
			#pragma vertex vert
			#pragma fragment frag
			#pragma multi_compile_instancing
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#include "Lighting.cginc"
			#include "AutoLight.cginc"
			#define ASE_NEEDS_FRAG_WORLD_POSITION


			struct appdata
			{
				float4 vertex : POSITION;
				float4 color : COLOR;
				float4 ase_texcoord : TEXCOORD0;
				float3 ase_normal : NORMAL;
				UNITY_VERTEX_INPUT_INSTANCE_ID
			};
			
			struct v2f
			{
				float4 vertex : SV_POSITION;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 worldPos : TEXCOORD0;
				#endif
				float4 ase_texcoord1 : TEXCOORD1;
				float4 ase_texcoord2 : TEXCOORD2;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				UNITY_VERTEX_OUTPUT_STEREO
			};

			//This is a late directive
			
			uniform float4 _AmbientColorMult;
			uniform sampler2D _MainTex;
			uniform float4 _MainTex_ST;
			uniform float4 _Color;
			uniform sampler2D _RampTex;
			uniform float _IsUseCustomLightDir;
			uniform float3 _LightDir;
			uniform float _RampMult;
			uniform float _HightlightAreaSize2;
			uniform float4 _HighlightColor2;
			uniform float _HightlightAreaSize;
			uniform float4 _HighlightColor;

			
			v2f vert ( appdata v )
			{
				v2f o;
				UNITY_SETUP_INSTANCE_ID(v);
				UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				UNITY_TRANSFER_INSTANCE_ID(v, o);

				float3 ase_worldNormal = UnityObjectToWorldNormal(v.ase_normal);
				o.ase_texcoord2.xyz = ase_worldNormal;
				
				o.ase_texcoord1.xy = v.ase_texcoord.xy;
				
				//setting value to unused interpolator channels and avoid initialization warnings
				o.ase_texcoord1.zw = 0;
				o.ase_texcoord2.w = 0;
				float3 vertexValue = float3(0, 0, 0);
				#if ASE_ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = vertexValue;
				#if ASE_ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.vertex = UnityObjectToClipPos(v.vertex);

				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#endif
				return o;
			}
			
			fixed4 frag (v2f i ) : SV_Target
			{
				UNITY_SETUP_INSTANCE_ID(i);
				UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(i);
				fixed4 finalColor;
				#ifdef ASE_NEEDS_FRAG_WORLD_POSITION
				float3 WorldPosition = i.worldPos;
				#endif
				float2 uv_MainTex = i.ase_texcoord1.xy * _MainTex_ST.xy + _MainTex_ST.zw;
				float3 ase_worldNormal = i.ase_texcoord2.xyz;
				float3 worldSpaceLightDir = UnityWorldSpaceLightDir(WorldPosition);
				float dotResult26 = dot( ase_worldNormal , (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) );
				float2 temp_cast_0 = (( ( dotResult26 * 0.3 ) * _RampMult )).xx;
				float3 ase_worldViewDir = UnityWorldSpaceViewDir(WorldPosition);
				ase_worldViewDir = normalize(ase_worldViewDir);
				float3 normalizeResult132 = normalize( ( (( _IsUseCustomLightDir )?( _LightDir ):( worldSpaceLightDir )) + ase_worldViewDir ) );
				float dotResult134 = dot( ase_worldNormal , normalizeResult132 );
				float normal_viewDir137 = dotResult134;
				float clampResult102 = clamp( (0.0 + (normal_viewDir137 - _HightlightAreaSize2) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize2)) , 0.0 , 1.0 );
				float clampResult39 = clamp( (0.0 + (normal_viewDir137 - _HightlightAreaSize) * (0.8 - 0.0) / (1.0 - _HightlightAreaSize)) , 0.0 , 1.0 );
				float4 temp_output_41_0 = ( clampResult39 * _HighlightColor );
				
				
				finalColor = ( ( UNITY_LIGHTMODEL_AMBIENT * _AmbientColorMult * unity_FogColor ) + ( tex2D( _MainTex, uv_MainTex ) * _Color * 1.0 * ( tex2D( _RampTex, temp_cast_0 ) + ( 1.0 - 0.3 ) ) ) + ( clampResult102 * _HighlightColor2 ) + temp_output_41_0 );
				return finalColor;
			}
			ENDCG
		}
	}
	CustomEditor "ASEMaterialInspector"
	
	Fallback "Specular"
}
/*ASEBEGIN
Version=19002
0;12;1920;1007;3518.123;3204.005;1.398704;True;False
Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;124;-3067.268,-2248.903;Inherit;True;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.Vector3Node;126;-3069.462,-2691.271;Inherit;False;Property;_LightDir;LightDir;1;0;Create;True;0;0;0;False;0;False;0.5,1,0;0,0,0;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.ToggleSwitchNode;139;-2709.957,-2302.947;Inherit;False;Property;_IsUseCustomLightDir;IsUseCustomLightDir;0;0;Create;True;0;0;0;False;0;False;0;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.ViewDirInputsCoordNode;127;-2693.537,-2957.537;Inherit;True;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.SimpleAddOpNode;129;-2290.053,-2488.823;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.WorldNormalVector;130;-2336.53,-2857.627;Inherit;True;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.NormalizeNode;132;-2129.64,-2454.342;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.WorldNormalVector;24;-1997.702,-984.265;Inherit;False;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.RangedFloatNode;48;-955.3777,-539.0539;Inherit;False;Constant;_Lambert;_Lambert;8;0;Create;True;0;0;0;False;0;False;0.3;0.5;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.DotProductOpNode;26;-1457.386,-1053.096;Inherit;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.DotProductOpNode;134;-1940.983,-2666.132;Inherit;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;45;-591.153,-752.9656;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;0.5;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;123;-577.788,-538.6266;Inherit;False;Property;_RampMult;RampMult;14;0;Create;True;0;0;0;False;0;False;2;1.5;0;10;0;1;FLOAT;0
Node;AmplifyShaderEditor.RegisterLocalVarNode;137;-1369.915,-2662.315;Inherit;False;normal_viewDir;-1;True;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;98;-786.1558,-2217.256;Inherit;False;Property;_HightlightAreaSize2;_HightlightAreaSize2;12;0;Create;True;0;0;0;False;0;False;0.9;0.751;0.5;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.GetLocalVarNode;110;-1343.724,-1806.946;Inherit;False;137;normal_viewDir;1;0;OBJECT;;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;122;-381.3874,-755.1265;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;47;-939.5202,-1614.68;Inherit;False;Property;_HightlightAreaSize;_HightlightAreaSize;11;0;Create;True;0;0;0;False;0;False;0.9;0.966;0.5;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.TFHCRemapNode;31;-629.1073,-1676.897;Inherit;True;5;0;FLOAT;0;False;1;FLOAT;0.9;False;2;FLOAT;1;False;3;FLOAT;0;False;4;FLOAT;0.8;False;1;FLOAT;0
Node;AmplifyShaderEditor.OneMinusNode;49;-590.3003,-429.0884;Inherit;False;1;0;FLOAT;0;False;1;FLOAT;0
Node;AmplifyShaderEditor.TFHCRemapNode;100;-488.0194,-2337.287;Inherit;True;5;0;FLOAT;0;False;1;FLOAT;0.9;False;2;FLOAT;1;False;3;FLOAT;0;False;4;FLOAT;0.8;False;1;FLOAT;0
Node;AmplifyShaderEditor.SamplerNode;121;-265.5842,-690.2659;Inherit;True;Property;_RampTex;RampTex;13;0;Create;True;0;0;0;False;0;False;-1;None;09e70204ae31f674b86c9e5e0b79e7d9;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.FogAndAmbientColorsNode;23;-922.7716,468.8702;Inherit;False;UNITY_LIGHTMODEL_AMBIENT;0;1;COLOR;0
Node;AmplifyShaderEditor.SamplerNode;2;-1058.854,-97.7173;Inherit;True;Property;_MainTex;_MainTex;4;0;Create;True;0;0;0;False;0;False;-1;None;2e356fd82489de04eb6f199da79aa3ad;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;101;-399.277,-1875.97;Inherit;False;Property;_HighlightColor2;_HighlightColor2;10;0;Create;True;0;0;0;False;0;False;0.6603774,0.6603774,0.6603774,1;0.1490196,0.1330293,0.1254902,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.RangedFloatNode;52;-786.9683,-321.8611;Inherit;False;Constant;_LightIntensity;LightIntensity;9;0;Create;True;0;0;0;False;0;False;1;0;0.1;3;0;1;FLOAT;0
Node;AmplifyShaderEditor.FogAndAmbientColorsNode;92;-1207.4,700.245;Inherit;False;unity_FogColor;0;1;COLOR;0
Node;AmplifyShaderEditor.ClampOpNode;102;-285.8029,-2094.207;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;1;-1045.211,-305.7242;Inherit;False;Property;_Color;_Color;2;0;Create;True;0;0;0;False;0;False;0,0,0,0;1,1,1,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ClampOpNode;39;-426.8909,-1433.817;Inherit;True;3;0;FLOAT;0;False;1;FLOAT;0;False;2;FLOAT;1;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;30;-540.3649,-1215.581;Inherit;False;Property;_HighlightColor;_HighlightColor;9;0;Create;True;0;0;0;False;0;False;0.6603774,0.6603774,0.6603774,1;0.227,0.1429259,0.0924815,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.ColorNode;89;-865.8641,595.2399;Inherit;False;Property;_AmbientColorMult;_AmbientColorMult;3;0;Create;True;0;0;0;False;0;False;1,1,1,1;0.01942452,0.06918012,0.2735849,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;46;-10.61037,-515.7557;Inherit;False;2;2;0;COLOR;0,0,0,0;False;1;FLOAT;0.5;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;103;16.98515,-2167.714;Inherit;True;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;88;-632.8641,563.24;Inherit;False;3;3;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;4;-600.8536,-169.7173;Inherit;True;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;FLOAT;0;False;3;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;41;-161.141,-1551.474;Inherit;True;2;2;0;FLOAT;0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.DotProductOpNode;135;-1789.604,-3251.635;Inherit;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;7;-328.8536,353.2827;Inherit;False;Property;_Glossiness;_Glossiness;8;0;Create;True;0;0;0;False;0;False;0;0.61;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;38;-578.3046,-972.497;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.ViewDirInputsCoordNode;44;-2659.234,-1731.837;Inherit;True;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.SimpleAddOpNode;91;-248.5876,33.34998;Inherit;True;4;4;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;2;COLOR;0,0,0,0;False;3;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;25;-2029.996,-790.8622;Inherit;False;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.WorldNormalVector;43;-2373.581,-1647.217;Inherit;True;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.WorldNormalVector;131;-2185.151,-3443.13;Inherit;True;False;1;0;FLOAT3;0,0,1;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.DotProductOpNode;37;-1978.033,-1455.722;Inherit;True;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;35;-314.4693,-1012.14;Inherit;True;2;2;0;COLOR;0,0,0,0;False;1;COLOR;0,0,0,0;False;1;COLOR;0
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;105;-434.9742,-2086.863;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;300;False;1;FLOAT;0
Node;AmplifyShaderEditor.SimpleAddOpNode;128;-1922.258,-2905.473;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleAddOpNode;32;-2327.105,-1278.413;Inherit;False;2;2;0;FLOAT3;0,0,0;False;1;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.SimpleAddOpNode;104;-670.2734,-2046.562;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;-0.993;False;1;FLOAT;0
Node;AmplifyShaderEditor.NormalizeNode;133;-1761.844,-2870.992;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.NormalizeNode;34;-2166.691,-1243.932;Inherit;False;False;1;0;FLOAT3;0,0,0;False;1;FLOAT3;0
Node;AmplifyShaderEditor.ViewDirInputsCoordNode;125;-2546.379,-3545.151;Inherit;True;World;False;0;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.SamplerNode;36;-905.0497,-1084.089;Inherit;True;Property;_TextureSample0;Texture Sample 0;5;0;Create;True;0;0;0;False;0;False;-1;None;None;True;0;False;white;Auto;False;Object;-1;Auto;Texture2D;8;0;SAMPLER2D;;False;1;FLOAT2;0,0;False;2;FLOAT;0;False;3;FLOAT2;0,0;False;4;FLOAT2;0,0;False;5;FLOAT;1;False;6;FLOAT;0;False;7;SAMPLERSTATE;;False;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.SimpleAddOpNode;42;-811.3618,-1386.173;Inherit;True;2;2;0;FLOAT;0;False;1;FLOAT;-0.993;False;1;FLOAT;0
Node;AmplifyShaderEditor.ColorNode;5;-565.454,371.5828;Inherit;False;Property;_EmissionColor;_EmissionColor;6;0;Create;True;0;0;0;False;0;False;0,0,0,0;0,0,0,1;True;0;5;COLOR;0;FLOAT;1;FLOAT;2;FLOAT;3;FLOAT;4
Node;AmplifyShaderEditor.WorldSpaceLightDirHlpNode;29;-2685.413,-1318.892;Inherit;True;False;1;0;FLOAT;0;False;4;FLOAT3;0;FLOAT;1;FLOAT;2;FLOAT;3
Node;AmplifyShaderEditor.SimpleMultiplyOpNode;40;-576.0621,-1426.473;Inherit;False;2;2;0;FLOAT;0;False;1;FLOAT;300;False;1;FLOAT;0
Node;AmplifyShaderEditor.RangedFloatNode;6;-341.8536,261.2827;Inherit;False;Property;_Metallic;_Metallic;7;0;Create;True;0;0;0;False;0;False;0;0.2;0;1;0;1;FLOAT;0
Node;AmplifyShaderEditor.TemplateMultiPassMasterNode;80;173,-21;Float;False;True;-1;2;ASEMaterialInspector;100;3;CasualGame/CyLuaBatteryRun/Nee_Toon;0770190933193b94aaa3065e307002fa;True;Unlit;0;0;Unlit;2;False;True;0;1;False;;0;False;;0;1;False;;0;False;;True;0;False;;0;False;;False;False;False;False;False;False;False;False;False;True;0;False;;False;True;0;False;;False;True;True;True;True;True;0;False;;False;False;False;False;False;False;False;True;False;255;False;;255;False;;255;False;;7;False;;1;False;;1;False;;1;False;;7;False;;1;False;;1;False;;1;False;;False;True;1;False;;True;3;False;;True;True;0;False;;0;False;;True;1;RenderType=Opaque=RenderType;True;2;False;0;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;False;True;1;LightMode=ForwardBase;False;False;0;Specular;0;0;Standard;1;Vertex Position,InvertActionOnDeselection;1;0;0;1;True;False;;False;0
WireConnection;139;0;124;0
WireConnection;139;1;126;0
WireConnection;129;0;139;0
WireConnection;129;1;127;0
WireConnection;132;0;129;0
WireConnection;26;0;24;0
WireConnection;26;1;139;0
WireConnection;134;0;130;0
WireConnection;134;1;132;0
WireConnection;45;0;26;0
WireConnection;45;1;48;0
WireConnection;137;0;134;0
WireConnection;122;0;45;0
WireConnection;122;1;123;0
WireConnection;31;0;110;0
WireConnection;31;1;47;0
WireConnection;49;0;48;0
WireConnection;100;0;110;0
WireConnection;100;1;98;0
WireConnection;121;1;122;0
WireConnection;102;0;100;0
WireConnection;39;0;31;0
WireConnection;46;0;121;0
WireConnection;46;1;49;0
WireConnection;103;0;102;0
WireConnection;103;1;101;0
WireConnection;88;0;23;0
WireConnection;88;1;89;0
WireConnection;88;2;92;0
WireConnection;4;0;2;0
WireConnection;4;1;1;0
WireConnection;4;2;52;0
WireConnection;4;3;46;0
WireConnection;41;0;39;0
WireConnection;41;1;30;0
WireConnection;135;0;131;0
WireConnection;135;1;133;0
WireConnection;38;0;36;0
WireConnection;38;1;1;0
WireConnection;91;0;88;0
WireConnection;91;1;4;0
WireConnection;91;2;103;0
WireConnection;91;3;41;0
WireConnection;37;0;43;0
WireConnection;37;1;34;0
WireConnection;35;0;41;0
WireConnection;35;1;38;0
WireConnection;105;0;104;0
WireConnection;128;0;126;0
WireConnection;128;1;125;0
WireConnection;32;0;29;0
WireConnection;32;1;44;0
WireConnection;104;0;110;0
WireConnection;133;0;128;0
WireConnection;34;0;32;0
WireConnection;42;0;110;0
WireConnection;40;0;42;0
WireConnection;80;0;91;0
ASEEND*/
//CHKSM=7E872F442C4C5A174228DA06324EFCB38927DBA1