using System;
using System.Collections;
using Animancer;
using UnityEngine;

namespace CasualGame.lib_ChuagnYi.Helper
{
    public interface ISync
    {
        public void SyncFrom();
        public void SyncTo();
    }

    [System.Serializable]
    public class AnimancerStateSync : ISync
    {
        public AnimancerState animancerState;
        public float          Speed;
        public float          NormalizedTime;
        public float          Time;
        public float          Duration;
        public bool           IsActive;
        public bool           IsStopped;
        public bool           IsPlaying;
        public float          paraFloat1;
        public float          paraFloat2;
        public float          paraFloat3;

        public AnimancerStateSync(AnimancerState animancerState)
        {
            this.animancerState = animancerState;
        }

        public void SyncFrom()
        {
            if (animancerState != null)
            {
                Speed          = animancerState.Speed;
                IsActive       = animancerState.IsActive;
                IsStopped      = animancerState.IsStopped;
                IsPlaying      = animancerState.IsPlaying;
                NormalizedTime = animancerState.NormalizedTime;
                Duration       = animancerState.Duration;
                Time           = animancerState.Time;
                switch (animancerState)
                {
                    case MixerState<Vector2> v2:
                        paraFloat1 = v2.Parameter.x;
                        paraFloat2 = v2.Parameter.y;
                        break;
                    case MixerState<float> f:
                        paraFloat1 = f.Parameter;
                        break;
                }
            }
        }

        public void SyncTo()
        {
            if (animancerState != null)
            {
                animancerState.Speed          = Speed;
                switch (animancerState)
                {
                    case MixerState<Vector2> v2:
                        v2.Parameter = new Vector2(paraFloat1, paraFloat2);
                        break;
                    case MixerState<float> f:
                        f.Parameter = paraFloat1;
                        break;
                }
            }
        }
    }
}