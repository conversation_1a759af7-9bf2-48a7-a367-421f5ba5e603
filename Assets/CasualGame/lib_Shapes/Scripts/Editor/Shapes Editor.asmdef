{"name": "ShapesEditor", "rootNamespace": "", "references": ["ShapesRuntime", "Unity.TextMeshPro", "Unity.RenderPipelines.Universal.Runtime", "Unity.RenderPipelines.Universal.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["USE_SHAPE"], "versionDefines": [], "noEngineReferences": false}