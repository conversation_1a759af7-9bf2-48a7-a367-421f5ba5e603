using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEditor;

namespace War.UPMTools
{
    class UnlockFilesCommand : ICommand
    {
        IEnumerable<string> m_Paths;
        bool m_Force;
        int m_Timeout;
        string targetsFileToUse;
        SVNAsyncOperation<LockOperationResult> m_SVNAsyncOperation;

        public UnlockFilesCommand(IEnumerable<string> paths, bool force, int timeout = -1)
        {
            m_Paths = paths;
            m_Force = force;
            m_Timeout = timeout;
            targetsFileToUse = FileUtil.GetUniqueTempPathInProject();
        }

        public SVNAsyncOperationBase StartAsyncOperation()
        {
            if (m_SVNAsyncOperation == null)
            {
                m_SVNAsyncOperation = SVNIntegration.UnlockFilesAsync(m_Paths, m_Force, targetsFileToUse, m_Timeout);
            }
            return m_SVNAsyncOperation;
        }

        public string Info()
        {
            return "UnlockFilesCommand";
        }
    }
}
