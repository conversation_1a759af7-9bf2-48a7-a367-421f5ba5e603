This adds a simple ILSupport extension that adds `ref T AsRef<T>(in T thing)` to
convert a read-only (in) ref to a regular ref, to avoid defensive copies when calling
(non-mutating) methods on it.  This is wrapped in UnsafeUtilityEx, as only Unity.Collections
has InternalsVisibleTo into this library.

Collections.LowLevel.ILSupport can and should be generated by codegen & ilpostprocessors,
however the ilpostproc infrastructure is unstable.  Given that the result of this is
constant, we just check in the final DLL.  The source for everything is in the source~
dir.

To rebuild or to add any functionality:
- delete the prebuilt DLLs in this directory
- and rename the source~ dir to source (without the ~)
- fire up Unity and let it build.  Everything should be successful, you're just now using the runtime-generated DLL.
- make whatever changes you need to
- make sure Unity's builds are up to date
- copy Library/ScriptAssemblies/Unity.Collections.LowLevel.ILSupport.dll into this dir
- rename source back to source~
